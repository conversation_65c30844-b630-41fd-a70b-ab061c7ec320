import pandas as pd


a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\Repl_Dataset_2024")


grouped = a[(a.case_capacity==1)&(a['dep'] == 'HDL')&(a.pmg!='HDL01')].groupby(['country', 'product_name'],as_index=False,observed=True).agg({"cases_delivered":"sum","case_capacity":"mean"})



# Define a function to get the top N products within each group
def get_top_n(group, n=5):
    return group.sort_values(by='cases_delivered', ascending=False).head(n)

# Apply the function to each country group and concatenate the results
top_5_products = grouped.groupby('country', group_keys=False, observed=True).apply(get_top_n,10)

print(top_5_products)









b = pd.read_clipboard()

products_all = b.groupby(["country"])["tpn"].apply(lambda s: s.tolist()).to_dict()

df = pd.DataFrame()

for k,v in products_all.items():
    b = a[(a.tpn.isin(v)) & (a.country == k)]
    df = pd.concat([df, b])
    
    
df.groupby(['country','dep'], observed=True, as_index=False)['product_name'].nunique()




# =============================================================================
# get top Foil cases stores
# =============================================================================
a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\outputs\model_outputs\Volume_update\INSIGHT_Volume_update.parquet.gz")


b = a[a.Suboperation == 'Foil Cases - put cases/tray onto shelves without opening'].groupby(["Country"], observed=True,as_index=False)['Driver_1_value'].mean()

d = pd.DataFrame()

for x in b.Country.unique().tolist():
    df = b[b.Country == x].sort_values(by='Driver_1_value', ascending=False).head()
    d = pd.concat([df, d])