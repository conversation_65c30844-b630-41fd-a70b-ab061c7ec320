import sys
import os
from PySide6.QtWidgets import (
    QA<PERSON>lication, QMainWindow, QPushButton, QMessageBox,
    QVBoxLayout, QWidget, QHBoxLayout, QDateEdit,
    QStyleFactory, QLabel
)
from PySide6.QtCore import QObject, Signal, QRunnable, Slot, QThreadPool, QDate, Qt, QPoint, QTimer
from PySide6.QtGui import QIcon, QColor, QPalette
from PySide6 import QtGui

import algos.quarterHour_fetch_data as al
from pathlib import Path
from datetime import datetime, timedelta

class WorkerSignals(QObject):
    finished = Signal(str)

class Worker(QRunnable):
    def __init__(self, start, end):
        super().__init__()
        self.signals = WorkerSignals()
        self.start = start
        self.end = end

    def run(self):
        # Simulate a long-running task
        data = al.quarterHour_report(self.start, self.end)
        directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd())
        filename = directory / f"15_mins_{self.start}_to_{self.end}.xlsx"
        al.formatting_df(data, filename)
        # data.to_excel(filename, index=False)

        self.signals.finished.emit(str(filename))

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("Quarter-hourly report")
        self.setGeometry(100, 100, 400, 150)

        # Apply Fusion style
        QApplication.setStyle(QStyleFactory.create("Fusion"))

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        self.layout = QVBoxLayout(self.central_widget)

        # Label for the start date
        self.start_label = QLabel("Start date:")
        self.layout.addWidget(self.start_label)

        # Start date input field
        self.start_input = QDateEdit(self)
        self.start_input.setDisplayFormat("yyyy-MM-dd")  # Display format
        self.start_input.setCalendarPopup(True)
        self.start_input.setDate(QDate.currentDate().addYears(-1))  # Set yesterday's date in last year as default
        self.start_input.setMaximumDate(QDate.currentDate().addDays(-1))  # Set maximum date to yesterday
        self.layout.addWidget(self.start_input)

        # Label for the end date
        self.end_label = QLabel("End date:")
        self.layout.addWidget(self.end_label)

        # End date input field
        self.end_input = QDateEdit(self)
        self.end_input.setDisplayFormat("yyyy-MM-dd")  # Display format
        self.end_input.setCalendarPopup(True)
        self.end_input.setDate(QDate.currentDate().addYears(-1))  # Set yesterday's date in last year as default
        self.end_input.setMaximumDate(QDate.currentDate().addDays(-1))  # Set maximum date to yesterday
        self.layout.addWidget(self.end_input)

        # Horizontal layout for the RUN button
        self.button_layout = QHBoxLayout()

        # Spacer
        self.button_layout.addStretch()

        # RUN button
        self.button = QPushButton("RUN!!", self)
        self.button.clicked.connect(self.run_task)
        self.button.setFixedSize(150, 50)  # Set button size
        self.button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: #ffffff;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)  # Business style colors
        self.button_layout.addWidget(self.button)

        self.layout.addLayout(self.button_layout)

        self.threadpool = QThreadPool()
        self.is_report_shown = False

        # Variables for grab and drop functionality
        self.grab_offset = QPoint()

        # Timer for updating the button label
        self.download_label_text = "Downloading"
        self.download_timer = QTimer(self)
        self.download_timer.timeout.connect(self.update_download_label)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.grab_offset = event.globalPos() - self.frameGeometry().topLeft()

    def mouseMoveEvent(self, event):
        if event.buttons() & Qt.LeftButton:
            self.move(event.globalPos() - self.grab_offset)

    def mouseReleaseEvent(self, event):
        pass

    def update_download_label(self):
        if len(self.download_label_text) >= 14:
            self.download_label_text = "Downloading"
        else:
            self.download_label_text += "."
        self.button.setText(self.download_label_text)

    def run_task(self):
        if self.is_report_shown:
            self.is_report_shown = False

        start_date = int(self.start_input.date().toString("yyyyMMdd"))
        end_date = int(self.end_input.date().toString("yyyyMMdd"))

        if end_date < start_date:
            QMessageBox.warning(self, "Error", "End date must be greater than or equal to start date.")
            return

        self.button.setEnabled(False)
        self.button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545; /* Red color */
                color: #ffffff;
                border-radius: 10px;
            }
        """)

        self.download_label_text = "Downloading"
        self.download_timer.start(500)  # Change the label every 500 milliseconds

        # Create a worker and add it to the thread pool
        worker = Worker(start_date, end_date)
        worker.signals.finished.connect(self.task_finished)
        self.threadpool.start(worker)

    @Slot(str)
    def task_finished(self, filename):
        self.download_timer.stop()  # Stop the timer when the task is finished
        self.button.setText("RUN!!")
        self.button.setEnabled(True)
        self.button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: #ffffff;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        if not self.is_report_shown:
            self.show_report_popup()
            self.is_report_shown = True

            if filename:
                os.startfile(filename)  # Open the file with the default application

    def show_report_popup(self):
        QMessageBox.information(self, "Task Finished", "Report is done.")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QtGui.QIcon('cash-desk.ico'))
    window = MainWindow()

    # Set background color for the main window
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor("#f2f2f2"))  # Light gray background
    window.setPalette(palette)

    window.show()
    sys.exit(app.exec())
