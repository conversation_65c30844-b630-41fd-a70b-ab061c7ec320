{"cells": [{"cell_type": "markdown", "id": "e8b0ea9b-f6d5-475f-9c08-eb8dda145779", "metadata": {}, "source": ["## Warehouse to Replenishment"]}, {"cell_type": "markdown", "id": "6ad8a634-9e12-4c0d-97f5-2098e4117c56", "metadata": {}, "source": ["##### import dependencies"]}, {"cell_type": "markdown", "id": "04fdd5be-0139-4389-9fc8-558b0d7c4bec", "metadata": {}, "source": ["## ak<PERSON><PERSON> lista script"]}, {"cell_type": "code", "execution_count": 1, "id": "f8a5f7ea-5740-4dde-958b-44b4ced9b240", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from os import listdir\n", "import numpy as np\n", "\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "cf518770-21e6-4b1a-bc5d-57a6ea8a6250", "metadata": {}, "outputs": [], "source": ["# CZ SK part\n", "cz = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\promo items_w14-27\\CZ promoce w14-27.xlsx\",\n", "                   skiprows=1, usecols = ['Country ID', 'TW', 'TPN', 'HM Linked stores', '3K/CHM Linked stores', '1K/SM Linked stores', 'Exp Linked stores'])\n", "sk = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\promo items_w14-27\\Filtering-mmraz-20221027114019.xlsx\",\n", "                   skiprows=1, usecols=['Country ID', 'Tesco week', 'TPN', '1K/SM Linked stores', 'Exp Linked stores', '3K/CHM Linked stores', 'HM Linked stores'] )\n", "sk['Tesco week'] = sk['Tesco week'].str[:2]\n", "sk.rename(columns={\"Tesco week\" : \"TW\"}, inplace=True)\n", "cz.TW = cz.TW.str[-2:]\n", "cz.TW = cz.TW.astype(\"int\")\n", "sk.TW = sk.TW.astype(\"int\")\n", "cz = cz.query(\"TW > 13 & TW < 28\")\n", "\n", "# cz['TPN'] = cz['TPN'].apply(lambda x: str(x)[-10:])\n", "# sk['TPN'] = sk['TPN'].apply(lambda x: str(x)[-10:])\n", "\n", "cz_sk= pd.concat([cz, sk])\n", "cz_sk.rename(columns={\"TPN\" : \"tpnb\", 'Country ID':'country', 'HM Linked stores':'hm', '3K/CHM Linked stores':'compact', '1K/SM Linked stores':'1K', 'Exp Linked stores':'exp'}, inplace=True)\n", "cz_sk = cz_sk[['country', 'tpnb', 'hm', 'compact', '1K', 'exp' ]].drop_duplicates()\n", "cz_sk.country = np.where(cz_sk.country == 'Czech', 'CZ', 'SK')\n", "for a in ['hm', 'compact', '1K', 'exp' ]:\n", "    cz_sk[a] = np.where(cz_sk[a] == 0, 0, 1)"]}, {"cell_type": "code", "execution_count": null, "id": "244b4ae2-7f3a-47f4-b81f-64e9edb65618", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import pyodbc\n", "\n", "tpn = list(set(cz_sk.tpnb))\n", "conn = pyodbc.connect(\"DSN=UKHadoop_CE_spark\",autocommit=True, Trusted_Connection=\"yes\")\n", "cursor = conn.cursor()\n", "\n", "collector = pd.DataFrame()\n", "\n", "index = 0\n", "s = list()\n", "for x in tpn:\n", "\n", "    s.append(str(x))\n", "    index += 1\n", "\n", "    if index % 200 == 0 or (len(tpn) == index):\n", "        stores_string = \",\".join(s)\n", "\n", "\n", "        sql = \"\"\" SELECT cntr_code AS country, slad_tpnb AS tpnb, slad_tpn AS tpn, slad_long_des as name\n", "                FROM DM.dim_artgld_details mstr\n", "                WHERE slad_tpn in ({pmg})\n", "                AND cntr_code in ('CZ', 'SK') \n", "                GROUP BY cntr_code, slad_tpnb, slad_tpn, slad_long_des\n", "\n", "                \"\"\".format(pmg=stores_string)\n", "        \n", "        collector = pd.concat([collector, pd.read_sql(sql,conn)])\n", "        s = list()\n", "        \n", "        print(f\"done with {collector.shape[0]} rows\")\n", "c = collector[['country', 'tpnb', 'tpn']].drop_duplicates()\n", "c[['tpnb', 'tpn']] = c[['tpnb', 'tpn']].astype('int64')\n", "cz_sk.rename(columns={'tpnb':'tpn'},inplace=True)\n", "\n", "cz_sk = cz_sk.merge(c, on=['country','tpn'], how='left')\n", "cz_sk.drop(['tpn'], axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "cae90c9d-ea9b-400e-9446-5de13bf6f161", "metadata": {}, "outputs": [], "source": ["cz_sk.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6523c428-138a-48fb-bbc2-572504d6d286", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "96877aa8-8678-4978-93ff-3783d840c36d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16cbbb94-9ffc-4553-8cd8-9375ee82a71c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4518b1b8-443b-4653-9f7d-3f419058e421", "metadata": {}, "outputs": [], "source": ["%%time\n", "\n", "# # CZ SK part\n", "# cz = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\promo items_w14-27\\CZ promoce w14-27.xlsx\",\n", "#                    skiprows=1, usecols = ['Country ID', 'TW', 'TPN', 'HM Linked stores', '3K/CHM Linked stores', '1K/SM Linked stores', 'Exp Linked stores'])\n", "# sk = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\promo items_w14-27\\Filtering-mmraz-20221027114019.xlsx\",\n", "#                    skiprows=1, usecols=['Country ID', 'Tesco week', 'TPN', '1K/SM Linked stores', 'Exp Linked stores', '3K/CHM Linked stores', 'HM Linked stores'] )\n", "# sk['Tesco week'] = sk['Tesco week'].str[:2]\n", "# sk.rename(columns={\"Tesco week\" : \"TW\"}, inplace=True)\n", "# cz.TW = cz.TW.str[-2:]\n", "# cz.TW = cz.TW.astype(\"int\")\n", "# sk.TW = sk.TW.astype(\"int\")\n", "# cz = cz.query(\"TW > 13 & TW < 28\")\n", "\n", "# cz['TPN'] = cz['TPN'].apply(lambda x: str(x)[-10:])\n", "# sk['TPN'] = sk['TPN'].apply(lambda x: str(x)[-10:])\n", "\n", "# cz_sk= pd.concat([cz, sk])\n", "# cz_sk.rename(columns={\"TPN\" : \"tpnb\", 'Country ID':'country', 'HM Linked stores':'hm', '3K/CHM Linked stores':'compact', '1K/SM Linked stores':'1K', 'Exp Linked stores':'exp'}, inplace=True)\n", "# cz_sk = cz_sk[['country', 'tpnb', 'hm', 'compact', '1K', 'exp' ]].drop_duplicates()\n", "# cz_sk.country = np.where(cz_sk.country == 'Czech', 'CZ', 'SK')\n", "# for a in ['hm', 'compact', '1K', 'exp' ]:\n", "#     cz_sk[a] = np.where(cz_sk[a] == 0, 0, 1)\n", "    \n", "    \n", "# HU part  \n", "date_plan_start = '0530'\n", "date_plan_end = '0904'\n", "\n", "    \n", "list_date = []\n", "list_date_real=[]\n", "list_date_real_=[]\n", "list_min_date = []\n", "list_max_date = []\n", "hu_df_hm= pd.DataFrame()\n", "hu_df_3k= pd.DataFrame()\n", "hu_df_1k= pd.DataFrame()\n", "hu_df_exp= pd.DataFrame()\n", "\n", "\n", "for a in listdir(r\"\\\\hu.tesco-europe.com\\hq-shares\\Support_Office\\SO Communication\\Hírek\\Akciós Pakk\\Akciós pakk hiper\\2022\"):\n", "    if a[-4:].isdigit():\n", "        integer = a[-4:]\n", "        list_date.append(int(integer))\n", "list_min_date.append(min(list_date, key=lambda x:abs(x-int(date_plan_start))))\n", "list_max_date.append(min(list_date, key=lambda x:abs(x-int(date_plan_end))))\n", "\n", "for a in listdir(r\"\\\\hu.tesco-europe.com\\hq-shares\\Support_Office\\SO Communication\\Hírek\\Akciós Pakk\\Akciós pakk hiper\\2022\"):\n", "    if a[-4:].isdigit():\n", "        integer = int(a[-4:])\n", "        if list_min_date[0] <= integer <= list_max_date[0]:\n", "            list_date_real.append(str(integer).rjust(4,'0'))\n", "            \n", "\n", "# Hypermarket            \n", "for a in listdir(r\"\\\\hu.tesco-europe.com\\hq-shares\\Support_Office\\SO Communication\\Hírek\\Akciós Pakk\\Akciós pakk hiper\\2022\"):\n", "    for b in list_date_real:\n", "        if a[-4:] == b:\n", "            df_hm = pd.read_excel(r\"\\\\hu.tesco-europe.com\\hq-shares\\Support_Office\\SO Communication\\Hírek\\Akciós Pakk\\Akciós pakk hiper\\2022\\{a}\\Kötelező kihelyezések\\Végleges akciós lista_{a}.xlsx\".format(a=a), sheet_name=\"HM\", usecols=['TPNB'])\n", "            df_hm['hm'] = 1\n", "            df_3k = pd.read_excel(r\"\\\\hu.tesco-europe.com\\hq-shares\\Support_Office\\SO Communication\\Hírek\\Akciós Pakk\\Akciós pakk hiper\\2022\\{a}\\Kötelező kihelyezések\\Végleges akciós lista_{a}.xlsx\".format(a=a), sheet_name=\"3K\", usecols=['TPNB'])\n", "            df_3k['compact'] = 1\n", "            hu_df_hm = pd.concat([hu_df_hm, df_hm]).fillna(0).drop_duplicates()\n", "            hu_df_3k = pd.concat([hu_df_3k, df_3k]).fillna(0).drop_duplicates()\n", "\n", "            #print(f\"{a}.xlsx is ready and df_hm has {hu_df_hm.shape[0]} and df_3k has {hu_df_3k.shape[0]} rows\")\n", "            \n", "hu_df_hip = hu_df_hm.merge(hu_df_3k, on='TPNB', how=\"outer\")\n", "\n", "\n", "# 1K, Express\n", "for a in listdir(r\"\\\\hu.tesco-europe.com\\hq-shares\\Support_Office\\SO Communication\\Hírek\\Akciós Pakk\\Akciós pakk hiper\\2022\"):\n", "    for b in list_date_real:\n", "        if a[-4:] == b:\n", "            try:\n", "                df_1k = pd.read_excel(r\"\\\\hu.tesco-europe.com\\hq-shares\\Support_Office\\SO Communication\\Hírek\\Akciós Pakk\\Akciós pakk kisformátum\\2022\\{a}\\Végleges akciós lista_Conv_{a}.xlsx\".format(a=a), sheet_name=\"1K\", usecols=['TPNB'])\n", "                df_1k['1K'] = 1\n", "\n", "                df_exp = pd.read_excel(r\"\\\\hu.tesco-europe.com\\hq-shares\\Support_Office\\SO Communication\\Hírek\\Akciós Pakk\\Akciós pakk kisformátum\\2022\\{a}\\Végleges akciós lista_Conv_{a}.xlsx\".format(a=a), sheet_name=\"Exp\", usecols=['TPNB'])\n", "                df_exp['exp'] = 1\n", "\n", "                hu_df_1k = pd.concat([hu_df_1k, df_1k]).fillna(0).drop_duplicates()\n", "                hu_df_exp = pd.concat([hu_df_exp, df_exp]).fillna(0).drop_duplicates()\n", "                #print(f\"{a}.xlsx is ready and df_1k has {hu_df_1k.shape[0]} and df_exp has {hu_df_exp.shape[0]} rows\")\n", "            except (FileNot<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ValueError):\n", "                pass\n", "hu_df_conv = hu_df_exp.merge(hu_df_1k, on='TPNB', how=\"outer\")\n", "\n", "\n", "hu_df = hu_df_hip.merge(hu_df_conv, on='TPNB', how='outer').fillna(0)\n", "\n", "hu_df['country'] = \"HU\"\n", "hu_df.rename(columns={\"TPNB\":\"tpnb\"}, inplace=True)\n", "\n", "            \n", "ce_promo_item_p4_p7 = pd.concat([cz_sk, hu_df])\n", "ce_promo_item_p4_p7[['hm','compact','1K','exp']] = ce_promo_item_p4_p7[['hm','compact','1K','exp']].astype(\"int\")\n", "ce_promo_item_p4_p7['tpnb'] = ce_promo_item_p4_p7['tpnb'].astype(\"int64\")"]}, {"cell_type": "code", "execution_count": null, "id": "9b7aea67-51ef-4161-b15a-ee83c83d35ca", "metadata": {}, "outputs": [], "source": ["ce_promo_item_p4_p7.to_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\ce_promo_item_p4_p7.parquet.gz\",index=False, compression=\"gzip\")"]}, {"cell_type": "code", "execution_count": null, "id": "8b69b0a4-31a0-43f7-8fce-4b3503a06910", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": null, "id": "4eeac053-27fc-4dd9-b1c1-db27e0a04aa1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8fa113b1-3271-4d72-af30-a09b46efa7b4", "metadata": {}, "outputs": [], "source": ["a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\ce_promo_item_p4_p7.parquet.gz\")"]}, {"cell_type": "code", "execution_count": null, "id": "519dd3cf-2cb1-4396-a1c2-6a3fc7504b3d", "metadata": {}, "outputs": [], "source": ["ce = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl_Dataset_2022_new_14w_27w.parquet\")"]}, {"cell_type": "code", "execution_count": null, "id": "82fbb7d1-f771-4649-b7ff-33dd4c74ef4b", "metadata": {}, "outputs": [], "source": ["format_ = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl\\Stores_Inputs_2023_Q1_presort_verz1_modul.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "40b84847-ab1c-4723-9b36-c8964af3f2fa", "metadata": {}, "outputs": [], "source": ["format_.rename(columns={'Store':'store'}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "a722201e-f0ce-4501-8c36-0fe7ae0e07e0", "metadata": {}, "outputs": [], "source": ["ce = ce.merge(format_[['store', 'Format']], on='store', how = 'left')"]}, {"cell_type": "code", "execution_count": null, "id": "dcec1436-6a24-4b85-981e-70939eac4785", "metadata": {}, "outputs": [], "source": ["hm_promo = a.query(\"hm == 1\")[['country', 'tpnb']].drop_duplicates()\n", "hm_promo['Format'] = \"Hypermarket\"\n", "hm_promo['promo_flag'] = 1\n", "compact_promo = a.query(\"compact == 1\")[['country', 'tpnb']].drop_duplicates()\n", "compact_promo['Format'] = \"Compact\"\n", "compact_promo['promo_flag'] = 1\n", "onek_promo = a[a['1K'] == 1][['country', 'tpnb']].drop_duplicates()\n", "onek_promo['Format'] = \"1K\"\n", "onek_promo['promo_flag'] = 1\n", "exp_promo = a[a['exp'] == 1][['country', 'tpnb']].drop_duplicates()\n", "exp_promo['Format'] = \"Express\"\n", "exp_promo['promo_flag'] = 1\n", "\n", "promo = pd.concat([hm_promo, compact_promo, onek_promo, exp_promo])\n", "\n", "ce = ce.merge(promo, on=['country', 'tpnb', 'Format'], how='left')\n", "    \n", "ce.promo_flag = ce.promo_flag.replace(np.nan, 0)\n", "ce[['day', 'country', 'Format']] = ce[['day', 'country', 'Format']].astype(\"category\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c81ae084-1452-4930-a03a-2a08d8d90bf8", "metadata": {}, "outputs": [], "source": ["ce.to_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl_Dataset_2022_new_14w_27w_promo_flag.parquet\", compression=\"gzip\")"]}, {"cell_type": "code", "execution_count": null, "id": "ff6a0c1c-7199-4b7e-b8fc-74f5fa95b5ca", "metadata": {}, "outputs": [], "source": ["a[a['exp'] == 1][['country', 'tpnb']].head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "9605261a-6493-405c-bd82-cdf737ae4cd1", "metadata": {}, "outputs": [], "source": ["ce.head()"]}, {"cell_type": "code", "execution_count": null, "id": "723934e2-7ade-49f4-81d9-efb86bc84424", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b7eeddd0-6f28-430b-b25a-688c30657c34", "metadata": {}, "outputs": [], "source": ["ce.shape"]}, {"cell_type": "code", "execution_count": null, "id": "fe84a40a-9a94-44ed-99d4-8ada6a30a3b6", "metadata": {}, "outputs": [], "source": ["ce.tpnb.astype(\"string\").str[:-4]"]}, {"cell_type": "code", "execution_count": null, "id": "b85aa672-ae9c-442a-84d3-3b2574729155", "metadata": {}, "outputs": [], "source": ["d = ce[(ce.tpnb.astype(\"string\").str[-4:] == '4669') & (ce.country == 'CZ')]"]}, {"cell_type": "code", "execution_count": null, "id": "ee9218ff-50a1-4a19-89c2-8af1a04c49e8", "metadata": {}, "outputs": [], "source": ["d.tpnb.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "754cb336-678b-4873-834f-5a6789560964", "metadata": {}, "outputs": [], "source": ["a[a.country == 'CZ'].head()"]}, {"cell_type": "code", "execution_count": null, "id": "9f77481f-800e-4718-99eb-2144c50bea1b", "metadata": {}, "outputs": [], "source": ["a[a.country == 'HU'].head()"]}, {"cell_type": "markdown", "id": "af2c97b6-e447-48bd-896b-78a6a17e95b8", "metadata": {"tags": []}, "source": ["#### Mo<PERSON>l"]}, {"cell_type": "code", "execution_count": 47, "id": "68853f92-1576-4e42-8e93-7e45268ebd2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['HM', 'EX', '1K']\n", "['HM', 'SM', 'EX', 'OD']\n", "['HM', 'SM', 'EX', 'OD']\n"]}], "source": ["from openpyxl import load_workbook\n", "import pandas as pd\n", "\n", "def get_sheetnames_xlsx(filepath):\n", "    wb = load_workbook(filepath, read_only=True, keep_links=False)\n", "    return wb.sheetnames\n", "\n", "hu = r\"\\\\huprgvmfs05\\CE_SRD_IDR_SHARED\\REPORT\\DB_Report_SRD_6462376\\DB Report Hungary\\DB Report 2022\\DB REPORT HU WEEK_33_2022(17_10_2022).xlsx\"\n", "cz = r\"\\\\huprgvmfs05\\CE_SRD_IDR_SHARED\\REPORT\\DB_Report_SRD_6462376\\DB Report CZ  & SK\\CZ&SK\\2022\\2022.10.17\\CZ_2022-10-17.xlsx\"\n", "sk = r\"\\\\huprgvmfs05\\CE_SRD_IDR_SHARED\\REPORT\\DB_Report_SRD_6462376\\DB Report CZ  & SK\\CZ&SK\\2022\\2022.10.17\\SK_2022-10-17.xlsx\"\n", "key_table = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2022\\Model_Inputs\\hausdorf_modul_tables\\DRG_mapping.xlsx\"\n", "prod_key_table=r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl\\Stores_Inputs_2023_Q1_presort_verz1_modul.xlsx\"\n", "\n", "hu_all = pd.DataFrame()\n", "cz_all = pd.DataFrame()\n", "sk_all = pd.DataFrame()\n", "\n", "for c, x in zip(['hu', 'cz', 'sk'], [hu, cz, sk] ): \n", "    sheetnames = get_sheetnames_xlsx(x)\n", "    print(sheetnames)\n", "    \n", "    for y in range(len(sheetnames)):\n", "        b = pd.read_excel(x, sheet_name=sheetnames[y])\n", "        if c == 'hu':\n", "            hu_all = pd.concat([hu_all, b])\n", "            hu_all['Store Number'] = str(4) + hu_all['Store Number'].astype('str')\n", "            hu_all['Store Number'] = hu_all['Store Number'].astype(str).str[-5:]\n", "\n", "        if c == 'cz':\n", "            cz_all = pd.concat([cz_all, b])\n", "            cz_all['Store Number'] = str(1) + cz_all['Store Number'].astype('str')\n", "            cz_all['Store Number'] = cz_all['Store Number'].astype(str).str[-5:]\n", "\n", "        if c == 'sk':\n", "            sk_all = pd.concat([sk_all, b])\n", "            sk_all['Store Number'] = str(2) + sk_all['Store Number'].astype('str')\n", "            sk_all['Store Number'] = sk_all['Store Number'].astype(str).str[-5:]\n", "            \n", "CE_mod = pd.concat([hu_all, cz_all, sk_all])\n", "CE_mod['CE_status'] = CE_mod['Status'].fillna('') + CE_mod['Planogram Status'].fillna('')\n", "CE_mod['total_modul_nr'] = CE_mod['No of mods'].fillna(0) + CE_mod['POG No Of Mods'].fillna(0)\n", "CE_mod = CE_mod.query(\"CE_status == 'Live'\")[['Store Number', 'Merchandising Group', 'total_modul_nr' ]]\n", "\n", "CE_mod_gr = CE_mod.groupby(CE_mod.columns[:2].tolist())['total_modul_nr'].sum().reset_index()\n", "CE_mod_gr.rename(columns={\"Store Number\" : \"store\"}, inplace=True)\n", "\n", "condition = [CE_mod_gr['store'].astype(str).str.match(\"^1\"), CE_mod_gr['store'].astype(str).str.match(\"^2\"), CE_mod_gr['store'].astype(str).str.match(\"^4\")]\n", "results = ['CZ','SK', \"HU\"]\n", "CE_mod_gr['country'] = np.select(condition, results, 0)\n", "CE_mod_gr['Key'] = CE_mod_gr['Merchandising Group'] + CE_mod_gr['country']\n", "\n", "# key table opening\n", "key = pd.read_excel(key_table)\n", "\n", "CE_mod_gr = CE_mod_gr.merge(key[['Key', 'DIV_DESC', 'DEP_DESC']].drop_duplicates(), on='Key', how='left')\n", "CE_mod_gr = CE_mod_gr[CE_mod_gr.DIV_DESC.notnull()]\n", "CE_mod_gr = CE_mod_gr.groupby(['store', 'DIV_DESC', 'DEP_DESC'])['total_modul_nr'].sum().reset_index()\n", "CE_mod_gr['merged'] = CE_mod_gr['DIV_DESC'] + CE_mod_gr['DEP_DESC']\n", "\n", "#prod key table\n", "prod_key = pd.read_excel(prod_key_table, sheet_name=\"key_to_moduls\")\n", "CE_mod_gr = CE_mod_gr.merge(prod_key[['merged', 'dep']], on=\"merged\", how=\"left\")\n", "CE_mod_gr = CE_mod_gr[CE_mod_gr.DIV_DESC != \"Centralized Hardline\" ]\n", "CE_mod_gr['store'] = CE_mod_gr['store'].astype(\"int32\")\n", "CE_mod_gr = CE_mod_gr.groupby(['store', 'dep'])['total_modul_nr'].sum().reset_index()\n", "\n", "#save\n", "CE_mod_gr.to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\hausdorf_modul_tables\\ce_moduls_dep.xlsx\", index=False)\n"]}, {"cell_type": "code", "execution_count": 60, "id": "b4478066-e807-4231-b92a-f62fe4ea80e9", "metadata": {}, "outputs": [], "source": ["a = CE_mod_gr.copy()"]}, {"cell_type": "code", "execution_count": 63, "id": "2d8d7a1b-1b56-4210-97a8-926bc1b28cb1", "metadata": {}, "outputs": [], "source": ["CE_mod_gr = CE_mod_gr[CE_mod_gr.DIV_DESC.notnull()]"]}, {"cell_type": "code", "execution_count": 72, "id": "81c837df-986b-4906-8a63-20ffefd44730", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Key</th>\n", "      <th>Country of STRAPP</th>\n", "      <th>DIV_ID</th>\n", "      <th>DIV_DESC</th>\n", "      <th>DEP_ID</th>\n", "      <th>DEP_DESC</th>\n", "      <th>SEC_ID</th>\n", "      <th>SEC_DESC</th>\n", "      <th>GRP_ID</th>\n", "      <th>GRP_DESC</th>\n", "      <th>SGR_ID</th>\n", "      <th>SGR_DESC</th>\n", "      <th>DRG_ID</th>\n", "      <th>DRG_DESC</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>439</th>\n", "      <td>C1WCZ</td>\n", "      <td>CZ</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>11</td>\n", "      <td><PERSON><PERSON>_<PERSON></td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>440</th>\n", "      <td>C1WCZ</td>\n", "      <td>CZ</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>12</td>\n", "      <td>Pineapples</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>441</th>\n", "      <td>C1WCZ</td>\n", "      <td>CZ</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>13</td>\n", "      <td>Special_Exotic</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>442</th>\n", "      <td>C1WCZ</td>\n", "      <td>CZ</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>14</td>\n", "      <td>POPULAR_Exotic</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>443</th>\n", "      <td>C1WCZ</td>\n", "      <td>CZ</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>15</td>\n", "      <td>CW_Spec_Exotic</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>444</th>\n", "      <td>C1WCZ</td>\n", "      <td>CZ</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>16</td>\n", "      <td>Pomelo</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4847</th>\n", "      <td>R5WCZ</td>\n", "      <td>CZ</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>570</td>\n", "      <td>Kitchen</td>\n", "      <td>40</td>\n", "      <td>Home Utility</td>\n", "      <td>71</td>\n", "      <td>Tele-Shopping</td>\n", "      <td>R5W</td>\n", "      <td>Media Shop</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4962</th>\n", "      <td>R3WCZ</td>\n", "      <td>CZ</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>580</td>\n", "      <td>Improvement</td>\n", "      <td>20</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>Y.</td>\n", "      <td>640</td>\n", "      <td>Handtools Extra</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRAS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5004</th>\n", "      <td>R4WCZ</td>\n", "      <td>CZ</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>600</td>\n", "      <td>Furniture</td>\n", "      <td>20</td>\n", "      <td>Wall Units</td>\n", "      <td>10</td>\n", "      <td>wardrobe</td>\n", "      <td>R4W</td>\n", "      <td>BEDROOM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5012</th>\n", "      <td>R4WCZ</td>\n", "      <td>CZ</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>600</td>\n", "      <td>Furniture</td>\n", "      <td>30</td>\n", "      <td>Bedroom Furnit</td>\n", "      <td>10</td>\n", "      <td>Commode/Armoir</td>\n", "      <td>R4W</td>\n", "      <td>BEDROOM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5013</th>\n", "      <td>R4WCZ</td>\n", "      <td>CZ</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>600</td>\n", "      <td>Furniture</td>\n", "      <td>30</td>\n", "      <td>Bedroom Furnit</td>\n", "      <td>20</td>\n", "      <td>beds</td>\n", "      <td>R4W</td>\n", "      <td>BEDROOM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5404</th>\n", "      <td>R3WCZ</td>\n", "      <td>CZ</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>5807</td>\n", "      <td>CS1 Improvement</td>\n", "      <td>20</td>\n", "      <td>CS1 D.I.Y.</td>\n", "      <td>640</td>\n", "      <td>CS1 Handtools Extra</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRAS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12680</th>\n", "      <td>C1WSK</td>\n", "      <td>SK</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>11</td>\n", "      <td><PERSON><PERSON>_<PERSON></td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12681</th>\n", "      <td>C1WSK</td>\n", "      <td>SK</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>12</td>\n", "      <td>Pineapples</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12682</th>\n", "      <td>C1WSK</td>\n", "      <td>SK</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>13</td>\n", "      <td>Special_Exotic</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12683</th>\n", "      <td>C1WSK</td>\n", "      <td>SK</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>14</td>\n", "      <td>POPULAR_Exotic</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12684</th>\n", "      <td>C1WSK</td>\n", "      <td>SK</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>15</td>\n", "      <td>CW_Spec_Exotic</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12685</th>\n", "      <td>C1WSK</td>\n", "      <td>SK</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>16</td>\n", "      <td>Pomelo</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17078</th>\n", "      <td>R5WSK</td>\n", "      <td>SK</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>570</td>\n", "      <td>Kitchen</td>\n", "      <td>40</td>\n", "      <td>Home Utility</td>\n", "      <td>71</td>\n", "      <td>Tele-Shopping</td>\n", "      <td>R5W</td>\n", "      <td>Media Shop</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17192</th>\n", "      <td>R3WSK</td>\n", "      <td>SK</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>580</td>\n", "      <td>Improvement</td>\n", "      <td>20</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>Y.</td>\n", "      <td>640</td>\n", "      <td>Handtools Extra</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17234</th>\n", "      <td>R4WSK</td>\n", "      <td>SK</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>600</td>\n", "      <td>Furniture</td>\n", "      <td>20</td>\n", "      <td>Wall Units</td>\n", "      <td>10</td>\n", "      <td>wardrobe</td>\n", "      <td>R4W</td>\n", "      <td>BEDROOM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17242</th>\n", "      <td>R4WSK</td>\n", "      <td>SK</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>600</td>\n", "      <td>Furniture</td>\n", "      <td>30</td>\n", "      <td>Bedroom Furnit</td>\n", "      <td>10</td>\n", "      <td>Commode/Armoir</td>\n", "      <td>R4W</td>\n", "      <td>BEDROOM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17243</th>\n", "      <td>R4WSK</td>\n", "      <td>SK</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>600</td>\n", "      <td>Furniture</td>\n", "      <td>30</td>\n", "      <td>Bedroom Furnit</td>\n", "      <td>20</td>\n", "      <td>beds</td>\n", "      <td>R4W</td>\n", "      <td>BEDROOM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17634</th>\n", "      <td>R3WSK</td>\n", "      <td>SK</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>5807</td>\n", "      <td>CS1 Improvement</td>\n", "      <td>20</td>\n", "      <td>CS1 D.I.Y.</td>\n", "      <td>640</td>\n", "      <td>CS1 Handtools Extra</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24831</th>\n", "      <td>C1WHU</td>\n", "      <td>HU</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>11</td>\n", "      <td><PERSON><PERSON>_<PERSON></td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24832</th>\n", "      <td>C1WHU</td>\n", "      <td>HU</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>12</td>\n", "      <td>Pineapples</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24833</th>\n", "      <td>C1WHU</td>\n", "      <td>HU</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>13</td>\n", "      <td>Special_Exotic</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24834</th>\n", "      <td>C1WHU</td>\n", "      <td>HU</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>14</td>\n", "      <td>POPULAR_Exotic</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24835</th>\n", "      <td>C1WHU</td>\n", "      <td>HU</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>15</td>\n", "      <td>CW_Spec_Exotic</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24836</th>\n", "      <td>C1WHU</td>\n", "      <td>HU</td>\n", "      <td>1</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>12</td>\n", "      <td>Produce</td>\n", "      <td>1204</td>\n", "      <td>Fruit</td>\n", "      <td>14</td>\n", "      <td>Exotic_Fruit</td>\n", "      <td>16</td>\n", "      <td>Pomelo</td>\n", "      <td>C1W</td>\n", "      <td>EXOTIC_FRUIT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29210</th>\n", "      <td>R3WHU</td>\n", "      <td>HU</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>580</td>\n", "      <td>Improvement</td>\n", "      <td>20</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>Y.</td>\n", "      <td>540</td>\n", "      <td>measure/spirit level</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29211</th>\n", "      <td>R3WHU</td>\n", "      <td>HU</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>580</td>\n", "      <td>Improvement</td>\n", "      <td>20</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>Y.</td>\n", "      <td>550</td>\n", "      <td>screwdriver/wrenche</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29212</th>\n", "      <td>R3WHU</td>\n", "      <td>HU</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>580</td>\n", "      <td>Improvement</td>\n", "      <td>20</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>Y.</td>\n", "      <td>560</td>\n", "      <td>hammers/pliers</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29213</th>\n", "      <td>R3WHU</td>\n", "      <td>HU</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>580</td>\n", "      <td>Improvement</td>\n", "      <td>20</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>Y.</td>\n", "      <td>570</td>\n", "      <td>saw/file/knives</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29644</th>\n", "      <td>R3WHU</td>\n", "      <td>HU</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>5807</td>\n", "      <td>CS1 Improvement</td>\n", "      <td>20</td>\n", "      <td>CS1 D.I.Y.</td>\n", "      <td>540</td>\n", "      <td>CS1 measure/spirit level</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29645</th>\n", "      <td>R3WHU</td>\n", "      <td>HU</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>5807</td>\n", "      <td>CS1 Improvement</td>\n", "      <td>20</td>\n", "      <td>CS1 D.I.Y.</td>\n", "      <td>550</td>\n", "      <td>CS1 screwdriver/wrenche</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29646</th>\n", "      <td>R3WHU</td>\n", "      <td>HU</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>5807</td>\n", "      <td>CS1 Improvement</td>\n", "      <td>20</td>\n", "      <td>CS1 D.I.Y.</td>\n", "      <td>560</td>\n", "      <td>CS1 hammers/pliers</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29647</th>\n", "      <td>R3WHU</td>\n", "      <td>HU</td>\n", "      <td>30</td>\n", "      <td>Hardlines</td>\n", "      <td>190</td>\n", "      <td>Home</td>\n", "      <td>5807</td>\n", "      <td>CS1 Improvement</td>\n", "      <td>20</td>\n", "      <td>CS1 D.I.Y.</td>\n", "      <td>570</td>\n", "      <td>CS1 saw/file/knives</td>\n", "      <td>R3W</td>\n", "      <td>HANDTOOLS_EXTRA</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Key Country of STRAPP  DIV_ID         DIV_DESC  DEP_ID DEP_DESC  \\\n", "439    C1WCZ                CZ       1  Fresh_Froz_Food      12  Produce   \n", "440    C1WCZ                CZ       1  Fresh_Froz_Food      12  Produce   \n", "441    C1WCZ                CZ       1  Fresh_Froz_Food      12  Produce   \n", "442    C1WCZ                CZ       1  Fresh_Froz_Food      12  Produce   \n", "443    C1WCZ                CZ       1  Fresh_Froz_Food      12  Produce   \n", "444    C1WCZ                CZ       1  Fresh_Froz_Food      12  Produce   \n", "4847   R5WCZ                CZ      30        Hardlines     190     Home   \n", "4962   R3WCZ                CZ      30        Hardlines     190     Home   \n", "5004   R4WCZ                CZ      30        Hardlines     190     Home   \n", "5012   R4WCZ                CZ      30        Hardlines     190     Home   \n", "5013   R4WCZ                CZ      30        Hardlines     190     Home   \n", "5404   R3WCZ                CZ      30        Hardlines     190     Home   \n", "12680  C1WSK                SK       1  Fresh_Froz_Food      12  Produce   \n", "12681  C1WSK                SK       1  Fresh_Froz_Food      12  Produce   \n", "12682  C1WSK                SK       1  Fresh_Froz_Food      12  Produce   \n", "12683  C1WSK                SK       1  Fresh_Froz_Food      12  Produce   \n", "12684  C1WSK                SK       1  Fresh_Froz_Food      12  Produce   \n", "12685  C1WSK                SK       1  Fresh_Froz_Food      12  Produce   \n", "17078  R5WSK                SK      30        Hardlines     190     Home   \n", "17192  R3WSK                SK      30        Hardlines     190     Home   \n", "17234  R4WSK                SK      30        Hardlines     190     Home   \n", "17242  R4WSK                SK      30        Hardlines     190     Home   \n", "17243  R4WSK                SK      30        Hardlines     190     Home   \n", "17634  R3WSK                SK      30        Hardlines     190     Home   \n", "24831  C1WHU                HU       1  Fresh_Froz_Food      12  Produce   \n", "24832  C1WHU                HU       1  Fresh_Froz_Food      12  Produce   \n", "24833  C1WHU                HU       1  Fresh_Froz_Food      12  Produce   \n", "24834  C1WHU                HU       1  Fresh_Froz_Food      12  Produce   \n", "24835  C1WHU                HU       1  Fresh_Froz_Food      12  Produce   \n", "24836  C1WHU                HU       1  Fresh_Froz_Food      12  Produce   \n", "29210  R3WHU                HU      30        Hardlines     190     Home   \n", "29211  R3WHU                HU      30        Hardlines     190     Home   \n", "29212  R3WHU                HU      30        Hardlines     190     Home   \n", "29213  R3WHU                HU      30        Hardlines     190     Home   \n", "29644  R3WHU                HU      30        Hardlines     190     Home   \n", "29645  R3WHU                HU      30        Hardlines     190     Home   \n", "29646  R3WHU                HU      30        Hardlines     190     Home   \n", "29647  R3WHU                HU      30        Hardlines     190     Home   \n", "\n", "       SEC_ID         SEC_DESC  GRP_ID        GRP_DESC  SGR_ID  \\\n", "439      1204            Fruit      14    Exotic_Fruit      11   \n", "440      1204            Fruit      14    Exotic_Fruit      12   \n", "441      1204            Fruit      14    Exotic_Fruit      13   \n", "442      1204            Fruit      14    Exotic_Fruit      14   \n", "443      1204            Fruit      14    Exotic_Fruit      15   \n", "444      1204            Fruit      14    Exotic_Fruit      16   \n", "4847      570          Kitchen      40    Home Utility      71   \n", "4962      580      Improvement      20          D.I.Y.     640   \n", "5004      600        Furniture      20      Wall Units      10   \n", "5012      600        Furniture      30  Bedroom Furnit      10   \n", "5013      600        Furniture      30  Bedroom Furnit      20   \n", "5404     5807  CS1 Improvement      20      CS1 D.I.Y.     640   \n", "12680    1204            Fruit      14    Exotic_Fruit      11   \n", "12681    1204            Fruit      14    Exotic_Fruit      12   \n", "12682    1204            Fruit      14    Exotic_Fruit      13   \n", "12683    1204            Fruit      14    Exotic_Fruit      14   \n", "12684    1204            Fruit      14    Exotic_Fruit      15   \n", "12685    1204            Fruit      14    Exotic_Fruit      16   \n", "17078     570          Kitchen      40    Home Utility      71   \n", "17192     580      Improvement      20          D.I.Y.     640   \n", "17234     600        Furniture      20      Wall Units      10   \n", "17242     600        Furniture      30  Bedroom Furnit      10   \n", "17243     600        Furniture      30  Bedroom Furnit      20   \n", "17634    5807  CS1 Improvement      20      CS1 D.I.Y.     640   \n", "24831    1204            Fruit      14    Exotic_Fruit      11   \n", "24832    1204            Fruit      14    Exotic_Fruit      12   \n", "24833    1204            Fruit      14    Exotic_Fruit      13   \n", "24834    1204            Fruit      14    Exotic_Fruit      14   \n", "24835    1204            Fruit      14    Exotic_Fruit      15   \n", "24836    1204            Fruit      14    Exotic_Fruit      16   \n", "29210     580      Improvement      20          D.I.Y.     540   \n", "29211     580      Improvement      20          D.I.Y.     550   \n", "29212     580      Improvement      20          D.I.Y.     560   \n", "29213     580      Improvement      20          D.I.Y.     570   \n", "29644    5807  CS1 Improvement      20      CS1 D.I.Y.     540   \n", "29645    5807  CS1 Improvement      20      CS1 D.I.Y.     550   \n", "29646    5807  CS1 Improvement      20      CS1 D.I.Y.     560   \n", "29647    5807  CS1 Improvement      20      CS1 D.I.Y.     570   \n", "\n", "                       SGR_DESC DRG_ID          DRG_DESC  \n", "439                  Kiwi_Fruit    C1W      EXOTIC_FRUIT  \n", "440                  Pineapples    C1W      EXOTIC_FRUIT  \n", "441              Special_Exotic    C1W      EXOTIC_FRUIT  \n", "442              POPULAR_Exotic    C1W      EXOTIC_FRUIT  \n", "443              CW_Spec_Exotic    C1W      EXOTIC_FRUIT  \n", "444                      Pomelo    C1W      EXOTIC_FRUIT  \n", "4847              Tele-Shopping    R5W        Media Shop  \n", "4962            Handtools Extra    R3W  HANDTOOLS_EXTRAS  \n", "5004                   wardrobe    R4W           BEDROOM  \n", "5012             Commode/Armoir    R4W           BEDROOM  \n", "5013                       beds    R4W           BEDROOM  \n", "5404        CS1 Handtools Extra    R3W  HANDTOOLS_EXTRAS  \n", "12680                Kiwi_Fruit    C1W      EXOTIC_FRUIT  \n", "12681                Pineapples    C1W      EXOTIC_FRUIT  \n", "12682            Special_Exotic    C1W      EXOTIC_FRUIT  \n", "12683            POPULAR_Exotic    C1W      EXOTIC_FRUIT  \n", "12684            CW_Spec_Exotic    C1W      EXOTIC_FRUIT  \n", "12685                    Pomelo    C1W      EXOTIC_FRUIT  \n", "17078             Tele-Shopping    R5W        Media Shop  \n", "17192           Handtools Extra    R3W   HANDTOOLS_EXTRA  \n", "17234                  wardrobe    R4W           BEDROOM  \n", "17242            Commode/Armoir    R4W           BEDROOM  \n", "17243                      beds    R4W           BEDROOM  \n", "17634       CS1 Handtools Extra    R3W   HANDTOOLS_EXTRA  \n", "24831                Kiwi_Fruit    C1W      EXOTIC_FRUIT  \n", "24832                Pineapples    C1W      EXOTIC_FRUIT  \n", "24833            Special_Exotic    C1W      EXOTIC_FRUIT  \n", "24834            POPULAR_Exotic    C1W      EXOTIC_FRUIT  \n", "24835            CW_Spec_Exotic    C1W      EXOTIC_FRUIT  \n", "24836                    Pomelo    C1W      EXOTIC_FRUIT  \n", "29210      measure/spirit level    R3W   HANDTOOLS_EXTRA  \n", "29211       screwdriver/wrenche    R3W   HANDTOOLS_EXTRA  \n", "29212            hammers/pliers    R3W   HANDTOOLS_EXTRA  \n", "29213           saw/file/knives    R3W   HANDTOOLS_EXTRA  \n", "29644  CS1 measure/spirit level    R3W   HANDTOOLS_EXTRA  \n", "29645   CS1 screwdriver/wrenche    R3W   HANDTOOLS_EXTRA  \n", "29646        CS1 hammers/pliers    R3W   HANDTOOLS_EXTRA  \n", "29647       CS1 saw/file/knives    R3W   HANDTOOLS_EXTRA  "]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["key.query(\"DRG_ID.str.contains('W')\")"]}, {"cell_type": "code", "execution_count": 65, "id": "ae0fa3d9-960f-4cee-a80f-c68b53eda2ee", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>store</th>\n", "      <th>Merchandising Group</th>\n", "      <th>total_modul_nr</th>\n", "      <th>country</th>\n", "      <th>Key</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>24273</th>\n", "      <td>14001</td>\n", "      <td>B0A</td>\n", "      <td>1.0</td>\n", "      <td>CZ</td>\n", "      <td>B0ACZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24274</th>\n", "      <td>14001</td>\n", "      <td>B0B</td>\n", "      <td>2.0</td>\n", "      <td>CZ</td>\n", "      <td>B0BCZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24275</th>\n", "      <td>14001</td>\n", "      <td>C0B</td>\n", "      <td>2.0</td>\n", "      <td>CZ</td>\n", "      <td>C0BCZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24276</th>\n", "      <td>14001</td>\n", "      <td>D0B</td>\n", "      <td>3.0</td>\n", "      <td>CZ</td>\n", "      <td>D0BCZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24277</th>\n", "      <td>14001</td>\n", "      <td>D0E</td>\n", "      <td>6.0</td>\n", "      <td>CZ</td>\n", "      <td>D0ECZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24331</th>\n", "      <td>14001</td>\n", "      <td>W6A</td>\n", "      <td>1.0</td>\n", "      <td>CZ</td>\n", "      <td>W6ACZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24332</th>\n", "      <td>14001</td>\n", "      <td>X0E</td>\n", "      <td>1.0</td>\n", "      <td>CZ</td>\n", "      <td>X0ECZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24333</th>\n", "      <td>14001</td>\n", "      <td>Z0A</td>\n", "      <td>1.0</td>\n", "      <td>CZ</td>\n", "      <td>Z0ACZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24334</th>\n", "      <td>14001</td>\n", "      <td>Z8O</td>\n", "      <td>2.0</td>\n", "      <td>CZ</td>\n", "      <td>Z8OCZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24335</th>\n", "      <td>14001</td>\n", "      <td>Z9O</td>\n", "      <td>1.0</td>\n", "      <td>CZ</td>\n", "      <td>Z9OCZ</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>63 rows × 5 columns</p>\n", "</div>"], "text/plain": ["       store Merchandising Group  total_modul_nr country    Key\n", "24273  14001                 B0A             1.0      CZ  B0ACZ\n", "24274  14001                 B0B             2.0      CZ  B0BCZ\n", "24275  14001                 C0B             2.0      CZ  C0BCZ\n", "24276  14001                 D0B             3.0      CZ  D0BCZ\n", "24277  14001                 D0E             6.0      CZ  D0ECZ\n", "...      ...                 ...             ...     ...    ...\n", "24331  14001                 W6A             1.0      CZ  W6ACZ\n", "24332  14001                 X0E             1.0      CZ  X0ECZ\n", "24333  14001                 Z0A             1.0      CZ  Z0ACZ\n", "24334  14001                 Z8O             2.0      CZ  Z8OCZ\n", "24335  14001                 Z9O             1.0      CZ  Z9OCZ\n", "\n", "[63 rows x 5 columns]"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["a.query(\"store == '14001'\")"]}, {"cell_type": "markdown", "id": "ab051556-9f63-4eef-a428-ba548c9e92f4", "metadata": {}, "source": ["### checkout products"]}, {"cell_type": "code", "execution_count": null, "id": "7b984736-50d2-4e5b-aba8-cc5a7bf33f15", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# checkout products\n", "srd_table = pd.read_csv(r\"\\\\huprgvmfs04.hu.tesco-europe.com\\SRD-közös\\Egyéb\\Balázsnak\\HU.txt\", sep='|')\n", "all_display_group = srd_table['Displaygroup_description'].unique().tolist()\n", "df = pd.DataFrame({'displays' : all_display_group})\n", "df_list = df[df['displays'].str.contains('ASCO') | df['displays'].str.contains('CHECKOUT')].sort_values(by=['displays'], ascending = True).reset_index(drop=True)['displays'].tolist()\n", "\n", "srd_table[srd_table['Displaygroup_description'].isin(df_list)]"]}, {"cell_type": "code", "execution_count": null, "id": "d59bf126-e625-4492-940c-02cffe453b2f", "metadata": {}, "outputs": [], "source": ["plano = pd.read_csv(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2022\\Model_Datasets\\For_DATASET\\2022_w14_w27\\plano\\FutureDatedShelfCap_All_Stores_20220824_010404.txt\", sep=\"|\",encoding = 'unicode_escape', low_memory=False)"]}, {"cell_type": "code", "execution_count": null, "id": "ea84e898-74ed-41b5-85d6-a58122c78ba2", "metadata": {}, "outputs": [], "source": ["all_display_group = plano['DRG name'].unique().tolist()\n", "df = pd.DataFrame({'displays' : all_display_group})\n", "df_list = df[df['displays'].str.contains('ASCO') | df['displays'].str.contains('CHECKOUT')].sort_values(by=['displays'], ascending = True).reset_index(drop=True)['displays'].tolist()\n", "\n", "plano[plano['DRG name'].str.upper().isin(df_list)]"]}, {"cell_type": "code", "execution_count": null, "id": "198868a0-df9f-4214-8259-6a6baa7b3d1c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "69b8df5f-152f-4e7b-a302-bbfaa13ccc9c", "metadata": {}, "outputs": [], "source": ["import pyarrow.parquet as pq\n", "\n", "hu_moduls= pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2022\\Model_Inputs\\hausdorf_modul_tables\\Hausdorf databases\\2023\\DB REPORT HU WEEK_33_2022(17_10_2022).xlsx\")\n", "for_pmg = pq.read_table(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2022\\Model_Datasets\\Repl_Dataset_2022_new_14w_27w.parquet\").to_pandas()"]}, {"cell_type": "code", "execution_count": null, "id": "3c9473d9-3156-4453-9a8a-52e78cf1373a", "metadata": {}, "outputs": [], "source": ["for_dep = for_pmg[['country','tpnb', 'dep']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "9d2f42cb-6102-4092-83a7-397336ae7bbe", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_columns', None)\n", "hu_moduls.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0d756ab6-34bf-457a-82af-125bfd0be8aa", "metadata": {}, "outputs": [], "source": ["srd_table['country'] = 'HU'\n", "srd_table.rename(columns={'Product_id' : 'tpnb'}, inplace=True)\n", "merged_a = srd_table.merge(for_dep, on=['country', 'tpnb'], how='left')\n", "merged_a = merged_a.query(\"dep.notnull()\")\n", "hu_moduls.rename(columns={'Merchandising Group' : 'Displaygroup'}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c089a099-01b0-4f17-80b5-7c0c47ce56c3", "metadata": {}, "outputs": [], "source": ["merged_a.head()"]}, {"cell_type": "code", "execution_count": null, "id": "969623b1-0c33-4c9b-b03e-666d052e4659", "metadata": {}, "outputs": [], "source": ["\n", "merged_a[['Displaygroup','Displaygroup_description','Product_Name', 'dep']].drop_duplicates().sort_values(by=['Displaygroup']).tail(50)"]}, {"cell_type": "code", "execution_count": null, "id": "d82bb2d1-1fd6-4d75-8ea4-bf504c098f5a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "directory = Path(\"C:/Users/<USER>/OneDrive - Tesco/Documents/Turn up the workin' !!!!!!!/#MODELS/###GA MODELS/#REPLENISHMENT\")\n", "excel_inputs_f = 'ReplModel_2022/Model_Inputs/Repl/Stores_Inputs_2022_Q1_VOL_2_wgll.xlsx'\n", "\n", "wh_prof_drivers = \"ReplModel_2022/Model_Inputs/WH/WH_profile_drivers.xlsx\"\n", "wh_most = \"ReplModel_2022/Model_Inputs/MOST/WH/WAREHOUSE MOST_Repl_2022_template.xlsb\"\n", "\n", "\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "markdown", "id": "d383a1c1-f179-4c73-836a-92e0f8199616", "metadata": {}, "source": ["##### WH Drivers & Profiles"]}, {"cell_type": "code", "execution_count": null, "id": "7be4e192-eb68-40ac-90b3-bd92e5184567", "metadata": {"tags": []}, "outputs": [], "source": ["# Initialize store_inputs and days\n", "store_inputs = pd.read_excel(directory/excel_inputs_f).iloc[: , :4]\n", "weekdays = ['Monday',\"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\" , \"Saturday\", \"Sunday\"]\n", "store_inputs['day'] = \"\"\n", "store_inputs['day'] = store_inputs['day'].apply(lambda x: weekdays)\n", "store_inputs = store_inputs.explode('day').drop_duplicates()\n", "\n", "# Initialize WH cases and deliveries to be one table\n", "wh_profile = pd.read_excel(directory/wh_prof_drivers, skiprows = 9).iloc[:,1:]\n", "wh_drivers = pd.read_excel(directory/wh_prof_drivers, 'Drivers',skiprows = 1 ).drop(['Plan size', 'Country', 'd', 'Store Name'], axis = 1)\n", "\n", "wh_cases_data = pd.read_excel(directory/wh_prof_drivers, 'cases_pmg').rename(\n", "    columns={'total_wh_cases' : 'Cases delivered',\n", "             'wh_cases_DTS' : 'Weekly direct cased delivered',\n", "             'wh_cases_DTW' : 'Weekly DC cased delivered',\n", "             'store':'Store'})\n", "\n", "wh_cases_data_pattisserie = pd.read_excel(directory/wh_prof_drivers, 'pattisserie').rename(\n", "    columns={'total_wh_cases' : 'Cases delivered',\n", "             'wh_cases_DTS' : 'Weekly direct cased delivered',\n", "             'wh_cases_DTW' : 'Weekly DC cased delivered',\n", "             'store':'Store', 'sec' : 'pmg'}).query('pmg == \"Patisserie_Cake\"')\n", "\n", "wh_cases_data = pd.concat([wh_cases_data, wh_cases_data_pattisserie]).fillna(0)\n", "wh_cases_data['Weekly direct beer cases delivered'] = np.where(wh_cases_data.pmg == 'BWS01', wh_cases_data['Weekly direct cased delivered'], 0)\n", "wh_cases_data['Weekly direct bakery cases delivered'] = np.where(wh_cases_data.pmg.str.contains('ISB'), wh_cases_data['Weekly direct cased delivered'], 0)\n", "wh_cases_data['Weekly direct patissery cases delivered'] = np.where(wh_cases_data.pmg.str.contains('Patisserie_Cake'), wh_cases_data['Weekly direct cased delivered'], 0)\n", "wh_cases_data['Weekly direct cigarettes cases delivered'] = np.where(wh_cases_data.pmg.str.contains('CIG'), wh_cases_data['Weekly direct cased delivered'], 0 )\n", "wh_cases_data['Weekly direct cosmetics cases delivered'] = np.where(wh_cases_data.pmg.str.contains('HEA'), wh_cases_data['Weekly direct cased delivered'], 0 )\n", "wh_cases_data['Produce crates back'] = np.where(wh_cases_data.pmg.str.contains('PRO'), wh_cases_data['Cases delivered'], 0 )\n", "wh_cases_data['Meat and poultry crates back'] = np.where(wh_cases_data.pmg.str.contains('MPC'), wh_cases_data['Cases delivered'] * 0.87 , 0 ) #from Gábor: 87% of cases = crates MPC 87% is a simple average of countries and PMGs\n", "\n", "wh_cases_data.drop(wh_cases_data[wh_cases_data['pmg'] == 'HDL01'].index, axis = 0, inplace = True)\n", "wh_cases_data['dep'] = wh_cases_data['pmg'].str[:3]\n", "wh_cases_data_grouped = wh_cases_data.groupby(['Store', 'day']).sum().reset_index()\n", "\n", "wh_deliveries_data = pd.read_excel(directory/wh_prof_drivers, 'deliveries').rename(\n", "    columns={'store' : 'Store',\n", "             'no_of_delivery_total' : 'Weekly deliveries',\n", "             'no_of_delivery_DTS' : 'Weekly direct deliveries',\n", "             'no_of_delivery_DTW' : 'Weekly DC deliveries',\n", "             'pallets' : 'Weekly DC pallets', 'rollcages' : 'Weekly DC rollcages'}).drop('country', axis = 1)\n", "Drivers = store_inputs.merge(wh_deliveries_data, on = ['Store', 'day'], how = 'left').merge(wh_cases_data_grouped, on = ['Store', 'day'], how = 'left')\n", "\n", "# Drivers sheet divided by 7 to get daily values and del the columns that drivers has\n", "wh_drivers_daily = wh_drivers.iloc[:,1:].applymap(lambda x: x/7)\n", "wh_drivers_daily.insert(0,\"Store\",wh_drivers.iloc[:,:1])\n", "\n", "for x in Drivers.iloc[:,5:].columns.tolist():\n", "    wh_drivers_daily.drop(x, axis = 1, inplace = True )\n", "    \n", "# Set up Drivers DataFrame\n", "Drivers = Drivers.merge(wh_drivers_daily, on = ['Store'], how = 'left')\n", "Drivers['Dep'] = 'WH'\n", "Drivers['Weekly deliveries'] = Drivers['Weekly DC deliveries'] + Drivers['Weekly direct deliveries'] + Drivers['Weekly other deliveries']\n", "Drivers['Total DC palletts + Rollcages'] = Drivers['Weekly DC pallets'] + Drivers['Weekly DC rollcages']\n", "Drivers['Weekly direct not beer, all other cases delivered'] = Drivers[['Weekly direct bakery cases delivered',\n", "                                                                   'Weekly direct patissery cases delivered',\n", "                                                                   'Weekly direct news and magz cases delivered',\n", "                                                                   'Weekly direct other GM and GRO cases delivered',\n", "                                                                   'Weekly direct other GM and GRO cases delivered (1 case = 1 item)',\n", "                                                                   'Weekly direct cigarettes cases delivered',\n", "                                                                   'Weekly direct sandwich cases delivered',\n", "                                                                   'Weekly direct cosmetics cases delivered']].sum(axis=1)\n", "Drivers['Weekly direct cased delivered'] = Drivers[['Weekly direct beer cases delivered','Weekly direct not beer, all other cases delivered']].sum(axis=1)\n", "Drivers['Weekly DC cased delivered'] = Drivers['Cases delivered'] - Drivers['Weekly direct cased delivered']\n", "Drivers['Total crates back'] = Drivers['Produce crates back'] + Drivers['Meat and poultry crates back']\n", "\n", "# Profile sheet-changing \"Y\" to 1 and \"N\" to 0\n", "wh_profile_y_to_1 = wh_profile.iloc[:,5:-3].applymap(lambda x:  1 if 'Y' in str(x) else 0)\n", "\n", "for x, y in zip(range(5), wh_profile.iloc[:,:5].columns):\n", "    \n", "    wh_profile_y_to_1.insert(x,wh_profile.iloc[:,:5].columns[x], wh_profile[y])\n", "    \n", "col_del = wh_profile_y_to_1.iloc[:,1:5].columns.tolist()\n", "wh_profile_y_to_1.drop(col_del, axis = 1, inplace=True)   \n", "\n", "# Drivers to Profile together\n", "Drivers = Drivers.merge(wh_profile_y_to_1, on = \"Store\", how = 'left')"]}, {"cell_type": "code", "execution_count": null, "id": "eddb986e-b38b-44b3-9699-efd45283c5df", "metadata": {"tags": []}, "outputs": [], "source": ["to_sum = Drivers.loc[:, ~Drivers.columns.isin(['day','Store', 'Country', 'Store Name', 'Plan Size'])].columns.tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "3bb700e5-d20d-4066-87d3-5a6ee4ac18d0", "metadata": {}, "outputs": [], "source": ["wh_drivers = Drivers.groupby(['Store', 'Country', 'Store Name', 'Plan Size'])[to_sum].sum().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "fab6932b-aac8-44c4-9bf2-557392d929b5", "metadata": {}, "outputs": [], "source": ["wh_drivers_excel=wh_drivers.iloc[:, :67]"]}, {"cell_type": "code", "execution_count": null, "id": "46f578cb-c58c-4072-b738-b584abf9693e", "metadata": {}, "outputs": [], "source": ["wh_drivers_excel.to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2022\\Model_Outputs\\wh_driver_2022.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "158767bb-2c1a-4017-8c17-c714e7c7ed05", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "    \n", "# Temporary\n", "wh_drivers_daily['day'] = ''\n", "wh_drivers_daily['day'] = wh_drivers_daily['day'].apply(lambda x: weekdays)\n", "wh_drivers_daily = wh_drivers_daily.explode('day').drop_duplicates()\n", "wh_drivers_daily['Dep'] = 'WH'\n", "wh_drivers_daily = wh_drivers_daily.merge(wh_profile_y_to_1, on = \"Store\", how = 'left')"]}, {"cell_type": "markdown", "id": "55efd161-4ea8-4564-8ede-756b9384d89c", "metadata": {}, "source": ["##### Time Values & Drivers"]}, {"cell_type": "code", "execution_count": null, "id": "8c9a8819-ece3-4c96-8179-468a4f3e0257", "metadata": {}, "outputs": [], "source": ["# Set up Most file\n", "activity_list = pd.read_excel(directory / wh_most,'Time Values', skiprows = 3)\n", "\n", "new_header = activity_list.iloc[0] #grab the first row for the header\n", "activity_list = activity_list[1:] #take the data less the header row\n", "activity_list.columns = new_header #set the header row as the df header\n", "\n", "cols = ['Activity_key_activities','Suboperation Description','Activity group','V F','DRIVER_1','DRIVER_2','FREQ2','DRIVER_3','DRIVER_4','PROFILE','RA','Head','Newspaper_Activity']\n", "cols2 = ['Activity_key_activities','Suboperation','Activity_Group','V_F','Driver_1','Driver_2','Freq_Driver_2','Driver_3','Driver_4','Profile','RA','Head','Newspaper_Activity']\n", "activity_list = activity_list[cols]\n", "for x, y in zip(cols, cols2):\n", "    activity_list.rename(columns={x:y},inplace=True)\n", "\n", "activity_list.dropna(subset=['Activity_key_activities'],inplace=True)\n", "activity_list.rename(columns={'Activity_key_activities':'Activity_key'},inplace=True)\n", "activity_list['Freq_Driver_2'] = activity_list['Freq_Driver_2'].replace(np.nan,0)\n", "activity_list = activity_list.replace(np.nan,'no_driver')\n", "\n", "# Adjusting activities to stores and days\n", "cols = activity_list.columns\n", "combined_rows = activity_list[cols].apply(lambda row: '|'.join(row.values.astype(str)), axis=1)\n", "combined_rows_values_list = list(combined_rows.values)\n", "\n", "time_values = store_inputs.copy()\n", "time_values['combined_rows'] = 0\n", "time_values['combined_rows'] = time_values['combined_rows'].apply(lambda x : combined_rows_values_list)\n", "time_values = time_values.explode('combined_rows').drop_duplicates()\n", "time_values[list(cols)] = time_values['combined_rows'].str.split('|', expand=True)\n", "time_values.drop([\"combined_rows\"], axis = 1, inplace = True)\n", "time_values['Dep'] = 'WH'\n", "\n", "#Creating Basic_times and Frequency df\n", "tv_freq = pd.read_excel(directory / wh_most,'Time Values')\n", "\n", "a = tv_freq[3:4].values[:,:14]\n", "tv_freq_columns_a = [item for sublist in a for item in sublist]\n", "tv_freq_columns_b = tv_freq.columns[14:35].tolist()\n", "c = tv_freq[3:4].values[:,35:]\n", "tv_freq_columns_c = [item for sublist in c for item in sublist]\n", "tv_freq_columns_ALL = tv_freq_columns_a + tv_freq_columns_b + tv_freq_columns_c\n", "\n", "tv_freq = tv_freq.iloc[4:, :]\n", "tv_freq.columns = tv_freq_columns_ALL\n", "\n", "tv_freq_3_columns = tv_freq[['Activity_key_activities', 'RA', 'Head']]\n", "\n", "act = ['Activity_key_activities', 'RA', 'Head']\n", "place = [36,37,38]\n", "\n", "for x, y in zip(place, act):\n", "    tv_freq.drop([y], axis=1).insert(x, y, tv_freq_3_columns[y] )\n", "\n", "cols = ['Activity_key_activities'] + tv_freq_columns_b \n", "cols2 = ['Activity_key_activities'] + tv_freq_columns_b \n", "tv_freq = tv_freq[cols]\n", "for x, y in zip(cols, cols2):\n", "    tv_freq.rename(columns={x:y},inplace=True)\n", "    \n", "tv_freq.drop(['Unnamed: 18'], axis = 1, inplace=True)\n", "\n", "times = tv_freq.melt(id_vars = tv_freq.iloc[:,:5], var_name= 'Country', value_name='freq').melt(\n", "    id_vars = ['Activity_key_activities','Country', 'freq'],var_name= 'Format', value_name='basic_time')\n", "\n", "times['Country'] = times['Country'].apply(lambda x: x[:-3][-2:])\n", "times = times[times['Country'] != 'PL'].rename(columns={'Activity_key_activities' : 'Activity_key'})\n", "times['Dep'] = 'WH'\n", "times = times.drop_duplicates()\n", "times[['freq','basic_time']] = times[['freq','basic_time']].astype('float')\n", "\n", "store_inputs_format = pd.read_excel(directory/excel_inputs_f)\n", "time_values = time_values.merge(store_inputs_format[['Store', 'Format']], on = 'Store', how = 'left')\n", "\n", "df_times = time_values.merge(times, on=['Activity_key','Country','Format', 'Dep'], how='left')\n", "\n", "drivers_to_time_values = Drivers.melt(id_vars=['Store', 'Dep', 'day'], var_name=['drivers'])\n", "drivers_to_time_values.value = pd.to_numeric(drivers_to_time_values.value, errors='coerce').replace(np.nan, 0)\n", "\n", "# VlookUp drivers to activities\n", "d_values = [1,2,3,4] # Here we VLOOKUP driver values between df_times and drivers_df. We have 4 drivers\n", "driver_initial_name = 'drivers'\n", "value_initial_name = 'value'\n", "for x in d_values:\n", "    driver_new_name = 'Driver_' + str(x)\n", "    value_new_name = 'Driver_' + str(x) + '_value'\n", "    drivers_to_time_values.rename(columns={driver_initial_name: driver_new_name}, inplace=True)\n", "    drivers_to_time_values.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "    df_times = df_times.merge(drivers_to_time_values, on=['Store', 'Dep', 'day', driver_new_name], how='left')\n", "    df_times[value_new_name] = df_times[value_new_name].replace(np.nan,0) # it seems we need NaN there\n", "    driver_initial_name = driver_new_name\n", "    value_initial_name = value_new_name\n", "driver_new_name = 'Profile' # Profiles\n", "value_new_name = 'Profile_value'\n", "drivers_to_time_values.rename(columns={driver_initial_name: driver_new_name}, inplace=True)\n", "drivers_to_time_values.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "\n", "df_times = df_times.merge(drivers_to_time_values, on=['Store', 'Dep','day', driver_new_name], how='left')\n", "df_times[value_new_name] = df_times[value_new_name].replace(np.nan,0) # it seems we need NaN there\n", "drivers_to_time_values.rename(columns={driver_new_name: 'drivers'}, inplace=True)\n", "drivers_to_time_values.rename(columns={value_new_name: 'value'}, inplace=True)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "5da145a6-7971-41da-af0d-cc6449894c3f", "metadata": {}, "outputs": [], "source": ["# what if hulladek<PERSON>zelés pilot\n", "\n", "stores = [41420, 41029, 44013, 44016, 44057, 41580, 41960, 41620, 41690, 44024, 41026, 41750]\n", "act = ['Dangerous waste shipping (meat, dairy): walk to area', 'Dangerous waste shipping (meat, dairy): move products', 'Dangerous waste shipping (meat, dairy): Communication with driver, open and close gate', 'Dangerous waste shipping (meat, dairy): Wait for driver to pack fresh waste', 'Dangerous waste shipping (meat, dairy): walk back from area']\n", "df_times.loc[(df_times.Store.isin(stores)) & (df_times.Suboperation.isin(act)), 'freq'] = 400\n", "df_times.loc[(df_times.Store.isin([44057])) & (df_times.Suboperation.isin(act)), 'freq'] = 500\n", "\n", "\n", "act2 = ['Using the compressor - remaining waste']\n", "df_times.loc[(df_times.Store.isin(stores)) & (df_times.Suboperation.isin(act2)), 'freq'] = 90\n"]}, {"cell_type": "markdown", "id": "a8bca941-4544-4d5f-8ef4-758f27e89b2c", "metadata": {}, "source": ["#### HoursCalculation"]}, {"cell_type": "code", "execution_count": null, "id": "b9a39982-4977-4aed-89e0-5f274802588c", "metadata": {}, "outputs": [], "source": ["def CalcModelHours(calc_hours):\n", "\n", "\n", "\n", "  calc_hours.Driver_3_value = np.where((calc_hours.Driver_3_value==0)&(calc_hours.Driver_3=='no_driver'),1,calc_hours.Driver_3_value) # here we have multiplicators and as we cannot divide by 0, I changed the zeros to 1\n", "  calc_hours.Driver_4_value = np.where((calc_hours.Driver_4_value==0)&(calc_hours.Driver_4=='no_driver'),1,calc_hours.Driver_4_value)\n", "  calc_hours['hours'] = (((calc_hours.Driver_1_value+(calc_hours.Driver_2_value*calc_hours.Freq_Driver_2/100))*calc_hours.Driver_3_value*calc_hours.Driver_4_value)*calc_hours.basic_time/60*calc_hours.freq/100)\n", "  calc_hours['hours'] = np.where((calc_hours.Profile_value==0)&(calc_hours.Profile!='no_driver'), 0, calc_hours['hours'])\n", "\n", "\n", "  return calc_hours\n", "\n", "hours_df = df_times.copy()\n", "hours_df[['Freq_Driver_2', 'Head']] = hours_df[['Freq_Driver_2', 'Head']].astype('float')\n", "hours_df['RA_time'] = np.where(hours_df.RA=='Y',hours_df.basic_time*(4/100),0)\n", "hours_df['basic_time'] = hours_df.basic_time+hours_df.RA_time\n", "hours_df.drop(columns={'RA_time'}, axis=1, inplace=True)\n", "hours_df['Driver_3_value'] = hours_df['Driver_3_value'] * 7    #it is important if you do daily model\n", "hours_df = CalcModelHours(hours_df)\n", "\n", "# Headcount calculation\n", "headcount_hrs = hours_df.loc[hours_df.Head==1, ('Store', 'Dep','day','Head','hours')]\n", "headcount_hrs = headcount_hrs.groupby(['Store', 'Dep', 'day'])['hours'].sum().reset_index()\n", "headcount_hrs['Headcount'] = np.where((((headcount_hrs.hours/8)-round(headcount_hrs.hours/8))>0.05),np.ceil(headcount_hrs.hours/8) / 7,round(headcount_hrs.hours/8) / 7)\n", "headcount_hrs.drop(columns={'hours'}, axis=1, inplace=True)\n", "hours_df = hours_df.merge(headcount_hrs, on=['Store', 'Dep', 'day'], how='left')\n", "hours_df['Driver_1_value'] = np.where(hours_df.Driver_1=='Headcount', hours_df.Headcount, hours_df['Driver_1_value'])\n", "hours_df['Driver_2_value'] = np.where(hours_df.Driver_2=='Headcount', hours_df.Headcount, hours_df['Driver_2_value'])\n", "hours_df['Driver_3_value'] = np.where(hours_df.Driver_3=='Headcount', hours_df.Headcount, hours_df['Driver_3_value'])\n", "hours_df['Driver_4_value'] = np.where(hours_df.Driver_4=='Headcount', hours_df.Headcount, hours_df['Driver_4_value'])\n", "hours_df.drop(columns={'Headcount'}, axis=1, inplace=True)\n", "hours_df = CalcModelHours(hours_df)\n", "hours_df['Yearly GBP'] = 5*hours_df.hours*52"]}, {"cell_type": "code", "execution_count": null, "id": "296bd561-8b5f-4c61-abe0-111dfc6df9b3", "metadata": {}, "outputs": [], "source": ["stores = [41420, 41029, 44013, 44016, 44057, 41580, 41960, 41620, 41690, 44024, 41026, 41750]\n", "as_is_hours = hours_df[hours_df.Store.isin(stores)].groupby(['Store', 'Suboperation', 'Activity_Group' ])['hours'].sum().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "ce7707fe-fc5c-4707-bf0f-6da2c835e765", "metadata": {}, "outputs": [], "source": ["stores = [41420, 41029, 44013, 44016, 44057, 41580, 41960, 41620, 41690, 44024, 41026, 41750]\n", "new_hours = hours_df[hours_df.Store.isin(stores)].groupby(['Store', 'Suboperation', 'Activity_Group' ])['hours'].sum().reset_index()\n", "new_hours.rename(columns={'hours' : 'new_hours'}, inplace = True)"]}, {"cell_type": "code", "execution_count": null, "id": "f33f37c7-b5fc-4546-b634-fd9b2974507c", "metadata": {}, "outputs": [], "source": ["as_is_hours.rename(columns={'hours' : 'old_hours'}, inplace = True)"]}, {"cell_type": "code", "execution_count": null, "id": "0972070d-381f-4708-b46b-1c07e5a954a8", "metadata": {}, "outputs": [], "source": ["diff = as_is_hours.merge(new_hours, on = ['Store', 'Suboperation', 'Activity_Group'], how = 'left')\n", "diff['diff'] = diff['new_hours'] - diff['old_hours']\n", "diff = diff[diff['diff'] != 0]\n", "results = diff.groupby(['Store', 'Activity_Group'])['diff'].sum().to_frame('diff_hours')\n", "results = results.merge(hours_df[['Store', 'Store Name']].drop_duplicates(), on = 'Store', how = 'left')\n", "cols = results.columns.tolist()\n", "cols=  cols[:1] + cols[-1:] + cols[1:2]\n", "results = results[cols]\n", "results.loc['Total'] = results[['diff_hours']].sum().reindex(results.columns, fill_value='')\n", "results"]}, {"cell_type": "code", "execution_count": null, "id": "a130d8ff-5206-4962-9c66-52b84df50587", "metadata": {}, "outputs": [], "source": ["results.loc['Total'] = results[['diff_hours']].sum().reindex(results.columns, fill_value='')"]}, {"cell_type": "code", "execution_count": null, "id": "3bf2a4d6-45dd-466e-a73a-6ae62a9237a0", "metadata": {}, "outputs": [], "source": ["results"]}, {"cell_type": "code", "execution_count": null, "id": "be69d4fd-e417-4945-b9d1-632776087d91", "metadata": {}, "outputs": [], "source": ["results.to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2022\\Model_Outputs\\Hulladekkezelés_pilot_extra_hours.xlsx\", index = False)"]}, {"cell_type": "code", "execution_count": null, "id": "74a54709-dd91-4575-8155-8b1fdecb0bb8", "metadata": {}, "outputs": [], "source": ["hours_df[(hours_df.Store == 41420) & (hours_df.Suboperation.str.contains(\"Communication with driver, open and close gate\")) & (hours_df.Driver_1 == 'Technical driver')].head()"]}, {"cell_type": "code", "execution_count": null, "id": "ad4f5f0b-3c02-4c4c-92dc-71317be29d66", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_columns', None)\n", "hours_df[(hours_df.Driver_1_value == 0) &(hours_df.Store == 41520)]['Driver_1'].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "7b8feba3-5aa1-4b20-92ec-8cb18917352c", "metadata": {}, "outputs": [], "source": ["hours_df.groupby('Driver_1')['Driver_1_value'].sum().reset_index().query('Driver_1_value == 0')"]}, {"cell_type": "code", "execution_count": null, "id": "9f6c5f22-db46-4ced-bc15-1c10b05f63f0", "metadata": {}, "outputs": [], "source": ["hours_df.groupby('Driver_3')['Driver_3_value'].sum().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "2a383eee-4e4b-47fb-8a1f-e0162f457af1", "metadata": {}, "outputs": [], "source": ["hours_df[hours_df.Driver_3 == 'Steps: ramp to department goods receiving area'\t]['Driver_3_value'].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "8d9f6665-1e40-47fd-9ebe-566d1564efb2", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_columns', None)\n", "hours_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4d2f31aa-413c-43f5-84d0-9d0caf5c4647", "metadata": {}, "outputs": [], "source": ["hours_df[hours_df.Store == 41520].to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2022\\Model_Outputs\\wh_insight_41520_new.xlsx\", index = False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}