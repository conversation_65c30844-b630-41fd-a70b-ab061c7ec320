import paramiko
import pandas as pd
import os
import time
import csv
from pathlib import Path
from typing import List, Tuple, Set
from tqdm import tqdm
import threading
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from rich.console import Console
from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn, FileSizeColumn, TransferSpeedColumn
from rich.table import Table
from rich.live import Live
from rich.panel import Panel
from rich.text import Text
import signal
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ssh_downloader.log'),
        logging.StreamHandler()
    ]
)

console = Console()

class SSHDownloaderEnhanced:
    def __init__(self, hostname: str, username: str, password: str):
        self.hostname = hostname
        self.username = username
        self.password = password
        self.ssh_client = None
        self.ftp_client = None
        self.stats = {
            'total_files': 0,
            'downloaded_files': 0,
            'failed_files': 0,
            'total_size': 0,
            'start_time': None,
            'errors': []
        }
        self.cancelled = False
        
        # Handle Ctrl+C gracefully
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully"""
        self.cancelled = True
        console.print("\n[yellow]Cancelling download... Please wait for cleanup.[/yellow]")
        self.cleanup()
        sys.exit(0)
    
    def connect(self) -> bool:
        """Establish SSH connection with retry logic"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with console.status(f"[bold green]Connecting to {self.hostname}... (Attempt {attempt + 1}/{max_retries})"):
                    self.ssh_client = paramiko.SSHClient()
                    self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                    self.ssh_client.connect(
                        hostname=self.hostname, 
                        username=self.username, 
                        password=self.password,
                        timeout=30
                    )
                    self.ftp_client = self.ssh_client.open_sftp()
                    
                console.print(f"[bold green]✓ Connected to {self.hostname}[/bold green]")
                return True
                
            except Exception as e:
                if attempt < max_retries - 1:
                    console.print(f"[yellow]Connection attempt {attempt + 1} failed: {str(e)}[/yellow]")
                    time.sleep(2)
                else:
                    console.print(f"[bold red]✗ Failed to connect after {max_retries} attempts: {str(e)}[/bold red]")
                    return False
        return False
    
    def cleanup(self):
        """Clean up connections"""
        if self.ftp_client:
            self.ftp_client.close()
        if self.ssh_client:
            self.ssh_client.close()
    
    def get_file_size(self, remote_path: str) -> int:
        """Get remote file size"""
        try:
            return self.ftp_client.stat(remote_path).st_size
        except:
            return 0
    
    def download_with_progress(self, remote_path: str, local_path: str, progress: Progress, task_id: TaskID) -> bool:
        """Download file with progress tracking"""
        try:
            file_size = self.get_file_size(remote_path)
            progress.update(task_id, total=file_size)
            
            def progress_callback(transferred, total):
                if not self.cancelled:
                    progress.update(task_id, completed=transferred)
            
            # Use progress callback for SFTP get
            self.ftp_client.get(remote_path, local_path, callback=progress_callback)
            return True
            
        except Exception as e:
            self.stats['errors'].append(f"Failed to download {remote_path}: {str(e)}")
            return False
    
    def create_status_table(self) -> Table:
        """Create a status table for live display"""
        table = Table(title="Download Status", show_header=True, header_style="bold magenta")
        table.add_column("Metric", style="cyan", no_wrap=True)
        table.add_column("Value", style="white")
        
        elapsed = time.time() - self.stats['start_time'] if self.stats['start_time'] else 0
        eta = self._calculate_eta(elapsed)
        
        table.add_row("Total Files", str(self.stats['total_files']))
        table.add_row("Downloaded", f"[green]{self.stats['downloaded_files']}[/green]")
        table.add_row("Failed", f"[red]{self.stats['failed_files']}[/red]")
        table.add_row("Success Rate", f"{self._calculate_success_rate():.1f}%")
        table.add_row("Elapsed Time", self._format_time(elapsed))
        table.add_row("ETA", eta)
        table.add_row("Avg Speed", self._calculate_avg_speed())
        
        return table
    
    def _calculate_eta(self, elapsed_time: float) -> str:
        """Calculate estimated time of arrival"""
        if self.stats['downloaded_files'] == 0:
            return "Calculating..."
        
        avg_time_per_file = elapsed_time / self.stats['downloaded_files']
        remaining_files = self.stats['total_files'] - self.stats['downloaded_files']
        eta_seconds = avg_time_per_file * remaining_files
        
        return self._format_time(eta_seconds)
    
    def _calculate_success_rate(self) -> float:
        """Calculate success rate percentage"""
        total_processed = self.stats['downloaded_files'] + self.stats['failed_files']
        if total_processed == 0:
            return 0.0
        return (self.stats['downloaded_files'] / total_processed) * 100
    
    def _calculate_avg_speed(self) -> str:
        """Calculate average download speed"""
        elapsed = time.time() - self.stats['start_time'] if self.stats['start_time'] else 0
        if elapsed == 0 or self.stats['total_size'] == 0:
            return "0 B/s"
        
        speed = self.stats['total_size'] / elapsed
        return self._format_bytes(speed) + "/s"
    
    def _format_time(self, seconds: float) -> str:
        """Format seconds into human readable time"""
        if seconds < 60:
            return f"{seconds:.0f}s"
        elif seconds < 3600:
            return f"{seconds//60:.0f}m {seconds%60:.0f}s"
        else:
            return f"{seconds//3600:.0f}h {(seconds%3600)//60:.0f}m"
    
    def _format_bytes(self, bytes_val: float) -> str:
        """Format bytes into human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_val < 1024.0:
                return f"{bytes_val:.1f} {unit}"
            bytes_val /= 1024.0
        return f"{bytes_val:.1f} TB"
    
    def parallel_download(self, download_tasks: List[Tuple[str, str]], max_workers: int = 3) -> List[bool]:
        """Download multiple files in parallel"""
        results = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TransferSpeedColumn(),
            TimeRemainingColumn(),
            console=console,
            transient=True
        ) as progress:
            
            # Create tasks for each download
            tasks = {}
            for remote_path, local_path in download_tasks:
                filename = os.path.basename(remote_path)
                task_id = progress.add_task(f"[cyan]{filename}", total=None)
                tasks[task_id] = (remote_path, local_path)
            
            # Use ThreadPoolExecutor for parallel downloads
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_task = {}
                
                for task_id, (remote_path, local_path) in tasks.items():
                    future = executor.submit(
                        self.download_with_progress, 
                        remote_path, 
                        local_path, 
                        progress, 
                        task_id
                    )
                    future_to_task[future] = task_id
                
                for future in as_completed(future_to_task):
                    if self.cancelled:
                        break
                        
                    task_id = future_to_task[future]
                    try:
                        result = future.result()
                        results.append(result)
                        
                        if result:
                            self.stats['downloaded_files'] += 1
                            progress.update(task_id, description=f"[green]✓ {tasks[task_id][0].split('/')[-1]}")
                        else:
                            self.stats['failed_files'] += 1
                            progress.update(task_id, description=f"[red]✗ {tasks[task_id][0].split('/')[-1]}")
                            
                    except Exception as e:
                        self.stats['failed_files'] += 1
                        self.stats['errors'].append(f"Task failed: {str(e)}")
                        results.append(False)
        
        return results
    
    def smart_retry_logic(self, wp_working_output: Path, saved_name: str, what_to_download: str, max_retries: int = 3) -> bool:
        """Smart retry logic for failed downloads"""
        retry_count = 0
        
        while retry_count < max_retries:
            failed_files = self._check_failed_downloads(wp_working_output, saved_name, what_to_download)
            
            if not failed_files:
                console.print("[bold green]✓ All files downloaded successfully![/bold green]")
                return True
            
            retry_count += 1
            console.print(f"\n[yellow]Retry attempt {retry_count}/{max_retries} for {len(failed_files)} files[/yellow]")
            
            # Display failed files
            if failed_files:
                table = Table(title="Files to Retry", show_header=True)
                table.add_column("File", style="cyan")
                table.add_column("Reason", style="yellow")
                
                for file_info in failed_files:
                    table.add_row(file_info['name'], file_info['reason'])
                
                console.print(table)
            
            # Retry failed downloads
            retry_tasks = [(f['remote_path'], f['local_path']) for f in failed_files if 'remote_path' in f]
            
            if retry_tasks:
                self.parallel_download(retry_tasks, max_workers=2)
            
            time.sleep(5)  # Wait before next retry
        
        if retry_count >= max_retries:
            console.print(f"[bold red]✗ Max retries ({max_retries}) reached. Some files may have failed.[/bold red]")
            return False
        
        return True
    
    def _check_failed_downloads(self, wp_working_output: Path, saved_name: str, what_to_download: str) -> List[dict]:
        """Check for failed downloads based on file size and other criteria"""
        failed_files = []
        
        for root, dirs, files in os.walk(wp_working_output / saved_name):
            for file in files:
                file_path = Path(root) / file
                
                if file.endswith('.zip'):
                    file_size = file_path.stat().st_size
                    
                    # Check if file is too small (likely corrupted)
                    if file_size < 400 and "dotcom" not in file:
                        failed_files.append({
                            'name': file,
                            'reason': f'File too small ({file_size} bytes)',
                            'local_path': str(file_path)
                        })
                    
                    # Additional checks can be added here
                    # e.g., check file integrity, specific content validation
        
        return failed_files

@rmf.timeit  # Keeping your original decorator
def ssh_downloader_enhanced(what_to_download: str, wp_working_output: str, saved_name: str, 
                           stores: tuple, hostname: str, password: str, max_parallel_downloads: int = 3):
    """
    Enhanced SSH downloader with fancy progress indicators and improved efficiency
    
    Args:
        what_to_download: Type of data to download
        wp_working_output: Working directory path
        saved_name: Name for saved files
        stores: Tuple of store numbers
        hostname: SSH hostname
        password: SSH password
        max_parallel_downloads: Maximum parallel downloads (default: 3)
    """
    
    wp_working_output = Path(wp_working_output)
    only_store_s = len(stores) < 10
    
    # Initialize enhanced downloader
    downloader = SSHDownloaderEnhanced(hostname, 'phrubos', password)
    downloader.stats['start_time'] = time.time()
    
    # Create output directory
    (wp_working_output / saved_name).mkdir(parents=True, exist_ok=True)
    
    # Display initial info
    console.print(Panel.fit(
        f"[bold blue]SSH Downloader Enhanced[/bold blue]\n"
        f"[cyan]Target:[/cyan] {what_to_download}\n"
        f"[cyan]Stores:[/cyan] {len(stores)} stores\n"
        f"[cyan]Mode:[/cyan] {'Store-specific' if only_store_s else 'All groups'}\n"
        f"[cyan]Parallel Downloads:[/cyan] {max_parallel_downloads}",
        title="Configuration"
    ))
    
    # Connect to SSH
    if not downloader.connect():
        return False
    
    try:
        # Map stores to countries
        countries = map_tuple_to_countries(stores)
        console.print(f"[bold cyan]Processing countries: {', '.join(countries)}[/bold cyan]")
        
        # Main download process with live status
        with Live(downloader.create_status_table(), refresh_per_second=2) as live:
            
            for country in countries:
                if downloader.cancelled:
                    break
                    
                console.print(f"\n[bold yellow]Processing {what_to_download} for {country}[/bold yellow]")
                
                # Setup paths
                folder_name = what_to_download
                output_folder = f"/home/<USER>/{folder_name}/output/"
                main_folder = f"/home/<USER>/{folder_name}/"
                
                # Upload SQL file if needed
                if what_to_download == 'losses':
                    downloader.ftp_client.put(
                        str(wp_working_output / "losses_download.sql"), 
                        main_folder + "losses_download.sql"
                    )
                
                # Process based on mode
                if not only_store_s:
                    csv_id = process_all_groups(downloader, country, main_folder, wp_working_output, saved_name, what_to_download)
                else:
                    csv_id = process_store_specific(downloader, country, main_folder, wp_working_output, saved_name, stores, folder_name)
                
                downloader.stats['total_files'] += csv_id
                
                # Download files with progress tracking
                download_country_files(downloader, country, output_folder, wp_working_output, saved_name, csv_id, only_store_s)
                
                # Update live display
                live.update(downloader.create_status_table())
        
        # Smart retry logic
        console.print("\n[bold blue]Checking for failed downloads...[/bold blue]")
        downloader.smart_retry_logic(wp_working_output, saved_name, what_to_download)
        
        # Process downloaded files
        process_downloaded_files(wp_working_output, saved_name, what_to_download)
        
        # Display final summary
        display_final_summary(downloader)
        
    except Exception as e:
        console.print(f"[bold red]Error during download: {str(e)}[/bold red]")
        logging.error(f"Download error: {str(e)}")
        return False
    
    finally:
        downloader.cleanup()
    
    return True

def process_all_groups(downloader, country, main_folder, wp_working_output, saved_name, what_to_download):
    """Process all groups for a country"""
    # Download parameters file
    downloader.ftp_client.get(
        main_folder + f"parameters_all_groups_{country}.csv", 
        wp_working_output / saved_name / f"parameters_all_groups_{country}.csv"
    )
    
    # Calculate number of rounds
    csv_id = pd.read_csv(
        wp_working_output / saved_name / f"parameters_all_groups_{country}.csv", 
        sep="|", 
        names=['country','id', 'stores']
    )['id'].count()
    
    if what_to_download in ['srd','losses', 'item_sold_dotcom', 'item_sold', 'cases', 'stock']:
        csv_id = csv_id - 1
    
    console.print(f"[green]Found {csv_id} processing rounds for {country}[/green]")
    
    # Clean output directory and start processing
    downloader.ssh_client.exec_command(f'rm -f {main_folder}output/*')
    downloader.ssh_client.exec_command(f"sh /home/<USER>/{what_to_download}/start_{country}")
    
    return csv_id

def process_store_specific(downloader, country, main_folder, wp_working_output, saved_name, stores, folder_name):
    """Process specific stores for a country"""
    selected_stores_set = set(map(str, stores))
    
    # Download template file
    downloader.ftp_client.get(
        f"/home/<USER>/{folder_name}/parameters_all_groups_{country}_store_spec.csv",
        wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv"
    )
    
    file_path = wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv"
    
    # Categorize stores by country
    cz_stores = sorted(store for store in selected_stores_set if store.startswith('1') and country == "CZ")
    sk_stores = sorted(store for store in selected_stores_set if store.startswith('2') and country == "SK")
    hu_stores = sorted(store for store in selected_stores_set if store.startswith('4') and country == "HU")
    
    # Write filtered stores to CSV
    with open(file_path, mode='w', newline='') as file:
        writer = csv.writer(file, delimiter='|')
        if cz_stores:
            writer.writerow(['CZ', '1', ','.join(cz_stores)])
        if sk_stores:
            writer.writerow(['SK', '1', ','.join(sk_stores)])
        if hu_stores:
            writer.writerow(['HU', '1', ','.join(hu_stores)])
    
    # Upload modified file
    downloader.ftp_client.put(
        wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv",
        f"/home/<USER>/{folder_name}/parameters_all_groups_{country}_store_spec.csv"
    )
    
    # Download updated file and get count
    downloader.ftp_client.get(
        main_folder + f"parameters_all_groups_{country}_store_spec.csv",
        wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv"
    )
    
    csv_id = pd.read_csv(
        wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv",
        sep="|", 
        names=['country','id', 'stores']
    )['id'].count()
    
    console.print(f"[green]Found {csv_id} processing rounds for {country} (store-specific)[/green]")
    
    # Clean output directory and start processing
    downloader.ssh_client.exec_command(f'rm -f {main_folder}output/*')
    downloader.ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}_store_spec")
    
    return csv_id

def download_country_files(downloader, country, output_folder, wp_working_output, saved_name, csv_id, only_store_s):
    """Download files for a specific country with enhanced progress tracking"""
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.1f}%"),
        TimeRemainingColumn(),
        console=console
    ) as progress:
        
        task = progress.add_task(f"[cyan]Downloading {country} files", total=csv_id)
        
        counter = 0
        consecutive_empty_checks = 0
        max_empty_checks = 10
        
        while counter < csv_id and not downloader.cancelled:
            try:
                file_list = downloader.ftp_client.listdir(output_folder)
                zip_files = [f for f in file_list if f.endswith(".zip")]
                
                if zip_files:
                    consecutive_empty_checks = 0
                    
                    for zip_file in zip_files:
                        remote_path = output_folder + zip_file
                        local_path = wp_working_output / saved_name / zip_file
                        
                        # Download file
                        downloader.ftp_client.get(remote_path, local_path)
                        
                        # Remove from server
                        downloader.ftp_client.remove(remote_path)
                        
                        counter += 1
                        progress.update(task, completed=counter)
                        
                        console.print(f"[green]✓ Downloaded: {zip_file}[/green]")
                        
                        # Update stats
                        downloader.stats['downloaded_files'] += 1
                        downloader.stats['total_size'] += local_path.stat().st_size
                        
                        if counter >= csv_id:
                            break
                else:
                    consecutive_empty_checks += 1
                    if consecutive_empty_checks >= max_empty_checks:
                        console.print(f"[yellow]No new files found after {max_empty_checks} checks. Moving on...[/yellow]")
                        break
                    
                    progress.update(task, description=f"[yellow]Waiting for files... ({consecutive_empty_checks}/{max_empty_checks})")
                    
                # Adaptive sleep based on mode
                sleep_time = 2 if only_store_s else 10
                time.sleep(sleep_time)
                
            except Exception as e:
                console.print(f"[red]Error downloading files: {str(e)}[/red]")
                downloader.stats['errors'].append(f"Download error for {country}: {str(e)}")
                break

def process_downloaded_files(wp_working_output: Path, saved_name: str, what_to_download: str):
    """Process downloaded files into final format"""
    console.print("\n[bold blue]Processing downloaded files...[/bold blue]")
    
    csv_files = [f for f in os.listdir(wp_working_output / saved_name) 
                 if f.endswith('.zip') and what_to_download in f]
    
    if not csv_files:
        console.print("[yellow]No files found to process[/yellow]")
        return
    
    try:
        with console.status("[bold green]Combining files..."):
            if what_to_download == "losses":
                ce_df = pd.concat([
                    pd.read_csv(os.path.join(wp_working_output / saved_name, f), 
                               sep=',', skiprows=6, skipfooter=1, engine='python') 
                    for f in csv_files
                ])
                
                # Save as parquet
                output_file = wp_working_output / saved_name / f"{what_to_download}_{saved_name}_raw_data.parquet"
                ce_df.to_parquet(output_file, compression="gzip")
                
            elif what_to_download == "srd":
                ce_df = pd.concat([
                    pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',') 
                    for f in csv_files
                ])
                
            elif what_to_download not in ("losses", "srd"):
                ce_df = pd.concat([
                    pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',') 
                    for f in csv_files
                ])
        
        console.print(f"[bold green]✓ Successfully processed {len(csv_files)} files[/bold green]")
        console.print(f"[cyan]Final dataset shape: {ce_df.shape}[/cyan]")
        
    except Exception as e:
        console.print(f"[bold red]Error processing files: {str(e)}[/bold red]")

def display_final_summary(downloader):
    """Display final download summary"""
    elapsed_time = time.time() - downloader.stats['start_time']
    
    summary_table = Table(title="Download Summary", show_header=True, header_style="bold green")
    summary_table.add_column("Metric", style="cyan", no_wrap=True)
    summary_table.add_column("Value", style="white")
    
    summary_table.add_row("Total Files", str(downloader.stats['total_files']))
    summary_table.add_row("Successfully Downloaded", f"[green]{downloader.stats['downloaded_files']}[/green]")
    summary_table.add_row("Failed Downloads", f"[red]{downloader.stats['failed_files']}[/red]")
    summary_table.add_row("Success Rate", f"{downloader._calculate_success_rate():.1f}%")
    summary_table.add_row("Total Size", downloader._format_bytes(downloader.stats['total_size']))
    summary_table.add_row("Total Time", downloader._format_time(elapsed_time))
    summary_table.add_row("Average Speed", downloader._calculate_avg_speed())
    
    console.print("\n")
    console.print(summary_table)
    
    # Display errors if any
    if downloader.stats['errors']:
        console.print("\n[bold red]Errors encountered:[/bold red]")
        for error in downloader.stats['errors'][-5:]:  # Show last 5 errors
            console.print(f"[red]• {error}[/red]")
        
        if len(downloader.stats['errors']) > 5:
            console.print(f"[yellow]... and {len(downloader.stats['errors']) - 5} more errors (check log file)[/yellow]")

def map_tuple_to_countries(input_tuple):
    """Map store numbers to countries based on starting digit"""
    country_codes = {
        '1': 'CZ',
        '2': 'SK', 
        '4': 'HU'
    }
    
    result = []
    for number in input_tuple:
        starting_digit = str(number)[0]
        if starting_digit in country_codes:
            country = country_codes[starting_digit]
            if country not in result:
                result.append(country)
    
    return result

# Example usage:
if __name__ == "__main__":
    # Example call with enhanced parameters
    ssh_downloader_enhanced(
        what_to_download="losses",
        wp_working_output="/path/to/working/directory", 
        saved_name="my_download_session",
        stores=(1001, 1002, 2001, 4001),
        hostname="your.ssh.server.com",
        password="your_password",
        max_parallel_downloads=5
    )