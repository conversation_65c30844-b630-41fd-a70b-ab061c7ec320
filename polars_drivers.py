import pandas as pd
import numpy as np
from pathlib import Path
import pyarrow.parquet as pq
import time
from functools import wraps
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from tqdm import tqdm
import polars as pl

from openpyxl import load_workbook
from openpyxl.styles import PatternFill, Border, Side, Alignment, Font, NamedStyle
from openpyxl.utils import get_column_letter

import os

from tagging_logic_0311 import tagging_on_product, check_tag_values, tagging_old_model     #v2_final: tagging_logic

weekdays_to_divide = 7

base_period_week_numbers = 14


# for end of the Calculation
def CurrentTime():
    now = datetime.now()
    current_time = now.strftime("%H:%M:%S")
    h = int(current_time[0:2])  # time format: '11:53:12'
    m = int(current_time[3:5])
    s = int(current_time[6:8])
    sec_time = h * 3600 + m * 60 + s
    return sec_time


# Python decorator to measure execution time of Functions
def timeit(func):
    @wraps(func)
    def timeit_wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        total_time = end_time - start_time
        if func.__name__ == "Replenishment_Model_Running":
            print(
                f" \n{func.__name__} is done! Elapsed time (sec & min): {total_time:.2f} sec which is {total_time/60:.2f} min"
            )
        else:
            print(f" \n{func.__name__} is done! Elapsed time: {total_time:.1f} sec which is {total_time/60:.1f} min")
        return result

    return timeit_wrapper


def optimize_types(dataframe):
    np_types = [
        np.int8,
        np.int16,
        np.int32,
        np.int64,
        np.uint8,
        np.uint16,
        np.uint32,
        np.uint64,
        np.float32,
        np.float64,
    ]  # , np.float16, np.float32, np.float64
    np_types = [np_type.__name__ for np_type in np_types]
    type_df = pd.DataFrame(data=np_types, columns=["class_type"])

    type_df["min_value"] = type_df[type_df["class_type"].str.contains("int")][
        "class_type"
    ].apply(lambda row: np.iinfo(row).min)
    type_df["max_value"] = type_df[type_df["class_type"].str.contains("int")][
        "class_type"
    ].apply(lambda row: np.iinfo(row).max)
    type_df["min_value_f"] = type_df[type_df["class_type"].str.contains("float")][
        "class_type"
    ].apply(lambda row: np.finfo(row).min)
    type_df["max_value_f"] = type_df[type_df["class_type"].str.contains("float")][
        "class_type"
    ].apply(lambda row: np.finfo(row).max)
    type_df["min_value"] = np.where(
        type_df["min_value"].isna(), type_df["min_value_f"], type_df["min_value"]
    )
    type_df["max_value"] = np.where(
        type_df["max_value"].isna(), type_df["max_value_f"], type_df["max_value"]
    )
    type_df.drop(columns=["min_value_f", "max_value_f"], inplace=True)

    type_df["range"] = type_df["max_value"] - type_df["min_value"]
    type_df.sort_values(by="range", inplace=True)
    try:
        for col in dataframe.loc[:, dataframe.dtypes == np.integer]:
            col_min = dataframe[col].min()
            col_max = dataframe[col].max()
            temp = type_df[
                (type_df["min_value"] <= col_min) & (type_df["max_value"] >= col_max)
            ]
            optimized_class = temp.loc[temp["range"].idxmin(), "class_type"]
            # print("Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}".format(col, col_min, col_max, optimized_class))
            dataframe[col] = dataframe[col].astype(optimized_class)
    except ValueError:
        pass
    try:
        for col in dataframe.loc[:, (dataframe.dtypes == np.floating)]:
            col_min = dataframe[col].min()
            col_max = dataframe[col].max()
            type_df = type_df[
                type_df["class_type"].astype("string").str.contains("float")
            ]
            temp = type_df[
                (type_df["min_value"] <= col_min) & (type_df["max_value"] >= col_max)
            ]
            optimized_class = temp.loc[temp["range"].idxmin(), "class_type"]
            # print("Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}".format(col, col_min, col_max, optimized_class))
            dataframe[col] = dataframe[col].astype(optimized_class)
    except ValueError:
        pass
    return dataframe


def optimize_floats(df: pd.DataFrame) -> pd.DataFrame:
    floats = df.select_dtypes(include=["float64"]).columns.tolist()
    df[floats] = df[floats].apply(pd.to_numeric, downcast="float")
    return df


def optimize_ints(df: pd.DataFrame) -> pd.DataFrame:
    ints = df.select_dtypes(include=["int64"]).columns.tolist()
    df[ints] = df[ints].apply(pd.to_numeric, downcast="integer")
    return df


def optimize_objects(df: pd.DataFrame):
    try:
        for col in df.select_dtypes(include=["object"]):
            if not (type(df[col][0]) == list):
                num_unique_values = len(df[col].unique())
                num_total_values = len(df[col])
                if float(num_unique_values) / num_total_values < 0.5:
                    df[col] = df[col].astype("category")
    except IndexError:
        pass
    return df


def optimize(df: pd.DataFrame):
    return optimize_floats(optimize_ints(optimize_objects(df)))





def Repl_Drivers_Calculation_polars(
    directory,
    Repl_Dataset,
    store_inputs,
    backstock_target,
    RC_Capacity_Ratio,
    shelf_trolley_cap_ratio_to_pallet,
    shelf_trolley_cap_ratio_to_rollcage,
    excel_inputs_f,
    MODULE_CRATES,
    TABLE_CRATES,
    FULFILL_TARGET,
    SALES_CYCLE,
    RC_CAPACITY,
    RC_DELIVERY,
    RC_VS_PAL_CAPACITY,
    only_tpn,
    tpnb_store,
    tpnb_country,
    selected_tpn,
    capping_shelves_ratio,
    stores,
    version,
    shelfService_gm,
    cases_to_replenish_only,

):
    # Define constants used throughout the function
    weekdays_to_divide = 7  # Default to weekly calculation
    shelf_trolley_cap_ratio_to_pallet = 0.8  # Typical ratio
    shelf_trolley_cap_ratio_to_rollcage = 0.6  # Typical ratio
    
    # Convert to Polars DataFrame if it's pandas
    if isinstance(Repl_Dataset, pd.DataFrame):
        Drivers = pl.from_pandas(Repl_Dataset)
    else:
        Drivers = Repl_Dataset.clone()
    
    # Filter by stores
    Drivers = Drivers.filter(pl.col("store").is_in(stores))
    
    # MU customization - adjust pallet_capacity
    Drivers = Drivers.with_columns([
        pl.when(pl.col("mu") > 0)
        .then(pl.col("pallet_capacity") / 2)
        .otherwise(pl.col("pallet_capacity"))
        .alias("pallet_capacity")
    ])
    
    # Drop columns
    columns_to_drop = ["ownbrand", "checkout_stand_flag"]
    existing_columns = [col for col in columns_to_drop if col in Drivers.columns]
    if existing_columns:
        Drivers = Drivers.drop(existing_columns)
    
    # Fix foil column
    Drivers = Drivers.with_columns([
        pl.when(pl.col("foil") == 0)
        .then(1)
        .otherwise(pl.col("foil"))
        .alias("foil")
    ])
    
    # Filter produce data
    produce_condition = (
        (pl.col("dep") == "PRO") & 
        (pl.col("pmg") != "PRO16") & 
        (pl.col("pmg") != "PRO19")
    )
    
    # Get unique PMGs to delete from produce
    pro_to_del = Drivers.filter(produce_condition).select("pmg").unique().to_series().to_list()
    
    # Filter out produce PMGs and HDL01
    Drivers = Drivers.filter(
        (~pl.col("pmg").is_in(pro_to_del)) & 
        (pl.col("pmg") != "HDL01")
    )
    
    if len(Drivers) > 0:
        # Shelf capacity customization
        weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        # Sort by store, pmg, tpnb, day
        Drivers = Drivers.sort(["store", "pmg", "tpnb", "day"])
        
        # Calculate capacity_avg_pmg using window function
        Drivers = Drivers.with_columns([
            pl.col("shelfCapacity").mean().over(["store", "pmg"]).alias("capacity_avg_pmg")
        ])
        
        # Adjust capacity_avg_pmg
        Drivers = Drivers.with_columns([
            pl.when(pl.col("capacity_avg_pmg") < 5)
            .then(8)
            .otherwise(pl.col("capacity_avg_pmg"))
            .alias("capacity_avg_pmg")
        ])
        
        # Fill null values with 0
        Drivers = Drivers.fill_null(0)
        
        # Update shelfCapacity
        Drivers = Drivers.with_columns([
            pl.when((pl.col("shelfCapacity") == 0) | (pl.col("shelfCapacity") == 1))
            .then(pl.col("capacity_avg_pmg"))
            .otherwise(pl.col("shelfCapacity"))
            .alias("shelfCapacity")
        ])
        
        # Update stock based on sold_units
        Drivers = Drivers.with_columns([
            pl.when((pl.col("stock") == 0) & (pl.col("sold_units") > 0))
            .then(pl.col("sold_units"))
            .otherwise(pl.col("stock"))
            .alias("stock")
        ])
        
        # Weight customization
        Drivers = Drivers.with_columns([
            (pl.col("weight") * pl.col("case_capacity")).alias("heavy"),
            (pl.col("weight") * pl.col("case_capacity")).alias("weight_selector")
        ])
        
        Drivers = Drivers.with_columns([
            pl.when(pl.col("weight_selector") >= 5).then(1).otherwise(0).cast(pl.Int8).alias("heavy"),
            pl.when(pl.col("weight_selector") < 5).then(1).otherwise(0).cast(pl.Int8).alias("light")
        ])
        
        Drivers = Drivers.drop("weight_selector")
        
        # Broken Items case cap
        Drivers = Drivers.with_columns([
            pl.when(pl.col("broken_case_flag") == 1)
            .then(pl.col("case_capacity"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("Broken Items")
        ])
        
        # cases_delivered_on_sf calculation
        Drivers = Drivers.with_columns([
            pl.when(pl.col("broken_case_flag") == 1)
            .then(pl.col("cases_delivered") - 1)
            .otherwise(pl.col("cases_delivered"))
            .cast(pl.Float32)
            .alias("cases_delivered_on_sf")
        ])
        
        Drivers = Drivers.with_columns([
            pl.when(pl.col("cases_delivered_on_sf") < 0)
            .then(0)
            .otherwise(pl.col("cases_delivered_on_sf"))
            .alias("cases_delivered_on_sf")
        ])
        
        # Setup the icream_nsrp
        Drivers = Drivers.with_columns([
            pl.when(pl.col("icream_nsrp") > 0)
            .then(pl.col("icream_nsrp") + pl.col("nsrp"))
            .otherwise(pl.col("nsrp"))
            .alias("nsrp")
        ])
        
        # SRP / full_pallet / mu customizing - handle broken case flag
        delivery_columns = ['srp', 'split_pallet', 'full_pallet', 'mu', 'icream_nsrp', 'single_pick']
        for col_name in delivery_columns:
            if col_name in Drivers.columns:
                Drivers = Drivers.with_columns([
                    pl.when((pl.col(col_name) > 0) & (pl.col("broken_case_flag") == 1))
                    .then(0)
                    .otherwise(pl.col(col_name))
                    .alias(col_name)
                ])
        
        # Handle nsrp for broken case flag
        Drivers = Drivers.with_columns([
            pl.when(pl.col("broken_case_flag") == 1)
            .then(1)
            .otherwise(pl.col("nsrp"))
            .alias("nsrp")
        ])
        
        # Single pick customization
        single_pick_columns = ['nsrp', 'srp', 'split_pallet', 'full_pallet', 'mu', 'icream_nsrp']
        for col_name in single_pick_columns:
            if col_name in Drivers.columns:
                Drivers = Drivers.with_columns([
                    pl.when(pl.col("single_pick") > 0)
                    .then(0)
                    .otherwise(pl.col(col_name))
                    .alias(col_name)
                ])
        
        # Backroom calculations
        Drivers = Drivers.with_columns([
            pl.when(pl.col("backroom_flag") == 1)
            .then(pl.col("sold_units_dotcom") / pl.col("case_capacity"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("backroom_cases_dotcom")
        ])
        
        # Handle inf and nan values for backroom_cases_dotcom
        Drivers = Drivers.with_columns([
            pl.when(pl.col("backroom_cases_dotcom").is_null() | 
                   pl.col("backroom_cases_dotcom").is_infinite() |
                   pl.col("backroom_cases_dotcom").is_nan())
            .then(0)
            .otherwise(pl.col("backroom_cases_dotcom"))
            .alias("backroom_cases_dotcom")
        ])
        
        Drivers = Drivers.with_columns([
            pl.when(pl.col("backroom_cases_dotcom") > 0)
            .then(pl.col("backroom_cases_dotcom") / pl.col("pallet_capacity"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("backroom_pallets")
        ])
        
        # Handle inf and nan values for backroom_pallets
        Drivers = Drivers.with_columns([
            pl.when(pl.col("backroom_pallets").is_null() | 
                   pl.col("backroom_pallets").is_infinite() |
                   pl.col("backroom_pallets").is_nan())
            .then(0)
            .otherwise(pl.col("backroom_pallets"))
            .alias("backroom_pallets")
        ])
        
        # Update cases_delivered_on_sf with backroom adjustments
        Drivers = Drivers.with_columns([
            pl.when(pl.col("backroom_cases_dotcom") > 0)
            .then(pl.col("cases_delivered_on_sf") - pl.col("backroom_cases_dotcom"))
            .otherwise(pl.col("cases_delivered_on_sf"))
            .cast(pl.Float32)
            .alias("cases_delivered_on_sf")
        ])
        
        Drivers = Drivers.with_columns([
            pl.when(pl.col("cases_delivered_on_sf") < 0)
            .then(0)
            .otherwise(pl.col("cases_delivered_on_sf"))
            .alias("cases_delivered_on_sf")
        ])
        
        # Secondary SRP calculations
        Drivers = Drivers.with_columns([
            pl.when(pl.col("sold_units") > pl.col("shelfCapacity"))
            .then(pl.col("stock") - (pl.col("shelfCapacity") / (1 - backstock_target)))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("secondary_srp")
        ])
        
        # Handle inf and nan values for secondary_srp
        Drivers = Drivers.with_columns([
            pl.when(pl.col("secondary_srp").is_null() | 
                   pl.col("secondary_srp").is_infinite() |
                   pl.col("secondary_srp").is_nan())
            .then(0)
            .otherwise(pl.col("secondary_srp"))
            .alias("secondary_srp")
        ])
        
        # Additional secondary_srp conditions
        Drivers = Drivers.with_columns([
            pl.when((1 - pl.col("shelfCapacity") / pl.col("stock")) > 0.4)
            .then(pl.col("secondary_srp"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("secondary_srp")
        ])
        
        Drivers = Drivers.with_columns([
            pl.when(pl.col("stock") < pl.col("shelfCapacity"))
            .then(0)
            .otherwise(pl.col("secondary_srp"))
            .alias("secondary_srp")
        ])
        
        # Final secondary_srp adjustment with delivery types
        Drivers = Drivers.with_columns([
            pl.when(
                (pl.col("srp") > 0) |
                (pl.col("full_pallet") > 0) |
                (pl.col("mu") > 0) |
                (pl.col("split_pallet") > 0) |
                (pl.col("icream_nsrp") > 0)
            )
            .then(pl.col("secondary_srp") / weekdays_to_divide)
            .otherwise(0)
            .alias("secondary_srp")
        ])
        
        # Secondary NSRP calculations (similar to SRP but with opposite conditions)
        Drivers = Drivers.with_columns([
            pl.when(pl.col("sold_units") > pl.col("shelfCapacity"))
            .then(pl.col("stock") - (pl.col("shelfCapacity") / (1 - backstock_target)))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("secondary_nsrp")
        ])
        
        # Handle inf and nan values for secondary_nsrp
        Drivers = Drivers.with_columns([
            pl.when(pl.col("secondary_nsrp").is_null() | 
                   pl.col("secondary_nsrp").is_infinite() |
                   pl.col("secondary_nsrp").is_nan())
            .then(0)
            .otherwise(pl.col("secondary_nsrp"))
            .alias("secondary_nsrp")
        ])
        
        Drivers = Drivers.with_columns([
            pl.when((1 - (pl.col("shelfCapacity") / pl.col("stock"))) > 0.4)
            .then(pl.col("secondary_nsrp"))
            .otherwise(0)
            .alias("secondary_nsrp")
        ])
        
        Drivers = Drivers.with_columns([
            pl.when(pl.col("stock") < pl.col("shelfCapacity"))
            .then(0)
            .otherwise(pl.col("secondary_nsrp"))
            .alias("secondary_nsrp")
        ])
        
        # Final secondary_nsrp adjustment (opposite condition to SRP)
        Drivers = Drivers.with_columns([
            pl.when(
                (pl.col("srp") == 0) &
                (pl.col("full_pallet") == 0) &
                (pl.col("mu") == 0) &
                (pl.col("split_pallet") == 0) &
                (pl.col("icream_nsrp") == 0)
            )
            .then(pl.col("secondary_nsrp") / weekdays_to_divide)
            .otherwise(0)
            .alias("secondary_nsrp")
        ])
        
        # Calculate shop_floor_capacity
        Drivers = Drivers.with_columns([
            (pl.col("shelfCapacity") + pl.col("secondary_nsrp") + pl.col("secondary_srp"))
            .cast(pl.Float32)
            .alias("shop_floor_capacity")
        ])
        
        # Calculate stock_morning
        Drivers = Drivers.with_columns([
            (pl.col("stock") - pl.col("unit") + pl.col("sold_units") + pl.col("sold_units_dotcom"))
            .alias("stock_morning")
        ])
        
        # Handle inf and nan values for stock_morning
        Drivers = Drivers.with_columns([
            pl.when(pl.col("stock_morning").is_null() | 
                   pl.col("stock_morning").is_infinite() |
                   pl.col("stock_morning").is_nan())
            .then(0)
            .otherwise(pl.col("stock_morning"))
            .alias("stock_morning")
        ])
        
        # Add fallbacks for missing columns before cases_to_replenish calculation
        required_columns = ["sold_units", "stock", "shop_floor_capacity", "stock_morning", "case_capacity"]
        current_columns = Drivers.columns
        print(f"Current columns before cases_to_replenish: {len(current_columns)}")
        
        for col in required_columns:
            if col not in current_columns:
                print(f"Adding missing column: {col}")
                Drivers = Drivers.with_columns([pl.lit(0).cast(pl.Float32).alias(col)])
        
        # Calculate cases_to_replenish with safe column access and error handling
        try:
            Drivers = Drivers.with_columns([
                (
                    pl.col("sold_units").fill_null(0).cast(pl.Float32) +
                    pl.min_horizontal(["stock", "shop_floor_capacity"]) -
                    pl.min_horizontal(["stock_morning", "shop_floor_capacity"])
                ) / pl.max_horizontal([pl.col("case_capacity").fill_null(1), pl.lit(1)])
                .cast(pl.Float32)
                .alias("cases_to_replenish")
            ])
            print("cases_to_replenish column created successfully")
        except Exception as e:
            print(f"Error creating cases_to_replenish: {e}")
            # Fallback: create a simple zero column
            Drivers = Drivers.with_columns([pl.lit(0).cast(pl.Float32).alias("cases_to_replenish")])
        
        # Apply bounds checking with safer approach
        try:
            # First check if the column exists
            if "cases_to_replenish" in Drivers.columns:
                Drivers = Drivers.with_columns([
                    pl.when(pl.col("cases_to_replenish").is_null() | (pl.col("cases_to_replenish") < 0))
                    .then(0.0)
                    .otherwise(pl.col("cases_to_replenish"))
                    .cast(pl.Float32)
                    .alias("cases_to_replenish")
                ])
                print("cases_to_replenish bounds applied successfully")
            else:
                # Create the column if it doesn't exist
                Drivers = Drivers.with_columns([pl.lit(0.0).cast(pl.Float32).alias("cases_to_replenish")])
                print("cases_to_replenish column created as fallback")
        except Exception as e:
            print(f"Error in bounds checking, creating fallback column: {e}")
            # Ultimate fallback
            Drivers = Drivers.with_columns([pl.lit(0.0).cast(pl.Float32).alias("cases_to_replenish")])
        
        # ClipStrip Flag handling - cast categorical to string for .str operations
        Drivers = Drivers.with_columns([
            pl.when(
                pl.col("product_name").cast(pl.Utf8).str.contains("HELL") |
                pl.col("product_name").cast(pl.Utf8).str.contains("XIXO")
            )
            .then(0)
            .otherwise(pl.col("clipstrip_flag"))
            .alias("clipstrip_flag")
        ])
        
        Drivers = Drivers.with_columns([
            pl.when(pl.col("clipstrip_flag") == 1)
            .then(pl.col("cases_to_replenish") * pl.col("case_capacity"))
            .otherwise(0)
            .alias("Clip Strip Items")
        ])
        
        Drivers = Drivers.with_columns([
            (pl.col("Clip Strip Items") / pl.col("case_capacity"))
            .cast(pl.Float32)
            .alias("Clip Strip Cases")
        ])
        
        # Handle inf and nan values for Clip Strip Cases with Polars-native approach
        Drivers = Drivers.with_columns([
            pl.when(pl.col("Clip Strip Cases").is_null() | 
                    pl.col("Clip Strip Cases").is_infinite() | 
                    pl.col("Clip Strip Cases").is_nan())
            .then(0)
            .otherwise(pl.col("Clip Strip Cases"))
            .cast(pl.Float32)
            .alias("Clip Strip Cases")
        ])
        
        # Update cases_to_replenish for clipstrip items
        Drivers = Drivers.with_columns([
            pl.when(pl.col("clipstrip_flag") == 1)
            .then(0)
            .otherwise(pl.col("cases_to_replenish"))
            .alias("cases_to_replenish")
        ])
        
        # Handle cases_to_replenish_only condition
        if cases_to_replenish_only == True:
            # Group by TPN level
            cases_to_replenish_tpn = Drivers.group_by(
                ['country', 'store', 'division', 'dep', 'pmg', 'tpnb', 'product_name']
            ).agg([
                pl.col('cases_to_replenish').sum()
            ])
            
            # Group by department level
            cases_to_replenish_dep = Drivers.group_by(
                ['country', 'store', 'division', 'dep']
            ).agg([
                pl.col('cases_to_replenish').sum()
            ])
            
            # Return early for cases_to_replenish_only mode
            return Drivers
        
        if cases_to_replenish_only == False:
            # Touch calculations
            Drivers = Drivers.with_columns([
                pl.when(pl.col("stock") > pl.col("shop_floor_capacity"))
                .then(pl.col("shop_floor_capacity"))
                .otherwise(pl.col("stock"))
                .cast(pl.Float32)
                .alias("o_touch")
            ])
            
            Drivers = Drivers.with_columns([
                pl.when(
                    (pl.col("is_capping_shelf") == 1) &
                    (
                        pl.col("stock") - (capping_shelves_ratio * pl.col("shop_floor_capacity"))
                        > pl.col("shop_floor_capacity")
                    )
                )
                .then(capping_shelves_ratio * pl.col("shop_floor_capacity"))
                .otherwise(0)
                .alias("c_touch")
            ])
            
            Drivers = Drivers.with_columns([
                pl.when(pl.col("stock") > pl.col("shop_floor_capacity"))
                .then(pl.col("stock") - pl.col("c_touch") - pl.col("shop_floor_capacity"))
                .otherwise(0)
                .cast(pl.Float32)
                .alias("t_touch")
            ])
            
            # Capping Shelf Cases calculation
            Drivers = Drivers.with_columns([
                ((pl.col("c_touch") / pl.col("case_capacity")) / weekdays_to_divide)
                .cast(pl.Float32)
                .alias("Capping Shelf Cases")
            ])
            
            # Handle inf and nan values for Capping Shelf Cases
            Drivers = Drivers.with_columns([
                pl.when(pl.col("Capping Shelf Cases").is_null() | 
                       pl.col("Capping Shelf Cases").is_infinite() |
                       pl.col("Capping Shelf Cases").is_nan())
                .then(0)
                .otherwise(pl.col("Capping Shelf Cases"))
                .alias("Capping Shelf Cases")
            ])
            
            # Adjust Capping Shelf Cases for full_pallet and mu
            Drivers = Drivers.with_columns([
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(0)
                .otherwise(pl.col("Capping Shelf Cases"))
                .alias("Capping Shelf Cases")
            ])
            
            # Calculate cases_to_repl_excl_cap_cases
            Drivers = Drivers.with_columns([
                pl.when(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases") > 0)
                .then(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases"))
                .otherwise(pl.col("cases_to_replenish"))
                .alias("cases_to_repl_excl_cap_cases")
            ])
            
            # Final adjustment for Capping Shelf Cases
            Drivers = Drivers.with_columns([
                pl.when(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases") > 0)
                .then(pl.col("Capping Shelf Cases"))
                .otherwise(0)
                .alias("Capping Shelf Cases")
            ])
            
            # Prepare store_inputs dataframes for merging
            if isinstance(store_inputs, pd.DataFrame):
                store_inputs_pl = pl.from_pandas(store_inputs)
            else:
                store_inputs_pl = store_inputs
            
            # Create stores_df
            stores_df = store_inputs_pl.select(["Store", "Format", "Plan Size"]).unique()
            stores_df = stores_df.rename({"Store": "store", "Format": "format", "Plan Size": "plan size"})
            
            # Create dep_df
            dep_df = store_inputs_pl.select([
                "Store", "Dep", "Racking", "Pallets Delivery Ratio", 
                "Backstock Pallet Ratio", "says", "pbl_pbs_25_perc_pallet"
            ]).unique()
            dep_df = dep_df.rename({
                "Store": "store", "Dep": "dep", "Racking": "racking",
                "Pallets Delivery Ratio": "pallets delivery ratio",
                "Backstock Pallet Ratio": "backstock pallet ratio",
                "says": "says", "pbl_pbs_25_perc_pallet": "pbl_pbs_25_perc_pallet"
            })
            
            # Create pmg_df  
            pmg_df = store_inputs_pl.select(["Country", "Format", "Pmg", "presortPerc", "prack"]).unique()
            pmg_df = pmg_df.rename({
                "Country": "country", "Format": "format", "Pmg": "pmg",
                "presortPerc": "presortperc", "prack": "prack"
            })
            
            # Fix format column with fallback for missing column
            if "format" not in Drivers.columns:
                print("Adding missing format column")
                Drivers = Drivers.with_columns([pl.lit("1K").cast(pl.Utf8).alias("format")])
            else:
                try:
                    Drivers = Drivers.with_columns([
                        pl.when(pl.col("format").cast(pl.Utf8) == pl.lit("1k"))
                        .then(pl.lit("1K"))
                        .otherwise(pl.col("format").cast(pl.Utf8))
                        .alias("format")
                    ])
                except Exception as e:
                    print(f"Error in format column handling: {e}")
                    # Fallback: just ensure format column exists
                    Drivers = Drivers.with_columns([pl.lit("1K").cast(pl.Utf8).alias("format")])
            
            # Perform merges with type alignment for join keys
            # Cast categorical columns to string for consistent joins in both DataFrames
            if "dep" in Drivers.columns:
                Drivers = Drivers.with_columns([pl.col("dep").cast(pl.Utf8)])
            if "store" in Drivers.columns:
                Drivers = Drivers.with_columns([pl.col("store").cast(pl.Utf8)])
            if "format" in Drivers.columns:
                Drivers = Drivers.with_columns([pl.col("format").cast(pl.Utf8)])
            
            # Cast join keys in lookup DataFrames to match
            if "store" in stores_df.columns:
                stores_df = stores_df.with_columns([pl.col("store").cast(pl.Utf8)])
            if "format" in stores_df.columns:
                stores_df = stores_df.with_columns([pl.col("format").cast(pl.Utf8)])
            if "store" in dep_df.columns:
                dep_df = dep_df.with_columns([pl.col("store").cast(pl.Utf8)])
            if "dep" in dep_df.columns:
                dep_df = dep_df.with_columns([pl.col("dep").cast(pl.Utf8)])
            if "country" in pmg_df.columns:
                pmg_df = pmg_df.with_columns([pl.col("country").cast(pl.Utf8)])
            if "format" in pmg_df.columns:
                pmg_df = pmg_df.with_columns([pl.col("format").cast(pl.Utf8)])
            if "pmg" in pmg_df.columns:
                pmg_df = pmg_df.with_columns([pl.col("pmg").cast(pl.Utf8)])
            
            Drivers = Drivers.join(stores_df, on=["store", "format"], how="inner")
            Drivers = Drivers.join(pmg_df, on=["country", "format", "pmg"], how="left")
            Drivers = Drivers.join(dep_df, on=["store", "dep"], how="left")
            
            # Adjust prack based on racking
            Drivers = Drivers.with_columns([
                pl.when(pl.col("racking") == 1)
                .then(pl.col("prack"))
                .otherwise(0)
                .alias("prack")
            ])
            
            # Handle presortperc adjustment
            try:
                Drivers = Drivers.with_columns([
                    pl.when(pl.col("dep").is_in(["BWS", "HEA", "DRY"]))
                    .then(pl.col("pre_sort_perc_by_pmg"))
                    .otherwise(pl.col("presortperc"))
                    .alias("presortperc")
                ])
            except:
                pass
            
            # New Delivery - Rollcages calculation
            Drivers = Drivers.with_columns([
                (
                    (pl.col("cases_delivered_on_sf") / pl.col("pallet_capacity")) *
                    RC_Capacity_Ratio
                ) * (1 - pl.col("pallets delivery ratio"))
                .cast(pl.Float32)
                .alias("New Delivery - Rollcages")
            ])
            
            # Handle inf and nan values for New Delivery - Rollcages with fallback
            if "New Delivery - Rollcages" not in Drivers.columns:
                print("Adding missing 'New Delivery - Rollcages' column")
                Drivers = Drivers.with_columns([pl.lit(0).alias("New Delivery - Rollcages")])
            else:
                Drivers = Drivers.with_columns([
                    pl.when(pl.col("New Delivery - Rollcages").is_null() | 
                           pl.col("New Delivery - Rollcages").is_infinite() |
                           pl.col("New Delivery - Rollcages").is_nan())
                    .then(0)
                    .otherwise(pl.col("New Delivery - Rollcages"))
                    .alias("New Delivery - Rollcages")
                ])
            
            # Handle PBL/PBS cage and pallet calculations
            try:
                # Combine MIX and PBL columns
                Drivers = Drivers.with_columns([
                    (pl.col("MIX_CAGE_%") + pl.col("PBL_CAGE_%")).alias("PBL_CAGE_%"),
                    (pl.col("MIX_PALLET_%") + pl.col("PBL_PALLET_%")).alias("PBL_PALLET_%")
                ])
                
                # Handle zero sum rows for cage columns
                cage_cols = ["PBL_CAGE_%", "PBS_CAGE_%"]
                pallet_cols = ["PBL_PALLET_%", "PBS_PALLET_%"]
                
                for cols in [cage_cols, pallet_cols]:
                    Drivers = Drivers.with_columns([
                        pl.when((pl.col(cols[0]) + pl.col(cols[1])) == 0)
                        .then(0.5)
                        .otherwise(pl.col(cols[0]))
                        .alias(cols[0]),
                        pl.when((pl.col(cols[0]) + pl.col(cols[1])) == 0)
                        .then(0.5)
                        .otherwise(pl.col(cols[1]))
                        .alias(cols[1])
                    ])
                
                # Calculate cage values
                for col in ["PBL_CAGE_%", "PBS_CAGE_%"]:
                    new_col = col[:-2]  # Remove '%' suffix
                    Drivers = Drivers.with_columns([
                        (pl.col(col) * pl.col("New Delivery - Rollcages"))
                        .cast(pl.Float32)
                        .alias(new_col)
                    ])
            except:
                pass
            
            # New Delivery - Pallets calculation
            Drivers = Drivers.with_columns([
                (
                    pl.col("cases_delivered_on_sf") / pl.col("pallet_capacity")
                ) * pl.col("pallets delivery ratio")
                .alias("New Delivery - Pallets")
            ])
            
            # Handle inf and nan values for New Delivery - Pallets with fallback
            if "New Delivery - Pallets" not in Drivers.columns:
                print("Adding missing 'New Delivery - Pallets' column")
                Drivers = Drivers.with_columns([pl.lit(0.0).cast(pl.Float32).alias("New Delivery - Pallets")])
            else:
                Drivers = Drivers.with_columns([
                    pl.when(pl.col("New Delivery - Pallets").is_null() | 
                           pl.col("New Delivery - Pallets").is_infinite() |
                           pl.col("New Delivery - Pallets").is_nan())
                    .then(0.0)
                    .otherwise(pl.col("New Delivery - Pallets"))
                    .cast(pl.Float32)
                    .alias("New Delivery - Pallets")
                ])
            
            # Handle pallet percentage calculations
            try:
                for col in ["PBL_PALLET_%", "PBS_PALLET_%"]:
                    new_col = col[:-2]  # Remove '%' suffix
                    Drivers = Drivers.with_columns([
                        (pl.col(col) * pl.col("New Delivery - Pallets"))
                        .cast(pl.Float32)
                        .alias(new_col)
                    ])
                
                for col in ["PBL_PALLET_%", "PBS_PALLET_%"]:
                    Drivers = Drivers.with_columns([
                        pl.col(col).fill_null(0.5).cast(pl.Float32).alias(col)
                    ])
            except:
                pass
            
            # New Delivery - Shelf Trolley calculation
            Drivers = Drivers.with_columns([
                (
                    pl.col("cases_delivered_on_sf") / pl.col("pallet_capacity")
                    * pl.lit(shelf_trolley_cap_ratio_to_pallet)
                ).alias("New Delivery - Shelf Trolley")
            ])
            
            # Handle inf and nan values for New Delivery - Shelf Trolley
            Drivers = Drivers.with_columns([
                pl.when(pl.col("New Delivery - Shelf Trolley").is_null() | 
                       pl.col("New Delivery - Shelf Trolley").is_infinite() |
                       pl.col("New Delivery - Shelf Trolley").is_nan())
                .then(0)
                .otherwise(pl.col("New Delivery - Shelf Trolley"))
                .cast(pl.Float32)
                .alias("New Delivery - Shelf Trolley")
            ])
            
            # Pre-sorted Cases calculation
            Drivers = Drivers.with_columns([
                (
                    pl.col("presortperc") *
                    pl.col("cases_delivered_on_sf") *
                    pl.col("pallets delivery ratio")
                ).alias("Pre-sorted Cases")
            ])
            
            # Pre-sorted Cases_pbl_pbs (same calculation)
            Drivers = Drivers.with_columns([
                (
                    pl.col("presortperc") *
                    pl.col("cases_delivered_on_sf") *
                    pl.col("pallets delivery ratio")
                ).alias("Pre-sorted Cases_pbl_pbs")
            ])
            
            # Light and Heavy Pre-sorted Cases
            Drivers = Drivers.with_columns([
                pl.when(pl.col("light") == 1)
                .then(pl.col("Pre-sorted Cases"))
                .otherwise(0)
                .cast(pl.Float32)
                .alias("L_Pre-sorted Cases"),
                pl.when(pl.col("heavy") == 1)
                .then(pl.col("Pre-sorted Cases"))
                .otherwise(0)
                .cast(pl.Float32)
                .alias("H_Pre-sorted Cases")
            ])
            
            # Handle inf and nan values for L_Pre-sorted and H_Pre-sorted Cases
            for col in ["L_Pre-sorted Cases", "H_Pre-sorted Cases"]:
                if col in Drivers.columns:
                    Drivers = Drivers.with_columns([
                        pl.when(pl.col(col).is_null() | 
                               pl.col(col).is_infinite() |
                               pl.col(col).is_nan())
                        .then(0)
                        .otherwise(pl.col(col))
                        .cast(pl.Float32)
                        .alias(col)
                    ])
                else:
                    print(f"Adding missing '{col}' column")
                    Drivers = Drivers.with_columns([pl.lit(0.0).cast(pl.Float32).alias(col)])
            
            # Full Pallet Cases and MU cases calculations
            Drivers = Drivers.with_columns([
                (pl.col("cases_to_replenish") * 
                 pl.when(pl.col("full_pallet") > 0)
                 .then(pl.col("full_pallet"))
                 .otherwise(0))
                .cast(pl.Float32)
                .alias("Full Pallet Cases"),
                (pl.col("cases_to_replenish") * 
                 pl.when(pl.col("mu") > 0)
                 .then(pl.col("mu"))
                 .otherwise(0))
                .cast(pl.Float32)
                .alias("MU cases")
            ])
            
            # Full + Half Pallet Cases
            Drivers = Drivers.with_columns([
                (pl.col("Full Pallet Cases") + pl.col("MU cases"))
                .alias("Full + Half Pallet Cases")
            ])
            
            # Racking Pallets calculation
            Drivers = Drivers.with_columns([
                (pl.col("prack") * pl.col("New Delivery - Pallets"))
                .alias("Racking Pallets")
            ])
    
    #######################################################
    # Replenished Rollcages/Pallets Calculations (L649-L840)
    #######################################################
    
    # Replenished Rollcages calculation
    Drivers = Drivers.with_columns([
        (pl.col("New Delivery - Rollcages") + 
         (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
          pl.col("pallets delivery ratio")) * RC_Capacity_Ratio)
        .alias("Replenished Rollcages")
    ])
    
    # Handle inf and nan values for Replenished Rollcages
    Drivers = Drivers.with_columns([
        pl.when(pl.col("Replenished Rollcages").is_null() | 
               pl.col("Replenished Rollcages").is_infinite() |
               pl.col("Replenished Rollcages").is_nan())
        .then(0)
        .otherwise(pl.col("Replenished Rollcages"))
        .alias("Replenished Rollcages")
    ])
    
    # PBL/PBS Rollcages calculations with error handling
    try:
        Drivers = Drivers.with_columns([
            ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
              pl.col("pallets delivery ratio")) * RC_Capacity_Ratio).alias("pre_sort_rc"),
            (((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
               pl.col("pallets delivery ratio")) * RC_Capacity_Ratio) * pl.col("PBS_CAGE_%")).alias("pre_sort_rc_pbs"),
            (((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
               pl.col("pallets delivery ratio")) * RC_Capacity_Ratio) * pl.col("PBL_CAGE_%")).alias("pre_sort_rc_pbl")
        ])
        
        Drivers = Drivers.with_columns([
            (pl.col("PBS_CAGE") + pl.col("pre_sort_rc_pbs")).alias("Replenished Rollcages PBS"),
            (pl.col("PBL_CAGE") + pl.col("pre_sort_rc_pbl")).alias("Replenished Rollcages PBL")
        ])
    except:
        pass
    
    ########################################
    # Replenished Pallets calculation
    ########################################
    
    Drivers = Drivers.with_columns([
        pl.when(
            (pl.col("New Delivery - Pallets") - 
             (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
              pl.col("pallets delivery ratio"))) <= 0
        ).then(0)
        .otherwise(
            pl.col("New Delivery - Pallets") - 
            (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
             pl.col("pallets delivery ratio"))
        ).map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
        .alias("Replenished Pallets")
    ])
    
    # PBL/PBS Pallets calculations with error handling
    try:
        Drivers = Drivers.with_columns([
            (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
             pl.col("pallets delivery ratio")).alias("pre_sort_pal"),
            ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
              pl.col("pallets delivery ratio")) * pl.col("PBS_PALLET_%")).alias("pre_sort_pal_pbs"),
            ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
              pl.col("pallets delivery ratio")) * pl.col("PBL_PALLET_%")).alias("pre_sort_pal_pbl")
        ])
        
        Drivers = Drivers.with_columns([
            pl.when((pl.col("PBS_PALLET") - pl.col("pre_sort_pal_pbs")) <= 0)
            .then(0)
            .otherwise(pl.col("PBS_PALLET") - pl.col("pre_sort_pal_pbs"))
            .alias("Replenished Pallets PBS"),
            
            pl.when((pl.col("PBL_PALLET") - pl.col("pre_sort_pal_pbl")) <= 0)
            .then(0)
            .otherwise(pl.col("PBL_PALLET") - pl.col("pre_sort_pal_pbl"))
            .alias("Replenished Pallets PBL")
        ])
    except:
        pass
    
    ################################################
    # Replenished Shelf Trolley and related calculations
    ################################################
    
    Drivers = Drivers.with_columns([
        ((pl.col("Replenished Rollcages") * shelf_trolley_cap_ratio_to_rollcage) +
         (pl.col("Replenished Pallets") * shelf_trolley_cap_ratio_to_pallet))
        .cast(pl.Float32).alias("Replenished Shelf Trolley")
    ])
    
    # Zero out replenished values for full_pallet/mu items
    replenished_cols = ["Replenished Rollcages", "Replenished Pallets", 
                       "Replenished Pallets PBL", "Replenished Pallets PBS",
                       "Replenished Rollcages PBL", "Replenished Rollcages PBS"]
    
    for col in replenished_cols:
        try:
            Drivers = Drivers.with_columns([
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(0)
                .otherwise(pl.col(col))
                .alias(col)
            ])
        except:
            pass
    
    # Unit_for_tagging calculation
    Drivers = Drivers.with_columns([
        ((pl.col("cases_to_repl_excl_cap_cases") + pl.col("Capping Shelf Cases")) * 
         pl.col("case_capacity")).alias("Unit_for_tagging")
    ])
    
    Drivers = Drivers.with_columns([
        pl.when(pl.col("Unit_for_tagging") < 0)
        .then(0)
        .otherwise(pl.col("Unit_for_tagging"))
        .alias("Unit_for_tagging")
    ])
    
    # =============================================================================
    # UK BackStock Logic
    # =============================================================================
    
    Drivers = Drivers.with_columns([
        # looseSingles calculation
        pl.when(pl.col("stock") > pl.col("case_capacity"))
        .then(pl.col("stock") % pl.col("case_capacity"))
        .otherwise(0)
        .alias("looseSingles")
    ])
    
    Drivers = Drivers.with_columns([
        # inCaseSingles calculation
        (pl.col("stock") - pl.col("looseSingles")).alias("inCaseSingles")
    ])
    
    Drivers = Drivers.with_columns([
        # spaceOnShelfSingles calculation  
        pl.when(
            (pl.col("shelfCapacity") > pl.col("looseSingles")) & 
            (pl.col("stock") > pl.col("shelfCapacity"))
        ).then(
            pl.col("shelfCapacity") - pl.col("looseSingles")
        ).otherwise(
            pl.col("shelfCapacity") - pl.col("stock") - pl.col("looseSingles")
        ).alias("spaceOnShelfSingles")
    ])
    
    Drivers = Drivers.with_columns([
        # Ensure spaceOnShelfSingles is not negative
        pl.when(pl.col("spaceOnShelfSingles") < 0)
        .then(0)
        .otherwise(pl.col("spaceOnShelfSingles"))
        .alias("spaceOnShelfSingles")
    ])
    
    Drivers = Drivers.with_columns([
        # caseUnitSpaceOnShelf calculation
        (pl.col("spaceOnShelfSingles") - 
         (pl.col("spaceOnShelfSingles") % pl.col("case_capacity")))
        .alias("caseUnitSpaceOnShelf")
    ])
    
    Drivers = Drivers.with_columns([
        # fullCaseSingleShelf calculation
        pl.when(
            (pl.col("caseUnitSpaceOnShelf") >= pl.col("inCaseSingles")) & 
            (pl.col("stock") > pl.col("caseUnitSpaceOnShelf"))
        ).then(
            pl.col("caseUnitSpaceOnShelf")
        ).otherwise(
            pl.col("stock") - pl.col("looseSingles")
        ).alias("fullCaseSingleShelf")
    ])
    
    Drivers = Drivers.with_columns([
        # totalSinglesShelf calculation
        pl.when(
            (pl.col("fullCaseSingleShelf") + pl.col("looseSingles")) > pl.col("shelfCapacity")
        ).then(
            pl.col("shelfCapacity")
        ).otherwise(
            pl.col("fullCaseSingleShelf") + pl.col("looseSingles")
        ).alias("totalSinglesShelf")
    ])
    
    Drivers = Drivers.with_columns([
        # totalSinglesBackstock calculation
        pl.when(pl.col("stock") > pl.col("totalSinglesShelf"))
        .then(pl.col("stock") - pl.col("totalSinglesShelf"))
        .otherwise(0)
        .alias("totalSinglesBackstock")
    ])
    
    Drivers = Drivers.with_columns([
        # totalCasesShelf calculation
        (pl.col("totalSinglesShelf") / pl.col("case_capacity")).alias("totalCasesShelf"),
        
        # Backstock Cases calculation
        ((pl.col("totalSinglesBackstock") / pl.col("case_capacity")) / weekdays_to_divide)
        .alias("Backstock Cases"),
        
        # Backstock unit calculation
        (pl.col("totalSinglesBackstock") / weekdays_to_divide).alias("Backstock unit")
    ])
    
    # =============================================================================
    # post-sort logic
    # =============================================================================
    
    Drivers = Drivers.with_columns([
        # post_sort_ratio calculation
        (pl.col("t_touch") / pl.col("stock"))
        .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float64)
        .alias("post_sort_ratio")
    ])
    
    # Adjust Backstock Cases for GM division
    Drivers = Drivers.with_columns([
        pl.when(pl.col("division") == "GM")
        .then(pl.col("cases_to_replenish") * pl.col("post_sort_ratio"))
        .otherwise(pl.col("Backstock Cases"))
        .alias("Backstock Cases")
    ])
    
    # Adjust Backstock Cases by subtracting Capping Shelf Cases
    Drivers = Drivers.with_columns([
        pl.when((pl.col("Backstock Cases") - pl.col("Capping Shelf Cases")) > 0)
        .then(pl.col("Backstock Cases") - pl.col("Capping Shelf Cases"))
        .otherwise(0)
        .alias("Backstock Cases")
    ])
    
    # Backstock_tpn_nr calculation with groupby
    Drivers = Drivers.with_columns([
        pl.when(
            pl.col("Backstock Cases").sum().over(["store", "tpnb"]) > 0
        ).then(1/7)
        .otherwise(0)
        .alias("Backstock_tpn_nr")
    ])
    
    #################################################
    # Additional Calculations (L841-L1088)
    #################################################
    
    # Post-sort Cases calculation
    Drivers = Drivers.with_columns([
        (pl.col("cases_to_replenish") * pl.col("post_sort_ratio"))
        .alias("Post-sort Cases")
    ])
    
    # Two Touch Cases calculation with error handling
    try:
        Drivers = Drivers.with_columns([
            (pl.col("t_touch") / pl.col("case_capacity"))
            .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
            .alias("Two Touch Cases")
        ])
        
        Drivers = Drivers.with_columns([
            (pl.col("Two Touch Cases") / weekdays_to_divide).alias("Two Touch Cases")
        ])
    except:
        Drivers = Drivers.with_columns([pl.lit(0).alias("Two Touch Cases")])
    
    # Two Touch unit calculation
    try:
        Drivers = Drivers.with_columns([
            (pl.col("t_touch") / weekdays_to_divide).alias("Two Touch unit")
        ])
    except:
        Drivers = Drivers.with_columns([pl.lit(0).alias("Two Touch unit")])
    
    # Post-sort Pallets calculation
    Drivers = Drivers.with_columns([
        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
        .then(pl.col("Post-sort Cases") / pl.col("pallet_capacity"))
        .otherwise(
            pl.col("Post-sort Cases") / pl.col("pallet_capacity") * 
            pl.col("backstock pallet ratio")
        )
        .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
        .alias("Post-sort Pallets")
    ])
    
    # Zero Post-sort Cases for full_pallet/mu items
    Drivers = Drivers.with_columns([
        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
        .then(0)
        .otherwise(pl.col("Post-sort Cases"))
        .alias("Post-sort Cases")
    ])
    
    # L_Post-sort Cases and H_Post-sort Cases
    try:
        Drivers = Drivers.with_columns([
            pl.when(pl.col("light") == 1)
            .then(pl.col("Post-sort Cases"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("L_Post-sort Cases"),
            
            pl.when(pl.col("heavy") == 1)
            .then(pl.col("Post-sort Cases"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("H_Post-sort Cases")
        ])
    except:
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("L_Post-sort Cases"),
            pl.lit(0).alias("H_Post-sort Cases")
        ])
    
    # Post-sort Rollcages calculation
    Drivers = Drivers.with_columns([
        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
        .then(0)
        .otherwise(
            (pl.col("Post-sort Cases") / pl.col("pallet_capacity") * 
             (1 - pl.col("backstock pallet ratio")) * RC_Capacity_Ratio)
        )
        .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
        .alias("Post-sort Rollcages")
    ])
    
    # Backstock Pallets calculation
    Drivers = Drivers.with_columns([
        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
        .then(pl.col("Backstock Cases") / pl.col("pallet_capacity"))
        .otherwise(
            pl.col("Backstock Cases") / pl.col("pallet_capacity") * 
            pl.col("backstock pallet ratio")
        )
        .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
        .alias("Backstock Pallets")
    ])
    
    # Zero Backstock Cases and Backstock unit for full_pallet/mu items
    Drivers = Drivers.with_columns([
        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
        .then(0)
        .otherwise(pl.col("Backstock Cases"))
        .alias("Backstock Cases"),
        
        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
        .then(0)
        .otherwise(pl.col("Backstock unit"))
        .alias("Backstock unit")
    ])
    
    # Backstock Rollcages calculation
    Drivers = Drivers.with_columns([
        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
        .then(0)
        .otherwise(
            (pl.col("Backstock Cases") / pl.col("pallet_capacity") * 
             (1 - pl.col("backstock pallet ratio")) * RC_Capacity_Ratio)
        )
        .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
        .alias("Backstock Rollcages")
    ])
    
    # Backstock Shelf Trolley calculation
    Drivers = Drivers.with_columns([
        (pl.col("Backstock Rollcages") * shelf_trolley_cap_ratio_to_rollcage +
         pl.col("Backstock Pallets") * shelf_trolley_cap_ratio_to_pallet)
        .alias("Backstock Shelf Trolley")
    ])
    
    # Pre-sorted Rollcages calculation
    Drivers = Drivers.with_columns([
        ((pl.col("Pre-sorted Cases") / pl.col("pallet_capacity")) * RC_Capacity_Ratio)
        .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
        .alias("Pre-sorted Rollcages")
    ])
    
    # Pre-sorted Shelf Trolley calculation
    Drivers = Drivers.with_columns([
        ((pl.col("Pre-sorted Cases") / pl.col("pallet_capacity")) * 
         shelf_trolley_cap_ratio_to_pallet)
        .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
        .alias("Pre-sorted Shelf Trolley")
    ])
    
    # Full Pallet and MU Pallet calculations
    Drivers = Drivers.with_columns([
        (pl.col("Full Pallet Cases") / pl.col("pallet_capacity"))
        .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
        .alias("Full Pallet"),
        
        (pl.col("MU cases") / pl.col("pallet_capacity"))
        .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
        .alias("MU Pallet")
    ])
    
    # One Touch Cases calculation with error handling
    try:
        Drivers = Drivers.with_columns([
            (pl.col("o_touch") / pl.col("case_capacity"))
            .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
            .alias("One Touch Cases")
        ])
        
        Drivers = Drivers.with_columns([
            (pl.col("One Touch Cases") / weekdays_to_divide).alias("One Touch Cases")
        ])
    except:
        Drivers = Drivers.with_columns([pl.lit(0).alias("One Touch Cases")])
    
    # Set foil based on tpnb_country
    if tpnb_country:
        Drivers = Drivers.with_columns([pl.lit(1).alias("foil")])
    
    # SRP calculations
    try:
        Drivers = Drivers.with_columns([
            (pl.col("srp") + pl.col("split_pallet")).alias("srp_split_pallet")
        ])
        
        # L_SRP calculation
        Drivers = Drivers.with_columns([
            pl.when((pl.col("srp_split_pallet") > 0) & (pl.col("light") == 1))
            .then(pl.col("cases_to_repl_excl_cap_cases") * pl.col("srp_split_pallet"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("L_SRP")
        ])
        
        # Apply SRP opening reduction opportunity to L_SRP
        try:
            Drivers = Drivers.with_columns([
                pl.when(pl.col("SRP opening reduction opportunity") == 1)
                .then(pl.col("L_SRP") * pl.col("foil"))
                .otherwise(pl.col("L_SRP"))
                .alias("L_SRP")
            ])
        except:
            pass
        
        # H_SRP calculation
        Drivers = Drivers.with_columns([
            pl.when((pl.col("srp_split_pallet") > 0) & (pl.col("heavy") == 1))
            .then(pl.col("cases_to_repl_excl_cap_cases") * pl.col("srp_split_pallet"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("H_SRP")
        ])
        
        # Apply SRP opening reduction opportunity to H_SRP
        try:
            Drivers = Drivers.with_columns([
                pl.when(pl.col("SRP opening reduction opportunity") == 1)
                .then(pl.col("H_SRP") * pl.col("foil"))
                .otherwise(pl.col("H_SRP"))
                .alias("H_SRP")
            ])
        except:
            pass
            
    except:
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("srp_split_pallet"),
            pl.lit(0).alias("L_SRP"),
            pl.lit(0).alias("H_SRP")
        ])
    
    # NSRP calculations
    try:
        Drivers = Drivers.with_columns([
            pl.when((pl.col("nsrp") > 0) & (pl.col("light") == 1))
            .then((pl.col("cases_to_repl_excl_cap_cases") * pl.col("nsrp")) * pl.col("foil"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("L_NSRP"),
            
            pl.when((pl.col("nsrp") > 0) & (pl.col("heavy") == 1))
            .then((pl.col("cases_to_repl_excl_cap_cases") * pl.col("nsrp")) * pl.col("foil"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("H_NSRP")
        ])
    except:
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("L_NSRP"),
            pl.lit(0).alias("H_NSRP")
        ])
    
    # Foil_Cases calculation
    try:
        # Create sum of srp, nsrp, icream_nsrp columns with error handling
        srp_sum_cols = []
        for col_name in ['srp', 'nsrp', 'icream_nsrp']:
            try:
                if col_name in Drivers.columns:
                    srp_sum_cols.append(pl.col(col_name))
            except:
                pass
        
        if srp_sum_cols:
            srp_sum_expr = pl.sum_horizontal(srp_sum_cols)
        else:
            srp_sum_expr = pl.lit(0)
        
        Drivers = Drivers.with_columns([
            pl.when(pl.col("foil") != 1)
            .then((1 - pl.col("foil")) * pl.col("cases_to_repl_excl_cap_cases") * srp_sum_expr)
            .otherwise(0)
            .alias("Foil_Cases")
        ])
    except:
        Drivers = Drivers.with_columns([pl.lit(0).alias("Foil_Cases")])
    
    # Secondary SRP/NSRP calculations based on tpnb_country
    if not tpnb_country:
        try:
            Drivers = Drivers.with_columns([
                (pl.col("secondary_srp") / pl.col("case_capacity"))
                .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
                .alias("Sec_SRP_cases"),
                
                (pl.col("secondary_nsrp") / pl.col("case_capacity"))
                .map_elements(lambda x: 0 if (np.isnan(x) or np.isinf(x)) else x, return_dtype=pl.Float32)
                .alias("Sec_NSRP_cases")
            ])
        except:
            Drivers = Drivers.with_columns([
                pl.lit(0).alias("Sec_SRP_cases"),
                pl.lit(0).alias("Sec_NSRP_cases")
            ])
    else:
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("Sec_SRP_cases"),
            pl.lit(0).alias("Sec_NSRP_cases")
        ])
    
    # L_NSRP_Items and H_NSRP_Items calculations
    Drivers = Drivers.with_columns([
        (pl.col("L_NSRP") * pl.col("case_capacity")).alias("L_NSRP_Items"),
        (pl.col("H_NSRP") * pl.col("case_capacity")).alias("H_NSRP_Items")
    ])
    
    #################################################
    # Hook Calculations and PMG Adjustments (L1089-L1200)
    #################################################
    
    # Hook calculation logic with PMG conditions
    pmg_condition = pl.col("pmg").is_in(["DRY13", "HDL21", "PPD02"])
    
    try:
        # L_Hook Fill Cases calculation
        Drivers = Drivers.with_columns([
            pl.when(pmg_condition)
            .then(pl.col("L_NSRP"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("L_Hook Fill Cases")
        ])
        
        # H_Hook Fill Cases calculation
        Drivers = Drivers.with_columns([
            pl.when(pmg_condition)
            .then(pl.col("H_NSRP"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("H_Hook Fill Cases")
        ])
        
        # Hook Fill Items calculation
        Drivers = Drivers.with_columns([
            pl.when(pmg_condition)
            .then(pl.col("L_NSRP_Items") + pl.col("H_NSRP_Items"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("Hook Fill Items")
        ])
        
        # Update L_NSRP - zero out for PMG conditions
        Drivers = Drivers.with_columns([
            pl.when(pmg_condition)
            .then(0)
            .otherwise(pl.col("L_NSRP"))
            .cast(pl.Float32)
            .alias("L_NSRP")
        ])
        
        # Update H_NSRP - zero out for PMG conditions
        Drivers = Drivers.with_columns([
            pl.when(pmg_condition)
            .then(0)
            .otherwise(pl.col("H_NSRP"))
            .cast(pl.Float32)
            .alias("H_NSRP")
        ])
        
        # Update L_NSRP_Items - zero out for PMG conditions
        Drivers = Drivers.with_columns([
            pl.when(pmg_condition)
            .then(0)
            .otherwise(pl.col("L_NSRP_Items"))
            .cast(pl.Float32)
            .alias("L_NSRP_Items")
        ])
        
        # Update H_NSRP_Items - zero out for PMG conditions
        Drivers = Drivers.with_columns([
            pl.when(pmg_condition)
            .then(0)
            .otherwise(pl.col("H_NSRP_Items"))
            .cast(pl.Float32)
            .alias("H_NSRP_Items")
        ])
        
    except:
        # Fallback if PMG column doesn't exist
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("L_Hook Fill Cases"),
            pl.lit(0).alias("H_Hook Fill Cases"),
            pl.lit(0).alias("Hook Fill Items")
        ])
    
    # Add Secondary SRP/NSRP to existing SRP calculations
    try:
        # Update L_SRP with Secondary SRP cases
        Drivers = Drivers.with_columns([
            pl.when((pl.col("srp_split_pallet") > 0) & (pl.col("light") == 1))
            .then(pl.col("L_SRP") + pl.col("Sec_SRP_cases"))
            .otherwise(pl.col("L_SRP"))
            .alias("L_SRP")
        ])
        
        # Update H_SRP with Secondary SRP cases
        Drivers = Drivers.with_columns([
            pl.when((pl.col("srp_split_pallet") > 0) & (pl.col("heavy") == 1))
            .then(pl.col("H_SRP") + pl.col("Sec_SRP_cases"))
            .otherwise(pl.col("H_SRP"))
            .alias("H_SRP")
        ])
        
        # Update L_NSRP with Secondary NSRP cases
        Drivers = Drivers.with_columns([
            pl.when((pl.col("nsrp") > 0) & (pl.col("light") == 1))
            .then(pl.col("L_NSRP") + pl.col("Sec_NSRP_cases"))
            .otherwise(pl.col("L_NSRP"))
            .alias("L_NSRP")
        ])
        
        # Update H_NSRP with Secondary NSRP cases
        Drivers = Drivers.with_columns([
            pl.when((pl.col("nsrp") > 0) & (pl.col("heavy") == 1))
            .then(pl.col("H_NSRP") + pl.col("Sec_NSRP_cases"))
            .otherwise(pl.col("H_NSRP"))
            .alias("H_NSRP")
        ])
        
    except:
        pass
    
    # H_CASE calculations based on weight conditions
    try:
        Drivers = Drivers.with_columns([
            pl.when(pl.col("weight") <= 1.5)
            .then(pl.col("H_NSRP") * pl.col("case_capacity"))
            .otherwise(0)
            .alias("H_CASE_L_NSRP_items"),
            
            pl.when(pl.col("weight") > 1.5)
            .then(pl.col("H_NSRP") * pl.col("case_capacity"))
            .otherwise(0)
            .alias("H_CASE_H_NSRP_items")
        ])
    except:
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("H_CASE_L_NSRP_items"),
            pl.lit(0).alias("H_CASE_H_NSRP_items")
        ])
    
    # High pallet cases calculations for DRY30 and DRY24 PMGs
    try:
        dry_pmg_condition = pl.col("pmg").is_in(["DRY30", "DRY24"])
        
        # High pallet cases on DRY30 and DRY24 (Full Pallet)
        Drivers = Drivers.with_columns([
            pl.when((pl.col("full_pallet") > 0) & dry_pmg_condition)
            .then(pl.col("Full Pallet Cases") * 0.2)
            .otherwise(0)
            .alias("High_pallet_cases_on_Dry30_and_DRY24")
        ])
        
        # High pallets on DRY30 and DRY24 (Full Pallet)
        Drivers = Drivers.with_columns([
            pl.when((pl.col("full_pallet") > 0) & dry_pmg_condition)
            .then(pl.col("Full Pallet"))
            .otherwise(0)
            .alias("High_pallets_on_Dry30_and_DRY24")
        ])
        
        # High half pallet cases on DRY30 and DRY24 (MU)
        Drivers = Drivers.with_columns([
            pl.when((pl.col("mu") > 0) & dry_pmg_condition)
            .then(pl.col("MU cases") * 0.2)
            .otherwise(0)
            .alias("High_half_pallet_cases_on_Dry30_and_DRY24")
        ])
        
        # High half pallets on DRY30 and DRY24 (MU)
        Drivers = Drivers.with_columns([
            pl.when((pl.col("mu") > 0) & dry_pmg_condition)
            .then(pl.col("MU Pallet"))
            .otherwise(0)
            .alias("High_half_pallets_on_Dry30_and_DRY24")
        ])
        
    except:
        # Fallback if PMG column doesn't exist
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("High_pallet_cases_on_Dry30_and_DRY24"),
            pl.lit(0).alias("High_pallets_on_Dry30_and_DRY24"),
            pl.lit(0).alias("High_half_pallet_cases_on_Dry30_and_DRY24"),
            pl.lit(0).alias("High_half_pallets_on_Dry30_and_DRY24")
        ])
    
    #################################################
    # Tagging Logic, Bulk Calculations, and Opening Types (L1201-L1444)
    #################################################
    
    # Tagging logic: Convert to pandas, apply tagging, convert back to Polars
    try:
        # Convert Polars DataFrame to pandas for tagging_on_product function
        drivers_pandas = Drivers.to_pandas()
        
        # Apply the pandas-based tagging logic
        drivers_pandas = tagging_on_product(drivers_pandas)
        
        # Convert back to Polars DataFrame
        Drivers = pl.from_pandas(drivers_pandas)
        
        print("✅ Tagging logic applied successfully (pandas conversion)")
        
    except Exception as e:
        print(f"⚠️ Warning: Tagging logic failed: {str(e)}")
        # Add fallback tag columns if tagging fails
        tag_columns = [
            "Hard_Tag", "Gillette_Tag", "Electro_Tag", "Soft_Tag", 
            "Salami_Tag", "Safer_Tag", "Bottle_Tag", "BliszterfülTag", 
            "CrocoTag", "Tag_total_nr"
        ]
        
        for col in tag_columns:
            if col not in Drivers.columns:
                Drivers = Drivers.with_columns([pl.lit(0).alias(col)])
    
    # Bulk Pallets calculation
    Drivers = Drivers.with_columns([
        (pl.col("Full Pallet") + pl.col("MU Pallet")).alias("Bulk Pallets")
    ])
    
    # Total RC's and Pallets calculation
    Drivers = Drivers.with_columns([
        (pl.col("Replenished Rollcages") + 
         pl.col("Replenished Pallets") +
         pl.col("Backstock Rollcages") +
         pl.col("Backstock Pallets") +
         pl.col("Bulk Pallets"))
        .alias("Total RC's and Pallets")
    ])
    
    # Opening type calculations with error handling
    try:
        # Define base results for opening type calculations
        cap_cases_col = pl.col("Capping Shelf Cases")
        
        # Tray + Hood conditions and calculations
        tray_hood_cond1 = (pl.col("opening_type") == "Tray + Hood") & (pl.col("light") == 1) & (pl.col("srp_split_pallet") > 0)
        tray_hood_cond2 = (pl.col("opening_type") == "Tray + Hood") & (pl.col("light") == 1) & (pl.col("nsrp") > 0)
        tray_hood_cond3 = (pl.col("opening_type") == "Tray + Hood") & (pl.col("heavy") == 1) & (pl.col("srp_split_pallet") > 0)
        tray_hood_cond4 = (pl.col("opening_type") == "Tray + Hood") & (pl.col("heavy") == 1) & (pl.col("nsrp") > 0)
        
        Drivers = Drivers.with_columns([
            pl.when(tray_hood_cond1).then(pl.col("L_SRP") + cap_cases_col)
            .when(tray_hood_cond2).then(pl.col("L_NSRP") + cap_cases_col)
            .when(tray_hood_cond3).then(pl.col("H_SRP") + cap_cases_col)
            .when(tray_hood_cond4).then(pl.col("H_NSRP") + cap_cases_col)
            .otherwise(0)
            .cast(pl.Float32)
            .alias("Ownbrand_tray_with_hood_cases")
        ])
        
        # Perforated box conditions and calculations
        perf_box_cond1 = (pl.col("opening_type") == "Perforated box") & (pl.col("light") == 1) & (pl.col("srp_split_pallet") > 0)
        perf_box_cond2 = (pl.col("opening_type") == "Perforated box") & (pl.col("light") == 1) & (pl.col("nsrp") > 0)
        perf_box_cond3 = (pl.col("opening_type") == "Perforated box") & (pl.col("heavy") == 1) & (pl.col("srp_split_pallet") > 0)
        perf_box_cond4 = (pl.col("opening_type") == "Perforated box") & (pl.col("heavy") == 1) & (pl.col("nsrp") > 0)
        
        Drivers = Drivers.with_columns([
            pl.when(perf_box_cond1).then(pl.col("L_SRP") + cap_cases_col)
            .when(perf_box_cond2).then(pl.col("L_NSRP") + cap_cases_col)
            .when(perf_box_cond3).then(pl.col("H_SRP") + cap_cases_col)
            .when(perf_box_cond4).then(pl.col("H_NSRP") + cap_cases_col)
            .otherwise(0)
            .cast(pl.Float32)
            .alias("Ownbrand_perforated_box_cases")
        ])
        
        # Shrink conditions and calculations
        shrink_cond1 = (pl.col("opening_type") == "Shrink") & (pl.col("light") == 1) & (pl.col("srp_split_pallet") > 0)
        shrink_cond2 = (pl.col("opening_type") == "Shrink") & (pl.col("light") == 1) & (pl.col("nsrp") > 0)
        shrink_cond3 = (pl.col("opening_type") == "Shrink") & (pl.col("heavy") == 1) & (pl.col("srp_split_pallet") > 0)
        shrink_cond4 = (pl.col("opening_type") == "Shrink") & (pl.col("heavy") == 1) & (pl.col("nsrp") > 0)
        
        Drivers = Drivers.with_columns([
            pl.when(shrink_cond1).then(pl.col("L_SRP") + cap_cases_col)
            .when(shrink_cond2).then(pl.col("L_NSRP") + cap_cases_col)
            .when(shrink_cond3).then(pl.col("H_SRP") + cap_cases_col)
            .when(shrink_cond4).then(pl.col("H_NSRP") + cap_cases_col)
            .otherwise(0)
            .cast(pl.Float32)
            .alias("Ownbrand_shrink_cases")
        ])
        
        # Tray + Shrink conditions and calculations
        tray_shrink_cond1 = (pl.col("opening_type") == "Tray + Shrink") & (pl.col("light") == 1) & (pl.col("srp_split_pallet") > 0)
        tray_shrink_cond2 = (pl.col("opening_type") == "Tray + Shrink") & (pl.col("light") == 1) & (pl.col("nsrp") > 0)
        tray_shrink_cond3 = (pl.col("opening_type") == "Tray + Shrink") & (pl.col("heavy") == 1) & (pl.col("srp_split_pallet") > 0)
        tray_shrink_cond4 = (pl.col("opening_type") == "Tray + Shrink") & (pl.col("heavy") == 1) & (pl.col("nsrp") > 0)
        
        Drivers = Drivers.with_columns([
            pl.when(tray_shrink_cond1).then(pl.col("L_SRP") + cap_cases_col)
            .when(tray_shrink_cond2).then(pl.col("L_NSRP") + cap_cases_col)
            .when(tray_shrink_cond3).then(pl.col("H_SRP") + cap_cases_col)
            .when(tray_shrink_cond4).then(pl.col("H_NSRP") + cap_cases_col)
            .otherwise(0)
            .cast(pl.Float32)
            .alias("Ownbrand_tray_with_shrink_cases")
        ])
        
        # Tray conditions and calculations
        tray_cond1 = (pl.col("opening_type") == "Tray") & (pl.col("light") == 1) & (pl.col("srp_split_pallet") > 0)
        tray_cond2 = (pl.col("opening_type") == "Tray") & (pl.col("light") == 1) & (pl.col("nsrp") > 0)
        tray_cond3 = (pl.col("opening_type") == "Tray") & (pl.col("heavy") == 1) & (pl.col("srp_split_pallet") > 0)
        tray_cond4 = (pl.col("opening_type") == "Tray") & (pl.col("heavy") == 1) & (pl.col("nsrp") > 0)
        
        Drivers = Drivers.with_columns([
            pl.when(tray_cond1).then(pl.col("L_SRP") + cap_cases_col)
            .when(tray_cond2).then(pl.col("L_NSRP") + cap_cases_col)
            .when(tray_cond3).then(pl.col("H_SRP") + cap_cases_col)
            .when(tray_cond4).then(pl.col("H_NSRP") + cap_cases_col)
            .otherwise(0)
            .cast(pl.Float32)
            .alias("Ownbrand_tray_cases")
        ])
        
        # Total ownbrand calculations
        Drivers = Drivers.with_columns([
            pl.when(pl.col("opening_type") == "no_data").then(0).otherwise(1).alias("total_ownbrand_op_type"),
            
            (pl.col("Ownbrand_tray_with_hood_cases") +
             pl.col("Ownbrand_perforated_box_cases") +
             pl.col("Ownbrand_shrink_cases") +
             pl.col("Ownbrand_tray_with_shrink_cases") +
             pl.col("Ownbrand_tray_cases"))
            .alias("total_ownbrand_op_cases")
        ])
        
    except:
        # Fallback if opening_type columns don't exist
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("Ownbrand_tray_with_hood_cases"),
            pl.lit(0).alias("Ownbrand_perforated_box_cases"),
            pl.lit(0).alias("Ownbrand_shrink_cases"),
            pl.lit(0).alias("Ownbrand_tray_with_shrink_cases"),
            pl.lit(0).alias("Ownbrand_tray_cases"),
            pl.lit(0).alias("total_ownbrand_op_type"),
            pl.lit(0).alias("total_ownbrand_op_cases")
        ])
    
    # Split pallet customizations with PMG/division conditions
    try:
        # Light split pallet conditions
        l_split_cond1 = pl.col("pmg").is_in(["DRY24", "BWS01"]) & (pl.col("split_pallet") > 0) & (pl.col("light") == 1)
        l_split_cond2 = pl.col("division").is_in(["Grocery"]) & (~pl.col("pmg").is_in(["DRY24", "BWS01"])) & (pl.col("split_pallet") > 0) & (pl.col("light") == 1)
        l_split_cond3 = pl.col("division").is_in(["GM"]) & (pl.col("split_pallet") > 0) & (pl.col("light") == 1)
        
        # Apply split pallet modifications to ownbrand columns
        ownbrand_cols = ["Ownbrand_tray_with_hood_cases", "Ownbrand_perforated_box_cases", "Ownbrand_shrink_cases", "Ownbrand_tray_with_shrink_cases", "Ownbrand_tray_cases"]
        
        for col in ownbrand_cols:
            Drivers = Drivers.with_columns([
                pl.when(l_split_cond1).then(pl.col(col) * 0)
                .when(l_split_cond2).then(pl.col(col) * 0.25)
                .when(l_split_cond3).then(pl.col(col) * 1)
                .otherwise(pl.col(col))
                .alias(col)
            ])
        
        # L_split_pallet_cases_for_opening calculation
        Drivers = Drivers.with_columns([
            pl.when(l_split_cond1).then(pl.col("L_SRP") * 0)
            .when(l_split_cond2).then(pl.col("L_SRP") * 0.25)
            .when(l_split_cond3).then(pl.col("L_SRP") * 1)
            .otherwise(0)
            .alias("L_split_pallet_cases_for_opening")
        ])
        
        # Heavy split pallet conditions
        h_split_cond1 = pl.col("pmg").is_in(["DRY24", "BWS01"]) & (pl.col("split_pallet") > 0) & (pl.col("heavy") == 1)
        h_split_cond2 = pl.col("division").is_in(["Grocery"]) & (~pl.col("pmg").is_in(["DRY24", "BWS01"])) & (pl.col("split_pallet") > 0) & (pl.col("heavy") == 1)
        h_split_cond3 = pl.col("division").is_in(["GM"]) & (pl.col("split_pallet") > 0) & (pl.col("heavy") == 1)
        
        # Apply heavy split pallet modifications to ownbrand columns
        for col in ownbrand_cols:
            Drivers = Drivers.with_columns([
                pl.when(h_split_cond1).then(pl.col(col) * 0)
                .when(h_split_cond2).then(pl.col(col) * 0.25)
                .when(h_split_cond3).then(pl.col(col) * 1)
                .otherwise(pl.col(col))
                .alias(col)
            ])
        
        # H_split_pallet_cases_for_opening calculation
        Drivers = Drivers.with_columns([
            pl.when(h_split_cond1).then(pl.col("H_SRP") * 0)
            .when(h_split_cond2).then(pl.col("H_SRP") * 0.25)
            .when(h_split_cond3).then(pl.col("H_SRP") * 1)
            .otherwise(0)
            .alias("H_split_pallet_cases_for_opening")
        ])
        
    except:
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("L_split_pallet_cases_for_opening"),
            pl.lit(0).alias("H_split_pallet_cases_for_opening")
        ])
    
    # SRP/NSRP for opening type calculations
    try:
        # L_SRP_for_opening_type calculation
        Drivers = Drivers.with_columns([
            pl.when((pl.col("srp") > 0) & (pl.col("light") == 1) & 
                   (pl.col("total_ownbrand_op_type") == 0) & 
                   (pl.col("opening_type") != "Returnable Plastic Crate"))
            .then(pl.col("L_SRP") + pl.col("Capping Shelf Cases"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("L_SRP_for_opening_type")
        ])
        
        # Update L_SRP_for_opening_type with split pallet logic
        Drivers = Drivers.with_columns([
            pl.when((pl.col("split_pallet") > 0) & (pl.col("light") == 1) & 
                   (pl.col("total_ownbrand_op_type") == 0) & 
                   (pl.col("opening_type") != "Returnable Plastic Crate"))
            .then(pl.col("L_split_pallet_cases_for_opening"))
            .otherwise(pl.col("L_SRP_for_opening_type"))
            .cast(pl.Float32)
            .alias("L_SRP_for_opening_type")
        ])
        
        # H_SRP_for_opening_type calculation
        Drivers = Drivers.with_columns([
            pl.when((pl.col("srp") > 0) & (pl.col("heavy") == 1) & 
                   (pl.col("total_ownbrand_op_type") == 0) & 
                   (pl.col("opening_type") != "Returnable Plastic Crate"))
            .then(pl.col("H_SRP") + pl.col("Capping Shelf Cases"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("H_SRP_for_opening_type")
        ])
        
        # Update H_SRP_for_opening_type with split pallet logic
        Drivers = Drivers.with_columns([
            pl.when((pl.col("split_pallet") > 0) & (pl.col("heavy") == 1) & 
                   (pl.col("total_ownbrand_op_type") == 0) & 
                   (pl.col("opening_type") != "Returnable Plastic Crate"))
            .then(pl.col("H_split_pallet_cases_for_opening"))
            .otherwise(pl.col("H_SRP_for_opening_type"))
            .cast(pl.Float32)
            .alias("H_SRP_for_opening_type")
        ])
        
        # L_NSRP_for_opening_type and H_NSRP_for_opening_type calculations
        Drivers = Drivers.with_columns([
            pl.when((pl.col("nsrp") > 0) & (pl.col("light") == 1) & 
                   (pl.col("total_ownbrand_op_type") == 0) & 
                   (pl.col("opening_type") != "Returnable Plastic Crate"))
            .then(pl.col("L_NSRP") + pl.col("Capping Shelf Cases"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("L_NSRP_for_opening_type"),
            
            pl.when((pl.col("nsrp") > 0) & (pl.col("heavy") == 1) & 
                   (pl.col("total_ownbrand_op_type") == 0) & 
                   (pl.col("opening_type") != "Returnable Plastic Crate"))
            .then(pl.col("H_NSRP") + pl.col("Capping Shelf Cases"))
            .otherwise(0)
            .cast(pl.Float32)
            .alias("H_NSRP_for_opening_type")
        ])
        
    except:
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("L_SRP_for_opening_type"),
            pl.lit(0).alias("H_SRP_for_opening_type"),
            pl.lit(0).alias("L_NSRP_for_opening_type"),
            pl.lit(0).alias("H_NSRP_for_opening_type")
        ])
    
    # Drop temporary columns (equivalent to pandas drop)
    try:
        Drivers = Drivers.drop(["total_ownbrand_op_type", "total_ownbrand_op_cases"])
    except:
        pass
    
    # Empty equipment calculations with fallbacks for missing columns
    # Add fallbacks for required columns
    required_cols = ["Bulk Pallets", "Replenished Pallets", "Replenished Rollcages"]
    for col in required_cols:
        if col not in Drivers.columns:
            print(f"Adding missing '{col}' column")
            Drivers = Drivers.with_columns([pl.lit(0.0).cast(pl.Float32).alias(col)])
    
    # Step 1: Calculate Empty Pallets and Empty Rollcages
    Drivers = Drivers.with_columns([
        (pl.col("Bulk Pallets") + pl.col("Replenished Pallets")).alias("Empty Pallets"),
        pl.col("Replenished Rollcages").alias("Empty Rollcages")
    ])
    
    # Step 2: Calculate Empty Shelf Trolley using the newly created columns
    Drivers = Drivers.with_columns([
        (pl.col("Empty Pallets") * pl.lit(shelf_trolley_cap_ratio_to_pallet) +
         pl.col("Empty Rollcages") * pl.lit(shelf_trolley_cap_ratio_to_rollcage))
        .alias("Empty Shelf Trolley")
    ])
    
    #################################################
    # PBL/PBS Settings and Final Calculations (L1447-L1753)
    #################################################
    
    # PBL PBS settings with error handling
    try:
        Drivers = Drivers.with_columns([
            # PBL PBS basic calculations
            (pl.col("Replenished Rollcages PBL") + pl.col("Replenished Pallets PBL"))
            .alias("Add_Walking PBL Cages"),
            
            pl.col("Replenished Rollcages PBS").alias("Add_Walking PBS Cages"),
            
            pl.lit(0).cast(pl.Float32).alias("Add_Walking PBL Pallets"),
            
            pl.col("Replenished Pallets PBS").alias("Add_Walking PBS Pallets")
        ])
        
        # PBS/PBL conditional logic
        Drivers = Drivers.with_columns([
            # Update Add_Walking PBL Pallets
            pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
            .then(0)
            .otherwise(pl.col("Add_Walking PBL Pallets"))
            .cast(pl.Float32)
            .alias("Add_Walking PBL Pallets"),
            
            # Update Add_Walking PBS Pallets
            pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
            .then(pl.col("Bulk Pallets"))
            .otherwise(pl.col("Add_Walking PBS Pallets"))
            .cast(pl.Float32)
            .alias("Add_Walking PBS Pallets")
        ])
        
    except:
        # Fallback if PBL/PBS columns don't exist
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("Add_Walking PBL Cages"),
            pl.lit(0).alias("Add_Walking PBS Cages"),
            pl.lit(0).alias("Add_Walking PBL Pallets"),
            pl.lit(0).alias("Add_Walking PBS Pallets")
        ])
    
    # Add_Walking calculations
    Drivers = Drivers.with_columns([
        pl.col("Backstock Rollcages").cast(pl.Float32).alias("Add_Walking Backstock Cages"),
        pl.col("Replenished Rollcages").cast(pl.Float32).alias("Add_Walking Cages"),
        
        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
        .then(pl.col("Bulk Pallets"))
        .otherwise(pl.col("Replenished Pallets"))
        .cast(pl.Float32)
        .alias("Add_Walking Pallets")
    ])
    
    # Sold cases calculation with NaN/Inf handling
    Drivers = Drivers.with_columns([
        (pl.col("sold_units") / pl.col("case_capacity"))
        .map_elements(lambda x: 0 if np.isnan(x) or np.isinf(x) else x, return_dtype=pl.Float32)
        .alias("sold_cases")
    ])
    
    # Rename sales column if it exists
    try:
        Drivers = Drivers.rename({"sales_excl_vat": "sales"})
    except:
        pass
    
    # Single Pick customization
    try:
        Drivers = Drivers.with_columns([
            pl.when(pl.col("single_pick") > 0)
            .then(pl.col("cases_to_replenish"))
            .otherwise(0)
            .alias("single_pick_items"),
            
            pl.when(pl.col("single_pick") > 0)
            .then(0)
            .otherwise(pl.col("cases_delivered"))
            .alias("cases_delivered_rsu")
        ])
    except:
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("single_pick_items"),
            pl.col("cases_delivered").alias("cases_delivered_rsu")
        ])
    
    # Stock calculations with fallbacks for missing columns
    # Add fallback for stock column if missing
    if "stock" not in Drivers.columns:
        print("Adding missing 'stock' column")
        Drivers = Drivers.with_columns([pl.lit(0.0).cast(pl.Float32).alias("stock")])
    
    # Step 1: Calculate stock_unit_weekly
    Drivers = Drivers.with_columns([
        (pl.col("stock") / pl.lit(weekdays_to_divide)).alias("stock_unit_weekly")
    ])
    
    # Step 2: Calculate stock_cases_weekly using the newly created column
    Drivers = Drivers.with_columns([
        (pl.col("stock_unit_weekly") / pl.col("case_capacity")).alias("stock_cases_weekly")
    ])
    
    # icreamNSRP logic
    try:
        Drivers = Drivers.with_columns([
            pl.when(pl.col("icream_nsrp") > 0)
            .then(pl.col("L_NSRP_Items") + pl.col("H_NSRP_Items"))
            .otherwise(0)
            .alias("icreamNSRP")
        ])
        
        # Zero out specific columns when icream_nsrp > 0
        icream_cols = ['H_CASE_H_NSRP_items', 'H_CASE_L_NSRP_items', 'L_NSRP_Items', 'H_NSRP_Items']
        for col in icream_cols:
            try:
                Drivers = Drivers.with_columns([
                    pl.when(pl.col("icream_nsrp") > 0)
                    .then(0)
                    .otherwise(pl.col(col))
                    .alias(col)
                ])
            except:
                pass
        
    except:
        Drivers = Drivers.with_columns([pl.lit(0).alias("icreamNSRP")])
    
    # shelfService_gm conditional logic
    if shelfService_gm == True:
        columns_for_shelService_deletion = [
            "Replenished Rollcages", "Replenished Pallets", "Backstock Pallets", "Backstock Rollcages",
            "Backstock Shelf Trolley", "Empty Pallets", "Empty Rollcages", "Empty Shelf Trolley",
            "Full Pallet", "Full Pallet Cases", "H_CASE_H_NSRP_items", "H_CASE_L_NSRP_items",
            "H_Hook Fill Cases", "H_NSRP_for_opening_type", "H_SRP", "H_NSRP", "H_SRP_for_opening_type",
            "Hook Fill Items", "L_NSRP", "L_NSRP_for_opening_type", "L_NSRP_Items", "L_SRP",
            "L_SRP_for_opening_type", "MU Pallet", "New Delivery - Pallets", "New Delivery - Rollcages",
            "New Delivery - Shelf Trolley", "Total RC's and Pallets", "Bulk Pallets",
            "Add_Walking Backstock Cages", "Add_Walking Pallets", "Racking Pallets", "Full + Half Pallet Cases"
        ]
        
        for col in columns_for_shelService_deletion:
            try:
                Drivers = Drivers.with_columns([
                    pl.when(pl.col("shelfservice_flag") == 1)
                    .then(0)
                    .otherwise(pl.col(col))
                    .alias(col)
                ])
            except:
                pass
    
    # Foil disassemble calculations
    try:
        foilDisassemble_cols = [
            'H_NSRP_for_opening_type', 'L_NSRP_for_opening_type', 'L_SRP_for_opening_type',
            'H_SRP_for_opening_type', 'Ownbrand_perforated_box_cases', 'Ownbrand_shrink_cases',
            'Ownbrand_tray_cases', 'Ownbrand_tray_with_hood_cases', 'Ownbrand_tray_with_shrink_cases'
        ]
        
        # Sum horizontal for total cases to disassemble
        valid_cols = [col for col in foilDisassemble_cols if col in Drivers.columns]
        if valid_cols:
            Drivers = Drivers.with_columns([
                pl.sum_horizontal(valid_cols).alias("total_cases_to_disassemble")
            ])
        else:
            Drivers = Drivers.with_columns([pl.lit(0).alias("total_cases_to_disassemble")])
        
        # Cases foil to disassemble calculation
        Drivers = Drivers.with_columns([
            pl.when((pl.col("total_cases_to_disassemble") > 0) & (pl.col("Foil_Cases") > 0))
            .then(pl.col("Foil_Cases") * pl.col("extra disassemble %"))
            .otherwise(0)
            .alias("cases_foil_to_disassemble")
        ])
        
    except:
        Drivers = Drivers.with_columns([
            pl.lit(0).alias("total_cases_to_disassemble"),
            pl.lit(0).alias("cases_foil_to_disassemble")
        ])
    
    # Final column adjustments
    Drivers = Drivers.with_columns([
        (pl.lit(1) / weekdays_to_divide).alias("nr_of_tpn"),
        (pl.col("stock") / weekdays_to_divide).alias("weekly_stock"),
        (pl.col("shelfCapacity") / weekdays_to_divide).alias("shelfCapacity"),
        (pl.col("case_capacity") / weekdays_to_divide).alias("case_capacity"),
        (pl.col("cases_to_replenish") + pl.col("Clip Strip Cases")).alias("cases_to_replenish"),
        pl.col("pallet_capacity").alias("pallet_capacity_avg")
    ])
    
    # Final column selection (equivalent to pandas column selection)
    final_columns = [
        "country", "store", "day", "dep", "pmg", "t_touch", "cases_to_replenish",
        "Sec_NSRP_cases", "Sec_SRP_cases", "light", "heavy", "backstock pallet ratio",
        "Replenished Rollcages", "Replenished Pallets", "tpnb", "sold_units", "sold_cases",
        "sales", "stock", "weekly_stock", "cases_delivered", "cases_delivered_on_sf",
        "cases_delivered_rsu", "Add_Walking Cages", "pallet_capacity", "Add_Walking Backstock Cages",
        "Add_Walking Pallets", "Backstock unit", "Backstock Cases", "Backstock Pallets",
        "Backstock Rollcages", "Backstock Shelf Trolley", "Backstock_tpn_nr", "backroom_pallets",
        "Bottle_Tag", "Broken Items", "broken_case_flag", "Bulk Pallets", "Capping Shelf Cases",
        "Clip Strip Cases", "Clip Strip Items", "Electro_Tag", "Empty Pallets", "Empty Rollcages",
        "Empty Shelf Trolley", "Foil_Cases", "Full Pallet", "Full Pallet Cases",
        "Full + Half Pallet Cases", "icreamNSRP", "Gillette_Tag", "H_Post-sort Cases",
        "H_CASE_H_NSRP_items", "H_CASE_L_NSRP_items", "single_pick_items", "H_Hook Fill Cases",
        "L_Hook Fill Cases", "H_NSRP_for_opening_type", "H_Pre-sorted Cases", "H_SRP",
        "H_NSRP", "H_SRP_for_opening_type", "Hard_Tag", "Hook Fill Items", "L_Post-sort Cases",
        "L_NSRP", "L_NSRP_for_opening_type", "L_NSRP_Items", "L_Pre-sorted Cases", "L_SRP",
        "L_SRP_for_opening_type", "MU Pallet", "New Delivery - Pallets", "New Delivery - Rollcages",
        "New Delivery - Shelf Trolley", "Ownbrand_perforated_box_cases", "Ownbrand_shrink_cases",
        "Ownbrand_tray_cases", "Ownbrand_tray_with_hood_cases", "Ownbrand_tray_with_shrink_cases",
        "Pre-sorted Rollcages", "Pre-sorted Shelf Trolley", "Post-sort Cases", "Post-sort Pallets",
        "Post-sort Rollcages", "Racking Pallets", "Safer_Tag", "Salami_Tag", "Soft_Tag",
        "CrocoTag", "BliszterfülTag", "Total RC's and Pallets", "High_pallet_cases_on_Dry30_and_DRY24",
        "High_pallets_on_Dry30_and_DRY24", "High_half_pallet_cases_on_Dry30_and_DRY24",
        "High_half_pallets_on_Dry30_and_DRY24", "Tag_total_nr", "shelfCapacity", "nr_of_tpn",
        "unit", "case_capacity", "Two Touch Cases", "Two Touch unit", "total_cases_to_disassemble",
        "cases_foil_to_disassemble", "stock_cases_weekly", "Add_Walking PBL Cages",
        "Add_Walking PBS Cages", "Add_Walking PBL Pallets", "Add_Walking PBS Pallets",
        "PBL_CAGE", "PBL_PALLET", "PBS_CAGE", "PBS_PALLET", "pallet_capacity_avg"
    ]
    
    # Select only existing columns to avoid errors
    existing_columns = [col for col in final_columns if col in Drivers.columns]
    Drivers = Drivers.select(existing_columns)
    
    # Note: optimize_objects(optimize_types(Drivers)) equivalent would be:
    # - Polars automatically optimizes data types and memory usage
    # - Additional optimization could be done with .shrink_to_fit() if needed
    
    # Create drivers_produce DataFrame (subset for produce items)
    # Filter for produce-related items based on common produce indicators
    try:
        drivers_produce = Drivers.filter(
            (pl.col("pmg").str.contains("(?i)produce|fruit|veg|fresh", strict=False)) |
            (pl.col("dep").str.contains("(?i)produce|fruit|veg|fresh", strict=False)) |
            (pl.col("product_name").str.contains("(?i)produce|fruit|veg|fresh", strict=False))
        )
    except:
        # Fallback: create empty DataFrame with same schema if filtering fails
        drivers_produce = Drivers.head(0)
    
    return Drivers, drivers_produce
