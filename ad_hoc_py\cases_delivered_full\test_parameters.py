#!/usr/bin/env python3
"""
Test parameter validation with different date ranges
"""

import sys
import os
import tempfile
from pathlib import Path
import re

def test_parameter_substitution():
    """Test parameter substitution with different date ranges"""
    print('🧪 Testing parameter substitution with different date ranges...')
    
    # Test cases with different date formats
    test_cases = [
        {
            'name': 'Current dates (20250630)',
            'start': '20250630',
            'end': '20250630'
        },
        {
            'name': 'Different dates (20250701-20250731)',
            'start': '20250701',
            'end': '20250731'
        },
        {
            'name': 'Year boundary (20241231-20250101)',
            'start': '20241231',
            'end': '20250101'
        }
    ]
    
    # Sample SQL content similar to the actual file
    sample_sql = """
    SELECT * FROM test_table 
    WHERE part_col BETWEEN 20240101 AND 20240131
    AND other_part_col BETWEEN 20240201 AND 20240228
    GROUP BY store, date
    """
    
    for test_case in test_cases:
        print(f"\n📋 Testing: {test_case['name']}")
        print(f"   Start: {test_case['start']}, End: {test_case['end']}")
        
        # Apply the regex patterns from delivered.py
        start_pattern = r"(?<=BETWEEN\s)(\d{8})"
        end_pattern = r"(?<=AND\s)(\d{8})"
        
        modified_content = re.sub(start_pattern, test_case['start'], sample_sql)
        modified_content = re.sub(end_pattern, test_case['end'], modified_content)
        
        print("   Modified SQL:")
        for line in modified_content.strip().split('\n'):
            if 'BETWEEN' in line or 'AND' in line:
                print(f"   {line.strip()}")
        
        # Verify the substitution
        expected_start = f"BETWEEN {test_case['start']}"
        expected_end = f"AND {test_case['end']}"
        
        if expected_start in modified_content and expected_end in modified_content:
            print("   ✅ Parameter substitution successful")
        else:
            print("   ❌ Parameter substitution failed")
    
    print("\n🔍 Testing edge cases...")
    
    # Test edge cases
    edge_cases = [
        "WHERE part_col BETWEEN 20240101 AND 20240131",
        "AND other_col BETWEEN 20240201 AND 20240228",
        "BETWEEN 20240301 AND 20240331 AND status = 'active'",
        "field BETWEEN 20240401 AND 20240430 OR field2 BETWEEN 20240501 AND 20240531"
    ]
    
    for edge_case in edge_cases:
        print(f"\n   Testing: {edge_case}")
        modified = re.sub(start_pattern, "20250630", edge_case)
        modified = re.sub(end_pattern, "20250630", modified)
        print(f"   Result:  {modified}")
        
        if "20250630" in modified and "202404" not in modified:
            print("   ✅ Edge case handled correctly")
        else:
            print("   ❌ Edge case failed")

def test_directory_creation():
    """Test directory creation logic"""
    print('\n📁 Testing directory creation logic...')
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test the directory creation logic from delivered.py
        saved_filename = "test_cases_delivered_20250630_20250630"
        place_to_save = temp_path / "inputs/files_for_dataset"
        
        # Create directories
        place_to_save.mkdir(parents=True, exist_ok=True)
        (place_to_save / saved_filename).mkdir(parents=True, exist_ok=True)
        
        # Verify creation
        if place_to_save.exists():
            print("   ✅ Main directory created successfully")
        else:
            print("   ❌ Main directory creation failed")
            
        if (place_to_save / saved_filename).exists():
            print("   ✅ Subdirectory created successfully")
        else:
            print("   ❌ Subdirectory creation failed")
        
        print(f"   📂 Created structure: {place_to_save / saved_filename}")

def test_error_scenarios():
    """Test error handling scenarios"""
    print('\n⚠️  Testing error handling scenarios...')
    
    # Test invalid date formats
    invalid_dates = ["invalid", "2025-06-30", "20250632", ""]
    
    for invalid_date in invalid_dates:
        print(f"\n   Testing invalid date: '{invalid_date}'")
        
        try:
            # This would be the type of validation we might want to add
            if not invalid_date or len(invalid_date) != 8 or not invalid_date.isdigit():
                print("   ✅ Invalid date detected (good)")
            else:
                print("   ❌ Invalid date not detected")
        except Exception as e:
            print(f"   ✅ Exception caught: {e}")

if __name__ == "__main__":
    print("🧪 Running Parameter Validation Tests")
    print("=" * 50)
    
    test_parameter_substitution()
    test_directory_creation()
    test_error_scenarios()
    
    print("\n" + "=" * 50)
    print("✅ Parameter validation tests completed!")
