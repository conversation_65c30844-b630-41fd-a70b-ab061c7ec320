import paramiko
import pandas as pd
import os
import time
from pathlib import Path

class DebugSRDDownloader:
    def __init__(self, hostname, password, local_folder="./srd_debug"):
        self.hostname = hostname
        self.password = password
        self.local_folder = Path(local_folder)
        self.local_folder.mkdir(exist_ok=True)
        
    def connect_ssh(self):
        """Establish SSH connection"""
        print("🔌 Connecting to server...")
        self.ssh_client = paramiko.SSHClient()
        self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.ssh_client.connect(hostname=self.hostname, username="phrubos", password=self.password, timeout=30)
        print("✅ Connected successfully")
    
    def debug_basic_commands(self):
        """Test basic commands first"""
        print("🔍 DEBUGGING: Testing basic commands")
        print("=" * 40)
        
        basic_tests = [
            ("pwd", "Check current directory"),
            ("whoami", "Check username"),
            ("ls -la /home/<USER>/", "Check home directory"),
            ("which beeline", "Check if beeline exists"),
            ("echo 'test' > /home/<USER>/test.txt", "Test file creation"),
            ("cat /home/<USER>/test.txt", "Test file reading"),
            ("rm /home/<USER>/test.txt", "Test file deletion")
        ]
        
        for cmd, description in basic_tests:
            print(f"🧪 {description}: {cmd}")
            stdin, stdout, stderr = self.ssh_client.exec_command(cmd, timeout=30)
            exit_status = stdout.channel.recv_exit_status()
            output = stdout.read().decode('utf-8').strip()
            errors = stderr.read().decode('utf-8').strip()
            
            if exit_status == 0:
                print(f"   ✅ Success: {output[:100]}")
            else:
                print(f"   ❌ Failed (exit {exit_status}): {errors[:100]}")
            print()
    
    def debug_beeline_connection(self):
        """Test beeline connection separately"""
        print("🔍 DEBUGGING: Testing beeline connection")
        print("=" * 40)
        
        # Test if beeline works at all
        simple_beeline = "/opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' -e 'SELECT 1 as test;'"
        
        print("🧪 Testing basic beeline connection...")
        stdin, stdout, stderr = self.ssh_client.exec_command(simple_beeline, timeout=60)
        exit_status = stdout.channel.recv_exit_status()
        output = stdout.read().decode('utf-8')
        errors = stderr.read().decode('utf-8')
        
        print(f"📊 Exit status: {exit_status}")
        print(f"📄 Output (last 300 chars): ...{output[-300:]}")
        if errors:
            print(f"⚠️ Errors (last 200 chars): ...{errors[-200:]}")
        
        return exit_status == 0
    
    def debug_table_access(self):
        """Test table access"""
        print("🔍 DEBUGGING: Testing table access")
        print("=" * 40)
        
        # Test table existence
        table_test = "/opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' -e 'SHOW TABLES IN sch_analysts;'"
        
        print("🧪 Testing table listing...")
        stdin, stdout, stderr = self.ssh_client.exec_command(table_test, timeout=60)
        exit_status = stdout.channel.recv_exit_status()
        output = stdout.read().decode('utf-8')
        
        print(f"📊 Exit status: {exit_status}")
        if "tbl_srd_planogram_analysis" in output:
            print("✅ SRD table found!")
        else:
            print("❌ SRD table not found in output")
            print(f"📄 Available tables: {output[-500:]}")
        
        return "tbl_srd_planogram_analysis" in output
    
    def method_super_simple(self):
        """Super simple method - just get data to console first"""
        print("🚀 SUPER SIMPLE METHOD: Console output first")
        print("=" * 40)
        
        # Simplest possible query
        simple_query = "/opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' -e 'SELECT Country, Store_Number, Store_Name FROM sch_analysts.tbl_srd_planogram_analysis LIMIT 3;'"
        
        print("🧪 Running simple query (3 rows, 3 columns)...")
        stdin, stdout, stderr = self.ssh_client.exec_command(simple_query, timeout=120)
        exit_status = stdout.channel.recv_exit_status()
        output = stdout.read().decode('utf-8')
        errors = stderr.read().decode('utf-8')
        
        print(f"📊 Exit status: {exit_status}")
        
        if exit_status == 0:
            print("✅ Query successful!")
            print("📄 Query output:")
            # Clean up the output to show just the data
            lines = output.split('\n')
            data_lines = [line for line in lines if line.strip() and not line.startswith('0: jdbc') and '+-' not in line]
            for line in data_lines[-10:]:  # Show last 10 relevant lines
                print(f"   {line}")
            return True
        else:
            print("❌ Query failed!")
            print(f"⚠️ Errors: {errors}")
            return False
    
    def method_file_redirect(self):
        """Try file redirection method"""
        print("🚀 FILE REDIRECT METHOD: Save to file")
        print("=" * 40)
        
        output_file = "/home/<USER>/simple_test.txt"
        
        # Use -f option for script file instead of -e
        script_content = "SELECT Country, Store_Number, Store_Name FROM sch_analysts.tbl_srd_planogram_analysis LIMIT 5;"
        
        # First create the SQL script
        create_script_cmd = f"echo '{script_content}' > /home/<USER>/test_query.sql"
        stdin, stdout, stderr = self.ssh_client.exec_command(create_script_cmd, timeout=30)
        stdout.channel.recv_exit_status()
        
        # Run beeline with script file and redirect output
        beeline_cmd = f"/opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' -f /home/<USER>/test_query.sql > {output_file} 2>&1"
        
        print("🧪 Running beeline with file redirect...")
        stdin, stdout, stderr = self.ssh_client.exec_command(beeline_cmd, timeout=120)
        exit_status = stdout.channel.recv_exit_status()
        
        print(f"📊 Exit status: {exit_status}")
        
        # Check if file was created
        check_cmd = f"ls -la {output_file} && cat {output_file}"
        stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd, timeout=30)
        file_output = stdout.read().decode('utf-8')
        
        if "No such file" not in file_output and len(file_output) > 50:
            print("✅ File created successfully!")
            print("📄 File contents:")
            print(file_output[-500:])  # Show last 500 chars
            
            # Try to download this file
            return self.download_simple_file(output_file, "test_output.txt")
        else:
            print("❌ File was not created")
            print(f"📄 Check output: {file_output}")
            return False
    
    def download_simple_file(self, remote_path, local_filename):
        """Download using SFTP"""
        local_path = self.local_folder / local_filename
        
        try:
            sftp = self.ssh_client.open_sftp()
            print(f"⬇️ Downloading {local_filename}...")
            sftp.get(remote_path, str(local_path))
            sftp.close()
            
            if local_path.exists():
                print(f"✅ Downloaded: {local_filename} ({local_path.stat().st_size} bytes)")
                
                # Show contents
                with open(local_path, 'r') as f:
                    content = f.read()
                    print("📄 Downloaded file contents:")
                    print(content[:500])
                
                return True
            else:
                print(f"❌ Download failed: {local_filename}")
                return False
                
        except Exception as e:
            print(f"❌ Download error: {e}")
            return False
    
    def run_debug_sequence(self):
        """Run complete debug sequence"""
        print("🕵️ DEBUGGING SRD DOWNLOAD ISSUES")
        print("=" * 50)
        
        try:
            # Step 1: Basic system checks
            self.debug_basic_commands()
            
            # Step 2: Test beeline connection
            if not self.debug_beeline_connection():
                print("❌ Beeline connection failed - cannot proceed")
                return False
            
            # Step 3: Test table access
            if not self.debug_table_access():
                print("❌ Cannot access SRD table - check permissions")
                return False
            
            # Step 4: Try simple query
            if not self.method_super_simple():
                print("❌ Simple query failed - check SQL syntax")
                return False
            
            # Step 5: Try file output
            if not self.method_file_redirect():
                print("❌ File redirect failed - trying alternative")
                return False
            
            print("\n🎉 All debug tests passed!")
            print("🚀 Ready to proceed with full download")
            return True
            
        except Exception as e:
            print(f"❌ Debug failed with error: {e}")
            return False
    
    def close(self):
        """Close SSH connection"""
        if hasattr(self, 'ssh_client'):
            self.ssh_client.close()
            print("🔌 Connection closed")

def debug_download():
    """Run the debug sequence"""
    hostname = "skpbj0003.global.tesco.org"
    password = "BigCityLife2005"
    
    debugger = DebugSRDDownloader(hostname, password)
    
    try:
        debugger.connect_ssh()
        success = debugger.run_debug_sequence()
        
        if success:
            print("\n✅ Debug completed - everything working!")
        else:
            print("\n❌ Debug found issues - check the output above")
            
    except Exception as e:
        print(f"❌ Debug error: {e}")
    finally:
        debugger.close()

if __name__ == "__main__":
    debug_download()