<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Productivity Model Dataset Explanation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .card h2 {
            color: #3498db;
            border-bottom: 2px solid #f1f1f1;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .dataset-preview {
            overflow-x: auto;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .category-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }
        .category {
            background-color: #f1f8ff;
            border: 1px solid #cce5ff;
            border-radius: 5px;
            padding: 8px 15px;
            font-size: 14px;
        }
        .important {
            color: #e74c3c;
            font-weight: bold;
        }
        .column-description {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }
        .column-card {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            border-left: 4px solid #3498db;
        }
        .column-name {
            font-weight: bold;
            color: #2c3e50;
        }
        .column-example {
            color: #7f8c8d;
            font-style: italic;
            font-size: 13px;
        }
        .date-range {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 10px 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        .key-insight {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 10px 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        .visual-diagram {
            margin: 30px 0;
            text-align: center;
        }
        .visual-diagram svg {
            max-width: 100%;
            height: auto;
        }
        .data-flow {
            max-width: 100%;
            height: auto;
        }
        .warning {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 10px 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Understanding the Productivity Replenishment "Model Dataset"</h1>
            <p>A comprehensive guide to our weekly average snapshot data</p>
        </div>

        <div class="card">
            <h2>What is the "Model Dataset"?</h2>
            <p>The "Model Dataset" is a weekly average snapshot of CE stores' performance calculated from data collected between <strong>May 27, 2024 to September 1, 2024</strong>. Instead of showing each individual day's performance, it provides a typical week profile showing average performance for each day of the week (Monday through Sunday).</p>
            
            <div class="date-range">
                <strong>Time Period:</strong> May 27, 2024 - September 1, 2024 (14 weeks)
            </div>
            
            <div class="key-insight">
                <strong>Key Insight:</strong> This is not raw daily data! It represents an <strong>average week</strong> calculated from the entire period.
            </div>
        </div>

        <div class="card">
            <h2>Dataset Preview Example</h2>
            <p>Here's an example of how the data is structured (there are lot more columns):</p>
            <div class="dataset-preview">
                <table>
                    <tr>
                        <th>store</th>
                        <th>pmg</th>
                        <th>tpnb</th>
                        <th>day</th>
                        <th>sold_units</th>
                        <th>stock</th>
                        <th>shelfCapacity</th>
                        <th>cases_delivered</th>
                        <th>country</th>
                        <th>product_name</th>
                        <th>Category name</th>
                    </tr>
                    <tr>
                        <td>21037</td>
                        <td>DRY13</td>
                        <td>105006461</td>
                        <td>Sunday</td>
                        <td>1.9</td>
                        <td>37.4</td>
                        <td>60.0</td>
                        <td>0.1</td>
                        <td>SK</td>
                        <td>ORION Margot s hrozienkami 80 g</td>
                        <td>SWEETS (excl. Biscuits)</td>
                    </tr>
                </table>
            </div>
            <p><strong>Note:</strong> The values like "1.9" sold units represent the <span class="important">average</span> number sold on Sundays during the data collection period.</p>
        </div>

        <div class="card">
            <h2>Data Categories</h2>
            <p>The dataset contains various categories of information (these are few of them):</p>
            
            <div class="category-group" style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px;">
                <div class="category" style="background-color: #f1f8ff; border: 1px solid #cce5ff; border-radius: 5px; padding: 8px 15px; font-size: 14px;">Store Information</div>
                <div class="category" style="background-color: #f1f8ff; border: 1px solid #cce5ff; border-radius: 5px; padding: 8px 15px; font-size: 14px;">Product Details</div>
                <div class="category" style="background-color: #f1f8ff; border: 1px solid #cce5ff; border-radius: 5px; padding: 8px 15px; font-size: 14px;">Sales Performance</div>
                <div class="category" style="background-color: #f1f8ff; border: 1px solid #cce5ff; border-radius: 5px; padding: 8px 15px; font-size: 14px;">Inventory Management</div>
                <div class="category" style="background-color: #f1f8ff; border: 1px solid #cce5ff; border-radius: 5px; padding: 8px 15px; font-size: 14px;">Product Categorization</div>
                <div class="category" style="background-color: #f1f8ff; border: 1px solid #cce5ff; border-radius: 5px; padding: 8px 15px; font-size: 14px;">Supplier Data</div>
                <div class="category" style="background-color: #f1f8ff; border: 1px solid #cce5ff; border-radius: 5px; padding: 8px 15px; font-size: 14px;">Merchandising Information</div>
              </div>
            
            <div class="column-description">
                <div class="column-card">
                    <div class="column-name">store</div>
                    <p>Store identifier number</p>
                    <div class="column-example">Example: 21037</div>
                </div>
                <div class="column-card">
                    <div class="column-name">day</div>
                    <p>Day of the week</p>
                    <div class="column-example">Example: Sunday</div>
                </div>
                <div class="column-card">
                    <div class="column-name">sold_units</div>
                    <p>Average number of units sold on this day of week</p>
                    <div class="column-example">Example: 1.9 units</div>
                </div>
                <div class="column-card">
                    <div class="column-name">stock</div>
                    <p>Average inventory level on this day of week</p>
                    <div class="column-example">Example: 37.4 units</div>
                </div>
                <div class="column-card">
                    <div class="column-name">shelfCapacity</div>
                    <p>Maximum number of units that fit on shelf</p>
                    <div class="column-example">Example: 60.0 units</div>
                </div>
                <div class="column-card">
                    <div class="column-name">cases_delivered</div>
                    <p>Average number of cases delivered on this day</p>
                    <div class="column-example">Example: 0.1 cases</div>
                </div>
                <div class="column-card">
                    <div class="column-name">product_name</div>
                    <p>Full description of the product</p>
                    <div class="column-example">Example: ORION Margot s hrozienkami 80 g</div>
                </div>
                <div class="column-card">
                    <div class="column-name">Category name</div>
                    <p>Product category classification</p>
                    <div class="column-example">Example: SWEETS (excl. Biscuits)</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>How to Interpret This Data</h2>
            
            <div class="visual-diagram">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 450">
                    <!-- Data Timeline -->
                    <rect x="100" y="50" width="600" height="100" fill="#f1f8ff" rx="5" ry="5" stroke="#3498db" stroke-width="2"/>
                    <text x="400" y="35" text-anchor="middle" font-weight="bold" font-size="16">14 Weeks of Raw Daily Data</text>
                    <text x="400" y="100" text-anchor="middle" font-size="16">May 27, 2024 - September 1, 2024</text>
                    
                    <!-- Days markers -->
                    <line x1="130" y1="50" x2="130" y2="150" stroke="#ddd" stroke-width="1" stroke-dasharray="5,5"/>
                    <line x1="215" y1="50" x2="215" y2="150" stroke="#ddd" stroke-width="1" stroke-dasharray="5,5"/>
                    <line x1="300" y1="50" x2="300" y2="150" stroke="#ddd" stroke-width="1" stroke-dasharray="5,5"/>
                    <line x1="385" y1="50" x2="385" y2="150" stroke="#ddd" stroke-width="1" stroke-dasharray="5,5"/>
                    <line x1="470" y1="50" x2="470" y2="150" stroke="#ddd" stroke-width="1" stroke-dasharray="5,5"/>
                    <line x1="555" y1="50" x2="555" y2="150" stroke="#ddd" stroke-width="1" stroke-dasharray="5,5"/>
                    <line x1="640" y1="50" x2="640" y2="150" stroke="#ddd" stroke-width="1" stroke-dasharray="5,5"/>
                    
                    <!-- Day labels -->
                    <text x="130" y="165" text-anchor="middle" font-size="14">M</text>
                    <text x="215" y="165" text-anchor="middle" font-size="14">T</text>
                    <text x="300" y="165" text-anchor="middle" font-size="14">W</text>
                    <text x="385" y="165" text-anchor="middle" font-size="14">T</text>
                    <text x="470" y="165" text-anchor="middle" font-size="14">F</text>
                    <text x="555" y="165" text-anchor="middle" font-size="14">S</text>
                    <text x="640" y="165" text-anchor="middle" font-size="14">S</text>
                    
                    <!-- Transformation arrow -->
                    <polygon points="400,180 380,210 420,210" fill="#2c3e50"/>
                    <text x="500" y="200" font-size="14" font-style="italic">Data is averaged by day of week</text>
                    
                    <!-- Model data -->
                    <rect x="100" y="230" width="600" height="120" fill="#e8f5e9" rx="5" ry="5" stroke="#4caf50" stroke-width="2"/>
                    <text x="400" y="255" text-anchor="middle" font-weight="bold" font-size="16">"Model Dataset": ONE AVERAGE WEEK</text>
                    
                    <!-- Weekday boxes -->
                    <rect x="115" y="270" width="70" height="60" fill="#fff" rx="3" ry="3" stroke="#ddd"/>
                    <text x="150" y="295" text-anchor="middle" font-size="12">Monday</text>
                    <text x="150" y="315" text-anchor="middle" font-size="10">Avg Values</text>
                    
                    <rect x="195" y="270" width="70" height="60" fill="#fff" rx="3" ry="3" stroke="#ddd"/>
                    <text x="230" y="295" text-anchor="middle" font-size="12">Tuesday</text>
                    <text x="230" y="315" text-anchor="middle" font-size="10">Avg Values</text>
                    
                    <rect x="275" y="270" width="70" height="60" fill="#fff" rx="3" ry="3" stroke="#ddd"/>
                    <text x="310" y="295" text-anchor="middle" font-size="12">Wednesday</text>
                    <text x="310" y="315" text-anchor="middle" font-size="10">Avg Values</text>
                    
                    <rect x="355" y="270" width="70" height="60" fill="#fff" rx="3" ry="3" stroke="#ddd"/>
                    <text x="390" y="295" text-anchor="middle" font-size="12">Thursday</text>
                    <text x="390" y="315" text-anchor="middle" font-size="10">Avg Values</text>
                    
                    <rect x="435" y="270" width="70" height="60" fill="#fff" rx="3" ry="3" stroke="#ddd"/>
                    <text x="470" y="295" text-anchor="middle" font-size="12">Friday</text>
                    <text x="470" y="315" text-anchor="middle" font-size="10">Avg Values</text>
                    
                    <rect x="515" y="270" width="70" height="60" fill="#fff" rx="3" ry="3" stroke="#ddd"/>
                    <text x="550" y="295" text-anchor="middle" font-size="12">Saturday</text>
                    <text x="550" y="315" text-anchor="middle" font-size="10">Avg Values</text>
                    
                    <rect x="595" y="270" width="70" height="60" fill="#fff" rx="3" ry="3" stroke="#2980b9" stroke-width="2"/>
                    <text x="630" y="295" text-anchor="middle" font-size="12" font-weight="bold">Sunday</text>
                    <text x="630" y="315" text-anchor="middle" font-size="10">Avg Values</text>
                    <text x="630" y="328" text-anchor="middle" font-size="9" fill="#e74c3c">1.9 units sold</text>
                    
                    <!-- Explanation -->
                    <text x="400" y="370" text-anchor="middle" font-size="14">Each row represents a store-product-day combination with average values</text>
                    <text x="400" y="395" text-anchor="middle" font-size="14">Fractional values (like 1.9 units) represent weekly averages</text>
                    
                    <!-- Example interpretation -->
                    <rect x="100" y="410" width="600" height="30" fill="#fff8e1" rx="5" ry="5" stroke="#ffc107" stroke-width="2"/>
                    <text x="400" y="430" text-anchor="middle" font-size="12" font-style="italic">Example: 1.9 units on Sunday means this product sells about 2 units every Sunday on average</text>
                </svg>
            </div>
            
            <p>The key to understanding this dataset:</p>
            <ul>
                <li>Each row represents a <strong>single product</strong> in a <strong>specific store</strong> on a <strong>specific day of the week</strong></li>
                <li>The values (like sold_units = 1.9) represent the <strong>average performance</strong> for that day across the 14-week period</li>
                <li>Fractional values are normal and expected since they represent averages</li>
                <li>For example: 1.9 units on Sunday means this product sells on average 1.9 units each Sunday</li>
            </ul>
            
            <div class="warning">
                <strong>Important:</strong> This dataset includes only products that were sold during the May 27 - Sept 1, 2024 period. Products that sold before or after this period are not included.
            </div>
        </div>


        <div class="card">
            <h2>How "Model Dataset" is Created and Used in Modelling Process</h2>
            <!-- <p>This "Model Dataset" is used for:</p> -->
            
            <div class="data-flow-diagram">
                <svg width="100%" height="640" viewBox="0 0 1100 640">
                    <!-- Main Flow Background -->
                    <rect x="50" y="20" width="1000" height="600" fill="#f8f9fa" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- Step 1: Data Collection & Transformation -->
                    <rect x="100" y="50" width="900" height="120" fill="white" rx="8" ry="8" stroke="#3498db" stroke-width="2"/>
                    <text x="120" y="80" font-size="18" font-weight="bold" fill="#2c3e50">Step 1: Data Collection & Transformation</text>
                    
                    <!-- Data Sources Container -->
                    <rect x="120" y="100" width="300" height="50" fill="#e1f5fe" rx="5" ry="5" stroke="#81d4fa" stroke-width="1"/>
                    <text x="270" y="130" text-anchor="middle" font-size="14">Hadoop + Other Sources</text>
                    
                    <!-- ETL Process -->
                    <rect x="480" y="100" width="160" height="50" fill="#e8f5e9" rx="5" ry="5" stroke="#a5d6a7" stroke-width="1"/>
                    <text x="560" y="130" text-anchor="middle" font-size="14">ETL Transformation</text>
                    
                    <!-- Dataset Creation -->
                    <rect x="780" y="100" width="160" height="50" fill="#ede7f6" rx="5" ry="5" stroke="#b39ddb" stroke-width="1"/>
                    <text x="860" y="130" text-anchor="middle" font-size="14">Weekly Model Dataset</text>
                    
                    <!-- Arrows -->
                    <line x1="420" y1="125" x2="480" y2="125" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="640" y1="125" x2="780" y2="125" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Step 2: Driver Creation -->
                    <rect x="100" y="200" width="900" height="120" fill="white" rx="8" ry="8" stroke="#3498db" stroke-width="2"/>
                    <text x="120" y="230" font-size="18" font-weight="bold" fill="#2c3e50">Step 2: Driver Creation</text>
                    
                    <!-- Model Dataset -->
                    <rect x="120" y="250" width="200" height="50" fill="#ede7f6" rx="5" ry="5" stroke="#b39ddb" stroke-width="1"/>
                    <text x="220" y="280" text-anchor="middle" font-size="14">Weekly Model Dataset</text>
                    
                    <!-- Python Engine -->
                    <rect x="380" y="250" width="200" height="50" fill="#fff3e0" rx="5" ry="5" stroke="#ffe0b2" stroke-width="1"/>
                    <text x="480" y="275" text-anchor="middle" font-size="14">Python Data Engine</text>
                    <text x="480" y="290" text-anchor="middle" font-size="12">Aggregation & Calculation</text>
                    
                    <!-- Driver Output -->
                    <rect x="700" y="250" width="200" height="50" fill="#e0f7fa" rx="5" ry="5" stroke="#80deea" stroke-width="1"/>
                    <text x="800" y="275" text-anchor="middle" font-size="14">Store Specific Drivers</text>
                    <text x="800" y="290" text-anchor="middle" font-size="12">e.g., Cases Delivered per Store</text>
                    
                    <!-- Arrows -->
                    <line x1="320" y1="275" x2="380" y2="275" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="580" y1="275" x2="700" y2="275" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Step 3: Activity Creation -->
                    <rect x="100" y="350" width="900" height="120" fill="white" rx="8" ry="8" stroke="#3498db" stroke-width="2"/>
                    <text x="120" y="380" font-size="18" font-weight="bold" fill="#2c3e50">Step 3: Activities + Drivers</text>
                    
                    <!-- Drivers Input -->
                    <rect x="120" y="400" width="180" height="50" fill="#e0f7fa" rx="5" ry="5" stroke="#80deea" stroke-width="1"/>
                    <text x="210" y="425" text-anchor="middle" font-size="14">Store Drivers</text>
                    <text x="210" y="440" text-anchor="middle" font-size="12">e.g., Stock Volumes</text>
                    
                    <!-- Activity Standards -->
                    <rect x="380" y="400" width="180" height="50" fill="#f3e5f5" rx="5" ry="5" stroke="#ce93d8" stroke-width="1"/>
                    <text x="470" y="425" text-anchor="middle" font-size="14">Activity Standards</text>
                    <text x="470" y="440" text-anchor="middle" font-size="12">e.g. "Tray fill", 25 sec/case</text>
                    
                    <!-- Activity Calculation -->
                    <rect x="640" y="400" width="260" height="50" fill="#fff8e1" rx="5" ry="5" stroke="#ffe082" stroke-width="1"/>
                    <text x="770" y="425" text-anchor="middle" font-size="14">Store Activity Calculations</text>
                    <text x="770" y="440" text-anchor="middle" font-size="12">Driver × Frequency × Activity Standard</text>
                    
                    <!-- Arrows -->
                    <line x1="300" y1="425" x2="380" y2="425" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="560" y1="425" x2="640" y2="425" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Step 4: Hours Calculation -->
                    <rect x="100" y="500" width="900" height="100" fill="white" rx="8" ry="8" stroke="#3498db" stroke-width="2"/>
                    <text x="120" y="530" font-size="18" font-weight="bold" fill="#2c3e50">Step 4: Hours Calculation</text>
                    
                    <!-- Activity Times -->
                    <rect x="120" y="550" width="200" height="40" fill="#fff8e1" rx="5" ry="5" stroke="#ffe082" stroke-width="1"/>
                    <text x="220" y="575" text-anchor="middle" font-size="14">Activity Time Values</text>
                    
                    <!-- Conversion Process -->
                    <rect x="400" y="550" width="200" height="40" fill="#e8f5e9" rx="5" ry="5" stroke="#a5d6a7" stroke-width="1"/>
                    <text x="500" y="575" text-anchor="middle" font-size="14">Seconds → Hours Conversion</text>
                    
                    <!-- Final Output -->
                    <rect x="680" y="550" width="220" height="40" fill="#ffebee" rx="5" ry="5" stroke="#ffcdd2" stroke-width="1"/>
                    <text x="790" y="575" text-anchor="middle" font-size="14">Store Activity Hours</text>
                    
                    <!-- Arrows -->
                    <line x1="320" y1="570" x2="400" y2="570" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="600" y1="570" x2="680" y2="570" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Arrow definitions -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
                        </marker>
                    </defs>
                    
                    <!-- Vertical flow arrows -->
                    <line x1="550" y1="170" x2="550" y2="200" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
                    <line x1="550" y1="320" x2="550" y2="350" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
                    <line x1="550" y1="470" x2="550" y2="500" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
                </svg>


                <ul>
                    <li><strong>Modeling labour demand for CE stores</strong> - Serving as the foundation for the multi-step process shown in the workflow diagram</li>
                    <li><strong>Driver creation</strong> - Generating store-specific metrics like cases per store using the Python Data Engine</li>
                    <li><strong>Activity standards calculation</strong> - Combining with task definitions (e.g., "Tray fill", 25 sec/case) to establish workload benchmarks</li>
                    <li><strong>Store activity calculations</strong> - Multiplying drivers by frequency and activity standards to determine workload</li>
                    <li><strong>Hours calculation</strong> - Converting activity values to actual labour hours needed per store</li>
                </ul>
            </div>
            
            <div class="key-insight">
                <strong>Remember:</strong> This dataset is the critical first input to the labour modeling process shown in the diagram, enabling accurate workforce planning across all stores.
            </div>
        </div>

</body>
</html>