
import pandas as pd
import pyodbc
import numpy as np
from datetime import datetime



pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)


start = '20230715'
end = '20240715'

# Define the start and end dates
start_date = datetime.strptime(start, '%Y%m%d')
end_date = datetime.strptime(end, '%Y%m%d')

# Calculate the difference between the two dates
difference = end_date - start_date

# Convert the difference into weeks
weeks = difference.days / 7



with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:


    sql = """ SELECT mstr.cntr_code AS country,
        /*cast(stores.dmst_store_code as INT) AS store,   */     
    slad_tpnb AS tpnb, slad_tpn AS tpn,
        dmat_div_des_en AS DIV_DESC,
        dmat_div_code as DIV_ID,
        dmat_dep_des_en AS DEP_DESC,
        dmat_dep_code as DEP_ID,
        dmat_sec_des_en AS SEC_DESC,
        dmat_sec_code as SEC_ID,
        dmat_grp_des_en AS GRP_DESC,
        dmat_grp_code as GRP_ID,
        dmat_sgr_des_en AS SGR_DESC,
        dmat_sgr_code as SGR_ID,
        slad_long_des as product_name,
        slad_unit AS unit_type,
        slad_case_size AS case_capacity,
        slad_net_weight AS weight,
        SUM(sunit.slsms_unit) /*/{weeks}*/  AS sold_units
        
                FROM DM.dim_artgld_details mstr
                JOIN dw.sl_sms sunit  ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id
                JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id
                JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
                AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
    
        WHERE
        /*CAST(dmat_div_code as INT) = 30
        AND CAST(dmat_dep_code as INT) = 200
        AND CAST(dmat_sec_code as INT) = 632
        AND CAST(dmat_grp_code as INT) = 560
        AND CAST(dmat_sgr_code as INT) = 500
        AND*/
        mstr.cntr_code IN ('HU', 'SK', 'CZ')
        AND sunit.part_col between {start} and {end}
        AND sunit.slsms_unit > 0 
    
        GROUP BY mstr.cntr_code,
        /*stores.dmst_store_code,*/
        slad_tpnb,slad_tpn,
        dmat_div_des_en,
        dmat_div_code,
        dmat_dep_des_en,
        dmat_dep_code,
        dmat_sec_des_en,
        dmat_sec_code,
        dmat_grp_des_en,
        dmat_grp_code,
        dmat_sgr_des_en,
        dmat_sgr_code,
        slad_long_des,
        slad_unit,
        slad_case_size,
        slad_net_weight
        
        """.format(weeks = weeks, start = int(start), end = int(end) )
        
    b = pd.read_sql(sql, conn)
    
    
# PBL part    
# a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\PBS_PBL\dcr_reports\pbs_pbl_dataset.parquet")
# a = a[~a['Deliv Type'].str.contains("PBS")]
# a['country'] = a["Store"].apply(lambda x: 'SK' if str(x).startswith("2") else ("CZ" if str(x).startswith("1") else ("HU" if str(x).startswith("4") else None)))
# a['delivery_type'] = 'PBL'
# a.columns = [x.lower() for x in a.columns]
# a = a[['country','tpnb', 'delivery_type']].drop_duplicates()

# b = b.merge(a, on=['country', 'tpnb'], how="left")
# b['delivery_type'] = b['delivery_type'].replace(np.nan, "not found in my PBL database")


