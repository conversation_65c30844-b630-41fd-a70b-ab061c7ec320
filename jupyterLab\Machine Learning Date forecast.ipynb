{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c840be6b-abea-49ee-bf9b-5a02d13fe289", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "import xgboost as xgb\n", "from sklearn.metrics import mean_squared_error\n", "plt.style.use('fivethirtyeight')"]}, {"cell_type": "code", "execution_count": 11, "id": "01a1c6dd-e1ea-4170-a490-9746dc88147d", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2022\\others\\time series forecasting_ML\\AEP_hourly.csv.zip\")\n", "df = df.set_index('Datetime')\n", "df.index = pd.to_datetime(df.index)"]}, {"cell_type": "code", "execution_count": 12, "id": "a58f233b-414e-48e1-9b7a-ab9ed47f834b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>AEP_MW</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Datetime</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2004-12-31 01:00:00</th>\n", "      <td>13478.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2004-12-31 02:00:00</th>\n", "      <td>12865.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2004-12-31 03:00:00</th>\n", "      <td>12577.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2004-12-31 04:00:00</th>\n", "      <td>12517.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2004-12-31 05:00:00</th>\n", "      <td>12670.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-01 20:00:00</th>\n", "      <td>21089.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-01 21:00:00</th>\n", "      <td>20999.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-01 22:00:00</th>\n", "      <td>20820.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-01 23:00:00</th>\n", "      <td>20415.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-01-02 00:00:00</th>\n", "      <td>19993.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>121273 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                      AEP_MW\n", "Datetime                    \n", "2004-12-31 01:00:00  13478.0\n", "2004-12-31 02:00:00  12865.0\n", "2004-12-31 03:00:00  12577.0\n", "2004-12-31 04:00:00  12517.0\n", "2004-12-31 05:00:00  12670.0\n", "...                      ...\n", "2018-01-01 20:00:00  21089.0\n", "2018-01-01 21:00:00  20999.0\n", "2018-01-01 22:00:00  20820.0\n", "2018-01-01 23:00:00  20415.0\n", "2018-01-02 00:00:00  19993.0\n", "\n", "[121273 rows x 1 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}