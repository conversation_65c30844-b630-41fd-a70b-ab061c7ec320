# -*- coding: utf-8 -*-
"""
Created on Wed Feb 14 13:33:35 2024

@author: phrubos
"""
import paramiko
import pandas as pd
from io import StringIO
import numpy as np
import os
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from openpyxl.formatting.rule import ColorScaleRule
from openpyxl.utils import get_column_letter
from datetime import datetime, timedelta


def generate_date_range(start_date, end_date):
    date_format = "%Y%m%d"  # Adjust format to match your input
    start = datetime.strptime(start_date, date_format)
    end = datetime.strptime(end_date, date_format)

    date_range = []
    current_date = start
    while current_date <= end:
        date_range.append(current_date.strftime("%Y-%m-%d"))  # Adjust output format as desired
        current_date += timedelta(days=1)

    return date_range


def generate_dictionary(start_date, end_date):
    date_range = generate_date_range(start_date, end_date)
    dictionary = {date: [100] for date in date_range}
    return dictionary


def get_unique_filename(directory, filename):
    base_name, extension = os.path.splitext(filename)
    version = 1
    new_filename = filename

    while os.path.exists(os.path.join(directory, new_filename)):
        version += 1
        new_filename = f"{base_name}_v{version}{extension}"

    return new_filename

def quarterHour_report(start, end, stores, customerModifier):
    
    
    # start = 20230202
    # end = 20230202
    # stores = ['41520']

    # customerModifier = {'Hétfő': [100], 'Kedd': [100], 'Szerda': [100], 'Csütörtök': [100], 'Péntek': [98], 'Szombat': [100], 'Vasárnap': [100]}

    if customerModifier:
        # Create DataFrame
        customerModifier_df = pd.DataFrame.from_dict(customerModifier, orient='index', columns=['Cust_Mod_%'])
        customerModifier_df.index.name = 'day'
        customerModifier_df.reset_index(inplace=True)


    if not customerModifier:
        customerModifier = generate_dictionary(str(start), str(end))
        customerModifier_df = pd.DataFrame.from_dict(customerModifier, orient='index', columns=['Cust_Mod_%'])
        customerModifier_df.index.name = 'day'
        customerModifier_df.reset_index(inplace=True)


    
    def convert_to_numeric(df):
        """
        Converts columns in a DataFrame to integers or floats if possible.
        
        Args:
        df (pandas.DataFrame): Input DataFrame.
        
        Returns:
        pandas.DataFrame: DataFrame with columns converted to integers or floats where possible.
        """
        for col in df.columns:
            if pd.api.types.is_numeric_dtype(df[col]):
                # If the column is already numeric, continue to the next column
                continue
            try:
                # Attempt to convert the column to numeric (integer or float)
                df[col] = pd.to_numeric(df[col])
            except ValueError:
                # If conversion fails, continue to the next column
                continue
        return df
      


    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh_client.connect(hostname='hdp0430.global.tesco.org', username='phrubos', password='BleachersTinymoves')
    
    

    s = list()
    for x in stores:

        s.append(str(x))


        stores_string = ",".join(s)
    
    sql = """
    
                SELECT
                
                h.store as store,
                h.day,
                h.weekdays_id,
                h.period,
                h.week,
                h.hour,
                cast(REPLACE(t.time_per_cust_min, ',', '.') as FLOAT ) as time_per_cust_min,
                h.customers,
                m.MBC_Tills
                
                from
                
                
                (Select
                CAST(a.site AS INT) AS store,
                CONCAT(YEAR(a.day), '-', LPAD(MONTH(a.day), 2, '0'), '-', LPAD(DAY(a.day), 2, '0')) AS day,
                cast(cal.dtdw_id as INT) as weekdays_id,
                cast(RIGHT(cal.dmtm_fp_code, 2) as INT) as period,
                RIGHT(cal.dmtm_fw_code, 3) as week,
                b.group_name AS group_name,
                HOUR(a.day) AS hour,
                COUNT(DISTINCT(CONCAT(a.eod, a.site, a.tin, a.pon))) AS customers
                
                FROM poshu.t001 a
                Left JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
                LEFT JOIN tesco_analysts.till_grouping_lzs b
                ON a.site = b.site
                AND a.pon = b.till
                
                
                WHERE a.part_col BETWEEN {start} AND {end}
                
                AND a.site in ({stores_string})
                
                AND b.group_name = 'Main_Bank'
                
                GROUP BY a.site, CONCAT(YEAR(a.day), '-', LPAD(MONTH(a.day), 2, '0'), '-', LPAD(DAY(a.day), 2, '0')),
                          HOUR(a.day), b.group_name, cal.dtdw_id, RIGHT(cal.dmtm_fp_code, 2), RIGHT(cal.dmtm_fw_code, 3)) h
                          
                LEFT JOIN tesco_analysts.Timecustomer t ON h.store = t.store
                LEFT JOIN tesco_analysts.mbc_till_nr m ON h.store = m.store 
                
                WHERE h.hour between 6 and 21
                
                
                ORDER BY h.store, h.day, h.hour
    
    """.format(start=start, end=end, stores_string=stores_string)
    
    
    # print(sql)
    
    
    
    # Beeline command with the SQL query
    beeline_command = f'/opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file ' \
                      f'-u "******************************************************;' \
                      f'transportMode=https?kyuubi.engine.share.level.subdomain=cep_user_dc2sweekly;' \
                      f'spark.hadoop.fs.s3a.assumed.role.arn=arn:aws:iam:::role/role-cep-resource-sch;' \
                      f'spark.yarn.queue=kyuubi" -e " {sql}"'
    
    
    
    # Execute the command and capture output
    _, stdout, _ = ssh_client.exec_command(beeline_command)
    # output = stdout.read().decode('utf-8')
    
    raw_data = str(stdout.read())
    data = "\n".join(raw_data.split("\\n")) 
    
    
    df = pd.read_table(StringIO(data), sep='|', skiprows=[0, 2], skipfooter=2, engine='python')
    
    df = df.loc[:, ~df.columns.str.contains('Unnamed')]
    
    df.columns = [col.strip() for col in df.columns]
    
    for x in df.columns:
        try:
            df[x] = df[x].str.strip()
        except:
            pass
    
    df = df.drop_duplicates()
    df = df[pd.to_numeric(df['store'], errors='coerce').notna()]
    
    df = convert_to_numeric(df)
    
    # Dictionary mapping weekday IDs to names
    weekday_names = {
        1: 'Hétfő',
        2: 'Kedd',
        3: 'Szerda',
        4: 'Csütörtök',
        5: 'Péntek',
        6: 'Szombat',
        7: 'Vasárnap'
    }

    # Create a new column 'weekday_name' by mapping weekday IDs to names
    df['weekday_name'] = df['weekdays_id'].map(weekday_names)
    
    df = df.merge(customerModifier_df, on='day', how='left')
    
    df['customers'] =df['customers'] * (df['Cust_Mod_%']/100)
    
    df['cust_timeval_hour'] = (df['customers'] * df['time_per_cust_min'])/60
    
    df['cust_timeval_hour'] = np.where(df['cust_timeval_hour'] > df['MBC_Tills'], df['MBC_Tills'], df['cust_timeval_hour'])
    
    df['customer_nr'] = df.groupby(['store', 'day'])['customers'].transform("sum")
    df['customer_nr'] = round(df['customer_nr'])
    
    
    #Pivoting
    df = df.pivot_table(index=['store', 'day', 'weekdays_id', 'weekday_name','period', 'week', 'MBC_Tills', 'customer_nr','Cust_Mod_%'],
                        columns='hour', values="cust_timeval_hour",
                        fill_value=0).reset_index()\
                        .sort_values(by=['store','day','weekdays_id'], ascending=[True, True, True])
                        
    df.drop("weekdays_id", axis=1, inplace=True) 

    rename_dict = {
        'hour':'óra',
        'store': 'Áruház',
        'day': 'Dátum',
        'weekday_name': 'Nap',
        'period': 'Periódus',
        'week': 'Hét',
        'MBC_Tills': 'Kasszaszám',
        'customer_nr': 'Vásárlószám',
        'Cust_Mod_%': 'Vásárlószám_módosító_%'
    }

    df.rename(columns=rename_dict, inplace=True)
    
    # Close SSH connection
    ssh_client.close()
    
    return df


def formatting_df(df, filename ):
    
    # Define the range of columns to apply conditional formatting (6-22)
    start_col = 8
    end_col = 25
    
    def apply_conditional_formatting(workbook, worksheet):

        
        # Iterate over each row in the DataFrame
        for idx, row in df.iterrows():
            row_number = idx + 1  # Excel row index
            

            
            # Add conditional formatting for the row
            worksheet.conditional_format(row_number, start_col , row_number, end_col + 1,
                                         {'type': '3_color_scale',
                                          'min_color': '#C6EFCE',  # Green
                                          'mid_color': '#f5f502',  # Yellow
                                          'max_color': '#f50221'})  # Red
    
    
    # Write DataFrame to Excel with formatting
    with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='Munkaerő igény', index=False)
        workbook = writer.book
        worksheet = writer.sheets['Munkaerő igény']

        
        # Center align and format numbers with one decimal place
    
        formating_numbers = workbook.add_format(
        {
            'num_format': '0.0',
            "align": "center_across",
            "valign": "vcenter",
            'bold': True,
        }
        )
    
    
        formating_A_E = workbook.add_format(
        {
            "align": "center_across",
            "valign": "vcenter",
        }
        )
    
        max_column_size = len(df.columns) - 1
        

        
        worksheet.set_column(start_col, max_column_size, None, formating_numbers)
        worksheet.set_column('A:H', None, formating_A_E)
        
        worksheet.set_tab_color("green")
        worksheet.set_zoom(90)
        apply_conditional_formatting(workbook, worksheet)
        
def adjust_column_widths(filename):
    # Load the existing workbook
    workbook = load_workbook(filename)
    worksheet = workbook.active
    
    # Example: Adjusting the width of the first 8 columns
    for col_idx in range(1, 9):
        max_length = 0
        column = get_column_letter(col_idx)
        
        # Iterate through each cell in the column to find the maximum length
        for cell in worksheet[column]:
            try:
                cell_length = len(str(cell.value))
                if cell_length > max_length:
                    max_length = cell_length
            except TypeError:
                pass  # Ignore None values
        
        # Set the column width based on the maximum length found
        worksheet.column_dimensions[column].width = max_length + 2  # Adding some padding
        
        
    # Adjusting the width of columns 9 to 25 to make them smaller
    for col_idx in range(9, 26):
        column = get_column_letter(col_idx)
        worksheet.column_dimensions[column].width = 5  # Set a fixed width (you can adjust this value as needed)


    # Save the workbook with adjusted column widths
    workbook.save(filename)
    
     
        


def create_a_folder(directory, folder_name):
    
    
    
    while True:
        # Check if a folder with the current name already exists
        if not os.path.exists(directory/folder_name):
            # If it doesn't exist, create the folder and break out of the loop
            os.mkdir(directory/folder_name)
            break
        else:
            break
        
        
        

def all_stores_separated_conditional_formatting(directory, folder_name ):
    


    # Define colors as aRGB hex values
    colors = ['FFC6EFCE', 'FFF5F502', 'FFF50221']  # '#C6EFCE', '#f5f502', '#f50221'
    
    # Convert aRGB hex values to PatternFill objects
    fills = [PatternFill(start_color=color, end_color=color, fill_type='solid') for color in colors]
    
    # Define conditional formatting rule
    color_scale_rule = ColorScaleRule(start_type='num', start_value=0,
                                      start_color=fills[0].fgColor.rgb,
                                      mid_type='percentile', mid_value=50,
                                      mid_color=fills[1].fgColor.rgb,
                                      end_type='percentile', end_value=100,
                                      end_color=fills[2].fgColor.rgb)
    
    # Function to apply conditional formatting to a worksheet
    def apply_conditional_formatting(ws):
        ws.conditional_formatting.add('I2:X1048576', color_scale_rule)  # Assuming the maximum possible row count is 1048576
    
    # Directory containing the Excel files
    folder_path = str(directory) + "\\" + folder_name
    
    # Iterate over each file in the folder
    for filename in os.listdir(folder_path):
        if filename.endswith('.xlsx'):
            file_path = os.path.join(folder_path, filename)
            workbook = load_workbook(file_path)
            sheet = workbook.active  # Assuming the conditional formatting is to be applied to the first sheet
            apply_conditional_formatting(sheet)
            adjust_column_widths(file_path)
            workbook.save(file_path)



stores = [41001,
 41002,
 41003,
 41004,
 41005,
 41006,
 41007,
 41008,
 41009,
 41010,
 41011,
 41012,
 41013,
 41014,
 41015,
 41016,
 41017,
 41018,
 41019,
 41020,
 41021,
 41022,
 41024,
 41025,
 41026,
 41027,
 41028,
 41029,
 41030,
 41031,
 41033,
 41034,
 41036,
 41037,
 41038,
 41039,
 41040,
 41041,
 41042,
 41043,
 41044,
 41045,
 41046,
 41047,
 41049,
 41051,
 41052,
 41053,
 41058,
 41059,
 41060,
 41390,
 41400,
 41410,
 41420,
 41430,
 41440,
 41450,
 41460,
 41470,
 41480,
 41490,
 41500,
 41510,
 41520,
 41530,
 41540,
 41550,
 41560,
 41570,
 41580,
 41590,
 41600,
 41610,
 41620,
 41630,
 41640,
 41650,
 41660,
 41670,
 41680,
 41690,
 41700,
 41710,
 41720,
 41730,
 41740,
 41750,
 41760,
 41770,
 41780,
 41790,
 41800,
 41810,
 41820,
 41830,
 41840,
 41850,
 41860,
 41870,
 41880,
 41890,
 41900,
 41910,
 41920,
 41930,
 41940,
 41950,
 41960,
 41970,
 41980,
 41990]





