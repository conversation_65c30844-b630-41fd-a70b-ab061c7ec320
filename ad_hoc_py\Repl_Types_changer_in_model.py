import pandas as pd

import numpy as np

def optimize_types(dataframe):
    np_types = [
        np.int8,
        np.int16,
        np.int32,
        np.int64,
        np.uint8,
        np.uint16,
        np.uint32,
        np.uint64,
        np.float32,
        np.float64,
    ]  # , np.float16, np.float32, np.float64
    np_types = [np_type.__name__ for np_type in np_types]
    type_df = pd.DataFrame(data=np_types, columns=["class_type"])

    type_df["min_value"] = type_df[type_df["class_type"].str.contains("int")][
        "class_type"
    ].apply(lambda row: np.iinfo(row).min)
    type_df["max_value"] = type_df[type_df["class_type"].str.contains("int")][
        "class_type"
    ].apply(lambda row: np.iinfo(row).max)
    type_df["min_value_f"] = type_df[type_df["class_type"].str.contains("float")][
        "class_type"
    ].apply(lambda row: np.finfo(row).min)
    type_df["max_value_f"] = type_df[type_df["class_type"].str.contains("float")][
        "class_type"
    ].apply(lambda row: np.finfo(row).max)
    type_df["min_value"] = np.where(
        type_df["min_value"].isna(), type_df["min_value_f"], type_df["min_value"]
    )
    type_df["max_value"] = np.where(
        type_df["max_value"].isna(), type_df["max_value_f"], type_df["max_value"]
    )
    type_df.drop(columns=["min_value_f", "max_value_f"], inplace=True)

    type_df["range"] = type_df["max_value"] - type_df["min_value"]
    type_df.sort_values(by="range", inplace=True)
    try:
        for col in dataframe.loc[:, dataframe.dtypes == np.integer]:
            col_min = dataframe[col].min()
            col_max = dataframe[col].max()
            temp = type_df[
                (type_df["min_value"] <= col_min) & (type_df["max_value"] >= col_max)
            ]
            optimized_class = temp.loc[temp["range"].idxmin(), "class_type"]
            # print("Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}".format(col, col_min, col_max, optimized_class))
            dataframe[col] = dataframe[col].astype(optimized_class)
    except ValueError:
        pass
    try:
        for col in dataframe.loc[:, (dataframe.dtypes == np.floating)]:
            col_min = dataframe[col].min()
            col_max = dataframe[col].max()
            type_df = type_df[
                type_df["class_type"].astype("string").str.contains("float")
            ]
            temp = type_df[
                (type_df["min_value"] <= col_min) & (type_df["max_value"] >= col_max)
            ]
            optimized_class = temp.loc[temp["range"].idxmin(), "class_type"]
            # print("Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}".format(col, col_min, col_max, optimized_class))
            dataframe[col] = dataframe[col].astype(optimized_class)
    except ValueError:
        pass
    return dataframe


def optimize_floats(df: pd.DataFrame) -> pd.DataFrame:
    floats = df.select_dtypes(include=["float64"]).columns.tolist()
    df[floats] = df[floats].apply(pd.to_numeric, downcast="float")
    return df


def optimize_ints(df: pd.DataFrame) -> pd.DataFrame:
    ints = df.select_dtypes(include=["int64"]).columns.tolist()
    df[ints] = df[ints].apply(pd.to_numeric, downcast="integer")
    return df


def optimize_objects(df: pd.DataFrame):
    try:
        for col in df.select_dtypes(include=["object"]):
            if not (type(df[col][0]) == list):
                num_unique_values = len(df[col].unique())
                num_total_values = len(df[col])
                if float(num_unique_values) / num_total_values < 0.5:
                    df[col] = df[col].astype("category")
    except IndexError:
        pass
    return df


def optimize(df: pd.DataFrame):
    return optimize_floats(optimize_ints(optimize_objects(df)))


def fast_compare_and_update_df(df_a, df_b):
    """Fast comparison and update of large dataframes maintaining original shape"""
    
    
    
    value_cols = ['srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', 'icream_nsrp', 'shelfCapacity']
    
    # Find matches where nsrp > 0 in df_a and other columns > 0 in df_b
    conditions = (df_a['nsrp'] > 0) 
    keys_a = df_a.loc[conditions, ['store', 'tpnb']].drop_duplicates()
    
    matching_rows = df_b.merge(keys_a, on=['store', 'tpnb'])
    matching_rows = matching_rows[
        (matching_rows[value_cols].drop('nsrp', axis=1) > 0).any(axis=1)
    ].drop_duplicates(['store', 'tpnb'])
    
    # Create a mapping DataFrame with unique store-tpnb combinations
    updates = matching_rows[['store', 'tpnb'] + value_cols]
    
    # Update values using merge with unique combinations
    df_a = df_a.merge(updates, on=['store', 'tpnb'], how='left', suffixes=('', '_y'))
    
    # Fill in the new values where they exist
    mask = ~df_a[value_cols[0] + '_y'].isna()
    df_a.loc[mask, value_cols] = df_a.loc[mask, [c + '_y' for c in value_cols]].values
    
    # Drop temporary columns
    df_a = df_a.drop([c + '_y' for c in value_cols], axis=1)
    
    return df_a


a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\2025w14-27_v01\Repl_Dataset_2025w14-27_v01")


b = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\JDA_SRD_Tables\11-08-2025_full\CE_JDA_SRD_for_model_%_way")
c = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\JDA_SRD_Tables\14-07-2025\CE_JDA_SRD_for_model_%_way")
d = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\JDA_SRD_Tables\18-06-2025\\CE_JDA_SRD_for_model_%_way")


a = fast_compare_and_update_df(a, d)


a = optimize_objects(optimize_types(a))



def print_row_percentages(df):
    columns = ['srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', 'icream_nsrp']
    
    # Group by country and sum
    grouped = df.groupby('country', observed=True)[columns].sum()
    
    # Calculate percentages row-wise
    percentages = grouped.div(grouped.sum(axis=1), axis=0) * 100
    
    # Round to 2 decimal places
    print(percentages.round(2))



print_row_percentages(a)


    
    
    
# #base    
#          srp  nsrp  mu  full_pallet  split_pallet  icream_nsrp
# country                                                       
# CZ      41.7  57.3 0.0          0.3           0.5          0.2
# HU      31.5  67.6 0.0          0.2           0.6          0.1
# SK      44.0  55.1 0.0          0.2           0.5          0.2


# #with august
#          srp  nsrp  mu  full_pallet  split_pallet  icream_nsrp
# country                                                       
# CZ      42.3  56.7 0.0          0.3           0.5          0.2
# HU      31.6  67.4 0.0          0.2           0.6          0.2
# SK      44.3  54.8 0.0          0.2           0.5          0.2


# #with july
#          srp  nsrp  mu  full_pallet  split_pallet  icream_nsrp
# country                                                       
# CZ      43.0  55.6 0.0          0.3           0.8          0.2
# HU      32.4  66.7 0.0          0.2           0.6          0.2
# SK      45.2  53.5 0.0          0.3           0.9          0.2

# #with june
#          srp  nsrp  mu  full_pallet  split_pallet  icream_nsrp
# country                                                       
# CZ      43.6  55.0 0.0          0.3           0.8          0.2
# HU      32.7  66.3 0.0          0.2           0.6          0.2
# SK      46.0  52.7 0.0          0.3           0.9          0.2


    