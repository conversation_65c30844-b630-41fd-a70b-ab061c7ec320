import pyodbc
import pandas as pd
import polars as pl
import time
from datetime import datetime, timedelta
import os

def analyze_pbl_pbs_latest_status(products, output_dir):
    """
    Analyze PBL/PBS data for products based on their most recent status (ignores date range).
    Gets the most up-to-date picture for each country+tpnb combination.
    
    Parameters:
    products (pd.DataFrame): DataFrame with 'country' and 'tpnb' columns
    output_dir (str, optional): Directory to save output file
    
    Returns:
    pandas.DataFrame: Results with latest order type analysis
    """
    
    start_time = time.time()
    
    print("Analyzing latest status with valid source_name for all products")
    print("Getting most up-to-date picture where source_name is not empty")
    
    # Database connection with better error handling
    try:
        conn = pyodbc.connect(
            "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
        )
        cursor = conn.cursor()
    except Exception as e:
        raise ConnectionError(f"Failed to connect to database: {str(e)}")

    # Clean and validate products data
    products = products[['country', 'tpnb']].dropna().drop_duplicates()
    
    # Filter out invalid TPNBs (assuming they should be positive integers)
    products = products[products['tpnb'] > 0]
    
    # Print summary of products per country
    products_dict = products.groupby(["country"], observed=True)['tpnb'].apply(lambda s: s.tolist()).to_dict()
    for key, value in products_dict.items():
        print(f"{key}: {len(value)} products")

    # Country code mapping
    country_mapping = {
        'CZ': 'CZ',  # Czech Republic
        'HU': 'HU',  # Hungary  
        'SK': 'SK'   # Slovakia
    }

    def create_country_query(country_code, tpnb_list):
        """Create SQL query for a specific country to get latest status with non-empty source_name"""
        # Convert tpnb list to comma-separated string for SQL IN clause
        tpnb_string = ','.join([str(int(tpnb)) for tpnb in tpnb_list])
        
        query = f"""
        WITH latest_date_with_source AS (
            -- Get the most recent date for each product WHERE source_name is not empty
            SELECT 
                bpr_tpn,
                MAX(part_col) as latest_date_with_data
            FROM 
                stg_go.go_207_tpn_store_basic
            WHERE 
                int_cntr_code = '{country_code}'
                AND bpr_tpn IN ({tpnb_string})
                AND source_name IS NOT NULL
                AND source_name != ''
                AND TRIM(source_name) != ''
            GROUP BY 
                bpr_tpn
        ),
        
        latest_records AS (
            -- Get all records for each product on their most recent date WITH valid source_name
            SELECT 
                b.bpr_tpn,
                b.bpr_dsc,
                b.source_name,
                b.int_cntr_code,
                b.part_col,
                -- Extract PBL/PBS from source_name
                CASE 
                    WHEN UPPER(b.source_name) LIKE '%PBL%' THEN 'PBL'
                    WHEN UPPER(b.source_name) LIKE '%PBS%' THEN 'PBS'
                    ELSE 'Other'
                END AS order_type
            FROM 
                stg_go.go_207_tpn_store_basic b
            INNER JOIN 
                latest_date_with_source l 
                ON b.bpr_tpn = l.bpr_tpn 
                AND b.part_col = l.latest_date_with_data
            WHERE 
                b.int_cntr_code = '{country_code}'
                AND b.bpr_tpn IN ({tpnb_string})
                AND b.source_name IS NOT NULL
                AND b.source_name != ''
                AND TRIM(b.source_name) != ''
        ),
        
        aggregated_results AS (
            -- Aggregate the latest records for each product
            SELECT 
                bpr_tpn,
                bpr_dsc,
                int_cntr_code,
                -- Determine final order type based on what we found on the latest date with data
                CASE 
                    WHEN COUNT(DISTINCT CASE WHEN order_type IN ('PBL', 'PBS') THEN order_type END) > 1 THEN 'Mixed'
                    WHEN COUNT(DISTINCT CASE WHEN order_type = 'PBL' THEN order_type END) > 0 THEN 'PBL'
                    WHEN COUNT(DISTINCT CASE WHEN order_type = 'PBS' THEN order_type END) > 0 THEN 'PBS'
                    ELSE 'Other'
                END AS final_order_type,
                -- Collect ALL unique source names from the latest date with data
                ARRAY_JOIN(COLLECT_SET(source_name), ', ') AS all_source_names,
                -- Count of different order types on latest date with data
                COUNT(DISTINCT order_type) AS total_order_types,
                -- Count of PBL/PBS specific types on latest date with data
                COUNT(DISTINCT CASE WHEN order_type IN ('PBL', 'PBS') THEN order_type END) AS pbl_pbs_type_count,
                -- Latest date with data (should be same for all records of this product)
                MIN(part_col) AS latest_date_with_data,
                MAX(part_col) AS latest_date_check,
                -- Count total records on latest date with data
                COUNT(*) AS total_records_on_latest_date,
                -- Count by type on latest date with data
                COUNT(CASE WHEN order_type = 'PBL' THEN 1 END) AS pbl_records_latest,
                COUNT(CASE WHEN order_type = 'PBS' THEN 1 END) AS pbs_records_latest,
                COUNT(CASE WHEN order_type = 'Other' THEN 1 END) AS other_records_latest
            FROM 
                latest_records
            GROUP BY 
                bpr_tpn, bpr_dsc, int_cntr_code
        )
        
        SELECT 
            bpr_tpn AS tpnb,
            bpr_dsc AS product_name,
            int_cntr_code AS country_code,
            final_order_type AS order_type,
            all_source_names AS example_source_name,
            total_order_types,
            pbl_pbs_type_count,
            latest_date_with_data AS latest_date,
            latest_date_check,
            total_records_on_latest_date,
            pbl_records_latest,
            pbs_records_latest,
            other_records_latest
        FROM 
            aggregated_results
        ORDER BY 
            bpr_tpn;
        """
        return query

    def get_historical_source_name(conn, country_code, tpnb):
        """Get the most recent non-empty source_name for a specific product from history"""
        query = f"""
        SELECT 
            source_name,
            part_col
        FROM 
            stg_go.go_207_tpn_store_basic
        WHERE 
            int_cntr_code = '{country_code}'
            AND bpr_tpn = {tpnb}
            AND source_name IS NOT NULL
            AND source_name != ''
            AND TRIM(source_name) != ''
        ORDER BY 
            part_col DESC
        LIMIT 1
        """
        
        try:
            result = pl.read_database(query, conn).to_pandas()
            if not result.empty:
                return result.iloc[0]['source_name']
            else:
                return None
        except Exception as e:
            print(f"Error getting historical source_name for TPNB {tpnb}: {str(e)}")
            return None

    # Execute queries for each country and collect results
    all_results = []
    total_processed = 0

    for country, tpnb_list in products_dict.items():
        if country not in country_mapping:
            print(f"Warning: No country code mapping found for {country}")
            continue
            
        country_code = country_mapping[country]
        print(f"\nProcessing {country} (code: {country_code}) with {len(tpnb_list)} products...")
        
        try:
            # Create and execute query for this country
            query = create_country_query(country_code, tpnb_list)
            
            # Execute query and convert to pandas
            country_result = pl.read_database(query, conn).to_pandas()
            
            if not country_result.empty:
                # Add the original country name for reference
                country_result['country'] = country
                print(f"Retrieved {len(country_result)} records for {country}")
                total_processed += len(country_result)
                
                # Print summary of order types found
                order_type_summary = country_result['order_type'].value_counts()
                print(f"Latest status distribution for {country}:")
                for ot, count in order_type_summary.items():
                    print(f"  {ot}: {count}")
                
                # Print date range of latest data with source_name
                if 'latest_date' in country_result.columns:
                    min_date = country_result['latest_date'].min()
                    max_date = country_result['latest_date'].max()
                    print(f"Latest dates with valid source_name: {min_date} to {max_date}")
                
                # Check if any products are missing (didn't have any valid source_name data)
                found_tpnbs = set(country_result['tpnb'].tolist())
                missing_tpnbs = [tpnb for tpnb in tpnb_list if tpnb not in found_tpnbs]
                if missing_tpnbs:
                    print(f"  Warning: {len(missing_tpnbs)} products have no valid source_name data in history: {missing_tpnbs[:5]}{'...' if len(missing_tpnbs) > 5 else ''}")
                
                all_results.append(country_result)
            else:
                print(f"No records found for {country}")
                
        except Exception as e:
            print(f"Error processing {country}: {str(e)}")
            continue

    # Process final results
    if all_results:
        final_result = pd.concat(all_results, ignore_index=True)
        print(f"\nTotal combined records: {len(final_result)}")
        print(f"Countries in final result: {sorted(final_result['country'].unique())}")
        
        # Print overall summary
        print(f"\nOverall latest status distribution:")
        overall_summary = final_result['order_type'].value_counts()
        for ot, count in overall_summary.items():
            print(f"  {ot}: {count}")
        
        # Print overall date range
        if 'latest_date' in final_result.columns:
            min_date = final_result['latest_date'].min()
            max_date = final_result['latest_date'].max()
            print(f"\nOverall latest data dates range: {min_date} to {max_date}")
        
        # Check how many rows had historical fallback
        historical_count = final_result['example_source_name'].str.contains('(historical)', na=False).sum()
        no_data_count = final_result['example_source_name'].str.contains('No historical data found', na=False).sum()
        
        if historical_count > 0:
            print(f"\nRows with historical source_name fallback: {historical_count}")
        if no_data_count > 0:
            print(f"Rows with no historical data found: {no_data_count}")
        
        # # Display sample of results
        # print(f"\nSample of results:")
        # print(final_result.head(10).to_string())
        
        # Save to Excel with better file handling
        if output_dir is None:
            output_dir = os.path.expanduser("~/Documents")  # Default to user's Documents
        
        current_date = datetime.now().strftime('%Y%m%d')
        output_file = os.path.join(output_dir, f"pbl_pbs_latest_status_{current_date}.xlsx")
        try:
            final_result.to_excel(output_file, index=False)
            print(f"\nResults saved to: {output_file}")
        except Exception as e:
            print(f"Could not save to Excel: {str(e)}")
            
    else:
        print("No data retrieved from any country")
        final_result = pd.DataFrame()

    # Print execution summary
    end_time = time.time()
    print(f"\nExecution Summary:")
    print(f"- Total execution time: {end_time - start_time:.2f} seconds")
    print(f"- Records processed: {total_processed}")
    print(f"- Countries processed: {len([c for c in products_dict.keys() if c in country_mapping])}")
    print(f"- Analysis type: Latest status (no date range filtering)")

    
    # Close database connection
    conn.close()
    
    return final_result

# Example usage:
if __name__ == "__main__":
    
    output_dir = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#DC\GM_project"
    products = pd.read_excel(os.path.join(output_dir, "gm_local_list_extended.xlsx"))
    
        
    # Run the analysis
    result = analyze_pbl_pbs_latest_status(products, output_dir)
    
    print("\nAnalysis completed successfully!")
        
