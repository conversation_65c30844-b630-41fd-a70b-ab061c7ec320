
import pandas as pd
import pyodbc
import numpy as np
from datetime import datetime



pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)


start = '20240527'
end = '20240901'

# Define the start and end dates
start_date = datetime.strptime(start, '%Y%m%d')
end_date = datetime.strptime(end, '%Y%m%d')

# Calculate the difference between the two dates
difference = end_date - start_date

# Convert the difference into weeks
weeks = (difference.days + 6) // 7



with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:
    
    df = pd.DataFrame()
    countries_adress = ["hu", "cz", "sk"]
    countries_to_select = ['"HU"', '"CZ"', '"SK"']
    for c1, c2 in zip(countries_adress, countries_to_select):

        sql = """
        
        
                    SELECT 
                    b.cntr_code AS country,
                    CAST(a.site as INT) as store, 
                   
                    CAST(b.slad_tpnb as INT) AS tpnb,
                    b.slad_long_des AS product_name,
                    hier.pmg AS pmg,
                    b.slad_tpn AS tpn,
                    b.dmat_div_des_en AS DIV_DESC,
                    b.dmat_div_code as DIV_ID,
                    b.dmat_dep_des_en AS DEP_DESC,
                    b.dmat_dep_code as DEP_ID,
                    b.dmat_sec_des_en AS SEC_DESC,
                    b.dmat_sec_code as SEC_ID,
                    b.dmat_grp_des_en AS GRP_DESC,
                    b.dmat_grp_code as GRP_ID,
                    b.dmat_sgr_des_en AS SGR_DESC,
                    b.dmat_sgr_code as SGR_ID,
                    AVG(b.slad_net_weight) AS weight_of_product,
                    b.slad_unit AS unit_type,
                    SUM(a.qty) AS sold_kg,
                    SUM(CASE WHEN a.qty > 0 AND a.qty <= 500 THEN 1 ELSE 0 END) AS `0.5kg`,
                    SUM(CASE WHEN a.qty > 0.5 AND a.qty <= 1000 THEN 1 ELSE 0 END) AS `1kg`,
                    SUM(CASE WHEN a.qty > 1000 AND a.qty <= 1500 THEN 1 ELSE 0 END) AS `1.5kg`,
                    SUM(CASE WHEN a.qty > 1500 AND a.qty <= 2000 THEN 1 ELSE 0 END) AS `2kg`,
                    SUM(CASE WHEN a.qty > 2000 AND a.qty <= 2500 THEN 1 ELSE 0 END) AS `2.5kg`,
                    SUM(CASE WHEN a.qty > 2500 AND a.qty <= 3000 THEN 1 ELSE 0 END) AS `3kg`,
                    SUM(CASE WHEN a.qty > 3000 THEN 1 ELSE 0 END) AS `3kg+`
                     
                     
                    FROM pos{c1}.t001 a
                    LEFT JOIN DM.dim_artgld_details b
                    ON a.ean = lpad(b.slem_ean,14,0)
                    RIGHT JOIN tesco_analysts.hierarchy_spm hier ON b.dmat_div_code = LPAD(hier.div_code,4,"0") 
                    AND b.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                    AND b.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                    AND b.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                    AND b.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
                    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
                    WHERE
                    hier.pmg in ("PRO03","PRO04","PRO05","PRO06")
                    AND a.part_col between {start} and {end}
                    AND b.cntr_code = {c2}
                    AND b.slad_unit = 'KG'
                    GROUP BY 
                        b.cntr_code,
                        CAST(a.site as INT), 
                       
                        CAST(b.slad_tpnb as INT),
                        b.slad_long_des,
                        hier.pmg,
                        b.slad_tpn,
                        b.dmat_div_des_en,
                        b.dmat_div_code,
                        b.dmat_dep_des_en,
                        b.dmat_dep_code,
                        b.dmat_sec_des_en,
                        b.dmat_sec_code,
                        b.dmat_grp_des_en,
                        b.dmat_grp_code,
                        b.dmat_sgr_des_en,
                        b.dmat_sgr_code,
                        b.slad_unit
        
        
                
                """.format(weeks = weeks, start = int(start), end = int(end), c1 = c1, c2=c2 )
                
                
        b = pd.read_sql(sql, conn)
        df = pd.concat([df,b])



        
        
        
    # sql = """
    
    
    # select * from dw.dim_suppliers
    # where dmsup_long_des = 'NEKUPTO HU KFT._HU41526_BUDAPES'
    # limit 5
    
    # """
        
    # b = pd.read_sql(sql, conn)
    
    
# PBL part    
# a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\PBS_PBL\dcr_reports\pbs_pbl_dataset.parquet")
# a = a[~a['Deliv Type'].str.contains("PBS")]
# a['country'] = a["Store"].apply(lambda x: 'SK' if str(x).startswith("2") else ("CZ" if str(x).startswith("1") else ("HU" if str(x).startswith("4") else None)))
# a['delivery_type'] = 'PBL'
# a.columns = [x.lower() for x in a.columns]
# a = a[['country','tpnb', 'delivery_type']].drop_duplicates()

# b = b.merge(a, on=['country', 'tpnb'], how="left")
# b['delivery_type'] = b['delivery_type'].replace(np.nan, "not found in my PBL database")

    # start = "'f2023w14'"
    # end = "'f2023w27'"
    
    # nr_weeks = int(end[7:9]) - int(start[7:9]) + 1

    # loss_query = f"""

    #     SELECT
    #     stores.cntr_code AS country,
    #     CAST(stores.dmst_store_code as INT) AS store,
    #     CAST(tpns.slad_tpnb as INT) AS tpnb,
    #     tpns.slad_long_des as product_name,
    #     hier.pmg AS pmg,
    #     CAST(a.lssls_rcode as INT) AS code,
    #     SUM(a.lssls_quantity_adj)*-1 AS amount 
    #     FROM dw.ls_stock_loss a
    #     JOIN dm.dim_stock_loss_rc b ON a.lssls_cntr_id = b.cntr_id
    #     AND a.lssls_rcode = b.lsrc_code 
    #     JOIN dm.dim_stores stores ON stores.dmst_store_id = a.lssls_dmst_id 
    #     AND stores.cntr_id = a.lssls_cntr_id 
    #     JOIN dm.dim_artgld_details tpns ON tpns.slad_dmat_id = a.lssls_dmat_id 
    #     LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
    #     JOIN tesco_analysts.hierarchy_spm hier ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0") 
    #     AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
    #     AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
    #     AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
    #     AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
    #     WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
    #     AND stores.cntr_code IN ("HU","CZ","SK")
    #     AND a.lssls_rcode in ('3', '4')
    #     AND SUBSTRING(hier.pmg, 1, 3) IN ("CLG")
    #     GROUP BY stores.cntr_code, stores.dmst_store_code, tpns.slad_tpnb,tpns.slad_long_des, hier.pmg, a.lssls_rcode
    # """.format(
    #         start=start, #
    #         end=end,
    #         nr_weeks=nr_weeks,
    #     )

    # b = pd.read_sql(loss_query, conn)
