import pandas as pd
import itertools

# Define stores and PMG values
stores = [25002, 25003, 25005, 25006, 25011, 25012, 25016, 25018, 25023, 
          25024, 25026, 25028, 25032, 25033, 25034]

dep = ['BWS','DAI','DRY','FRZ','HDL','HEA','NEW','PPD','PRO','SFM','SFB','SFP']

# Create all possible combinations of stores and PMG values
combinations = list(itertools.product(stores, dep))

# Create DataFrame
df = pd.DataFrame(combinations, columns=['store', 'dep'])

# Optional: Sort by Store and PMG
df = df.sort_values(['store', 'dep']).reset_index(drop=True)

# Display the first few rows to verify
print("\nFirst few rows of the dataframe:")
print(df.head())

# Display shape of the dataframe
print("\nDataframe shape:", df.shape)