from pathlib import Path
import os
import time
import PySimpleGUI as sg
import threading
import pandas as pd
import polars as pl
import py_model.Replenishment_Model_24 as rm
import py_model.ClassesModel as cm
import py_model.Replenishment_Model_Functions_24 as rmf
from xlsxwriter import Workbook
import openpyxl
from openpyxl import  load_workbook
import pyarrow.parquet as pq
import queue
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.pyplot as plt
import seaborn as sns
import base64
import re
import json

verz_info = "2024 Q1"
mainthread_popup_queue:queue.Queue = None


def extract_path_suffix(input_string, delimiter='/inputs/'):
    try:
        suffix = input_string.split(delimiter)[1]
        return delimiter[1:] + suffix
    except IndexError:
        # If the delimiter is not found, handle the exception
        return None

def image_to_base64(image):

    with open(f"{image}", "rb") as img_file:
        my_string = base64.b64encode(img_file.read())

    return my_string

def piechart(df, chart_type):


    def delete_figure_agg(figure_agg):
        figure_agg.get_tk_widget().forget()
        try:
            draw_figure.canvas_packed.pop(figure_agg.get_tk_widget())
        except Exception as e:
            pass
        plt.close('all')







    if chart_type == 'manual':
        if 'country' in df.columns:
            if df.country.nunique() > 1:
                df_for_stores = df[['country','srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']].groupby(['country'],observed=True).sum().reset_index().melt(id_vars='country').groupby(['country','variable'],observed=True).sum().reset_index() #df[['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']].melt().groupby('variable').sum().reset_index()
                # df_for_stores.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_02\test.xlsx", index=False)
                # df_for_stores.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_02\test_1.xlsx",index=False)
            else:
                # df_for_stores = df.head()

                df_for_stores = df[['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']].melt().groupby('variable').sum().reset_index()
                # print(df_for_stores.head())

        if not 'country' in df.columns:
            if df.store.nunique() > 1:
                df_for_stores = df[['store','srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']].groupby(['store'],observed=True).sum().reset_index().melt(id_vars='store').groupby(['store','variable'],observed=True).sum().reset_index() #df[['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']].melt().groupby('variable').sum().reset_index()
            else:
                # df_for_stores = df.head()
                df_for_stores = df[['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']].melt().groupby('variable').sum().reset_index()



    if chart_type == 'from_system':



        df_for_countries = df[['country','srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']].groupby(['country'],observed=True).sum().reset_index().melt(id_vars='country').groupby(['country','variable'],observed=True).sum().reset_index()

        # df_for_countries.to_excel(
        #     r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_02\test_2.xlsx",
        #     index=False)
        # print(df_for_countries.head())
        deps = df.dep.unique().tolist()
        # deps = df['Category name'].unique().tolist()


    def piechart_sns(df_for_stores):


        try:
            if 'country' in df_for_stores.columns:

                if df_for_stores.country.nunique() > 1:
                    df_for_stores['country'] = df_for_stores['country'].astype('str')
                    def pie(v, l, color=None):
                        colors = sns.color_palette('pastel')[0:5]
                        plt.pie(v,  colors=colors, autopct=lambda p: '{:.0f}%'.format(round(p)) if p > 0.5 else '', explode=[0.05]*len(l.values),  pctdistance=0.8,shadow=True )

                    g = sns.FacetGrid(df_for_stores, col="country", height=4)
                    g.map(pie, "value", "variable").add_legend(labels=df_for_stores.variable.unique())
                    g.set_xlabels("")
                    g.set_ylabels("")

                    return plt.gcf()

            if not 'country' in df_for_stores.columns:
                if 7 > df_for_stores.store.nunique() > 1:
                    def pie(v, l, color=None):
                        colors = sns.color_palette('pastel')[0:5]
                        plt.pie(v, colors=colors, autopct=lambda p: '{:.0f}%'.format(round(p)) if p > 0.5 else '',
                                explode=[0.05] * len(l.values), pctdistance=0.8, shadow=True)

                    g = sns.FacetGrid(df_for_stores, col="store", height=4, col_wrap=3)
                    g.map(pie, "value", "variable").add_legend(labels=df_for_stores.variable.unique())
                    g.set_xlabels("")
                    g.set_ylabels("")

                    return plt.gcf()
                else:
                    # define data
                    data = df_for_stores.groupby('variable')['value'].sum().reset_index()['value'].tolist()
                    labels = df_for_stores['variable'].unique().tolist()

                    # define Seaborn color palette to use
                    colors = sns.color_palette('pastel')[0:5]

                    # create pie chart
                    plt.pie(data, colors=colors, autopct=lambda p: '{:.0f}%'.format(round(p)) if p > 0.5 else '',
                            explode=[0.05] * len(labels), pctdistance=0.8, shadow=True)
                    plt.legend(labels, bbox_to_anchor=(1., 1), borderaxespad=-0.5, loc=2)

                    return plt.gcf()
        except:

            # define data
            data = df_for_stores['value'].tolist()
            labels = df_for_stores['variable'].tolist()

            # define Seaborn color palette to use
            colors = sns.color_palette('pastel')[0:5]

            # create pie chart
            plt.pie(data, colors=colors, autopct=lambda p: '{:.0f}%'.format(round(p)) if p > 0.5 else '', explode=[0.05]*len(labels),  pctdistance=0.8, shadow=True)
            plt.legend(labels,bbox_to_anchor=(1., 1), borderaxespad=-0.5, loc=2)

            return plt.gcf()


    def piechart_sns_countries(df_for_countries):

        def pie(v, l, color=None):
            colors = sns.color_palette('pastel')[0:5]
            plt.pie(v,  colors=colors, autopct=lambda p: '{:.0f}%'.format(round(p)) if p > 0.5 else '', explode=[0.05]*len(l.values),  pctdistance=0.8,shadow=True )


        g = sns.FacetGrid(df_for_countries, col="country", height=4)
        g.map(pie, "value", "variable").add_legend(labels=df_for_countries.variable.unique())
        g.set_xlabels("")
        g.set_ylabels("")

        return plt.gcf()



    if chart_type == "manual":
        layout = [[sg.Text('Piechart weighted by Shelf Capacity')],
                  [sg.Canvas(size=(1200, 500), key='-CANVAS-')],
                  [sg.Exit(), sg.Button('Save as JPG', key='save_chart')]]
    if chart_type == "from_system":
        layout = [[sg.Text(f'Piechart weighted by Shelf Capacity for these departments: {deps}')],
                  [sg.Canvas(size=(1200, 500), key='-CANVAS-')],
                  [sg.Exit(), sg.Button('Save as JPG', key='save_chart')]]

    def draw_figure(canvas, figure):
        figure_canvas_agg = FigureCanvasTkAgg(figure, canvas)
        figure_canvas_agg.draw()
        figure_canvas_agg.get_tk_widget().pack(side='top', fill='both', expand=1)
        return figure_canvas_agg

    figure_agg = None
    window = sg.Window('Piechart weighted by Shelf Capacity', layout, grab_anywhere=True, finalize=True, element_justification='center')

    if chart_type == "manual":
        try:
            figure_agg = draw_figure(window['-CANVAS-'].TKCanvas, piechart_sns(df_for_stores))
        except ValueError:
            sg.popup_error("This PMG does not exist!")
            pass


    if chart_type == "from_system":
        try:
            figure_agg = draw_figure(window['-CANVAS-'].TKCanvas, piechart_sns_countries(df_for_countries))
        except ValueError:
            sg.popup_error("This PMG does not exist!")
            pass

    directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd())

    while True:
        event, values = window.read()
        if event == sg.WIN_CLOSED or event == 'Exit':
            try:
                delete_figure_agg(figure_agg)
            except:
                pass
            break
        if event== 'save_chart':
            piechart_save_name = sg.popup_get_text('Name your PieChart!')
            plt.savefig(directory / f"outputs\Charts\PieChart_{piechart_save_name}.jpg")
            sg.popup('PieChart has been saved into Outputs/Charts folder')


    window.close()


def show_table(df, sheet_name):


    if sheet_name == 'country_tpnb':



        df_to_show = df.groupby(['country', 'tpnb', 'division', 'pmg', "product_name", 'opening_type', 'as_is_model_contains?'],
                                observed=True).agg({
            'srp': 'sum', 'nsrp': 'sum', 'full_pallet': 'sum', 'mu': 'sum', 'split_pallet': 'sum', 'sold_units': 'sum','cases_delivered':'sum',
            'case_capacity': 'mean', 'shelf_capacity':'sum'
        }).reset_index()
        # df.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_02\test2.xlsx", index=False)
        shel_cap_mean = df.groupby(['country', 'tpnb'],
                                observed=True).agg({'shelf_capacity':'mean'
        }).reset_index()


        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
            df_to_show[r] = (df_to_show[r] / df_to_show.shelf_capacity) * 100

        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
            df_to_show[r] = df_to_show[r].apply(lambda x: f'{x:.0f}%')

        df_to_show.drop('shelf_capacity', axis=1,inplace=True)
        df_to_show = df_to_show.merge(shel_cap_mean, on=['country','tpnb'], how='left')

        df_to_show = df_to_show.astype(
            {'sold_units': 'int', 'cases_delivered':'int',
             'case_capacity': 'int','shelf_capacity':'int'})


        df_to_show = df_to_show.sort_values(by=['country', 'sold_units'], ascending=[True, False])


        # Uses the first row (which should be column names) as columns names
        header_list = list(df_to_show.columns)
        # Drops the first row in the table (otherwise the header names and the first row will be the same)
        data = df_to_show[0:].values.tolist()


        layout = [[sg.Frame('New File Name:',[[sg.Input(key='saved_name_as_is',enable_events=True, default_text='Here add a saving name', size=(30,1), expand_x=True),
                                               sg.Button("Export_to_excel", key= "export", button_color="lightblue") ],
                                              ]), sg.VSeparator(),
                   sg.Frame('Options:',
                            [[sg.Button("Show me the Piechart!", key="piechart_country", button_color="lightblue"), sg.Button("Add Volume/sold_units to input table!", key="add_volume", button_color="lightblue")],
                             ]),sg.VSeparator(),sg.Text(f"Nr Of TPNB: {df.tpnb.nunique()} \nVolume: {int(round(df.sold_units.sum(),0))}", key='number_of_tpnb'), sg.VSeparator()],
            [sg.Table(values=data,
                      headings=header_list,
                      pad=(5, 5),
                      max_col_width = 10,
                      display_row_numbers=False,
                      auto_size_columns=True,
                      select_mode=sg.TABLE_SELECT_MODE_BROWSE,
                      vertical_scroll_only=False,
                      justification="center",
                      num_rows=min(20, len(data)))]
        ]

    if sheet_name != 'country_tpnb':

        # Uses the first row (which should be column names) as columns names
        header_list = list(df.columns)
        # Drops the first row in the table (otherwise the header names and the first row will be the same)
        data = df[0:].values.tolist()

        df_to_show = df.copy()

        layout = [[sg.Frame('New File Name:',
                            [[sg.Input(key='saved_name_as_is', enable_events=True,
                                                         default_text='Here add a saving name', size=(30, 1),
                                                         expand_x=True),
                                                sg.Button("Export_to_excel", key="export", button_color="lightblue")],
                                               ]),sg.VSeparator(),
                  sg.Frame('Options:',
                            [[sg.Button("Show me the Piechart!", key="piechart_stores", button_color="lightblue"), sg.Button("Add Volume/sold_units to input table!", key="add_volume", button_color="lightblue")],
                             ]),sg.VSeparator(),sg.Text(f"Nr Of TPNB: {df.tpnb.nunique()}\nVolume: {int(round(df.sold_units.sum(),0))}", key='number_of_tpnb'), sg.VSeparator()],
                  [sg.Table(values=data,
                            headings=header_list,
                            pad=(5, 5),
                            max_col_width=10,
                            display_row_numbers=True,
                            auto_size_columns=True,
                            select_mode=sg.TABLE_SELECT_MODE_BROWSE,
                            vertical_scroll_only=False,
                            justification="center",
                            num_rows=min(20, len(data)))]
                  ]

    window = sg.Window(sheet_name, layout, grab_anywhere=True, finalize=True)
    window['saved_name_as_is'].update(select=True)
    new_version_name_as_is = "default"
    while True:  # The Event Loop
        event, values = window.read()
        if event == sg.WIN_CLOSED:
            break
        if event == "saved_name_as_is":
            new_version_name_as_is = values["saved_name_as_is"]
        if event == "export":
            print('export')
            df_to_show.to_excel(Path(os.getcwd()) /f'outputs/ReplTypeChanger_outputs/AS_is_{new_version_name_as_is}.xlsx', index = False)
            question_export_model("export", new_version_name_as_is)
        if event == "piechart_stores":
            piechart(df, "manual")
        if event == "piechart_country":
            piechart(df, "manual")
        if event == "add_volume":

            if sheet_name == 'country_tpnb':
                try:
                    FilePath = Path(os.getcwd()) / 'selected_tpns.xlsx'
                    wb = openpyxl.load_workbook(FilePath)
                    ws = wb[sheet_name]
                    ws.delete_rows(2, 100000)
                    wb.save(FilePath)
                    wb.close()
                except PermissionError:
                    sg.popup_error("Close the selected_tpns.xlsx")
                    pass
                data_to_input_file(df, 'country_tpnb')
                question_input("input")


            if sheet_name == 'store_tpnb':
                try:
                    FilePath = Path(os.getcwd()) / 'selected_tpns.xlsx'
                    wb = openpyxl.load_workbook(FilePath)
                    ws = wb[sheet_name]
                    ws.delete_rows(2, 100000)
                    wb.save(FilePath)
                    wb.close()
                except PermissionError:
                    sg.popup_error("Close the selected_tpns.xlsx")
                    pass

                data_to_input_file(df, 'store_tpnb')
                question_input("input")

    window.close()


def showing_settings(TPN_Cost_Base,repl_type, image_toggle_on, image_toggle_off, opening_type, console, sales_modifier, case_cap_modifier,  shelfcap_modifier, chunk_size):
    def toggle_on_or_off(true_false):
        if true_false == True:
            toggle = image_to_base64(image_toggle_on)
        if true_false == False:
            toggle = image_to_base64(image_toggle_off)
        return toggle

    repl_type="none"
    opening_type = "none"

    products_change_to_fram = sg.Frame('Products ReplType change to:',[[
            sg.Radio('srp', 'group 2', key='repl_type_srp', enable_events=True, default=False),sg.Push(),
            sg.Radio('nsrp', 'group 2', key='repl_type_nsrp' , enable_events=True),sg.Push(),
            sg.Radio('FP', 'group 2', key='repl_type_full_pallet' , enable_events=True, tooltip='Full Pallet'),sg.Push(),
            sg.Radio('HP', 'group 2', key='repl_type_mu' , enable_events=True, tooltip='Half Pallet'),sg.Push(),
            sg.Radio('SP', 'group 2', key='repl_type_split_pallet' , enable_events=True, tooltip='Split Pallet'),sg.Push(),
            sg.Radio('single_pick', 'group 2', key='repl_type_single_pick', enable_events=True), sg.Push(),
            sg.Radio('froz_pour', 'group 2', key='repl_type_frozen_srp', enable_events=True, tooltip='Pouring on Frozen'), sg.Push(),
            sg.Radio('None', 'group 2', key='repl_type_none' , enable_events=True, default=True)]], expand_x=True,expand_y=True)

    other_settings = sg.Frame("Other Settings:",[[sg.Col([[sg.T("CostBase"),sg.Push(),sg.Button(image_data=toggle_on_or_off(TPN_Cost_Base),k="costbase_check",border_width=0,
                  button_color=(
                sg.theme_background_color(),
                sg.theme_background_color(),
            ),
            disabled_button_color=(
                sg.theme_background_color(),
                sg.theme_background_color(),
            ),
            metadata=BtnInfo(
                TPN_Cost_Base
            )
        ),sg.Push(), sg.T("volume\nmodifier"),sg.Push(),sg.Button(image_data=toggle_on_or_off(sales_modifier),k="sales_modifier",border_width=0,
                  button_color=(
                sg.theme_background_color(),
                sg.theme_background_color(),
            ),
            disabled_button_color=(
                sg.theme_background_color(),
                sg.theme_background_color(),
            ),
            metadata=BtnInfo(
                sales_modifier
            )
        ),sg.Push(), sg.T("case capacity\nmodifier"),sg.Push(),sg.Button(image_data=toggle_on_or_off(case_cap_modifier),k="casecap_modifier",border_width=0,
                  button_color=(
                sg.theme_background_color(),
                sg.theme_background_color(),
            ),
            disabled_button_color=(
                sg.theme_background_color(),
                sg.theme_background_color(),
            ),
            metadata=BtnInfo(
                case_cap_modifier
            )
        ),sg.Push(), sg.T("shelf capacity\nmodifier"),sg.Push(),sg.Button(image_data=toggle_on_or_off(shelfcap_modifier),k="shelfcap_modifier",border_width=0,
                  button_color=(
                sg.theme_background_color(),
                sg.theme_background_color(),
            ),
            disabled_button_color=(
                sg.theme_background_color(),
                sg.theme_background_color(),
            ),
            metadata=BtnInfo(
                shelfcap_modifier
            )
        ),]], expand_y=True,expand_x=True)]], expand_x=True, expand_y=True, pad=(0,20))

    opening_types = sg.Frame('Products Opening Types to:',[[sg.Radio(' Tray+Hood', 'group 5', key='op_type_Tray + Hood', enable_events=True, default=False),sg.Push(),
             sg.Radio('Perforated', 'group 5', key='op_type_Perforated box' , enable_events=True, default=False),sg.Push(),
            sg.Radio('Shrink', 'group 5', key='op_type_Shrink' , enable_events=True, default=False),sg.Push(),
            sg.Radio('Tray+shrink', 'group 5', key='op_type_Tray + Shrink' , enable_events=True, default=False),sg.Push(),
            sg.Radio('Tray', 'group 5', key='op_type_Tray' , enable_events=True, default=False),sg.Push(),
            sg.Radio('None', 'group 5', key='op_type_none' , enable_events=True, default=True)]], expand_y=True, expand_x=True)

    how_many_tpnb_at_one_time =    sg.Frame('How many TPNB will be processed at one time?:',
                                            [[sg.Input(default_text = int(chunk_size),
                                                       key='how_many_tpnb', size = 5), sg.B('ok', key='ok_tpnb', button_color="lightblue")]],
                                            element_justification='center',expand_y=True, expand_x=True)


    layout=[[products_change_to_fram], [other_settings], [opening_types], [how_many_tpnb_at_one_time]]

    window = sg.Window("Settings", layout, grab_anywhere=True, finalize=True)

    while True:  # The Event Loop
        event, values = window.read()
        if event == sg.WIN_CLOSED:
            break

        elif event.startswith("repl_type_"):
            repl_type = event[10:]
            console.update("")
            print(f"You select: {repl_type}")
            TPN_Cost_Base = False
            window["costbase_check"].metadata.state = False
            window["costbase_check"].update(image_data=image_to_base64(image_toggle_off))

        elif event.startswith("op_type_"):
            opening_type = event[8:]
            console.update("")
            print(f"You select: {opening_type}")
            TPN_Cost_Base = False
            window["costbase_check"].metadata.state = False
            window["costbase_check"].update(image_data=image_to_base64(image_toggle_off))

        elif event == 'costbase_check':
            window[event].metadata.state = not window[event].metadata.state
            window[event].update(
                image_data=image_to_base64(image_toggle_on) if window[event].metadata.state else image_to_base64(image_toggle_off)
            )
            if window["costbase_check"].metadata.state == True:
                TPN_Cost_Base = True
                console.update("")
                print("You select: 'CostBase calculation on selected TPNBs'")
                case_cap_modifier = False
                sales_modifier = False
                shelfcap_modifier = False



                window["sales_modifier"].metadata.state = False
                window["sales_modifier"].update(image_data=image_to_base64(image_toggle_off))
                window["casecap_modifier"].metadata.state = False
                window["casecap_modifier"].update(image_data=image_to_base64(image_toggle_off))
                window["shelfcap_modifier"].metadata.state = False
                window["shelfcap_modifier"].update(image_data=image_to_base64(image_toggle_off))
            if window["costbase_check"].metadata.state == False:

                TPN_Cost_Base = False
                console.update("")
                print("You deselect: 'CostBase calculation on selected TPNBs'")
                window["costbase_check"].update(image_data=image_to_base64(image_toggle_off))

        elif event == 'sales_modifier':
            window[event].metadata.state = not window[event].metadata.state
            window[event].update(
                image_data=image_to_base64(image_toggle_on) if window[event].metadata.state else image_to_base64(image_toggle_off)
            )
            if window["sales_modifier"].metadata.state == True:
                sales_modifier = True

                if case_cap_modifier == True:
                    print("You select: 'SalesModifier calculation on selected TPNBs'")
                else:
                    console.update("")
                    print("You select: 'SalesModifier calculation on selected TPNBs'")

                TPN_Cost_Base = False
                window["costbase_check"].metadata.state = False
                window["costbase_check"].update(image_data=image_to_base64(image_toggle_off))


            if window["sales_modifier"].metadata.state == False:
                sales_modifier = False
                if case_cap_modifier == True:
                    print("You deselect: 'SalesModifier calculation on selected TPNBs'")
                else:
                    console.update("")
                    print("You deselect: 'SalesModifier calculation on selected TPNBs'")

                window["sales_modifier"].update(image_data=image_to_base64(image_toggle_off))

        elif event == 'casecap_modifier':
            window[event].metadata.state = not window[event].metadata.state
            window[event].update(
                image_data=image_to_base64(image_toggle_on) if window[event].metadata.state else image_to_base64(image_toggle_off)
            )
            if window["casecap_modifier"].metadata.state == True:
                case_cap_modifier = True
                if sales_modifier == True:

                    print("You select: 'CaseCapacityModifier calculation on selected TPNBs'")
                else:
                    console.update("")
                    print("You select: 'CaseCapacityModifier calculation on selected TPNBs'")
                TPN_Cost_Base = False
                window["costbase_check"].metadata.state = False
                window["costbase_check"].update(image_data=image_to_base64(image_toggle_off))


            if window["casecap_modifier"].metadata.state == False:
                case_cap_modifier = False
                if sales_modifier == True:

                    print("You deselect: 'CaseCapacityModifier calculation on selected TPNBs'")
                else:
                    console.update("")
                    print("You deselect: 'CaseCapacityModifier calculation on selected TPNBs'")
                window["casecap_modifier"].update(image_data=image_to_base64(image_toggle_off))

        elif event == 'shelfcap_modifier':
            window[event].metadata.state = not window[event].metadata.state
            window[event].update(
                image_data=image_to_base64(image_toggle_on) if window[event].metadata.state else image_to_base64(image_toggle_off)
            )
            if window["shelfcap_modifier"].metadata.state == True:
                shelfcap_modifier = True
                if sales_modifier == True:

                    print("You select: 'ShelfCapacityModifier calculation on selected TPNBs'")
                else:
                    console.update("")
                    print("You select: 'ShelfCapacityModifier calculation on selected TPNBs'")
                TPN_Cost_Base = False
                window["costbase_check"].metadata.state = False
                window["costbase_check"].update(image_data=image_to_base64(image_toggle_off))


            if window["shelfcap_modifier"].metadata.state == False:
                shelfcap_modifier = False
                if sales_modifier == True:

                    print("You deselect: 'ShelfCapacityModifier calculation on selected TPNBs'")
                else:
                    console.update("")
                    print("You deselect: 'ShelfCapacityModifier calculation on selected TPNBs'")
                window["shelfcap_modifier"].update(image_data=image_to_base64(image_toggle_off))

        elif event == "ok_tpnb":

            try:
                chunk_size = int(values['how_many_tpnb'])
                # settings['selected_chunksize'] = chunk_size
                # save_settings(settings)
                save_setting('selected_chunksize', chunk_size)

                sg.popup(f"{chunk_size} TPNB will be processed at one time!")
            except:
                sg.popup_error(
                    'It is not integer formula! Please give a number about how many tpnb will be processed at one time')

    window.close()
    return TPN_Cost_Base, repl_type, opening_type, sales_modifier, case_cap_modifier, shelfcap_modifier, chunk_size


def table_for_showing_stores(df):
    # Uses the first row (which should be column names) as columns names
    header_list = list(df.columns)
    # Drops the first row in the table (otherwise the header names and the first row will be the same)
    data = df[0:].values.tolist()

    #input_data_dict = input_data.to_dict('dict')
    layout = [[sg.Col([[sg.Frame('New File Name:',[[sg.Input(key='saved_name_as_is',enable_events=True, default_text='Here add a saving name', size=(25,1), expand_x=True)],
                                           [sg.Button("Export_to_excel", key= "export", button_color="lightblue")]]

                                        , element_justification="center", expand_y=True)]]),
                                            sg.Col([[sg.Frame('Replenishment Types:',
                         [[sg.Radio('all_types', 'group 4', key='all_types', enable_events=True)
                              ,sg.Radio('srp', 'group 4', key='rt_srp', enable_events=True),
                           sg.Radio('nsrp', 'group 4', key='rt_nsrp', enable_events=True)],
                          [sg.Radio('full_pallet', 'group 4', key='rt_full_pallet', enable_events=True),
                           sg.Radio('mu', 'group 4', key='rt_mu', enable_events=True),
                           sg.Radio('split_pallet', 'group 4', key='rt_split_pallet', enable_events=True)]],element_justification="center", expand_y=True)]] ),
               sg.Col([[sg.Frame("Choose mode for saving TPNB:",[[sg.Col(
                               [[sg.Radio('STORE + TPNB', 'group 1', key='sheet_store_tpnb', enable_events=True)],
                                [sg.Radio('COUNTRY + TPNB', 'group 1', key='sheet_country_tpnb', enable_events=True)]]),
                   sg.Col(
               [[sg.Button("Clear Sheet", key = "clear_sheet", enable_events=True, button_color="lightblue")],
               [sg.Button("Add TPNB to selected_tpns.xlsx", key = "add_tpnb_country", enable_events=True, button_color="lightblue")],
                [sg.Button("Open Input Table!", key="open_input",enable_events=True, button_color="lightblue")]], element_justification="center")]],
                               )]],
                      )],

        [sg.Table(values=data,
                  headings=header_list,
                  pad=(25, 25),
                  display_row_numbers=False,
                  auto_size_columns=True,
                  select_mode=sg.TABLE_SELECT_MODE_BROWSE,
                  justification="center",
                  key="table",
                  num_rows=min(20, len(data)))],
              [sg.T(f"Total Volume: {round(df.sold_units.sum(),0)}")]
    ]
    window = sg.Window("Stores view", layout, grab_anywhere=True, finalize=True)
    window['saved_name_as_is'].update(select=True)
    new_version_name_as_is = "default"
    sheet_name = ""
    df_filtered = df.copy()

    while True:  # The Event Loop
        event, values = window.read()
        if event == sg.WIN_CLOSED:
            break
        if event == "saved_name_as_is":
            new_version_name_as_is = values["saved_name_as_is"]
        if event == "export":
            df.to_excel(Path(os.getcwd()) /f'outputs/ReplTypeChanger_outputs/AS_is_{new_version_name_as_is}.xlsx', index = False)
            question_export_model("export",new_version_name_as_is)

        if event == 'add_tpnb_country':
            try:
                if sheet_name == 'store_tpnb':
                    data_to_input_file(df_filtered, sheet_name)
                if sheet_name == 'country_tpnb':
                    df_filter_country = df_filtered.groupby(['country','tpnb','division','pmg','product_name'], observed=True)['sold_units'].sum().reset_index()
                    data_to_input_file(df_filter_country, sheet_name)
                if sheet_name == "":
                    sg.popup_error("Please select a type ('tpnb', etc...)")
            except:
                pass

        if event == "all_types":
            df_filtered = df.copy()
            window['table'].update(values=df_filtered[0:].values.tolist())

        if event == "rt_srp":
            df_filtered = df[(df['srp'].str.replace('%', '').astype(int) > 0)].sort_values(by=['srp'], ascending=False)
            window['table'].update(values=df_filtered[0:].values.tolist())
        if event == "rt_nsrp":
            df_filtered = df[(df['nsrp'].str.replace('%', '').astype(int) > 0) ].sort_values(by=['nsrp'], ascending=False)
            window['table'].update(values=df_filtered[0:].values.tolist())
        if event == "rt_full_pallet":
            df_filtered = df[(df['full_pallet'].str.replace('%', '').astype(int) > 0)].sort_values(by=['full_pallet'], ascending=False)
            window['table'].update(values=df_filtered[0:].values.tolist())
        if event == "rt_mu":
            df_filtered = df[(df['mu'].str.replace('%', '').astype(int) > 0)].sort_values(by=['mu'], ascending=False)
            window['table'].update( values=df_filtered[0:].values.tolist())
        if event == "rt_split_pallet":
            df_filtered = df[(df['split_pallet'].str.replace('%', '').astype(int) > 0)].sort_values(by=['split_pallet'], ascending=False)
            window['table'].update( values=df_filtered[0:].values.tolist())

        if event == 'sheet_store_tpnb':
            sheet_name = 'store_tpnb'
        if event == "sheet_country_tpnb":
            sheet_name = 'country_tpnb'
        if event == 'sheet_tpnb':
            sheet_name = 'tpnb'


        if event == 'clear_sheet':
            try:
                really_wanna_delete_tpnb(sheet_name)
            except:
                pass

        if event == 'open_input':
            os.system(f"start EXCEL.EXE selected_tpns.xlsx")


    window.close()

def clear_input_sheets(sheet_name):
    try:
        FilePath = Path(os.getcwd()) / 'selected_tpns.xlsx'
        wb = openpyxl.load_workbook(FilePath)
        ws = wb[sheet_name]
        #ws.delete_cols(2, 6)
        ws.delete_rows(2, 100000)
        wb.save(FilePath)
        wb.close()

        sg.popup_ok(f'You deleted the elements of {sheet_name}')
        question_input("input")
    except PermissionError:
        sg.popup_error("Close the selected_tpns.xlsx")
        pass

def really_wanna_delete_tpnb(sheet_name):

    if sheet_name:
        while True:
            event, values = sg.Window(f'Open Input Table?',
                                      [[sg.Stretch(), sg.Text(f'Do you really want to delete elements from {sheet_name}?'), sg.Stretch()],
                                       [sg.Button('Yes', s=12, key='Yes'), sg.Button('No', s=12, key="No")]],
                                      modal=True).read(close=True)
            if event == 'Yes':
                clear_input_sheets(sheet_name)
                break
            if event == 'No':
                break
    if not sheet_name:
        sg.popup_error("Please select a type ('tpnb', etc...)")


def data_to_input_file(data, sheet_name):

    try:

        FilePath = Path(os.getcwd()) / 'selected_tpns.xlsx'


        if sheet_name == 'country_tpnb':
            # data = data[['country', 'tpnb', 'division', 'dep', 'pmg', 'product_name', "sold_units"]].drop_duplicates()
            data = data.groupby(['country', 'tpnb', 'division', 'pmg', 'product_name' ], observed=True)["sold_units"].sum().reset_index()
            data["sold_units"] = data["sold_units"].astype("int64")
            data = data.sort_values(by=['sold_units'],ascending=False)
            with pd.ExcelWriter(FilePath, mode='a', engine='openpyxl', if_sheet_exists='overlay') as writer:
                startrow = writer.sheets[sheet_name].max_row
                data.to_excel(writer, sheet_name=sheet_name , index=False, header=False, startrow = startrow)

            data_to_show = pd.read_excel(FilePath, sheet_name=sheet_name)
            country_data = data_to_show.country.unique()
            tpnb_data= data_to_show.tpnb.unique()
            sg.popup_ok(f'input file contains: \n\n {data_to_show[["country", "tpnb"]]}')

        if sheet_name == 'store_tpnb':
            data = data[['store', 'tpnb', 'division',  'pmg', 'product_name', 'sold_units']].drop_duplicates()
            data = data.sort_values(by=['sold_units'],ascending=False)
            with pd.ExcelWriter(FilePath, mode='a', engine='openpyxl', if_sheet_exists='overlay') as writer:
                startrow = writer.sheets[sheet_name].max_row
                data.to_excel(writer, sheet_name=sheet_name, index=False, header=False, startrow=startrow)

            data_to_show = pd.read_excel(FilePath, sheet_name=sheet_name)
            store_data = data_to_show.store.unique()
            tpnb_data = data_to_show.tpnb.unique()

            sg.popup_ok(f'input file contains: \n\n {data_to_show[["store", "tpnb"]]}')
    except PermissionError:
        sg.popup_error("Close the selected_tpns.xlsx")
        pass

def question_input(what_do_you_want_opening):

    if what_do_you_want_opening == "input":

        while True:
            event, values = sg.Window(f'Open Input Table?',
                                      [[sg.Stretch(), sg.Text('Do you want to open input table?'), sg.Stretch()],
                                       [sg.Button('Yes', s=12, key='Yes'), sg.Button('No', s=12, key="No")]],
                                      modal=True).read(close=True)
            if event == 'Yes':
                    os.system(f"start EXCEL.EXE selected_tpns.xlsx")
                    break
            if event == 'No':
                break

def question_export_model(what_do_you_want_opening, new_version_name_as_is):
    if what_do_you_want_opening == "export":
        while True:
            event, values = sg.Window(f'Open Exported Table?',
                                      [[sg.Stretch(), sg.Text('Do you want to open exported table?'), sg.Stretch()],
                                       [sg.Button('Yes', s=12, key='Yes'), sg.Button('No', s=12, key="No")]],
                                      modal=True).read(close=True)
            if event == 'Yes':
                    os.system(f"start EXCEL.EXE outputs/ReplTypeChanger_outputs/AS_is_{new_version_name_as_is}.xlsx")
                    break
            if event == 'No':
                break

    if what_do_you_want_opening == "model":
        while True:
            event, values = sg.Window(f'Open model DIFF Table?',
                                      [[sg.Stretch(), sg.Text('Do you want to open Model DIFF table?'), sg.Stretch()],
                                       [sg.Button('Yes', s=12, key='Yes'), sg.Button('No', s=12, key="No")]],
                                      modal=True).read(close=True)
            if event == 'Yes':

                FilePath = Path(os.getcwd()) / 'selected_tpns.xlsx'


                selected_countries_list= pd.read_excel(FilePath, sheet_name='country_tpnb')


                for x in selected_countries_list['country'].unique().tolist():
                    output_xlsx = f"outputs/ReplTypeChanger_outputs/ReplTypeChanger_output_{new_version_name_as_is}_{x}.xlsx"
                    if os.path.isfile(output_xlsx):
                        os.system(f"start EXCEL.EXE {output_xlsx}")

                break
            if event == 'No':
                break

    if what_do_you_want_opening == "costbase":
        while True:
            event, values = sg.Window(f'Open CostBase Table?',
                                      [[sg.Stretch(), sg.Text('Do you want to open CostBase table?'), sg.Stretch()],
                                       [sg.Button('Yes', s=12, key='Yes'), sg.Button('No', s=12, key="No")]],
                                      modal=True).read(close=True)
            if event == 'Yes':
                    os.system(f"start EXCEL.EXE outputs/ReplTypeChanger_outputs/CostBase_TPNB_{new_version_name_as_is}.xlsx")
                    break
            if event == 'No':
                break




def show_table_part2(df,  as_is_data_nsrp_stores,  format_ind):



    # Uses the first row (which should be column names) as columns names
    header_list = list(df.columns)
    # Drops the first row in the table (otherwise the header names and the first row will be the same)
    data = df[0:].values.tolist()

    if format_ind == 'Y':


        table_frame=[[sg.Frame('New File Name:',[[sg.Input(key='saved_name_as_is',enable_events=True, default_text='Here add a saving name', size=(25,1), expand_x=True)],
                                               [sg.Button("Export_to_excel", key= "export", button_color="lightblue",disabled=True)]], element_justification="center")
                         ,sg.Frame("Settings:",[

                            [sg.Frame("Country:",[[sg.Button("CE",key="ce", focus=True, button_color="lightblue",disabled=True),
                                                                      sg.Button("HU", key="hu", button_color="lightblue",disabled=True),
                                                                      sg.Button("CZ", key="cz", button_color="lightblue",disabled=True),
                                                                      sg.Button("SK", key="sk", button_color="lightblue",disabled=True)]], element_justification="left"),
                            sg.Frame("Format:", [
                                                     [sg.Button("All", key="all", focus=True, button_color="lightblue",disabled=True),
                                                      sg.Button("Hypermarket", key="hip", button_color="lightblue",disabled=True),
                                                      sg.Button("Compact", key="comp", button_color="lightblue",disabled=True),
                                                      sg.Button("1K", key="1k", button_color="lightblue",disabled=True),
                                                      sg.Button("Express", key="exp", button_color="lightblue",disabled=True)]],
                                                                                  element_justification="left"),
                    sg.Frame('Replenishment Types:',
                             [[sg.Radio('all_types', 'group 4', key='all_types', enable_events=True,disabled=True)
                                 ,sg.Radio('srp', 'group 4', key='rt_srp', enable_events=True,disabled=True),
                               sg.Radio('nsrp', 'group 4', key='rt_nsrp', enable_events=True,disabled=True)],
                              [sg.Radio('full_pallet', 'group 4', key='rt_full_pallet', enable_events=True,disabled=True),
                               sg.Radio('mu', 'group 4', key='rt_mu', enable_events=True,disabled=True),
                               sg.Radio('split_pallet', 'group 4', key='rt_split_pallet', enable_events=True,disabled=True)]]

                , element_justification= "center"
                           ),
                                                sg.Col([[
                                                sg.Button("Show me where are more repl types", bind_return_key=True, button_color="lightblue",disabled=True)],
                        [sg.Button("Show me the stores", enable_events=True, key="show_me_stores", button_color="lightblue", disabled=True)],
                                                ]),

                                                ]],expand_x=True, element_justification="center") ],

            [sg.Table(values=data,
                      key="table",
                      enable_events=True,
                      headings=header_list,
                      pad=(5, 5),
                      display_row_numbers=False,
                      auto_size_columns=True,
                      select_mode=sg.TABLE_SELECT_MODE_BROWSE,
                      justification="center",
                      vertical_scroll_only=False,
                      num_rows=min(20, len(data)),expand_x=True, expand_y=True)],

                     [sg.Col([[sg.Text("", size=(50, 1), expand_x=True, key='-Position0-')],
                              [sg.Text("", size=(50, 1), expand_x=True, key='-Position-')],
                                [sg.Text("", size=(50, 1), expand_x=True, key='-Position2-')]]),
                      sg.Col(
                          [[sg.Button("clear sheet of Excel", key="clear_sheet", enable_events=True,
                                      button_color="lightblue", disabled=True, auto_size_button=True),
                            sg.Button("Add selected TPNB to Excel", key="add_tpnb_country",
                                      enable_events=True, button_color="lightblue", disabled=True,
                                      auto_size_button=True),
                            sg.Button("Add full data to Excel", key="add_tpnb_country_full",
                                      enable_events=True, button_color="lightblue", disabled=True,
                                      auto_size_button=True),
                            sg.Button("Open Input Table!", key="open_input", enable_events=True,
                                      button_color="lightblue", disabled=True, auto_size_button=True)]],
                          element_justification="right")]]

    if format_ind == 'N':
        table_frame = [[sg.Frame('New File Name:', [[sg.Input(key='saved_name_as_is', enable_events=True,
                                                              default_text='Here add a saving name', size=(25, 1),
                                                              expand_x=True)],
                                                    [sg.Button("Export_to_excel", key="export",
                                                               button_color="lightblue",disabled=True)]],
                                 element_justification="center")
                           , sg.Frame("Settings:", [
                [sg.Frame("Country:", [[sg.Button("CE", key="ce", focus=True, button_color="lightblue", disabled=True),
                                        sg.Button("HU", key="hu", button_color="lightblue",disabled=True),
                                        sg.Button("CZ", key="cz", button_color="lightblue",disabled=True),
                                        sg.Button("SK", key="sk", button_color="lightblue",disabled=True)]],
                          element_justification="left"),
                 sg.Frame("Format:", [
                     [sg.Button("All", key="all", focus=True, button_color="lightblue",disabled=True),
                      sg.Button("Hypermarket", key="hip", button_color="lightblue",disabled=True),
                      sg.Button("Compact", key="comp", button_color="lightblue",disabled=True),
                      sg.Button("1K", key="1k", button_color="lightblue",disabled=True),
                      sg.Button("Express", key="exp", button_color="lightblue",disabled=True)]],
                          element_justification="left", visible=False),
                 sg.Frame('Replenishment Types:',
                          [[sg.Radio('all_types', 'group 4', key='all_types', enable_events=True,disabled=True)
                               , sg.Radio('srp', 'group 4', key='rt_srp', enable_events=True,disabled=True),
                            sg.Radio('nsrp', 'group 4', key='rt_nsrp', enable_events=True,disabled=True)],
                           [sg.Radio('full_pallet', 'group 4', key='rt_full_pallet', enable_events=True,disabled=True),
                            sg.Radio('mu', 'group 4', key='rt_mu', enable_events=True,disabled=True),
                            sg.Radio('split_pallet', 'group 4', key='rt_split_pallet', enable_events=True,disabled=True)]]

                          , element_justification="center"
                          ),
                 sg.Col([[
                     sg.Button("Show me where are more repl types", bind_return_key=True, button_color="lightblue",disabled=True)],
                     [sg.Button("Show me the stores", enable_events=True, key="show_me_stores",
                                button_color="lightblue", disabled=True),
                      sg.Button("Sold_Unit_DESC", enable_events=True, key="Sold_Unit_DESC",
                                button_color="lightblue", disabled=True)
                      ]]),

                 ]], expand_x=True, element_justification="center")],

                       [sg.Table(values=data,
                                 key="table",
                                 enable_events=True,
                                 headings=header_list,
                                 pad=(5, 5),
                                 display_row_numbers=False,
                                 auto_size_columns=True,
                                 select_mode=sg.TABLE_SELECT_MODE_BROWSE,
                                 justification="center",
                                 vertical_scroll_only=False,
                                 num_rows=min(20, len(data)), expand_x=True, expand_y=True)],
                       [sg.Col([[sg.Text("", size=(50, 1), expand_x=True, key='-Position0-')],
                                [sg.Text("", size=(50, 1), expand_x=True, key='-Position-')],
                                [sg.Text("", size=(50, 1), expand_x=True, key='-Position2-')]]),
                        sg.Col(
                            [[sg.Button("clear sheet of Excel", key="clear_sheet", enable_events=True,
                                        button_color="lightblue", disabled=True, auto_size_button=True),
                              sg.Button("Add selected TPNB to Excel", key="add_tpnb_country",
                                        enable_events=True, button_color="lightblue", disabled=True, auto_size_button=True),
                              sg.Button("Add full data to Excel", key="add_tpnb_country_full",
                                        enable_events=True, button_color="lightblue", disabled=True, auto_size_button=True),
                              sg.Button("Open Input Table!", key="open_input", enable_events=True,
                                        button_color="lightblue", disabled=True, auto_size_button=True)]],
                            element_justification="right")]]




    layout = [[sg.Column(table_frame, vertical_alignment='center') ]]
    window = sg.Window("Repl Model data", layout, grab_anywhere=True, finalize=True, resizable=True)
    window['saved_name_as_is'].update(select=True)
    new_version_name_as_is = "default"
    countries = ['CZ','SK','HU']
    selected_format = ['Hypermarket', 'Compact', 'Express', '1K']
    if format_ind == 'N':
        df_filtered = df[df['country'].isin(countries)]
    if format_ind == 'Y':
        df_filtered = df[df['country'].isin(countries) & df['format'].isin(selected_format)]
    selected_tpn_list = []
    selected_country = []
    df_filtered_rt = None


    while True:  # The Event Loop
        event, values = window.read()
        if event == sg.WIN_CLOSED:
            break
        if event == "saved_name_as_is":
            new_version_name_as_is = values["saved_name_as_is"]
        if event == "export":
            if df_filtered_rt is None:
                df_filtered.to_excel(Path(os.getcwd()) / f'outputs/ReplTypeChanger_outputs/AS_is_{new_version_name_as_is}.xlsx', index=False)
            if df_filtered_rt is not None:
                df_filtered_rt.to_excel(Path(os.getcwd()) / f'outputs/ReplTypeChanger_outputs/AS_is_{new_version_name_as_is}.xlsx',
                                     index=False)

            question_export_model('export', new_version_name_as_is)

        if event == "ce":
            countries = ['CZ','SK','HU']
            df_filtered = df[df['country'].isin(countries)]
            window['table'].update( values=df_filtered[0:].values.tolist())
            df_filtered_rt = None
        if event == "hu":
            countries = ['HU']
            df_filtered = df[df['country'].isin(countries)]
            window['table'].update(values=df_filtered[0:].values.tolist())

            df_filtered_rt = None
        if event == "cz":
            countries = ['CZ']
            df_filtered = df[df['country'].isin(countries)]
            window['table'].update(values=df_filtered[0:].values.tolist())

            df_filtered_rt = None
        if event == "sk":
            countries = ['SK']
            df_filtered = df[df['country'].isin(countries)]
            window['table'].update(values=df_filtered[0:].values.tolist())
            df_filtered_rt = None

        if event == "all":
            format = ['Hypermarket', 'Compact', 'Express', '1K']
            df_filtered = df[(df['format'].isin(format)) & df['country'].isin(countries)]
            window['table'].update(values=df_filtered[0:].values.tolist())
            df_filtered_rt = None
        if event == "hip":
            format = ['Hypermarket']
            df_filtered = df[(df['format'].isin(format)) & df['country'].isin(countries)]
            window['table'].update(values=df_filtered[0:].values.tolist())
            df_filtered_rt = None
        if event == "comp":
            format = ['Compact']
            df_filtered = df[(df['format'].isin(format)) & df['country'].isin(countries)]
            window['table'].update(values=df_filtered[0:].values.tolist())
            df_filtered_rt = None
        if event == "1k":
            format = ['1K']
            df_filtered = df[(df['format'].isin(format)) & df['country'].isin(countries)]
            window['table'].update(values=df_filtered[0:].values.tolist())
            df_filtered_rt = None
        if event == "exp":
            format = ['Express']
            df_filtered = df[(df['format'].isin(format)) & df['country'].isin(countries)]
            window['table'].update(values=df_filtered[0:].values.tolist())
            df_filtered_rt = None


        if event.startswith("Show me where"):
            if df_filtered_rt is  None:
                srp_nsrp = ((df_filtered.srp.str.replace('%', '').astype(int) > 0) & (df_filtered.nsrp.str.replace('%', '').astype(int) > 0))
                srp_full_pallet = ((df_filtered.srp.str.replace('%', '').astype(int) > 0) & (df_filtered.full_pallet.str.replace('%', '').astype(int) > 0))
                srp_mu = ((df_filtered.srp.str.replace('%', '').astype(int) > 0) & (df_filtered.mu.str.replace('%', '').astype(int) > 0))
                nsrp_full_pallet = ((df_filtered.nsrp.str.replace('%', '').astype(int) > 0) & (df_filtered.full_pallet.str.replace('%', '').astype(int) > 0))
                nsrp_mu = ((df_filtered.nsrp.str.replace('%', '').astype(int) > 0) & (df_filtered.mu.str.replace('%', '').astype(int) > 0))
                full_pallet_mu = ((df_filtered.full_pallet.str.replace('%', '').astype(int) > 0) & (df_filtered.mu.str.replace('%', '').astype(int) > 0))

                df_filtered = df_filtered.loc[
                    srp_nsrp | srp_full_pallet | srp_mu | nsrp_full_pallet | nsrp_mu | full_pallet_mu
                ].sort_values(by=['nsrp', 'full_pallet'], ascending=False)

                window['table'].update(values=df_filtered[0:].values.tolist())
            if df_filtered_rt is not None:
                srp_nsrp = ((df_filtered_rt.srp.str.replace('%', '').astype(int) > 0) & (df_filtered_rt.nsrp.str.replace('%', '').astype(int) > 0))
                srp_full_pallet = ((df_filtered_rt.srp.str.replace('%', '').astype(int) > 0) & (df_filtered_rt.full_pallet.str.replace('%', '').astype(int) > 0))
                srp_mu = ((df_filtered_rt.srp.str.replace('%', '').astype(int) > 0) & (df_filtered_rt.mu.str.replace('%', '').astype(int) > 0))
                nsrp_full_pallet = ((df_filtered_rt.nsrp.str.replace('%', '').astype(int) > 0) & (df_filtered_rt.full_pallet.str.replace('%', '').astype(int) > 0))
                nsrp_mu = ((df_filtered_rt.nsrp.str.replace('%', '').astype(int) > 0) & (df_filtered_rt.mu.str.replace('%', '').astype(int) > 0))
                full_pallet_mu = ((df_filtered_rt.full_pallet.str.replace('%', '').astype(int) > 0) & (df_filtered_rt.mu.str.replace('%', '').astype(int) > 0))

                df_filtered_rt = df_filtered_rt.loc[
                    srp_nsrp | srp_full_pallet | srp_mu | nsrp_full_pallet | nsrp_mu | full_pallet_mu
                    ].sort_values(by=['nsrp', 'full_pallet'], ascending=False)

                window['table'].update(values=df_filtered_rt[0:].values.tolist())


        if event == "clear_sheet":
            really_wanna_delete_tpnb("country_tpnb")

        if event == "add_tpnb_country":
            try:
                if df_filtered_rt is None:
                    data_to_input_file(df_filtered.loc[(df_filtered.country.isin([selected_country]))& (df_filtered.tpnb.isin([selected_tpn]))], "country_tpnb")
                if df_filtered_rt is not None:
                    data_to_input_file(df_filtered_rt.loc[(df_filtered_rt.country.isin([selected_country]))& (df_filtered_rt.tpnb.isin([selected_tpn]))], "country_tpnb")
            except UnboundLocalError:
                sg.popup_error("Please select a row!")
                pass

        if event == "add_tpnb_country_full":
            if df_filtered_rt is None:
                df_filtered = df_filtered[['country', 'tpnb', 'division', 'pmg', 'product_name', "sold_units"]].drop_duplicates()
                data_to_input_file(df_filtered, "country_tpnb")
            if df_filtered_rt is not None:
                df_filtered_rt = df_filtered_rt[['country', 'tpnb', 'division','pmg', 'product_name','sold_units']].drop_duplicates()
                data_to_input_file(df_filtered_rt, "country_tpnb")

        if event == "open_input":
            os.system(f"start EXCEL.EXE selected_tpns.xlsx")

        if event == "table":
            a = [row for row in values[event]]
            index = a[0]
            if df_filtered_rt is None:
                #print(df_filtered.head())
                selected_tpn = df_filtered.iloc[index][['tpnb','product_name', 'country']][0]
                selected_tpn_list = selected_tpn.tolist()
                selected_product = df_filtered.iloc[index ][['tpnb', 'product_name','country']][1]
                selected_country = df_filtered.iloc[index ][['tpnb', 'product_name','country']][2]
                #print(df_filtered.head())
                window['-Position0-'].update(f'Number of TPNB: {len(df_filtered.product_name.unique())} Volume: {df_filtered.sold_units.sum()}')

                try:
                    selected_format = df_filtered.iloc[index][['tpnb', 'product_name', 'country', 'format']][3]
                except:
                    selected_format = ['Hypermarket', 'Compact', 'Express', '1K']
            if df_filtered_rt is not None:
                try:
                    selected_tpn = df_filtered_rt.iloc[index][['tpnb', 'product_name', 'country']][0]
                    selected_tpn_list = selected_tpn.tolist()
                    selected_product = df_filtered_rt.iloc[index][['tpnb', 'product_name', 'country']][1]
                    selected_country = df_filtered_rt.iloc[index][['tpnb', 'product_name', 'country']][2]
                    #print(df_filtered_rt.head())
                    window['-Position0-'].update(f'Number of TPNBs: {len(df_filtered_rt.product_name.unique())} Volume: {df_filtered_rt.sold_units.sum()}')
                    try:
                        selected_format = df_filtered_rt.iloc[index][['tpnb', 'product_name', 'country', 'format']][3]
                    except:
                        selected_format = ['Hypermarket', 'Compact', 'Express', '1K']
                except IndexError:
                    pass

            window['-Position-'].update(f'Selected TPNB is: {selected_tpn}')
            window['-Position2-'].update(f'Selected Product is: {selected_product}')

        if selected_tpn_list > 0:
            for x in ["show_me_stores","ce","cz","sk","hu",
                      'all','hip','comp','1k','exp','all_types',
                      'rt_srp',"rt_nsrp","rt_full_pallet", "rt_mu", "rt_split_pallet",
                      "Show me where are more repl types","export", "clear_sheet",
                      "add_tpnb_country","add_tpnb_country_full","open_input", ]:
                window[x].update(disabled=False)
        if event == "show_me_stores":

            if format_ind == 'Y':
                nsrp_stores = as_is_data_nsrp_stores[(as_is_data_nsrp_stores.tpnb.isin([selected_tpn_list]))
                                                     & (as_is_data_nsrp_stores.country.isin([selected_country]))
                                                    & (as_is_data_nsrp_stores.format.isin([selected_format]))].sort_values(by=['store'])
            if format_ind == 'N':
                nsrp_stores = as_is_data_nsrp_stores[(as_is_data_nsrp_stores.tpnb.isin([selected_tpn_list]))
                                                     & (as_is_data_nsrp_stores.country.isin([selected_country]))
                                                    ].sort_values(by=['store'])


            if len(nsrp_stores.country.unique()) > 0:
                table_for_showing_stores(nsrp_stores)

            if len(nsrp_stores.country.unique()) == 0:
                sg.popup_error("Please select a TPNB first!")

        if event == 'all_types':
            window['table'].update(values=df_filtered[0:].values.tolist())
        if event == "rt_srp":
            df_filtered_rt = df_filtered[(df_filtered['srp'].str.replace('%', '').astype(int) > 0)].sort_values(by=['srp'], ascending=False)
            window['table'].update(values=df_filtered_rt[0:].values.tolist())
            window["Sold_Unit_DESC"].update(disabled=False)
        if event == "rt_nsrp":
            df_filtered_rt = df_filtered[(df_filtered['nsrp'].str.replace('%', '').astype(int) > 0) ].sort_values(by=['nsrp'], ascending=False)
            window['table'].update(values=df_filtered_rt[0:].values.tolist())
            window["Sold_Unit_DESC"].update(disabled=False)
        if event == "rt_full_pallet":
            df_filtered_rt = df_filtered[(df_filtered['full_pallet'].str.replace('%', '').astype(int) > 0)].sort_values(by=['full_pallet'], ascending=False)
            window['table'].update(values=df_filtered_rt[0:].values.tolist())
            window["Sold_Unit_DESC"].update(disabled=False)
        if event == "rt_mu":
            df_filtered_rt = df_filtered[(df_filtered['mu'].str.replace('%', '').astype(int) > 0)].sort_values(by=['mu'], ascending=False)
            window['table'].update( values=df_filtered_rt[0:].values.tolist())
            window["Sold_Unit_DESC"].update(disabled=False)
        if event == "rt_split_pallet":
            df_filtered_rt = df_filtered[(df_filtered['split_pallet'].str.replace('%', '').astype(int) > 0)].sort_values(by=['split_pallet'], ascending=False)
            window['table'].update( values=df_filtered_rt[0:].values.tolist())
            window["Sold_Unit_DESC"].update(disabled=False)

        if event == "Sold_Unit_DESC":
            df_filtered_rt = df_filtered_rt.sort_values(by=['sold_units'], ascending=False)
            window['table'].update( values=df_filtered_rt[0:].values.tolist())

        # if df_filtered_rt is not None:
        #     new_output = []
        #     for line in df_filtered_rt['product_name'].values.tolist():
        #         if values['-FILTER-'] in line.lower():
        #             new_output.append(line)
        #     window['table'].update(values=df_filtered_rt['product_name'].isin(new_output).values.tolist())
        # if df_filtered_rt is None:
        #     new_output = []
        #     for line in df_filtered['product_name'].values.tolist():
        #         if values['-FILTER-'] in line.lower():
        #             new_output.append(line)
        #     window['table'].update(values=df_filtered['product_name'].isin(new_output).values.tolist())

    window.close()

def get_pmg_selector(pmg_list):
    frame_listbox = [[sg.Listbox(values=pmg_list
                                 , key='listbox', size=(30, 6), visible=True, bind_return_key=True,
                                 enable_events=True,
                                 select_mode=sg.LISTBOX_SELECT_MODE_EXTENDED),
                      sg.Button("Select", key='select_pmg', button_color="lightblue")
                      ],
                     [sg.Listbox(values=[], key='selected_listbox', size=(30, 6), visible=False, bind_return_key=True),
                      sg.Button("Remove", key='remove_pmg', visible=False, button_color="lightblue")]]

    part_2_layout_b = [sg.Frame("Pmg-selector:", layout=frame_listbox, visible=True, key="frame_listbox",
                                 element_justification="center")]

    layout = [part_2_layout_b]
    window = sg.Window('Pmg_Selector', layout, grab_anywhere=True, finalize=True)

    while True:  # The Event Loop
        event, values = window.read()
        if event == sg.WIN_CLOSED:
            break



    window.close()




def run_model(window, new_version_name, for_what_ifs : cm.What_Ifs_Inputs, repl_dataset_f):

    # to TPN level
    change_repl_type = True
    tpnb_store = False
    tpnb_country = True

    if for_what_ifs.sheet_name == "country_tpnb":
        tpnb_store = False
        tpnb_country = True

    if for_what_ifs.sheet_name == "store_tpnb":
        tpnb_store = True
        tpnb_country = False


    Cost_Base = False

    model_true_false = cm.ModelTrueFalse(None,
                                          None,
                                          None,tpnb_store, tpnb_country,
                                          None, None, None,
                                          None, None,
                                          None, None, None, change_repl_type, None, None, Cost_Base, None, None, None)

    act_version_name = 'Q1_verz1_jav'
    directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd())
    excel_inputs_f = 'inputs/Repl/Stores_Inputs_2024_Q1_wo_prepacked_hours_fluct_cust_repaired.xlsx'
    losses_f = 'inputs/files_for_dataset/Dataset_24/losses_2023w14_27_newer'
    most_f = 'inputs\MOST\Repl\MOST_Repl_24_Q1_v3.xlsb'
    selected_tpn = 'selected_tpns.xlsx'

    act_model_outputs = f"outputs/model_outputs/{act_version_name}/OPB_DEP_{act_version_name}.xlsx"
    act_model_insights = f"outputs/model_outputs/{act_version_name}/INSIGHT_{act_version_name}.parquet.gz"
    act_model_cost_base = f"inputs/model_outputs/{act_version_name}/ReplCostBase_{act_version_name}"

    stores = list(pd.read_excel(directory / excel_inputs_f, usecols=['Store'])['Store'].unique())

    data_paths = cm.Data_Paths(
        directory,
        repl_dataset_f,
        None,
        None,
        None,
        None,
        None,
        None,
        excel_inputs_f,
        None,
        losses_f,
        most_f,
        act_model_outputs,
        act_model_insights,
        selected_tpn,
        stores,
        None,
        None,
        None,
        None,
        None,
        None,
        act_model_cost_base,
        None,
        None,
        None
    )


    try:
        summary = rm.Replenishment_Model_Running(model_true_false, act_version_name, new_version_name, data_paths,
                                                         None, for_what_ifs)
    except PermissionError as p:
        sg.popup_error(" Close the Excel File in order to be able overwritten...... and start it over!")
    except MemoryError as m:
        sg.popup_error(f"{m} Close the program and try selecting less data or set free memory on your computer!")


    if mainthread_popup_queue:
        mainthread_popup_queue.put(new_version_name)

def process_popup(TPN_Cost_Base):
    try:
        new_version_name = mainthread_popup_queue.get_nowait()
        if TPN_Cost_Base == True:
            question_export_model("costbase", new_version_name)
        else:
            question_export_model("model", new_version_name)
    except queue.Empty:     # get_nowait() will get exception when Queue is empty
        pass

# Class holding the button graphic info. At this time only the state is kept
class BtnInfo:
    def __init__(self, state=True):
        self.state = state        # Can have 3 states - True, False, None (disabled)


# def save_settings(settings):
#     with open('settings.json', 'w') as f:
#         json.dump(settings, f)


def save_setting(setting_key, setting_value):
    settings = load_settings()
    settings[setting_key] = setting_value
    with open('settings.json', 'w') as f:
        json.dump(settings, f)

def load_settings():
    try:
        with open('settings.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}


def gui():

    settings = load_settings()

    global mainthread_popup_queue
    mainthread_popup_queue = queue.Queue()
    image_piechart2 = 'icons/Icons/pie-chart.png'
    image_settings = 'icons/Icons/settings.png'
    image_toggle_on = 'icons/toggle-left.png'
    image_toggle_off = 'icons/toggle-right_disabled.png'
    image_left = 'icons/arrow-left-circle.png'
    image_right = 'icons/arrow-right-circle.png'
    image_select_act_output = 'icons/database.png'
    taskbar_icon = 'icons/box.ico'

    repl_dataset_f =  settings.get('selected_setting', '')#'inputs/Repl_Dataset_2024'


    sg.theme('DarkBlue')
    sg.set_options(font=('Impact ', 11)) #, button_color="lightblue"
    input = "selected_tpns.xlsx"

    def get_last_part(input_string):
        parts = input_string.split('/')
        return parts[-1]

    frame_layout_col_1_inside_col=[[sg.Radio('TPNB', 'group 1', key='sheet_tpnb', enable_events=True, default=True, visible=False) ],
                                   [sg.Radio('COUNTRY + TPNB', 'group 1', key='sheet_country_tpnb', enable_events=True,default=True)],
                                    [sg.Radio('STORE + TPNB', 'group 1', key='sheet_store_tpnb',default=False, enable_events=True, disabled=True) ]
            ]

    frame_layout_col_1 = [[sg.Column(frame_layout_col_1_inside_col),sg.Button("Open Input Table!", key="fillup",enable_events=True, button_color="lightblue")],
            [sg.Button("Show me 'Prod Model' data!!", key="open", tooltip="Show As Is Data", button_color="lightblue", expand_x=True)]]

    col1 = [
            [sg.Frame("Which Type You'd Like To Run?", frame_layout_col_1, expand_x=True)]
            ]


    other_options_checkboxes = [[sg.Checkbox("sales",key="mod_sales"),
                                 sg.Checkbox("case capacity", key="mode_casecap")]]
    other_options_frame=[sg.Frame('Other options to change:',other_options_checkboxes )]

    a = [sg.Frame('New File Name:',[[sg.Input(key='saved_name',enable_events=True, default_text='Here add a saving name', size=(25,1), expand_x=True),
                                                     ]]), ]
    settings_frame = sg.Frame("Settings to 'What ifs':",
                              [[sg.T("Saving Name:"),sg.Input(key='saved_name',enable_events=True,
                                         default_text='Here add a saving name', size=(25,1), expand_x=True)]
                                  ,[sg.T("Settings:"),sg.Push(),sg.Button(  key="options_to_settings",
                       image_filename=image_settings,image_size=(40, 40), image_subsample=3,  auto_size_button = True,
                       border_width=0, button_color=(sg.theme_background_color(),sg.theme_background_color())),
                                    sg.Button(key="set_dataset",
                                              image_filename=image_select_act_output, image_size=(40, 40), image_subsample=3,
                                              auto_size_button=True,
                                              border_width=0,
                                              button_color=(sg.theme_background_color(), sg.theme_background_color())),
                                    sg.Push() ],
                              [sg.T("Dataset: "),sg.Push(), sg.Text(f"'{get_last_part(repl_dataset_f)}'", font=("Helvetica", 12, "bold"), auto_size_text=True, key='dataset_text'), sg.Push()]])


    col2 = [[sg.Column([
            [settings_frame]]),
             sg.Column([[sg.Button("Run", key = "run", tooltip="Run the Model", button_color="lightblue", expand_x=True, expand_y=True, pad=(0,0)),
              ]], element_justification='center', expand_y=True, expand_x=True)],
        [sg.Frame("Console:", [[sg.MLine(size=(70, 8), k='-ML-',auto_size_text=True, reroute_stdout=True,
                  reroute_stderr=True,do_not_clear=False, write_only=True, autoscroll=True,auto_refresh=True)]], pad=(0,0))]]

    ####################PART2########################
    frame_layout_part2_pmg_tpn =  [[sg.Radio('Pmg', 'group 4', key='pmg_level', enable_events=True ),
                                   sg.Radio('Tpnb', 'group 4', key='tpnb_level', enable_events=True )],
                                   [sg.Radio('Division', 'group 4', key='division_level', enable_events=True ),
                                    sg.Radio('Department', 'group 4', key='dep_level', enable_events=True)],
                                    [sg.Radio('Section', 'group 4', key='sec_level', enable_events=True),
                                    sg.Radio('Group', 'group 4', key='group_level', enable_events=True),
                                    sg.Radio('sub-Group', 'group 4', key='sgroup_level', enable_events=True)
                                    ]]
    part_2_pmg_tpn = [sg.Frame('Options:',frame_layout_part2_pmg_tpn, visible=False)]

    frame_layout_part2_a = [[sg.Radio('Grocery', 'group 3', key='gro', enable_events=True, default=True, tooltip='BeerWineSpirit, DRY, Health&Beauty'),
                                sg.Radio('Prepacked Fresh', 'group 3', key='ppd',tooltip='PPD-FRZ-DAI', enable_events=True),
                             sg.Radio('GM', 'group 3', key='gm', enable_events=True, tooltip="General Merchandise"),
                             sg.Radio('CatRes', 'group 3', key='catres', enable_events=True, tooltip="Category Reset Grouping")],
                            part_2_pmg_tpn

                ]



    part_2_layout_a = [[sg.Frame('Departments:',frame_layout_part2_a, expand_y=True) ],
             [sg.Button("Show me 'Prod Model' data!!", key="open_as_is_search", tooltip="Show As Is Data", button_color="lightblue", expand_x=True, expand_y=True, size=(30,5))],
                       [sg.Column([[sg.ReadFormButton('', key='piecharts_countries',
                                         image_filename=image_piechart2, image_size=(100, 100), image_subsample=2,auto_size_button = True, tooltip="PieChart for the Countries!",
                                         border_width=0, button_color=(sg.theme_background_color(),sg.theme_background_color()))]], element_justification='center', justification='center')]]



    frame_listbox = [[sg.Button("Select", key = 'select_pmg', button_color="lightblue"),sg.Listbox(values=["  -------- CLICK ON ONE OF DEPARTMENTS!! --------"],
                   key='listbox', size=(50, 6), visible = True, bind_return_key = True,
                        select_mode=sg.LISTBOX_SELECT_MODE_EXTENDED,
                      horizontal_scroll = True )
                      ],[sg.Button("Remove", key = 'remove_pmg', visible=True , button_color="lightblue"  ),
                     sg.Listbox(values=[], key='selected_listbox', size=(50, 4), visible=True, bind_return_key=True,
                                select_mode=sg.LISTBOX_SELECT_MODE_EXTENDED, horizontal_scroll = True)
                       ],
                     [sg.Button("Remove all", key = 'remove_all_pmg', visible=True , button_color="lightblue" )]
                     ]

    part_2_tpnb = [[sg.Text('Search By Tpnb:', )],
                    [sg.Input('Here Add Tpnb!', size=(20, 1),  enable_events=True, key='tpnb_input')]]

    div_frame_listbox = [[sg.Listbox(values=[]
                 ,  key='div_listbox', size=(50, 6), visible = False, bind_return_key = True, horizontal_scroll = True),sg.Button("Select", key = 'select_pmg')
                      ],
                     [sg.Listbox(values=[], key='div_selected_listbox', size=(50, 6), visible=False, bind_return_key=True, horizontal_scroll = True),
                      sg.Button("Remove", key = 'div_remove_pmg', visible=False  )
                      ],
                     [sg.Button("Remove all", key = 'div_remove_all_pmg', visible=False  )]
                     ]

    part_2_layout_b = [[sg.Col([[sg.Frame("Pmg-selector:",layout = frame_listbox, visible=True, key = "frame_listbox", element_justification="center")],
                       [sg.Frame("Tpnb-selector:",layout = part_2_tpnb, visible=False, key = "frame_tpnb", element_justification="center")]]),
                        sg.Col([[sg.Checkbox('Format\nlevel?', key='format_level', tooltip='You can see it on format level (Hyper, Compact....)')],
                                [sg.Checkbox('Ownbrand', key='only_ownbrand', tooltip='You can see Ownbrand products only')]],element_justification='left')
                        ]]
    part_2_layout_b_division = [[sg.Frame("Division-selector:",layout = div_frame_listbox, visible=False, key = "div_frame_listbox", element_justification="center")]]


    tab_1 = [
              [sg.Column(col1, key = 'col1'), sg.VSeparator(key = 'sep1'), sg.Column(col2, key = 'col2', expand_y=True)]]
    pmg_tab = [
              [sg.Column(part_2_layout_a, key = 'part2_col1'),sg.VSeparator(key = 'sep2'),
              sg.Column(part_2_layout_b, key = 'part2_col2', visible=True)]]

    hier_tab = [[sg.T("")]]

    pmg_hierarchy_tabs = [[sg.TabGroup([[
        sg.Tab('', pmg_tab, image_source=image_left, tooltip="PMG level"),
        sg.Tab('', hier_tab, image_source=image_right, tooltip="Hierarchy level"),
    ]],key='hier_pmg', tab_location="lefttop")]]


    layout = [[sg.TabGroup([[
        sg.Tab('Scenario Calculator',tab_1),
        sg.Tab('Model TPNB selector', pmg_hierarchy_tabs )
    ]],  key='-group1-', tab_location='top')]]


    # ------------------ Add following code block into your script ------------
    def get_scaling():
        # called before window created
        root = sg.tk.Tk()
        scaling = root.winfo_fpixels('1i') / 72
        root.destroy()
        return scaling

    # Find the number in original screen when GUI designed.
    my_scaling = 1.334646962233169  # call get_scaling() 1.334646962233169
    my_width, my_height = sg.Window.get_screen_size()  # call sg.Window.get_screen_size() 1536, 864

    # Get the number for new screen
    scaling_old = get_scaling()
    width, height = sg.Window.get_screen_size()

    scaling = scaling_old * min(width / my_width, height / my_height)

    sg.set_options(scaling=scaling)
    # -------------------------------------------------------------------------

    global verz_info
    window =sg.Window(f'Replenishment Whatifs App {verz_info}', layout,grab_anywhere=True, resizable=True,finalize = True, icon=taskbar_icon)
    window['saved_name'].update(select=True)


    sheet_name='country_tpnb'
    repl_type = "none"
    opening_type = "none"
    new_version_name = 'default'
    selected_department = 'gro'
    TPN_Cost_Base = False
    console = window['-ML-']
    volume_modifier = False
    case_cap_modifier = False
    shelf_capacity_modifier = False
    chunk_size = settings.get('selected_chunksize', 290)


    format = pd.read_excel(Path(os.getcwd()) / 'inputs/pmg_codes_store_format.xlsx', sheet_name="stores",
                           usecols=['store', 'format'])
    pmg_list = list(set())
    list2 = list(set())
    selected_option = ""
    selected_pmg = []
    format_ind = 'N'
    ownbrand_ind='N'
    pmg_or_catres = 0

    div_list=[]
    while True:  # The Event Loop
        event, values = window.read(timeout=500)
        if event == sg.WIN_CLOSED:
            break

        process_popup(TPN_Cost_Base)
        if event.startswith("sheet_"):
            sheet_name = event[6:]
            window['-ML-'].update('')


        if event == "set_dataset":
            new_replDataset = sg.popup_get_file(
                "open the DataSet you want to use", multiple_files=False, no_window=True, default_path=str(Path(os.getcwd())))
            #repl_dataset_f = re.search(f"inputs(.*)", repl_dataset_f).group(1)

            # selected_setting = values[0]
            #
            # print(values[0])
            if new_replDataset:
                sg.popup(f"\nYour selected ReplDataset:\n\n{get_last_part(new_replDataset)}\n", no_titlebar=True)
                repl_dataset_f = new_replDataset
                window['dataset_text'].update(
                    f"'{get_last_part(repl_dataset_f)}'")

                # settings['selected_setting'] = extract_path_suffix(new_replDataset)
                save_setting('selected_setting', extract_path_suffix(new_replDataset))
                # save_settings(settings)


        if event == "fillup":
            os.system(f"start EXCEL.EXE selected_tpns.xlsx")

        if event == "saved_name":
            new_version_name = values['saved_name']
        if event == "options_to_settings":
            TPN_Cost_Base, repl_type, opening_type, volume_modifier, case_cap_modifier, shelf_capacity_modifier, chunk_size = showing_settings(TPN_Cost_Base,repl_type,
                                                                      image_toggle_on, image_toggle_off,
                                                                      opening_type, console,
                                                                      volume_modifier, case_cap_modifier, shelf_capacity_modifier, chunk_size)
        if event== "run":
            directory = Path(os.getcwd())
            for_what_ifs = cm.What_Ifs_Inputs(sheet_name, repl_type,
                                               TPN_Cost_Base, opening_type,
                                               volume_modifier, case_cap_modifier, shelf_capacity_modifier, chunk_size, None)
            window['-ML-'].update('')

            threading.Thread(target=run_model, args=(window,new_version_name,for_what_ifs, repl_dataset_f), daemon=True).start()


        if values['format_level'] == True:
            format_ind = 'Y'

        if values['format_level'] == False:
            format_ind = 'N'

        if values['only_ownbrand'] == True:
            ownbrand_ind = 'Y'



        if values['only_ownbrand'] == False:
            ownbrand_ind = 'N'



        if event == 'open':
            window['-ML-'].update('')
            print('Opening DataSet.....🔍 ')


            try:
                input_open = pd.read_excel(Path(os.getcwd()) / 'selected_tpns.xlsx', sheet_name=sheet_name)


                if sheet_name == 'store_tpnb':
                    dict_list = input_open.groupby("store")["tpnb"].apply(lambda s: s.tolist()).to_dict()



                    data = pq.read_table(
                        Path(os.getcwd()) / repl_dataset_f, filters=[("store", "in", [k for k in dict_list.keys()])],
                        columns=['store','tpnb','division', 'dep','pmg', "product_name",'as_is_model_contains?',
                                 'srp', 'nsrp', 'full_pallet', 'mu','split_pallet', 'sold_units','cases_delivered','case_capacity',
                                 'Perforated box','Shrink', 'Tray', 'Tray + Hood', 'Tray + Shrink']).to_pandas()

                    op_type_cond = [data['Perforated box'] == 1, data['Shrink'] == 1,
                                    data['Tray'] == 1, data['Tray + Hood'] == 1,
                                    data['Tray + Shrink'] == 1]
                    op_type_result = ['Perforated box', 'Shrink', 'Tray', 'Tray + Hood', 'Tray + Shrink']
                    data['opening_type'] = np.select(op_type_cond, op_type_result, "not Ownbrand")

                    data.drop(['Perforated box','Shrink', 'Tray', 'Tray + Hood', 'Tray + Shrink'], axis=1, inplace=True)

                    df = pd.DataFrame()
                    for k, v in dict_list.items():
                        a = data.loc[(data.store == k) & (data.tpnb.isin(v))]
                        df = pd.concat([df,a])


                    df[['srp', 'nsrp', 'full_pallet', 'mu','split_pallet']] = df[['srp', 'nsrp', 'full_pallet', 'mu','split_pallet']].apply(
                        lambda x: x / 7)

                    df_to_show = df.groupby(['store','tpnb','division', 'dep','pmg', "product_name",'as_is_model_contains?', 'opening_type'], observed=True).agg({
                        'srp':'sum', 'nsrp':'sum', 'full_pallet':'sum', 'mu':'sum', 'split_pallet':'sum', 'sold_units':'sum',
                        'cases_delivered':'sum', 'case_capacity':'mean'
                    }).reset_index()
                    df_to_show = df_to_show.astype(
                        {'srp': 'int', 'nsrp': 'int', 'full_pallet': 'int', 'mu': 'int', 'split_pallet':'int', 'sold_units': 'int', 'cases_delivered':'int', 'case_capacity':'int'})
                    df_to_show = df_to_show.sort_values(by=['store','sold_units'], ascending=[True,False])
                    show_table(df_to_show, sheet_name)
                    window['-ML-'].update('')
                    del [df_to_show]

                if sheet_name == 'country_tpnb':


                    directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd())


                    dict_list = (
                        input_open.groupby("country")["tpnb"]
                            .apply(lambda s: s.tolist())
                            .to_dict()
                    )

                    df = pq.read_table(directory / repl_dataset_f,
                                       columns=['country', 'store', 'tpnb', 'division', 'dep', 'pmg', "product_name",
                                                'as_is_model_contains?', 'case_capacity','shelfCapacity',
                                                'srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet', 'sold_units',
                                                'cases_delivered',
                                                'opening_type']).to_pandas()
                    df.rename(columns={'shelfCapacity':'shelf_capacity'}, inplace=True)



                    df_to_show = pd.DataFrame()
                    for k, v in dict_list.items():
                        filt_df  =  df[(df.country == k) & (df.tpnb.isin(v))]
                        df_to_show = pd.concat([df_to_show, filt_df])


                    for r in ['srp', 'nsrp', 'full_pallet', 'mu','split_pallet']:
                        df_to_show[r] = np.where(df_to_show[r] >0, df_to_show.shelf_capacity * df_to_show[r], 0)


                    # df_to_show[['srp', 'nsrp', 'full_pallet', 'mu','split_pallet']] = df_to_show[['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']].apply(lambda x: x/7)


                    df_to_show = df_to_show.groupby(['country','store','tpnb','division', 'pmg', "product_name", 'opening_type','as_is_model_contains?'], observed=True).agg({
                        'srp':'sum', 'nsrp':'sum', 'full_pallet':'sum', 'mu':'sum', 'split_pallet':'sum', 'sold_units':'sum','cases_delivered':'sum', 'case_capacity':'mean', 'shelf_capacity':'sum'
                    }).reset_index()

                    df_to_show[['srp', 'nsrp','full_pallet', 'mu', 'split_pallet', 'shelf_capacity']] = df_to_show[['srp', 'nsrp','full_pallet', 'mu', 'split_pallet', 'shelf_capacity']]/7



                    df_to_show = df_to_show.sort_values(by=['store','shelf_capacity'], ascending=[True,False])
                    show_table(df_to_show,  sheet_name)
                    window['-ML-'].update('')
                    del [df_to_show, df]



            except PermissionError:
                sg.popup_error("Close the selected_tpns.xlsx")
                pass
            except KeyError:
                sg.popup_error(f"{sheet_name} sheet is EMPTY..\nPlease fill it up with Data!")
                question_input('input')
                pass


        is_modifying_department = (event == 'gro' or event == 'ppd' or event == 'gm')

        if is_modifying_department:
            selected_department = event

        if (is_modifying_department):
            pmg = pd.read_excel(Path(os.getcwd()) / 'inputs/pmg_codes_store_format.xlsx', sheet_name="pmg")

            if selected_department == 'gro':
                _list = ['DRY', 'BWS', 'HEA']
                pmg_list = list(pmg.loc[pmg['Dep'].isin(_list)]['pmg_code_name'])
                flag = 1
                # print(pmg_list)

            if selected_department == 'ppd':
                _list = ['PPD', 'DAI', 'FRZ']
                pmg_list = list(pmg.loc[pmg['Dep'].isin(_list)]['pmg_code_name'])
                flag = 1

            if selected_department == 'gm':
                _list = ['HDL']
                pmg_list = list(pmg.loc[pmg['Dep'].isin(_list)]['pmg_code_name'])
                flag = 1

            window['listbox'].update(visible=True, values=pmg_list)
            window['frame_listbox'].update(visible=True)

        if event == "catres":
            catres_names = pd.read_excel(Path(os.getcwd()) / 'inputs/pmg_codes_store_format.xlsx', sheet_name="catres")
            pmg_list = list(catres_names['Category name'])

            window['listbox'].update(visible=True, values=pmg_list)
            window['frame_listbox'].update(visible=True)
            flag = 0

        if event == 'select_pmg':
            try:
                selections = values["listbox"]
                if not selections:
                    sg.popup_error("You need to select an element")
                for x in selections:
                    list2.append(x)
                    pmg_list.remove(x)

                window['remove_pmg'].update(visible=True)
                window['remove_all_pmg'].update(visible=True)
                window['selected_listbox'].update(visible=True, values=list(set(list2)))
                window['listbox'].update(pmg_list)
                for pmg in list2:
                    try:
                        if any(item in pmg_list for item in list(catres_names['Category name'])):

                            selected_pmg.append(pmg)

                        if not any(item in pmg_list for item in list(catres_names['Category name'])):
                            if flag == 0:
                                selected_pmg.append(pmg)
                            if flag == 1:
                                selected_pmg.append(pmg[:5])

                    except:

                        selected_pmg.append(pmg[:5])

                selected_pmg = list(set(selected_pmg))

            except ValueError:
                sg.popup_error("You need to select an element")
                pass

        if event == 'remove_pmg':
            try:
                selections = values["selected_listbox"]
                if not selections:
                    sg.popup_error("You need to select an element")
                for x in selections:
                    pmg_list.append(x)
                    list2.remove(x)
                    window["selected_listbox"].update(list(set(list2)))
                    window["listbox"].update(pmg_list)
                    try:
                        if any(item in pmg_list for item in list(catres_names['Category name'])):
                            selected_pmg = [x for x in list2]

                    except:
                        selected_pmg = [x[:5] for x in list2]



            except ValueError:
                sg.popup_error("Select an Item to remove")
                pass

        if event == 'remove_all_pmg':
            try:
                list2 = []
                selected_pmg = []
                window["selected_listbox"].update(list(set(list2)))
                window["listbox"].update(pmg_list)

            except ValueError:
                sg.popup_error("Select an Item to remove")
                pass



        if event == 'open_as_is_search':


            if len(selected_pmg) > 0:

                with pl.StringCache():
                    a = pl.read_parquet(Path(os.getcwd()) / repl_dataset_f,
                                        columns=['country', 'store', 'division', 'Category name', 'dep', 'pmg', 'tpnb',
                                                 'product_name', 'srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet','icream_nsrp',
                                                 'sold_units', 'as_is_model_contains?', 'ownbrand', 'shelfCapacity']).rename({"shelfCapacity": "shelf_capacity"}).with_columns(
                        pl.when(pl.col('shelf_capacity') == 0).then(1).otherwise(pl.col('shelf_capacity')).alias('shelf_capacity'),
                        pl.when(pl.col('icream_nsrp') > 0).then(pl.col('icream_nsrp') + pl.col('nsrp')).otherwise(pl.col('nsrp')).alias('nsrp')
                        )


                    try:
                        if any(item in selected_pmg for item in list(catres_names['Category name'])):

                            a = a.filter(pl.col("Category name").is_in(selected_pmg))
                        else:
                            a = a.filter(pl.col("pmg").is_in(selected_pmg))
                    except:
                        a = a.filter(pl.col("pmg").is_in(selected_pmg))

                    if ownbrand_ind == 'Y':
                        a = a.filter(pl.col("ownbrand") == 'Y')
                        b = a.clone()


                        for x in ['srp','nsrp','full_pallet','mu','split_pallet']:
                            b = b.with_columns(pl.when(pl.col(x)>0).then(pl.col("shelf_capacity")*pl.col(x)).otherwise(0).alias(x))



                        as_is_data_pmg = b.groupby([
                            'country', 'Category name', 'division', 'pmg', 'tpnb', 'product_name', 'as_is_model_contains?'
                        ]).agg([
                            (pl.col('srp').sum() ).cast(pl.Int32),
                            (pl.col('nsrp').sum() ).cast(pl.Int32),
                            (pl.col('full_pallet').sum() ).cast(pl.Int32),
                            (pl.col('mu').sum() ).cast(pl.Int32),
                            (pl.col('split_pallet').sum() ).cast(pl.Int32),
                            pl.col("sold_units").sum().cast(pl.Int64),
                            pl.col("shelf_capacity").sum().cast(pl.Int64)


                        ]).sort(["sold_units"], descending=True).to_pandas()



                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_pmg[r] = (as_is_data_pmg[r] / as_is_data_pmg.shelf_capacity) * 100

                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_pmg[r] = as_is_data_pmg[r].apply(lambda x: f'{x:.0f}%')

                        #as_is_data_pmg.drop('shelf_capacity', axis=1, inplace=True)

                        as_is_data_nsrp_stores = a.groupby([
                            'country','store','Category name', 'division', 'pmg', 'tpnb', 'product_name', 'as_is_model_contains?'
                        ]).agg([
                            (pl.col('srp').mean() ).cast(pl.Float32),
                            (pl.col('nsrp').mean() ).cast(pl.Float32),
                            (pl.col('full_pallet').mean() ).cast(pl.Float32),
                            (pl.col('mu').mean() ).cast(pl.Float32),
                            (pl.col('split_pallet').mean() ).cast(pl.Float32),
                            pl.col("sold_units").sum().cast(pl.Float64).round(1),

                        ]).sort(["sold_units"], descending=True).to_pandas()

                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_nsrp_stores[r] = as_is_data_nsrp_stores[r].apply(lambda x: f'{x*100:.0f}%')






                # without format level
                    if format_ind =='N' and ownbrand_ind == 'N':

                        b = a.clone()


                        for x in ['srp','nsrp','full_pallet','mu','split_pallet']:
                            b = b.with_columns(pl.when(pl.col(x)>0).then(pl.col("shelf_capacity")*pl.col(x)).otherwise(0).alias(x))



                        as_is_data_pmg = b.groupby([
                            'country', 'Category name', 'division', 'pmg', 'tpnb', 'product_name', 'as_is_model_contains?'
                        ]).agg([
                            (pl.col('srp').sum() ).cast(pl.Int32),
                            (pl.col('nsrp').sum() ).cast(pl.Int32),
                            (pl.col('full_pallet').sum() ).cast(pl.Int32),
                            (pl.col('mu').sum() ).cast(pl.Int32),
                            (pl.col('split_pallet').sum() ).cast(pl.Int32),
                            pl.col("sold_units").sum().cast(pl.Int64),
                            pl.col("shelf_capacity").sum().cast(pl.Int64)


                        ]).sort(["sold_units"], descending=True).to_pandas()

                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_pmg[r] = (as_is_data_pmg[r] / as_is_data_pmg.shelf_capacity) * 100

                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_pmg[r] = as_is_data_pmg[r].apply(lambda x: f'{x:.0f}%')

                        #as_is_data_pmg.drop('shelf_capacity', axis=1, inplace=True)



                        as_is_data_nsrp_stores = a.groupby([
                            'country','store','Category name', 'division', 'pmg', 'tpnb', 'product_name', 'as_is_model_contains?'
                        ]).agg([
                            (pl.col('srp').mean() ).cast(pl.Float32),
                            (pl.col('nsrp').mean() ).cast(pl.Float32),
                            (pl.col('full_pallet').mean() ).cast(pl.Float32),
                            (pl.col('mu').mean() ).cast(pl.Float32),
                            (pl.col('split_pallet').mean() ).cast(pl.Float32),
                            pl.col("sold_units").sum().cast(pl.Float64).round(1),

                        ]).sort(["sold_units"], descending=True).to_pandas()

                        # print(as_is_data_nsrp_stores[(as_is_data_nsrp_stores.store == 11001) & (
                        #             as_is_data_nsrp_stores.tpnb == 100501174)].head())

                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_nsrp_stores[r] = as_is_data_nsrp_stores[r].apply(lambda x: f'{x*100:.0f}%')


                    # with format level
                    if format_ind =='Y':


                        format_pl = pl.from_pandas(format).with_columns([ pl.col('store').cast(pl.Int64)])

                        a = a.with_columns([ pl.col('store').cast(pl.Int64)]).join(format_pl, on=['store'], how='left')

                        b = a.clone()

                        for x in ['srp','nsrp','full_pallet','mu','split_pallet']:
                            b = b.with_columns(pl.when(pl.col(x)>0).then(pl.col("shelf_capacity")*pl.col(x)).otherwise(0).alias(x))

                        as_is_data_pmg = b.groupby([
                            'country','format', 'Category name', 'division', 'pmg', 'tpnb', 'product_name', 'as_is_model_contains?'
                        ]).agg([
                            (pl.col('srp').sum() ).cast(pl.Int32),
                            (pl.col('nsrp').sum() ).cast(pl.Int32),
                            (pl.col('full_pallet').sum() ).cast(pl.Int32),
                            (pl.col('mu').sum() ).cast(pl.Int32),
                            (pl.col('split_pallet').sum() ).cast(pl.Int32),
                            pl.col("sold_units").sum().cast(pl.Int64),
                            pl.col("shelf_capacity").sum().cast(pl.Int64)


                        ]).sort(["sold_units"], descending=True).to_pandas()

                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_pmg[r] = (as_is_data_pmg[r] / as_is_data_pmg.shelf_capacity) * 100

                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_pmg[r] = as_is_data_pmg[r].apply(lambda x: f'{x:.0f}%')

                        #as_is_data_pmg.drop('shelf_capacity', axis=1, inplace=True)


                        as_is_data_nsrp_stores = a.groupby([
                            'country','store','format','Category name', 'division', 'pmg', 'tpnb', 'product_name', 'as_is_model_contains?'
                        ]).agg([
                            (pl.col('srp').mean() ).cast(pl.Float32),
                            (pl.col('nsrp').mean() ).cast(pl.Float32),
                            (pl.col('full_pallet').mean() ).cast(pl.Float32),
                            (pl.col('mu').mean() ).cast(pl.Float32),
                            (pl.col('split_pallet').mean() ).cast(pl.Float32),
                            pl.col("sold_units").sum().cast(pl.Float64).round(1),

                        ]).sort(["sold_units"], descending=True).to_pandas()



                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_nsrp_stores[r] = as_is_data_nsrp_stores[r].apply(lambda x: f'{x*100:.0f}%')

                    if all(val == 'Y' for val in (ownbrand_ind, format_ind)):

                        a = a.filter(pl.col("ownbrand") == 'Y')

                        # format = pl.from_pandas(format).with_columns([ pl.col('store').cast(pl.Int32)])

                        a = a.with_columns([ pl.col('store').cast(pl.Int64)]).join(format_pl, on=['store'], how='left')

                        b = a.clone()

                        for x in ['srp','nsrp','full_pallet','mu','split_pallet']:
                            b = b.with_columns(pl.when(pl.col(x)>0).then(pl.col("shelf_capacity")*pl.col(x)).otherwise(0).alias(x))

                        as_is_data_pmg = b.groupby([
                            'country','format', 'Category name', 'division', 'pmg', 'tpnb', 'product_name', 'as_is_model_contains?'
                        ]).agg([
                            (pl.col('srp').sum() ).cast(pl.Int32),
                            (pl.col('nsrp').sum() ).cast(pl.Int32),
                            (pl.col('full_pallet').sum() ).cast(pl.Int32),
                            (pl.col('mu').sum() ).cast(pl.Int32),
                            (pl.col('split_pallet').sum() ).cast(pl.Int32),
                            pl.col("sold_units").sum().cast(pl.Int64),
                            pl.col("shelf_capacity").sum().cast(pl.Int64)


                        ]).sort(["sold_units"], descending=True).to_pandas()

                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_pmg[r] = (as_is_data_pmg[r] / as_is_data_pmg.shelf_capacity) * 100

                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_pmg[r] = as_is_data_pmg[r].apply(lambda x: f'{x:.0f}%')

                        #as_is_data_pmg.drop('shelf_capacity', axis=1, inplace=True)


                        as_is_data_nsrp_stores = a.groupby([
                            'country','store','format','Category name', 'division', 'pmg', 'tpnb', 'product_name', 'as_is_model_contains?'
                        ]).agg([
                            (pl.col('srp').mean() ).cast(pl.Float32),
                            (pl.col('nsrp').mean() ).cast(pl.Float32),
                            (pl.col('full_pallet').mean() ).cast(pl.Float32),
                            (pl.col('mu').mean() ).cast(pl.Float32),
                            (pl.col('split_pallet').mean() ).cast(pl.Float32),
                            pl.col("sold_units").sum().cast(pl.Float64).round(1),

                        ]).sort(["sold_units"], descending=True).to_pandas()


                        for r in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                            as_is_data_nsrp_stores[r] = as_is_data_nsrp_stores[r].apply(lambda x: f'{x*100:.0f}%')

                    as_is_data_pmg = as_is_data_pmg.query("srp != 'nan%'")
                    as_is_data_pmg["shelf_capacity"] = (as_is_data_pmg["shelf_capacity"]/7).astype("int")

                    as_is_data_nsrp_stores = as_is_data_nsrp_stores.query("srp != 'nan%'")
                    show_table_part2(as_is_data_pmg,  as_is_data_nsrp_stores, format_ind)
                del [as_is_data_pmg,  as_is_data_nsrp_stores, a, b]

            else:
                sg.popup_error("Select an Item to check")

        if event == 'piecharts_countries':

            if len(selected_pmg) > 0:
                try:
                    if any(item in selected_pmg for item in list(catres_names['Category name'])):
                        as_is_data = pq.read_table(Path(os.getcwd()) / repl_dataset_f, filters=[("Category name", "in", selected_pmg)],
                                                   columns=['country','store','Category name', 'division', 'dep', 'pmg', 'tpnb', 'product_name', 'srp', 'nsrp','full_pallet', 'mu', 'split_pallet', 'sold_units', 'ownbrand', "shelfCapacity"]).to_pandas()
                    else:
                        as_is_data = pq.read_table(Path(os.getcwd()) / repl_dataset_f, filters=[("pmg", "in", selected_pmg)],
                                                   columns=['country','store','Category name', 'division', 'dep', 'pmg', 'tpnb', 'product_name', 'srp', 'nsrp','full_pallet', 'mu', 'split_pallet', 'sold_units', 'ownbrand',"shelfCapacity"]).to_pandas()
                except:

                        as_is_data = pq.read_table(Path(os.getcwd()) / repl_dataset_f, filters=[("pmg", "in", selected_pmg)],
                                                   columns=['country','store','Category name', 'division', 'dep', 'pmg', 'tpnb', 'product_name', 'srp', 'nsrp','full_pallet', 'mu', 'split_pallet', 'sold_units', 'ownbrand',"shelfCapacity"]).to_pandas()

                as_is_data.rename(columns={"shelfCapacity":'shelf_capacity'}, inplace=True)
                if ownbrand_ind == 'Y':

                    as_is_data = as_is_data.query("ownbrand == 'Y'")
                    try:
                        if any(item in selected_pmg for item in list(catres_names['Category name'])):
                            as_is_data_pmg = as_is_data.loc[as_is_data['Category name'].isin(selected_pmg)]
                        else:
                            as_is_data_pmg = as_is_data.loc[as_is_data['pmg'].isin(selected_pmg)]

                    except:
                        as_is_data_pmg = as_is_data.loc[as_is_data['pmg'].isin(selected_pmg)]

                    #as_is_data_pmg = as_is_data_pmg.astype(
                     #   {'srp': 'int', 'nsrp': 'int', 'full_pallet': 'int', 'mu': 'int', 'split_pallet': 'int'})

                if ownbrand_ind == 'N':
                    try:

                        if any(item in selected_pmg for item in list(catres_names['Category name'])):
                            as_is_data_pmg = as_is_data.loc[as_is_data['Category name'].isin(selected_pmg)]
                        else:
                            as_is_data_pmg = as_is_data.loc[as_is_data['pmg'].isin(selected_pmg)]
                    except:
                        as_is_data_pmg = as_is_data.loc[as_is_data['pmg'].isin(selected_pmg)]

                    # as_is_data_pmg = as_is_data.loc[as_is_data['pmg'].isin(selected_pmg)]
                    #as_is_data_pmg = as_is_data_pmg.astype(
                     #   {'srp': 'int', 'nsrp': 'int', 'full_pallet': 'int', 'mu': 'int', 'split_pallet': 'int'})
                    # pd.set_option("display.max_columns", None)
                for x in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:
                    as_is_data_pmg[x] = np.where(as_is_data_pmg[x] > 0, as_is_data_pmg[x] * as_is_data_pmg['shelf_capacity'], 0)

                as_is_data_pmg[['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet', 'shelf_capacity']] = as_is_data_pmg[['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet', 'shelf_capacity']] /7
                # as_is_data_pmg.sort_values(by=['store', 'tpnb']).head(10).to_excel(
                #     r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_02\test_3.xlsx",
                #     index=False)

                piechart(as_is_data_pmg, "from_system")
                del [as_is_data_pmg, as_is_data]


    window.close()

if __name__ == '__main__':
    try:
        gui()
    except Exception as e:
        print(f"{e}")

