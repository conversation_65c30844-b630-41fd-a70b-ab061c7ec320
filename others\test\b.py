import streamlit as st
from string import Template
from textwrap import dedent

class HTML_Template:
    base_style = Template(
        dedent(
            """
            <style>
                $css
            </style>"""
        )
    )

# Enhanced CSS with better shadows and transitions
container_css = """
.st-key-card_one {
    background-color: #808080;
    border-radius: 1rem;
    padding: 1.5rem;
    margin: 1rem 0;
    min-height: 100px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.st-key-card_one:hover {
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

.st-key-card_one div[data-testid="stText"] div {
    color: white;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.st-key-card_two {
    background-color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin: 1rem 0;
    min-height: 100px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.st-key-card_two:hover {
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

.st-key-card_two div[data-testid="stText"] div {
    color: #333;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}
"""

# Apply the CSS using the template
st.markdown(HTML_Template.base_style.substitute(css=container_css), unsafe_allow_html=True)

# Create cards with columns for better layout
col1, col2 = st.columns(2, gap="large")

with col1:
    with st.container(key="card_one"):
        st.text("This is card one")
        st.button("Card One Button", type="primary")

with col2:
    with st.container(key="card_two"):
        st.text("This is card two")
        st.button("Card Two Button")

# You can add more cards with different styles as needed