import tkinter as tk
from tkinter import ttk
import random

class ResponsiveGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Responsive Tkinter Application")
        
        # Set initial window size (not fullscreen)
        window_width = 800
        window_height = 600
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        center_x = int((screen_width - window_width) / 2)
        center_y = int((screen_height - window_height) / 2)
        self.root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')
        
        # Configure grid weight to make it responsive
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_columnconfigure(1, weight=3)
        self.root.grid_rowconfigure(1, weight=1)
        
        # Create and configure frames
        self.create_frames()
        self.create_widgets()
        
        # Bind resize event
        self.root.bind('<Configure>', self.on_resize)
        
        # Toggle fullscreen binding
        self.root.bind('<F11>', self.toggle_fullscreen)
        self.is_fullscreen = False
        
    def create_frames(self):
        # Left sidebar frame
        self.sidebar_frame = ttk.Frame(self.root, relief="ridge", padding="10")
        self.sidebar_frame.grid(row=0, column=0, rowspan=2, sticky="nsew")
        self.sidebar_frame.grid_columnconfigure(0, weight=1)
        
        # Top frame
        self.top_frame = ttk.Frame(self.root, relief="ridge", padding="10")
        self.top_frame.grid(row=0, column=1, sticky="nsew")
        self.top_frame.grid_columnconfigure(0, weight=1)
        
        # Main content frame
        self.main_frame = ttk.Frame(self.root, relief="ridge", padding="10")
        self.main_frame.grid(row=1, column=1, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
        
    def create_widgets(self):
        # Sidebar widgets
        ttk.Label(self.sidebar_frame, text="Navigation").grid(row=0, column=0, pady=5)
        for i in range(5):
            ttk.Button(self.sidebar_frame, text=f"Menu {i+1}").grid(row=i+1, column=0, pady=2, sticky="ew")
            
        # Top frame widgets
        ttk.Label(self.top_frame, text="Search:").grid(row=0, column=0, padx=5)
        ttk.Entry(self.top_frame).grid(row=0, column=1, padx=5)
        ttk.Button(self.top_frame, text="Search").grid(row=0, column=2, padx=5)
        ttk.Button(self.top_frame, text="Settings").grid(row=0, column=3, padx=5)
        
        # Main content widgets
        # Create a Text widget with scrollbar
        self.text_widget = tk.Text(self.main_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(self.main_frame, orient="vertical", command=self.text_widget.yview)
        self.text_widget.configure(yscrollcommand=scrollbar.set)
        
        # Grid them with proper weights
        self.text_widget.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        
        # Add some sample text
        sample_text = "This is a responsive Tkinter GUI.\n\n" * 10
        self.text_widget.insert("1.0", sample_text)
        
    def on_resize(self, event):
        # This method will be called when the window is resized
        # You can add specific resize behavior here
        pass
        
    def toggle_fullscreen(self, event=None):
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes('-fullscreen', self.is_fullscreen)
        return "break"

if __name__ == "__main__":
    root = tk.Tk()
    app = ResponsiveGUI(root)
    root.mainloop()