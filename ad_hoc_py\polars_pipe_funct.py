import polars as pl
import random 

# Create a Polars DataFrame with base columns
df = pl.DataFrame({
    'name': ['<PERSON>', '<PERSON>', '<PERSON>'], 
    'offensive_skill': [5, 30, 85], 
    'defensive_skill': [92, 30, 10]
    })
    
# Define polars custom functions to apply
def add_position_column(df):
    df = df.with_columns( 
        pl.when(pl.col('defensive_skill') > 50).then('CB')
        .when(pl.col('offensive_skill') > 50).then('FW')
        .otherwise('bench').alias("position")
    )
    return df

def add_squad_number_column(df):
    df = df.with_columns( 
        pl.when(pl.col('position') == 'CD').then(pl.lit(random.sample(range(2, 6), 1)[0], dtype=pl.Int8))
        .when(pl.col('position') == 'FW').then(pl.lit(random.sample(range(7, 19), 1)[0], dtype=pl.Int8))
        .otherwise('-').alias("squad_number")
    )
    return df

# Chain operations together using the pipe function


df = df.pipe(add_position_column).pipe(add_squad_number_column)



result = (
    df.lazy()
    .pipe(add_position_column)
    .pipe(add_squad_number_column)
    .collect()
)

result  








pl.Config.set_tbl_rows(20)




    
import polars as pl
from custom_expressions import CustomStringMethodsCollection


def clean_locations(df):
    return (df
            .with_columns(
                pl.col('name')
                .custom.to_title_case()
                .str.replace_all('0', 'o', literal=True)
                .str.replace_all('3', 'e', literal=True)
                .str.replace_all('Londen', 'London', literal=True)
                .str.replace_all('Livrepool', 'Liverpool', literal=True)
            ))
    
    
df = df.pipe(clean_locations)





        
        