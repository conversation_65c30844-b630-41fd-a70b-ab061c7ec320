import pandas as pd
import pyodbc
import numpy as np
from datetime import datetime
import polars as pl



pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)


with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:

    sql = """
    SELECT
        stores.cntr_code AS country,
        CAST(stores.dmst_store_code as INT) AS store,
        /*CAST(tpns.slad_tpnb as INT) AS tpnb,
        tpns.slad_long_des AS product_name,*/
        hier.pmg AS pmg,
        CAST(a.lssls_rcode as INT) AS code,
        SUM(a.lssls_quantity_adj) AS amount,
        COUNT(DISTINCT tpns.slad_tpnb) as unique_tpn
        FROM dw.ls_stock_loss a
        JOIN dm.dim_stock_loss_rc b ON a.lssls_cntr_id = b.cntr_id
        AND a.lssls_rcode = b.lsrc_code 
        JOIN dm.dim_stores stores ON stores.dmst_store_id = a.lssls_dmst_id 
        AND stores.cntr_id = a.lssls_cntr_id 
        JOIN dm.dim_artgld_details tpns ON tpns.slad_dmat_id = a.lssls_dmat_id 
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
        JOIN tesco_analysts.hierarchy_spm hier ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0") 
        AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
        AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
        AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
        AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
        WHERE a.part_col BETWEEN 20240527 AND 20240901 
        AND stores.cntr_code IN ("HU", "CZ", "SK")
        /*AND a.lssls_rcode in ('3', '2', '4', '544','14','104')
        AND SUBSTRING(hier.pmg, 1, 3) IN  ("MPC")*/
        GROUP BY stores.cntr_code, stores.dmst_store_code,  /*tpns.slad_tpnb,  tpns.slad_long_des,*/ hier.pmg, a.lssls_rcode
        
        """
        
    art_gold = pd.read_sql(sql, conn)
        
        



# products = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_10\CAYG Operational Cost_TPN list.xlsx",
#                          sheet_name="CE3 STORE LIST")[['Country', 'TPN']].drop_duplicates()

# dict_list = (
#     products.groupby("Country")["TPN"]
#     .apply(lambda s: s.tolist())
#     .to_dict()
# )






# with pyodbc.connect(
#     "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
# ) as conn:
    
#     df2 = pd.DataFrame()
#     for k, v in dict_list.items():
        
#         s = list()
#         for x in v:

#             s.append(str(x))

#         tpn = tuple(s)
        
        
#         stock = """
#         SELECT 
#         CAST(stores.dmst_store_code AS INT) AS store,
#         hier.pmg AS pmg,
#         CAST(mstr.slad_tpnb AS INT) AS tpnb,
#         mstr.slad_tpn AS tpn,
#         mstr.slad_long_des as product_name,
#         AVG(stock.slstks_stock_unit_sl) AS stock,
#         AVG(stock.slstks_price) as item_price
        
#         FROM dw.sl_stocks stock
        
#         JOIN dm.dim_stores stores ON stock.slstks_dmst_id = stores.dmst_store_id AND stock.slstks_cntr_id = stores.cntr_id
#         JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = stock.slstks_dmat_id AND mstr.cntr_id = stock.slstks_cntr_id
#         LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = stock.part_col
#         JOIN tesco_analysts.hierarchy_spm hier
#         ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0")
#         AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0")
#         AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0")
#         AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0")
#         AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
        
#         WHERE stock.part_col BETWEEN 20240108 AND 20241008
#         AND stores.cntr_code = '{k}'
#         AND mstr.slad_tpn in {tpn}
#         AND stock.slstks_stock_unit_sl > 0
        
#         GROUP BY stores.dmst_store_code, hier.pmg, mstr.slad_tpnb, mstr.slad_tpn, mstr.slad_long_des
#         ORDER BY stores.dmst_store_code, hier.pmg, mstr.slad_tpnb""".format(k=k, tpn=tpn)
        
#         art_gold = pd.read_sql(stock, conn)
#         df2 = pd.concat([df2, art_gold])
        
        
#CAYG Suppliers
       
cayg_suppl = pl.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_10\CAYG Operational Cost_TPN list_with_Stock_supplierCodes.xlsx",
                           engine="calamine", sheet_name="Suppliers").to_pandas()[["country","suppl_name"]].drop_duplicates()
dict_list_cayg = (
    cayg_suppl.groupby("country")["suppl_name"]
    .apply(lambda s: s.tolist())
    .to_dict()
)


# with pyodbc.connect(
#     "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
# ) as conn:
    
#     df2 = pd.DataFrame()
#     for k, v in dict_list_cayg.items():
        
#         s = list()
#         for x in v:

#             s.append(str(x))

#         suppl_codes = tuple(s)
        
        
#         unit_delivered_stock = """
#         WITH unit_delivered AS (
#             SELECT
#                 CASE 
#                     WHEN a.int_cntr_id = 1 THEN 'CZ'
#                     WHEN a.int_cntr_id = 2 THEN 'SK'
#                     WHEN a.int_cntr_id = 4 THEN 'HU'
#                     ELSE 'Unknown'
#                 END AS country,
#                 CAST(CONCAT(a.int_cntr_id, a.store) AS INT) as store,
#                 supl.dmsup_long_des as supplier_name,
#                 supl.dmsup_code as suppl_code,
#                 Count(DISTINCT a.product) as unique_tpn,
#                 SUM(a.qty) as unit
#             FROM stg_go.go_106_order_receiving a
#             LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product and b.cntr_id = a.int_cntr_id
#             LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
#             LEFT JOIN dw.dim_suppliers supl ON supl.dmsup_cntr_id = b.cntr_id AND supl.dmsup_id = b.slad_dmsup_id
#             WHERE supl.dmsup_long_des IN {suppl_codes}
#                 AND a.int_cntr_code = '{k}'
#                 AND a.part_col BETWEEN 20231009 AND 20241008
#             GROUP BY
#                 CASE 
#                     WHEN a.int_cntr_id = 1 THEN 'CZ'
#                     WHEN a.int_cntr_id = 2 THEN 'SK'
#                     WHEN a.int_cntr_id = 4 THEN 'HU'
#                     ELSE 'Unknown'
#                 END,
#                 CONCAT(a.int_cntr_id, a.store),
#                 supl.dmsup_long_des,
#                 supl.dmsup_code
#             ORDER BY CONCAT(a.int_cntr_id, a.store)
#         ),
#         stock AS (
#             SELECT
#                 stores.cntr_code as country,
#                 CAST(stores.dmst_store_code AS INT) AS store,
#                 supl.dmsup_long_des as supplier_name,
#                 supl.dmsup_code as suppl_code,
#                 AVG(stock.slstks_stock_unit_sl) AS stock,
#                 AVG(stock.slstks_price) as item_price
#             FROM dw.sl_stocks stock
#             JOIN dm.dim_stores stores ON stock.slstks_dmst_id = stores.dmst_store_id AND stock.slstks_cntr_id = stores.cntr_id
#             JOIN dm.dim_artgld_details b ON b.slad_dmat_id = stock.slstks_dmat_id AND b.cntr_id = stock.slstks_cntr_id
#             LEFT JOIN dw.dim_suppliers supl ON supl.dmsup_cntr_id = b.cntr_id AND supl.dmsup_id = b.slad_dmsup_id
#             WHERE supl.dmsup_long_des IN {suppl_codes}
#                 AND stores.cntr_code = '{k}'
#                 AND stock.part_col BETWEEN 20231009 AND 20241008
#                 AND stock.slstks_stock_unit_sl > 0
#             GROUP BY stores.cntr_code, stores.dmst_store_code, supl.dmsup_long_des, supl.dmsup_code
#         )
#         SELECT 
#             COALESCE(s.country, d.country) AS country,
#             COALESCE(s.store, d.store) AS store,
#             COALESCE(s.supplier_name, d.supplier_name) as supplier_name,
#             COALESCE(s.suppl_code, d.suppl_code) as suppl_code,
#             COALESCE(s.unique_tpn, 0) AS unique_tpn,
#             COALESCE(s.unit, 0) AS unit_delivered,
#             COALESCE(d.stock, 0) AS AVG_stock,
#             COALESCE(d.item_price, 0) AS AVG_item_price
#         FROM unit_delivered s
#         FULL OUTER JOIN stock d
#             ON s.country = d.country
#             AND s.store = d.store
#             AND s.supplier_name = d.supplier_name
#             AND s.suppl_code = d.suppl_code
#         ORDER BY 
#             country,
#             store,
#             supplier_name,
#             suppl_code
#         """.format(k=k, suppl_codes = suppl_codes)

        
        
#         art_gold = pd.read_sql(unit_delivered_stock, conn)
#         df2 = pd.concat([df2, art_gold])
        
        


def create_sql_string_list(items):
    return ", ".join(f"'{item}'" for item in items)

def execute_query(conn, query):
    try:
        return pd.read_sql(query, conn)
    except pyodbc.Error as e:
        print(f"An error occurred: {str(e)}")
        print(f"SQL Query: {query}")
        return None

with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:
    
    df2 = pd.DataFrame()
    for k, v in dict_list_cayg.items():
        
        suppl_codes_str = create_sql_string_list(v)
        
        # Query 1: unit_delivered
        unit_delivered_query = f"""
        SELECT
            CASE 
                WHEN a.int_cntr_id = 1 THEN 'CZ'
                WHEN a.int_cntr_id = 2 THEN 'SK'
                WHEN a.int_cntr_id = 4 THEN 'HU'
                ELSE 'Unknown'
            END AS country,
            CAST(CONCAT(a.int_cntr_id, a.store) AS INT) as store,
            supl.dmsup_long_des as supplier_name,
            supl.dmsup_code as suppl_code,
            Count(DISTINCT a.product) as unique_tpn,
            SUM(a.qty) as unit
        FROM stg_go.go_106_order_receiving a
        LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product and b.cntr_id = a.int_cntr_id
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
        LEFT JOIN dw.dim_suppliers supl ON supl.dmsup_cntr_id = b.cntr_id AND supl.dmsup_id = b.slad_dmsup_id
        WHERE supl.dmsup_long_des IN ({suppl_codes_str})
            AND a.int_cntr_code = '{k}'
            AND a.part_col BETWEEN 20231009 AND 20241008
        GROUP BY
            CASE 
                WHEN a.int_cntr_id = 1 THEN 'CZ'
                WHEN a.int_cntr_id = 2 THEN 'SK'
                WHEN a.int_cntr_id = 4 THEN 'HU'
                ELSE 'Unknown'
            END,
            CONCAT(a.int_cntr_id, a.store),
            supl.dmsup_long_des,
            supl.dmsup_code
        """
        
        unit_delivered_df = execute_query(conn, unit_delivered_query)
        if unit_delivered_df is None:
            continue

        # Query 2: stock
        stock_query = f"""
        SELECT
            stores.cntr_code as country,
            CAST(stores.dmst_store_code AS INT) AS store,
            supl.dmsup_long_des as supplier_name,
            supl.dmsup_code as suppl_code,
            AVG(stock.slstks_stock_unit_sl) AS stock,
            AVG(stock.slstks_price) as item_price
        FROM dw.sl_stocks stock
        JOIN dm.dim_stores stores ON stock.slstks_dmst_id = stores.dmst_store_id AND stock.slstks_cntr_id = stores.cntr_id
        JOIN dm.dim_artgld_details b ON b.slad_dmat_id = stock.slstks_dmat_id AND b.cntr_id = stock.slstks_cntr_id
        LEFT JOIN dw.dim_suppliers supl ON supl.dmsup_cntr_id = b.cntr_id AND supl.dmsup_id = b.slad_dmsup_id
        WHERE supl.dmsup_long_des IN ({suppl_codes_str})
            AND stores.cntr_code = '{k}'
            AND stock.part_col BETWEEN 20231009 AND 20241008
            AND stock.slstks_stock_unit_sl > 0
        GROUP BY stores.cntr_code, stores.dmst_store_code, supl.dmsup_long_des, supl.dmsup_code
        """
        
        stock_df = execute_query(conn, stock_query)
        if stock_df is None:
            continue

        # Combine results
        combined_df = pd.merge(unit_delivered_df, stock_df, 
                               on=['country', 'store', 'supplier_name', 'suppl_code'], 
                               how='outer')
        
        combined_df['unique_tpn'] = combined_df['unique_tpn'].fillna(0)
        combined_df['unit'] = combined_df['unit'].fillna(0)
        combined_df['stock'] = combined_df['stock'].fillna(0)
        combined_df['item_price'] = combined_df['item_price'].fillna(0)
        
        combined_df = combined_df.rename(columns={'unit': 'unit_delivered', 'stock': 'AVG_stock', 'item_price': 'AVG_item_price'})
        
        combined_df = combined_df.sort_values(['country', 'store', 'supplier_name', 'suppl_code'])
        
        df2 = pd.concat([df2, combined_df])