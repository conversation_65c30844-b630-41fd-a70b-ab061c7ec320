{"cells": [{"cell_type": "code", "execution_count": 2, "id": "b7ff71de-9729-4c0f-bae3-d3f5fd496ccb", "metadata": {}, "outputs": [], "source": ["!mkdir kaggle"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}