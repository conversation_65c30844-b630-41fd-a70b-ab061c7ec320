2025-06-10 09:04:57,036 - INFO - Attempting to connect to skpbj0003.global.tesco.org...
2025-06-10 09:04:57,262 - INFO - Connected (version 2.0, client OpenSSH_8.0)
2025-06-10 09:04:58,089 - INFO - Auth banner: b'Authorized uses only. All activity may be monitored and reported.\n'
2025-06-10 09:04:58,090 - INFO - Authentication (password) successful!
2025-06-10 09:04:58,091 - INFO - CONNECTION: SSH Connection established successfully
2025-06-10 09:04:58,092 - INFO - CHECKING: SQL file existence...
2025-06-10 09:04:58,332 - INFO - FILE FOUND: -rwxrwxrwt 1 phrubos domain users 9786 Jun 10 08:04 /home/<USER>/srd/srd.sql
2025-06-10 09:04:58,333 - INFO - CHECKING: Disk space...
2025-06-10 09:04:58,605 - INFO - DISK SPACE: Filesystem                   Size  Used Avail Use% Mounted on
/dev/mapper/vg_root-lv_home   15G  1.5G   14G  10% /home

2025-06-10 09:04:58,605 - INFO - STARTING: Script execution: SRD Planogram Analysis
2025-06-10 09:04:58,606 - INFO - ============================================================
2025-06-10 09:05:03,881 - INFO - SQL: 0: ******************************************> DROP TABLE IF EXISTS sch_analysts 
.tbl_srd_planogram_analysis;
2025-06-10 09:05:08,884 - INFO - SQL: DROP TABLE IF EXISTS sch_analysts.tbl_srd_planogram_analysis
2025-06-10 09:05:08,885 - INFO - SQL: DROP TABLE IF EXISTS sch_analysts.tbl_srd_planogram_analysis
2025-06-10 09:05:09,887 - INFO - SUCCESS: 25/06/10 08:05:09 INFO ExecuteStatement: Processing phrubos's query[da0f1352-dcbe-40bc-bf9d-34826cdb47fd]: RUNNING_STATE -> FINISHED_STATE, time taken: 6.203 seconds
2025-06-10 09:05:10,888 - INFO - SUCCESS: 2025-06-10 08:05:09.607 [KyuubiSessionManager-exec-pool: Thread-3005] INFO org.apache.kyuubi.operation.ExecuteStatement: Query[da0f1352-dcbe-40bc-bf9d-34826cdb47fd] in FINISHED_STATE
2025-06-10 09:05:10,888 - INFO - SUCCESS: 2025-06-10 08:05:09.607 [KyuubiSessionManager-exec-pool: Thread-3005] INFO org.apache.kyuubi.operation.ExecuteStatement: Processing phrubos's query[da0f1352-dcbe-40bc-bf9d-34826cdb47fd]: RUNNING_STATE -> FINISHED_STATE, time taken: 6.205 seconds
2025-06-10 09:05:10,889 - INFO - SQL: No rows selected (6.255 seconds)
2025-06-10 09:05:10,890 - INFO - SQL: 0: ******************************************> SELECT 'Creating enhanced SRD tab 
le to target 6.5M rows...' AS status;
2025-06-10 09:05:10,891 - INFO - SQL: SELECT 'Creating enhanced
2025-06-10 09:05:11,892 - INFO - SQL: SELECT 'Creating enhanced SRD table to target 6.5M rows...' AS status
2025-06-10 09:05:12,894 - INFO - SUCCESS: 2025-06-10 08:05:09.689 [KyuubiSessionManager-exec-pool: Thread-3006] INFO org.apache.kyuubi.operation.ExecuteStatement: Query[08b40612-880b-4faa-a315-5d3fc43cc6b9] in FINISHED_STATE
2025-06-10 09:05:12,896 - INFO - SUCCESS: 2025-06-10 08:05:09.689 [KyuubiSessionManager-exec-pool: Thread-3006] INFO org.apache.kyuubi.operation.ExecuteStatement: Processing phrubos's query[08b40612-880b-4faa-a315-5d3fc43cc6b9]: RUNNING_STATE -> FINISHED_STATE, time taken: 0.019 seconds
2025-06-10 09:05:12,898 - INFO - SQL: 1 row selected (0.088 seconds)
2025-06-10 09:05:13,902 - INFO - TABLE CREATE: 0: ******************************************> CREATE TABLE sch_analysts.tbl_srd 
_planogram_analysis
2025-06-10 09:05:13,903 - INFO - SQL: . . . . . . . . . . . . . . . . . . . . . . .>   SELECT DISTINCT
2025-06-10 09:05:15,905 - INFO - SQL: . . . . . . . . . . . . . . . . . . . . . . .>   SELECT DISTINCT
2025-06-10 09:05:16,906 - INFO - SQL: . . . . . . . . . . . . . . . . . . . . . . .>           WHERE (p.dbstatus not i 
n ('4','3')) 
AND CONCAT(p.dbversiONkey,p.int_cntr_code) in (SELECT DISTINCT CONCAT(dbparentpl 
anogramkey,int_cntr_code) as A FROM Flop_POG_1)),
2025-06-10 09:05:16,909 - INFO - SQL: . . . . . . . . . . . . . . . . . . . . . . .>   SELECT DISTINCT
2025-06-10 09:05:18,914 - INFO - SQL: . . . . . . . . . . . . . . . . . . . . . . .>   SELECT DISTINCT
2025-06-10 09:05:20,915 - INFO - SQL: . . . . . . . . . . . . . .>   SELECT DISTINCT
2025-06-10 09:05:25,919 - INFO - SQL: . . . . . . . . . . . . . . . . . . . . . . .>   SELECT DISTINCT
2025-06-10 09:05:26,921 - INFO - SQL: . . . . . . . . . . . . . . . . . . . . . . .>           FROM (SELECT DISTINCT
2025-06-10 09:05:27,925 - INFO - SQL: . . . . . . . . . . . . . . . . . . . . . . .>                   FROM (SELECT DI 
STINCT
2025-06-10 09:05:29,929 - INFO - SQL: . . . . . . . . . . . . . . . . . . . . . . .> SELECT DISTINCT
2025-06-10 09:05:36,934 - INFO - TABLE CREATE: CREATE TABLE sch_analysts.tbl_srd_planogram_analysis
2025-06-10 09:05:36,935 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:37,936 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:37,938 - INFO - SQL: WHERE (p.dbstatus not in ('4','3'))	AND CONCAT(p.dbversiONkey,p.int_cntr_code) in (SELECT DISTINCT CONCAT(dbparentplanogramkey,int_cntr_code) as A FROM Flop_POG_1)),
2025-06-10 09:05:37,940 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:38,943 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:38,946 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:40,952 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:40,952 - INFO - SQL: FROM (SELECT DISTINCT
2025-06-10 09:05:41,954 - INFO - SQL: FROM (SELECT DISTINCT
2025-06-10 09:05:41,954 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:45,958 - INFO - TABLE CREATE: CREATE TABLE sch_analysts.tbl_srd_planogram_analysis
2025-06-10 09:05:45,961 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:45,965 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:45,967 - INFO - SQL: WHERE (p.dbstatus not in ('4','3'))	AND CONCAT(p.dbversiONkey,p.int_cntr_code) in (SELECT DISTINCT CONCAT(dbparentp
2025-06-10 09:05:46,973 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:46,975 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:47,976 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:48,978 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:49,981 - INFO - SQL: FROM (SELECT DISTINCT
2025-06-10 09:05:49,982 - INFO - SQL: FROM (SELECT DISTINCT
2025-06-10 09:05:50,983 - INFO - SQL: SELECT DISTINCT
2025-06-10 09:05:54,989 - INFO - TABLE CREATE: mberoffixtures:int,desc1:varchar(100),desc2:varchar(100),desc11:varchar(100),desc12:varchar(100),desc13:varchar(100),desc17:varchar(100),desc29:varchar(100),desc44:varchar(100),pendingdate:string,livedate:string,status1:varchar(100),dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>) is different from the schema when this table was created by Spark SQL(struct<DBStatus:int,DBKey1:int,DBKey2:int,DBKey3:int,DBKey4:int,DBKey5:int,Name:string,DBKey:int,Width:decimal(15,3),Depth:decimal(15,3),NumberOfFixtures:int,Desc1:string,Desc2:string,Desc11:string,Desc12:string,Desc13:string,Desc17:string,Desc29:string,Desc44:string,PendingDate:string,LiveDate:string,Status1:string,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:05:55,992 - INFO - TABLE CREATE: ntfloorplankey:int,dbparentfixturekey:int,dbparentplanogramkey:int,dbkey:int,x:decimal(15,3),width:decimal(15,3),z:decimal(15,3),height:decimal(15,3),y:decimal(15,3),depth:decimal(15,3),rankx:int,rankz:int,ranky:int,merchwidth:int,merchdepth:int,desc3:varchar(100),locationid:int,overidingwidth:decimal(15,3),linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),department:decimal(15,3),heightoveride:decimal(15,3),depthoveride:decimal(15,3),partid:int,angle:decimal(15,3),segmentstart:int,segmentend:int,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_flr_key:string>) is different from the schema when this table was created by Spark SQL(struct<DBTime:string,DBParentFloorplanKey:int,DBParentFixtureKey:int,DBParentPlanogramKey:int,DBKey:int,X:decimal(15,3),Width:decimal(15,3),Z:decimal(15,3),Height:decimal(15,3),Y:decimal(15,3),Depth:decimal(15,3),RankX:int,RankZ:int,RankY:int,MerchWidth:int,MerchDepth:int,Desc3:string,LocationID:int,OveridingWidth:decimal(15,3),Linear:decimal(
2025-06-10 09:05:56,996 - INFO - SQL: 15,3),Square:decimal(15,3),Cubic:decimal(15,3),Department:decimal(15,3),HeightOveride:decimal(15,3),DepthOveride:decimal(15,3),PartID:int,Angle:decimal(15,3),SegmentStart:int,SegmentEnd:int,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_flr_key:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:06:00,000 - INFO - TABLE CREATE: ,value25:decimal(15,3),value26:decimal(15,3),value27:decimal(15,3),value28:decimal(15,3),value29:decimal(15,3),value30:decimal(15,3),value31:decimal(15,3),value32:decimal(15,3),value33:decimal(15,3),value34:decimal(15,3),value35:decimal(15,3),value36:decimal(15,3),value37:decimal(15,3),value38:decimal(15,3),value39:decimal(15,3),value40:decimal(15,3),value41:decimal(15,3),value42:decimal(15,3),value43:decimal(15,3),value44:decimal(15,3),value45:decimal(15,3),value46:decimal(15,3),value47:decimal(15,3),value48:decimal(15,3),value49:decimal(15,3),value50:decimal(15,3),flag1:int,flag2:int,flag3:int,flag4:int,flag5:int,flag6:int,flag7:int,flag8:int,flag9:int,flag10:int,notes:varchar(100),capacity:int,numberofproductsallocated:int,salesallocated:decimal(15,3),costallocated:decimal(15,3),movementallocated:decimal(15,3),marginallocated:decimal(15,3),annualprofitallocated:decimal(15,3),status1:varchar(100),datecreated:string,datemodified:string,datepending:string,dateeffective:string,datefinished:string,date3:string,
2025-06-10 09:06:01,002 - INFO - TABLE CREATE: dbdateeffectiveto:string,dbversionkey:int,id:int,department:varchar(100),dbfamilykey:int,dbreplacekey:int,dbparentpgtemplatekey:int,abbrevname:varchar(100),numberofsections:int,linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),pendingdate:string,livedate:string,finisheddate:string,dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>) is different from the schema when this table was created by Spark SQL(struct<DBStatus:int,DBKey1:int,DBKey2:int,DBKey3:int,DBKey4:int,DBKey5:int,DBKey6:int,Name:string,DBKey:int,Width:decimal(15,3),Height:decimal(15,3),Depth:decimal(15,3),BackDepth:decimal(15,3),DrawBack:int,BaseWidth:decimal(15,3),BaseHeight:decimal(15,3),BaseDepth:decimal(15,3),DrawBase:int,BaseColor:int,DrawNotches:int,NotchOffset:decimal(15,3),NotchSpacing:decimal(15,3),DoubleNotches:int,NotchColor:int,NotchMarks:int,DrawPegs:int,DrawPegholes:int,MerchXMin:int,MerchXMax:int,MerchXUprights:int,MerchXCaps:int,MerchXPlacement:int,MerchXNumber:int,MerchXSiz
2025-06-10 09:06:04,005 - INFO - TABLE CREATE: 3),Value39:decimal(15,3),Value40:decimal(15,3),Value41:decimal(15,3),Value42:decimal(15,3),Value43:decimal(15,3),Value44:decimal(15,3),Value45:decimal(15,3),Value46:decimal(15,3),Value47:decimal(15,3),Value48:decimal(15,3),Value49:decimal(15,3),Value50:decimal(15,3),Flag1:int,Flag2:int,Flag3:int,Flag4:int,Flag5:int,Flag6:int,Flag7:int,Flag8:int,Flag9:int,Flag10:int,Notes:string,Capacity:int,NumberOfProductsAllocated:int,SalesAllocated:decimal(15,3),CostAllocated:decimal(15,3),MovementAllocated:decimal(15,3),MarginAllocated:decimal(15,3),AnnualProfitAllocated:decimal(15,3),Status1:string,DateCreated:string,DateModified:string,DatePending:string,DateEffective:string,DateFinished:string,Date3:string,DBDateEffectiveTo:string,DBVersionKey:int,ID:int,Department:string,DBFamilyKey:int,DBReplaceKey:int,DBParentPGTemplateKey:int,AbbrevName:string,NumberOfSections:int,Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),PendingDate:string,LiveDate:string,FinishedDate:string,DBTime:string,int_filename:string,in
2025-06-10 09:06:05,006 - INFO - SQL: t_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:06:05,007 - INFO - SQL: 25/06/10 08:05:16 WARN HiveExternalCatalog: The table schema given by Hive metastore(struct<dbparentplanogramkey:int,type:int,name:varchar(100),dbkey:int,x:decimal(15,3),width:decimal(15,3),y:decimal(15,3),height:decimal(15,3),z:decimal(15,3),depth:decimal(15,3),slope:decimal(15,3),angle:decimal(15,3),numberofpositions:int,numberofdividers:int,linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),columns:int,rows:int,locationid:int,segment:int,firstsegment:int,lastsegment:int,weightcapacity:decimal(15,3),notch:int,dbparentfixturekey:int,dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>) is different from the sch
2025-06-10 09:06:06,009 - INFO - TABLE CREATE: ema when this table was created by Spark SQL(struct<DBParentPlanogramKey:int,Type:int,Name:string,DBKey:int,X:decimal(15,3),Width:decimal(15,3),Y:decimal(15,3),Height:decimal(15,3),Z:decimal(15,3),Depth:decimal(15,3),Slope:decimal(15,3),Angle:decimal(15,3),NumberOfPositions:int,NumberOfDividers:int,Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),Columns:int,Rows:int,LocationID:int,Segment:int,FirstSegment:int,LastSegment:int,WeightCapacity:decimal(15,3),Notch:int,DBParentFixtureKey:int,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:06:07,011 - INFO - TABLE CREATE: 28:decimal(15,3),value29:decimal(15,3),casemultiple:decimal(15,3),dayssupply:decimal(15,3),deliveryschedule:varchar(100),recommendedfacings:int,numberofpositions:int,clustername:varchar(100),value35:decimal(15,3),capacityunrestricted:int,dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>) is different from the schema when this table was created by Spark SQL(struct<DBParentPlanogramKey:int,DBParentProductKey:int,DBKey:int,DFacings:int,Units:int,Capacity:int,Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),Sales:decimal(15,3),Value26:decimal(15,3),Value27:decimal(15,3),Value28:decimal(15,3),Value29:decimal(15,3),CaseMultiple:decimal(15,3),DaysSupply:decimal(15,3),DeliverySchedule:string,RecommendedFacings:int,NumberOfPositions:int,ClusterName:string,Value35:decimal(15,3),CapacityUnrestricted:int,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>). We have to fall back to the table schem
2025-06-10 09:06:25,020 - INFO - TABLE CREATE: 25/06/10 08:05:21 INFO SparkContext: Created broadcast 112 from sql at ExecuteStatement.scala:99
2025-06-10 09:06:26,021 - INFO - TABLE CREATE: archar(100),desc11:varchar(100),desc12:varchar(100),desc13:varchar(100),desc17:varchar(100),desc29:varchar(100),desc44:varchar(100),pendingdate:string,livedate:string,status1:varchar(100),dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>) is different from the schema when this table was created by Spark SQL(struct<DBStatus:int,DBKey1:int,DBKey2:int,DBKey3:int,DBKey4:int,DBKey5:int,Name:string,DBKey:int,Width:decimal(15,3),Depth:decimal(15,3),NumberOfFixtures:int,Desc1:string,Desc2:string,Desc11:string,Desc12:string,Desc13:string,Desc17:string,Desc29:string,Desc44:string,PendingDate:string,LiveDate:string,Status1:string,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:06:28,023 - INFO - SUCCESS: 25/06/10 08:05:23 INFO DAGScheduler: Job 85 finished: sql at ExecuteStatement.scala:99, took 0.303923 s
2025-06-10 09:06:28,024 - INFO - SUCCESS: 25/06/10 08:05:23 INFO SQLOperationListener: Finished stage: Stage(239, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 101; Took: 301 msec
2025-06-10 09:06:31,026 - INFO - TABLE CREATE: 25/06/10 08:05:23 INFO SparkContext: Created broadcast 115 from sql at ExecuteStatement.scala:99
2025-06-10 09:06:32,028 - INFO - TABLE CREATE: ore(struct<dbtime:string,dbparentfloorplankey:int,dbparentfixturekey:int,dbparentplanogramkey:int,dbkey:int,x:decimal(15,3),width:decimal(15,3),z:decimal(15,3),height:decimal(15,3),y:decimal(15,3),depth:decimal(15,3),rankx:int,rankz:int,ranky:int,merchwidth:int,merchdepth:int,desc3:varchar(100),locationid:int,overidingwidth:decimal(15,3),linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),department:decimal(15,3),heightoveride:decimal(15,3),depthoveride:decimal(15,3),partid:int,angle:decimal(15,3),segmentstart:int,segmentend:int,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_flr_key:string>) is different from the schema when this table was created by Spark SQL(struct<DBTime:string,DBParentFloorplanKey:int,DBParentFixtureKey:int,DBParentPlanogramKey:int,DBKey:int,X:decimal(15,3),Width:decimal(15,3),Z:decimal(15,3),Height:decimal(15,3),Y:decimal(15,3),Depth:decimal(15,3),RankX:int,RankZ:int,RankY:int,MerchWidth:int,MerchDepth:int,Desc3:string,LocationID:int,OveridingWidt
2025-06-10 09:06:33,029 - INFO - SQL: h:decimal(15,3),Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),Department:decimal(15,3),HeightOveride:decimal(15,3),DepthOveride:decimal(15,3),PartID:int,Angle:decimal(15,3),SegmentStart:int,SegmentEnd:int,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_flr_key:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:06:34,030 - INFO - SUCCESS: 25/06/10 08:05:28 INFO SQLOperationListener: Finished stage: Stage(241, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 100; Took: 3189
2025-06-10 09:06:35,032 - INFO - SUCCESS: 25/06/10 08:05:28 INFO DAGScheduler: Job 87 finished: sql at ExecuteStatement.scala:99, took 3.191371 s
2025-06-10 09:06:38,034 - INFO - TABLE CREATE: 25/06/10 08:05:28 INFO SparkContext: Created broadcast 118 from sql at ExecuteStatement.scala:99
2025-06-10 09:06:42,037 - INFO - TABLE CREATE: lag7:int,flag8:int,flag9:int,flag10:int,notes:varchar(100),capacity:int,numberofproductsallocated:int,salesallocated:decimal(15,3),costallocated:decimal(15,3),movementallocated:decimal(15,3),marginallocated:decimal(15,3),annualprofitallocated:decimal(15,3),status1:varchar(100),datecreated:string,datemodified:string,datepending:string,dateeffective:string,datefinished:string,date3:string,dbdateeffectiveto:string,dbversionkey:int,id:int,department:varchar(100),dbfamilykey:int,dbreplacekey:int,dbparentpgtemplatekey:int,abbrevname:varchar(100),numberofsections:int,linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),pendingdate:string,livedate:string,finisheddate:string,dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>) is different from the schema when this table was created by Spark SQL(struct<DBStatus:int,DBKey1:int,DBKey2:int,DBKey3:int,DBKey4:int,DBKey5:int,DBKey6:int,Name:string,DBKey:int,Width:decimal(15,3),Height:decimal(15,3),Depth:decimal(15,3),Ba
2025-06-10 09:06:45,039 - INFO - TABLE CREATE: ue21:decimal(15,3),Value22:decimal(15,3),Value23:decimal(15,3),Value24:decimal(15,3),Value25:decimal(15,3),Value26:decimal(15,3),Value27:decimal(15,3),Value28:decimal(15,3),Value29:decimal(15,3),Value30:decimal(15,3),Value31:decimal(15,3),Value32:decimal(15,3),Value33:decimal(15,3),Value34:decimal(15,3),Value35:decimal(15,3),Value36:decimal(15,3),Value37:decimal(15,3),Value38:decimal(15,3),Value39:decimal(15,3),Value40:decimal(15,3),Value41:decimal(15,3),Value42:decimal(15,3),Value43:decimal(15,3),Value44:decimal(15,3),Value45:decimal(15,3),Value46:decimal(15,3),Value47:decimal(15,3),Value48:decimal(15,3),Value49:decimal(15,3),Value50:decimal(15,3),Flag1:int,Flag2:int,Flag3:int,Flag4:int,Flag5:int,Flag6:int,Flag7:int,Flag8:int,Flag9:int,Flag10:int,Notes:string,Capacity:int,NumberOfProductsAllocated:int,SalesAllocated:decimal(15,3),CostAllocated:decimal(15,3),MovementAllocated:decimal(15,3),MarginAllocated:decimal(15,3),AnnualProfitAllocated:decimal(15,3),Status1:string,DateCreated:string,DateModified:string,D
2025-06-10 09:06:46,040 - INFO - SUCCESS: atePending:string,DateEffective:string,DateFinished:string,Date3:string,DBDateEffectiveTo:string,DBVersionKey:int,ID:int,Department:string,DBFamilyKey:int,DBReplaceKey:int,DBParentPGTemplateKey:int,AbbrevName:string,NumberOfSections:int,Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),PendingDate:string,LiveDate:string,FinishedDate:string,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:06:47,042 - INFO - SUCCESS: 25/06/10 08:05:31 INFO SQLOperationListener: Finished stage: Stage(240, 0); Name: 'sql at ExecuteStatement
2025-06-10 09:06:50,044 - INFO - SUCCESS: 25/06/10 08:05:33 INFO SQLOperationListener: Finished stage: Stage(243, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 101; Took: 3272 msec
2025-06-10 09:06:50,045 - INFO - SUCCESS: 25/06/10 08:05:33 INFO DAGScheduler: Job 89 finished: sql at ExecuteStatement.sca
2025-06-10 09:06:54,048 - INFO - TABLE CREATE: 25/06/10 08:05:33 INFO SparkContext: Created broadcast 121 from sql at ExecuteStatement.scala:99
2025-06-10 09:06:55,049 - INFO - TABLE CREATE: archar(100),desc29:varchar(100),desc44:varchar(100),pendingdate:string,livedate:string,status1:varchar(100),dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>) is different from the schema when this table was created by Spark SQL(struct<DBStatus:int,DBKey1:int,DBKey2:int,DBKey3:int,DBKey4:int,DBKey5:int,Name:string,DBKey:int,Width:decimal(15,3),Depth:decimal(15,3),NumberOfFixtures:int,Desc1:string,Desc2:string,Desc11:string,Desc12:string,Desc13:string,Desc17:string,Desc29:string,Desc44:string,PendingDate:string,LiveDate:string,Status1:string,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:06:56,050 - INFO - TABLE CREATE: 25/06/10 08:05:34 INFO SparkContext: Created broadcast 123 from sql at ExecuteStatement.scala:99
2025-06-10 09:06:57,052 - INFO - TABLE CREATE: rplankey:int,dbparentfixturekey:int,dbparentplanogramkey:int,dbkey:int,x:decimal(15,3),width:decimal(15,3),z:decimal(15,3),height:decimal(15,3),y:decimal(15,3),depth:decimal(15,3),rankx:int,rankz:int,ranky:int,merchwidth:int,merchdepth:int,desc3:varchar(100),locationid:int,overidingwidth:decimal(15,3),linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),department:decimal(15,3),heightoveride:decimal(15,3),depthoveride:decimal(15,3),partid:int,angle:decimal(15,3),segmentstart:int,segmentend:int,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_flr_key:string>) is different from the schema when this table was created by Spark SQL(struct<DBTime:string,DBParentFloorplanKey:int,DBParentFixtureKey:int,DBParentPlanogramKey:int,DBKey:int,X:decimal(15,3),Width:decimal(15,3),Z:decimal(15,3),Height:decimal(15,3),Y:decimal(15,3),Depth:decimal(15,3),RankX:int,RankZ:int,RankY:int,MerchWidth:int,MerchDepth:int,Desc3:string,LocationID:int,OveridingWidth:decimal(15,3),Linear:decimal(15,3),
2025-06-10 09:06:58,053 - INFO - SQL: Square:decimal(15,3),Cubic:decimal(15,3),Department:decimal(15,3),HeightOveride:decimal(15,3),DepthOveride:decimal(15,3),PartID:int,Angle:decimal(15,3),SegmentStart:int,SegmentEnd:int,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_flr_key:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:06:59,055 - INFO - TABLE CREATE: 25/06/10 08:05:36 INFO SparkContext: Created broadcast 125 from sql at ExecuteStatement.scala:99
2025-06-10 09:07:02,056 - INFO - TABLE CREATE: 31:decimal(15,3),value32:decimal(15,3),value33:decimal(15,3),value34:decimal(15,3),value35:decimal(15,3),value36:decimal(15,3),value37:decimal(15,3),value38:decimal(15,3),value39:decimal(15,3),value40:decimal(15,3),value41:decimal(15,3),value42:decimal(15,3),value43:decimal(15,3),value44:decimal(15,3),value45:decimal(15,3),value46:decimal(15,3),value47:decimal(15,3),value48:decimal(15,3),value49:decimal(15,3),value50:decimal(15,3),flag1:int,flag2:int,flag3:int,flag4:int,flag5:int,flag6:int,flag7:int,flag8:int,flag9:int,flag10:int,notes:varchar(100),capacity:int,numberofproductsallocated:int,salesallocated:decimal(15,3),costallocated:decimal(15,3),movementallocated:decimal(15,3),marginallocated:decimal(15,3),annualprofitallocated:decimal(15,3),status1:varchar(100),datecreated:string,datemodified:string,datepending:string,dateeffective:string,datefinished:string,date3:string,dbdateeffectiveto:string,dbversionkey:int,id:int,department:varchar(100),dbfamilykey:int,dbreplacekey:int,dbparentpgtemplatekey:int,abbrev
2025-06-10 09:07:03,057 - INFO - TABLE CREATE: name:varchar(100),numberofsections:int,linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),pendingdate:string,livedate:string,finisheddate:string,dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>) is different from the schema when this table was created by Spark SQL(struct<DBStatus:int,DBKey1:int,DBKey2:int,DBKey3:int,DBKey4:int,DBKey5:int,DBKey6:int,Name:string,DBKey:int,Width:decimal(15,3),Height:decimal(15,3),Depth:decimal(15,3),BackDepth:decimal(15,3),DrawBack:int,BaseWidth:decimal(15,3),BaseHeight:decimal(15,3),BaseDepth:decimal(15,3),DrawBase:int,BaseColor:int,DrawNotches:int,NotchOffset:decimal(15,3),NotchSpacing:decimal(15,3),DoubleNotches:int,NotchColor:int,NotchMarks:int,DrawPegs:int,DrawPegholes:int,MerchXMin:int,MerchXMax:int,MerchXUprights:int,MerchXCaps:int,MerchXPlacement:int,MerchXNumber:int,MerchXSize:int,MerchXDirection:int,MerchXSqueeze:int,MerchYMin:int,MerchYMax:int,MerchYUprights:int,MerchYCaps:int,MerchYPlacement:int,MerchYNumber
2025-06-10 09:07:06,060 - INFO - TABLE CREATE: ue45:decimal(15,3),Value46:decimal(15,3),Value47:decimal(15,3),Value48:decimal(15,3),Value49:decimal(15,3),Value50:decimal(15,3),Flag1:int,Flag2:int,Flag3:int,Flag4:int,Flag5:int,Flag6:int,Flag7:int,Flag8:int,Flag9:int,Flag10:int,Notes:string,Capacity:int,NumberOfProductsAllocated:int,SalesAllocated:decimal(15,3),CostAllocated:decimal(15,3),MovementAllocated:decimal(15,3),MarginAllocated:decimal(15,3),AnnualProfitAllocated:decimal(15,3),Status1:string,DateCreated:string,DateModified:string,DatePending:string,DateEffective:string,DateFinished:string,Date3:string,DBDateEffectiveTo:string,DBVersionKey:int,ID:int,Department:string,DBFamilyKey:int,DBReplaceKey:int,DBParentPGTemplateKey:int,AbbrevName:string,NumberOfSections:int,Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),PendingDate:string,LiveDate:string,FinishedDate:string,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_dbkey:string>). We have to fall back to the table schema from Hive metastore which is not
2025-06-10 09:07:07,061 - INFO - TABLE CREATE: 25/06/10 08:05:37 INFO SparkContext: Created broadcast 127 from sql at ExecuteStatement.scala:99
2025-06-10 09:07:09,063 - INFO - SUCCESS: 25/06/10 08:05:41 INFO SQLOperationListener: Finished stage: Stage(242, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 10; Took: 12810 msec
2025-06-10 09:07:12,065 - INFO - SUCCESS: 25/06/10 08:05:48 INFO SQLOperationListener: Finished stage: Stage(245, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 17; Took: 13914 msec
2025-06-10 09:07:15,067 - INFO - SUCCESS: 25/06/10 08:05:50 INFO SQLOperationListener: Finished stage: Stage(248, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 100; Took: 11205 msec
2025-06-10 09:07:15,067 - INFO - SUCCESS: 25/06/10 08:05:50 INFO DAGScheduler: Job 94 finished: sql at ExecuteStatement.scala:99, took 11.206832 s
2025-06-10 09:07:19,070 - INFO - TABLE CREATE: 25/06/10 08:05:50 INFO SparkContext: Created broadcast 130 from sql at ExecuteStatement.scala:99
2025-06-10 09:07:20,072 - INFO - SUCCESS: 25/06/10 08:05:55 INFO SQLOperationListener: Finished stage: Stage(246, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 20; Took: 19358 msec
2025-06-10 09:07:23,074 - INFO - SUCCESS: 25/06/10 08:05:56 INFO DAGScheduler: Job 96 finished: sql at ExecuteStatement.scala:99, took 3.849000 s
2025-06-10 09:07:23,075 - INFO - SUCCESS: 25/06/10 08:05:56 INFO SQLOperationListener: Finished stage: Stage(250, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 101; Took: 3846 msec
2025-06-10 09:07:27,079 - INFO - TABLE CREATE: 25/06/10 08:05:56 INFO SparkContext: Created broadcast 133 from sql at ExecuteStatement.scala:99
2025-06-10 09:07:27,082 - INFO - SUCCESS: 25/06/10 08:05:58 INFO SQLOperationListener: Finished stage: Stage(244, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 12; Took: 24938 msec
2025-06-10 09:07:30,088 - INFO - TABLE CREATE: 25/06/10 08:05:58 WARN HiveExternalCatalog: The table schema given by Hive metastore(struct<dbparentplanogramkey:int,type:int,name:varchar(100),dbkey:int,x:decimal(15,3),width:decimal(15,3),y:decimal(15,3),height:decimal(15,3),z:decimal(15,3),depth:decimal(15,3),slope:decimal(15,3),angle:decimal(15,3),numberofpositions:int,numberofdividers:int,linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),columns:int,rows:int,locationid:int,segment:int,firstsegment:int,lastsegment:int,weightcapacity:decimal(15,3),notch:int,dbparentfixturekey:int,dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>) is different from the schema when this table was created by Spark SQL(struct<DBParentPlanogramKey:int,Type:int,Name:
2025-06-10 09:07:31,093 - INFO - SQL: string,DBKey:int,X:decimal(15,3),Width:decimal(15,3),Y:decimal(15,3),Height:decimal(15,3),Z:decimal(15,3),Depth:decimal(15,3),Slope:decimal(15,3),Angle:decimal(15,3),NumberOfPositions:int,NumberOfDividers:int,Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),Columns:int,Rows:int,LocationID:int,Segment:int,FirstSegment:int,LastSegment:int,WeightCapacity:decimal(15,3),Notch:int,DBParentFixtureKey:int,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:07:32,098 - INFO - SUCCESS: 25/06/10 08:05:59 INFO SQLOperationListener: Finished stage: Stage(247, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; nu
2025-06-10 09:07:35,103 - INFO - SUCCESS: 25/06/10 08:06:00 INFO SQLOperationListener: Finished stage: Stage(252, 0); Nam
2025-06-10 09:07:36,107 - INFO - SUCCESS: 25/06/10 08:06:00 INFO DAGScheduler: Job 98 finished: sql at ExecuteStatement.scala:99, took 1.982305 s
2025-06-10 09:07:39,111 - INFO - TABLE CREATE: 25/06/10 08:06:00 INFO SparkContext: Created broadcast 136 from sql at ExecuteStatement.scala:99
2025-06-10 09:07:39,116 - INFO - SUCCESS: 25/06/10 08:06:02 INFO SQLOperationListener: Finished stage: Stage(249, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 50; Took: 11813 msec
2025-06-10 09:07:43,120 - INFO - TABLE CREATE: ltiple:decimal(15,3),dayssupply:decimal(15,3),deliveryschedule:varchar(100),recommendedfacings:int,numberofpositions:int,clustername:varchar(100),value35:decimal(15,3),capacityunrestricted:int,dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>) is different from the schema when this table was created by Spark SQL(struct<DBParentPlanogramKey:int,DBParentProductKey:int,DBKey:int,DFacings:int,Units:int,Capacity:int,Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),Sales:decimal(15,3),Value26:decimal(15,3),Value27:decimal(15,3),Value28:decimal(15,3),Value29:decimal(15,3),CaseMultiple:decimal(15,3),DaysSupply:decimal(15,3),DeliverySchedule:string,RecommendedFacings:int,NumberOfPositions:int,ClusterName:string,Value35:decimal(15,3),CapacityUnrestricted:int,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>). We have to fall back to the table schema from Hive metastore which is not case prese
2025-06-10 09:07:45,125 - INFO - SUCCESS: 25/06/10 08:06:05 INFO SQLOperationListener: Finished stage: Stage(254, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 100; Took: 1858 msec
2025-06-10 09:07:45,126 - INFO - SUCCESS: 25/06/10 08:06:05 INFO DAGScheduler: Job 100 finished: sql at ExecuteStatement.scala:99, took 1.860251 s
2025-06-10 09:07:49,128 - INFO - TABLE CREATE: 25/06/10 08:06:05 INFO SparkContext: Created broadcast 139 from sql at ExecuteStatement.scala:99
2025-06-10 09:07:49,129 - INFO - SUCCESS: 25/06/10 08:06:05 INFO SQLOperationListener: Finished stage: Stage(251, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 50; Took: 9073 msec
2025-06-10 09:07:53,134 - INFO - TABLE CREATE: ntext: Created broadcast 141 from sql at ExecuteStatement.scala:99
2025-06-10 09:07:53,135 - INFO - SUCCESS: 25/06/10 08:06:07 INFO SQLOperationListener: Finished stage: Stage(253, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 50; Took: 6076 msec
2025-06-10 09:07:56,140 - INFO - TABLE CREATE: metastore(struct<dbparentplanogramkey:int,type:int,name:varchar(100),dbkey:int,x:decimal(15,3),width:decimal(15,3),y:decimal(15,3),height:decimal(15,3),z:decimal(15,3),depth:decimal(15,3),slope:decimal(15,3),angle:decimal(15,3),numberofpositions:int,numberofdividers:int,linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),columns:int,rows:int,locationid:int,segment:int,firstsegment:int,lastsegment:int,weightcapacity:decimal(15,3),notch:int,dbparentfixturekey:int,dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>) is different from the schema when this table was created by Spark SQL(struct<DBParentPlanogramKey:int,Type:int,Name:string,DBKey:int,X:decimal(15,3),Width:decimal(15,3),Y:decimal(15,3),Height:decimal(15,3),Z:decimal(15,3),Depth:decimal(15,3),Slope:decimal(15,3),Angle:decimal(15,3),NumberOfPositions:int,NumberOfDividers:int,Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),Columns:int,Rows:int,LocationID:int,Segment:int,Firs
2025-06-10 09:07:57,142 - INFO - SQL: tSegment:int,LastSegment:int,WeightCapacity:decimal(15,3),Notch:int,DBParentFixtureKey:int,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:07:58,143 - INFO - TABLE CREATE: 25/06/10 08:06:08 INFO SparkContext: Created broadcast 143 from sql at ExecuteStatement.scala:99
2025-06-10 09:07:58,146 - INFO - TABLE CREATE: 25/06/10 08:06:09 WARN HiveExternalCatalog: The table schema given by Hive metastore(struct<dbtime:string,dbparentfloorplankey:int,dbparentfixturekey:int,dbparentplanogramkey:int,dbkey:int,x:decimal(15,3),width:decimal(15,3),z:decimal(15,3),height:decimal(15,3),y:decimal(15,3),depth:decimal(15,3),rankx:int,rankz:int,ranky:int,merchwidth:int,merchdepth:int,desc3:varchar(100),locationid:int,overidingwidth:decimal(15,3),linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),department:decimal(15,3),heightoveride:decimal(15,3),depthoveride:decimal(15,3),partid:int,angle:decimal(15,3),segmentstart:int,segmentend:int,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_flr_key:string>) is different from the schema when this table was created by Spark SQL(st
2025-06-10 09:07:59,147 - INFO - SQL: ruct<DBTime:string,DBParentFloorplanKey:int,DBParentFixtureKey:int,DBParentPlanogramKey:int,DBKey:int,X:decimal(15,3),Width:decimal(15,3),Z:decimal(15,3),Height:decimal(15,3),Y:decimal(15,3),Depth:decimal(15,3),RankX:int,RankZ:int,RankY:int,MerchWidth:int,MerchDepth:int,Desc3:string,LocationID:int,OveridingWidth:decimal(15,3),Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),Department:decimal(15,3),HeightOveride:decimal(15,3),DepthOveride:decimal(15,3),PartID:int,Angle:decimal(15,3),SegmentStart:int,SegmentEnd:int,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_flr_key:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:07:59,147 - INFO - SUCCESS: 25/06/10 08:06:09 INFO SQLOperationListener: Finished stage: Stage(255, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 50; Took: 3991 msec
2025-06-10 09:08:03,150 - INFO - TABLE CREATE: 25/06/10 08:06:09 INFO SparkContext: Created broadcast 145 from sql at ExecuteStatement.scala:99
2025-06-10 09:08:03,151 - INFO - SUCCESS: 25/06/10 08:06:10 INFO SQLOperationListener: Finished stage: Stage(256, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 50; Took: 3390 msec
2025-06-10 09:08:07,157 - INFO - TABLE CREATE: 25/06/10 08:06:11 INFO SparkContext: Created broadcast 147 from sql at ExecuteStatement.scala:99
2025-06-10 09:08:07,160 - INFO - SUCCESS: 25/06/10 08:06:11 INFO SQLOperationListener: Finished stage: Stage(257, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 50; Took: 3009 msec
2025-06-10 09:08:10,164 - INFO - TABLE CREATE: 25/06/10 08:06:12 WARN HiveExternalCatalog: The table schema given by Hive metastore(struct<dbparentplanogramkey:int,type:int,name:varchar(100),dbkey:int,x:decimal(15,3),width:decimal(15,3),y:decimal(15,3),height:decimal(15,3),z:decimal(15,3),depth:decimal(15,3),slope:decimal(15,3),angle:decimal(15,3),numberofpositions:int,numberofdividers:int,linear:decimal(15,3),square:decimal(15,3),cubic:decimal(15,3),columns:int,rows:int,locationid:int,segment:int,firstsegment:int,lastsegment:int,weightcapacity:decimal(15,3),notch:int,dbparentfixturekey:int,dbtime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>) is different from the schema when this table was created by Spark SQL(struct<DBParentPlanogramKey:int,Typ
2025-06-10 09:08:11,165 - INFO - SQL: e:int,Name:string,DBKey:int,X:decimal(15,3),Width:decimal(15,3),Y:decimal(15,3),Height:decimal(15,3),Z:decimal(15,3),Depth:decimal(15,3),Slope:decimal(15,3),Angle:decimal(15,3),NumberOfPositions:int,NumberOfDividers:int,Linear:decimal(15,3),Square:decimal(15,3),Cubic:decimal(15,3),Columns:int,Rows:int,LocationID:int,Segment:int,FirstSegment:int,LastSegment:int,WeightCapacity:decimal(15,3),Notch:int,DBParentFixtureKey:int,DBTime:string,int_filename:string,int_insert_dt:timestamp,int_cntr_code:string,part_col_par_spc_key:string>). We have to fall back to the table schema from Hive metastore which is not case preserving.
2025-06-10 09:08:11,167 - INFO - SUCCESS: 25/06/10 08:06:12 INFO SQLOperationListener: Finished stage: Stage(258, 0); Name: 'sql at ExecuteStatement.scala:99'; Status: succeeded; numTasks: 50; Took: 2447 msec
2025-06-10 09:08:15,171 - INFO - TABLE CREATE: 25/06/10 08:06:12 INFO SparkContext: Created broadcast 149 from sql at ExecuteStatement.scala:99
2025-06-10 09:08:18,178 - INFO - SUCCESS: 25/06/10 08:06:13 INFO SQLOperationListener: Finished stage: Stage(259, 0); Name: 'sql at ExecuteStatement.scala:99'; Statu
2025-06-10 09:08:20,182 - INFO - ============================================================
2025-06-10 09:08:20,183 - INFO - TIMING: Total execution time: 203.15 seconds
2025-06-10 09:08:20,185 - INFO - EXIT STATUS: 0
2025-06-10 09:08:20,186 - INFO - RESULT: Script finished successfully!
2025-06-10 09:08:20,188 - INFO - VERIFICATION: Checking table creation...
2025-06-10 09:08:26,136 - INFO - VERIFICATION 1: 0: ******************************************> SHOW TABLES IN sch_analysts LIKE "*srd*";

+---------------+----------------------------------------------------+--------------+
|   namespace   |       ...
2025-06-10 09:08:26,137 - INFO - SUCCESS: Table found in database!
2025-06-10 09:08:32,168 - INFO - VERIFICATION 2: 0: ******************************************> SELECT COUNT(*) FROM sch_analysts.tbl_srd_planogram_analysis;

+-----------+
| count(1)  |
+-----------+
| 6549922   |
+-----------+
0: jdbc:hive2://cep-...
2025-06-10 09:08:32,170 - INFO - SUCCESS: Table is accessible and contains data!
2025-06-10 09:08:32,171 - INFO - CONNECTION: SSH connection closed
2025-06-10 09:28:25,709 - INFO - Connected (version 2.0, client OpenSSH_8.0)
2025-06-10 09:28:26,278 - INFO - Auth banner: b'Authorized uses only. All activity may be monitored and reported.\n'
2025-06-10 09:28:26,279 - INFO - Authentication (password) successful!
