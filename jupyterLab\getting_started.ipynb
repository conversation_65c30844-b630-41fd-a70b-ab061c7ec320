{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Getting Started with <PERSON><PERSON> on Notebooks\n", "\n", "!!! important \"Supported Python versions\"\n", "\n", "    Taipy requires **Python 3.8** or newer.\n", "\n", "Welcome to the **Getting Started** guide for <PERSON><PERSON>. This tour shows you how to create an entire application using \n", "the two components of Taipy:\n", "\n", "- **Graphical User Interface builder** (Taipy GUI): allows any Python developer to create a complex and interactive GUI.\n", "\n", "- **Scenario Management** (Taipy Core): implements a modern backend for any data-driven application based on your business case.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_00/imd_end_interface.png width=700>\n", "</div>\n", "\n", "\n", "You can use Taipy GUI without Taipy Core and vice-versa. However, as you will see, they are incredibly efficient \n", "when combined.\n", "\n", "Each step of the **\"Getting Started\"** will focus on basic concepts of *Taipy*. Note that every step is dependent on \n", "the code of the previous one. After completing the last step, you will have the skills to develop your own Tai<PERSON> \n", "application. \n", "\n", "## Before we begin\n", "\n", "Three packages have to be installed:\n", "\n", " 1. **Taipy** package, it requires Python 3.8 or newer;\n", "\n", " 2. **scikit-learn**: A Machine-Learning package that will be used in the Getting Started user code;\n", "\n", " 3. **statsmodels**: Another package for statistics also used in the user code.\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting taipy\n", "  Downloading taipy-3.0.0.tar.gz (280 kB)\n", "     -------------------------------------- 280.8/280.8 kB 2.5 MB/s eta 0:00:00\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: scikit-learn in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (0.24.2)\n", "Requirement already satisfied: statsmodels in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (0.12.2)\n", "Collecting cookiecutter<2.2,>=2.1.1 (from taipy)\n", "  Downloading cookiecutter-2.1.1-py2.py3-none-any.whl (36 kB)\n", "Collecting taipy-gui<3.1,>=3.0 (from taipy)\n", "  Downloading taipy-gui-3.0.0.tar.gz (2.6 MB)\n", "     ---------------------------------------- 2.6/2.6 MB 10.4 MB/s eta 0:00:00\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Collecting taipy-rest<3.1,>=3.0 (from taipy)\n", "  Downloading taipy-rest-3.0.0.tar.gz (24 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Collecting taipy-templates<3.1,>=3.0 (from taipy)\n", "  Downloading taipy-templates-3.0.0.tar.gz (16 kB)\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: numpy>=1.13.3 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from scikit-learn) (1.26.1)\n", "Requirement already satisfied: scipy>=0.19.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from scikit-learn) (1.7.1)\n", "Requirement already satisfied: joblib>=0.11 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from scikit-learn) (1.1.0)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from scikit-learn) (2.2.0)\n", "Requirement already satisfied: pandas>=0.21 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from statsmodels) (2.1.1)\n", "Requirement already satisfied: patsy>=0.5 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from statsmodels) (0.5.2)\n", "Requirement already satisfied: binaryornot>=0.4.4 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from cookiecutter<2.2,>=2.1.1->taipy) (0.4.4)\n", "Requirement already satisfied: Jinja2<4.0.0,>=2.7 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from cookiecutter<2.2,>=2.1.1->taipy) (3.1.2)\n", "Requirement already satisfied: click<9.0.0,>=7.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from cookiecutter<2.2,>=2.1.1->taipy) (8.0.3)\n", "Requirement already satisfied: pyyaml>=5.3.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from cookiecutter<2.2,>=2.1.1->taipy) (6.0)\n", "Requirement already satisfied: jinja2-time>=0.2.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from cookiecutter<2.2,>=2.1.1->taipy) (0.2.0)\n", "Requirement already satisfied: python-slugify>=4.0.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from cookiecutter<2.2,>=2.1.1->taipy) (5.0.2)\n", "Requirement already satisfied: requests>=2.23.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from cookiecutter<2.2,>=2.1.1->taipy) (2.31.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pandas>=0.21->statsmodels) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pandas>=0.21->statsmodels) (2021.3)\n", "Requirement already satisfied: tzdata>=2022.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pandas>=0.21->statsmodels) (2023.3)\n", "Requirement already satisfied: six in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from patsy>=0.5->statsmodels) (1.16.0)\n", "Collecting numpy>=1.13.3 (from scikit-learn)\n", "  Downloading numpy-1.22.4-cp39-cp39-win_amd64.whl (14.7 MB)\n", "     ---------------------------------------- 14.7/14.7 MB 2.6 MB/s eta 0:00:00\n", "Collecting flask<3.1,>=3.0.0 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading flask-3.0.0-py3-none-any.whl.metadata (3.6 kB)\n", "Collecting flask-cors<5.0,>=4.0.0 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading Flask_Cors-4.0.0-py2.py3-none-any.whl.metadata (5.4 kB)\n", "Collecting flask-socketio<6.0,>=5.3.6 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading Flask_SocketIO-5.3.6-py3-none-any.whl.metadata (2.6 kB)\n", "Collecting markdown<4.0,>=3.4.4 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading Markdown-3.5.2-py3-none-any.whl.metadata (7.0 kB)\n", "Collecting python-dotenv<1.1,>=1.0.0 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading python_dotenv-1.0.0-py3-none-any.whl (19 kB)\n", "Requirement already satisfied: tzlocal<5.0,>=3.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from taipy-gui<3.1,>=3.0->taipy) (4.1)\n", "Collecting gevent<24.0,>=23.7.0 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading gevent-23.9.1-cp39-cp39-win_amd64.whl.metadata (13 kB)\n", "Collecting gevent-websocket<0.11,>=0.10.1 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading gevent_websocket-0.10.1-py3-none-any.whl (22 kB)\n", "Requirement already satisfied: kthread<0.3,>=0.2.3 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from taipy-gui<3.1,>=3.0->taipy) (0.2.3)\n", "Collecting taipy-config<3.1,>=3.0 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading taipy-config-3.0.0.tar.gz (28 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Requirement already satisfied: gitignore-parser<0.2,>=0.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from taipy-gui<3.1,>=3.0->taipy) (0.1.9)\n", "Collecting simple-websocket<1.0,>=0.10.1 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading simple_websocket-0.10.1-py3-none-any.whl.metadata (1.3 kB)\n", "Collecting twisted<24.0,>=23.8.0 (from taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading twisted-23.10.0-py3-none-any.whl.metadata (9.5 kB)\n", "Collecting flask-restful<0.4,>=0.3.9 (from taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading Flask_RESTful-0.3.10-py2.py3-none-any.whl.metadata (1.0 kB)\n", "Collecting flask-migrate<4.0,>=3.1 (from taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading Flask_Migrate-3.1.0-py3-none-any.whl (20 kB)\n", "Collecting flask-marshmallow<0.15,>=0.14 (from taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading flask_marshmallow-0.14.0-py2.py3-none-any.whl (10 kB)\n", "Collecting marshmallow-sqlalchemy<0.29,>=0.25 (from taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading marshmallow_sqlalchemy-0.28.2-py2.py3-none-any.whl (16 kB)\n", "Requirement already satisfied: passlib<1.8,>=1.7.4 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from taipy-rest<3.1,>=3.0->taipy) (1.7.4)\n", "Collecting apispec<6.0,>=5.1 (from apispec[yaml]<6.0,>=5.1->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading apispec-5.2.2-py3-none-any.whl (29 kB)\n", "Collecting apispec-webframeworks<0.6,>=0.5.2 (from taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading apispec_webframeworks-0.5.2-py2.py3-none-any.whl (12 kB)\n", "Collecting taipy-core<3.1,>=3.0 (from taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading taipy-core-3.0.0.tar.gz (141 kB)\n", "     -------------------------------------- 141.3/141.3 kB 1.4 MB/s eta 0:00:00\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: chardet>=3.0.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from binaryornot>=0.4.4->cookiecutter<2.2,>=2.1.1->taipy) (3.0.4)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from click<9.0.0,>=7.0->cookiecutter<2.2,>=2.1.1->taipy) (0.4.4)\n", "Collecting Werkzeug>=3.0.0 (from flask<3.1,>=3.0.0->taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading werkzeug-3.0.1-py3-none-any.whl.metadata (4.1 kB)\n", "Collecting itsdangerous>=2.1.2 (from flask<3.1,>=3.0.0->taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading itsdangerous-2.1.2-py3-none-any.whl (15 kB)\n", "Collecting click<9.0.0,>=7.0 (from cookiecutter<2.2,>=2.1.1->taipy)\n", "  Downloading click-8.1.7-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting blinker>=1.6.2 (from flask<3.1,>=3.0.0->taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading blinker-1.7.0-py3-none-any.whl.metadata (1.9 kB)\n", "Requirement already satisfied: importlib-metadata>=3.6.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from flask<3.1,>=3.0.0->taipy-gui<3.1,>=3.0->taipy) (6.8.0)\n", "Collecting marshmallow>=2.0.0 (from flask-marshmallow<0.15,>=0.14->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading marshmallow-3.20.2-py3-none-any.whl.metadata (7.5 kB)\n", "Collecting Flask-SQLAlchemy>=1.0 (from flask-migrate<4.0,>=3.1->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading flask_sqlalchemy-3.1.1-py3-none-any.whl.metadata (3.4 kB)\n", "Collecting alembic>=0.7 (from flask-migrate<4.0,>=3.1->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading alembic-1.13.1-py3-none-any.whl.metadata (7.4 kB)\n", "Requirement already satisfied: aniso8601>=0.82 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from flask-restful<0.4,>=0.3.9->taipy-rest<3.1,>=3.0->taipy) (9.0.1)\n", "Collecting python-socketio>=5.0.2 (from flask-socketio<6.0,>=5.3.6->taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading python_socketio-5.11.0-py3-none-any.whl.metadata (3.2 kB)\n", "Requirement already satisfied: zope.event in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gevent<24.0,>=23.7.0->taipy-gui<3.1,>=3.0->taipy) (4.5.0)\n", "Requirement already satisfied: zope.interface in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gevent<24.0,>=23.7.0->taipy-gui<3.1,>=3.0->taipy) (5.4.0)\n", "Collecting greenlet>=2.0.0 (from gevent<24.0,>=23.7.0->taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading greenlet-3.0.3-cp39-cp39-win_amd64.whl.metadata (3.9 kB)\n", "Requirement already satisfied: cffi>=1.12.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gevent<24.0,>=23.7.0->taipy-gui<3.1,>=3.0->taipy) (1.14.6)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from Jinja2<4.0.0,>=2.7->cookiecutter<2.2,>=2.1.1->taipy) (2.1.3)\n", "Requirement already satisfied: arrow in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from jinja2-time>=0.2.0->cookiecutter<2.2,>=2.1.1->taipy) (1.3.0)\n", "Requirement already satisfied: SQLAlchemy<2.0,>=1.3.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from marshmallow-sqlalchemy<0.29,>=0.25->taipy-rest<3.1,>=3.0->taipy) (1.4.31)\n", "Requirement already satisfied: packaging>=21.3 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from marshmallow-sqlalchemy<0.29,>=0.25->taipy-rest<3.1,>=3.0->taipy) (23.2)\n", "Requirement already satisfied: text-unidecode>=1.3 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from python-slugify>=4.0.0->cookiecutter<2.2,>=2.1.1->taipy) (1.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests>=2.23.0->cookiecutter<2.2,>=2.1.1->taipy) (2.0.4)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests>=2.23.0->cookiecutter<2.2,>=2.1.1->taipy) (2.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests>=2.23.0->cookiecutter<2.2,>=2.1.1->taipy) (1.25.11)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests>=2.23.0->cookiecutter<2.2,>=2.1.1->taipy) (2021.10.8)\n", "Requirement already satisfied: wsproto in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from simple-websocket<1.0,>=0.10.1->taipy-gui<3.1,>=3.0->taipy) (1.2.0)\n", "Requirement already satisfied: toml<0.11,>=0.10 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from taipy-config<3.1,>=3.0->taipy-gui<3.1,>=3.0->taipy) (0.10.2)\n", "Collecting deepdiff<6.3,>=6.2 (from taipy-config<3.1,>=3.0->taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading deepdiff-6.2.3-py3-none-any.whl (73 kB)\n", "     -------------------------------------- 73.2/73.2 kB 806.7 kB/s eta 0:00:00\n", "Requirement already satisfied: pyarrow<11.0,>=10.0.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (10.0.1)\n", "Requirement already satisfied: networkx<3.0,>=2.6 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (2.6.3)\n", "Requirement already satisfied: openpyxl<3.2,>=3.1.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (3.1.2)\n", "Collecting modin<1.0,>=0.23.0 (from modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading modin-0.26.0-py3-none-any.whl.metadata (17 kB)\n", "Collecting pymongo<5.0,>=4.2.0 (from pymongo[srv]<5.0,>=4.2.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading pymongo-4.6.1-cp39-cp39-win_amd64.whl.metadata (22 kB)\n", "INFO: pip is looking at multiple versions of taipy-core to determine which version is compatible with other requirements. This could take a while.\n", "Collecting marshmallow-sqlalchemy<0.29,>=0.25 (from taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading marshmallow_sqlalchemy-0.28.1-py2.py3-none-any.whl (15 kB)\n", "Collecting SQLAlchemy>=1.3.0 (from marshmallow-sqlalchemy<0.29,>=0.25->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading SQLAlchemy-2.0.25-cp39-cp39-win_amd64.whl.metadata (9.8 kB)\n", "Collecting typing-extensions>=4.6.0 (from SQLAlchemy>=1.3.0->marshmallow-sqlalchemy<0.29,>=0.25->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading typing_extensions-4.9.0-py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: attrs>=21.3.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from twisted<24.0,>=23.8.0->taipy-gui<3.1,>=3.0->taipy) (23.1.0)\n", "Requirement already satisfied: automat>=0.8.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from twisted<24.0,>=23.8.0->taipy-gui<3.1,>=3.0->taipy) (22.10.0)\n", "Requirement already satisfied: constantly>=15.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from twisted<24.0,>=23.8.0->taipy-gui<3.1,>=3.0->taipy) (15.1.0)\n", "Requirement already satisfied: hyperlink>=17.1.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from twisted<24.0,>=23.8.0->taipy-gui<3.1,>=3.0->taipy) (21.0.0)\n", "Requirement already satisfied: incremental>=22.10.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from twisted<24.0,>=23.8.0->taipy-gui<3.1,>=3.0->taipy) (22.10.0)\n", "Requirement already satisfied: twisted-iocpsupport<2,>=1.0.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from twisted<24.0,>=23.8.0->taipy-gui<3.1,>=3.0->taipy) (1.0.2)\n", "Requirement already satisfied: pytz-deprecation-shim in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from tzlocal<5.0,>=3.0->taipy-gui<3.1,>=3.0->taipy) (0.1.0.post0)\n", "Collecting <PERSON><PERSON> (from alembic>=0.7->flask-migrate<4.0,>=3.1->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading Mako-1.3.0-py3-none-any.whl.metadata (2.9 kB)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from cffi>=1.12.2->gevent<24.0,>=23.7.0->taipy-gui<3.1,>=3.0->taipy) (2.20)\n", "Requirement already satisfied: ordered-set<4.2.0,>=4.0.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from deepdiff<6.3,>=6.2->taipy-config<3.1,>=3.0->taipy-gui<3.1,>=3.0->taipy) (4.1.0)\n", "Requirement already satisfied: or<PERSON><PERSON> in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from deepdiff<6.3,>=6.2->taipy-config<3.1,>=3.0->taipy-gui<3.1,>=3.0->taipy) (3.9.9)\n", "Requirement already satisfied: zipp>=0.5 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from importlib-metadata>=3.6.0->flask<3.1,>=3.0.0->taipy-gui<3.1,>=3.0->taipy) (3.6.0)\n", "Collecting fsspec>=2022.05.0 (from modin<1.0,>=0.23.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading fsspec-2023.12.2-py3-none-any.whl.metadata (6.8 kB)\n", "Requirement already satisfied: psutil>=5.8.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from modin<1.0,>=0.23.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (5.8.0)\n", "Requirement already satisfied: dask>=2.22.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (2022.2.0)\n", "Requirement already satisfied: distributed>=2.22.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (2021.10.0)\n", "Requirement already satisfied: et-xmlfile in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from openpyxl<3.2,>=3.1.2->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (1.1.0)\n", "Collecting dnspython<3.0.0,>=1.16.0 (from pymongo<5.0,>=4.2.0->pymongo[srv]<5.0,>=4.2.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading dnspython-2.4.2-py3-none-any.whl.metadata (4.9 kB)\n", "Collecting bidict>=0.21.0 (from python-socketio>=5.0.2->flask-socketio<6.0,>=5.3.6->taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading bidict-0.22.1-py3-none-any.whl (35 kB)\n", "Collecting python-engineio>=4.8.0 (from python-socketio>=5.0.2->flask-socketio<6.0,>=5.3.6->taipy-gui<3.1,>=3.0->taipy)\n", "  Downloading python_engineio-4.8.2-py3-none-any.whl.metadata (2.2 kB)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from zope.interface->gevent<24.0,>=23.7.0->taipy-gui<3.1,>=3.0->taipy) (60.6.0)\n", "Requirement already satisfied: types-python-dateutil>=2.8.10 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from arrow->jinja2-time>=0.2.0->cookiecutter<2.2,>=2.1.1->taipy) (*********)\n", "Requirement already satisfied: h11<1,>=0.9.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from wsproto->simple-websocket<1.0,>=0.10.1->taipy-gui<3.1,>=3.0->taipy) (0.14.0)\n", "Requirement already satisfied: cloudpickle>=1.1.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from dask>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (2.0.0)\n", "Requirement already satisfied: partd>=0.3.10 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from dask>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (1.2.0)\n", "Requirement already satisfied: toolz>=0.8.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from dask>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (0.11.1)\n", "Collecting dask>=2.22.0 (from modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy)\n", "  Downloading dask-2021.10.0-py3-none-any.whl (1.0 MB)\n", "     ---------------------------------------- 1.0/1.0 MB 5.9 MB/s eta 0:00:00\n", "Requirement already satisfied: msgpack>=0.6.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from distributed>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (1.0.2)\n", "Requirement already satisfied: sortedcontainers!=2.0.0,!=2.0.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from distributed>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (2.4.0)\n", "Requirement already satisfied: tblib>=1.6.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from distributed>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (1.7.0)\n", "Requirement already satisfied: zict>=0.1.3 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from distributed>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (2.0.0)\n", "Requirement already satisfied: tornado>=6.0.3 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from distributed>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (6.3.3)\n", "Requirement already satisfied: locket in c:\\users\\<USER>\\anaconda3\\lib\\site-packages\\locket-0.2.1-py3.9.egg (from partd>=0.3.10->dask>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (0.2.1)\n", "Requirement already satisfied: heapdict in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from zict>=0.1.3->distributed>=2.22.0->modin[dask]<1.0,>=0.23.0->taipy-core<3.1,>=3.0->taipy-rest<3.1,>=3.0->taipy) (1.0.1)\n", "Downloading flask-3.0.0-py3-none-any.whl (99 kB)\n", "   ---------------------------------------- 99.7/99.7 kB 952.0 kB/s eta 0:00:00\n", "Downloading click-8.1.7-py3-none-any.whl (97 kB)\n", "   ---------------------------------------- 97.9/97.9 kB 943.0 kB/s eta 0:00:00\n", "Downloading Flask_Cors-4.0.0-py2.py3-none-any.whl (14 kB)\n", "Downloading Flask_RESTful-0.3.10-py2.py3-none-any.whl (26 kB)\n", "Downloading Flask_SocketIO-5.3.6-py3-none-any.whl (18 kB)\n", "Downloading gevent-23.9.1-cp39-cp39-win_amd64.whl (1.5 MB)\n", "   ---------------------------------------- 1.5/1.5 MB 1.1 MB/s eta 0:00:00\n", "Downloading Markdown-3.5.2-py3-none-any.whl (103 kB)\n", "   -------------------------------------- 103.9/103.9 kB 749.0 kB/s eta 0:00:00\n", "Downloading SQLAlchemy-2.0.25-cp39-cp39-win_amd64.whl (2.1 MB)\n", "   ---------------------------------------- 2.1/2.1 MB 2.2 MB/s eta 0:00:00\n", "Downloading simple_websocket-0.10.1-py3-none-any.whl (7.5 kB)\n", "Downloading twisted-23.10.0-py3-none-any.whl (3.2 MB)\n", "   ---------------------------------------- 3.2/3.2 MB 2.8 MB/s eta 0:00:00\n", "Downloading alembic-1.13.1-py3-none-any.whl (233 kB)\n", "   -------------------------------------- 233.4/233.4 kB 842.2 kB/s eta 0:00:00\n", "Downloading blinker-1.7.0-py3-none-any.whl (13 kB)\n", "Downloading flask_sqlalchemy-3.1.1-py3-none-any.whl (25 kB)\n", "Downloading greenlet-3.0.3-cp39-cp39-win_amd64.whl (290 kB)\n", "   -------------------------------------- 290.8/290.8 kB 944.8 kB/s eta 0:00:00\n", "Downloading marshmallow-3.20.2-py3-none-any.whl (49 kB)\n", "   ---------------------------------------- 49.4/49.4 kB 502.7 kB/s eta 0:00:00\n", "Downloading modin-0.26.0-py3-none-any.whl (1.1 MB)\n", "   ---------------------------------------- 1.1/1.1 MB 1.7 MB/s eta 0:00:00\n", "Downloading pymongo-4.6.1-cp39-cp39-win_amd64.whl (472 kB)\n", "   ---------------------------------------- 472.7/472.7 kB 1.1 MB/s eta 0:00:00\n", "Downloading python_socketio-5.11.0-py3-none-any.whl (75 kB)\n", "   ---------------------------------------- 75.4/75.4 kB 693.3 kB/s eta 0:00:00\n", "Downloading typing_extensions-4.9.0-py3-none-any.whl (32 kB)\n", "Downloading werkzeug-3.0.1-py3-none-any.whl (226 kB)\n", "   ---------------------------------------- 226.7/226.7 kB 1.1 MB/s eta 0:00:00\n", "Downloading dnspython-2.4.2-py3-none-any.whl (300 kB)\n", "   ---------------------------------------- 300.4/300.4 kB 1.0 MB/s eta 0:00:00\n", "Downloading fsspec-2023.12.2-py3-none-any.whl (168 kB)\n", "   -------------------------------------- 169.0/169.0 kB 728.2 kB/s eta 0:00:00\n", "Downloading python_engineio-4.8.2-py3-none-any.whl (57 kB)\n", "   ---------------------------------------- 57.4/57.4 kB 604.1 kB/s eta 0:00:00\n", "Downloading Mako-1.3.0-py3-none-any.whl (78 kB)\n", "   ---------------------------------------- 78.6/78.6 kB 876.5 kB/s eta 0:00:00\n", "Building wheels for collected packages: taipy, taipy-gui, taipy-rest, taipy-templates, taipy-core, taipy-config\n", "  Building wheel for taipy (pyproject.toml): started\n", "  Building wheel for taipy (pyproject.toml): finished with status 'done'\n", "  Created wheel for taipy: filename=taipy-3.0.0-py3-none-any.whl size=283869 sha256=bb53351dddc48af17ecb5c30c1652349ae83ef33729e4f7e29002f426c57a8ee\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\56\\02\\b4\\24f4706ab0c365192b3a332aa8f1e027442449f3c2bb23f06c\n", "  Building wheel for taipy-gui (pyproject.toml): started\n", "  Building wheel for taipy-gui (pyproject.toml): finished with status 'done'\n", "  Created wheel for taipy-gui: filename=taipy_gui-3.0.0-py3-none-any.whl size=2687450 sha256=f70d523cfe34617e14f5cd353404e112684d48502e10f029cac635174e8301ce\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\ef\\e7\\16\\6a9b8845a8bdaf83e476ddc65512d505894aebc581f8ddbd60\n", "  Building wheel for taipy-rest (setup.py): started\n", "  Building wheel for taipy-rest (setup.py): finished with status 'done'\n", "  Created wheel for taipy-rest: filename=taipy_rest-3.0.0-py3-none-any.whl size=42208 sha256=13b452dc3c8aa7e3bbc76ce7297df40c9f9738a2e22c167b69201e36bb2e171f\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\44\\c3\\e0\\67e3347ffe234b5119bfc88c224faab5644e630da8d3208b9b\n", "  Building wheel for taipy-templates (pyproject.toml): started\n", "  Building wheel for taipy-templates (pyproject.toml): finished with status 'done'\n", "  Created wheel for taipy-templates: filename=taipy_templates-3.0.0-py3-none-any.whl size=24923 sha256=4d9f43ad522c74168d899f39a9fe6ada70a9efcf4502c68fd56c9813babb5a27\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\d1\\4e\\e5\\41d12ccb992b9ea287693ab9bcc0ea55274dd41ea0b1201c06\n", "  Building wheel for taipy-core (pyproject.toml): started\n", "  Building wheel for taipy-core (pyproject.toml): finished with status 'done'\n", "  Created wheel for taipy-core: filename=taipy_core-3.0.0-py3-none-any.whl size=250298 sha256=9c3f85062f2ef90e34764a5885f0378d02ee37955439db35925217c74a354ad3\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\96\\fa\\ad\\3392c4acd27fc8d0e168fe561d0c4e920e805152bc4e8dca8e\n", "  Building wheel for taipy-config (setup.py): started\n", "  Building wheel for taipy-config (setup.py): finished with status 'done'\n", "  Created wheel for taipy-config: filename=taipy_config-3.0.0-py3-none-any.whl size=50094 sha256=a2bc272eb1edc0053ca5b74e0865e9025b6529bf477ee49f136a2caa338d2245\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\bc\\68\\df\\e499042913287709bbbd7bf3ecab031591ed7d7300c2cb834c\n", "Successfully built taipy taipy-gui taipy-rest taipy-templates taipy-core taipy-config\n", "Installing collected packages: Werkzeug, typing-extensions, taipy-templates, python-dotenv, numpy, marshmallow, Mako, itsdangerous, greenlet, fsspec, dnspython, deepdiff, click, blinker, bidict, apispec, twisted, taipy-config, SQLAlchemy, simple-websocket, pymongo, markdown, gevent, flask, dask, python-engineio, modin, marshmallow-sqlalchemy, gevent-websocket, Flask-SQLAlchemy, flask-restful, flask-marshmallow, flask-cors, apispec-webframeworks, alembic, python-socketio, flask-migrate, cookiecutter, taipy-core, flask-socketio, taipy-rest, taipy-gui, taipy\n", "  Attempting uninstall: Werkzeug\n", "    Found existing installation: Werkzeug 2.0.2\n", "    Uninstalling Werkzeug-2.0.2:\n", "      Successfully uninstalled Werkzeug-2.0.2\n", "  Attempting uninstall: typing-extensions\n", "    Found existing installation: typing_extensions 4.4.0\n", "    Uninstalling typing_extensions-4.4.0:\n", "      Successfully uninstalled typing_extensions-4.4.0\n", "  Attempting uninstall: taipy-templates\n", "    Found existing installation: taipy-templates 2.4.0\n", "    Uninstalling taipy-templates-2.4.0:\n", "      Successfully uninstalled taipy-templates-2.4.0\n", "  Attempting uninstall: python-dotenv\n", "    Found existing installation: python-dotenv 0.20.0\n", "    Uninstalling python-dotenv-0.20.0:\n", "      Successfully uninstalled python-dotenv-0.20.0\n", "  Attempting uninstall: numpy\n", "    Found existing installation: numpy 1.26.1\n", "    Uninstalling numpy-1.26.1:\n", "      Successfully uninstalled numpy-1.26.1\n", "  Attempting uninstall: itsdangerous\n", "    Found existing installation: itsdangerous 2.0.1\n", "    Uninstalling itsdangerous-2.0.1:\n", "      Successfully uninstalled itsdangerous-2.0.1\n", "  Attempting uninstall: greenlet\n", "    Found existing installation: greenlet 1.1.1\n", "    Uninstalling greenlet-1.1.1:\n", "      Successfully uninstalled greenlet-1.1.1\n", "  Attempting uninstall: fsspec\n", "    Found existing installation: fsspec 2021.10.1\n", "    Uninstalling fsspec-2021.10.1:\n", "      Successfully uninstalled fsspec-2021.10.1\n", "  Attempting uninstall: click\n", "    Found existing installation: click 8.0.3\n", "    Uninstalling click-8.0.3:\n", "      Successfully uninstalled click-8.0.3\n", "  Attempting uninstall: blinker\n", "    Found existing installation: blinker 1.4\n", "    Uninstalling blinker-1.4:\n", "      Successfully uninstalled blinker-1.4\n", "  Attempting uninstall: twisted\n", "    Found existing installation: Twisted 22.10.0\n", "    Uninstalling Twisted-22.10.0:\n", "      Successfully uninstalled Twisted-22.10.0\n", "  Attempting uninstall: SQLAlchemy\n", "    Found existing installation: SQLAlchemy 1.4.31\n", "    Uninstalling SQLAlchemy-1.4.31:\n", "      Successfully uninstalled SQLAlchemy-1.4.31\n", "  Attempting uninstall: markdown\n", "    Found existing installation: Markdown 3.3.4\n", "    Uninstalling Markdown-3.3.4:\n", "      Successfully uninstalled Markdown-3.3.4\n", "  Attempting uninstall: gevent\n", "    Found existing installation: gevent 21.8.0\n", "    Uninstalling gevent-21.8.0:\n", "      Successfully uninstalled gevent-21.8.0\n", "  Attempting uninstall: flask\n", "    Found existing installation: Flask 1.1.2\n", "    Uninstalling Flask-1.1.2:\n", "      Successfully uninstalled Flask-1.1.2\n", "  Attempting uninstall: dask\n", "    Found existing installation: dask 2022.2.0\n", "    Uninstalling dask-2022.2.0:\n", "      Successfully uninstalled dask-2022.2.0\n", "  Attempting uninstall: cookiecutter\n", "    Found existing installation: cookiecutter 1.7.2\n", "    Uninstalling cookiecutter-1.7.2:\n", "      Successfully uninstalled cookiecutter-1.7.2\n", "Successfully installed Flask-SQLAlchemy-3.1.1 Mako-1.3.0 SQLAlchemy-2.0.25 Werkzeug-3.0.1 alembic-1.13.1 apispec-5.2.2 apispec-webframeworks-0.5.2 bidict-0.22.1 blinker-1.7.0 click-8.1.7 cookiecutter-2.1.1 dask-2021.10.0 deepdiff-6.2.3 dnspython-2.4.2 flask-3.0.0 flask-cors-4.0.0 flask-marshmallow-0.14.0 flask-migrate-3.1.0 flask-restful-0.3.10 flask-socketio-5.3.6 fsspec-2023.12.2 gevent-23.9.1 gevent-websocket-0.10.1 greenlet-3.0.3 itsdangerous-2.1.2 markdown-3.3.6 marshmallow-3.20.2 marshmallow-sqlalchemy-0.28.1 modin-0.26.0 numpy-1.22.4 pymongo-4.6.1 python-dotenv-1.0.0 python-engineio-4.8.2 python-socketio-5.11.0 simple-websocket-0.10.1 taipy-3.0.0 taipy-config-3.0.0 taipy-core-3.0.0 taipy-gui-3.0.0 taipy-rest-3.0.0 taipy-templates-3.0.0 twisted-23.10.0 typing-extensions-4.9.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: Ignoring invalid distribution -atplotlib (c:\\users\\<USER>\\anaconda3\\lib\\site-packages)\n", "WARNING: Ignoring invalid distribution -atplotlib (c:\\users\\<USER>\\anaconda3\\lib\\site-packages)\n", "  WARNING: Failed to remove contents in a temporary directory 'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\~umpy.libs'.\n", "  You can safely remove it manually.\n", "  WARNING: Failed to remove contents in a temporary directory 'C:\\Users\\<USER>\\Anaconda3\\Lib\\site-packages\\~.mpy'.\n", "  You can safely remove it manually.\n", "ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "daal4py 2021.3.0 requires daal==2021.2.3, which is not installed.\n", "numba 0.54.1 requires numpy<1.21,>=1.17, but you have numpy 1.22.4 which is incompatible.\n", "styleframe 4.1 requires pandas<2, but you have pandas 2.1.1 which is incompatible.\n"]}], "source": ["!pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org taipy scikit-learn statsmodels\n", "\n", "# !pip install taipy\n", "# !pip install scikit-learn\n", "# !pip install statsmodels"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using Notebooks\n", "Some functions will be used in the Getting Started for Notebooks that are primarly used for Notebooks (`gui.stop()`, `gui.run()`, `gui.on_change`, `set_content()`)\n", "To have more explanation on these different functions, you can find the documentation related [here](https://docs.taipy.io/manuals/gui/notebooks/)\n", "**Warning**: Do not forget to stop your server when you are finished. You can do so by restarting your kernel.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code for this step [here](https://docs.taipy.io/getting_started/src/step_00.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 0: First web page\n", "\n", "To create your first Taipy web page, you only need one line of code. Create a `Gui` object with a String and run it. \n", "A client link will be displayed in the console. Enter it in a web browser to open your first Taipy web client!\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2024-01-18 16:08:45][<PERSON><PERSON>][INFO] Running in 'single_client' mode in notebook environment\n", "[2024-01-18 16:08:50][<PERSON><PERSON>][INFO]  * Server starting on http://127.0.0.1:5000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["UserWarning: libuv only supports millisecond timer resolution; all times less will be set to 1 ms\n"]}], "source": ["from taipy.gui import <PERSON><PERSON>, <PERSON><PERSON>\n", "\n", "# A dark mode is available in Taipy\n", "# However, we will use the light mode for the Getting Started\n", "\n", "# We can use G<PERSON>(\"# Getting Started with <PERSON><PERSON>\").run() directly\n", "# However, we need a <PERSON><PERSON> and Gui object to modify the content of the page\n", "# in the Notebook\n", "\n", "main_page = Markdown(\"# Getting Started with <PERSON><PERSON>\")\n", "gui = Gui(main_page)\n", "gui.run(dark_mode=False)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "If you want to run multiple servers at the same time, you can change the server port number (5000 by default) in the `.run()` method. For example, `Gui(...).run(port=xxxx)`.\n", "\n", "\n", "Note that you can style the text. <PERSON><PERSON> uses the Markdown syntax to style your text and more. Therefore, `#` creates \n", "a title, `##` makes a subtitle. Put your text in `*` for *italics* or in `**` to have it in **bold**.\n", "\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_00/result.png width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_01.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 1: Visual elements\n", "\n", "Many visual elements can be added to the basic code viewed in Step 0. This Step shows how to use visual elements \n", "like charts, sliders and tables and implement them in the GUI.\n", "\n", "## Importing the Dataset\n", "\n", "Suppose that you have a [*dataset.csv*](https://docs.taipy.io/getting_started/step_01/dataset.csv) file, using the *Pandas* library, you can retrieve this dataset \n", "with the following code:\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["El<PERSON><PERSON>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["UserWarning: libuv only supports millisecond timer resolution; all times less will be set to 1 ms\n"]}], "source": ["import pandas as pd\n", "\n", "def get_data(path_to_csv: str):\n", "    # pandas.read_csv() returns a pd.DataFrame\n", "    dataset = pd.read_csv(path_to_csv)\n", "    dataset[\"Date\"] = pd.to_datetime(dataset[\"Date\"])\n", "    return dataset\n", "\n", "# Read the dataframe\n", "path_to_csv = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\TaiPy\\dataset.csv\"\n", "dataset = get_data(path_to_csv)\n", "\n", "...\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "_dataset_ is a *pd.DataFrame*, a basic *Pandas main* object representing, in this case, a realistic time series. \n", "It represents the historical number of articles sold for a given store on a 15-minute basis (we have the historical \n", "sales data for the year 2021). Being a real dataset, there will sometimes be missing information for specific days. \n", "The columns are:\n", "\n", "- Index: a unique identifier for each data point.\n", "\n", "- Date: the date of the data point. Each date are separated by 15 minutes.\n", "\n", "- Value: the number of articles sold per 15-minute timeframe.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_01/table.png width=700>\n", "</div>\n", "\n", "After creating your first web client with just one line of code and reading our dataset data with the code above, \n", "let's add some  visual elements to our initial page.\n", "\n", "## Visual elements\n", "\n", "Taipy GUI can be considered as an **augmented** Markdown; it adds the concept of \n", "**[Visual elements](https://docs.taipy.io/en/latest/manuals/gui/viselements/)** on top of all the Markdown syntax. A visual \n", "element is a Taipy graphical object displayed on the client. It can be a \n", "[slider](https://docs.taipy.io/en/latest/manuals/gui/viselements/slider/), a \n", "[chart](https://docs.taipy.io/en/latest/manuals/gui/viselements/chart/), a \n", "[table](https://docs.taipy.io/en/latest/manuals/gui/viselements/table/), an \n", "[input](https://docs.taipy.io/en/latest/manuals/gui/viselements/input/), a \n", "[menu](https://docs.taipy.io/en/latest/manuals/gui/viselements/menu/), etc. Check the list \n", "[here](https://docs.taipy.io/en/latest/manuals/gui/controls/).\n", "\n", "Every visual element follows a similar syntax:\n", "\n", "`<|{variable}|visual_element_name|param_1=param_1|param_2=param_2| ... |>`.\n", "\n", "For example, a [slider](https://docs.taipy.io/en/latest/manuals/gui/viselements/slider/) is written this way :\n", "\n", "`<|{variable}|slider|min=min_value|max=max_value|>`.\n", "\n", "For each visual element you wish to add to your web page, you must include the syntax above inside your markdown \n", "string (representing your page). For example, at the beginning of the page, let's display:\n", "\n", "- a Python variable *n_week*;\n", "\n", "- a slider that will \"visually\" modify the value of __n_week__.\n", "\n", "Here is the overall syntax:\n", "\n", "```\n", "*<|{n_week}|>*\n", "<|{n_week}|slider|min=1|max=52|>\n", "```\n", "\n", "We will then create a chart and a table:\n", "\n", "```\n", "<|{dataset}|chart|type=bar|x=Date|y=Value|height=100%|>\n", "<|{dataset}|table|height=400px|width=95%|>\n", "```\n", "\n", "Here is the combined code:\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["gui.stop()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2024-01-18 16:35:04][<PERSON><PERSON>][INFO] Gui server has been stopped.\n", "[2024-01-18 16:35:04][<PERSON><PERSON>][INFO] Running in 'single_client' mode in notebook environment\n", "[2024-01-18 16:35:06][<PERSON><PERSON>][INFO]  * Server starting on http://127.0.0.1:5000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["UserWarning: libuv only supports millisecond timer resolution; all times less will be set to 1 ms\n"]}], "source": ["...\n", "\n", "from taipy import Gui\n", "\n", "dataset = get_data(path_to_csv)\n", "\n", "# Initial value\n", "n_week = 10\n", "\n", "# Definition of the page\n", "page = \"\"\"\n", "# Getting started with <PERSON><PERSON>\n", "\n", "Week number: *<|{n_week}|>*\n", "\n", "Interact with this slider to change the week number:\n", "<|{n_week}|slider|min=1|max=52|>\n", "\n", "## Dataset:\n", "\n", "Display the last three months of data:\n", "<|{dataset[9000:]}|chart|type=bar|x=Date|y=Value|height=100%|>\n", "\n", "<|{dataset}|table|height=400px|width=95%|>\n", "\"\"\"\n", "\n", "# Create a Gui object with our page content\n", "gui.stop()\n", "main_page.set_content(page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_01/result.gif width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_02.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 2: Interactive GUI\n", "\n", "Now, the page has several visual elements:\n", "\n", "- A slider that is connected to the Python variable *n_week* ;\n", "\n", "- A chart and a table controls that represent the DataFrame content.\n", "\n", "Taipy GUI manages everything. To go further into Taipy GUI, let's consider the concept of **state**.\n", "\n", "## Multi-client - state\n", "\n", "Try to open a few clients with the same URL. You will see that every client is independent from each other; you can change *n_week* on a client, and *n_week* will not change in other clients. This is due to the concept of **state**.\n", "\n", "The state holds the value of all the variables that are used in the user interface, for one specific connection.\n", "\n", "For example, at the beginning, `state.n_week = 10`. When *n_week* is modified by the slider (through a given graphical client), this is, in fact, *state.n_week* that is modified, not *n_week* (the global Python variable). Therefore, if you open 2 different clients, *n_week* will have 2 state values (*state.n_week*), one for each client.\n", "\n", "In the code below, this concept will be used to connect a variable (*n_week*) to other variables:\n", "\n", "- We will create a chart that will only display one week of data corresponding to the selected week of the slider.\n", "\n", "- A connection has to be made between the slider's value  (*state.n_week*) and the chart data (*state.dataset_week*).\n", "\n", "## How to connect two variables - the *[on_change](https://docs.taipy.io/en/latest/manuals/gui/callbacks/)* function\n", "\n", "In *Taipy*, the `on_change()` function is a \"special\" function. **Taipy** will check if you created a function with this name and will use it. Whenever the state of a variable is modified, the *callback* function is called with three parameters:\n", "\n", "- state (the state object containing all the variables)\n", "\n", "- The name of the modified variable\n", "\n", "- Its value.\n", "\n", "Here, `on_change()` will be called whenever the slider's value (*state.n_week*) changes. Each time this happens, *state.dataset_week* will be updated according to the new value of the selected week. Then, <PERSON><PERSON> will propagate this change automatically to the associated chart.\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'state' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_27220\\199085926.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     19\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     20\u001b[0m \u001b[0mgui\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mstop\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 21\u001b[1;33m \u001b[0mgui\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mon_change\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mon_change\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mstate\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mstate\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mn_week\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     22\u001b[0m \u001b[0mmain_page\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mset_content\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mpage\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     23\u001b[0m \u001b[0mgui\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrun\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mNameError\u001b[0m: name 'state' is not defined"]}, {"name": "stderr", "output_type": "stream", "text": ["UserWarning: libuv only supports millisecond timer resolution; all times less will be set to 1 ms\n"]}], "source": ["# Select the week based on the slider value\n", "dataset_week = dataset[dataset[\"Date\"].dt.isocalendar().week == n_week]\n", "\n", "page = \"\"\"\n", "# Getting started with <PERSON><PERSON>\n", "\n", "Select week: *<|{n_week}|>*\n", "\n", "<|{n_week}|slider|min=1|max=52|>\n", "\n", "<|{dataset_week}|chart|type=bar|x=Date|y=Value|height=100%|width=100%|>\n", "\"\"\"\n", "\n", "# on_change is the function that is called when any variable is changed\n", "def on_change(state, var_name: str, var_value):\n", "    if var_name == \"n_week\":\n", "        # Update the dataset when the slider is moved\n", "        state.dataset_week = dataset[dataset[\"Date\"].dt.isocalendar().week == var_value]\n", "\n", "gui.stop()\n", "gui.on_change = on_change(state, state.n_week, None)\n", "main_page.set_content(page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_02/result.gif width=700>\n", "</div>\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_03.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 3: Introducing <PERSON><PERSON>\n", "\n", "From Step 2, you now know the basics of Taipy GUI. Let's go for a moment over the Scenario Management aspect of Tai<PERSON>.\n", "\n", "Even if Taipy GUI can be used without Taipy Core (and vice-versa), there are a lot of reasons for using Taipy Core:\n", "\n", "- Taipy Core efficiently manages the execution of your functions/pipelines.\n", "\n", "- Taipy Core manages data sources and monitors KPIs.\n", "\n", "- Taipy Core provides an easy management of multiple pipelines and end-user scenarios which comes in handy in the \n", "  context of Machine Learning or Mathematical optimization.\n", "\n", "To apprehend the Scenario Management aspect of <PERSON><PERSON>, you need to understand four essential concepts.\n", "\n", "\n", "## Four fundamental [concepts](https://docs.taipy.io/en/latest/manuals/core/concepts/) in Taipy Core:\n", "\n", "- [**Data Nodes**](https://docs.taipy.io/en/latest/manuals/core/concepts/data-node/): are the translation of variables in \n", "  Taipy. Data Nodes don't contain the data itself but know how to retrieve it. They can refer to any kind of data: \n", "  any *Python* object (*string*, *int*, *list*, *dict*, *model*, *dataframe*, etc), a Pickle file, a CSV file, an \n", "  SQL database, etc. They know how to read and write data. You can even write your own custom Data Node if needed to \n", "  access a particular data format.\n", "\n", "- [**Tasks**](https://docs.taipy.io/en/latest/manuals/core/concepts/task/): are the translation of functions in Taipy.\n", "\n", "- [**Pipelines**](https://docs.taipy.io/en/latest/manuals/core/concepts/pipeline/): are a list of tasks executed with \n", "  intelligent scheduling created automatically by <PERSON><PERSON>. They usually represent a sequence of Tasks/functions \n", "  corresponding to different algorithms like a simple baseline Algorithm or a more sophisticated Machine-Learning \n", "  pipeline.\n", "\n", "- [**<PERSON><PERSON><PERSON><PERSON>**](https://docs.taipy.io/en/latest/manuals/core/concepts/scenario/): End-Users very often require modifying \n", "  various parameters to reflect different business situations. Taipy Scenarios will provide the framework to \n", "  \"play\"/\"execute\" pipelines under different conditions/variations (i.e. data/parameters modified by the end-user)\n", "\n", "\n", "Let's create a Machine Learning (ML) example to clarify these concepts.\n", "\n", "In a ML context, it is common to have numerous training and testing pipelines for different algorithms. For \n", "simplification, we will only configure a single baseline pipeline that will predict on a given **day** the values \n", "for the following days. In Taipy, you will describe (i.e. configure) your pipeline with three tasks:\n", "\n", "- Retrieval of the initial dataset,\n", "\n", "- Data Cleaning,\n", "\n", "- Predictions (for *number of predictions*) from **day** onwards. In our example, predictions represents the number \n", "  of items sold in a given store on a 15-min basis.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_03/baseline_pipeline.svg width=500>\n", "</div>\n", "\n", "This graph is created by configuring Data Nodes (variables) and tasks (functions). This configuration doesn't \n", "execute anything; it is just a configuration that enables <PERSON><PERSON> to map the Tasks and Data Nodes as a Directed \n", "Acyclic Graph (DAG).\n", "\n", "## Data Nodes configuration\n", "\n", "Data Nodes can point to:\n", "\n", "- any kind of *Python variables* by default: *int*, *string*, *dict*, *list*, *np.array*, *pd.DataFrame*, *models*, etc. \n", "\n", "- a CSV file, Pickle file or SQL database.\n", "\n", "During the configuration of the Data Nodes, the developer specifies the type or format of each Data Node. A *Python* \n", "variable is stored by default by a Pickle file.\n", "\n", "Some parameters for Data Node configuration:\n", "\n", "- **Storage type**: This is where the storage type is selected: CSV file, SQL database, Pickle file, etc. Here, the initial dataset is a CSV file so *storage_type=\"csv\"* for this Data Node. <PERSON><PERSON> knows how to \n", "  access it, thanks to the path. By default, the storage type is *pickle*.\n", "\n", "- **[Scope](https://docs.taipy.io/en/latest/manuals/core/concepts/scope/)**: You can find below three types of Scope in the \n", "  code: the Pipeline, the Scenario (by default) and the Global scope.\n", "\n", "    - *Global scope*: all Data Nodes are shared between every pipelines, scenarios and cycles. For example, the \n", "      initial dataset is shared between every pipelines and scenarios.\n", "\n", "    - *Scenario scope*: they are shared between all the pipelines of the scenario.\n", "\n", "    - *Pipeline scope*: Data Nodes don't have access to other Data Nodes from other pipelines. A 'predictions' Data \n", "      Node is created for each pipeline in the current example. So, adding pipelines/algorithms will store \n", "      predictions in different \"predictions\" Data Nodes.\n", "\n", "- **Cacheable**: This is a parameter used to increase the efficiency of the program. If the Data Node has already \n", "  been created and if its input/upstream data nodes haven’t changed since the last run (of the pipeline), then it is \n", "  not necessary to rerun the task that creates it.\n", "\n", "\n", "### Input Data Nodes configuration\n", "These are the input Data Nodes. They represent the variables in Taipy when a pipeline is executed. Still, first, we \n", "have to configure them to create the DAG.\n", "\n", "- *initial_dataset* is simply the initial CSV file. <PERSON><PERSON> needs some parameters to read this data: *path* and \n", "  *header*. The `scope` is global; each scenario or pipeline has the same initial dataset.\n", "\n", "- *day* is the beginning of the predictions. The default value is the 26th of July. It means the training data will \n", "  end before the 26th of July, and predictions will begin on this day.\n", "\n", "- *n_predictions* is the number of predictions you want to make while predicting. The default value is 40. A \n", "  prediction represents the number of items sold in a given store per 15-minute time slot.\n", "\n", "- *max_capacity* is the maximum value that can take a prediction; it is the ceiling of the projections. The default \n", "  value is 200. It means that, in our example, the maximum number of items sold per 15 minutes is 200.\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import datetime as dt\n", "import pandas as pd\n", "\n", "from taipy import Config, Scope\n", "\n", "## Input Data Nodes\n", "initial_dataset_cfg = Config.configure_data_node(id=\"initial_dataset\",\n", "                                                 storage_type=\"csv\",\n", "                                                 path=path_to_csv,\n", "                                                 scope=Scope.GLOBAL)\n", "\n", "# We assume the current day is the 26th of July 2021.\n", "# This day can be changed to simulate multiple executions of scenarios on different days\n", "day_cfg = Config.configure_data_node(id=\"day\", default_data=dt.datetime(2021, 7, 26))\n", "\n", "n_predictions_cfg = Config.configure_data_node(id=\"n_predictions\", default_data=40)\n", "\n", "max_capacity_cfg = Config.configure_data_node(id=\"max_capacity\", default_data=200)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Remaining Data Nodes\n", "\n", "- *cleaned_dataset* is the dataset after cleaning (after the `clean_data()` function). _cacheable_ is set to True \n", "  with a `scope.GLOBAL`. It means if the initial dataset didn't change, <PERSON><PERSON> will not re-execute the `clean_data()` \n", "  task. In other words, after the creation of this data node through `clean_data()`, <PERSON><PERSON> knows that it is not \n", "  necessary to create it again.\n", "\n", "- *predictions* are the predictions of the model. In this pipeline, it will be the output of the `predict_baseline()` \n", "  function. Each pipeline will create its own *prediction* Data Node hence `scope=Scope.PIPELINE`.\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["## Remaining Data Nodes\n", "cleaned_dataset_cfg = Config.configure_data_node(id=\"cleaned_dataset\",\n", "                                             cacheable=True,\n", "                                             validity_period=dt.<PERSON><PERSON><PERSON>(days=1),\n", "                                             scope=Scope.GLOBAL) \n", "\n", "predictions_cfg = Config.configure_data_node(id=\"predictions\", scope=Scope.PIPELINE)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "## Functions\n", "\n", "Here’s the code of each of the two *Python* functions: `clean_data()` and `predict_baseline()`. Their goal is \n", "respectively to clean the data and to predict the data.\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def clean_data(initial_dataset: pd.DataFrame):\n", "    print(\"     Cleaning data\")\n", "    # Convert the date column to datetime\n", "    initial_dataset[\"Date\"] = pd.to_datetime(initial_dataset[\"Date\"])\n", "    cleaned_dataset = initial_dataset.copy()\n", "    return cleaned_dataset\n", "\n", "\n", "def predict_baseline(cleaned_dataset: pd.DataFrame, n_predictions: int, day: dt.datetime, max_capacity: int):\n", "    print(\"     Predicting baseline\")\n", "    # Select the train data\n", "    train_dataset = cleaned_dataset[cleaned_dataset[\"Date\"] < day]\n", "    \n", "    predictions = train_dataset[\"Value\"][-n_predictions:].reset_index(drop=True)\n", "    predictions = predictions.apply(lambda x: min(x, max_capacity))\n", "    return predictions\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Tasks\n", "\n", "Tasks are the translation of functions in Taipy. These tasks combined with Data Nodes create your graph (DAG). \n", "Creating a task is simple; you need:\n", "\n", "- An id\n", "\n", "- A function\n", "\n", "- Inputs\n", "\n", "- Outputs\n", "\n", "### clean_data_task\n", "\n", "The first task that you want to create is your `clean_data()` task. It will take your initial dataset (input Data \n", "Node), clean it (calling the `clean_data()` function) and generate the cleaned dataset Data Node.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_03/clean_data.svg width=300>\n", "</div>\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["clean_data_task_cfg = Config.configure_task(id=\"clean_data\",\n", "                                            function=clean_data,\n", "                                            input=initial_dataset_cfg,\n", "                                            output=cleaned_dataset_cfg)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### predict_baseline_task\n", "\n", "This task will take the cleaned dataset and predict it according to your parameters i.e. the three input Data Nodes: \n", "*Day*, *Number of predictions* and *Max Capacity*.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_03/predict_baseline.svg width=300>\n", "</div>\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["predict_baseline_task_cfg = Config.configure_task(id=\"predict_baseline\",\n", "                                                  function=predict_baseline,\n", "                                                  input=[cleaned_dataset_cfg, n_predictions_cfg, day_cfg, max_capacity_cfg],\n", "                                                  output=predictions_cfg)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_04.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 4: Pipeline Management\n", "\n", "In Step 3, you have described your graph; let's implement it with <PERSON><PERSON>! \n", "\n", "## Pipeline configuration\n", "\n", "To configure your first pipeline, you need to list all the tasks you want to be done by the pipeline. This pipeline executes the cleaning (*clean_data_task*) and the predicting (*predict_baseline_task*). Note that the **task_configs** is a list, so you don't have to worry about the order of the tasks. <PERSON><PERSON> does that for you and optimizes its execution.\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Create the first pipeline configuration\n", "baseline_pipeline_cfg = Config.configure_pipeline(id=\"baseline\",\n", "                                                  task_configs=[clean_data_task_cfg, predict_baseline_task_cfg])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Pipeline creation and execution\n", "\n", "Then, create your pipeline from its configuration, submit it, and print the \"predictions\" Data Node results.\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import taipy as tp\n", "\n", "# Create the pipeline\n", "baseline_pipeline = tp.create_pipeline(baseline_pipeline_cfg)\n", "# Submit the pipeline (Execution)\n", "tp.submit(baseline_pipeline)\n", "    \n", "# Read output data from the pipeline\n", "baseline_predictions = baseline_pipeline.predictions.read()\n", "print(\"Predictions of baseline algorithm\\n\", baseline_predictions)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "> Note that when creating the pipeline (`tp.create_pipeline()`), all associated Taipy objects of the pipeline (Data nodes, Tasks, etc) get automatically created (unless already present).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_05.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 5: GUI and Pipeline\n", "\n", "In Step 4, we created a first pipeline using only Taipy Core. Let's update the GUI to reflect the results of the \n", "pipeline.\n", "\n", "A \"Predict\" [button](https://docs.taipy.io/en/latest/manuals/gui/viselements/button/) is added to the page to create the \n", "pipeline and run it. When you press a button, <PERSON><PERSON> calls the function passed to the *on_action* property.\n", "\n", "`<|Text displayed on button|button|on_action=fct_name_called_when_pressed|>`\n", "   \n", "A [chart](https://docs.taipy.io/en/latest/manuals/gui/viselements/chart/) control can be found at the end of the markdown to \n", "visualize the predictions. The chart plots two traces: the historical values and the predicted values.\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "# Initialize the \"predictions\" dataset\n", "predictions_dataset = pd.DataFrame({\"Date\":[dt.datetime(2021, 6, 1)], \"Historical values\":[np.NaN], \"Predicted values\":[np.NaN]})\n", "\n", "# Add a button and a chart for our predictions\n", "pipeline_page = page + \"\"\"\n", "Press <|predict|button|on_action=predict|> to predict with default parameters (30 predictions) and June 1st as day.\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "`create_and_submit_pipeline()` creates and executes the pipeline after being called by `predict()`. \n", "\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def predict(state):\n", "    print(\"'Predict' button clicked\")\n", "    pipeline = create_and_submit_pipeline()\n", "    update_predictions_dataset(state, pipeline)\n", "\n", "\n", "def create_and_submit_pipeline():\n", "    print(\"Execution of pipeline...\")\n", "    # Create the pipeline from the pipeline config\n", "    pipeline = tp.create_pipeline(baseline_pipeline_cfg)\n", "    # Submit the pipeline (Execution)\n", "    tp.submit(pipeline)\n", "    return pipeline\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "After the execution of the pipeline (`tp.submit()`), the data stored in *predictions* and *cleaned_data* Data \n", "Nodes become accessible. The `read()` method accesses the data in Data Nodes.\n", "\n", "The `create_predictions_dataset()` function below creates a final dataframe (that concatenates the predictions and \n", "the historical data together) containing three columns:\n", "\n", "- Date,\n", "\n", "- Historical values,\n", "\n", "- Predicted values.\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def create_predictions_dataset(pipeline):\n", "    print(\"Creating predictions dataset...\")\n", "    # Read data from the pipeline\n", "    predictions = pipeline.predictions.read()\n", "    day = pipeline.day.read()\n", "    n_predictions = pipeline.n_predictions.read()\n", "    cleaned_data = pipeline.cleaned_dataset.read()\n", "    \n", "    # Set arbitrarily the time window for the chart as 5 times the number of predictions\n", "    window = 5 * n_predictions\n", "\n", "    # Create the historical dataset that will be displayed\n", "    new_length = len(cleaned_data[cleaned_data[\"Date\"] < day]) + n_predictions\n", "    temp_df = cleaned_data[:new_length]\n", "    temp_df = temp_df[-window:].reset_index(drop=True)\n", "    \n", "    # Create the series that will be used in the concat\n", "    historical_values = pd.Series(temp_df[\"Value\"], name=\"Historical values\")\n", "    predicted_values = pd.Series([np.NaN]*len(temp_df), name=\"Predicted values\")\n", "    predicted_values[-len(predictions):] = predictions\n", "    \n", "    # Create the predictions dataset\n", "    # Columns : [Date, Historical values, Predicted values]\n", "    return pd.concat([temp_df[\"Date\"], historical_values, predicted_values], axis=1)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "It is now really simple to get  the predictions dataset and display it in the \"Prediction chart\" created above.\n", "\n", "\n", "When you press the \"Predict\" button, this function below is called. It will update the predictions' dataset, and \n", "this change will propagate to the chart.\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def update_predictions_dataset(state, pipeline):\n", "    print(\"Updating predictions dataset...\")\n", "    state.predictions_dataset = create_predictions_dataset(pipeline)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This is what the structure of the code looks like for the GUI:\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_05/organisation.svg width=500>\n", "</div>\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["gui.stop()\n", "main_page.set_content(pipeline_page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_05/result.png width=700>\n", "</div>\n", "\n", "> **Important Remark**: A better option would have been to have the `create_predictions_dataset()` modeled as a last **Task** inside the pipeline graph.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_06.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 6: Creation of Scenarios\n", "\n", "Now that you have seen how to create and run a single pipeline, let's configure a scenario. Remember, scenarios are \n", "required whenever the end-user wants to run variations of the pipelines and perform what-if analysis to simulate \n", "different business situations . Each scenario would represent a different solution to your problem. Here, \n", "*max_capacity*, *day* and *number of predictions* can influence the scenario.\n", "\n", "In this example, we will run two pipelines: our initial  pipeline (*baseline*) together with a new one (referred as \n", "\"*ml*\") that will implement a  different prediction function/model.\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# For the sake of clarity, we have used an AutoRegressive model rather than a pure ML model such as:\n", "# Random Forest, Linear Regression, LSTM, etc   \n", "from statsmodels.tsa.ar_model import AutoReg\n", "\n", "# This is the function that will be used by the task\n", "def predict_ml(cleaned_dataset: pd.DataFrame, n_predictions: int, day: dt.datetime, max_capacity: int):\n", "    print(\"     Predicting with ML\")\n", "    # Select the train data\n", "    train_dataset = cleaned_dataset[cleaned_dataset[\"Date\"] < day]\n", "    \n", "    # Fit the AutoRegressive model\n", "    model = AutoReg(train_dataset[\"Value\"], lags=7).fit()\n", "    \n", "    # Get the n_predictions forecasts\n", "    predictions = model.forecast(n_predictions).reset_index(drop=True)\n", "    predictions = predictions.apply(lambda x: min(x, max_capacity))\n", "    return predictions\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "A **predict_ml** Task config will need to be created and associated with the newly created `predict_ml()` function.\n", "The **predict_ml** Task configuration is created using the same format as before with a function, inputs, and outputs.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_06/predict_ml.svg width=300>\n", "</div>\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Create the task configuration of the predict_ml function.\n", "## We use the same input and ouput as the previous predict_baseline task but we change the funtion\n", "predict_ml_task_cfg = Config.configure_task(id=\"predict_ml\",\n", "                                            function=predict_ml,\n", "                                            input=[cleaned_dataset_cfg, n_predictions_cfg, day_cfg, max_capacity_cfg],\n", "                                            output=predictions_cfg)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "With this new task, the Machine Learning pipeline can finally be configured.\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Create the new ml pipeline that will clean and predict with the ml model\n", "ml_pipeline_cfg = Config.configure_pipeline(id=\"ml\", task_configs=[clean_data_task_cfg, predict_ml_task_cfg])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "To configure a scenario, you need to use `tp.configure_scenario` and the list of the related pipelines. You can \n", "easily add more pipelines/algorithms if you wished to.\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# Configure our scenario which is our business problem.\n", "scenario_cfg = Config.configure_scenario(id=\"scenario\", pipeline_configs=[baseline_pipeline_cfg, ml_pipeline_cfg])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "The configuration is now complete. Now, you can create your scenario and execute it. When creating it, <PERSON><PERSON> will \n", "create your pipelines (and its associated Tasks), and when you submit the scenario, it will run them based on \n", "<PERSON><PERSON>’s built-in intelligent scheduling. <PERSON><PERSON> knows in which sequence the Tasks need to be performed.\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# Create the scenario\n", "scenario = tp.create_scenario(scenario_cfg)\n", "# Execute it\n", "tp.submit(scenario)\n", "# Get the resulting scenario\n", "## Print the predictions of the two pipelines (baseline and ml)\n", "print(\"\\nBaseline predictions\\n\", scenario.baseline.predictions.read())\n", "print(\"\\nMachine Learning predictions\\n\", scenario.ml.predictions.read())   \n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_07.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 7: GUI and Scenario\n", "\n", "In Step 6, using Taipy Core, we implemented a scenario configuration and created our first scenario (based on that \n", "config) . In this step, we will implement a graphical interface that makes use of scenarios. \n", "\n", "- First, a scenario will be created and executed at the beginning.\n", "\n", "- Then, a Taipy GUI *selector* will be used to select one of the two pipelines associated with the scenario: the \n", "  *baseline* or the *ml* pipeline.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_07/selector.gif width=250>\n", "</div>\n", "\n", "A [selector](https://docs.taipy.io/en/latest/manuals/gui/viselements/selector/) only needs two properties: a value that gets \n", "dynamically updated through the selector and the list of values possible (aka \"lov\"). Here is the syntax for a selector:\n", "\n", "`<|{selected_value}|selector|lov={lov_selector}|>`.\n", "\n", "An \"Update chart\" button will update the chart according to the selected pipeline.\n", "\n", "These variables below are the parameters of the pipeline selector. The selected pipeline will be the first among \n", "\"baseline\" and \"ml\" when starting the client.\n", "\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# Set the list of pipelines names\n", "# It will be used in a selector of pipelines\n", "pipeline_selector = [\"baseline\", \"ml\"]\n", "selected_pipeline = pipeline_selector[0]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This pipeline selector is added in the Markdown file just before the chart as well as the \"Update chart\" button.\n", "\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["scenario_page = page + \"\"\"\n", "Select the pipeline\n", "<|{selected_pipeline}|selector|lov={pipeline_selector}|> <|Update chart|button|on_action=update_chart|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "The code around the GUI has evolved. `create_scenario()` is creating a scenario and submitting it with the \n", "`submit_scenario()` function. `update_chart()` is updating the chart based upon the selected scenario and pipeline.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_07/organisation.svg width=500>\n", "</div>\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def create_scenario():\n", "    print(\"Creating scenario...\")\n", "    scenario = tp.create_scenario(scenario_cfg)\n", "    scenario = submit_scenario(scenario)\n", "    return scenario\n", "\n", "def submit_scenario(scenario):\n", "    print(\"Submitting scenario...\")\n", "    tp.submit(scenario)\n", "    return scenario\n", "\n", "def update_chart(state):\n", "    print(\"'Update chart' button clicked\")\n", "    # Select the right pipeline\n", "    pipeline = scenario.pipelines[state.selected_pipeline]\n", "\n", "    # Update the chart based on this pipeline\n", "    # It is the same function as created before in step_5\n", "    update_predictions_dataset(state, pipeline)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Before running the GUI, these two lines of code will erase the previous scenarios, pipelines, data nodes that you \n", "created in the previous steps to avoid any problem of compatibility.\n", "\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# Delete all entities\n", "Config.configure_global_app(clean_entities_enabled=True)\n", "tp.clean_all_entities()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# Creation of our first scenario\n", "scenario = create_scenario()\n", "gui.stop()\n", "main_page.set_content(scenario_page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_07/result.gif width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_08.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 8: Modify Data Nodes content\n", "\n", "Now that the GUI has been created to handle one scenario, it would be interesting to change the \"initial\" variables \n", "to see their impact on the predictions. These variables are: the *number of predictions*, the *max capacity* and the \n", "*day*. How can we interact with them in real-time?\n", "\n", "It can easily be done using the `write()` function of Data Nodes.\n", "\n", "First, to link these variables to a visual element, they have to be initialized. \n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# Initial variables\n", "## Initial variables for the scenario   \n", "day = dt.datetime(2021, 7, 26)\n", "n_predictions = 40\n", "max_capacity = 200\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Second, we will add to the Markdown (before the chart), a visual element binding each of these variables. We will be \n", "using them to \"modify\" the scenario. See the documentation for these newly introduced visual elements here: \n", "[date](https://docs.taipy.io/en/latest/manuals/gui/viselements/date/) and \n", "[number](https://docs.taipy.io/en/latest/manuals/gui/viselements/number/). A \"Save button\" is also created to run the \n", "\"submit_scenario()\" function when pressed.\n", "\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["page_scenario_manager = page + \"\"\"\n", "# Change your scenario\n", "\n", "**Prediction date**\\n\\n <|{day}|date|not with_time|>\n", "\n", "**Max capacity**\\n\\n <|{max_capacity}|number|>\n", "\n", "**Number of predictions**\\n\\n<|{n_predictions}|number|>\n", "\n", "<|Save changes|button|on_action={submit_scenario}|>\n", "\n", "Select the pipeline\n", "<|{selected_pipeline}|selector|lov={pipeline_selector}|> <|Update chart|button|on_action={update_chart}|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "`create_scenario()` function is almost the same as before except for the need to track the *scenario_id* of the \n", "newly created scenario (using the Global variable *selected_scenario*).\n", "\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["def create_scenario():\n", "    global selected_scenario\n", "\n", "    print(\"Creating scenario...\")\n", "    scenario = tp.create_scenario(scenario_cfg)\n", "  \n", "    selected_scenario = scenario.id\n", "  \n", "    tp.submit(scenario)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "The `submit_scenario()` function introduces two essential Taipy functions:\n", "\n", "- `tp.get(scenario_id)`: Taipy function used to get the scenario from its id.\n", "\n", "- `write(new_value)`: a Data Node function that changes the value stored in the Data Node. For example, \n", "  *scenario.max_capacity* is a Data Node whose value can be changed to 100 like this\n", "  `scenario.max_capacity.write(100)`.\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def submit_scenario(state):\n", "    print(\"Submitting scenario...\")\n", "    # Get the selected scenario: in this current step a single scenario is created then modified here.\n", "    scenario = tp.get(selected_scenario)\n", "    \n", "    # Conversion to the right format\n", "    state_day = dt.datetime(state.day.year, state.day.month, state.day.day)\n", "\n", "    # Change the default parameters by writing in the datanodes\n", "    scenario.day.write(state_day)\n", "    scenario.n_predictions.write(int(state.n_predictions))\n", "    scenario.max_capacity.write(int(state.max_capacity))\n", "\n", "    # Execute the pipelines/code\n", "    tp.submit(scenario)\n", "    \n", "    # Update the chart when we change the scenario\n", "    update_chart(state)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "`update_chart()` uses a previous function (`update_predictions_dataset()`) to update the *predictions_dataset* \n", "with the correct pipeline.\n", "\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["def update_chart(state):\n", "    # Select the right scenario and pipeline\n", "    scenario = tp.get(selected_scenario)\n", "    pipeline = scenario.pipelines[state.selected_pipeline]\n", "    # Update the chart based on this pipeline\n", "    update_predictions_dataset(state, pipeline)\n", "\n", "\n", "global selected_scenario\n", "# Creation of a single scenario\n", "create_scenario()\n", "gui.stop()\n", "main_page.set_content(page_scenario_manager)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_08/result.gif width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_09.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 9: <PERSON><PERSON>\n", "\n", "Now that you know how to create a scenario, submit it and change it, you will create in this step a Taipy program \n", "able to manage multiple scenarios (and pipelines).\n", "\n", "## Dynamic selectors\n", "\n", "Let's manage multiple scenarios through a dynamic scenario selector. This selector will be updated whenever a new \n", "scenario is created. It will store the \"id\" of the scenarios and their names. For clarity, only their names do get \n", "displayed (in the selector).\n", "\n", "This code initializes the scenario selector with previously created scenarios. If there are no scenarios yet, the \n", "scenario selector will be empty.\n", "\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# Get all the scenarios already created\n", "all_scenarios = tp.get_scenarios()\n", "\n", "# Delete the scenarios that don't have a name attribute\n", "# All the scenarios of the previous steps do not have an associated name so they will be deleted,\n", "# this will not be the case for those created by this step\n", "[tp.delete(scenario.id) for scenario in all_scenarios if scenario.name is None]\n", "\n", "# Initial variable for the scenario selector\n", "# The list of possible values (lov) for the scenario selector is a list of tuples (scenario_id, scenario_name),\n", "# but the selected_scenario is just used to retrieve the scenario id and what gets displayed is the name of the scenario.\n", "scenario_selector = [(scenario.id, scenario.name) for scenario in tp.get_scenarios()]\n", "selected_scenario = None\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Beside adding to the Markdown the new scenario selector, we also add a new \"Create new scenario\" button. This button \n", "calls the `create_scenario()` function. So, now each time we modify the parameters (*day*, *max_capacity*, \n", "*n_prediction*) we will create a new scenario upon clicking on this \"Create new scenario\" button.\n", "\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["scenario_manager_page = page + \"\"\"\n", "# Create your scenario\n", "\n", "**Prediction date**\\n\\n <|{day}|date|not with_time|>\n", "\n", "**Max capacity**\\n\\n <|{max_capacity}|number|>\n", "\n", "**Number of predictions**\\n\\n<|{n_predictions}|number|>\n", "\n", "<|Create new scenario|button|on_action=create_scenario|>\n", "\n", "## <PERSON><PERSON><PERSON> \n", "<|{selected_scenario}|selector|lov={scenario_selector}|dropdown|>\n", "\n", "## Display the pipeline\n", "<|{selected_pipeline}|selector|lov={pipeline_selector}|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Here is the main code for managing scenarios. As you can see, the architecture doesn't change from the previous code.\n", "Two functions have been altered: `_create_scenario()` and `submit_scenario()`. \n", "\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["def create_name_for_scenario(state)->str:\n", "    name = f\"<PERSON><PERSON><PERSON> ({state.day.strftime('%A, %d %b')}; {state.max_capacity}; {state.n_predictions})\"\n", "    \n", "    # Change the name if it is the same as some scenarios\n", "    if name in [s[1] for s in state.scenario_selector]:\n", "        name += f\" ({len(state.scenario_selector)})\"\n", "    return name\n", "\n", "\n", "def update_chart(state):\n", "    # Now, the selected_scenario comes from the state, it is interactive\n", "    scenario = tp.get(state.selected_scenario[0])\n", "    pipeline = scenario.pipelines[state.selected_pipeline]\n", "    update_predictions_dataset(state, pipeline)\n", "    \n", "\n", "# Change the create_scenario function in order to change the default parameters\n", "# and allow the creation of multiple scenarios\n", "def create_scenario(state):\n", "    print(\"Execution of scenario...\")\n", "    # Extra information for the scenario\n", "    creation_date = state.day\n", "    name = create_name_for_scenario(state)\n", "    # Create a scenario\n", "    scenario = tp.create_scenario(scenario_cfg,creation_date=creation_date, name=name)\n", "    \n", "    state.selected_scenario = (scenario.id, name)\n", "    # Submit the scenario that is currently selected\n", "    submit_scenario(state)\n", "\n", "\n", "def submit_scenario(state):\n", "    print(\"Submitting scenario...\")\n", "    # Get the currently selected scenario\n", "    scenario = tp.get(state.selected_scenario[0])\n", "    \n", "    # Conversion to the right format (change?)\n", "    day = dt.datetime(state.day.year, state.day.month, state.day.day) \n", "\n", "    # Change the default parameters by writing in the Data Nodes\n", "    scenario.day.write(day)\n", "    scenario.n_predictions.write(int(state.n_predictions))\n", "    scenario.max_capacity.write(int(state.max_capacity))\n", "    scenario.creation_date = state.day\n", "        \n", "\n", "    # Execute the scenario\n", "    tp.submit(scenario)\n", "    \n", "    # Update the scenario selector and the scenario that is currently selected\n", "    update_scenario_selector(state, scenario) # change list to scenario\n", "    \n", "    # Update the chart directly\n", "    update_chart(state) \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "The function below will update the scenario selector whenever the user creates a new scenario. It is called in the \n", "`submit_scenario` function.\n", "\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["def update_scenario_selector(state, scenario):\n", "    print(\"Updating scenario selector...\")\n", "    # Update the scenario selector\n", "    state.scenario_selector += [(scenario.id, scenario.name)]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This graph summarizes the code for the GUI.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_09/organisation.svg width=500>\n", "</div>\n", "\n", "\n", "## Automating the graph update - *on_change* function\n", "\n", "The `on_change` function can automatically change the graph when another pipeline or scenario is selected.\n", "\n"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["def on_change(state, var_name: str, var_value):\n", "    if var_name == \"n_week\":\n", "        # Update the dataset when the slider is moved\n", "        state.dataset_week = dataset[dataset[\"Date\"].dt.isocalendar().week == var_value]\n", "        \n", "    elif var_name == \"selected_pipeline\" or var_name == \"selected_scenario\":\n", "        # Update the chart when the scenario or the pipeline is changed\n", "        # Check if we can read the Data Node to update the chart\n", "        if tp.get(state.selected_scenario[0]).predictions.read() is not None:\n", "            update_chart(state)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Run the Gui.\n", "\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["gui.stop()\n", "gui.on_change = on_change\n", "main_page.set_content(scenario_manager_page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_09/result.gif width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_10.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 10: Embellish your App\n", "\n", "With just a few steps, you have created a full forecasting application which predicts across multiple days with different parameters. However, the page's layout is not yet optimal and it could be greatly improved. This will be done during this step. To get a more aesthetically pleasing page, three new useful controls will be used. These are:\n", "\n", "- [menu](https://docs.taipy.io/en/latest/manuals/gui/viselements/menu/): creates a menu on the left to navigate through the pages.\n", "\n", "`<|menu|label=Menu|lov={lov_pages}|on_action=on_menu|>`. For example, this code creates a menu with two pages:\n", "\n"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["from taipy import Gui\n", "\n", "def on_menu():\n", "    print('Menu function called')\n", "\n", "gui.stop()\n", "main_page.set_content(\"<|menu|label=Menu|lov={['Data Visualization', 'Scenario Manager']}|on_action=on_menu|>\")\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/menu.png width=50>\n", "</div>\n", "\n", "\n", "\n", "- [part](https://docs.taipy.io/en/latest/manuals/gui/viselements/part/): creates a group of text/visual elements. A useful property of `part` is _render_. If set to False, it will not display the part. This allows the developer to dynamically display a group of visual elements or not.\n", "\n", "```\n", "<|part|render={bool_variable}|\n", "Text\n", "Or visual elements...\n", "|>\n", "```\n", "\n", "- [layout](https://docs.taipy.io/en/latest/manuals/gui/viselements/layout/): creates invisible columns where you can put your texts and visual elements. The _columns_ property indicates the width and number of columns. Here, we create three columns of the same width.\n", "\n", "```\n", "<|layout|columns=1 1 1|\n", "But<PERSON> in first column <|Press|button|>\n", "\n", "Second column\n", "\n", "Third column\n", "|>\n", "```\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/layout.png width=500>\n", "</div>\n", "\n", "\n", "One strategy to switch from one page to another is:\n", "\n", "1. To create a specific Markdown string for each page;\n", "\n", "2. Use the Menu control to switch from one page to another by controlling the page variable.\n", "\n", "This is how you can easily create multiple pages; there are many other ways to do so.\n", " \n", "First, let’s start by creating the 2 pages.\n", "\n", "The first page contains the original chart and slider defined in Step 2. Let’s use the same Markdown as the one defined in Step 2. It is named _page_ (and is also present in Step 9). \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["# Our first page is the original page\n", "# (with the slider and the chart that displays a week of the historical data)\n", "page_data_visualization = page\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/data_visualization.png width=700>\n", "</div>\n", "\n", "\n", "Then let’s create our second page which contains the page corresponding to the creation of scenarios seen in Step 9.\n", "\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["# Second page: create scenarios and display results\n", "page_scenario_manager = \"\"\"\n", "# Create your scenario\n", "\n", "<|layout|columns=1 1 1 1|\n", "<|\n", "**Prediction date**\\n\\n <|{day}|date|not with_time|>\n", "|>\n", "\n", "<|\n", "**Max capacity**\\n\\n <|{max_capacity}|number|>\n", "|>\n", "\n", "<|\n", "**Number of predictions**\\n\\n<|{n_predictions}|number|>\n", "|>\n", "\n", "<|\n", "<br/>\\n <|Create new scenario|button|on_action=create_scenario|>\n", "|>\n", "|>\n", "\n", "<|part|render={len(scenario_selector) > 0}|\n", "<|layout|columns=1 1|\n", "<|\n", "## Scenario \\n <|{selected_scenario}|selector|lov={scenario_selector}|dropdown|>\n", "|>\n", "\n", "<|\n", "## Display the pipeline \\n <|{selected_pipeline}|selector|lov={pipeline_selector}|dropdown|>\n", "|>\n", "|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/scenario_manager.gif width=700>\n", "</div>\n", "\n", "\n", "The menu combines these two pages. When a page is selected in the menu control, `on_menu()` is called and updates the \n", "page.\n", "\n"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["# Create a menu with our pages\n", "multi_pages = \"\"\"\n", "<|menu|label=Menu|lov={[\"Data Visualization\", \"Scenario Manager\"]}|on_action=on_menu|>\n", "\n", "<|part|render={page==\"Data Visualization\"}|\"\"\" + page_data_visualization + \"\"\"|>\n", "<|part|render={page==\"Scenario Manager\"}|\"\"\" + page_scenario_manager + \"\"\"|>\n", "\"\"\"\n", "\n", "\n", "# The initial page is the \"Data Visualization\" page\n", "page = \"Data Visualization\"\n", "def on_menu(state, var_name: str, fct: str, var_value: list):\n", "    # Change the value of the state.page variable in order to render the correct page\n", "    state.page = var_value[\"args\"][0]\n", "\n", "\n", "gui.stop()\n", "main_page.set_content(multi_pages)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/multi_pages.png width=700>\n", "</div>\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_11.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 11: Introducing Cycles\n", "\n", "So far, we have talked about how having different scenarios helps us to oversee our assumptions about the future. \n", "For example, in business, it is critical to weigh different options in order to come up with an optimal solution. \n", "However, this decision making process isn’t just a one-time task, but rather a recurrent operation that happens over \n", "a time period. This is why we want to introduce [Cycles](https://docs.taipy.io/en/latest/manuals/core/concepts/cycle/).\n", "\n", "A cycle can be thought of as a place to store different and recurrent scenarios, within a time frame. In Taipy Core, \n", "each cycle will have a unique primary scenario, which represents the reference scenario for a time period.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_11/cycle.svg width=300>\n", "</div>\n", "\n", "Typically, in a Machine Learning problem, a lot of scenarios are created daily to predict the next day. Among all \n", "those scenarios, there is only one primary scenario. In the step's example, scenarios are attached to a DAILY cycle. \n", "Using Cycles is useful because some specific <PERSON><PERSON>'s functions exist to navigate through these Cycles. <PERSON><PERSON> can get \n", "all the scenarios created in a day by providing the Cycle. You can also get every primary scenario ever made to \n", "quickly see their progress over time.\n", "\n", "Moreover, nothing is more straightforward than creating a Cycle. The frequency parameter in a scenario configuration \n", "will create the desired type of Cycle. In the code below, the scenario has a daily cycle. It will be attached to the \n", "correct period (day) when it is created.\n", "\n", "As you can see, a Cycle can be made very easily once you have the desired frequency. In this snippet of code, since \n", "we have specified `frequency=Frequency.DAILY`, the corresponding scenario will be automatically attached to the \n", "correct period (*day*) once it is created. \n", "\n"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["from taipy import Config, Frequency\n", "\n", "# Create scenarios each week and compare them\n", "scenario_daily_cfg = Config.configure_scenario(id=\"scenario\",\n", "                                           pipeline_configs=[baseline_pipeline_cfg, ml_pipeline_cfg],\n", "                                           frequency=Frequency.DAILY)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "To avoid any compatibility issue between scenarios with or without cycle, this code will erase the previous \n", "scenarios, pipelines, datanodes that you have maybe created in the previous steps.\n", "\n"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["# Delete all entities\n", "Config.configure_global_app(clean_entities_enabled=True)\n", "tp.clean_all_entities()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "To clarify this concept of primary scenario, the scenario selector will show a `*` before its name if the scenario \n", "is primary. This is why we update the following functions.\n", "\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["# Change the inital scenario selector to see which scenarios are primary\n", "scenario_selector = [(scenario.id, (\"*\" if scenario.is_primary else \"\") + scenario.name) for scenario in tp.get_scenarios()]\n", "\n", "# Redefine update_scenario_selector to add \"*\" in the display name when the scnario is primary\n", "def update_scenario_selector(state, scenario):\n", "    print(\"Updating scenario selector...\")\n", "    # Create the scenario name for the scenario selector\n", "    # This name changes dependind whether the scenario is primary or not\n", "    scenario_name = (\"*\" if scenario.is_primary else \"\") + scenario.name\n", "    print(scenario_name)\n", "    # Update the scenario selector\n", "    state.scenario_selector += [(scenario.id, scenario_name)]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "In `create_scenario()`, *scenario_daily_cfg* is now the configuration used to create the scenario. By creating it, \n", "you also create the dependent Cycle. For example, setting `creation_date` to 04/02/2021 makes a cycle related to \n", "this day. All scenarios that are created on this day belong to this Cycle with just one primary scenario. Changing \n", "`creation_date` again will create another cycle for a different day and so on.\n", "\n"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["# Change the create_scenario function to create a scenario with the selected frequency\n", "def create_scenario(state):\n", "    print(\"Execution of scenario...\")\n", "    # Extra information for scenario\n", "    creation_date = state.day\n", "    name = create_name_for_scenario(state)\n", "\n", "    # Create a scenario with the week cycle\n", "    scenario = tp.create_scenario(scenario_daily_cfg, creation_date=creation_date, name=name)\n", "\n", "    state.selected_scenario = (scenario.id, name)\n", "\n", "    # Change the scenario that is currently selected\n", "    submit_scenario(state)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Two buttons are added to the GUI (\"Make primary\" and \"Delete scenario\"). They call the `make_primary()` and \n", "`delete_scenario()` functions below.\n", "\n", "`make_primary()` changes the current primary scenario of the cycle thanks to `tp.set_primary(scenario)`. It is the \n", "Taipy function used to make a scenario primary.\n", "\n", "> Note that the previous primary scenario will not longer be primary. There is always just one primary scenario in a cycle. \n", "\n"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["selected_scenario_is_primary = None\n", "\n", "def make_primary(state):\n", "    print(\"Making the current scenario primary...\")\n", "    scenario = tp.get(state.selected_scenario[0])\n", "    # Take the current scenario primary\n", "    tp.set_primary(scenario)\n", "    \n", "    # Update the scenario selector accordingly\n", "    state.scenario_selector = [(scenario.id, (\"*\" if scenario.is_primary else \"\") + scenario.name)\n", "                               for scenario in tp.get_scenarios()]\n", "    state.selected_scenario_is_primary = True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This function is triggered by the \"Delete scenario\" button.\n", "\n", "> Note that a primary scenario cannot be deleted.\n", "\n"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["from taipy.gui import notify\n", "\n", "def remove_scenario_from_selector(state, scenario: list):\n", "    # Take all the scenarios in the selector that doesn't have the scenario.id\n", "    state.scenario_selector = [(s[0], s[1]) for s in state.scenario_selector if s[0] != scenario.id]\n", "    state.selected_scenario = state.scenario_selector[-1]\n", "\n", "def delete_scenario(state):\n", "    scenario = tp.get(state.selected_scenario[0])\n", "    \n", "    if scenario.is_primary:\n", "        # Notify the user that primary scenarios can not be deleted\n", "        notify(state, \"info\", \"Cannot delete the primary scenario\")\n", "    else:\n", "        # Delete the scenario and the related objects (datanodes, tasks, jobs,...)\n", "        tp.delete(scenario.id)\n", "        \n", "        # Update the scenario selector accordingly\n", "        remove_scenario_from_selector(state,scenario)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "As previously said, just two visual elements (\"Make primary\" and \"Delete scenario\" buttons) have been added to the \n", "page. This code is almost identical to the previous *page_scenario_manager*.\n", "\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["# Add a \"Delete scenario\" and a \"Make primary\" buttons\n", "page_scenario_manager = \"\"\"\n", "# Create your scenario:\n", "\n", "<|layout|columns=1 1 1 1|\n", "<|\n", "**Prediction date**\\n\\n <|{day}|date|not with_time|>\n", "|>\n", "\n", "<|\n", "**Max capacity**\\n\\n <|{max_capacity}|number|>\n", "|>\n", "\n", "<|\n", "**Number of predictions**\\n\\n<|{n_predictions}|number|>\n", "|>\n", "\n", "<|\n", "<br/>\n", "<br/>\n", "<|Create new scenario|button|on_action=create_scenario|>\n", "|>\n", "|>\n", "\n", "\n", "<|part|render={len(scenario_selector) > 0}|\n", "<|layout|columns=1 1|\n", "\n", "<|layout|columns=1 1|\n", "<|\n", "## Scenario \\n <|{selected_scenario}|selector|lov={scenario_selector}|dropdown|>\n", "|>\n", "\n", "<br/>\n", "<br/>\n", "<br/>\n", "<br/>\n", "<|Delete scenario|button|on_action=delete_scenario|active={len(scenario_selector)>0}|>\n", "<|Make primary|button|on_action=make_primary|active={not(selected_scenario_is_primary) and len(scenario_selector)>0}|>\n", "|>\n", "\n", "\n", "<|\n", "## Display the pipeline \\n <|{selected_pipeline}|selector|lov={pipeline_selector}|dropdown|>\n", "|>\n", "|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["# Redefine the multi_pages\n", "multi_pages = \"\"\"\n", "<|menu|label=Menu|lov={[\"Data Visualization\", \"Scenario Manager\"]}|on_action=on_menu|>\n", "\n", "<|part|render={page==\"Data Visualization\"}|\"\"\" + page_data_visualization + \"\"\"|>\n", "<|part|render={page==\"Scenario Manager\"}|\"\"\" + page_scenario_manager + \"\"\"|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "When the selected scenario is changed, <PERSON><PERSON> calls the `on_change` and will update `state.\n", "selected_scenario_is_primary` (set to `True` if the selected scenario is primary).\n", "\n"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["def on_change(state, var_name: str, var_value):\n", "    if var_name == \"n_week\":\n", "        # Update the dataset when the slider is moved\n", "        state.dataset_week = dataset[dataset[\"Date\"].dt.isocalendar().week == var_value]\n", "        \n", "    elif var_name == \"selected_pipeline\" or var_name == \"selected_scenario\":\n", "        # Update selected_scenario_is_primary indicating if the current scenario is primary or not\n", "        state.selected_scenario_is_primary = tp.get(state.selected_scenario[0]).is_primary\n", "\n", "        # Check if we can read the Data Node to update the chart\n", "        if tp.get(state.selected_scenario[0]).predictions.read() is not None:\n", "            update_chart(state)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["gui.stop()\n", "gui.on_change = on_change\n", "main_page.set_content(multi_pages)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_11/result.gif style=\"margin:auto;display:block;border:>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_12.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 12: <PERSON><PERSON><PERSON>\n", "\n", "Cycles are helpful to keep track of KPI over time. The goal of this step is to compare the primary scenario of every \n", "cycle and its pipelines over time.\n", "\n", "To achieve this:\n", "\n", "- A new dataframe has to be initialized. It will store the metrics for the *baseline* and *ml* pipeline. \n", "\n", "- Then, a part will use a boolean to show or not the comparison.\n", "\n", "- Finally, a selector will change the displayed metrics of the graph.\n", "\n"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["# Initial dataset for comparison\n", "comparison_scenario = pd.DataFrame({\"Scenario Name\":[],\n", "                                    \"RMSE baseline\":[], \"MAE baseline\":[],\n", "                                    \"RMSE ML\":[], \"MAE ML\":[]})\n", "\n", "# Indicates if the comparison is done\n", "comparison_scenario_done = False\n", "\n", "# Selector for metrics\n", "metric_selector = [\"RMSE\", \"MAE\"]\n", "selected_metric = metric_selector[0]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "First, a function has to be created to compare the primary scenario of all the cycles. \n", "`tp.get_primary_scenarios()` is the useful function to use for this effect. `compare()` goes through all of these \n", "scenarios and pipelines and add the metrics in lists. In the end, *state.comparison_scenario* is updated and \n", "*comparison_scenario_done* set to `True`.\n", "\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import mean_absolute_error, mean_squared_error\n", "\n", "\n", "def compute_metrics(historical_data, predicted_data):\n", "    rmse = mean_squared_error(historical_data, predicted_data)\n", "    mae = mean_absolute_error(historical_data, predicted_data)\n", "    return rmse, mae\n", "\n", "\n", "def compare(state):\n", "    print(\"Comparing...\")\n", "    # Initial lists for comparison\n", "    scenario_names = []\n", "    rmses_baseline = []\n", "    maes_baseline = []\n", "    rmses_ml = []\n", "    maes_ml = []\n", "    \n", "    # Go through all the primary scenarios\n", "    all_scenarios = tp.get_primary_scenarios()\n", "    all_scenarios_ordered = sorted(all_scenarios, key=lambda x: x.creation_date.timestamp())\n", "    \n", "    for scenario in all_scenarios_ordered:\n", "        print(f\"<PERSON><PERSON><PERSON> {scenario.name}\")\n", "        # Go through all the pipelines\n", "        for pipeline in scenario.pipelines.values():\n", "            print(f\"     Pipeline {pipeline.config_id}\")\n", "            # Get the predictions dataset with the historical data\n", "            only_prediction_dataset = create_predictions_dataset(pipeline)[-pipeline.n_predictions.read():]\n", "            \n", "            # Series to compute the metrics (true values and predicted values)\n", "            historical_values = only_prediction_dataset[\"Historical values\"]\n", "            predicted_values = only_prediction_dataset[\"Predicted values\"]\n", "            \n", "            # Compute the metrics for this pipeline and primary scenario\n", "            rmse, mae = compute_metrics(historical_values, predicted_values)\n", "            \n", "            # Add values to the appropriate lists\n", "            if \"baseline\" in pipeline.config_id:\n", "                rmses_baseline.append(rmse)\n", "                maes_baseline.append(mae)\n", "            elif \"ml\" in pipeline.config_id:\n", "                rmses_ml.append(rmse)\n", "                maes_ml.append(mae)\n", "\n", "        scenario_names.append(scenario.creation_date.strftime(\"%A %d %b\"))\n", "        \n", "    # Update comparison_scenario\n", "    state.comparison_scenario = pd.DataFrame({\"Scenario Name\":scenario_names,\n", "                                              \"RMSE baseline\":rmses_baseline,\n", "                                              \"MAE baseline\":maes_baseline,\n", "                                              \"RMSE ML\":rmses_ml,\n", "                                              \"MAE ML\":maes_ml})\n", "    \n", "    # When comparison_scenario_done will be set to True,\n", "    # the part with the graphs will be finally rendered\n", "    state.comparison_scenario_done = True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Let's create a page related to this comparison. As said before, this page will contain a graph to compare scenarios \n", "and pipelines; and a selector to choose the metric on which to compare. When pressed the button at the bottom of the \n", "page calls the `compare()` function. When finished, the _render_ property of the *part* will render the rest of the \n", "page. Also, a new <PERSON><PERSON>'s block is present in the Markdown: \n", "[expandable](https://docs.taipy.io/en/latest/manuals/gui/viselements/expandable/).\n", "\n"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["# Performance page\n", "page_performance = \"\"\"\n", "<br/>\n", "\n", "<|part|render={comparison_scenario_done}|\n", "\n", "<|Table|expanded=False|expandable|\n", "<|{comparison_scenario}|table|width=100%|>\n", "|>\n", "\n", "<|{selected_metric}|selector|lov={metric_selector}|dropdown|>\n", "\n", "<|part|render={selected_metric==\"RMSE\"}|\n", "<|{comparison_scenario}|chart|type=bar|x=Scenario Name|y[1]=RMSE baseline|y[2]=RMSE ML|height=100%|width=100%|>\n", "|>\n", "\n", "<|part|render={selected_metric==\"MAE\"}|\n", "<|{comparison_scenario}|chart|type=bar|x=Scenario Name|y[1]=MAE baseline|y[2]=MAE ML|height=100%|width=100%|>\n", "|>\n", "\n", "|>\n", "\n", "\n", "<center>\n", "<|Compare primarys|button|on_action=compare|>\n", "</center>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_12/page_performance.gif width=700>\n", "</div>\n", "\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["# Add the page_performance section to the menu   \n", "multi_pages = \"\"\"\n", "<|menu|label=Menu|lov={[\"Data Visualization\", \"Scenario Manager\", \"Performance\"]}|on_action=on_menu|>\n", "\n", "<|part|render={page==\"Data Visualization\"}|\"\"\" + page_data_visualization + \"\"\"|>\n", "<|part|render={page==\"Scenario Manager\"}|\"\"\" + page_scenario_manager + \"\"\"|>\n", "<|part|render={page==\"Performance\"}|\"\"\" + page_performance + \"\"\"|>\n", "\n", "\"\"\"\n", "\n", "gui.stop()\n", "main_page.set_content(multi_pages)\n", "gui.run()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}