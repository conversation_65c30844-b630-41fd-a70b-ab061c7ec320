

-- Clean up
DROP TABLE IF EXISTS sch_analysts.tbl_cases_delivered_productivity;

SELECT 'Creating enhanced cases_delivered table to feed our productivity models...' AS status;


CREATE TABLE sch_analysts.tbl_cases_delivered_productivity
USING PARQUET
AS

WITH ors_data AS (
    /* First CTE - Simplified and more efficient version */
    SELECT
        concat(substr(fw.dmtm_fw_code, 2, 4), substr(fw.dmtm_fw_code, 7, 2)) AS upl_date,
        art.cntr_code AS country,
        str.slsp_dmst_code AS store,
        SUBSTR(str.slsp_dmst_code, 2, 4) AS store_om,
        str.slsp_name AS store_desc,
        art.slad_tpn AS tpn,
        art.slad_tpnb AS tpnb,
        art.slad_long_des AS art_des,
        art.own_brand AS own_brand,
        CASE WHEN sl.tpnb IS NULL THEN 'N' ELSE 'Y' END AS starline,
        hier.pmg as pmg,
        art.dmat_div_code AS divs,
        art.dmat_div_des_en AS division,
        art.dmat_dep_code AS dept,
        art.dmat_dep_des_en AS department,
        art.dmat_sec_code AS sect,
        art.dmat_sec_des_en AS section,
        art.dmat_grp_code AS grp,
        art.dmat_grp_des_en AS group,
        art.dmat_sgr_code AS sgr,
        art.dmat_sgr_des_en AS subgroup,
        supp.dmsup_code AS supp_om,
        supp.dmsup_long_des AS supp_des,
        ors.orsor_code AS orsor_code,
        'NA' AS supp_po_no,
        --ors.orsor_type AS orsor_type,
        
        (CASE
            WHEN ors.orsor_type = "A"
                THEN 'PBL'
            WHEN ors.orsor_type = "T"
                THEN 'PBS'
            ELSE ors.orsor_type
        END) AS orsor_type,
        
        
        (CASE
            WHEN ors.orsor_pack_qty IS NULL
                THEN 1
            ELSE ors.orsor_pack_qty
        END) AS orsor_pack_qty,
        
        (CASE
            WHEN ors.orsor_sku_percar IS NULL
                THEN 1
            ELSE ors.orsor_sku_percar
        END) AS orsor_sku_percar,
        

        CAST(ors.orsor_date AS DATE) AS orsor_date,
        CAST(ors.orsor_exp_deliv AS DATE) AS orsor_exp_deliv,
        NVL(ors.orsor_or_quantity, 0) + NVL(ors.orsor_cancelled_quantity, 0) AS orsor_or_quantity,
        ors.orsor_tops_original_quantity AS tops_orig_qty,
        ors.orsor_tops_shortened_quantity AS tops_short_qty,
        ors.orsor_status AS orsor_status,
        ors.orsor_id AS orsor_id,
        ors.orsor_dmat_id AS orsor_dmat_id,
        ors.orsor_dmst_id_src AS orsor_dmst_id_src,
        ors.orsor_cntr_id AS orsor_cntr_id,
        ors.orsor_received_quantity AS orsor_received_quantity
    FROM
        dw.ors_orders ors
    JOIN
        dm.dim_artgld_details art
        ON  ors.orsor_cntr_id = art.cntr_id 
        AND ors.orsor_dmat_id = art.slad_dmat_id
    JOIN
        dm.dim_suppliers supp
        ON  art.slad_dmsup_id = supp.dmsup_id 
        AND art.cntr_id = supp.cntr_id
    JOIN
        dw.sl_store_params str
        ON  ors.orsor_cntr_id = str.slsp_cntr_id 
        AND ors.orsor_dmst_id_trg = str.slsp_dmst_id
    JOIN
        dm.dim_time_d fw
        ON  CAST(fw.dmtm_value AS DATE) = CAST(ors.orsor_exp_deliv AS DATE)
    LEFT JOIN
        sch_analysts.tbl_ce_eva sl
        ON  sl.tpnb = art.slad_tpnb 
        AND sl.country = art.cntr_code
        
    JOIN
        tesco_analysts.hierarchy_spm hier ON art.dmat_div_code = LPAD(hier.div_code,4,"0") 
        AND art.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
        AND art.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
        AND art.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
        AND art.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
    WHERE
        ors.orsor_cntr_id = '4'
        AND ors.part_col BETWEEN 20250630 AND 20250630
        AND orsor_type in ('A', 'T', 'DTS')
        --AND (str.slsp_dmst_code = 44070 OR str.slsp_dmst_code IS NULL) -- Added store filter early
        AND str.slsp_dmst_code in (41640,41510,41500,41520,41440,41670,41590,41970,41780,41390,44013,44057 )
        --AND art.slad_tpnb in (105381925, 111267649)
        AND art.dmat_sec_des_en = 'Bake-off'
),

rec_data AS (
    /* Second CTE - For receiving data with early filtering for rec_acc_quant > 0 */
    SELECT
        r.orsrec_cntr_id AS cntr_id,
        r.orsrec_orsor_id AS orsor_id,
        r.orsrec_dmat_id AS dmat_id,
        sum(r.orsrec_quant) AS rec_qty,
        sum(NVL(r.orsrec_acc_quant, 0)) AS rec_acc_quant
    FROM
        dw.ors_receivings r
    WHERE
        r.orsrec_cntr_id in (1,2,3,4)
        AND r.part_col BETWEEN 20250630 AND 20250630
        AND NVL(r.orsrec_acc_quant, 0) > 0 -- Filter for positive receivings only
    GROUP BY
        r.orsrec_cntr_id,
        r.orsrec_orsor_id,
        r.orsrec_dmat_id
),

ordrec_data AS (
    /* Third CTE - Combines orders and receivings */
    SELECT
        ors.upl_date AS upl_date,
        ors.country AS country,
        ors.store AS store,
        ors.store_om AS store_om,
        ors.store_desc AS store_desc,
        ors.tpn AS tpn,
        ors.tpnb AS tpnb,
        ors.art_des AS art_des,
        ors.own_brand AS own_brand,
        ors.starline AS starline,
        ors.pmg as pmg,
        ors.divs AS divs,
        ors.division as division,
        ors.dept AS dept,
        ors.department as department,
        ors.sect AS sect,
        ors.section as section,
        ors.grp AS grp,
        ors.group as group,
        ors.sgr AS sgr,
        ors.subgroup as subgroup,
        ors.supp_om AS supp_om,
        ors.supp_des AS supp_des,
        ors.orsor_code AS orsor_code,
        ors.supp_po_no AS supp_po_no,
        ors.orsor_type AS orsor_type,
        ors.orsor_pack_qty AS orsor_pack_qty,
        ors.orsor_sku_percar AS orsor_sku_percar,
        ors.orsor_date AS orsor_date,
        ors.orsor_exp_deliv AS orsor_exp_deliv,
        ors.orsor_or_quantity AS orsor_or_quantity,
        ors.tops_orig_qty AS tops_orig_qty,
        ors.tops_short_qty AS tops_short_qty,
        (CASE
            WHEN ors.orsor_pack_qty >= ors.orsor_sku_percar
                THEN COALESCE(rec.rec_acc_quant, 0)
            ELSE (COALESCE(rec.rec_acc_quant, 0))/NULLIF(ors.orsor_sku_percar, 0)
        END) AS rec_case,
        (CASE
            WHEN ors.orsor_pack_qty >= ors.orsor_sku_percar
                THEN (COALESCE(rec.rec_acc_quant, 0))*ors.orsor_pack_qty
            ELSE COALESCE(rec.rec_acc_quant, 0) 
        END) AS rec_unit,
        COALESCE(ors.orsor_received_quantity, 0) AS planned_shipped_qty,
        ors.orsor_status AS orsor_status,
        ors.orsor_id AS orsor_id,
        ors.orsor_dmat_id AS orsor_dmat_id,
        ors.orsor_dmst_id_src AS orsor_dmst_id_src,
        ors.orsor_cntr_id AS orsor_cntr_id
    FROM
        ors_data ors
    JOIN  -- Changed from LEFT JOIN to INNER JOIN for receivings
        rec_data rec
        ON  rec.cntr_id = ors.orsor_cntr_id 
        AND rec.orsor_id = ors.orsor_id
    -- No need for rec_case > 0 filter here as we're using INNER JOIN with rec_data
    -- and we've already filtered rec_data for positive receivings
),

base_data AS (
    SELECT
        ors.upl_date AS fw,
        ors.country AS country,
        ors.store AS store,
        ors.store_om AS store_om,
        ors.store_desc AS store_desc,
        ors.tpn AS tpn,
        ors.tpnb AS tpnb,
        ors.art_des AS art_des,
        ors.own_brand AS own_brand,
        ors.starline AS starline,
        -- For DTS order type, allow dc to be NULL, otherwise use the COALESCE logic
        CASE 
            WHEN ors.orsor_type = 'DTS' THEN COALESCE(vp_dc.ph_code, direct_dc.slsp_dmst_code)
            ELSE COALESCE(vp_dc.ph_code, direct_dc.slsp_dmst_code)
        END AS dc,
        CASE 
            WHEN ors.orsor_type = 'DTS' THEN COALESCE(vp_dc.ph_name, direct_dc.slsp_name)
            ELSE COALESCE(vp_dc.ph_name, direct_dc.slsp_name)
        END AS dc_desc,
        ors.orsor_id AS orsor_id,
        ors.orsor_status AS ord_status,
        ors.pmg as pmg,
        ors.divs AS divs,
        ors.division as division,
        ors.dept AS dept,
        ors.department as department,
        ors.sect AS sect,
        ors.section as section,
        ors.grp AS grp,
        ors.group as group,
        ors.sgr AS sgr,
        ors.subgroup as subgroup,
        ors.supp_om AS supp_om,
        ors.supp_des AS supp_des,
        ors.orsor_code AS store_transfer_no,
        ors.supp_po_no AS supp_po_no,
        ors.orsor_type AS ord_type,
        goart.goar_order_group AS og,
        goart.goar_distribution_group_code AS dg,
        ors.orsor_pack_qty AS pack_qty1,
        ors.orsor_sku_percar AS pack_qty2,
        ors.orsor_date AS ord_date,
        ors.orsor_exp_deliv AS exp_del_date,
        ors.orsor_or_quantity AS ord_qty,
        ors.tops_orig_qty AS tops_orig_qty,
        ors.tops_short_qty AS tops_short_qty,
        ors.rec_case AS rec_case,
        ors.rec_unit AS rec_unit,
        ors.planned_shipped_qty AS planned_shipped_qty,
        ors.orsor_cntr_id AS cntr_id
    FROM
        ordrec_data ors
    LEFT JOIN
        dm.go_article goart
        ON  ors.orsor_cntr_id = goart.cntr_id 
        AND ors.orsor_dmat_id = goart.goar_dmat_id
    -- Join for virtual->physical DC relationship
    LEFT JOIN
        (
            SELECT
                ph.slsp_dmst_id AS ph_id,
                ph.slsp_dmst_code AS ph_code,
                ph.slsp_name AS ph_name,
                vr.slsp_dmst_id AS vr_id,
                ph.slsp_cntr_id AS cntr
            FROM
                dw.sl_store_params ph
            JOIN
                dw.sl_store_params vr
                ON  ph.slsp_dmst_id = vr.slsp_phys_dmst_id 
                AND ph.slsp_cntr_id = vr.slsp_cntr_id
            WHERE
                ph.slsp_dmst_code != 'null'
        ) vp_dc
        ON  ors.orsor_dmst_id_src = vp_dc.vr_id 
        AND ors.orsor_cntr_id = vp_dc.cntr
    -- Join for direct DC relationship
    LEFT JOIN
        dw.sl_store_params direct_dc
        ON  ors.orsor_cntr_id = direct_dc.slsp_cntr_id 
        AND ors.orsor_dmst_id_src = direct_dc.slsp_dmst_id
    -- Modified WHERE clause to allow DTS records even if dc is NULL
    WHERE ors.orsor_type = 'DTS' OR COALESCE(vp_dc.ph_code, direct_dc.slsp_dmst_code) IS NOT NULL
)


SELECT * FROM base_data;
;
