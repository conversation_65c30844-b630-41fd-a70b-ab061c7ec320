
import pandas as pd
import pyodbc
import numpy as np
from datetime import datetime
import time
from functools import wraps
from datetime import datetime

pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)

# Python decorator to measure execution time of Functions
def timeit(func):
    @wraps(func)
    def timeit_wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        total_time = end_time - start_time
        if func.__name__ == "Replenishment_Model_Running":
            print(
                f" \n{func.__name__} is done! Elapsed time (sec & min): {total_time:.2f} sec which is {total_time/60:.2f} min"
            )
        else:
            print(f" \n{func.__name__} is done! Elapsed time: {total_time:.1f} sec which is {total_time/60:.1f} min")
        return result

    return timeit_wrapper

f"""

WITH delivered_cases AS(
    select
    CAST(CONCAT(a.int_cntr_id,a.store) AS INT) as store ,
    cal.dtdw_day_desc_en as day,
    COUNT(distinct(CONCAT(CONCAT(a.int_cntr_id,a.store),a.ref_no)))/{nr_weeks} as no_of_delivery_total
    from stg_go.go_106_order_receiving a
    LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product and b.cntr_id = a.int_cntr_id
    LEFT JOIN tesco_analysts.hierarchy_spm d ON b.dmat_div_code = lpad(d.div_code,4,"0")
    AND b.dmat_dep_code = lpad(d.dep_code,4,"0")
    AND b.dmat_sec_code = lpad(d.sec_code,4,"0")
    AND b.dmat_grp_code = lpad(d.grp_code,4,"0")
    AND b.dmat_sgr_code = lpad(d.sgr_code,4,"0")
    JOIN dm.dim_stores stores ON CAST(stores.dmst_store_code as INT) = CAST(CONCAT(a.int_cntr_id,a.store) AS INT)
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = TRANSLATE(SUBSTRING(a.act_del_datetime, 1,10),"-","")
    Where stores.convenience IN ('Convenience', 'HM')
    AND cal.dmtm_fw_code BETWEEN {start} AND {end}
    GROUP BY  stores.cntr_code, CONCAT(a.int_cntr_id,a.store) ,  cal.dtdw_day_desc_en
    ORDER BY  stores.cntr_code, CONCAT(a.int_cntr_id,a.store) ,  cal.dtdw_day_desc_en),


    sold_units AS (
        SELECT
            CAST(stores.dmst_store_code AS INT) AS store, 
            cal.dtdw_day_desc_en as day
            SUM(sunit.slsms_unit){nr_weeks} AS sold_units
        FROM dw.sl_sms sunit 
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
        JOIN dm.dim_stores stores 
            ON stores.cntr_id = sunit.slsms_cntr_id 
            AND stores.dmst_store_id = sunit.slsms_dmst_id
        WHERE stores.cntr_code IN ('SK', 'CZ', 'HU')
            AND cal.dmtm_fw_code BETWEEN {start} AND {end}
            AND sunit.slsms_unit > 0
        GROUP BY 
            stores.dmst_store_code,
            cal.dtdw_day_desc_en)
    SELECT 
        COALESCE(s.store, d.store) AS store,
        COALESCE(s.day, d.day) AS day,
        COALESCE(s.no_of_delivery_total, 0) AS no_of_delivery_total,
        COALESCE(d.sold_units, 0) AS sold_units,
    FROM delivered_cases s
    FULL OUTER JOIN sold_units d ON 
        s.store = d.store AND
        s.day = d.day






    
    """

@timeit
def cloth_sold_del_stock_unit(start, end):

    with pyodbc.connect(
        "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
    ) as conn:
       
        
        
        sql = f"""
        
    WITH hierarchy_fields AS (
        SELECT DISTINCT
            slad_dmat_id,
            cntr_id,
            cntr_code,
            slad_tpnb,
            slad_tpn,
            slad_long_des,
            dmat_div_des_en AS DIV_DESC,
            dmat_div_code AS DIV_ID,
            dmat_dep_des_en AS DEP_DESC,
            dmat_dep_code AS DEP_ID,
            dmat_sec_des_en AS SEC_DESC,
            dmat_sec_code AS SEC_ID,
            dmat_grp_des_en AS GRP_DESC,
            dmat_grp_code AS GRP_ID,
            dmat_sgr_des_en AS SGR_DESC,
            dmat_sgr_code AS SGR_ID
        FROM DM.dim_artgld_details
    ),
    sold_units AS (
        SELECT
            h.cntr_code AS country,
            CAST(stores.dmst_store_code AS INT) AS store, 
            SUBSTRING(d.pmg, 1, 3) AS dep,
            d.pmg,
            h.slad_tpnb AS TPNB,
            h.slad_tpn AS TPN,
            h.slad_long_des AS product_name,
            h.DIV_DESC,
            h.DIV_ID,
            h.DEP_DESC,
            h.DEP_ID,
            h.SEC_DESC,
            h.SEC_ID,
            h.GRP_DESC,
            h.GRP_ID,
            h.SGR_DESC,
            h.SGR_ID,
            SUM(sunit.slsms_unit) AS sold_units
        FROM dw.sl_sms sunit 
        JOIN hierarchy_fields h
            ON h.slad_dmat_id = sunit.slsms_dmat_id 
            AND h.cntr_id = sunit.slsms_cntr_id
        JOIN dm.dim_stores stores 
            ON stores.cntr_id = sunit.slsms_cntr_id 
            AND stores.dmst_store_id = sunit.slsms_dmst_id
        JOIN tesco_analysts.hierarchy_spm d 
            ON h.DIV_ID = LPAD(d.div_code, 4, '0') 
            AND h.DEP_ID = LPAD(d.dep_code, 4, '0') 
            AND h.SEC_ID = LPAD(d.sec_code, 4, '0') 
            AND h.GRP_ID = LPAD(d.grp_code, 4, '0') 
            AND h.SGR_ID = LPAD(d.sgr_code, 4, '0') 
        WHERE stores.cntr_code IN ('SK', 'CZ', 'HU')
            AND sunit.part_col BETWEEN 20241025 AND 20241025
            AND SUBSTRING(d.pmg, 1, 3) = 'CLG'
            AND stores.dmst_store_code = 41520
            AND sunit.slsms_unit > 0
        GROUP BY 
            h.cntr_code,
            stores.dmst_store_code,
            SUBSTRING(d.pmg, 1, 3),
            d.pmg,
            h.slad_tpnb,
            h.slad_tpn,
            h.slad_long_des,
            h.DIV_DESC,
            h.DIV_ID,
            h.DEP_DESC,
            h.DEP_ID,
            h.SEC_DESC,
            h.SEC_ID,
            h.GRP_DESC,
            h.GRP_ID,
            h.SGR_DESC,
            h.SGR_ID
    ),
    delivered_units AS (
        SELECT 
            CASE 
                WHEN a.int_cntr_id = 1 THEN 'CZ'
                WHEN a.int_cntr_id = 2 THEN 'SK'
                WHEN a.int_cntr_id = 4 THEN 'HU'
            END AS country,
            CAST(CONCAT(a.int_cntr_id, a.store) AS INT) AS store,
            SUBSTRING(d.pmg, 1, 3) AS dep,
            d.pmg,
            h.slad_tpnb AS TPNB,
            h.slad_tpn AS TPN,
            h.slad_long_des AS product_name,
            h.DIV_DESC,
            h.DIV_ID,
            h.DEP_DESC,
            h.DEP_ID,
            h.SEC_DESC,
            h.SEC_ID,
            h.GRP_DESC,
            h.GRP_ID,
            h.SGR_DESC,
            h.SGR_ID,
            SUM(a.qty) AS unit_delivered
        FROM stg_go.go_106_order_receiving a
        JOIN hierarchy_fields h
            ON h.slad_tpnb = a.product 
            AND h.cntr_id = a.int_cntr_id
        JOIN tesco_analysts.hierarchy_spm d 
            ON h.DIV_ID = LPAD(d.div_code, 4, '0')
            AND h.DEP_ID = LPAD(d.dep_code, 4, '0')
            AND h.SEC_ID = LPAD(d.sec_code, 4, '0')
            AND h.GRP_ID = LPAD(d.grp_code, 4, '0')
            AND h.SGR_ID = LPAD(d.sgr_code, 4, '0')
        WHERE a.int_cntr_id IN (1, 2, 4)
            AND a.part_col BETWEEN 20241025 AND 20241025
            AND SUBSTRING(d.pmg, 1, 3) = 'CLG'
            AND CONCAT(a.int_cntr_id, a.store) = 41520
            AND a.qty > 0
        GROUP BY 
            a.int_cntr_id,
            a.store,
            d.pmg,
            h.slad_tpnb,
            h.slad_tpn,
            h.slad_long_des,
            h.DIV_DESC,
            h.DIV_ID,
            h.DEP_DESC,
            h.DEP_ID,
            h.SEC_DESC,
            h.SEC_ID,
            h.GRP_DESC,
            h.GRP_ID,
            h.SGR_DESC,
            h.SGR_ID
    ),
    avg_stock AS (
        SELECT 
            stores.cntr_code AS country,
            CAST(stores.dmst_store_code AS INT) AS store,
            SUBSTRING(d.pmg, 1, 3) AS dep,
            d.pmg,
            h.slad_tpnb AS TPNB,
            h.slad_tpn AS TPN,
            h.slad_long_des AS product_name,
            h.DIV_DESC,
            h.DIV_ID,
            h.DEP_DESC,
            h.DEP_ID,
            h.SEC_DESC,
            h.SEC_ID,
            h.GRP_DESC,
            h.GRP_ID,
            h.SGR_DESC,
            h.SGR_ID,
            ROUND(AVG(CAST(stock.slstks_stock_unit_sl AS DECIMAL(18,2))), 2) AS avg_stock
        FROM dw.sl_stocks stock
        JOIN hierarchy_fields h
            ON h.slad_dmat_id = stock.slstks_dmat_id 
            AND h.cntr_id = stock.slstks_cntr_id
        JOIN dm.dim_stores stores 
            ON stores.cntr_id = stock.slstks_cntr_id 
            AND stores.dmst_store_id = stock.slstks_dmst_id
        JOIN tesco_analysts.hierarchy_spm d 
            ON h.DIV_ID = LPAD(d.div_code, 4, '0')
            AND h.DEP_ID = LPAD(d.dep_code, 4, '0')
            AND h.SEC_ID = LPAD(d.sec_code, 4, '0')
            AND h.GRP_ID = LPAD(d.grp_code, 4, '0')
            AND h.SGR_ID = LPAD(d.sgr_code, 4, '0')
        WHERE stores.cntr_code IN ('SK', 'CZ', 'HU')
            AND stock.part_col BETWEEN 20241025 AND 20241025
            AND SUBSTRING(d.pmg, 1, 3) = 'CLG'
            AND stores.dmst_store_code = 41520
            AND stock.slstks_stock_unit_sl > 0
        GROUP BY 
            stores.cntr_code,
            stores.dmst_store_code,
            d.pmg,
            h.slad_tpnb,
            h.slad_tpn,
            h.slad_long_des,
            h.DIV_DESC,
            h.DIV_ID,
            h.DEP_DESC,
            h.DEP_ID,
            h.SEC_DESC,
            h.SEC_ID,
            h.GRP_DESC,
            h.GRP_ID,
            h.SGR_DESC,
            h.SGR_ID
    )
    SELECT 
        COALESCE(s.country, d.country, a.country) AS country,
        COALESCE(s.store, d.store, a.store) AS store,
        COALESCE(s.dep, d.dep, a.dep) AS dep,
        COALESCE(s.pmg, d.pmg, a.pmg) AS pmg,
        COALESCE(s.TPNB, d.TPNB, a.TPNB) AS TPNB,
        COALESCE(s.TPN, d.TPN, a.TPN) AS TPN,
        COALESCE(s.product_name, d.product_name, a.product_name) AS product_name,
        COALESCE(s.DIV_DESC, d.DIV_DESC, a.DIV_DESC) AS DIV_DESC,
        COALESCE(s.DIV_ID, d.DIV_ID, a.DIV_ID) AS DIV_ID,
        COALESCE(s.DEP_DESC, d.DEP_DESC, a.DEP_DESC) AS DEP_DESC,
        COALESCE(s.DEP_ID, d.DEP_ID, a.DEP_ID) AS DEP_ID,
        COALESCE(s.SEC_DESC, d.SEC_DESC, a.SEC_DESC) AS SEC_DESC,
        COALESCE(s.SEC_ID, d.SEC_ID, a.SEC_ID) AS SEC_ID,
        COALESCE(s.GRP_DESC, d.GRP_DESC, a.GRP_DESC) AS GRP_DESC,
        COALESCE(s.GRP_ID, d.GRP_ID, a.GRP_ID) AS GRP_ID,
        COALESCE(s.SGR_DESC, d.SGR_DESC, a.SGR_DESC) AS SGR_DESC,
        COALESCE(s.SGR_ID, d.SGR_ID, a.SGR_ID) AS SGR_ID,
        COALESCE(s.sold_units, 0) AS sold_units,
        COALESCE(d.unit_delivered, 0) AS unit_delivered,
        COALESCE(a.avg_stock, 0) AS avg_stock
    FROM sold_units s
    FULL OUTER JOIN delivered_units d ON 
        s.country = d.country AND
        s.store = d.store AND
        s.dep = d.dep AND
        s.pmg = d.pmg AND
        s.TPNB = d.TPNB AND
        s.TPN = d.TPN AND
        s.product_name = d.product_name AND
        s.DIV_DESC = d.DIV_DESC AND
        s.DIV_ID = d.DIV_ID AND
        s.DEP_DESC = d.DEP_DESC AND
        s.DEP_ID = d.DEP_ID AND
        s.SEC_DESC = d.SEC_DESC AND
        s.SEC_ID = d.SEC_ID AND
        s.GRP_DESC = d.GRP_DESC AND
        s.GRP_ID = d.GRP_ID AND
        s.SGR_DESC = d.SGR_DESC AND
        s.SGR_ID = d.SGR_ID
    FULL OUTER JOIN avg_stock a ON 
        COALESCE(s.country, d.country) = a.country AND
        COALESCE(s.store, d.store) = a.store AND
        COALESCE(s.dep, d.dep) = a.dep AND
        COALESCE(s.pmg, d.pmg) = a.pmg AND
        COALESCE(s.TPNB, d.TPNB) = a.TPNB AND
        COALESCE(s.TPN, d.TPN) = a.TPN AND
        COALESCE(s.product_name, d.product_name) = a.product_name AND
        COALESCE(s.DIV_DESC, d.DIV_DESC) = a.DIV_DESC AND
        COALESCE(s.DIV_ID, d.DIV_ID) = a.DIV_ID AND
        COALESCE(s.DEP_DESC, d.DEP_DESC) = a.DEP_DESC AND
        COALESCE(s.DEP_ID, d.DEP_ID) = a.DEP_ID AND
        COALESCE(s.SEC_DESC, d.SEC_DESC) = a.SEC_DESC AND
        COALESCE(s.SEC_ID, d.SEC_ID) = a.SEC_ID AND
        COALESCE(s.GRP_DESC, d.GRP_DESC) = a.GRP_DESC AND
        COALESCE(s.GRP_ID, d.GRP_ID) = a.GRP_ID AND
        COALESCE(s.SGR_DESC, d.SGR_DESC) = a.SGR_DESC AND
        COALESCE(s.SGR_ID, d.SGR_ID) = a.SGR_ID
    ORDER BY country, dep  """
        
        
        
        # sql = f"""
        
        # WITH hierarchy_fields AS (
        #     SELECT DISTINCT
        #         slad_dmat_id,
        #         cntr_id,
        #         slad_tpnb,
        #         CONCAT(
        #             dmat_div_code, '|', dmat_dep_code, '|',
        #             dmat_sec_code, '|', dmat_grp_code, '|', dmat_sgr_code
        #         ) AS hierarchy_key,
        #         slad_tpn,
        #         slad_long_des AS product_name,
        #         dmat_div_des_en AS DIV_DESC,
        #         dmat_div_code AS DIV_ID,
        #         dmat_dep_des_en AS DEP_DESC,
        #         dmat_dep_code AS DEP_ID,
        #         dmat_sec_des_en AS SEC_DESC,
        #         dmat_sec_code AS SEC_ID,
        #         dmat_grp_des_en AS GRP_DESC,
        #         dmat_grp_code AS GRP_ID,
        #         dmat_sgr_des_en AS SGR_DESC,
        #         dmat_sgr_code AS SGR_ID
        #     FROM DM.dim_artgld_details
        # ),
        # spm_hierarchy AS (
        #     SELECT 
        #         pmg,
        #         CONCAT(
        #             LPAD(div_code, 4, '0'), '|',
        #             LPAD(dep_code, 4, '0'), '|',
        #             LPAD(sec_code, 4, '0'), '|',
        #             LPAD(grp_code, 4, '0'), '|',
        #             LPAD(sgr_code, 4, '0')
        #         ) AS hierarchy_key
        #     FROM tesco_analysts.hierarchy_spm
        #     WHERE SUBSTRING(pmg, 1, 3) = 'CLG'
        # ),
        # base_data AS (
        #     SELECT
        #         h.slad_tpnb AS TPNB,
        #         h.slad_tpn AS TPN,
        #         h.product_name,
        #         SUBSTRING(d.pmg, 1, 3) AS dep,
        #         d.pmg,
        #         h.DIV_DESC, h.DIV_ID, h.DEP_DESC, h.DEP_ID,
        #         h.SEC_DESC, h.SEC_ID, h.GRP_DESC, h.GRP_ID,
        #         h.SGR_DESC, h.SGR_ID,
        #         SUM(COALESCE(su.slsms_unit, 0)) AS sold_units,
        #         SUM(COALESCE(du.qty, 0)) AS unit_delivered,
        #         ROUND(AVG(COALESCE(st.slstks_stock_unit_sl, 0)), 2) AS avg_stock
        #     FROM hierarchy_fields h
        #     JOIN spm_hierarchy d ON d.hierarchy_key = h.hierarchy_key
        #     LEFT JOIN dw.sl_sms su ON 
        #         h.slad_dmat_id = su.slsms_dmat_id 
        #         AND h.cntr_id = su.slsms_cntr_id
        #         AND su.part_col BETWEEN 20240527 AND 20240901
        #         AND su.slsms_unit > 0
        #     LEFT JOIN stg_go.go_106_order_receiving du ON 
        #         h.slad_tpnb = du.product 
        #         AND h.cntr_id = du.int_cntr_id
        #         AND du.part_col BETWEEN 20240527 AND 20240901
        #         AND du.qty > 0
        #     LEFT JOIN dw.sl_stocks st ON 
        #         h.slad_dmat_id = st.slstks_dmat_id 
        #         AND h.cntr_id = st.slstks_cntr_id
        #         AND st.part_col BETWEEN 20240527 AND 20240901
        #         AND st.slstks_stock_unit_sl > 0
        #     WHERE EXISTS (
        #         SELECT 1 FROM dm.dim_stores s 
        #         WHERE s.cntr_id IN (1, 2, 4)
        #         AND s.dmst_store_code = '41520'
        #     )
        #     GROUP BY 
        #         h.slad_tpnb, h.slad_tpn, h.product_name,
        #         SUBSTRING(d.pmg, 1, 3), d.pmg,
        #         h.DIV_DESC, h.DIV_ID, h.DEP_DESC, h.DEP_ID,
        #         h.SEC_DESC, h.SEC_ID, h.GRP_DESC, h.GRP_ID,
        #         h.SGR_DESC, h.SGR_ID
        # )
        # SELECT 
        #     TPNB, TPN, product_name, dep, pmg,
        #     DIV_DESC, DIV_ID, DEP_DESC, DEP_ID,
        #     SEC_DESC, SEC_ID, GRP_DESC, GRP_ID,
        #     SGR_DESC, SGR_ID,
        #     sold_units, unit_delivered, avg_stock
        # FROM base_data
        # ORDER BY dep, TPNB
        
        # """    
    
        
        
        b = pd.read_sql(sql, conn)
        
        return b
    
start = '20240527'
end = '20240901'
    
df = cloth_sold_del_stock_unit(start, end)
        
        
        