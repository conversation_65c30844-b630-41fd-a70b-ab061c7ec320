#!/usr/bin/env python3
"""
Performance testing for the delivered.py script
"""

import time
import sys
import os
import psutil
import threading
from pathlib import Path

sys.path.append('.')
import delivered

def monitor_resources(duration=120, interval=5):
    """Monitor system resources during execution"""
    print(f'📊 Starting resource monitoring for {duration} seconds...')
    
    start_time = time.time()
    measurements = []
    
    while time.time() - start_time < duration:
        try:
            # Get current process info
            process = psutil.Process()
            
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Process metrics
            process_memory = process.memory_info()
            process_cpu = process.cpu_percent()
            
            measurement = {
                'timestamp': time.time() - start_time,
                'system_cpu': cpu_percent,
                'system_memory_used': memory.used / (1024**3),  # GB
                'system_memory_percent': memory.percent,
                'process_memory_rss': process_memory.rss / (1024**2),  # MB
                'process_memory_vms': process_memory.vms / (1024**2),  # MB
                'process_cpu': process_cpu
            }
            
            measurements.append(measurement)
            time.sleep(interval)
            
        except Exception as e:
            print(f'⚠️  Resource monitoring error: {e}')
            break
    
    return measurements

def analyze_performance(measurements):
    """Analyze performance measurements"""
    if not measurements:
        print('❌ No performance data to analyze')
        return
    
    print('\n📈 Performance Analysis:')
    print('=' * 40)
    
    # Calculate averages
    avg_cpu = sum(m['system_cpu'] for m in measurements) / len(measurements)
    avg_memory = sum(m['system_memory_percent'] for m in measurements) / len(measurements)
    max_process_memory = max(m['process_memory_rss'] for m in measurements)
    avg_process_cpu = sum(m['process_cpu'] for m in measurements) / len(measurements)
    
    print(f'⏱️  Total monitoring time: {measurements[-1]["timestamp"]:.1f} seconds')
    print(f'🖥️  Average system CPU usage: {avg_cpu:.1f}%')
    print(f'💾 Average system memory usage: {avg_memory:.1f}%')
    print(f'📊 Peak process memory usage: {max_process_memory:.1f} MB')
    print(f'⚡ Average process CPU usage: {avg_process_cpu:.1f}%')
    
    # Performance assessment
    print('\n🎯 Performance Assessment:')
    if avg_cpu < 50:
        print('✅ CPU usage: Good (< 50%)')
    elif avg_cpu < 80:
        print('⚠️  CPU usage: Moderate (50-80%)')
    else:
        print('❌ CPU usage: High (> 80%)')
    
    if max_process_memory < 500:
        print('✅ Memory usage: Good (< 500 MB)')
    elif max_process_memory < 1000:
        print('⚠️  Memory usage: Moderate (500-1000 MB)')
    else:
        print('❌ Memory usage: High (> 1000 MB)')

def test_connection_performance():
    """Test SSH connection performance"""
    print('🔌 Testing SSH connection performance...')
    
    import paramiko
    
    connection_times = []
    
    for i in range(3):
        print(f'   Attempt {i+1}/3...')
        start_time = time.time()
        
        try:
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh_client.connect(
                hostname=delivered.hostname, 
                username=delivered.username, 
                password=delivered.password, 
                timeout=30
            )
            
            connection_time = time.time() - start_time
            connection_times.append(connection_time)
            
            # Test a simple command
            cmd_start = time.time()
            _, stdout, stderr = ssh_client.exec_command('echo "test"')
            stdout.read()
            cmd_time = time.time() - cmd_start
            
            ssh_client.close()
            
            print(f'   ✅ Connection: {connection_time:.2f}s, Command: {cmd_time:.2f}s')
            
        except Exception as e:
            print(f'   ❌ Connection failed: {e}')
            
        time.sleep(1)  # Brief pause between attempts
    
    if connection_times:
        avg_connection_time = sum(connection_times) / len(connection_times)
        print(f'\n📊 Average connection time: {avg_connection_time:.2f} seconds')
        
        if avg_connection_time < 2:
            print('✅ Connection performance: Excellent (< 2s)')
        elif avg_connection_time < 5:
            print('⚠️  Connection performance: Good (2-5s)')
        else:
            print('❌ Connection performance: Slow (> 5s)')

def test_file_operations():
    """Test file operation performance"""
    print('\n📁 Testing file operation performance...')
    
    # Test directory creation
    start_time = time.time()
    test_dir = Path('test_performance_dir')
    test_dir.mkdir(exist_ok=True)
    (test_dir / 'subdir').mkdir(exist_ok=True)
    dir_time = time.time() - start_time
    
    # Test file writing
    start_time = time.time()
    test_file = test_dir / 'test_file.sql'
    test_content = "SELECT * FROM test_table WHERE date BETWEEN 20240101 AND 20240131;" * 100
    test_file.write_text(test_content)
    write_time = time.time() - start_time
    
    # Test file reading
    start_time = time.time()
    content = test_file.read_text()
    read_time = time.time() - start_time
    
    # Test regex operations
    import re
    start_time = time.time()
    pattern = r"(?<=BETWEEN\s)(\d{8})"
    modified_content = re.sub(pattern, "20250630", content)
    regex_time = time.time() - start_time
    
    # Cleanup
    import shutil
    shutil.rmtree(test_dir, ignore_errors=True)
    
    print(f'   📂 Directory creation: {dir_time*1000:.1f}ms')
    print(f'   ✍️  File writing: {write_time*1000:.1f}ms')
    print(f'   📖 File reading: {read_time*1000:.1f}ms')
    print(f'   🔍 Regex processing: {regex_time*1000:.1f}ms')
    
    total_file_ops = dir_time + write_time + read_time + regex_time
    print(f'   📊 Total file operations: {total_file_ops*1000:.1f}ms')

if __name__ == "__main__":
    print("🚀 Performance Testing Suite")
    print("=" * 50)
    
    # Test individual components
    test_connection_performance()
    test_file_operations()
    
    print("\n" + "=" * 50)
    print("✅ Performance testing completed!")
    print("\n💡 Note: For full end-to-end performance testing,")
    print("   run the main delivered.py script and monitor system resources.")
