class What_Ifs_Inputs:
    def __init__(
        self,
        sheet_name: str,
        repl_type: str,
        TPN_Cost_Base: bool,
        opening_type: bool,
        volume_modifier: bool,
        case_cap_modifier: bool,
        shelf_capacity_modifier: bool,
        chunk_size: int,
        only_WH: bool
    ):

        self.sheet_name = sheet_name
        self.repl_type = repl_type
        self.TPN_Cost_Base = TPN_Cost_Base
        self.opening_type = opening_type
        self.volume_modifier = volume_modifier
        self.case_cap_modifier = case_cap_modifier
        self.shelf_capacity_modifier = shelf_capacity_modifier
        self.chunk_size = chunk_size
        self.only_WH = only_WH
