import pandas as pd


a = pd.read_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\opsdev_verz_0119.parquet"
)

b = pd.read_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\opsdev_split_pallet_version.parquet"
)


op = pd.DataFrame()

for x in [a, b]:

    c = x[["store", "tpnb"]].drop_duplicates()
    op = pd.concat([op, c]).drop_duplicates()


opsdev_final_a = op.merge(a, on=["store", "tpnb"], how="left")

opsdev_final_null = opsdev_final_a[opsdev_final_a.srp.isnull()]

opsdev_final_b = (
    opsdev_final_null[["store", "tpnb"]]
    .drop_duplicates()
    .merge(b, on=["store", "tpnb"], how="left")
)


opsdev_final_a = opsdev_final_a[opsdev_final_a.srp.notnull()]

opsdev_final = pd.concat([opsdev_final_a, opsdev_final_b])


opsdev_final.to_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\opsdev_verz_0119_.parquet",
    index=False,
    compression="gzip",
)
