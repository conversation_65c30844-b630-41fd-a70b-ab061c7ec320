{"cells": [{"cell_type": "code", "execution_count": null, "id": "6dd919af-c39d-45d5-a62e-d0affe271df9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import polars as pl\n", "from pathlib import Path\n", "import numpy as np\n", "\n", "excel_inputs_f = \"inputs/Repl/Stores_Inputs_2024_Q1_add_rumbleExtraBack.xlsx\" #Stores_Inputs_2024_Q1_add_rumbleExtraBack.xlsx\n", "directory = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\")\n", "items_sold_f = r\"inputs\\files_for_dataset\\Dataset_24\\item_sold_ce_test_hier\"\n", "items_sold_dotcom = r\"inputs\\files_for_dataset\\Dataset_24\\item_sold_dotcom_ce_test_1\"\n", "stock_f = r\"inputs\\files_for_dataset\\Dataset_24\\stock_ce_test_1\"\n", "ops_dev_f = r\"inputs\\files_for_dataset\\Dataset_24\\CE_JDA_SRD_for_model_modified\"\n", "box_op_type_f = r\"inputs\\files_for_dataset\\Dataset_24\\ownbrand_opening_type_22-09-2023.xlsx\"\n", "cases_f = r\"inputs\\files_for_dataset\\Dataset_24\\cases_ce\"\n", "pallet_capacity_f = r\"inputs\\files_for_dataset\\Dataset_24\\pallet_capacity_CE_2023w14_27\"\n", "broken_cases_f = r\"inputs\\files_for_dataset\\Dataset_24\\broken_cases_list_2023w14_27.csv.gz\"\n", "single_pick_f = r\"\\\\euprgvmfs01\\GMforCentralEurope\\Supply Chain\\Single Picking\\Single pick list.xlsx\" \n", "foil_f = r\"inputs\\files_for_dataset\\Dataset_24\\foil_new_new_new\"\n", "shelfCapacity = r\"inputs\\files_for_dataset\\plano\\planogram_w14_w27_repaired.csv.gz\"\n", "presort_pal_del = r\"inputs\\files_for_dataset\\PBS_PBL_presort\\presort_pal_del\"\n", "stores = list(\n", "    pl.read_excel(directory / excel_inputs_f, engine=\"calamine\")[\"Store\"]\n", "    .unique()\n", ")\n", "\n", "def Store_Inputs_Creator(directory, excel_inputs_f, stores):\n", "\n", "    # Store inputs\n", "\n", "    pmg_df = pd.read_excel(directory / excel_inputs_f, \"pmg\")\n", "    pmg_df = pmg_df[pmg_df.Area == \"Replenishment\"]\n", "    store_list_df = pd.read_excel(directory / excel_inputs_f, \"store_list\")\n", "    store_list_df = store_list_df.loc[store_list_df.Store.isin(stores)]\n", "    capping_df = pd.read_excel(directory / excel_inputs_f, \"capping\")\n", "    capping_df[\"is_capping_shelf\"] = 1\n", "    Dprofiles_df = pd.read_excel(directory / excel_inputs_f, \"Dprofiles\")\n", "    Pprofiles_df = pd.read_excel(directory / excel_inputs_f, \"Pprofiles\")\n", "    pmg_array = pmg_df.values\n", "    store_array = store_list_df.values\n", "    result = len(store_array) * len(pmg_array)  #\n", "    df_array = np.empty([result, 9], dtype=\"object\")  # create an empty array\n", "    counter = 0\n", "    for s in range(len(store_array)):\n", "        for p in range(len(pmg_array)):\n", "            df_array[counter][0] = store_array[s][1]  # country\n", "            df_array[counter][1] = store_array[s][0]  # store\n", "            df_array[counter][2] = store_array[s][2]  # store_name\n", "            df_array[counter][3] = store_array[s][3]  # sqm\n", "            df_array[counter][4] = store_array[s][4]  # format\n", "            df_array[counter][5] = pmg_array[p][0]  # pmg\n", "            df_array[counter][6] = pmg_array[p][1]  # pmg_name\n", "            df_array[counter][7] = pmg_array[p][2]  # dep\n", "            df_array[counter][8] = pmg_array[p][3]  # division\n", "            counter += 1\n", "    store_inputs = pd.DataFrame(\n", "        columns=[\n", "            \"Country\",\n", "            \"Store\",\n", "            \"Store Name\",\n", "            \"Plan Size\",\n", "            \"Format\",\n", "            \"Pmg\",\n", "            \"Pmg Name\",\n", "            \"Dep\",\n", "            \"Division\",\n", "        ]\n", "    )\n", "    store_inputs = pd.concat(\n", "        [store_inputs, pd.DataFrame(df_array, columns=store_inputs.columns)]\n", "    )\n", "    store_inputs[\"Dep\"] = np.where(store_inputs.Pmg == \"HDL01\", \"NEW\", store_inputs.Dep)\n", "    store_inputs = store_inputs.merge(capping_df, on=[\"Store\", \"Pmg\"], how=\"left\")\n", "    store_inputs[\"is_capping_shelf\"] = store_inputs[\"is_capping_shelf\"].replace(\n", "        np.nan, 0\n", "    )\n", "    store_inputs = store_inputs.merge(Dprofiles_df, on=[\"Store\", \"Dep\"], how=\"left\")\n", "    store_inputs = store_inputs.merge(\n", "        Pprofiles_df, on=[\"Country\", \"Format\", \"Pmg\"], how=\"left\"\n", "    )\n", "    store_inputs[\"Store\"] = store_inputs[\"Store\"].astype(\"int64\")\n", "    \n", "    # store_inputs.drop(['Pallet_%', 'Pre_sort_perc_by_pmg'], axis=1, inplace=True)\n", "    \n", "    # store_inputs.columns = [i.lower() for i in store_inputs.columns]\n", "    return store_inputs\n", "\n", "\n", "store_inputs = Store_Inputs_Creator(directory, excel_inputs_f, stores)"]}, {"cell_type": "code", "execution_count": null, "id": "044fd0f7-69f1-4a36-8324-fc0b367ce10c", "metadata": {}, "outputs": [], "source": ["with pl.<PERSON><PERSON><PERSON>():\n", "    print(\"\\n###########\")\n", "    print(\"Repl_Dataset Build: has been started to calculate...\")\n", "    print(\"###########\\n\")\n", "\n", "    #Creating Base for Repl_Dataset\n", "    weekdays = [\n", "        \"Monday\",\n", "        \"Tuesday\",\n", "        \"Wednesday\",\n", "        \"Thursday\",\n", "        \"Friday\",\n", "        \"Saturday\",\n", "        \"Sunday\",\n", "    ]\n", "    store_inputs_lower = store_inputs.copy()\n", "    store_inputs_lower.columns = [i.lower() for i in store_inputs_lower.columns]\n", "    store_inputs_lower = store_inputs_lower.rename(columns={\"pmg name\": \"pmg_name\"})\n", "\n", "    store_inputs_lower = store_inputs_lower[\n", "        [\"store\", \"format\", \"division\", \"pmg\", \"dep\", \"is_capping_shelf\"]\n", "    ].drop_duplicates()\n", "    store_inputs_lower = pl.from_pandas(store_inputs_lower)\n", "    \n", "\n", "    single_pick_df = pl.read_excel(single_pick_f).rename({'TPN':'tpn'}).with_columns(pl.lit(1).alias(\"single_pick\")).select(['tpn','single_pick'])\n", "    try:\n", "        opsdev = pl.read_parquet(directory/ops_dev_f)\\\n", "        .with_columns(pl.col('tpnb').cast(pl.Int64),\n", "                                    pl.col('store').cast(pl.Int64))\\\n", "        .select(\n", "                    [\n", "                        \"store\",\n", "                        \"tpnb\",\n", "                        \"srp\",\n", "                        \"nsrp\",\n", "                        \"mu\",\n", "                        \"full_pallet\",\n", "                        \"split_pallet\",\n", "                        \"icream_nsrp\",\n", "                        \"checkout_stand_flag\",\n", "                        \"clipstrip_flag\",\n", "                        \"backroom_flag\",\n", "                        \"shelfCapacity\"\n", "                        \n", "                    ]\n", "                )\n", "    except:\n", "        opsdev = pl.from_pandas(opsdev_df)\\\n", "        .with_columns(pl.col('tpnb').cast(pl.Int64),\n", "                                    pl.col('store').cast(pl.Int64))\\\n", "        .select(\n", "                    [\n", "                        \"store\",\n", "                        \"tpnb\",\n", "                        \"srp\",\n", "                        \"nsrp\",\n", "                        \"mu\",\n", "                        \"full_pallet\",\n", "                        \"split_pallet\",\n", "                        \"icream_nsrp\",\n", "                        \"checkout_stand_flag\",\n", "                        \"clipstrip_flag\",\n", "                        \"backroom_flag\",\n", "                        \"shelfCapacity\"\n", "                    \n", "                    ]\n", "                )\n", "    \n", "    stock = pl.read_parquet(directory/stock_f).select([\"store\", \"day\", \"tpnb\", \"stock\", \"item_price\"])\n", "    \n", "    isold = pl.read_parquet(directory/items_sold_f).with_columns(\n", "    \n", "        [pl.col(\"day\").cast(pl.Utf8, strict=False),\n", "        pl.col(\"pmg\").cast(pl.Utf8, strict=False),\n", "        pl.col(\"country\").cast(pl.Utf8, strict=False)]\n", "    ).rename({\"division\": \"division_hier\"})\n", "\n", "    isold_dotcom = pl.read_parquet(directory/items_sold_dotcom)\\\n", "    .select([\"store\", \"day\", \"tpnb\", \"sold_units_dotcom\"])\n", "    \n", "    cases = pl.read_parquet(directory/cases_f)\\\n", "    .select([\"store\", \"day\", \"tpnb\", \"unit\"])\n", "    try:\n", "        pallet_cap = pl.read_parquet(directory/pallet_capacity_f)\n", "    except:\n", "        pallet_cap = pl.from_pandas(pallet_cap_df)\n", "    \n", "    op_type = pl.read_excel(directory/box_op_type_f)\n", "    try:\n", "        foil = pl.read_parquet(directory/foil_f)\\\n", "        .select(pl.all().exclude(\n", "                        [\"level4\", \"country\"]\n", "        )).with_columns(pl.col(\"store\").cast(pl.Int64),\n", "                        pl.col(\"tpnb\").cast(pl.Int64))\n", "    except:\n", "        foil = pl.from_pandas(foil_df)\\\n", "        .select(pl.all().exclude(\n", "                        [\"level4\", \"country\"]\n", "        )).with_columns(pl.col(\"store\").cast(pl.Int64),\n", "                        pl.col(\"tpnb\").cast(pl.Int64))\n", "    try:\n", "        shelfCapacity_df = pl.read_csv(directory/shelfCapacity)\\\n", "            .select(['store','tpnb','capacity', 'icase']).unique()\n", "    except:\n", "        shelfCapacity_df = pl.from_pandas(shelfcap_df)\\\n", "            .select(['store','tpnb','capacity', 'icase']).unique()\\\n", "                .with_columns(pl.col(\"store\").cast(pl.Int64),\n", "                                pl.col(\"tpnb\").cast(pl.Int64))\n", "\n", "    print(\"\\nInputs are loaded into Memory!\")\n", "    \n", "\n", "    def debug(result, info=\"\"):\n", "        print(f\"{info} {result.shape}\" )\n", "        return result\n", "    \n", "    def all_tpn_all_day(isold):\n", "        \n", "        \n", "        result = isold.select([\"store\", \"pmg\", \"tpnb\"]).unique()\\\n", "        .with_columns([pl.lit(None).alias(\"day\")])\\\n", "        .with_columns(\n", "            [pl.col(\"day\").map_batches(lambda s: weekdays).alias(\"day\")]\n", "        )\\\n", "        .explode(\"day\").unique()\\\n", "        .with_columns(\n", "        [pl.col(\"day\").cast(pl.Utf8, strict=False)]\n", "        ).join(\n", "            isold.select([\"store\", \"pmg\", \"tpnb\", \"day\", \"sold_units\", \"sales_excl_vat\"]),\n", "            on=[\"store\", \"pmg\", \"tpnb\", \"day\"],\n", "            how=\"left\",\n", "        )\n", "\n", "\n", "        return result\n", "    \n", "    def join_all_table(Repl_Dataset):\n", "        \n", "        result = Repl_Dataset.join(\n", "            isold.select(\n", "                [\n", "                    pl.all().exclude(\n", "                [\"sold_units\", \"sales_excl_vat\", \"day\"])\n", "                \n", "                ]\n", "                    ).unique(),\n", "                    on=[\"store\", \"pmg\", \"tpnb\"],\n", "                    how=\"left\")\\\n", "                .with_columns(\n", "                    [\n", "                        pl.when(pl.col(\"pmg\") == \"DRY18\")\n", "                        .then(pl.lit(\"DRY15\"))\n", "                        .otherwise(pl.col(\"pmg\"))\n", "                        .alias(\"pmg\")\n", "                    ])\\\n", "                .join(\n", "                    store_inputs_lower, on=[\"store\", \"pmg\"], how=\"left\"\n", "                )\\\n", "                .join(\n", "                    isold_dotcom, on=[\"store\", \"day\", \"tpnb\"], how=\"left\"\n", "                )\\\n", "                .join(stock, on=[\"store\", \"day\", \"tpnb\"], how=\"left\")\\\n", "                .join(opsdev, on=[\"tpnb\", \"store\"], how=\"left\")\\\n", "                .join(cases, on=[\"store\", \"day\", \"tpnb\"], how=\"left\")\\\n", "                .join(op_type, on=[\"country\", \"tpnb\"], how=\"left\")\\\n", "                .join(pallet_cap, on=[\"country\", \"tpnb\"], how=\"left\")\\\n", "                .join(foil, on=[\"store\", \"tpnb\"], how=\"left\")\\\n", "                .join(single_pick_df, on=\"tpn\", how=\"left\")\\\n", "                .join(shelfCapacity_df, on=['store','tpnb'], how='left')\\\n", "                .fill_null(0)\n", "        print(\"\\nAll Tables Combined!!\")\n", "        return result \n", "    \n", "    def mod_columns(Repl_Dataset):\n", "\n", "        pro_to_del = Repl_Dataset.filter((pl.col(\"dep\") == \"PRO\") & (pl.col(\"pmg\") != \"PRO16\") & (pl.col(\"pmg\") != \"PRO19\"))[\"pmg\"].unique().to_list()\n", "        \n", "        result = Repl_Dataset.with_columns(\n", "            [\n", "                pl.when(\n", "                    (pl.col(\"srp\") == 0)\n", "                    & (pl.col(\"nsrp\") == 0)\n", "                    & (pl.col(\"full_pallet\") == 0)\n", "                    & (pl.col(\"mu\") == 0)\n", "                    & (pl.col(\"split_pallet\") == 0)\n", "                    & (pl.col(\"icream_nsrp\") == 0)\n", "                )\n", "                .then(1)\n", "                .otherwise(pl.col(\"nsrp\"))\n", "                .alias(\"nsrp\"),\n", "                pl.when(pl.col(\"icase\") == 0)\n", "                .then(pl.col(\"case_capacity\"))\n", "                .otherwise(pl.col(\"icase\"))\n", "                .alias(\"icase\"),\n", "            ]\n", "        )\\\n", "        .drop(\"case_capacity\")\\\n", "        .rename({\"icase\": \"case_capacity\"})\\\n", "    .with_columns(\n", "            [(pl.col(\"unit\") / pl.col(\"case_capacity\")).alias(\"cases_delivered\")]\n", "        )\\\n", "        .with_columns(\n", "        [\n", "            pl.when(pl.col(\"unit_type\") != \"KG\")\n", "            .then(pl.lit(\"SNGL\"))\n", "            .otherwise(pl.col(\"unit_type\"))\n", "            .alias(\"unit_type\")\n", "        ]\n", "    )\\\n", "        .with_columns(\n", "        [\n", "            pl.when(pl.col(\"dep\") == \"NEW\")\n", "            .then(pl.lit(\"HDL\"))\n", "            .otherwise(pl.col(\"dep\"))\n", "            .alias(\"dep\")\n", "        ]\n", "    )\\\n", "        .with_columns(\n", "        [pl.col(\"pallet_capacity\").round(0).alias(\"pallet_capacity\")]\n", "    )\\\n", "        .with_columns(\n", "        [\n", "            pl.when((pl.col(\"srp\") > 0) & (pl.col(\"pmg\").str.contains(\"FRZ\")))\n", "            .then(1)\n", "            .otherwise(pl.col(\"nsrp\"))\n", "            .alias(\"nsrp\"),\n", "            pl.when((pl.col(\"srp\") > 0) & (pl.col(\"pmg\").str.contains(\"FRZ\")))\n", "            .then(0)\n", "            .otherwise(pl.col(\"srp\"))\n", "            .alias(\"srp\"),\n", "        ]\n", "    )\\\n", "        .with_columns(pl.when(pl.col(c) == 0).then(pl.col(c).mean().over(['country','pmg'])).otherwise(pl.col(c)).alias(c) for c in Repl_Dataset.columns if c in ['shelfCapacity', 'pallet_capacity', 'capacity'])\\\n", "        .with_columns(pl.when(pl.col(c) == 0).then(pl.col(c).mean().over(['country','tpnb'])).otherwise(pl.col(c)).alias(c) for c in Repl_Dataset.columns if c in ['item_price'])\\\n", "        .with_columns(pl.when(pl.col(\"stock\") == 0).then(pl.col('sold_units')).otherwise(pl.col(\"stock\")).alias(\"stock\"))\\\n", "        .with_columns(pl.when(pl.col(c) == 0).then(1).otherwise(pl.col(c)).alias(c) for c in Repl_Dataset.columns if c in ['shelfCapacity', 'capacity'])\\\n", "        .with_columns(pl.when(pl.col(\"opening_type\").is_null()).then(pl.lit(\"no_data\")).otherwise(pl.col(\"opening_type\")).alias(\"opening_type\"),\n", "                     pl.lit(\"Y\").alias('as_is_model_contains?'))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col('pmg') == 'SFM03')).then(pl.lit('SFP01')).otherwise(pl.col('pmg')).alias('pmg'))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col('pmg') == 'SFP01')).then(pl.lit('SFP')).otherwise(pl.col('dep')).alias('dep'))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col(\"case_capacity\") == 0)).then(3).otherwise(pl.col(\"case_capacity\")).alias(\"case_capacity\"))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col(\"pmg\").is_in([\"HDL06\", \"HDL07\"])) & (pl.col(\"case_capacity\") < 10) & (pl.col(\"single_pick\") == 0)).then(10).otherwise(pl.col(\"case_capacity\")).alias(\"case_capacity\"))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col(\"pmg\").is_in([\"HDL55\", \"HDL35\"])) & (pl.col(\"case_capacity\") < 3) & (pl.col(\"single_pick\") == 0)).then(3).otherwise(pl.col(\"case_capacity\")).alias(\"case_capacity\"))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col(\"dep\").is_in(['SFB','SFM', \"SFP\"])) & (pl.col(\"case_capacity\") < 5)).then(pl.col('case_capacity').mean().over(['country','dep'])).otherwise(pl.col(\"case_capacity\")).alias(\"case_capacity\"))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col(\"single_pick\")) > 0).then(1).otherwise(pl.col(\"case_capacity\")).alias(\"case_capacity\"))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col(\"dep\") == \"HDL\")&(pl.col(\"pallet_capacity\").is_between(0,5))).then(pl.col('pallet_capacity').mean().over(['store','pmg'])).otherwise(pl.col(\"pallet_capacity\")).alias(\"pallet_capacity\"))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col(\"pmg\").is_in([\"HDL06\", \"HDL07\"])) & (pl.col(\"pallet_capacity\") < 100)).then(150).otherwise(pl.col(\"pallet_capacity\")).alias(\"pallet_capacity\"))\\\n", "        .with_columns(pl.when((~pl.col(\"pmg\").is_in(pro_to_del)) & (pl.col(\"dep\").is_in(['SFB', 'SFM', 'SFP']))&(pl.col(\"pallet_capacity\") < pl.col('pallet_capacity').mean().over(['store','pmg'])))\\\n", "                      .then(pl.col('pallet_capacity').mean().over(['store','pmg'])).otherwise(pl.col(\"pallet_capacity\")).alias(\"pallet_capacity\"))\n", "        \n", "        print(\"\\nModifications on Columns are done!!\")\n", "        \n", "        return result\n", "\n", "\n", "            \n", "\n", "\n", "    def optimization(Repl_Dataset):\n", "\n", "        result = Repl_Dataset.with_columns(\n", "            pl.col(\n", "                \n", "                [\n", "                \"srp\",\n", "                \"nsrp\",\n", "                \"mu\",\n", "                \"full_pallet\",\n", "                \"split_pallet\",\n", "                \"icream_nsrp\",\n", "                \"capacity\",\n", "                \"extra disassemble %\",\n", "                \"pallet_capacity\",\n", "                \"case_capacity\" ,\n", "                 \"sold_units\",\n", "                \"stock\",\n", "                \"sales_excl_vat\",\n", "                \"weight\",\n", "                \"item_price\",\n", "                \"unit\",\n", "                \"foil\",\n", "                \"cases_delivered\"\n", "\n", "            ]).cast(pl.Float32),\\\n", "            pl.col(\n", "                    [\n", "                \"checkout_stand_flag\",\n", "                \"backroom_flag\",\n", "                \"clipstrip_flag\",\n", "                \"is_capping_shelf\",\n", "                \"single_pick\",\n", "                \"SRP opening reduction opportunity\",\n", "\n", "                    ]\n", "                ).cast(pl.Int8),\\\n", "            pl.col(\n", "\n", "                [\n", "                    'pmg',\n", "                    'day', \n", "                    'country',\n", "                    'format',\n", "                    'division',\n", "                    'dep',\n", "                    'as_is_model_contains?',\n", "                    'opening_type'\n", "                ]\n", "            ).cast(pl.Categorical),\\\n", "            pl.col(\n", "                [\n", "                    'DIV_ID',\n", "                    'DEP_ID', \n", "                    'SEC_ID',\n", "                    'GRP_ID',\n", "                    'SGR_ID',\n", "\n", "                ]\n", "            ).cast(pl.Int16),\\\n", "            pl.col(\n", "                [\n", "                    'tpnb',\n", "                    'store' \n", "                ]\n", "            ).cast(pl.Int32)\n", "\n", "            )\\\n", "            .with_columns(pl.when(pl.col('shelfCapacity') > pl.col('capacity')).then(pl.col('shelfCapacity')).otherwise(pl.col('capacity')).alias('capacity'))\\\n", "                          .drop(\"shelfCapacity\")\\\n", "                              .rename({\"capacity\": \"shelfCapacity\"})\n", "                              \n", "\n", "            \n", "        \n", "        print(\"\\nOptimization on Columns are done!!\")\n", "        \n", "        return result\n", "\n", "    \n", "    def memory_usage(Repl_Dataset):\n", "        print(\"Memory Usage: \" \"{:.1f} gb\".format(Repl_Dataset.estimated_size('gb')))\n", "        return Repl_Dataset\n", "    \n", "    #Repl_Dataset = isold.pipe(all_tpn_all_day).pipe(join_all_table).pipe(mod_columns).pipe(optimization).collect().pipe(debug, \"Dataset shape: \").pipe(memory_usage)\n", "\n", "\n", "    Repl_Dataset = isold.pipe(all_tpn_all_day)\\\n", "                    .pipe(debug, \"tpns with weekdays shape: \")\\\n", "                    .pipe(join_all_table)\\\n", "                    .pipe(debug, \"Tables's combination shape\")\\\n", "                    .pipe(mod_columns)\\\n", "                    .pipe(memory_usage)\\\n", "                    .pipe(debug, \"End shape\")\\\n", "                    .pipe(optimization)\\\n", "                    .pipe(memory_usage)\n", "                    \n", "                    \n", "    # #broken_case_flag                       \n", "    # if not broken_case:\n", "        \n", "    #     Repl_Dataset = Repl_Dataset.join(\n", "    #         pl.read_csv(directory/broken_cases_f).with_columns(pl.col(['store','tpnb']).cast(pl.Int32),\n", "    #                                                            pl.col('day').cast(pl.Categorical)),\n", "    #                                      on=[\"store\", \"day\", \"tpnb\"], how=\"left\").fill_null(0)\\\n", "    #         .with_columns(pl.col('broken_case_flag').cast(pl.Int8))\n"]}, {"cell_type": "code", "execution_count": null, "id": "a2fcced4-9538-4219-8149-42ed6aea4fa1", "metadata": {}, "outputs": [], "source": ["Repl_Dataset = Repl_Dataset.filter(pl.col(\"store\") == 41520).to_pandas()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "233a2aee-45e6-48e2-9a80-027ded426082", "metadata": {}, "outputs": [], "source": ["del isold"]}, {"cell_type": "code", "execution_count": null, "id": "00eb102b-b94f-4aed-bab1-69e8904a8d18", "metadata": {}, "outputs": [], "source": ["Repl_Dataset.to_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\Calculations\\2024_04\\new_dataset_41520\", compression=\"gzip\")"]}, {"cell_type": "code", "execution_count": 1, "id": "b6a04a27-7481-445f-a358-e3f7d5aa013f", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "b = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\Calculations\\2024_04\\new_dataset_41520\")\n", "a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\as_is_modelDataSet_updated_10-01_sServiceUpdALBI_\")"]}, {"cell_type": "code", "execution_count": null, "id": "d72d952f-5865-47de-9214-88c269bea2af", "metadata": {}, "outputs": [], "source": ["a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\as_is_modelDataSet_updated_10-01_sServiceUpdALBI_\")"]}, {"cell_type": "code", "execution_count": 2, "id": "3c9de71d-f8a4-4fef-b315-749df06af4e1", "metadata": {}, "outputs": [], "source": ["a = a[a.store == 41520]\n"]}, {"cell_type": "code", "execution_count": null, "id": "c8c6891f-fc2c-4955-a434-6be8a6d90e7d", "metadata": {}, "outputs": [], "source": ["a.columns"]}, {"cell_type": "code", "execution_count": 3, "id": "915bff3f-1fab-4ac7-945e-b584a69df131", "metadata": {}, "outputs": [{"data": {"text/plain": ["(184618, 55)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["a.shape"]}, {"cell_type": "code", "execution_count": 4, "id": "172e73e7-2067-42e5-a239-4e3704426e33", "metadata": {}, "outputs": [{"data": {"text/plain": ["(184618, 49)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["b.shape"]}, {"cell_type": "code", "execution_count": null, "id": "8c2f3ca0-615f-4447-8169-9fcf3a3befc9", "metadata": {}, "outputs": [], "source": ["Repl_Dataset.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "92bdf80f-c0e3-4712-aaff-ff1a2cf9c57e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1099, 55)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["a[a.dep.isin(['SFP', 'SFM'])].shape"]}, {"cell_type": "code", "execution_count": 6, "id": "3734ea34-c84e-433c-9b9f-07ed4784d052", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1099, 49)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["b[b.dep.isin(['SFP', 'SFM'])].shape"]}, {"cell_type": "code", "execution_count": 7, "id": "c7fd1f87-f9c7-4bc3-8f04-fae8cacfcf40", "metadata": {}, "outputs": [{"data": {"text/plain": ["7280.2695"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["b[b.dep.isin(['SFP', 'SFM'])]['case_capacity'].sum()"]}, {"cell_type": "code", "execution_count": 8, "id": "32a5d914-a872-4db4-83c8-9b770e7e2666", "metadata": {}, "outputs": [{"data": {"text/plain": ["7100.3508822430595"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["a[a.dep.isin(['SFP', 'SFM'])]['case_capacity'].sum()"]}, {"cell_type": "code", "execution_count": 13, "id": "565c9929-b147-4cd9-9872-a64a8773915e", "metadata": {}, "outputs": [{"data": {"text/plain": ["38997.0"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["a[a.dep.isin(['PRO'])]['case_capacity'].sum()"]}, {"cell_type": "code", "execution_count": 17, "id": "2c49a332-c6bf-4fda-b28a-d95fa88cee5d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>store</th>\n", "      <th>pmg</th>\n", "      <th>tpnb</th>\n", "      <th>day</th>\n", "      <th>sold_units</th>\n", "      <th>sales_excl_vat</th>\n", "      <th>country</th>\n", "      <th>tpn</th>\n", "      <th>ownbrand</th>\n", "      <th>unit_type</th>\n", "      <th>...</th>\n", "      <th>opening_type</th>\n", "      <th>pallet_capacity</th>\n", "      <th>foil</th>\n", "      <th>SRP opening reduction opportunity</th>\n", "      <th>extra disassemble %</th>\n", "      <th>single_pick</th>\n", "      <th>shelfCapacity</th>\n", "      <th>case_capacity</th>\n", "      <th>cases_delivered</th>\n", "      <th>as_is_model_contains?</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "<p>0 rows × 49 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [store, pmg, tpnb, day, sold_units, sales_excl_vat, country, tpn, ownbrand, unit_type, weight, division_hier, DIV_ID, department, DEP_ID, section, SEC_ID, group, GRP_ID, subgroup, SGR_ID, product_name, format, division, dep, is_capping_shelf, sold_units_dotcom, stock, item_price, srp, nsrp, mu, full_pallet, split_pallet, icream_nsrp, checkout_stand_flag, clipstrip_flag, backroom_flag, unit, opening_type, pallet_capacity, foil, SRP opening reduction opportunity, extra disassemble %, single_pick, shelfCapacity, case_capacity, cases_delivered, as_is_model_contains?]\n", "Index: []\n", "\n", "[0 rows x 49 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["b[b['pallet_capacity'] == 0].head()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}