# -*- coding: utf-8 -*-
"""
Created on Mon Nov 14 13:32:03 2022

@author: phrubos
"""
import pyarrow.parquet as pq
import pandas as pd
from pathlib import Path
import numpy as np

repl_types = ["srp", "nsrp", "full_pallet", "mu"]
new_repl_type = "srp"

Repl_Dataset = pq.read_table(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\inputs\Repl_Dataset_2022_new_14w_27w_promo_flag.parquet"
).to_pandas()


############# SRP ##############
tpns = pd.DataFrame()

for x in ["HU", "SK", "CZ"]:
    tpns = pd.concat(
        [
            tpns,
            pd.read_excel(
                r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\others\22_oct-nov\Productivity model vs SRD database.xlsx",
                sheet_name=x,
                usecols=["country", "tpnb"],
            ),
        ]
    )

dict_list = tpns.groupby("country")["tpnb"].apply(lambda s: s.tolist()).to_dict()

for repl_type in repl_types:
    for k, v in dict_list.items():
        Repl_Dataset.loc[
            (Repl_Dataset.country == k)
            & (Repl_Dataset.tpnb.isin(v))
            & (Repl_Dataset.full_pallet == 0)
            & (Repl_Dataset.mu == 0),
            repl_type,
        ] = (1 if repl_type == new_repl_type else 0)


########## full pallet ###############

new_repl_type = "full_pallet"

cz = pd.read_excel(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\others\22_oct-nov\pallet_DIV2_CZ  BEER.xlsx",
    sheet_name="pallet_beer",
)
cz["store"] = cz["store"].apply(lambda x: str(1) + str(x)).astype("int")
cz = cz[["store", "tpnb"]].drop_duplicates()


sk = pd.read_excel(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\others\22_oct-nov\Pallet report SK Beer.xlsx"
)
sk["store"] = sk["store"].apply(lambda x: str(1) + str(x)).astype("int")
sk = sk[["store", "tpnb"]].drop_duplicates()


tpns = pd.concat([cz, sk])

dict_list = tpns.groupby("store")["tpnb"].apply(lambda s: s.tolist()).to_dict()

for repl_type in repl_types:
    for k, v in dict_list.items():
        Repl_Dataset.loc[
            (Repl_Dataset.store == k) & (Repl_Dataset.tpnb.isin(v)), repl_type
        ] = (1 if repl_type == new_repl_type else 0)

Repl_Dataset.to_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\inputs\Repl_Dataset_2022_new_14w_27w_promo_flag_full_pallet_melinda.parquet",
    compression="gzip",
)
