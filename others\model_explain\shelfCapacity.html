<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shelf Capacity Data Structure</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 10px;
            letter-spacing: -1px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            position: relative;
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .emoji {
            font-size: 2rem;
        }
        
        .data-preview {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 0.85rem;
            min-width: 800px;
        }
        
        .data-table th {
            background: #4a5568;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 500;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            white-space: nowrap;
        }
        
        .data-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #e2e8f0;
            color: #4a5568;
            white-space: nowrap;
        }
        
        .data-table tr:hover {
            background: #edf2f7;
        }
        
        .shelf-capacity-column {
            background: #fef5e7 !important;
            font-weight: 600;
            color: #d69e2e !important;
        }
        
        .replen-column {
            background: #f3e8ff !important;
            font-weight: 600;
            color: #7c3aed !important;
        }
        
        .explanations-container {
            display: flex;
            gap: 30px;
            margin-top: 30px;
            align-items: flex-start;
        }
        
        .capacity-explanation {
            flex: 1;
            background: linear-gradient(135deg, #fef5e7, #fed7aa);
            padding: 20px;
            border-radius: 12px;
            border: 3px solid #f6ad55;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .capacity-explanation h4 {
            color: #c05621;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 1.1rem;
            text-align: center;
        }
        
        .capacity-explanation p {
            color: #9c4221;
            font-size: 0.95rem;
            line-height: 1.4;
            margin: 0;
            text-align: center;
        }
        
        .replen-explanation {
            flex: 1;
            background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
            padding: 20px;
            border-radius: 12px;
            border: 3px solid #9f7aea;
        }
        
        .replen-explanation h4 {
            color: #6b46c1;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            justify-content: center;
        }
        
        .replen-explanation p {
            color: #553c9a;
            font-size: 1rem;
            line-height: 1.5;
        }
        
        .column-highlight {
            position: relative;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .card {
                padding: 25px;
            }
            
            .data-table {
                font-size: 0.75rem;
            }
            
            .explanations-container {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Shelf Capacity Data Structure</h1>
            <p>How productivity models use planogram data for shelf capacity calculations</p>
        </div>
        
        <div class="card">
            <div class="section-title">
                <span class="emoji">📋</span>
                Input Data Structure
            </div>
            
            <p style="margin-bottom: 20px; color: #718096; font-size: 1.1rem;">
                Your raw planogram data with replenishment intelligence:
            </p>
            
            <div class="data-preview" style="position: relative;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Country</th>
                            <th>Store_Number</th>
                            <th>Product_id</th>
                            <th>Planogram_on_Store</th>
                            <th>Position_Capacity</th>
                            <th style="background: #d69e2e;">Position_Capacity_X_Planogram_on_Store</th>
                            <th style="background: #9f7aea;">merchstyle_string</th>
                            <th style="background: #9f7aea;">Pallet_info</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>CZ</td>
                            <td>11015</td>
                            <td>100358232</td>
                            <td>1</td>
                            <td>12</td>
                            <td class="shelf-capacity-column">12</td>
                            <td class="replen-column">Unit</td>
                            <td class="replen-column">None</td>
                        </tr>
                        <tr>
                            <td>CZ</td>
                            <td>11007</td>
                            <td>100393023</td>
                            <td>1</td>
                            <td>21</td>
                            <td class="shelf-capacity-column">21</td>
                            <td class="replen-column">Unit</td>
                            <td class="replen-column">None</td>
                        </tr>
                        <tr>
                            <td>CZ</td>
                            <td>11009</td>
                            <td>205320412</td>
                            <td>2</td>
                            <td>30</td>
                            <td class="shelf-capacity-column">60</td>
                            <td class="replen-column">Tray</td>
                            <td class="replen-column">None</td>
                        </tr>
                        <tr>
                            <td>CZ</td>
                            <td>11021</td>
                            <td>220168284</td>
                            <td>2</td>
                            <td>40</td>
                            <td class="shelf-capacity-column">80</td>
                            <td class="replen-column">Tray</td>
                            <td class="replen-column">None</td>
                        </tr>
                        <tr>
                            <td>CZ</td>
                            <td>11009</td>
                            <td>100545932</td>
                            <td>1</td>
                            <td>4</td>
                            <td class="shelf-capacity-column">4</td>
                            <td class="replen-column">Unit</td>
                            <td class="replen-column">None</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="explanations-container">
                <div class="capacity-explanation">
                    <h4>📊 ShelfCapacity for Models</h4>
                    <p>Productivity models use this aggregated value as <strong>"shelfCapacity"</strong></p>
                </div>
                
                <div class="replen-explanation">
                    <h4>🚚 Replenishment Intelligence</h4>
                    <p>
                        <strong>merchstyle_string:</strong> Unit = Non shelf-ready packaging, Tray = shelf-ready packaging<br>
                        <strong>Pallet_info:</strong> Split_Pallet, Half_Pallet, Pallet = bulk delivery methods
                        <br><br>
                        These columns tell productivity models <strong>how products should be replenished</strong>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>