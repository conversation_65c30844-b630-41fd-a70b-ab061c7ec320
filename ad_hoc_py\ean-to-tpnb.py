# -*- coding: utf-8 -*-
"""
Created on Wed Apr 19 12:57:36 2023

@author: phrubos
"""

import pandas as pd
from pathlib import Path
import pyodbc

conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()

# EAN = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_04\Bottle_EAN.xlsx")

# ean = tuple(EAN.ean.tolist())

ean = tuple([5000112652291])

sql = """ SELECT cntr_code AS country, slad_tpnb AS tpnb, slad_tpn AS tpn, slem_ean as ean,
                dmat_div_des_en AS division,
                 dmat_dep_des_en AS department,
                 dmat_sec_des_en AS section,
                 dmat_grp_des_en AS group,
                 dmat_sgr_des_en AS subgroup,
                 slad_long_des as product_name
                        FROM DM.dim_artgld_details mstr
                        WHERE slem_ean = 5000112652291
                        AND cntr_code = 'SK' 
                        AND dmat_sgr_des_en <> "Do not use"
                        GROUP BY cntr_code, slem_ean,  slad_tpnb, slad_tpn,
                        dmat_div_des_en,
                        dmat_dep_des_en,
                        dmat_sec_des_en,
                        dmat_grp_des_en,
                        dmat_sgr_des_en,
                        slad_long_des
                        
        """.format(ean = ean)
        
        
art_gold = pd.read_sql(sql, conn)