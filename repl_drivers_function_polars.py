import polars as pl
import numpy as np
from typing import Tuple
import time
import pandas as pd

weekdays_to_divide = 7
base_period_week_numbers = 14

def Repl_Drivers_Calculation_polars(
    directory,
    Repl_Dataset,
    store_inputs,
    backstock_target,
    RC_Capacity_Ratio,
    shelf_trolley_cap_ratio_to_pallet,
    shelf_trolley_cap_ratio_to_rollcage,
    excel_inputs_f,
    MODULE_CRATES,
    TABLE_CRATES,
    FULFILL_TARGET,
    SALES_CYCLE,
    RC_CAPACITY,
    RC_DELIVERY,
    RC_VS_PAL_CAPACITY,
    only_tpn,
    tpnb_store,
    tpnb_country,
    selected_tpn,
    capping_shelves_ratio,
    stores,
    version,
    shelfService_gm,
    cases_to_replenish_only,
):
    # Convert input DataFrame to Polars if it's pandas
    if isinstance(Repl_Dataset, pd.DataFrame):
        Drivers = pl.from_pandas(Repl_Dataset)
    else:
        Drivers = Repl_Dataset.clone()

    # Initial filtering and transformations
    Drivers = (Drivers
        .filter(pl.col("store").is_in(stores))
        .with_columns([
            # MU customization
            pl.when(pl.col("mu") > 0)
            .then(pl.col("pallet_capacity") / 2)
            .otherwise(pl.col("pallet_capacity"))
            .alias("pallet_capacity"),
            
            # Foil transformation
            pl.when(pl.col("foil") == 0)
            .then(pl.lit(1))
            .otherwise(pl.col("foil"))
            .alias("foil")
        ])
        .drop(["ownbrand", "checkout_stand_flag"])
    )

    # Extract Produce data
    Drivers_produce = (Drivers
        .filter(
            (pl.col("dep") == "PRO") & 
            (pl.col("pmg") != "PRO16") & 
            (pl.col("pmg") != "PRO19")
        )
    )
    
    pro_to_del = Drivers_produce.select("pmg").unique()
    
    # Filter main dataset
    Drivers = (Drivers
        .filter(
            ~pl.col("pmg").is_in(pro_to_del["pmg"]) & 
            (pl.col("pmg") != "HDL01")
        )
    )

    if Drivers.height > 0:
        weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        # Sort and process shelf capacity
        Drivers = (Drivers
            .with_columns([
                pl.col("day").cast(pl.Categorical).cat.set_ordering(weekday_order)
            ])
            .sort(["store", "pmg", "tpnb", "day"])
            .with_columns([
                pl.col("shelfCapacity")
                .mean()
                .over(["store", "pmg"])
                .alias("capacity_avg_pmg"),
                
                # Handle capacity_avg_pmg
                pl.when(pl.col("capacity_avg_pmg") < 5)
                .then(pl.lit(8))
                .otherwise(pl.col("capacity_avg_pmg"))
                .alias("capacity_avg_pmg"),
                
                # Replace NaN with 0
                pl.all().fill_null(0)
            ])
            .with_columns([
                # Handle shelf capacity
                pl.when((pl.col("shelfCapacity") == 0) | (pl.col("shelfCapacity") == 1))
                .then(pl.col("capacity_avg_pmg"))
                .otherwise(pl.col("shelfCapacity"))
                .alias("shelfCapacity"),
                
                # Handle stock calculation
                pl.when((pl.col("stock") == 0) & (pl.col("sold_units") > 0))
                .then(pl.col("sold_units"))
                .otherwise(pl.col("stock"))
                .alias("stock")
            ])
            .with_columns([
                # Weight customization
                (pl.col("weight") * pl.col("case_capacity")).alias("heavy"),
                (pl.col("weight") * pl.col("case_capacity")).alias("weight_selector"),
                
                # Heavy/Light flags
                pl.when(pl.col("weight_selector") >= 5)
                .then(pl.lit(1))
                .otherwise(pl.lit(0))
                .cast(pl.Int8)
                .alias("heavy"),
                
                pl.when(pl.col("weight_selector") < 5)
                .then(pl.lit(1))
                .otherwise(pl.lit(0))
                .cast(pl.Int8)
                .alias("light")
            ])
            .drop("weight_selector")
            .with_columns([
                # Broken Items case cap
                pl.when(pl.col("broken_case_flag") == 1)
                .then(pl.col("case_capacity"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("Broken Items"),
                
                # Cases delivered on sf
                pl.when(pl.col("broken_case_flag") == 1)
                .then(pl.col("cases_delivered") - 1)
                .otherwise(pl.col("cases_delivered"))
                .cast(pl.Float32)
                .alias("cases_delivered_on_sf")
            ])
            .with_columns([
                pl.when(pl.col("cases_delivered_on_sf") < 0)
                .then(pl.lit(0))
                .otherwise(pl.col("cases_delivered_on_sf"))
                .alias("cases_delivered_on_sf"),
                
                # Setup icream_nsrp
                pl.when(pl.col("icream_nsrp") > 0)
                .then(pl.col("icream_nsrp") + pl.col("nsrp"))
                .otherwise(pl.col("nsrp"))
                .alias("nsrp")
            ])
        )

        # Handle broken case flag adjustments
        cols_to_adjust = ['srp', 'split_pallet', 'full_pallet', 'mu', 'icream_nsrp', 'single_pick']
        for col in cols_to_adjust:
            Drivers = Drivers.with_columns([
                pl.when((pl.col(col) > 0) & (pl.col("broken_case_flag") == 1))
                .then(pl.lit(0))
                .otherwise(pl.col(col))
                .alias(col)
            ])

        Drivers = Drivers.with_columns([
            pl.when(pl.col("broken_case_flag") == 1)
            .then(pl.lit(1))
            .otherwise(pl.col("nsrp"))
            .alias("nsrp")
        ])

        # Single pick customization
        cols_to_zero = ['nsrp', 'srp', 'split_pallet', 'full_pallet', 'mu', 'icream_nsrp']
        for col in cols_to_zero:
            Drivers = Drivers.with_columns([
                pl.when(pl.col("single_pick") > 0)
                .then(pl.lit(0))
                .otherwise(pl.col(col))
                .alias(col)
            ])

        # Backroom calculations
        Drivers = (Drivers
            .with_columns([
                pl.when(pl.col("backroom_flag") == 1)
                .then(pl.col("sold_units_dotcom") / pl.col("case_capacity"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("backroom_cases_dotcom")
            ])
            .with_columns([
                pl.col("backroom_cases_dotcom")
                .fill_null(0)
                .replace({float('inf'): 0, float('-inf'): 0})
                .alias("backroom_cases_dotcom")
            ])
            .with_columns([
                pl.when(pl.col("backroom_cases_dotcom") > 0)
                .then(pl.col("backroom_cases_dotcom") / pl.col("pallet_capacity"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("backroom_pallets")
            ])
            .with_columns([
                pl.col("backroom_pallets")
                .fill_null(0)
                .replace({float('inf'): 0, float('-inf'): 0})
                .alias("backroom_pallets")
            ])
            .with_columns([
                pl.when(pl.col("backroom_cases_dotcom") > 0)
                .then(pl.col("cases_delivered_on_sf") - pl.col("backroom_cases_dotcom"))
                .otherwise(pl.col("cases_delivered_on_sf"))
                .cast(pl.Float32)
                .alias("cases_delivered_on_sf")
            ])
            .with_columns([
                pl.when(pl.col("cases_delivered_on_sf") < 0)
                .then(pl.lit(0))
                .otherwise(pl.col("cases_delivered_on_sf"))
                .alias("cases_delivered_on_sf")
            ])
        )

        # Secondary SRP calculations
        Drivers = (Drivers
            .with_columns([
                pl.when(pl.col("sold_units") > pl.col("shelfCapacity"))
                .then(pl.col("stock") - (pl.col("shelfCapacity") / (1 - backstock_target)))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("secondary_srp")
            ])
            .with_columns([
                pl.col("secondary_srp")
                .fill_null(0)
                .replace({float('inf'): 0, float('-inf'): 0})
                .alias("secondary_srp")
            ])
            .with_columns([
                pl.when((1 - (pl.col("shelfCapacity") / pl.col("stock"))) > 0.4)
                .then(pl.col("secondary_srp"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("secondary_srp")
            ])
            .with_columns([
                pl.when(pl.col("stock") < pl.col("shelfCapacity"))
                .then(pl.lit(0))
                .otherwise(pl.col("secondary_srp"))
                .alias("secondary_srp")
            ])
            .with_columns([
                pl.when(
                    (pl.col("srp") > 0) | 
                    (pl.col("full_pallet") > 0) | 
                    (pl.col("mu") > 0) | 
                    (pl.col("split_pallet") > 0) | 
                    (pl.col("icream_nsrp") > 0)
                )
                .then(pl.col("secondary_srp") / weekdays_to_divide)
                .otherwise(pl.lit(0))
                .alias("secondary_srp")
            ])
        )

        # Secondary NSRP calculations
        Drivers = (Drivers
            .with_columns([
                pl.when(pl.col("sold_units") > pl.col("shelfCapacity"))
                .then(pl.col("stock") - (pl.col("shelfCapacity") / (1 - backstock_target)))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("secondary_nsrp")
            ])
            .with_columns([
                pl.col("secondary_nsrp")
                .fill_null(0)
                .replace({float('inf'): 0, float('-inf'): 0})
                .alias("secondary_nsrp")
            ])
            .with_columns([
                pl.when((1 - (pl.col("shelfCapacity") / pl.col("stock"))) > 0.4)
                .then(pl.col("secondary_nsrp"))
                .otherwise(pl.lit(0))
                .alias("secondary_nsrp")
            ])
            .with_columns([
                pl.when(pl.col("stock") < pl.col("shelfCapacity"))
                .then(pl.lit(0))
                .otherwise(pl.col("secondary_nsrp"))
                .alias("secondary_nsrp")
            ])
            .with_columns([
                pl.when(
                    (pl.col("srp") == 0) & 
                    (pl.col("full_pallet") == 0) & 
                    (pl.col("mu") == 0) & 
                    (pl.col("split_pallet") == 0) & 
                    (pl.col("icream_nsrp") == 0)
                )
                .then(pl.col("secondary_nsrp") / weekdays_to_divide)
                .otherwise(pl.lit(0))
                .alias("secondary_nsrp")
            ])
        )

        # Shop floor capacity calculation
        Drivers = Drivers.with_columns([
            (pl.col("shelfCapacity") + pl.col("secondary_nsrp") + pl.col("secondary_srp"))
            .cast(pl.Float32)
            .alias("shop_floor_capacity")
        ])

        # Stock morning and cases to replenish calculations
        Drivers = (Drivers
            .with_columns([
                (pl.col("stock") - pl.col("unit") + pl.col("sold_units") + pl.col("sold_units_dotcom"))
                .fill_null(0)
                .replace({float('inf'): 0, float('-inf'): 0})
                .alias("stock_morning")
            ])
            .with_columns([
                (
                    (pl.col("sold_units") +
                    pl.min_horizontal([pl.col("stock"), pl.col("shop_floor_capacity")]) -
                    pl.min_horizontal([pl.col("stock_morning"), pl.col("shop_floor_capacity")]))
                    / pl.col("case_capacity")
                ).alias("cases_to_replenish")
            ])
            .with_columns([
                pl.when(pl.col("cases_to_replenish") < 0)
                .then(pl.lit(0))
                .otherwise(pl.col("cases_to_replenish"))
                .alias("cases_to_replenish")
            ])
        )

        # ClipStrip Flag processing
        Drivers = (Drivers
            .with_columns([
                pl.when(
                    pl.col("product_name").str.contains("HELL") | 
                    pl.col("product_name").str.contains("XIXO")
                )
                .then(pl.lit(0))
                .otherwise(pl.col("clipstrip_flag"))
                .alias("clipstrip_flag"),

                # Clip Strip Items and Cases
                pl.when(pl.col("clipstrip_flag") == 1)
                .then(pl.col("cases_to_replenish") * pl.col("case_capacity"))
                .otherwise(pl.lit(0))
                .alias("Clip Strip Items")
            ])
            .with_columns([
                (pl.col("Clip Strip Items") / pl.col("case_capacity"))
                .cast(pl.Float32)
                .fill_null(0)
                .replace({float('inf'): 0, float('-inf'): 0})
                .alias("Clip Strip Cases"),

                # Update cases_to_replenish
                pl.when(pl.col("clipstrip_flag") == 1)
                .then(pl.lit(0))
                .otherwise(pl.col("cases_to_replenish"))
                .alias("cases_to_replenish")
            ])
        )

        if cases_to_replenish_only:
            # Group by calculations for cases to replenish
            cases_to_replenish_tpn = (Drivers
                .groupby(['country', 'store', 'division', 'dep', 'pmg', 'tpnb', 'product_name'])
                .agg(pl.col('cases_to_replenish').sum())
            )
            
            cases_to_replenish_dep = (Drivers
                .groupby(['country', 'store', 'division', 'dep'])
                .agg(pl.col('cases_to_replenish').sum())
            )
            
        else:
            # Touch calculations
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("stock") > pl.col("shop_floor_capacity"))
                    .then(pl.col("shop_floor_capacity"))
                    .otherwise(pl.col("stock"))
                    .cast(pl.Float32)
                    .alias("o_touch"),

                    pl.when(
                        (pl.col("is_capping_shelf") == 1) &
                        (pl.col("stock") - (capping_shelves_ratio * pl.col("shop_floor_capacity")) > pl.col("shop_floor_capacity"))
                    )
                    .then(capping_shelves_ratio * pl.col("shop_floor_capacity"))
                    .otherwise(pl.lit(0))
                    .alias("c_touch")
                ])
                .with_columns([
                    pl.when(pl.col("stock") > pl.col("shop_floor_capacity"))
                    .then(pl.col("stock") - pl.col("c_touch") - pl.col("shop_floor_capacity"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("t_touch")
                ])
                .with_columns([
                    (pl.col("c_touch") / pl.col("case_capacity") / weekdays_to_divide)
                    .cast(pl.Float32)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("Capping Shelf Cases")
                ])
                .with_columns([
                    pl.when(pl.col("full_pallet") > 0 | pl.col("mu") > 0)
                    .then(pl.lit(0))
                    .otherwise(pl.col("Capping Shelf Cases"))
                    .alias("Capping Shelf Cases")
                ])
            )

            # Cases to replenish excluding capping cases
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases") > 0)
                    .then(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases"))
                    .otherwise(pl.col("cases_to_replenish"))
                    .alias("cases_to_repl_excl_cap_cases"),

                    pl.when(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases") > 0)
                    .then(pl.col("Capping Shelf Cases"))
                    .otherwise(pl.lit(0))
                    .alias("Capping Shelf Cases")
                ])
            )

            # Store inputs processing
            stores_df = (pl.from_pandas(store_inputs)
                .select(["Store", "Format", "Plan Size"])
                .unique()
                .with_columns([
                    pl.col("Store").alias("store"),
                    pl.col("Format").alias("format"),
                    pl.col("Plan Size").alias("plan size")
                ])
            )

            dep_df = (pl.from_pandas(store_inputs)
                .select([
                    "Store", "Dep", "Racking", "Pallets Delivery Ratio",
                    "Backstock Pallet Ratio", "says", "pbl_pbs_25_perc_pallet"
                ])
                .unique()
                .with_columns([col.alias(col.name.lower()) for col in pl.all()])
            )

            pmg_df = (pl.from_pandas(store_inputs)
                .select(["Country", "Format", "Pmg", "presortPerc", "prack"])
                .unique()
                .with_columns([col.alias(col.name.lower()) for col in pl.all()])
            )

            # Format standardization and merging
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("format") == "1k")
                    .then(pl.lit("1K"))
                    .otherwise(pl.col("format"))
                    .alias("format")
                ])
                .join(stores_df, on=["store", "format"], how="inner")
                .join(pmg_df, on=["country", "format", "pmg"], how="left")
                .join(dep_df, on=["store", "dep"], how="left")
                .with_columns([
                    pl.when(pl.col("racking") == 1)
                    .then(pl.col("prack"))
                    .otherwise(pl.lit(0))
                    .alias("prack")
                ])
            )

            # Replenished Rollcages calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("New Delivery - Rollcages") +
                    (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
                    pl.col("pallets delivery ratio")) * RC_Capacity_Ratio)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .cast(pl.Float32)
                    .alias("Replenished Rollcages")
                ])
            )

            # Pre-sort calculations for rollcages
            try:
                Drivers = (Drivers
                    .with_columns([
                        ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
                        pl.col("pallets delivery ratio")) * RC_Capacity_Ratio)
                        .alias("pre_sort_rc"),
                        
                        ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
                        pl.col("pallets delivery ratio")) * RC_Capacity_Ratio * pl.col("PBS_CAGE_%"))
                        .alias("pre_sort_rc_pbs"),
                        
                        ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
                        pl.col("pallets delivery ratio")) * RC_Capacity_Ratio * pl.col("PBL_CAGE_%"))
                        .alias("pre_sort_rc_pbl"),
                        
                        (pl.col("PBS_CAGE") + pl.col("pre_sort_rc_pbs"))
                        .alias("Replenished Rollcages PBS"),
                        
                        (pl.col("PBL_CAGE") + pl.col("pre_sort_rc_pbl"))
                        .alias("Replenished Rollcages PBL")
                    ])
                )
            except:
                pass

            # Replenished Pallets calculations
            Drivers = (Drivers
                .with_columns([
                    pl.when(
                        pl.col("New Delivery - Pallets") - 
                        (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
                        pl.col("pallets delivery ratio")) <= 0
                    )
                    .then(pl.lit(0))
                    .otherwise(
                        pl.col("New Delivery - Pallets") - 
                        (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
                        pl.col("pallets delivery ratio"))
                    )
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .cast(pl.Float32)
                    .alias("Replenished Pallets")
                ])
            )

            # Pre-sort calculations for pallets
            try:
                Drivers = (Drivers
                    .with_columns([
                        ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
                        pl.col("pallets delivery ratio")))
                        .alias("pre_sort_pal"),
                        
                        ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
                        pl.col("pallets delivery ratio")) * pl.col("PBS_PALLET_%"))
                        .alias("pre_sort_pal_pbs"),
                        
                        ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") * 
                        pl.col("pallets delivery ratio")) * pl.col("PBL_PALLET_%"))
                        .alias("pre_sort_pal_pbl"),
                        
                        pl.when(pl.col("PBS_PALLET") - pl.col("pre_sort_pal_pbs") <= 0)
                        .then(pl.lit(0))
                        .otherwise(pl.col("PBS_PALLET") - pl.col("pre_sort_pal_pbs"))
                        .alias("Replenished Pallets PBS"),
                        
                        pl.when(pl.col("PBL_PALLET") - pl.col("pre_sort_pal_pbl") <= 0)
                        .then(pl.lit(0))
                        .otherwise(pl.col("PBL_PALLET") - pl.col("pre_sort_pal_pbl"))
                        .alias("Replenished Pallets PBL")
                    ])
                )
            except:
                pass

            # Replenished Shelf Trolley calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("Replenished Rollcages") * shelf_trolley_cap_ratio_to_rollcage +
                    pl.col("Replenished Pallets") * shelf_trolley_cap_ratio_to_pallet)
                    .cast(pl.Float32)
                    .alias("Replenished Shelf Trolley")
                ])
            )

            # Update replenished values based on full_pallet and mu conditions
            replenished_cols = ["Replenished Rollcages", "Replenished Pallets", 
                              "Replenished Pallets PBL", "Replenished Pallets PBS",
                              "Replenished Rollcages PBL", "Replenished Rollcages PBS"]
            
            for col in replenished_cols:
                try:
                    Drivers = Drivers.with_columns([
                        pl.when(pl.col("full_pallet") > 0 | pl.col("mu") > 0)
                        .then(pl.lit(0))
                        .otherwise(pl.col(col))
                        .alias(col)
                    ])
                except:
                    pass

            # Unit for tagging calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("cases_to_repl_excl_cap_cases") + pl.col("Capping Shelf Cases")) *
                    pl.col("case_capacity")
                    .alias("Unit_for_tagging")
                ])
                .with_columns([
                    pl.when(pl.col("Unit_for_tagging") < 0)
                    .then(pl.lit(0))
                    .otherwise(pl.col("Unit_for_tagging"))
                    .alias("Unit_for_tagging")
                ])
            )

            # UK BackStock Logic
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("stock") > pl.col("case_capacity"))
                    .then(pl.col("stock") % pl.col("case_capacity"))
                    .otherwise(pl.lit(0))
                    .alias("looseSingles"),
                ])
                .with_columns([
                    (pl.col("stock") - pl.col("looseSingles"))
                    .alias("inCaseSingles"),
                    
                    pl.when((pl.col("shelfCapacity") > pl.col("looseSingles")) & 
                           (pl.col("stock") > pl.col("shelfCapacity")))
                    .then(pl.col("shelfCapacity") - pl.col("looseSingles"))
                    .otherwise(pl.col("shelfCapacity") - pl.col("stock") - pl.col("looseSingles"))
                    .alias("spaceOnShelfSingles")
                ])
                .with_columns([
                    (pl.col("spaceOnShelfSingles") - 
                    (pl.col("spaceOnShelfSingles") % pl.col("case_capacity")))
                    .alias("caseUnitSpaceOnShelf")
                ])
                .with_columns([
                    pl.when((pl.col("caseUnitSpaceOnShelf") >= pl.col("inCaseSingles")) & 
                           (pl.col("stock") > pl.col("caseUnitSpaceOnShelf")))
                    .then(pl.col("caseUnitSpaceOnShelf"))
                    .otherwise(pl.col("stock") - pl.col("looseSingles"))
                    .alias("fullCaseSingleShelf")
                ])
                .with_columns([
                    pl.when(pl.col("fullCaseSingleShelf") + pl.col("looseSingles") > pl.col("shelfCapacity"))
                    .then(pl.col("shelfCapacity"))
                    .otherwise(pl.col("fullCaseSingleShelf") + pl.col("looseSingles"))
                    .alias("totalSinglesShelf")
                ])
                .with_columns([
                    pl.when(pl.col("stock") > pl.col("totalSinglesShelf"))
                    .then(pl.col("stock") - pl.col("totalSinglesShelf"))
                    .otherwise(pl.lit(0))
                    .alias("totalSinglesBackstock"),
                    
                    (pl.col("totalSinglesShelf") / pl.col("case_capacity"))
                    .alias("totalCasesShelf"),
                    
                    (pl.col("totalSinglesBackstock") / pl.col("case_capacity") / weekdays_to_divide)
                    .alias("Backstock Cases"),
                    
                    (pl.col("totalSinglesBackstock") / weekdays_to_divide)
                    .alias("Backstock unit")
                ])
            )


    # =============================================================================
    # post-sort logic       
    # =============================================================================
            
            # Post-sort logic calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("t_touch") / pl.col("stock"))
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("post_sort_ratio")
                ])
                .with_columns([
                    pl.when(pl.col("division") == "GM")
                    .then(pl.col("cases_to_replenish") * pl.col("post_sort_ratio"))
                    .otherwise(pl.col("Backstock Cases"))
                    .alias("Backstock Cases")
                ])
                .with_columns([
                    pl.when(pl.col("Backstock Cases") - pl.col("Capping Shelf Cases") > 0)
                    .then(pl.col("Backstock Cases") - pl.col("Capping Shelf Cases"))
                    .otherwise(pl.lit(0))
                    .alias("Backstock Cases"),

                    (pl.col("cases_to_replenish") * pl.col("post_sort_ratio"))
                    .alias("Post-sort Cases")
                ])
                .with_columns([
                    (pl.col("t_touch") / pl.col("case_capacity"))
                    .cast(pl.Float32)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("Two Touch Cases")
                ])
                .with_columns([
                    (pl.col("Two Touch Cases") / weekdays_to_divide)
                    .alias("Two Touch Cases"),

                    (pl.col("t_touch") / weekdays_to_divide)
                    .alias("Two Touch unit")
                ])
            )

            # Post-sort Pallets calculations
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("full_pallet") > 0 | pl.col("mu") > 0)
                    .then(pl.col("Post-sort Cases") / pl.col("pallet_capacity"))
                    .otherwise(pl.col("Post-sort Cases") / pl.col("pallet_capacity") * pl.col("backstock pallet ratio"))
                    .cast(pl.Float32)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("Post-sort Pallets"),

                    pl.when(pl.col("full_pallet") > 0 | pl.col("mu") > 0)
                    .then(pl.lit(0))
                    .otherwise(pl.col("Post-sort Cases"))
                    .alias("Post-sort Cases")
                ])
                .with_columns([
                    pl.when(pl.col("light") == 1)
                    .then(pl.col("Post-sort Cases"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("L_Post-sort Cases"),

                    pl.when(pl.col("heavy") == 1)
                    .then(pl.col("Post-sort Cases"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("H_Post-sort Cases")
                ])
            )

            # Post-sort Rollcages calculations
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("full_pallet") > 0 | pl.col("mu") > 0)
                    .then(pl.lit(0))
                    .otherwise(
                        pl.col("Post-sort Cases") / pl.col("pallet_capacity") * 
                        (1 - pl.col("backstock pallet ratio")) * RC_Capacity_Ratio
                    )
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .cast(pl.Float32)
                    .alias("Post-sort Rollcages")
                ])
            )

            # Backstock calculations
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("full_pallet") > 0 | pl.col("mu") > 0)
                    .then(pl.col("Backstock Cases") / pl.col("pallet_capacity"))
                    .otherwise(
                        pl.col("Backstock Cases") / pl.col("pallet_capacity") * 
                        pl.col("backstock pallet ratio")
                    )
                    .cast(pl.Float32)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("Backstock Pallets"),

                    pl.when(pl.col("full_pallet") > 0 | pl.col("mu") > 0)
                    .then(pl.lit(0))
                    .otherwise(pl.col("Backstock Cases"))
                    .alias("Backstock Cases"),

                    pl.when(pl.col("full_pallet") > 0 | pl.col("mu") > 0)
                    .then(pl.lit(0))
                    .otherwise(pl.col("Backstock unit"))
                    .alias("Backstock unit")
                ])
                .with_columns([
                    pl.when(pl.col("full_pallet") > 0 | pl.col("mu") > 0)
                    .then(pl.lit(0))
                    .otherwise(
                        pl.col("Backstock Cases") / pl.col("pallet_capacity") * 
                        (1 - pl.col("backstock pallet ratio")) * RC_Capacity_Ratio
                    )
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .cast(pl.Float32)
                    .alias("Backstock Rollcages")
                ])
            )

            # Shelf Trolley and other final calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("Backstock Rollcages") * shelf_trolley_cap_ratio_to_rollcage +
                     pl.col("Backstock Pallets") * shelf_trolley_cap_ratio_to_pallet)
                    .alias("Backstock Shelf Trolley"),

                    (pl.col("Pre-sorted Cases") / pl.col("pallet_capacity") * RC_Capacity_Ratio)
                    .cast(pl.Float32)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("Pre-sorted Rollcages"),

                    (pl.col("Pre-sorted Cases") / pl.col("pallet_capacity") * shelf_trolley_cap_ratio_to_pallet)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("Pre-sorted Shelf Trolley"),

                    (pl.col("Full Pallet Cases") / pl.col("pallet_capacity"))
                    .cast(pl.Float32)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("Full Pallet"),

                    (pl.col("MU cases") / pl.col("pallet_capacity"))
                    .cast(pl.Float32)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("MU Pallet"),

                    (pl.col("o_touch") / pl.col("case_capacity"))
                    .cast(pl.Float32)
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("One Touch Cases")
                ])
                .with_columns([
                    (pl.col("One Touch Cases") / weekdays_to_divide)
                    .alias("One Touch Cases")
                ])
            )

            if tpnb_country:
                
                Drivers = Drivers.with_columns(pl.lit(1).alias("foil"))
                
            # SRP and NSRP calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("srp") + pl.col("split_pallet"))
                    .alias("srp_split_pallet"),

                    pl.when(
                        (pl.col("srp_split_pallet") > 0) & (pl.col("light") == 1)
                    )
                    .then(pl.col("cases_to_repl_excl_cap_cases") * pl.col("srp_split_pallet"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("L_SRP")
                ])
                .with_columns([
                    pl.when(pl.col("SRP opening reduction opportunity") == 1)
                    .then(pl.col("L_SRP") * pl.col("foil"))
                    .otherwise(pl.col("L_SRP"))
                    .alias("L_SRP"),

                    pl.when(
                        (pl.col("srp_split_pallet") > 0) & (pl.col("heavy") == 1)
                    )
                    .then(pl.col("cases_to_repl_excl_cap_cases") * pl.col("srp_split_pallet"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("H_SRP")
                ])
                .with_columns([
                    pl.when(pl.col("SRP opening reduction opportunity") == 1)
                    .then(pl.col("H_SRP") * pl.col("foil"))
                    .otherwise(pl.col("H_SRP"))
                    .alias("H_SRP"),

                    pl.when(
                        (pl.col("nsrp") > 0) & (pl.col("light") == 1)
                    )
                    .then(pl.col("cases_to_repl_excl_cap_cases") * pl.col("nsrp") * pl.col("foil"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("L_NSRP"),

                    pl.when(
                        (pl.col("nsrp") > 0) & (pl.col("heavy") == 1)
                    )
                    .then(pl.col("cases_to_repl_excl_cap_cases") * pl.col("nsrp") * pl.col("foil"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("H_NSRP"),

                    pl.when(pl.col("foil") != 1)
                    .then(
                        (1 - pl.col("foil")) * 
                        pl.col("cases_to_repl_excl_cap_cases") * 
                        (pl.col("srp") + pl.col("nsrp") + pl.col("icream_nsrp"))
                    )
                    .otherwise(pl.lit(0))
                    .alias("Foil_Cases")
                ])
            )

            # Secondary SRP/NSRP cases calculations
            if not tpnb_country:
                Drivers = (Drivers
                    .with_columns([
                        (pl.col("secondary_srp") / pl.col("case_capacity"))
                        .cast(pl.Float32)
                        .fill_null(0)
                        .replace({float('inf'): 0, float('-inf'): 0})
                        .alias("Sec_SRP_cases"),

                        (pl.col("secondary_nsrp") / pl.col("case_capacity"))
                        .cast(pl.Float32)
                        .fill_null(0)
                        .replace({float('inf'): 0, float('-inf'): 0})
                        .alias("Sec_NSRP_cases")
                    ])
                )
            else:
                Drivers = (Drivers
                    .with_columns([
                        pl.lit(0).alias("Sec_SRP_cases"),
                        pl.lit(0).alias("Sec_NSRP_cases")
                    ])
                )

            # NSRP Items calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("L_NSRP") * pl.col("case_capacity"))
                    .alias("L_NSRP_Items"),
                    
                    (pl.col("H_NSRP") * pl.col("case_capacity"))
                    .alias("H_NSRP_Items")
                ])
            )

            # Hook calculations
            hook_pmgs = ["DRY13", "HDL21", "PPD02"]
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("pmg").is_in(hook_pmgs))
                    .then(pl.col("L_NSRP"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("L_Hook Fill Cases"),

                    pl.when(pl.col("pmg").is_in(hook_pmgs))
                    .then(pl.col("H_NSRP"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("H_Hook Fill Cases"),

                    pl.when(pl.col("pmg").is_in(hook_pmgs))
                    .then(pl.col("L_NSRP_Items") + pl.col("H_NSRP_Items"))
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias("Hook Fill Items")
                ])
                .with_columns([
                    pl.when(pl.col("pmg").is_in(hook_pmgs))
                    .then(pl.lit(0))
                    .otherwise(pl.col("L_NSRP"))
                    .cast(pl.Float32)
                    .alias("L_NSRP"),

                    pl.when(pl.col("pmg").is_in(hook_pmgs))
                    .then(pl.lit(0))
                    .otherwise(pl.col("H_NSRP"))
                    .cast(pl.Float32)
                    .alias("H_NSRP"),

                    pl.when(pl.col("pmg").is_in(hook_pmgs))
                    .then(pl.lit(0))
                    .otherwise(pl.col("L_NSRP_Items"))
                    .cast(pl.Float32)
                    .alias("L_NSRP_Items"),

                    pl.when(pl.col("pmg").is_in(hook_pmgs))
                    .then(pl.lit(0))
                    .otherwise(pl.col("H_NSRP_Items"))
                    .cast(pl.Float32)
                    .alias("H_NSRP_Items")
                ])
            )

            # Update SRP and NSRP with secondary cases
            Drivers = (Drivers
                .with_columns([
                    pl.when(
                        (pl.col("srp_split_pallet") > 0) & (pl.col("light") == 1)
                    )
                    .then(pl.col("L_SRP") + pl.col("Sec_SRP_cases"))
                    .otherwise(pl.col("L_SRP"))
                    .alias("L_SRP"),

                    pl.when(
                        (pl.col("srp_split_pallet") > 0) & (pl.col("heavy") == 1)
                    )
                    .then(pl.col("H_SRP") + pl.col("Sec_SRP_cases"))
                    .otherwise(pl.col("H_SRP"))
                    .alias("H_SRP"),

                    pl.when(
                        (pl.col("nsrp") > 0) & (pl.col("light") == 1)
                    )
                    .then(pl.col("L_NSRP") + pl.col("Sec_NSRP_cases"))
                    .otherwise(pl.col("L_NSRP"))
                    .alias("L_NSRP"),

                    pl.when(
                        (pl.col("nsrp") > 0) & (pl.col("heavy") == 1)
                    )
                    .then(pl.col("H_NSRP") + pl.col("Sec_NSRP_cases"))
                    .otherwise(pl.col("H_NSRP"))
                    .alias("H_NSRP")
                ])
            )

            # NSRP items by weight calculations
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("weight") <= 1.5)
                    .then(pl.col("H_NSRP") * pl.col("case_capacity"))
                    .otherwise(pl.lit(0))
                    .alias("H_CASE_L_NSRP_items"),

                    pl.when(pl.col("weight") > 1.5)
                    .then(pl.col("H_NSRP") * pl.col("case_capacity"))
                    .otherwise(pl.lit(0))
                    .alias("H_CASE_H_NSRP_items")
                ])
            )

            # High pallet calculations for DRY30 and DRY24
            dry_pmgs = ["DRY30", "DRY24"]
            Drivers = (Drivers
                .with_columns([
                    # High pallet cases
                    pl.when(
                        (pl.col("full_pallet") > 0) & pl.col("pmg").is_in(dry_pmgs)
                    )
                    .then(pl.col("Full Pallet Cases") * 0.2)
                    .otherwise(pl.lit(0))
                    .alias("High_pallet_cases_on_Dry30_and_DRY24"),

                    # High pallets
                    pl.when(
                        (pl.col("full_pallet") > 0) & pl.col("pmg").is_in(dry_pmgs)
                    )
                    .then(pl.col("Full Pallet"))
                    .otherwise(pl.lit(0))
                    .alias("High_pallets_on_Dry30_and_DRY24"),

                    # High half pallet cases
                    pl.when(
                        (pl.col("mu") > 0) & pl.col("pmg").is_in(dry_pmgs)
                    )
                    .then(pl.col("MU cases") * 0.2)
                    .otherwise(pl.lit(0))
                    .alias("High_half_pallet_cases_on_Dry30_and_DRY24"),

                    # High half pallets
                    pl.when(
                        (pl.col("mu") > 0) & pl.col("pmg").is_in(dry_pmgs)
                    )
                    .then(pl.col("MU Pallet"))
                    .otherwise(pl.lit(0))
                    .alias("High_half_pallets_on_Dry30_and_DRY24")
                ])
            )

            # Calculate Bulk Pallets and Total RC's and Pallets
            Drivers = (Drivers
                .with_columns([
                    (pl.col("Full Pallet") + pl.col("MU Pallet"))
                    .alias("Bulk Pallets"),
                    
                    (pl.col("Replenished Rollcages") + 
                     pl.col("Replenished Pallets") + 
                     pl.col("Backstock Rollcages") + 
                     pl.col("Backstock Pallets") + 
                     (pl.col("Full Pallet") + pl.col("MU Pallet")))
                    .alias("Total RC's and Pallets")
                ])
            )

            # Define opening type conditions
            opening_types = {
                "Tray + Hood": "Ownbrand_tray_with_hood_cases",
                "Perforated box": "Ownbrand_perforated_box_cases",
                "Shrink": "Ownbrand_shrink_cases",
                "Tray + Shrink": "Ownbrand_tray_with_shrink_cases",
                "Tray": "Ownbrand_tray_cases"
            }

            # Calculate cases for each opening type
            for opening_type, col_name in opening_types.items():
                Drivers = Drivers.with_columns([
                    pl.when(
                        ((pl.col("opening_type") == opening_type) & 
                         (pl.col("light") == 1) & 
                         (pl.col("srp_split_pallet") > 0)) |
                        ((pl.col("opening_type") == opening_type) & 
                         (pl.col("light") == 1) & 
                         (pl.col("nsrp") > 0)) |
                        ((pl.col("opening_type") == opening_type) & 
                         (pl.col("heavy") == 1) & 
                         (pl.col("srp_split_pallet") > 0)) |
                        ((pl.col("opening_type") == opening_type) & 
                         (pl.col("heavy") == 1) & 
                         (pl.col("nsrp") > 0))
                    )
                    .then(
                        pl.when(pl.col("light") == 1)
                        .then(pl.col("L_SRP") + pl.col("Capping Shelf Cases"))
                        .otherwise(pl.col("H_SRP") + pl.col("Capping Shelf Cases"))
                    )
                    .otherwise(pl.lit(0))
                    .cast(pl.Float32)
                    .alias(col_name)
                ])

            # Calculate total ownbrand metrics
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("opening_type") == "no_data")
                    .then(pl.lit(0))
                    .otherwise(pl.lit(1))
                    .alias("total_ownbrand_op_type"),

                    (pl.col("Ownbrand_tray_with_hood_cases") +
                     pl.col("Ownbrand_perforated_box_cases") +
                     pl.col("Ownbrand_shrink_cases") +
                     pl.col("Ownbrand_tray_with_shrink_cases") +
                     pl.col("Ownbrand_tray_cases"))
                    .alias("total_ownbrand_op_cases")
                ])
            )

            # Split pallet opening cases customization
            pmg_conditions = {
                "DRY24_BWS01": (pl.col("pmg").is_in(["DRY24", "BWS01"]), 0),
                "GROCERY": (pl.col("division") == "Grocery", 0.25),
                "GM": (pl.col("division") == "GM", 1)
            }

            # Calculate split pallet cases for light and heavy
            for weight_type in ["L", "H"]:
                Drivers = (Drivers
                    .with_columns([
                        pl.when(
                            (cond[0]) & 
                            (pl.col("split_pallet") > 0) & 
                            (pl.col(f"{weight_type.lower()}ight") == 1)
                        )
                        .then(pl.col(f"{weight_type}_SRP") * factor)
                        .otherwise(pl.lit(0))
                        .alias(f"{weight_type}_split_pallet_cases_for_opening")
                        for cond, factor in pmg_conditions.values()
                    ])
                )

            # Update opening type columns based on split pallet conditions
            for col_name in opening_types.values():
                for cond, factor in pmg_conditions.values():
                    Drivers = Drivers.with_columns([
                        pl.when(
                            (cond) & 
                            (pl.col("split_pallet") > 0)
                        )
                        .then(pl.col(col_name) * factor)
                        .otherwise(pl.col(col_name))
                        .alias(col_name)
                    ])

            # Calculate SRP and NSRP for opening type
            for weight_type in ["L", "H"]:
                Drivers = (Drivers
                    .with_columns([
                        pl.when(
                            (pl.col("srp") > 0) &
                            (pl.col(f"{weight_type.lower()}ight") == 1) &
                            (pl.col("total_ownbrand_op_type") == 0) &
                            (pl.col("opening_type") != "Returnable Plastic Crate")
                        )
                        .then(pl.col(f"{weight_type}_SRP") + pl.col("Capping Shelf Cases"))
                        .otherwise(pl.lit(0))
                        .cast(pl.Float32)
                        .alias(f"{weight_type}_SRP_for_opening_type")
                    ])
                    .with_columns([
                        pl.when(
                            (pl.col("split_pallet") > 0) &
                            (pl.col(f"{weight_type.lower()}ight") == 1) &
                            (pl.col("total_ownbrand_op_type") == 0) &
                            (pl.col("opening_type") != "Returnable Plastic Crate")
                        )
                        .then(pl.col(f"{weight_type}_split_pallet_cases_for_opening"))
                        .otherwise(pl.col(f"{weight_type}_SRP_for_opening_type"))
                        .cast(pl.Float32)
                        .alias(f"{weight_type}_SRP_for_opening_type"),

                        pl.when(
                            (pl.col("nsrp") > 0) &
                            (pl.col(f"{weight_type.lower()}ight") == 1) &
                            (pl.col("total_ownbrand_op_type") == 0) &
                            (pl.col("opening_type") != "Returnable Plastic Crate")
                        )
                        .then(pl.col(f"{weight_type}_NSRP") + pl.col("Capping Shelf Cases"))
                        .otherwise(pl.lit(0))
                        .cast(pl.Float32)
                        .alias(f"{weight_type}_NSRP_for_opening_type")
                    ])
                )

            # Drop temporary columns
            Drivers = Drivers.drop(["total_ownbrand_op_type", "total_ownbrand_op_cases"])

            # Convert Polars to Pandas for tagging
            Drivers_pd = Drivers.to_pandas()
            
            # Apply tagging function
            Drivers_pd = tagging_on_product(Drivers_pd)
            
            # Convert back to Polars and maintain schema
            Drivers = pl.from_pandas(Drivers_pd)

            # Empty containers calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("Bulk Pallets") + pl.col("Replenished Pallets"))
                    .alias("Empty Pallets"),

                    pl.col("Replenished Rollcages")
                    .alias("Empty Rollcages"),

                    ((pl.col("Empty Pallets") * pl.lit(shelf_trolley_cap_ratio_to_pallet) +
                     pl.col("Empty Rollcages") * pl.lit(shelf_trolley_cap_ratio_to_rollcage)))
                    .alias("Empty Shelf Trolley")
                ])
            )

            # PBL/PBS calculations
            try:
                # Initial PBL/PBS assignments
                Drivers = (Drivers
                    .with_columns([
                        (pl.col("Replenished Rollcages PBL") + pl.col("Replenished Pallets PBL"))
                        .alias("Add_Walking PBL Cages"),

                        pl.col("Replenished Rollcages PBS")
                        .alias("Add_Walking PBS Cages"),

                        pl.lit(0).alias("Add_Walking PBL Pallets"),

                        pl.col("Replenished Pallets PBS")
                        .alias("Add_Walking PBS Pallets")
                    ])
                )

                # Update walking pallets based on conditions
                Drivers = (Drivers
                    .with_columns([
                        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                        .then(pl.lit(0))
                        .otherwise(pl.col("Add_Walking PBL Pallets"))
                        .cast(pl.Float32)
                        .alias("Add_Walking PBL Pallets"),

                        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                        .then(pl.col("Bulk Pallets"))
                        .otherwise(pl.col("Add_Walking PBS Pallets"))
                        .cast(pl.Float32)
                        .alias("Add_Walking PBS Pallets")
                    ])
                )
            except:
                pass

            # Walking and backstock calculations
            Drivers = (Drivers
                .with_columns([
                    pl.col("Backstock Rollcages")
                    .cast(pl.Float32)
                    .alias("Add_Walking Backstock Cages"),

                    pl.col("Replenished Rollcages")
                    .cast(pl.Float32)
                    .alias("Add_Walking Cages"),

                    pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                    .then(pl.col("Bulk Pallets"))
                    .otherwise(pl.col("Replenished Pallets"))
                    .cast(pl.Float32)
                    .alias("Add_Walking Pallets")
                ])
            )

            # Sales and case calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("sold_units") / pl.col("case_capacity"))
                    .fill_null(0)
                    .replace({float('inf'): 0, float('-inf'): 0})
                    .alias("sold_cases")
                ])
                .rename({"sales_excl_vat": "sales"})
            )

            # Single pick customization
            Drivers = (Drivers
                .with_columns([
                    pl.when(pl.col("single_pick") > 0)
                    .then(pl.col("cases_to_replenish"))
                    .otherwise(pl.lit(0))
                    .alias("single_pick_items"),

                    pl.when(pl.col("single_pick") > 0)
                    .then(pl.lit(0))
                    .otherwise(pl.col("cases_delivered"))
                    .alias("cases_delivered_rsu")
                ])
            )

            # Weekly calculations
            Drivers = (Drivers
                .with_columns([
                    (pl.col("stock") / pl.lit(weekdays_to_divide))
                    .alias("stock_unit_weekly"),

                    ((pl.col("stock") / pl.lit(weekdays_to_divide)) / pl.col("case_capacity"))
                    .alias("stock_cases_weekly"),

                    (pl.lit(1) / pl.lit(weekdays_to_divide))
                    .alias("nr_of_tpn"),

                    (pl.col("stock") / pl.lit(weekdays_to_divide))
                    .alias("weekly_stock"),

                    (pl.col("shelfCapacity") / pl.lit(weekdays_to_divide))
                    .alias("shelfCapacity"),

                    (pl.col("case_capacity") / pl.lit(weekdays_to_divide))
                    .alias("case_capacity"),

                    (pl.col("cases_to_replenish") + pl.col("Clip Strip Cases"))
                    .alias("cases_to_replenish"),

                    pl.col("pallet_capacity").alias("pallet_capacity_avg")
                ])
            )

            # Ice cream NSRP calculations
            Drivers = (Drivers
                .with_columns(
                    pl.when(pl.col("icream_nsrp") > 0)
                    .then(pl.col("L_NSRP_Items") + pl.col("H_NSRP_Items"))
                    .otherwise(pl.lit(0))
                    .alias("icreamNSRP")
                )
            )

            # Zero out NSRP items for ice cream
            nsrp_cols = ["H_CASE_H_NSRP_items", "H_CASE_L_NSRP_items", "L_NSRP_Items", "H_NSRP_Items"]
            for col in nsrp_cols:
                Drivers = Drivers.with_columns(
                    pl.when(pl.col("icream_nsrp") > 0)
                    .then(pl.lit(0))
                    .otherwise(pl.col(col))
                    .alias(col)
                )

            # Shelf service GM handling
            if shelfService_gm:
                columns_for_shelService_deletion = [
                    "Replenished Rollcages", "Replenished Pallets", "Backstock Pallets",
                    "Backstock Rollcages", "Backstock Shelf Trolley", "Empty Pallets",
                    "Empty Rollcages", "Empty Shelf Trolley", "Full Pallet",
                    "Full Pallet Cases", "H_CASE_H_NSRP_items", "H_CASE_L_NSRP_items",
                    "H_Hook Fill Cases", "H_NSRP_for_opening_type", "H_SRP", "H_NSRP",
                    "H_SRP_for_opening_type", "Hook Fill Items", "L_NSRP",
                    "L_NSRP_for_opening_type", "L_NSRP_Items", "L_SRP",
                    "L_SRP_for_opening_type", "MU Pallet", "New Delivery - Pallets",
                    "New Delivery - Rollcages", "New Delivery - Shelf Trolley",
                    "Total RC's and Pallets", "Bulk Pallets", "Add_Walking Backstock Cages",
                    "Add_Walking Pallets", "Racking Pallets", "Full + Half Pallet Cases"
                ]

                for col in columns_for_shelService_deletion:
                    Drivers = Drivers.with_columns(
                        pl.when(pl.col("shelfservice_flag") == 1)
                        .then(pl.lit(0))
                        .otherwise(pl.col(col))
                        .alias(col)
                    )

            # Foil disassemble calculations
            foilDisassemble_cols = [
                "H_NSRP_for_opening_type", "L_NSRP_for_opening_type",
                "L_SRP_for_opening_type", "H_SRP_for_opening_type",
                "Ownbrand_perforated_box_cases", "Ownbrand_shrink_cases",
                "Ownbrand_tray_cases", "Ownbrand_tray_with_hood_cases",
                "Ownbrand_tray_with_shrink_cases"
            ]

            Drivers = (Drivers
                .with_columns([
                    pl.sum_horizontal(foilDisassemble_cols)
                    .alias("total_cases_to_disassemble"),

                    pl.when(
                        (pl.sum_horizontal(foilDisassemble_cols) > 0) & 
                        (pl.col("Foil_Cases") > 0)
                    )
                    .then(pl.col("Foil_Cases") * pl.col("extra disassemble %"))
                    .otherwise(pl.lit(0))
                    .alias("cases_foil_to_disassemble")
                ])
            )

            # Select final columns
            final_columns = [
                "country", "store", "day", "dep", "pmg", "t_touch", "cases_to_replenish",
                "Sec_NSRP_cases", "Sec_SRP_cases", "light", "heavy", "backstock pallet ratio",
                "Replenished Rollcages", "Replenished Pallets", "tpnb", "sold_units",
                "sold_cases", "sales", "stock", "weekly_stock", "cases_delivered",
                "cases_delivered_on_sf", "cases_delivered_rsu", "Add_Walking Cages",
                "pallet_capacity", "Add_Walking Backstock Cages", "Add_Walking Pallets",
                "Backstock unit", "Backstock Cases", "Backstock Pallets", "Backstock Rollcages",
                "Backstock Shelf Trolley", "backroom_pallets", "Bottle_Tag", "Broken Items",
                "broken_case_flag", "Bulk Pallets", "Capping Shelf Cases", "Clip Strip Cases",
                "Clip Strip Items", "Electro_Tag", "Empty Pallets", "Empty Rollcages",
                "Empty Shelf Trolley", "Foil_Cases", "Full Pallet", "Full Pallet Cases",
                "Full + Half Pallet Cases", "icreamNSRP", "Gillette_Tag", "H_Post-sort Cases",
                "H_CASE_H_NSRP_items", "H_CASE_L_NSRP_items", "single_pick_items",
                "H_Hook Fill Cases", "L_Hook Fill Cases", "H_NSRP_for_opening_type",
                "H_Pre-sorted Cases", "H_SRP", "H_NSRP", "H_SRP_for_opening_type",
                "Hard_Tag", "Hook Fill Items", "L_Post-sort Cases", "L_NSRP",
                "L_NSRP_for_opening_type", "L_NSRP_Items", "L_Pre-sorted Cases", "L_SRP",
                "L_SRP_for_opening_type", "MU Pallet", "New Delivery - Pallets",
                "New Delivery - Rollcages", "New Delivery - Shelf Trolley",
                "Ownbrand_perforated_box_cases", "Ownbrand_shrink_cases",
                "Ownbrand_tray_cases", "Ownbrand_tray_with_hood_cases",
                "Ownbrand_tray_with_shrink_cases", "Pre-sorted Rollcages",
                "Pre-sorted Shelf Trolley", "Post-sort Cases", "Post-sort Pallets",
                "Post-sort Rollcages", "Racking Pallets", "Safer_Tag", "Salami_Tag",
                "Soft_Tag", "CrocoTag", "BliszterfülTag", "Total RC's and Pallets",
                "High_pallet_cases_on_Dry30_and_DRY24", "High_pallets_on_Dry30_and_DRY24",
                "High_half_pallet_cases_on_Dry30_and_DRY24", "High_half_pallets_on_Dry30_and_DRY24",
                "Tag_total_nr", "shelfCapacity", "nr_of_tpn", "unit", "case_capacity",
                "Two Touch Cases", "Two Touch unit", "total_cases_to_disassemble",
                "cases_foil_to_disassemble", "stock_cases_weekly", "Add_Walking PBL Cages",
                "Add_Walking PBS Cages", "Add_Walking PBL Pallets", "Add_Walking PBS Pallets",
                "PBL_CAGE", "PBL_PALLET", "PBS_CAGE", "PBS_PALLET", "pallet_capacity_avg"
            ]

            Drivers = Drivers.select(final_columns)

            Drivers  = Drivers.to_pandas()
            
            
            
            if cases_to_replenish_only == False: 
        
                # PRODUCE
            
                if len(Drivers_produce) > 0:
            
                    Drivers_produce = Drivers_produce[
                        [
                            "country",
                            "store",
                            "day",
                            "tpnb",
                            "dep",
                            "pmg",
                            "cases_delivered",
                            "case_capacity",
                            "pallet_capacity",
                            "stock",
                            "sold_units",
                            "sales_excl_vat",
                            "unit_type",
                            "weight",
                            "srp",
                            "nsrp",
                            "full_pallet",
                            "mu",
                            "split_pallet",
                            "icream_nsrp",
                            "unit",
                            "shelfCapacity"

                        ]
                    ]
                    produce_df = pd.read_excel(directory / excel_inputs_f, "produce_dataframe")
                    produce_df.columns = [i.lower() for i in produce_df.columns]
                    produce_modules = pd.read_excel(directory / excel_inputs_f, "produce_modules")
                    produce_modules.columns = [i.lower() for i in produce_modules.columns]
                    RC_table = produce_df[["pmg", "replenishment_type", "rc_capacity"]].copy()
                    Drivers_produce = Drivers_produce.merge(
                        produce_df[produce_df.columns[~produce_df.columns.isin(["rc_capacity"])]],
                        on="pmg",
                        how="left",
                    )
                    # =============================================================================
                    # Crates Customizing + Average Items in Case
                    # - custom crates shows a total amount of LARGE CRATES. So, if we have 4 small crates then we treat them as 2 large
                    # - daily_crates_on_stock = stock crates + sold crates
                    # - items in case is necesary for categories replenished as an item
                    # =============================================================================
                    # Drivers_produce['stock'] = Drivers_produce['cases_delivered']
                    # Drivers_produce['stock'] = ((Drivers_produce.groupby(['store', 'tpn'])['stock'].transform("max"))/7).astype("float32")
                    Drivers_produce["sold_units"] = np.where(
                        (Drivers_produce.pmg == "PRO04") | (Drivers_produce.pmg == "PRO01"),
                        Drivers_produce["sold_units"] * 1,
                        Drivers_produce["sold_units"],
                    )
                    Drivers_produce["crates_on_stock"] = (
                        Drivers_produce.stock / Drivers_produce.case_capacity
                    )
                    Drivers_produce["custom_sold_crates"] = np.where(
                        Drivers_produce.unit_type == "KG",
                        Drivers_produce.sold_units / (Drivers_produce.case_capacity * 1),
                        Drivers_produce.sold_units / Drivers_produce.case_capacity,
                    )
                    Drivers_produce["custom_sold_crates"] = np.where(
                        (Drivers_produce.crate_size == "Small"),
                        Drivers_produce.custom_sold_crates / 2,
                        Drivers_produce.custom_sold_crates,
                    ).astype("float32")
                    Drivers_produce["custom_sold_crates"] = np.where(
                        (Drivers_produce.crate_size == "Other"),
                        Drivers_produce.custom_sold_crates / 4,
                        Drivers_produce.custom_sold_crates,
                    ).astype("float32")
                    Drivers_produce["custom_stock_crates"] = np.where(
                        (Drivers_produce.crate_size == "Small"),
                        Drivers_produce.crates_on_stock / 2,
                        Drivers_produce.crates_on_stock,
                    ).astype("float32")
                    Drivers_produce["custom_stock_crates"] = np.where(
                        (Drivers_produce.crate_size == "Other"),
                        Drivers_produce.custom_stock_crates / 4,
                        Drivers_produce.custom_stock_crates,
                    ).astype("float32")
                    # Drivers_produce['daily_crates_on_stock'] = Drivers_produce.custom_sold_crates + Drivers_produce.custom_stock_crates # daily_stock = stock on the end of a day + what they sold this day
                    Drivers_produce["total_sales_per_repl_type"] = Drivers_produce.groupby(
                        ["store", "day", "replenishment_type"], observed=True
                    )["sold_units"].transform("sum")
                    # =============================================================================
                    # Capacity
                    # - custom_crates_on_stock = total of daily crates on stock (daily sold crates + daily stock)
                    # - custom_tpn_on_stock = total amount of TPNs per pmg and store
                    # - multideck_group = in here we check how many crates can be filled on modules. We take into consideration:
                    #     1. stock ratio per pmg/store
                    #     2. amount of crates per tpn. If we have more than 4 crates per TPN then we put it to warehouse (we have a place just for 4 tpns per one TPN)
                    # - backstock = is calculated based on custom_crates_on_stock. So it is daily sold crates + daily stock
                    # =============================================================================
                    # Drivers_produce['crates_per_tpn'] = Drivers_produce.daily_crates_on_stock / dataset_group.tpn
                    # Drivers_produce['daily_crates_on_stock_tpn'] = np.where(Drivers_produce.daily_crates_on_stock > 4, 4 * Drivers_produce.tpn, Drivers_produce.daily_crates_on_stock)
                    Drivers_produce = Drivers_produce.merge(
                        produce_modules[["store", "tables", "multidecks"]], on="store", how="left"
                    )
                    Drivers_produce["one_touch"] = np.where(
                        Drivers_produce.replenishment_type == "Multideck",
                        MODULE_CRATES * Drivers_produce.multidecks,
                        0,
                    )  # dataset_group (banana: 3 crates on hammock + 4 below)
                    Drivers_produce["one_touch"] = np.where(
                        Drivers_produce.replenishment_type == "Produce_table",
                        TABLE_CRATES * Drivers_produce.tables,
                        Drivers_produce.one_touch,
                    )  # calculate shop-floor capacity based on knowledge about total amount of modules
                    Drivers_produce["one_touch"] = np.where(
                        Drivers_produce.replenishment_type == "Stand", 50, Drivers_produce.one_touch
                    )
                    Drivers_produce["one_touch"] = np.where(
                        Drivers_produce.replenishment_type == "Hammok", 7, Drivers_produce.one_touch
                    )
                    Drivers_produce["one_touch"] = np.where(
                        Drivers_produce.replenishment_type == "Bin", 50, Drivers_produce.one_touch
                    )
            
                    Drivers_produce["sales_repl_type_tpn_sales_ratio"] = (
                        Drivers_produce["sold_units"] / Drivers_produce["total_sales_per_repl_type"]
                    )
                    Drivers_produce["sales_repl_type_tpn_sales_ratio"] = (
                        Drivers_produce["sales_repl_type_tpn_sales_ratio"]
                        .replace(np.nan, 0)
                        .replace([np.inf, -np.inf], 0)
                    )
                    Drivers_produce["one_touch_for_tpns"] = np.ceil(
                        Drivers_produce["one_touch"]
                        * Drivers_produce["sales_repl_type_tpn_sales_ratio"]
                    )
            
                    Drivers_produce["backstock"] = np.where(
                        Drivers_produce.one_touch_for_tpns <= Drivers_produce.custom_stock_crates,
                        Drivers_produce.custom_stock_crates - Drivers_produce.one_touch_for_tpns,
                        0,
                    ).astype("float32")
            
                    Drivers_produce.rename(columns={"sales_excl_vat": "sales"}, inplace=True)
                    Drivers_produce = Drivers_produce[
                        [
                            "country",
                            "store",
                            "day",
                            "dep",
                            "pmg",
                            "tpnb",
                            "stock",
                            "weight",
                            "replenishment_type",
                            "unit_type",
                            "pallet_capacity",
                            "one_touch",
                            "backstock",
                            "cases_delivered",
                            "sold_units",
                            "sales",
                            "custom_sold_crates",
                            "case_capacity",
                            "srp",
                            "nsrp",
                            "full_pallet",
                            "mu",
                            "split_pallet",
                            "icream_nsrp",
                            "unit",
                            "shelfCapacity"

                        ]
                    ]
            
                    Drivers_produce["total_unit_to_fill"] = np.where(
                        (
                            Drivers_produce.cases_delivered * Drivers_produce.case_capacity
                            + Drivers_produce.stock
                        )
                        > Drivers_produce.sold_units,
                        Drivers_produce.sold_units,
                        (
                            Drivers_produce.cases_delivered * Drivers_produce.case_capacity
                            + Drivers_produce.stock
                        ),
                    )
            
                    cycle_list = 5
                    cycle_list_2 = 2
                    cycle_list_1 = 1
            
                    unit_cond = [
                        Drivers_produce["custom_sold_crates"] >= cycle_list,
                        (Drivers_produce["custom_sold_crates"] < cycle_list)
                        & (Drivers_produce["custom_sold_crates"] > cycle_list_1),
                        (Drivers_produce["custom_sold_crates"] < cycle_list)
                        & (Drivers_produce["custom_sold_crates"] < cycle_list_2)
                        & (Drivers_produce["custom_sold_crates"] < cycle_list_1),
                    ]
                    unit_result = [
                        Drivers_produce.sold_units / cycle_list,
                        Drivers_produce.sold_units / cycle_list_2,
                        Drivers_produce.sold_units / cycle_list_1,
                    ]
            
                    Drivers_produce["UNIT_to_replenish_per_rounds"] = np.select(
                        unit_cond, unit_result, 0
                    )
            
                    crates_cond = [
                        Drivers_produce["custom_sold_crates"] >= cycle_list,
                        (Drivers_produce["custom_sold_crates"] < cycle_list)
                        & (Drivers_produce["custom_sold_crates"] > cycle_list_1),
                        (Drivers_produce["custom_sold_crates"] < cycle_list)
                        & (Drivers_produce["custom_sold_crates"] < cycle_list_2)
                        & (Drivers_produce["custom_sold_crates"] > 0.5),
                        (Drivers_produce["custom_sold_crates"] < 0.5),
                    ]
            
                    crates_result = [
                        (
                            (
                                np.ceil(
                                    Drivers_produce["UNIT_to_replenish_per_rounds"]
                                    / Drivers_produce.case_capacity
                                )
                            )
                            * cycle_list
                        ),
                        (
                            (
                                np.ceil(
                                    Drivers_produce["UNIT_to_replenish_per_rounds"]
                                    / Drivers_produce.case_capacity
                                )
                            )
                            * cycle_list_2
                        ),
                        (
                            (
                                np.ceil(
                                    Drivers_produce["UNIT_to_replenish_per_rounds"]
                                    / Drivers_produce.case_capacity
                                )
                            )
                            * cycle_list_1
                        ),
                        0,
                    ]
            
                    Drivers_produce["CRATES_to_replenish"] = np.select(
                        crates_cond, crates_result, 0
                    )
            
                    # Drivers_produce['shelf_filling'] = Drivers_produce.one_touch
                    # Drivers_produce['crates_to_replenish'] = 0
            
                    # for x, y in zip(range(1,cycle_list+1),range(cycle_list)):
            
                    #     Drivers_produce[f'cycle_{x}']=np.where((((Drivers_produce['shelf_filling']-(SALES_CYCLE[y]*Drivers_produce['custom_sold_crates']))/Drivers_produce['shelf_filling'])<FULFILL_TARGET),1,0)
                    #     Drivers_produce['crates_to_replenish']=np.where(Drivers_produce[f'cycle_{x}']>0, ((Drivers_produce['crates_to_replenish']+(Drivers_produce['one_touch']-Drivers_produce['shelf_filling']))+(Drivers_produce['custom_sold_crates']*SALES_CYCLE[y])), Drivers_produce['crates_to_replenish'])
                    #     Drivers_produce['shelf_filling']=np.where(Drivers_produce[f'cycle_{x}']>0, Drivers_produce['one_touch'], Drivers_produce['shelf_filling']-(Drivers_produce['custom_sold_crates']*SALES_CYCLE[y]))
            
                    # =============================================================================
                    # Weekly Drivers calculation
                    # - backstock_cases_replenished is required just to know how many times I need to move
                    # - backstock_rc shows amount of RCs which have to be moved on Shop-Floor (sometimes the same RC needs to be moved more than once). So it is NOT amount of RCs bout amout of stock movements
                    # =============================================================================
            
                    Drivers_produce = Drivers_produce.merge(
                        RC_table, on=["pmg", "replenishment_type"], how="left"
                    )
                    # Drivers_produce['one_touch_cases'] = (Drivers_produce.one_touch/(Drivers_produce.one_touch+Drivers_produce.backstock))*Drivers_produce.cases_delivered
                    Drivers_produce["Backstock Cases"] = Drivers_produce["backstock"]
                    
                    Drivers_produce["Post-sort Cases"] = Drivers_produce["Backstock Cases"]
            
                    Drivers_produce["backstock_cases_frequency"] = cycle_list
                    Drivers_produce["backstock_cases_replenished"] = Drivers_produce[
                        "backstock"
                    ]  # ((Drivers_produce.CRATES_to_replenish/Drivers_produce.backstock)*Drivers_produce['Backstock Cases'])
                    Drivers_produce["pre_sorted_cases"] = (
                        Drivers_produce.backstock_cases_replenished * 0.25
                    )  # presort% from Store_Inputs table
                    Drivers_produce["Pre-sorted Rollcages"] = (
                        Drivers_produce.pre_sorted_cases / Drivers_produce.rc_capacity
                    )
                    Drivers_produce["one_touch_rc"] = (
                        Drivers_produce.cases_delivered / Drivers_produce.rc_capacity
                    )
                    Drivers_produce["Backstock Rollcages"] = 0
                    Drivers_produce["Backstock Pallets"] = np.where(
                        Drivers_produce.replenishment_type != "Other",
                        (
                            (
                                Drivers_produce.backstock_cases_replenished
                                / Drivers_produce.rc_capacity
                            )
                            * RC_CAPACITY
                        )
                        * (1 - RC_DELIVERY),
                        0,
                    ).astype("float32")
                    Drivers_produce["backstock_rc_incl_frequencies"] = (
                        Drivers_produce.backstock_cases_replenished
                        / Drivers_produce.rc_capacity
                        * Drivers_produce.backstock_cases_frequency
                    )
                    Drivers_produce = Drivers_produce.replace(np.nan, 0)
                    Drivers_produce["weight_selector"] = (
                        Drivers_produce.weight * Drivers_produce.case_capacity
                    )  # 1. Heavy & Light
                    Drivers_produce["heavy_crates"] = np.where(
                        Drivers_produce.weight_selector >= 5, 1, 0
                    ).astype("int8")
                    Drivers_produce["light_crates"] = np.where(
                        Drivers_produce.weight_selector < 5, 1, 0
                    ).astype("int8")
                    Drivers_produce.drop(["weight_selector"], axis=1, inplace=True)
                    Drivers_produce["L_Pre-sorted Crates"] = np.where(
                        Drivers_produce["light_crates"] == 1, Drivers_produce["pre_sorted_cases"], 0
                    ).astype("float32")
                    Drivers_produce["H_Pre-sorted Crates"] = np.where(
                        Drivers_produce["heavy_crates"] == 1, Drivers_produce["pre_sorted_cases"], 0
                    ).astype("float32")
                    Drivers_produce["L_Post-sort Cases"] = np.where(
                        Drivers_produce["light_crates"] == 1, Drivers_produce["Post-sort Cases"], 0
                    ).astype("float32")
                    Drivers_produce["H_Post-sort Cases"] = np.where(
                        Drivers_produce["heavy_crates"] == 1, Drivers_produce["pre_sorted_cases"], 0
                    ).astype("float32")
                    Drivers_produce["New Delivery - Rollcages"] = (
                        Drivers_produce.cases_delivered / Drivers_produce.rc_capacity
                    ) * RC_DELIVERY
                    Drivers_produce["New Delivery - Pallets"] = np.where(
                        Drivers_produce.replenishment_type != "Other",
                        (Drivers_produce["New Delivery - Rollcages"] * RC_CAPACITY)
                        * (1 - RC_DELIVERY),
                        0,
                    ).astype("float32")
                    # Drivers_produce['New Delivery - Pallet_cases'] = Drivers_produce['New Delivery - Pallets'] * Drivers_produce.case_capacity
                    # Drivers_produce['New Delivery - Rollcages'] = (Drivers_produce.cases_delivered-Drivers_produce['New Delivery - Pallet_cases']) /Drivers_produce.rc_capacity
                    Drivers_produce["Replenished Rollcages"] = Drivers_produce[
                        "New Delivery - Rollcages"
                    ]  # Replenished Rollcages and Pallets - it is different than on Repl as we do not pre-sort new delivery on produce (just backstock)
                    Drivers_produce["Replenished Pallets"] = Drivers_produce[
                        "New Delivery - Pallets"
                    ]
                    Drivers_produce["Green crates case fill"] = np.where(
                        (
                            (Drivers_produce.replenishment_type == "Multideck")
                            | (Drivers_produce.replenishment_type == "Produce_table")
                            | (Drivers_produce.replenishment_type == "Hammok")
                        )
                        & (Drivers_produce.unit_type == "KG"),
                        Drivers_produce.CRATES_to_replenish,
                        0,
                    )
                    Drivers_produce["L_Green crates case fill"] = np.where(
                        Drivers_produce["light_crates"] == 1,
                        Drivers_produce["Green crates case fill"],
                        0,
                    )
                    Drivers_produce["H_Green crates case fill"] = np.where(
                        Drivers_produce["heavy_crates"] == 1,
                        Drivers_produce["Green crates case fill"],
                        0,
                    )
                    Drivers_produce["Green crates unit fill"] = np.where(
                        (
                            (Drivers_produce.replenishment_type == "Multideck")
                            | (Drivers_produce.replenishment_type == "Produce_table")
                            | (Drivers_produce.replenishment_type == "Hammok")
                        )
                        & (Drivers_produce.unit_type != "KG"),
                        Drivers_produce.CRATES_to_replenish,
                        0,
                    )
                    Drivers_produce["Green crates unit fill items"] = np.where(
                        (
                            (Drivers_produce.replenishment_type == "Multideck")
                            | (Drivers_produce.replenishment_type == "Produce_table")
                            | (Drivers_produce.replenishment_type == "Hammok")
                        )
                        & (Drivers_produce.unit_type != "KG"),
                        Drivers_produce.total_unit_to_fill,
                        0,
                    )
                    Drivers_produce["Bulk Product Cases"] = np.where(
                        Drivers_produce.pmg == "PRO14", Drivers_produce.CRATES_to_replenish, 0
                    )  # Bulk Seeds / Nuts / Dried Fruits - CASE
                    Drivers_produce["Backstock_Frequency"] = np.where(
                        Drivers_produce.replenishment_type != "Other",
                        Drivers_produce.backstock_rc_incl_frequencies / 1,
                        0,
                    ).astype("float32")
                    Drivers_produce["Empty Rollcages"] = Drivers_produce[
                        "Backstock Rollcages"
                    ]  # we calculate it in wrong way but we did not use it in the model as we calculated it in the model's Drivers sheet
                    Drivers_produce["Empty Pallets"] = Drivers_produce["Backstock Pallets"]
                    Drivers_produce["Potted Plants Cases"] = np.where(
                        Drivers_produce.pmg == "PRO13", Drivers_produce.CRATES_to_replenish, 0
                    )  # Flower + Garden - Tray fill & Unit Fill (potted plant) - herbs
                    Drivers_produce["Potted Plants Items"] = np.where(
                        Drivers_produce.pmg == "PRO13", Drivers_produce.total_unit_to_fill, 0
                    )
                    Drivers_produce["Banana Cases"] = np.where(
                        Drivers_produce.pmg == "PRO01", Drivers_produce.CRATES_to_replenish, 0
                    )
                    Drivers_produce["Banana Shelves Cases"] = np.where(
                        Drivers_produce.pmg == "PRO01",
                        Drivers_produce.CRATES_to_replenish * 0.57,
                        0,
                    )  # Banana_Cases & Banana shelves cases below hammock & Banana Hammock - BUNCH
                    Drivers_produce["Banana Hammock Bunch"] = np.where(
                        Drivers_produce.pmg == "PRO01", Drivers_produce.total_unit_to_fill, 0
                    )
                    Drivers_produce["Cut Flowers"] = np.where(
                        Drivers_produce.pmg == "PRO17", Drivers_produce.CRATES_to_replenish, 0
                    )  # Flower + Garden - Cut Flower - CASE= Bucket; Cut Flowers
                    Drivers_produce["Flowers Rollcages"] = np.where(
                        Drivers_produce.pmg == "PRO17",
                        RC_DELIVERY * Drivers_produce["one_touch_rc"],
                        0,
                    ).astype("float32")
                    Drivers_produce["Flowers Pallets"] = np.where(
                        Drivers_produce.pmg == "PRO17",
                        (
                            Drivers_produce["one_touch_rc"]
                            - (Drivers_produce["one_touch_rc"] * RC_DELIVERY)
                        )
                        * RC_VS_PAL_CAPACITY,
                        0,
                    ).astype("float32")
                    Drivers_produce["Bulk Pallets"] = 0
                    Drivers_produce["Total RC's and Pallets"] = (
                        Drivers_produce["Replenished Rollcages"]
                        + Drivers_produce["Replenished Pallets"]
                        + Drivers_produce["Backstock Rollcages"]
                        + Drivers_produce["Backstock Pallets"]
                        + Drivers_produce["Bulk Pallets"]
                    ).astype("float32")
                    Drivers_produce["Total Green Crates"] = (
                        Drivers_produce["Green crates case fill"]
                        + Drivers_produce["Green crates unit fill"]
                    )
            
                    Drivers_produce["Add_Walking Backstock Cages"] = (
                        Drivers_produce["Backstock Rollcages"]
                    ).astype("float32")
                    Drivers_produce["Add_Walking Cages"] = (
                        Drivers_produce["Replenished Rollcages"]
                    ).astype("float32")
                    Drivers_produce["Add_Walking Pallets"] = Drivers_produce["Replenished Pallets"]
            
                    Drivers_produce.replace([np.inf, -np.inf], 0, inplace=True)
                    Drivers_produce.replace(np.nan, 0, inplace=True)
                    Drivers_produce['cases_delivered_rsu'] = Drivers_produce["cases_delivered"]
                    
                    Drivers_produce["Post-sort Rollcages"] = Drivers_produce["Backstock Rollcages"]
                    Drivers_produce["Post-sort Pallets"] = Drivers_produce["Backstock Pallets"]
                    
                    Drivers_produce["weekly_stock"] = Drivers_produce["stock"] / 7 
                    Drivers_produce["case_capacity"] = Drivers_produce["case_capacity"] / 7
                    Drivers_produce["shelfCapacity"] = Drivers_produce["shelfCapacity"] / 7
                    Drivers_produce = Drivers_produce[
                        [
                            "country",
                            "store",
                            "day",
                            "dep",
                            "pmg",
                            "tpnb",
                            "sold_units",
                            "sales",
                            "stock",
                            "cases_delivered",
                            "cases_delivered_rsu",
                            "Backstock Rollcages",
                            "Backstock Cases",
                            "Post-sort Cases",
                            "L_Post-sort Cases",
                            "H_Post-sort Cases",
                            "Backstock Pallets",
                            "Banana Cases",
                            "Banana Shelves Cases",
                            "Banana Hammock Bunch",
                            "Cut Flowers",
                            "Flowers Rollcages",
                            "Flowers Pallets",
                            "Bulk Pallets",
                            "Total RC's and Pallets",
                            "Total Green Crates",
                            "Add_Walking Backstock Cages",
                            "Add_Walking Cages",
                            "Add_Walking Pallets",
                            "Potted Plants Cases",
                            "Potted Plants Items",
                            "Post-sort Pallets",
                            "Post-sort Rollcages",
                            "Empty Rollcages",
                            "Empty Pallets",
                            "New Delivery - Rollcages",
                            "New Delivery - Pallets",
                            "Green crates case fill",
                            "L_Green crates case fill",
                            "H_Green crates case fill",
                            "Green crates unit fill",
                            "Green crates unit fill items",
                            "Bulk Product Cases",
                            "Pre-sorted Rollcages",
                            "L_Pre-sorted Crates",
                            "H_Pre-sorted Crates",
                            "unit",
                            "CRATES_to_replenish",
                            "case_capacity",
                            "shelfCapacity",
                            "weekly_stock"
                        ]
                    ]
                    Drivers_produce = optimize_objects(optimize_types(Drivers_produce))
            
                if len(Drivers_produce) == 0:
                    Drivers_produce = Drivers_produce[["store", "tpnb"]]
            
                return Drivers_produce, Drivers
        
            if cases_to_replenish_only == True:
                return  cases_to_replenish_tpn, cases_to_replenish_dep


