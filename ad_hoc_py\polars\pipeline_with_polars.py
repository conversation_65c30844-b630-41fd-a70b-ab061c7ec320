import json
import polars as pl
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict
import matplotlib.pyplot as plt
plt.rcParams["figure.figsize"] = (20,10)
pl.Config.set_tbl_width_chars(100)


directory = r"C:\Users\<USER>\Downloads\archive_kagle"
csv_path  = r'\GBvideos.csv'
json_path = r'\US_category_id.json'

df = pl.read_csv(directory + csv_path)

with open(directory + json_path, 'r') as f:
    categories = json.load(f)
    
id_to_category = {}
for c in categories['items']:
    id_to_category[int(c['id'])] = c['snippet']['title']
    
    
def parse_dates(df: pl.DataFrame, date_cols: Dict[str, str]) -> pl.DataFrame:
    expressions = []
    for date_col, format in date_cols.items():
        expressions.append(pl.col(date_col).str.to_date(format=format))
        
    df = df.with_columns(expressions)
    return df

# Column name with expected date format
date_column_format = {
    "trending_date": '%y.%d.%m',
    "publish_time": '%Y-%m-%dT%H:%M:%S%.fZ'
}

df = parse_dates(df, date_column_format).with_columns(
    pl.col("category_id").map_dict(id_to_category)
)


df = df.with_columns(
    time_to_trending=pl.col("trending_date") - pl.col("publish_time").dt.date(),
    likes_to_dislike_ratio=pl.col("likes") / pl.col("dislikes"),
    likes_to_views_ratio=pl.col("likes") / pl.col("views"),
    comments_to_views_ratio=pl.col("comment_count") / pl.col("views"),
)

# Sense check 2 features
print(df[["trending_date", "publish_time", "time_to_trending"]].sample(2))
print(df[["likes", "dislikes", "likes_to_dislike_ratio"]].sample(2))



df = df.with_columns(
    trending_weekday = pl.col('trending_date').dt.weekday(),
    trending_month = pl.col('trending_date').dt.month(),
    trending_year = pl.col("trending_date").dt.year()
)

time_to_trending_df = df.groupby(
    ["video_id", "title", "category_id", "channel_title"]
).agg(
    pl.col("time_to_trending").min().dt.days(),
    pl.col("trending_date").min().dt.date().alias("first_day_in_trending"),
    pl.col("trending_date").max().dt.date().alias("last_day_in_trending"),
    (pl.col("trending_date").max() - pl.col("trending_date").min()).dt.days()
    .alias("days_in_trending"),
)

print(f"Average time to trending is {time_to_trending_df['time_to_trending'].mean()} days")
print(f"Median time to trending is {time_to_trending_df['time_to_trending'].median()} days")


time_to_trending_df = time_to_trending_df.filter(pl.col("time_to_trending") <= 60)
print(f"Average time to trending is {time_to_trending_df['time_to_trending'].mean()} days")
print(f"Median time to trending is {time_to_trending_df['time_to_trending'].median()} days")


# fastest_category_to_trending = (
#     time_to_trending_df.with_columns(
#         # Count over category ID
#         times_in_trending=pl.count().over("category_id")
#     # Filter infrequent categories
#     ).filter(pl.col("times_in_trending") >= 100)
#     # Calculate mean time to trending
#     .groupby("category_id")
#     .agg(pl.col("time_to_trending").mean())
#     .sort("time_to_trending")
# )

def avg_frequent(
    df: pl.DataFrame,
    by: str,
    frequency_threshold: int,
    metric: str = "time_to_trending",
) -> pl.DataFrame:
    results = (
        df.with_columns(times_in_trending=pl.count().over(by))
        .filter(pl.col("times_in_trending") >= frequency_threshold)
        .groupby(by)
        .agg(pl.col(metric).mean())
        .sort(metric)
    )

    return results

fastest_category_to_trending = avg_frequent(
    time_to_trending_df, by="category_id", frequency_threshold=100
).head(3)
fastest_channel_to_trending = avg_frequent(
    time_to_trending_df, by="channel_title", frequency_threshold=10
).head(3)

print(fastest_category_to_trending)
print(fastest_channel_to_trending)



longest_trending_categories = avg_frequent(
    time_to_trending_df,
    by="category_id",
    frequency_threshold=100,
    metric="days_in_trending",
).tail(3)  # tails because it's sorted in descending

longest_trending_channels = avg_frequent(
    time_to_trending_df,
    by="channel_title",
    frequency_threshold=10,
    metric="days_in_trending",
).tail(3)

print(longest_trending_categories)
print(longest_trending_channels)

top_categories = (
    df["category_id"].value_counts(sort=True).head(7)["category_id"].to_list()
)
print(top_categories)

trending_monthly_stats = df.groupby_dynamic(
    index_column="trending_date",  # date column
    every="1mo",  # can also me 1w, 1d, 1h etc
    closed="both",  # including starting and end date
    by="category_id", # other grouping columns
    include_boundaries=True,  # showcase the boudanries
).agg(
    pl.col("video_id").n_unique().alias("videos_number"),
)

print(trending_monthly_stats.sample(3))





# plotting_df = trending_monthly_stats.filter(pl.col("category_id").is_in(top_categories))

# sns.lineplot(
#     x=plotting_df["trending_date"],
#     y=plotting_df["videos_number"],
#     hue=plotting_df["category_id"],
#     style=plotting_df["category_id"],
#     markers=True,
#     dashes=False,
#     palette='Set2'
# )

# plt.title("Total Number of Videos in Trending per Category per Month")



trending_monthly_stats_unique = (
    time_to_trending_df.sort("first_day_in_trending")
    .groupby_dynamic(
        index_column="first_day_in_trending",
        every="1mo",
        by="category_id",
        include_boundaries=True,
    )
    .agg(pl.col("video_id").n_unique().alias("videos_number"))
)

views_per_category_date = (
    df.groupby(["category_id", "trending_date"])
    .agg(pl.col("views").sum())
    .sort(["category_id", "trending_date"])
)

# Calculate rolling average
views_per_category_date_rolling = views_per_category_date.groupby_rolling(
    index_column="trending_date",  # Date column
    by="category_id",  # Grouping column
    period="1w"  # Rolling length
).agg(
    pl.col("views").mean().alias("rolling_weekly_average")
)

# Plotting
plotting_df = views_per_category_date_rolling.filter(pl.col("category_id").is_in(['Music', 'Entertainment']))
sns.lineplot(
    x=plotting_df["trending_date"],
    y=plotting_df["rolling_weekly_average"],
    hue=plotting_df["category_id"],
    style=plotting_df["category_id"],
    markers=True,
    dashes=False,
    palette='Set2'
)

plt.title("7-day Views Average")





def process_date(df, date_column, format):
    result = df.with_columns(pl.col(date_column).str.to_date(format))
    return result


def filter_year(df, date_column, year):
    result = df.filter(pl.col(date_column).dt.year() == year)
    return result


def get_first_by_month(df, date_column, metric):
    result = df.with_columns(
        pl.col(metric)
        .rank(method="ordinal", descending=True)
        .over(pl.col(date_column).dt.month())
        .alias("rank")
    ).filter(pl.col("rank") == 1)

    return result

def select_data_to_write(df, columns):
    result = df.select([pl.col(c) for c in columns])
    return result



a = pl.read_csv(directory + csv_path).pipe(process_date, date_column="trending_date", format="%y.%d.%m").pipe(filter_year, date_column="trending_date", year=2018).pipe(get_first_by_month, date_column="trending_date", metric="views").pipe(
    select_data_to_write,
    columns=["trending_date", "title", "channel_title", "views"],
    )


b = (
    pl.scan_csv(directory + csv_path).pipe(process_date, date_column="trending_date", format="%y.%d.%m")
    .pipe(filter_year, date_column="trending_date", year=2018)
    .pipe(get_first_by_month, date_column="trending_date", metric="views")
    .pipe(
        select_data_to_write,
        columns=["trending_date", "title", "channel_title", "views"],
    )).collect()




# =============================================================================
# Building Data PipeLine with Polars
# =============================================================================

import json
import polars as pl
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict
import matplotlib.pyplot as plt
plt.rcParams["figure.figsize"] = (20,10)
pl.Config.set_tbl_width_chars(100)


directory = r"C:\Users\<USER>\Downloads\archive_kagle"
csv_path  = r'\GBvideos.csv'
json_path = r'\US_category_id.json'


