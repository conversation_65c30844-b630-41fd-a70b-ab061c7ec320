{"cells": [{"cell_type": "code", "execution_count": null, "id": "2a7c419e-2c12-4ecf-bbfd-41924fca1e18", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pathlib import Path\n", "import pyarrow.parquet as pq\n", "import plotly.express as px\n", "import warnings\n", "import plotly.graph_objects as go\n", "import numpy as np\n", "from plotly.subplots import make_subplots\n", "import time\n", "import plotly.io as pio\n", "import io\n", "from PIL import Image\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "import math\n", " \n", "\n", "from ipyvizzu import Data, Config, Style\n", "\n", "from ipyvizzustory import Story, Slide, Step\n"]}, {"cell_type": "code", "execution_count": null, "id": "41b33bb3-308d-44f5-aee7-05e320e14872", "metadata": {}, "outputs": [], "source": ["a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl_Dataset_2023Q1.parquet\")\n", "a.rename(columns={'capacity':'shelfCapacity'},inplace=True)\n", "b = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\JDA_SRD_Tables\\10-01-2024\\as_is_modelDataSet_updated_10-01_old_way\")\n", "\n", "storelist = sorted(list(set(a.store.unique().tolist()).intersection(b.store.unique().tolist())))\n", "\n", "\n", "category_reset_df = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\CategoryReset\\cateres_products.xlsx\"\n", "\n", "cat_df = pd.read_excel(category_reset_df, 'groc')\n", "\n", "repl_types = [\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]\n", "\n", "def data_for_ipyvizzu(a, b, wheighted):\n", "    ind = 2023\n", "    \n", "    df_ce = pd.DataFrame()\n", "    \n", "    for x in [a, b]:\n", "        x = x[x.division == 'Grocery']\n", "        x = x[x.store.isin(storelist)]\n", "        x = x[x.pmg != 'SFB01']\n", "        x['version'] = ind\n", "\n", "        if wheighted:\n", "            for r in repl_types:\n", "                    x[r] = np.where(x[r] > 0, x.sold_units * x[r] , 0)\n", "    \n", "        x = x.groupby(['country', 'division','pmg', 'version'], observed=True)[[\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]].sum().reset_index()\n", "        ind += 1\n", "    \n", "        df_ce = pd.concat([df_ce, x])\n", "    \n", "    # df_ce = df_ce.merge(cat_df, on=[ 'pmg'], how='left')\n", "    \n", "    df_ce_total = df_ce.groupby(['division','version'], observed=True)[[\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]].sum().reset_index()\n", "    df_ce_total['country'] = 'CE'\n", "    \n", "    df_ce = pd.concat([df_ce, df_ce_total])\n", "    \n", "    df_ce_DIV_DESC = df_ce.groupby([ 'country','division','version'], observed=True)[[\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]].sum().reset_index()\n", "    df_ce_DIV_DESC = df_ce_DIV_DESC.melt(id_vars = ['country','division','version'], var_name = 'repl_types')\n", "    df_ce_DIV_DESC['value_%'] = df_ce_DIV_DESC['value'] / df_ce_DIV_DESC.groupby(['country',  'version'], observed=True)['value'].transform('sum')\n", "    \n", "    \n", "    df = df_ce_DIV_DESC.copy()\n", "    df['division'] = df['division'] .astype('string')\n", "    # df.drop(\"division\", axis=1, inplace=True)\n", "    df['value_%'] = df['value_%']*100\n", "    df.rename(columns={'value_%':'value [%]'}, inplace=True)\n", "    df['value [%]'] = df['value [%]'].map(lambda x: round(x, 1))\n", "    \n", "    \n", "    df['country_year'] = df['country'] + '_' + df['version'].astype('str')\n", "    df_to_show = df[(df.division == 'Grocery')]\n", "    \n", "    \n", "    return df_to_show\n", "\n", "\n", "\n", "\n", " \n", "data = Data()\n", "\n", "\n", "wheighted_version = data_for_ipyvizzu(a, b, True)\n", "tpn_number_version = data_for_ipyvizzu(a, b, False)\n", "tpn_number_version.rename(columns={'value [%]': 'tpnb [%]'}, inplace=True)\n", "\n", "both_version_df = wheighted_version.merge(tpn_number_version[['country_year', 'division', 'repl_types', 'tpnb [%]']], on=['country_year','division', 'repl_types'], how='left')\n"]}, {"cell_type": "code", "execution_count": null, "id": "ce2a732a-c666-4359-9047-20242670cb2e", "metadata": {}, "outputs": [], "source": ["pd.options.display.float_format = \"{:,.0f}\".format\n", "tpn_number_version.head()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "78c43b47-8ad7-4d09-99e2-6ceb9dd2ab1b", "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": null, "id": "4313398c-a5a7-4081-9ac3-c46dfb787695", "metadata": {}, "outputs": [], "source": ["cat_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cf9b53fc-db96-4787-a903-73ea9719d241", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cad4a2a4-c428-48d9-ad0a-127960799327", "metadata": {}, "outputs": [], "source": ["a.columns"]}, {"cell_type": "code", "execution_count": null, "id": "18ef46a5-c687-40e7-a17b-965f8105e4d6", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "7fe97271-6497-4539-866d-0030629efa77", "metadata": {}, "outputs": [], "source": ["df_ce_DIV_DESC.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3be4cde9-9694-4687-8653-66123c5581ec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9fcf1ed4-ee90-44ee-8921-60a749454ba4", "metadata": {}, "outputs": [], "source": ["both_version_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "32c38a6a-ae78-46ca-9b92-711db3abc61c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import math\n", " \n", "\n", "from ipyvizzu import Data, Config, Style\n", "\n", "from ipyvizzustory import Story, Slide, Step\n", "\n", " \n", "\n", " \n", "data = Data()\n", "\n", "\n", "\n", "data.add_df(both_version_df)\n", "\n", " \n", "\n", "# Create story object, add data to it\n", "\n", "story = Story(data=data)\n", "\n", "story.set_feature(\"tooltip\", True)\n", "\n", "# Set the size of the HTML element\n", "\n", "# that appears within the notebook\n", "\n", "story.set_size(\"100%\", \"450px\")\n", "\n", " \n", "\n", "\n", "\n", "\n", "slide1 = Slide(\n", "\n", "    Step(\n", "\n", "        Style(\n", "\n", "            {\n", "\n", "                \"legend\": {\n", "\n", "                    \"label\": {\"fontSize\": \"1.1em\"},\n", "\n", "                    \"paddingRight\": \"-1em\",\n", "\n", "                },\n", "\n", "                \"plot\": {\n", "\n", "                    \"marker\": {\"label\": {\"fontSize\": \"1.1em\"}},\n", "\n", "                    \"paddingLeft\": \"10em\",\n", "\n", "                    \"xAxis\": {\n", "\n", "                        \"title\": {\"color\": \"#00000000\"},\n", "\n", "                        \"label\": {\"fontSize\": \"1.1em\"},\n", "\n", "                    },\n", "\n", "                    \"yAxis\": {\"label\": {\"fontSize\": \"1.1em\"}},\n", "\n", "                },\n", "\n", "                \"logo\": {\"width\": \"6em\"},\n", "\n", "                \"fontSize\": \"0.8em\",\n", "\n", "            }\n", "\n", "        ),\n", "\n", "        Config(\n", "\n", "            {\n", "\n", "                \"x\": {\"set\": [\"value [%]\", \"repl_types\"]},\n", "\n", "                \"y\": \"country_year\",\n", "\n", "                \"color\": \"repl_types\",\n", "\n", "                \"label\": \"value [%]\",\n", "\n", "                \"title\": \"Grocery wheighted by sales 2024 vs. 2023 \",\n", "\n", "                \"split\": <PERSON><PERSON><PERSON>,\n", "\n", "            }\n", "\n", "        ),\n", "\n", "    )\n", "\n", ")\n", "\n", "# Create data object, read csv to data frame and add data frame to data object\n", "\n", "\n", "\n", "# Add the slide to the story\n", "\n", "story.add_slide(slide1)\n", "\n", "\n", "slide2 = Slide(\n", "\n", "    Step(\n", "\n", "        Style({\"plot\": {\"xAxis\": {\"label\": {\"color\": \"#00000000\"}}}}),\n", "\n", "        Config(\n", "\n", "            {\n", "\n", "                \"split\": True,\n", "\n", "                \"title\": \"Grocery wheighted by sales 2024 vs. 2023 (Separated)\",\n", "\n", "            }\n", "\n", "        ),\n", "\n", "    )\n", "\n", ")\n", "\n", "story.add_slide(slide2)\n", "slide3 = Slide(\n", "\n", "    Step(\n", "\n", "        Style(\n", "\n", "            {\n", "\n", "                \"legend\": {\n", "\n", "                    \"label\": {\"fontSize\": \"1.1em\"},\n", "\n", "                    \"paddingRight\": \"-1em\",\n", "\n", "                },\n", "\n", "                \"plot\": {\n", "\n", "                    \"marker\": {\"label\": {\"fontSize\": \"1.1em\"}},\n", "\n", "                    \"paddingLeft\": \"10em\",\n", "\n", "                    \"xAxis\": {\n", "\n", "                        \"title\": {\"color\": \"#00000000\"},\n", "\n", "                        \"label\": {\"fontSize\": \"1.1em\"},\n", "\n", "                    },\n", "\n", "                    \"yAxis\": {\"label\": {\"fontSize\": \"1.1em\"}},\n", "\n", "                },\n", "\n", "                \"logo\": {\"width\": \"6em\"},\n", "\n", "                \"fontSize\": \"0.8em\",\n", "\n", "            }\n", "\n", "        ),\n", "\n", "        Config(\n", "\n", "            {\n", "\n", "                \"x\": {\"set\": [\"tpnb [%]\", \"repl_types\"]},\n", "\n", "                \"y\": \"country_year\",\n", "\n", "                \"color\": \"repl_types\",\n", "\n", "                \"label\": \"tpnb [%]\",\n", "\n", "                \"title\": \"Grocery only TPNB numbers 2024 vs. 2023 \",\n", "                \"split\": <PERSON><PERSON><PERSON>,\n", "                #+ \"2023\",\n", "\n", "            }\n", "\n", "        ),\n", "\n", "    )\n", "\n", ")\n", "\n", "story.add_slide(slide3)\n", "\n", "slide4 = Slide(\n", "\n", "    Step(\n", "\n", "        Style({\"plot\": {\"xAxis\": {\"label\": {\"color\": \"#00000000\"}}}}),\n", "\n", "        Config(\n", "\n", "            {\n", "\n", "                \"split\": True,\n", "\n", "                \"title\": \"Grocery only TPNB numbers 2024 vs. 2023 (Separated)\",\n", "\n", "            }\n", "\n", "        ),\n", "\n", "    )\n", "\n", ")\n", "\n", "story.add_slide(slide4)\n", "    \n", "\n", "story.play()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d06c2c05-bea6-4c4f-b08c-0be3b6e22776", "metadata": {}, "outputs": [], "source": ["story.export_to_html(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\Calculations\\2024_01\\23_vs_24_grocery_srp.html\")"]}, {"cell_type": "code", "execution_count": null, "id": "c81cb4de-7eea-43d8-80ed-6455211cc2d2", "metadata": {}, "outputs": [], "source": ["df_ce.head()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}