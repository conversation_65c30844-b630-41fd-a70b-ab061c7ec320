
import pandas as pd
import pyodbc
import numpy as np
from datetime import datetime, timedelta


pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)


#store_infos
store_f = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\Repl\Repl_Stores_Inputs_2025_Q1_v7_GBPratesUpdate (1)_<PERSON><PERSON><PERSON>&others.xlsx"

store_df = pd.read_excel(store_f)


ASCO_fetching = False
MBC_fetching = True


if MBC_fetching:
    
    
    df = pd.DataFrame()
    countries_adress = ["hu", "cz", "sk"]
    
    for c in countries_adress:
    
    
        sql = """
         
                SELECT 
                    CAST(a.site AS INT) AS store,
                    b.bank AS bank_type,
                    a.part_col AS day,
                    a.pon AS tillnumber,
                    FROM_UNIXTIME(
                        FLOOR(UNIX_TIMESTAMP(a.day) / 300) * 300
                    ) AS interval_start,
                    COUNT(DISTINCT a.pon) AS different_tills_per_interval,
                    COUNT(*) AS total_transactions_per_till 
                FROM pos{c}.t010 a
                JOIN pos{c}.t_tillmap b
                ON a.site = b.site
                AND a.pon = b.till
                AND a.part_col = b.part_col
                WHERE a.part_col BETWEEN ******** AND ********
                AND b.bank IN (1, 2, 7)
                GROUP BY CAST(a.site AS INT), b.bank, a.part_col, a.pon, 
                    FROM_UNIXTIME(
                        FLOOR(UNIX_TIMESTAMP(a.day) / 300) * 300
                    )
    
        
        """.format(
            c=c)
    
        with pyodbc.connect(
            "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
        ) as conn:
            
        
            
            df = pd.concat([df, pd.read_sql(sql, conn)])
            print("##################")
            print(f"DONE with {c}")
            print("##################\n")
            
    df.columns = [x.capitalize() for x in df.columns]
    
    df = df.merge(store_df, on=["Store"], how="left")
    
    df = df[~((df['Format'].isin(['Hypermarket', 'Compact'])) & (df['Bank_type'] == 7))]
    
    
    df.to_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Katka_MBC_project\dataset_********_********", compression="gzip")


if ASCO_fetching:
    
    df = pd.DataFrame()
    countries_address = ["hu", "cz", "sk"]
    hu_asco = (27, 28, 29)
    cz_asco = (11, 12, 13)
    sk_asco = (27, 28, 29)
    asco_values = [hu_asco, cz_asco, sk_asco]

    # Function to get the start and end of each month
    def month_range(start_date, end_date):
        current = start_date
        while current <= end_date:
            yield current.strftime("%Y%m%d"), (current.replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            current = (current.replace(day=1) + timedelta(days=32)).replace(day=1)
    
    # Date range 
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 6, 30)

    # Loop through both lists simultaneously
    for c, a in zip(countries_address, asco_values):
        
        for month_start, month_end in month_range(start_date, end_date):
            
            sql = """
                    SELECT 
                        CAST(a.site AS INT) AS store,
                        b.bank AS bank_type,
                        a.part_col AS day,
                        a.pon AS tillnumber,
                        FROM_UNIXTIME(
                            FLOOR(UNIX_TIMESTAMP(a.day) / 300) * 300
                        ) AS interval_start,
                        COUNT(DISTINCT a.pon) AS different_tills_per_interval,
                        COUNT(*) AS total_transactions_per_till 
                    FROM pos{c}.t010 a
                    JOIN pos{c}.t_tillmap b
                    ON a.site = b.site
                    AND a.pon = b.till
                    AND a.part_col = b.part_col
                    WHERE a.part_col BETWEEN {month_start} AND {month_end}
                    AND b.bank IN {a}
                    GROUP BY CAST(a.site AS INT), b.bank, a.part_col, a.pon, 
                        FROM_UNIXTIME(
                            FLOOR(UNIX_TIMESTAMP(a.day) / 300) * 300
                        )
            """.format(
                c=c, a=a, month_start=month_start, month_end=month_end.strftime("%Y%m%d")
            )
    
            with pyodbc.connect(
                "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
            ) as conn:
                
                # Concatenate the data for each month
                
                df = pd.concat([df, pd.read_sql(sql, conn)])
                
                print("##################")
                print(f"DONE with {c} for {month_start} to {month_end.strftime('%Y%m%d')}")
                print("##################\n")
                
        df.columns = [x.capitalize() for x in df.columns]       
        df.to_parquet(fr"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Katka_MBC_project\ASCO_{c}", compression="gzip")
        df = pd.DataFrame()
            
    # df.columns = [x.capitalize() for x in df.columns]
    
    # df = df.merge(store_df, on=["Store"], how="left")
    

    
    





# =============================================================================
# 
# =============================================================================

# df = df[df.Store == 41570]


# =============================================================================
# By Till Usage
# =============================================================================
#total_tills_usage_per_5minutes
df_max_per_day = df[["Store", "Tillnumber", "Day", "Interval_start", "Different_tills_per_interval"]].pivot_table(index=["Store", "Day", "Interval_start"], columns="Tillnumber", values="Different_tills_per_interval", aggfunc="sum", fill_value=0).reset_index()        
columns_to_sum = df_max_per_day.columns[df_max_per_day.columns.get_loc('Interval_start') + 1:]
df_max_per_day['total_tills_usage_per_5minutes'] = df_max_per_day[columns_to_sum].sum(axis=1)


# Total_transactions_per_till
trans_df = df[["Store", "Tillnumber", "Day", "Interval_start", "Total_transactions_per_till"]].pivot_table(index=["Store", "Day", "Interval_start"], columns="Tillnumber", values="Total_transactions_per_till", aggfunc="sum", fill_value=0).reset_index()        
columns_to_sum = trans_df.columns[trans_df.columns.get_loc('Interval_start') + 1:]
trans_df['Total_transactions_per_till'] = trans_df[columns_to_sum].sum(axis=1)

#Merge them together
df_max_per_day_and_trans = df_max_per_day.merge(trans_df[["Store","Day","Interval_start", "Total_transactions_per_till"]], on=["Store","Day","Interval_start"], how="left")


#Max till usage per day
df_max_per_day_daily_max = df_max_per_day.groupby(["Store", "Day"]).agg(total_tills_usage_per_day=("total_tills_usage_per_5minutes","max")).reset_index()



# Function to create a sequential numbering for unique Tillnumber within each store
def rename_tills(group):
    unique_tills = group['Tillnumber'].unique()
    till_mapping = {till: idx + 1 for idx, till in enumerate(sorted(unique_tills))}
    group['nr_of_till_working_at_same_time'] = group['Tillnumber'].map(till_mapping)
    return group

# Apply the function to each group
df = df.groupby('Store').apply(rename_tills)

# Reset the index if needed
df.reset_index(drop=True, inplace=True)

# final Max_total_tills_usage
a = df[["Store", "nr_of_till_working_at_same_time"]].drop_duplicates()
count_dict = df_max_per_day_daily_max['total_tills_usage_per_day'].value_counts().to_dict()
a['Max_total_tills_usage'] = a['nr_of_till_working_at_same_time'].map(count_dict).fillna(0).astype(int)

# final nr_of_5_minutes and nr_of_hours
b = df_max_per_day_and_trans.groupby(["Store","Day","Interval_start"])["total_tills_usage_per_5minutes"].sum().reset_index()
sumif_result = b.groupby('total_tills_usage_per_5minutes')['total_tills_usage_per_5minutes'].sum()
sumif_dict = sumif_result.to_dict()
a['nr_of_5_minutes'] = a['nr_of_till_working_at_same_time'].map(sumif_dict).fillna(0).astype(int)
a['nr_of_hours'] = a['nr_of_5_minutes'] / 12

#final Total_transactions_per_till
c = df_max_per_day_and_trans.groupby(["total_tills_usage_per_5minutes"],as_index=False)["Total_transactions_per_till"].sum()
c.rename(columns={"total_tills_usage_per_5minutes":"nr_of_till_working_at_same_time"}, inplace=True)
a = a.merge(c, on="nr_of_till_working_at_same_time", how="left").replace(np.nan,0)
a["Transactions_per_hours"] = (a["Total_transactions_per_till"] / a["nr_of_hours"]).replace(np.nan,0)
a.drop("Store", axis=1,inplace=True)
a = a.set_index("nr_of_till_working_at_same_time").sort_values(by="nr_of_till_working_at_same_time").T



# =============================================================================
# By Till numbers
# =============================================================================
# Group by 'Tillnumber' and count the occurrences
nr_of_5_minutes = df['Tillnumber'].value_counts().sort_index()

# Convert the count of occurrences to hours
hours = nr_of_5_minutes * (5 / 60)

# Sum the 'Total_transactions_per_till' for each 'Tillnumber'
nr_of_transactions = df.groupby('Tillnumber')['Total_transactions_per_till'].sum()

# Combine into a single DataFrame
summary_df = pd.DataFrame({
    'nr of 5 minutes': nr_of_5_minutes,
    'hours': hours,
    'nr of transaction': nr_of_transactions
}).reset_index().rename(columns={'index': 'Tillnumber'})

summary_df["Transaction_per_hours"] = (summary_df["nr of transaction"] / summary_df["hours"]).replace(np.nan,0)

# Transpose the DataFrame to match the required format
b = summary_df.set_index('Tillnumber').T





        
