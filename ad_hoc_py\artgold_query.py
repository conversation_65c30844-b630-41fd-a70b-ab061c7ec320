# -*- coding: utf-8 -*-
"""
Created on Tue Nov  1 11:28:50 2022

@author: phrubos
"""

import pandas as pd
import numpy as np
from pathlib import Path
import pyodbc


pd.set_option('display.max_columns', None)

conn = pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()



zolinak_1 = """


select *
from (
select distinct a13.CNTR_Code Country,
a13.dmst_store_code Local_Store,
a13.dmst_store_des Local_Store_Name,
coalesce(sal.Section_Gold, trg.Section_Gold, rtc.Section_Gold, los.Section_Gold) Section_Gold,
coalesce(sal.SEC_DES_EN, trg.SEC_DES_EN, rtc.SEC_DES_EN, los.SEC_DES_EN) SEC_DES_EN,
coalesce(sal.Group_Gold, trg.Group_Gold, rtc.Group_Gold, los.Group_Gold) Group_Gold,
coalesce(sal.GRP_DES_EN, trg.GRP_DES_EN, rtc.GRP_DES_EN, los.GRP_DES_EN) GRP_DES_EN,
coalesce(sal.Subgroup_Gold, trg.Subgroup_Gold, rtc.Subgroup_Gold, los.Subgroup_Gold) Subgroup_Gold,
coalesce(sal.SGR_DES_EN, trg.SGR_DES_EN, rtc.SGR_DES_EN, los.SGR_DES_EN) SGR_DES_EN,
coalesce(sal.slad_tpnb, trg.slad_tpnb, rtc.slad_tpnb, los.slad_tpnb) TPN_item,
coalesce(sal.SLAD_LONG_DES, trg.SLAD_LONG_DES, rtc.SLAD_LONG_DES, los.SLAD_LONG_DES) TPN_item_Name,
coalesce(sal.TPNCE_ID, trg.TPNCE_ID, rtc.TPNCE_ID, los.TPNCE_ID) TPNCE_ID,
coalesce(sal.rtc_rc, trg.rtc_rc, rtc.rtc_rc, los.rtc_rc) Local_RC_rtc,
sal.LNASS_CART Carton_size,
nvl(sal.SLSMS_UNIT_CS,0) Sold_units,
nvl(sal.SLSMS_SALEX_CS,0) Sales_excl_VAT,
nvl(sal.SLSMS_SALEX_CS_GBP,0) Sales_excl_VAT_GBP,
nvl(los.RC2_units,0) RC2_units,
nvl(los.RC3_units,0) RC3_units,
nvl(los.RC4_units,0) RC4_units,
nvl(los.RC544_units,0) RC544_units,
nvl(los.RC13_units,0) RC13_units,
nvl(los.RC14_units,0) RC14_units,
nvl(sal.SLSMS_TR_TRNUM,0) Transaction_count,
nvl(trg.SLTRG_TR_UNIT,0) GHS_excl_GIAB_Sold_units,
nvl(trg.SLTRG_TR_TRNUM,0) GHS_excl_GIAB_Transaction_count,
nvl(rtc.SLRTC_RTCUNIT,0) RTC_units
from (
select a11.SLSMS_cntr_id CNTR_ID,
a11.SLSMS_dmst_id dmst_store_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE Section_Gold,
a12.DMAT_SEC_DES_EN SEC_DES_EN,
a12.DMAT_GRP_CODE Group_Gold,
a12.DMAT_GRP_DES_EN GRP_DES_EN,
a12.DMAT_SGR_CODE Subgroup_Gold,
a12.DMAT_SGR_DES_EN SGR_DES_EN,
a13.rtc_rc,
LNASS_CART,
sum(a11.SLSMS_TR_TRNUM) SLSMS_TR_TRNUM,
sum(a11.SLSMS_SALEX_CS) SLSMS_SALEX_CS,
sum(a11.SLSMS_UNIT_CS) SLSMS_UNIT_CS,
sum(a11.SLSMS_SALEX_CS / nvl(DMEXR_RATE,1)) SLSMS_SALEX_CS_GBP
from DW.SL_SMS a11
join dm.dim_time_d tim on a11.part_col = tim.dmtm_d_code
join dm.DIM_ARTGLD_DETAILS a12 on (a11.SLSMS_cntr_id = a12.CNTR_ID and a11.SLSMS_dmat_id = a12.slad_dmat_id)
join dm.rtc_rc a13 on (a11.SLSMS_cntr_id = a13.CNTR_ID)
left join DW.DIM_EXCHANGE_RATES rat on a11.SLSMS_cntr_id = rat.DMEXR_CNTR_ID and rat.DMEXR_CRNCY_TO = 'GBP' and tim.DMTM_FY_CODE = rat.DMEXR_DMTM_FY_CODE
left join (select a12.CNTR_ID, a12.SLAD_DMAT_ID, max(coalesce(cart1.LNASS_CART,cart2.LNASS_CART,cart4.LNASS_CART)) LNASS_CART
from dm.DIM_ARTGLD_DETAILS a12
left join sylncz.ln_art_sup_st cart1 on a12.CNTR_ID = 1 and a12.SLAD_DMAT_ID = cart1.LNASS_DMAT_ID
left join sylnsk.ln_art_sup_st cart2 on a12.CNTR_ID = 2 and a12.SLAD_DMAT_ID = cart2.LNASS_DMAT_ID
left join sylnhu.ln_art_sup_st cart4 on a12.CNTR_ID = 4 and a12.SLAD_DMAT_ID = cart4.LNASS_DMAT_ID
where a12.CNTR_CODE in ('CZ', 'SK', 'HU')
and a12.DMAT_SEC_CODE in ('1101', '1102', '1103', '1104', '1105', '1107')
group by a12.CNTR_ID, a12.SLAD_DMAT_ID
) cart on a12.CNTR_ID = cart.CNTR_ID and a12.SLAD_DMAT_ID = cart.SLAD_DMAT_ID
where (a12.CNTR_CODE in ('CZ', 'SK', 'HU')
and a12.DMAT_SEC_CODE in ('1101', '1102', '1103', '1104', '1105', '1107')
and a11.part_col in ('20230612', '20230613',	'20230614', '20230615', '20230616', '20230617', '20230618', '20230619', '20230620', '20230621', '20230622', '20230623', '20230624', '20230625',	'20230626', '20230627', '20230628', '20230629',	'20230630', '20230701', '20230702', '20230703',	'20230704', '20230705',	'20230706', '20230707',	'20230708',	'20230709', '20230710', '20230711', '20230712', '20230713', '20230714', '20230715', '20230716',	'20230717',	'20230718', '20230719', '20230720',	'20230721', '20230722', '20230723', '20230724', '20230725', '20230726', '20230727', '20230728', '20230729', '20230730')
and a13.rtc_rc in ('RTC') )
group by a11.SLSMS_cntr_id,
a11.SLSMS_dmst_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE,
a12.DMAT_SEC_DES_EN,
a12.DMAT_GRP_CODE,
a12.DMAT_GRP_DES_EN,
a12.DMAT_SGR_CODE,
a12.DMAT_SGR_DES_EN,
a13.rtc_rc,
LNASS_CART
--having sum(a11.SLSMS_UNIT_CS) > 0
) sal
full outer join (
select a11.SLTRG_cntr_id CNTR_ID,
a11.SLTRG_dmst_id dmst_store_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE Section_Gold,
a12.DMAT_SEC_DES_EN SEC_DES_EN,
a12.DMAT_GRP_CODE Group_Gold,
a12.DMAT_GRP_DES_EN GRP_DES_EN,
a12.DMAT_SGR_CODE Subgroup_Gold,
a12.DMAT_SGR_DES_EN SGR_DES_EN,
a13.rtc_rc,
sum(a11.SLTRG_TR_TRNUM) SLTRG_TR_TRNUM,
sum(a11.SLTRG_TR_UNIT) SLTRG_TR_UNIT
from DW.SL_TRG a11
join dm.DIM_ARTGLD_DETAILS a12 on (a11.SLTRG_cntr_id = a12.CNTR_ID and a11.SLTRG_dmat_id = a12.slad_dmat_id)
join dm.rtc_rc a13 on (a11.SLTRG_cntr_id = a13.CNTR_ID)
where (a12.CNTR_CODE in ('CZ', 'SK', 'HU')
and a12.DMAT_SEC_CODE in ('1101', '1102', '1103', '1104', '1105', '1107')
and a11.part_col in ('20230612', '20230613',	'20230614', '20230615', '20230616', '20230617', '20230618', '20230619', '20230620', '20230621', '20230622', '20230623', '20230624', '20230625',	'20230626', '20230627', '20230628', '20230629',	'20230630', '20230701', '20230702', '20230703',	'20230704', '20230705',	'20230706', '20230707',	'20230708',	'20230709', '20230710', '20230711', '20230712', '20230713', '20230714', '20230715', '20230716',	'20230717',	'20230718', '20230719', '20230720',	'20230721', '20230722', '20230723', '20230724', '20230725', '20230726', '20230727', '20230728', '20230729', '20230730')
and a13.rtc_rc in ('RTC')
and a11.SLTRG_TYPE = 'GHS')
group by a11.SLTRG_cntr_id,
a11.SLTRG_dmst_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE,
a12.DMAT_SEC_DES_EN,
a12.DMAT_GRP_CODE,
a12.DMAT_GRP_DES_EN,
a12.DMAT_SGR_CODE,
a12.DMAT_SGR_DES_EN,
a13.rtc_rc
) trg on sal.CNTR_ID = trg.CNTR_ID and sal.dmst_store_id = trg.dmst_store_id and sal.slad_dmat_id = trg.slad_dmat_id and sal.rtc_rc = trg.rtc_rc
full outer join (
select a11.SLRTC_cntr_id CNTR_ID,
a11.SLRTC_dmst_id dmst_store_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE Section_Gold,
a12.DMAT_SEC_DES_EN SEC_DES_EN,
a12.DMAT_GRP_CODE Group_Gold,
a12.DMAT_GRP_DES_EN GRP_DES_EN,
a12.DMAT_SGR_CODE Subgroup_Gold,
a12.DMAT_SGR_DES_EN SGR_DES_EN,
a11.SLRTC_RC rtc_rc,
sum(a11.SLRTC_RTCUNIT) SLRTC_RTCUNIT
from DW.SL_RTC a11
join dm.DIM_ARTGLD_DETAILS a12 on (a11.SLRTC_cntr_id = a12.CNTR_ID and a11.SLRTC_dmat_id = a12.slad_dmat_id)
where (a12.CNTR_CODE in ('CZ', 'SK', 'HU')
and a12.DMAT_SEC_CODE in ('1101', '1102', '1103', '1104', '1105', '1107')
and a11.part_col in ('20230612', '20230613',	'20230614', '20230615', '20230616', '20230617', '20230618', '20230619', '20230620', '20230621', '20230622', '20230623', '20230624', '20230625',	'20230626', '20230627', '20230628', '20230629',	'20230630', '20230701', '20230702', '20230703',	'20230704', '20230705',	'20230706', '20230707',	'20230708',	'20230709', '20230710', '20230711', '20230712', '20230713', '20230714', '20230715', '20230716',	'20230717',	'20230718', '20230719', '20230720',	'20230721', '20230722', '20230723', '20230724', '20230725', '20230726', '20230727', '20230728', '20230729', '20230730')
and a11.SLRTC_RC in ('RTC') )
group by a11.SLRTC_cntr_id,
a11.SLRTC_dmst_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE,
a12.DMAT_SEC_DES_EN,
a12.DMAT_GRP_CODE,
a12.DMAT_GRP_DES_EN,
a12.DMAT_SGR_CODE,
a12.DMAT_SGR_DES_EN,
a11.SLRTC_RC
) rtc on coalesce(sal.CNTR_ID, trg.CNTR_ID) = rtc.CNTR_ID and coalesce(sal.dmst_store_id, trg.dmst_store_id) = rtc.dmst_store_id and coalesce(sal.slad_dmat_id, trg.slad_dmat_id) = rtc.slad_dmat_id and coalesce(sal.rtc_rc, trg.rtc_rc) = rtc.rtc_rc
full outer join (
select coalesce(loss.CNTR_ID, corr.CNTR_ID) CNTR_ID,
coalesce(loss.dmst_store_id, corr.dmst_store_id) dmst_store_id,
coalesce(loss.slad_dmat_id, corr.slad_dmat_id) slad_dmat_id,
coalesce(loss.slad_tpnb, corr.slad_tpnb) slad_tpnb,
coalesce(loss.SLAD_LONG_DES, corr.SLAD_LONG_DES) SLAD_LONG_DES,
coalesce(loss.TPNCE_ID, corr.TPNCE_ID) TPNCE_ID,
coalesce(loss.DMAT_SEC_CODE, corr.DMAT_SEC_CODE) Section_Gold,
coalesce(loss.DMAT_SEC_DES_EN, corr.DMAT_SEC_DES_EN) SEC_DES_EN,
coalesce(loss.DMAT_GRP_CODE, corr.DMAT_GRP_CODE) Group_Gold,
coalesce(loss.DMAT_GRP_DES_EN, corr.DMAT_GRP_DES_EN) GRP_DES_EN,
coalesce(loss.DMAT_SGR_CODE, corr.DMAT_SGR_CODE) Subgroup_Gold,
coalesce(loss.DMAT_SGR_DES_EN, corr.DMAT_SGR_DES_EN) SGR_DES_EN,
coalesce(loss.rtc_rc, corr.rtc_rc) rtc_rc,
nvl(loss.RC544_units,0) + nvl(corr.RC544_units,0) RC544_units,
nvl(loss.RC3_units,0) + nvl(corr.RC3_units,0) RC3_units,
nvl(loss.RC4_units,0) + nvl(corr.RC4_units,0) RC4_units,
nvl(loss.RC14_units,0) + nvl(corr.RC14_units,0) RC14_units,
nvl(loss.RC2_units,0) + nvl(corr.RC2_units,0) RC2_units,
nvl(loss.RC13_units,0) + nvl(corr.RC13_units,0) RC13_units
from (
select a11.LSSLS_cntr_id CNTR_ID,
a11.LSSLS_dmst_id dmst_store_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE,
a12.DMAT_SEC_DES_EN,
a12.DMAT_GRP_CODE,
a12.DMAT_GRP_DES_EN,
a12.DMAT_SGR_CODE,
a12.DMAT_SGR_DES_EN,
a13.rtc_rc,
sum(Case when a11.LSSLS_RCODE = 544 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC544_units,
sum(Case when a11.LSSLS_RCODE = 3 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC3_units,
sum(Case when a11.LSSLS_RCODE = 4 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC4_units,
sum(Case when a11.LSSLS_RCODE = 14 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC14_units,
sum(Case when a11.LSSLS_RCODE = 2 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC2_units,
sum(Case when a11.LSSLS_RCODE = 13 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC13_units
from dw.LS_STOCK_LOSS a11
join dm.dim_time_d tim on a11.part_col = tim.dmtm_d_code
join dm.DIM_ARTGLD_DETAILS a12 on (a11.LSSLS_cntr_id = a12.CNTR_ID and a11.LSSLS_dmat_id = a12.slad_dmat_id)
join dm.rtc_rc a13 on (a11.LSSLS_cntr_id = a13.CNTR_ID)
where (a12.CNTR_CODE in ('CZ', 'SK', 'HU')
and a12.DMAT_SEC_CODE in ('1101', '1102', '1103', '1104', '1105', '1107')
and a11.part_col in ('20230612', '20230613',	'20230614', '20230615', '20230616', '20230617', '20230618', '20230619', '20230620', '20230621', '20230622', '20230623', '20230624', '20230625',	'20230626', '20230627', '20230628', '20230629',	'20230630', '20230701', '20230702', '20230703',	'20230704', '20230705',	'20230706', '20230707',	'20230708',	'20230709', '20230710', '20230711', '20230712', '20230713', '20230714', '20230715', '20230716',	'20230717',	'20230718', '20230719', '20230720',	'20230721', '20230722', '20230723', '20230724', '20230725', '20230726', '20230727', '20230728', '20230729', '20230730')
and a13.rtc_rc in ('RTC') )
group by a11.LSSLS_cntr_id,
a11.LSSLS_dmst_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE,
a12.DMAT_SEC_DES_EN,
a12.DMAT_GRP_CODE,
a12.DMAT_GRP_DES_EN,
a12.DMAT_SGR_CODE,
a12.DMAT_SGR_DES_EN,
a13.rtc_rc ) loss
full outer join (
select a11.LSSLSC_cntr_id CNTR_ID,
a11.LSSLSC_dmst_id dmst_store_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE,
a12.DMAT_SEC_DES_EN,
a12.DMAT_GRP_CODE,
a12.DMAT_GRP_DES_EN,
a12.DMAT_SGR_CODE,
a12.DMAT_SGR_DES_EN,
a13.rtc_rc,
sum(Case when a11.LSSLSC_RCODE = 544 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC544_units,
sum(Case when a11.LSSLSC_RCODE = 3 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC3_units,
sum(Case when a11.LSSLSC_RCODE = 4 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC4_units,
sum(Case when a11.LSSLSC_RCODE = 14 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC14_units,
sum(Case when a11.LSSLSC_RCODE = 2 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC2_units,
sum(Case when a11.LSSLSC_RCODE = 13 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC13_units
from dw.LS_STOCK_LOSS_CORR a11
join dm.dim_time_d tim on a11.LSSLSC_DAY = tim.dmtm_value
join dm.DIM_ARTGLD_DETAILS a12 on (a11.LSSLSC_cntr_id = a12.CNTR_ID and a11.LSSLSC_dmat_id = a12.slad_dmat_id)
join dm.rtc_rc a13 on (a11.LSSLSC_cntr_id = a13.CNTR_ID)
where (a12.CNTR_CODE in ('CZ', 'SK', 'HU')
and a12.DMAT_SEC_CODE in ('1101', '1102', '1103', '1104', '1105', '1107')
and tim.dmtm_d_code in ('20230612', '20230613',	'20230614', '20230615', '20230616', '20230617', '20230618', '20230619', '20230620', '20230621', '20230622', '20230623', '20230624', '20230625',	'20230626', '20230627', '20230628', '20230629',	'20230630', '20230701', '20230702', '20230703',	'20230704', '20230705',	'20230706', '20230707',	'20230708',	'20230709', '20230710', '20230711', '20230712', '20230713', '20230714', '20230715', '20230716',	'20230717',	'20230718', '20230719', '20230720',	'20230721', '20230722', '20230723', '20230724', '20230725', '20230726', '20230727', '20230728', '20230729', '20230730')
and a13.rtc_rc in ('RTC') )
group by a11.LSSLSC_cntr_id,
a11.LSSLSC_dmst_id,
a12.slad_dmat_id,
a12.slad_tpnb,
a12.SLAD_LONG_DES,
a12.TPNCE_ID,
a12.DMAT_SEC_CODE,
a12.DMAT_SEC_DES_EN,
a12.DMAT_GRP_CODE,
a12.DMAT_GRP_DES_EN,
a12.DMAT_SGR_CODE,
a12.DMAT_SGR_DES_EN,
a13.rtc_rc
) corr on loss.CNTR_ID = corr.CNTR_ID and loss.dmst_store_id = corr.dmst_store_id and loss.slad_dmat_id = corr.slad_dmat_id and loss.rtc_rc = corr.rtc_rc
) los on coalesce(sal.CNTR_ID, trg.CNTR_ID, rtc.CNTR_ID) = los.CNTR_ID and coalesce(sal.dmst_store_id, trg.dmst_store_id, rtc.dmst_store_id) = los.dmst_store_id and coalesce(sal.slad_dmat_id, trg.slad_dmat_id, rtc.slad_dmat_id) = los.slad_dmat_id
and coalesce(sal.rtc_rc, trg.rtc_rc, rtc.rtc_rc) = los.rtc_rc
join dm.DIM_STORES a13 on (coalesce(sal.CNTR_ID, trg.CNTR_ID, rtc.CNTR_ID, los.CNTR_ID) = a13.CNTR_ID and coalesce(sal.dmst_store_id, trg.dmst_store_id, rtc.dmst_store_id, los.dmst_store_id) = a13.dmst_store_id)
) x
where Sold_units > 0
;



"""

art_gold = pd.read_sql(zolinak_1, conn)





zolinak_2 = """

select
a2.cntr_code as `Country`
,a2.dmst_store_code as `Local Store`
,a2.dmst_store_des as ``
,a3.dmat_sec_code as `Section (Gold)`
,a3.dmat_sec_des_en as `_1`
,a3.slad_tpn as `TPN item`
,a3.slad_long_des as `_2`
,concat(substr(a1.part_col,1,4),".",substr(a1.part_col,5,2),".",substr(a1.part_col,7,2)) as `Day`
,a1.lssls_rcode as `Local RC stock correction RC stock loss`
from dw.ls_stock_loss a1
join dm.dim_stores a2 on a1.lssls_dmst_id = dmst_store_id and a1.lssls_cntr_id = a2.cntr_id
join dm.dim_artgld_details a3 on a1.lssls_dmat_id = a3.slad_dmat_id and a1.lssls_cntr_id = a3.cntr_id
left join dw.ls_stock_loss_corr a4 on a1.lssls_cntr_id = a4.lsslsc_cntr_id and a1.lssls_dmst_id = a4.lsslsc_dmst_id and a1.lssls_dmat_id = a4.lsslsc_dmat_id and a1.lssls_rcode = a4.lsslsc_rcode and a1.lssls_day = a4.lsslsc_day
where
a1.lssls_rcode in (544) and
a2.cntr_code in ('CZ', 'SK', 'HU') and
a1. part_col between '20230612' and '20230730' and
a3.dmat_sec_code in (1101, 1102, 1103, 1104, 1105, 1107) 
order by a2.dmst_store_code, a1.lssls_rcode, a3.slad_tpn;

"""

art_gold_2 = pd.read_sql(zolinak_2, conn)

# aa = """
# select * from DM.dim_artgld_details
# limit 10

# """
# art_gold = pd.read_sql(aa, conn)

products = pd.read_excel(
        r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_07\sold_unit_2022_wo_CAYG.xlsx")

products = products[['MTPN','CZ', 'SK', 'HU']].melt(id_vars=['MTPN'], value_vars=['CZ', 'SK', 'HU'], var_name='country', value_name='sold_units' ).query("sold_units !=0")

dict_list = (
    products.groupby("country")["MTPN"]
    .apply(lambda s: s.tolist())
    .to_dict()
)



# catres = catres[["country","DIV_ID","DEP_ID", "SEC_ID", "GRP_ID", "SGR_ID"]].melt(id_vars="country")

products=  products.groupby("country")["tpnb"].apply(lambda s: s.tolist()).to_dict()

lista = pd.read_clipboard()
products = tuple(lista.tpn)


products=  lista.groupby("country")["tpn"].apply(lambda s: s.tolist()).to_dict()

tpn = f"{products}"


df2 = pd.DataFrame()
for k, v in products.items():
    
    s = list()
    for x in v:

        s.append(str(x))

    tpn = tuple(s)


    sql = """ SELECT cntr_code AS country, slad_tpnb AS tpnb, slad_tpn AS tpn,
    dmat_div_des_en AS DIV_DESC,
    dmat_div_code as DIV_ID,
    dmat_dep_des_en AS DEP_DESC,
    dmat_dep_code as DEP_ID,
    dmat_sec_des_en AS SEC_DESC,
    dmat_sec_code as SEC_ID,
    dmat_grp_des_en AS GRP_DESC,
    dmat_grp_code as GRP_ID,
    dmat_sgr_des_en AS SGR_DESC,
    dmat_sgr_code as SGR_ID,
    slad_long_des as product_name
            FROM DM.dim_artgld_details mstr

    WHERE slad_tpn in {tpn}
    AND cntr_code = '{k}'
    AND dmat_sgr_des_en <> "Do not use"
    GROUP BY cntr_code, slad_tpnb,slad_tpn,
    dmat_div_des_en,
    dmat_div_code,
    dmat_dep_des_en,
    dmat_dep_code,
    dmat_sec_des_en,
    dmat_sec_code,
    dmat_grp_des_en,
    dmat_grp_code,
    dmat_sgr_des_en,
    dmat_sgr_code,
    slad_long_des
    
    """.format(tpn=tpn, k=k)
    
    art_gold = pd.read_sql(sql, conn)
    df2 = pd.concat([df2, art_gold])
    
    
    
sql="""


select * from dw.sl_stocks stock
limit 10

"""

df = pd.read_sql(sql, conn)


# art_gold = pd.read_sql(sql, conn)


# for x in ['DIV_ID', 'DEP_ID', 'SEC_ID', 'GRP_ID', 'SGR_ID']:
#     b[x] = b[x].apply(str).str.pad(width=4, side='left', fillchar='0')
