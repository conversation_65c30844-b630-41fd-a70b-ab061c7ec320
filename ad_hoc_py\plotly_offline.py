# -*- coding: utf-8 -*-
"""
Created on Wed Nov  9 14:04:55 2022

@author: phrubos
"""

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# pio.renderers.default = 'svg'
pio.renderers.default = "browser"
import pandas as pd

new = pd.read_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\inputs\Repl_Dataset_2022_new_14w_27w_promo_flag.parquet"
)
store_format = new[["store", "Format"]].drop_duplicates()
new = (
    new[new.dep == "DRY"]
    .groupby(["country", "Format"], observed=True)["srp", "nsrp", "full_pallet", "mu"]
    .sum()
    .reset_index()
)
new = new.melt(
    id_vars=["country", "Format"],
    value_vars=["srp", "nsrp", "full_pallet", "mu"],
    var_name="repl_type",
    value_name="total",
)
new_gr = new.groupby(["country", "repl_type"])["total"].sum().reset_index()

old = pd.read_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2021\Model_Datasets\repl_dataset_2022_vol2.parquet"
)
old = old.merge(store_format, on="store", how="left")
old = (
    old[old.dep == "DRY"]
    .groupby(["country", "Format"], observed=True)["srp", "nsrp", "full_pallet", "mu"]
    .sum()
    .reset_index()
)
old = old.melt(
    id_vars=["country", "Format"],
    value_vars=["srp", "nsrp", "full_pallet", "mu"],
    var_name="repl_type",
    value_name="total",
)
old_gr = old.groupby(["country", "repl_type"])["total"].sum().reset_index()


with open("a_graph.html", "w") as f:

    for a, b in zip([old_gr, new_gr], ("2022", "2023")):

        # Create subplots: use 'domain' type for Pie subplot
        fig = make_subplots(
            rows=1,
            cols=3,
            specs=[[{"type": "domain"}, {"type": "domain"}, {"type": "domain"}]],
        )
        fig.add_trace(
            go.Pie(
                labels=a[a.country == "HU"]["repl_type"].tolist(),
                values=a[a.country == "HU"]["total"].tolist(),
                name="HU",
                textinfo="label+percent",
            ),
            1,
            1,
        )
        fig.add_trace(
            go.Pie(
                labels=a[a.country == "CZ"]["repl_type"].tolist(),
                values=a[a.country == "CZ"]["total"].tolist(),
                name="CZ",
                textinfo="label+percent",
            ),
            1,
            2,
        )
        fig.add_trace(
            go.Pie(
                labels=a[a.country == "SK"]["repl_type"].tolist(),
                values=a[a.country == "SK"]["total"].tolist(),
                name="SK",
                textinfo="label+percent",
            ),
            1,
            3,
        )

        # Use `hole` to create a donut-like pie chart
        fig.update_traces(hole=0.4, hoverinfo="label+percent+name")

        fig.update_layout(
            title_text=f"Replenishment Types % {b}",
            # Add annotations in the center of the donut pies.
            annotations=[
                dict(text="HU", x=0.13, y=0.5, font_size=20, showarrow=False),
                dict(text="CZ", x=0.50, y=0.5, font_size=20, showarrow=False),
                dict(text="SK", x=0.87, y=0.5, font_size=20, showarrow=False),
            ],
        )

        f.write(fig.to_html(full_html=False, include_plotlyjs="cdn"))

    # fig.show()

    # fig.write_html(f"{b}_repl_type.html")


for a, b in zip([old_gr], ("2022")):

    # Create subplots: use 'domain' type for Pie subplot
    fig1 = make_subplots(
        rows=1,
        cols=3,
        specs=[[{"type": "domain"}, {"type": "domain"}, {"type": "domain"}]],
    )
    fig1.add_trace(
        go.Pie(
            labels=a[a.country == "HU"]["repl_type"].tolist(),
            values=a[a.country == "HU"]["total"].tolist(),
            name="HU",
            textinfo="label+percent",
        ),
        1,
        1,
    )
    fig1.add_trace(
        go.Pie(
            labels=a[a.country == "CZ"]["repl_type"].tolist(),
            values=a[a.country == "CZ"]["total"].tolist(),
            name="CZ",
            textinfo="label+percent",
        ),
        1,
        2,
    )
    fig1.add_trace(
        go.Pie(
            labels=a[a.country == "SK"]["repl_type"].tolist(),
            values=a[a.country == "SK"]["total"].tolist(),
            name="SK",
            textinfo="label+percent",
        ),
        1,
        3,
    )

    # Use `hole` to create a donut-like pie chart
    fig1.update_traces(hole=0.4, hoverinfo="label+percent+name")

    fig1.update_layout(
        title_text=f"Replenishment Types % {b}",
        # Add annotations in the center of the donut pies.
        annotations=[
            dict(text="HU", x=0.13, y=0.5, font_size=20, showarrow=False),
            dict(text="CZ", x=0.50, y=0.5, font_size=20, showarrow=False),
            dict(text="SK", x=0.87, y=0.5, font_size=20, showarrow=False),
        ],
    )


for a, b in zip([new_gr], ("2023")):

    # Create subplots: use 'domain' type for Pie subplot
    fig2 = make_subplots(
        rows=1,
        cols=3,
        specs=[[{"type": "domain"}, {"type": "domain"}, {"type": "domain"}]],
    )
    fig2.add_trace(
        go.Pie(
            labels=a[a.country == "HU"]["repl_type"].tolist(),
            values=a[a.country == "HU"]["total"].tolist(),
            name="HU",
            textinfo="label+percent",
        ),
        1,
        1,
    )
    fig2.add_trace(
        go.Pie(
            labels=a[a.country == "CZ"]["repl_type"].tolist(),
            values=a[a.country == "CZ"]["total"].tolist(),
            name="CZ",
            textinfo="label+percent",
        ),
        1,
        2,
    )
    fig2.add_trace(
        go.Pie(
            labels=a[a.country == "SK"]["repl_type"].tolist(),
            values=a[a.country == "SK"]["total"].tolist(),
            name="SK",
            textinfo="label+percent",
        ),
        1,
        3,
    )

    # Use `hole` to create a donut-like pie chart
    fig2.update_traces(hole=0.4, hoverinfo="label+percent+name")

    fig2.update_layout(
        title_text=f"Replenishment Types % {b}",
        # Add annotations in the center of the donut pies.
        annotations=[
            dict(text="HU", x=0.13, y=0.5, font_size=20, showarrow=False),
            dict(text="CZ", x=0.50, y=0.5, font_size=20, showarrow=False),
            dict(text="SK", x=0.87, y=0.5, font_size=20, showarrow=False),
        ],
    )


def combine_plotly_figs_to_html(
    plotly_figs, html_fname, include_plotlyjs="cdn", separator=None, auto_open=False
):
    with open(html_fname, "w") as f:
        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))
        for fig in plotly_figs[1:]:
            if separator:
                f.write(separator)
            f.write(fig.to_html(full_html=False, include_plotlyjs=False))

    if auto_open:
        import pathlib, webbrowser

        uri = pathlib.Path(html_fname).absolute().as_uri()
        webbrowser.open(uri)


html_fname = "proba2.html"
plotly_figs = [fig, fig2]
combine_plotly_figs_to_html(
    plotly_figs, html_fname, include_plotlyjs="cdn", separator=None, auto_open=False
)


import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
import numpy as np
import plotly

y1 = np.random.randn(200) - 1
y2 = np.random.randn(200)
y3 = np.random.randn(200) + 1
x = np.linspace(0, 1, 200)

colors = ["#3f3f3f", "#00bfff", "#ff7f00"]

fig = make_subplots(
    rows=3,
    cols=2,
    column_widths=[0.55, 0.45],
    row_heights=[1.0, 1.0, 1.0],
    specs=[
        [{"type": "scatter"}, {"type": "xy"}],
        [{"type": "scatter"}, {"type": "xy", "rowspan": 2}],
        [{"type": "scatter"}, None],
    ],
)


fig.add_trace(
    go.Scatter(
        x=x,
        y=y1,
        hoverinfo="x+y",
        mode="lines",
        line=dict(color="#3f3f3f", width=1),
        showlegend=False,
    ),
    row=1,
    col=1,
)

fig.add_trace(
    go.Scatter(
        x=x,
        y=y2,
        hoverinfo="x+y",
        mode="lines",
        line=dict(color="#00bfff", width=1),
        showlegend=False,
    ),
    row=2,
    col=1,
)

fig.add_trace(
    go.Scatter(
        x=x,
        y=y3,
        hoverinfo="x+y",
        mode="lines",
        line=dict(color="#ff7f00", width=1),
        showlegend=False,
    ),
    row=3,
    col=1,
)


boxfig = go.Figure(
    data=[
        go.Box(x=y1, showlegend=False, notched=True, marker_color="#3f3f3f", name="3"),
        go.Box(x=y2, showlegend=False, notched=True, marker_color="#00bfff", name="2"),
        go.Box(x=y3, showlegend=False, notched=True, marker_color="#ff7f00", name="1"),
    ]
)

for k in range(len(boxfig.data)):
    fig.add_trace(boxfig.data[k], row=1, col=2)

group_labels = ["Group 1", "Group 2", "Group 3"]
hist_data = [y1, y2, y3]

distplfig = ff.create_distplot(
    hist_data, group_labels, colors=colors, bin_size=0.2, show_rug=False
)

for k in range(len(distplfig.data)):
    fig.add_trace(distplfig.data[k], row=2, col=2)
fig.update_layout(barmode="overlay")
plotly.offline.plot(
    fig,
    filename=r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\py\ad_hoc_py\proba.html",
)
