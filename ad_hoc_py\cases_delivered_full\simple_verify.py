#!/usr/bin/env python3
"""
Simple verification script for table existence
"""

import paramiko
import sys
sys.path.append('.')
import delivered

def check_table_exists():
    """Check if the table exists"""
    print('🔍 Checking if table exists...')
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        ssh_client.connect(
            hostname=delivered.hostname, 
            username=delivered.username, 
            password=delivered.password, 
            timeout=30
        )
        print('✅ Connected to server')
        
        # Check if table exists using SHOW TABLES
        cmd = "echo 'SHOW TABLES;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true | grep cases_delivered"
        
        _, stdout, stderr = ssh_client.exec_command(cmd, timeout=60)
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        
        print('📊 Table search output:')
        print(output)
        
        if 'tbl_cases_delivered_productivity' in output:
            print('✅ Table exists!')
        else:
            print('❌ Table not found in output')
            
        ssh_client.close()
        print('🔌 Connection closed')
        
        return True
        
    except Exception as e:
        print(f'❌ Error during verification: {e}')
        return False

if __name__ == "__main__":
    check_table_exists()
