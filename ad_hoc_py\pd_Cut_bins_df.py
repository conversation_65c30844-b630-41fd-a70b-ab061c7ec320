import pandas as pd
import numpy as np

# Create a sample DataFrame
repl_df = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_06\main_drivers_profiles_Repl_WH.xlsx", sheet_name="Repl")
wh_df = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_06\main_drivers_profiles_Repl_WH.xlsx", sheet_name="Warehouse")

# Define the number of bins/groups
num_groups = 5

# Define alphabet labels for the groups
alphabet_labels = ['E', 'D', 'C', 'B', 'A']

repl_columns = ['sold_units',
                'cases_delivered',
                'Waste Items',
                'stock_unit']


wh_columns = ['Cases delivered',
              'Weekly DC cased delivered',
              'Weekly DC deliveries',
              'Weekly DC pallets',
              'Weekly DC rollcages',
              'Weekly deliveries',
              'Weekly direct deliveries']

    

def grouping_on_condition(a, column_list):

    ce = pd.DataFrame()    

    # for country in a.Country.unique().tolist():
    #     a_country = a.query("Country == @country")
        
    for x in a.Division.unique().tolist():
        a_div = a.query("Division == @x")
        for column in a_div[column_list].columns:
            bins = np.linspace(a_div[column].min(), a_div[column].max(), num_groups + 1)
            labels = alphabet_labels[:num_groups]
            a_div[column + '_group'] = pd.cut(a_div[column], bins=bins, labels=labels, include_lowest=True)
    
    
        ce = pd.concat([ce, a_div])
            
    return ce

repl_with_groups = grouping_on_condition(repl_df, repl_columns)
wh_with_groups = grouping_on_condition(wh_df, wh_columns)




with pd.ExcelWriter(
   r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_07\main_drivers_profiles_Repl_WH_CE.xlsx", engine="xlsxwriter"
) as writer:
        
    for df, sheetname in zip([repl_with_groups, wh_with_groups],['Repl', 'Warehouse']):
        df.to_excel(writer, sheet_name=f"{sheetname}", index=False)
        workbook = writer.book
        worksheet = writer.sheets[f"{sheetname}"]
        
        formating_data = workbook.add_format(
            {
                "align": "center_across",
                "valign": "vcenter",
            }
        )
        max_column_size = len(df.columns) - 1

        worksheet.set_column(0, max_column_size, 18, formating_data)
        worksheet.set_zoom(90)
        
        dfs = {sheetname: df}

        for sheetname, df in dfs.items():
            df.to_excel(writer, sheet_name=sheetname, index=False)  # send df to writer
            worksheet = writer.sheets[sheetname]  # pull worksheet object
            for idx, col in enumerate(df):  # loop through all columns
                series = df[col]
                max_len = (
                    max(
                        (
                            series.astype(str).map(len).max(),  # len of largest item
                            len(str(series.name)),  # len of column name/header
                        )
                    )
                    + 1.5
                )  # adding a little extra space
                worksheet.set_column(idx, idx, max_len)  # set column width
        


