import pandas as pd
from pathlib import Path
import pyodbc
import glob
import polars as pl
import sys
import os
import time
import numpy as np
import pyarrow.parquet as pq

from re import search  # searching substring in fullstring
from zipfile import ZipFile
from os import listdir
import os
from os.path import isfile, join

sys.path.append(os.path.dirname(Path.cwd()))
import Replenishment_Model_Functions_23 as rmf
import Get_System_Data_SQL as gsd

import warnings

warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)


time_start = time.time()


######### for SQL queries ##########
saved_filename = "exp_comp_hip_sfm_sfb"  # it will be the dataset name


date_plan = 20230813  # for planogram file
####################################


hiper = [21005, 41970, 11029]
hiper_d = ["'f2023w26'"] * len(hiper)
hiper_s = ["hiper"]* len(hiper)

compact = [21047, 41049, 11122]
compact_d = ["'f2023w22'"] * len(compact)
comp_s = ["compact"]* len(compact)

exp = [24133, 43001, 14036]
exp_d = ["'f2023w12'"] * len(exp)
exp_s = ["express"]* len(exp)

express = [exp, exp_d, exp_s]
comp = [compact, compact_d, comp_s]
hip = [hiper, hiper_d, hiper_s]

nr_weeks = 1


pmg_list = "('BWS', 'DAI','DRY','FRZ','HDL','HEA','PPD','PRO', 'SFM', 'SFB')"



directory = (
    Path(__file__).parent if "__file__" in locals() else Path.cwd()
).parent.parent

place_to_save = Path(directory / f"inputs/files_for_dataset/{saved_filename}")
broken_case = False

as_is_dataset_f = "inputs/as_is_modelDataSet_updated_13-07"
excel_inputs_f = "inputs/Repl/Stores_Inputs_2023_Q1_SFM_SFB.xlsx"
box_op_type_f = "inputs/files_for_dataset/ownbrand_opening_type_2022_mod.xlsx"
broken_cases_f = "inputs/files_for_dataset/broken_cases_list_22w14_27.csv.gz"
catres_path = r"others\CategoryReset\Category Reset CE_hier.xlsx"


stores = [item for sublist in [hiper, compact, exp] for item in sublist]


conn = pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()









item_sold_df = pd.DataFrame()
item_sold_dotcom_df = pd.DataFrame()
stock_df = pd.DataFrame()
cases_delivered_df = pd.DataFrame()
pallet_cap_df = pd.DataFrame()

print("\n###########")
print("Item Sold Downloading for new Products")
print("###########\n")

for d in [express, comp, hip]:
    for x, y in zip(d[0], d[1]):

        item_sold = """
                SELECT 
                cast(stores.dmst_store_code as INT) AS store,
                cal.dtdw_day_desc_en as day,  hier.pmg AS pmg,
                cast(mstr.slad_tpnb as INT) AS tpnb,
                mstr.own_brand as ownbrand,
                mstr.slad_long_des AS product_name,
                mstr.slad_unit AS unit_type,
                mstr.slad_case_size AS case_capacity,
                mstr.slad_net_weight AS weight,
                SUM(sunit.slsms_unit)/{nr_weeks} AS sold_units,
                SUM(sunit.slsms_salex)/{nr_weeks} AS sales_excl_vat 
                FROM dw.sl_sms sunit 
                JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id 
                LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
                JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id
                AND mstr.cntr_id = sunit.slsms_cntr_id 
                JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
                AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
                WHERE cal.dmtm_fw_code = {y} 
                AND cast(stores.dmst_store_code as INT) = {x}
                AND sunit.slsms_unit > 0 
                AND sunit.slsms_salex > 0 
                AND stores.convenience IN ('Convenience', 'HM')
                AND SUBSTRING(hier.pmg, 1, 3) IN {pmg_list}
                GROUP BY  stores.dmst_store_code, cal.dtdw_day_desc_en,  hier.pmg, mstr.slad_tpnb, mstr.own_brand, mstr.slad_long_des, mstr.slad_unit, mstr.slad_case_size, mstr.slad_net_weight
                ORDER BY  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb, cal.dtdw_day_desc_en
                    """.format(
                    x=x,
                    y=y,
                    nr_weeks=nr_weeks, 
                    pmg_list=pmg_list
        )
    
        sold_units = pd.read_sql(item_sold, conn)
        item_sold_df = pd.concat([item_sold_df, sold_units])
        item_sold_df = item_sold_df.drop_duplicates(subset=['store', 'day', 'tpnb'],keep='last')

        print(f"\nItem Sold Done with {x} on week of {y}\n")

print("\n###########")
print("Item Sold DotCom Downloading for new Products")
print("###########\n")
for d in [express, comp, hip]:
    for x, y in zip(d[0], d[1]):
        isold_dotcom = """
                SELECT 
                cast(stores.dmst_store_code as INT) AS store,
                cal.dtdw_day_desc_en as day,
                hier.pmg AS pmg,
                cast(mstr.slad_tpnb as INT) AS tpnb,
                SUM(sunit.sltrg_tr_unit)/{nr_weeks} AS sold_units_dotcom
                FROM dw.sl_trg sunit 
                JOIN dm.dim_stores stores ON stores.cntr_id = sunit.sltrg_cntr_id AND stores.dmst_store_id = sunit.sltrg_dmst_id 
                LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
                JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.sltrg_dmat_id
                AND mstr.cntr_id = sunit.sltrg_cntr_id 
                JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
                AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
                WHERE cal.dmtm_fw_code = {y} 
                AND cast(stores.dmst_store_code as INT) = {x}
                AND sunit.sltrg_tr_unit > 0 
                AND stores.convenience IN ('Convenience', 'HM')
                AND SUBSTRING(hier.pmg, 1, 3) IN {pmg_list}
                GROUP BY  stores.dmst_store_code, cal.dtdw_day_desc_en, hier.pmg,  mstr.slad_tpnb
                ORDER BY  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb, cal.dtdw_day_desc_en
                """.format(
            x=x,
            y=y,
            nr_weeks=nr_weeks, 
            pmg_list=pmg_list
        )
    
        sold_units_dotcom = pd.read_sql(isold_dotcom, conn)
        item_sold_dotcom_df = pd.concat([item_sold_dotcom_df, sold_units_dotcom])
        item_sold_dotcom_df = item_sold_dotcom_df.drop_duplicates(subset=['store', 'day', 'tpnb'],keep='last')
    
        print(f"\nSold Unit Dotcom Done with {x} on week of {y}\n")

print("\n###########")
print("Stock Downloading for new Products")
print("###########\n")
for d in [express, comp, hip]:
    for x, y in zip(d[0], d[1]):

        stock_sql = """
                SELECT 
                CAST(stores.dmst_store_code AS INT) AS store,
                cal.dtdw_day_desc_en as day,
                hier.pmg AS pmg,
                CAST(mstr.slad_tpnb AS INT) AS tpnb,
                SUM(stock.slstks_stock_unit_sl)/{nr_weeks} AS stock,
                AVG(stock.slstks_price) as item_price
                FROM dw.sl_stocks stock
                JOIN dm.dim_stores stores ON stock.slstks_dmst_id = stores.dmst_store_id AND stock.slstks_cntr_id = stores.cntr_id
                JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = stock.slstks_dmat_id AND mstr.cntr_id = stock.slstks_cntr_id
                LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = stock.part_col
                JOIN tesco_analysts.hierarchy_spm hier
                ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0")
                AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0")
                AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0")
                AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0")
                AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
                WHERE cal.dmtm_fw_code = {y} 
                AND cast(stores.dmst_store_code as INT) = {x}
                AND stock.slstks_stock_unit_sl > 0
                AND SUBSTRING(hier.pmg, 1, 3) IN {pmg_list}
                GROUP BY stores.dmst_store_code, hier.pmg, cal.dtdw_day_desc_en, mstr.slad_tpnb
                ORDER BY stores.dmst_store_code, hier.pmg, mstr.slad_tpnb
                    """.format(
                    x=x,
                    y=y,
                    nr_weeks=nr_weeks, 
                    pmg_list=pmg_list
        )
    
        stock = pd.read_sql(stock_sql, conn)
        stock_df = pd.concat([stock_df, stock])
        stock_df = stock_df.drop_duplicates(subset=['store', 'day', 'tpnb'],keep='last')
    
        print(f"\nStock Done with {x} on week of {y}\n")

print("\n###########")
print("Cases Delivered Downloading for new Products")
print("###########\n")
for d in [express, comp, hip]:
    for x, y in zip(d[0], d[1]):

        Cases_delivered = """
                select 
                CAST(CONCAT(cases.int_cntr_id, cases.store) AS INT) as store,
                cal.dtdw_day_desc_en as day,
                CAST(cases.product as INT) as tpnb,
                mstr.slad_long_des AS product_name,
                hier.pmg AS pmg,
                SUM(cases.qty)/{nr_weeks} as unit,
                AVG(mstr.slad_case_size) as artgld_case_capacity
                from stg_go.go_106_order_receiving cases
                LEFT JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = cases.product and mstr.cntr_id = cases.int_cntr_id
                Right JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = lpad(hier.div_code,4,"0")
                AND mstr.dmat_dep_code = lpad(hier.dep_code,4,"0")
                AND mstr.dmat_sec_code = lpad(hier.sec_code,4,"0")
                AND mstr.dmat_grp_code = lpad(hier.grp_code,4,"0")
                AND mstr.dmat_sgr_code = lpad(hier.sgr_code,4,"0")
                LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = cases.part_col
                WHERE cal.dmtm_fw_code = {y}
                AND CAST(CONCAT(cases.int_cntr_id, cases.store) AS INT) = {x}
                AND SUBSTRING(hier.pmg, 1, 3) IN {pmg_list}
                GROUP BY  CONCAT(cases.int_cntr_id, cases.store), cal.dtdw_day_desc_en, cases.product, mstr.slad_long_des, hier.pmg
                ORDER BY  CONCAT(cases.int_cntr_id, cases.store), cal.dtdw_day_desc_en, hier.pmg
                """.format(
                        x=x,
                        y=y,
                        nr_weeks=nr_weeks, 
                        pmg_list=pmg_list
        )
    
        cases = pd.read_sql(Cases_delivered, conn)
        cases_delivered_df = pd.concat([cases_delivered_df, cases])
        cases_delivered_df = cases_delivered_df.drop_duplicates(subset=['store', 'day', 'tpnb'],keep='last')
    
        print(f"\nCases Done with {x} on week of {y}\n")

srd_p3 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\16-05-2023\CE_JDA_SRD_for_model"
srd_p5 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\13-07-2023\CE_JDA_SRD_for_model"
srd_p6 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\14-08-2023\CE_JDA_SRD_for_model"

SRD_tables = pd.DataFrame()
for x, y in zip([exp, compact, hiper], [srd_p3, srd_p5, srd_p6]):
    
    df = pq.read_table(y,filters=[("store", "in", x)]).to_pandas()
    SRD_tables = pd.concat([SRD_tables, df])
    
opsdev_df = SRD_tables.copy()
    
        
            
            
pallet_cap_df = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\pallet_capacity_CE_0831.parquet")


store_inputs = rmf.Store_Inputs_Creator(directory, excel_inputs_f, [item for sublist in [exp, compact, hiper] for item in sublist])

print("Planogram compiling has been started....")


plano_ce = pd.DataFrame()
for date_plan, stores in zip([20230823, 20230726, 20230518], [hiper, compact, exp]):


########################################### Collecting data PART I ############################################################
    def check_closest_of_date(date_plan):
    
        list_date = []
        list_min_date = []
        countries = ["HU", "SK", "CZ"]
    
        for c in countries:
            for a in listdir("//PVCKBSQLDB001{c}.global.tesco.org/BACKUP/".format(c=c)):
                if a[:8].isdigit():
    
                    integer = int(a[:8])
                    list_date.append(integer)
    
            list_min_date.append(min(list_date, key=lambda x: abs(x - date_plan)))
    
            return list_min_date[0]
    
    date = check_closest_of_date(date_plan)
    countries = ["HU", "SK", "CZ"]
    
    for c in countries:
        # Create a ZipFile Object and load sample.zip in it
        with ZipFile(
            "//PVCKBSQLDB001{c}.global.tesco.org/BACKUP/{date}.7z".format(
                date=date, c=c
            ),
            "r",
        ) as zipObj:
            # Get a list of all archived file names from the zip
            listOfFileNames = zipObj.namelist()
            # Iterate over the file names
            for fileName in listOfFileNames:
                # Check filename endswith csv
                if search(
                    "{date}/FutureDatedShelfCap_All_Stores_{date}".format(date=date),
                    fileName,
                ) and fileName.endswith(".txt"):
    
                    # Extract a single file from zip
    
                    zipObj.extract(fileName, place_to_save / "plano")
                    old_file = os.path.join(
                        place_to_save / "plano" / f"{date}", os.path.basename(fileName)
                    )
                    new_file = os.path.join(
                        place_to_save / "plano",
                        f"FutureDatedShelfCap_All_Stores_{date}" + f"_{c}" + ".txt",
                    )
                    os.rename(old_file, new_file)
    
                    print(new_file[-46:])
    
    ########################################### Compiling data to one dataset PART II ############################################################
    def planogram_total(df_outer, cntr_code, cntr_id, stores):
        df = df_outer.copy()
        to_drop = ["DRG ID", "DRG name", "DRC ID", "DRC name", "Hyper DRG", "Supplier"]
        for d in to_drop:
            try:
                df = df.drop([d], axis=1)
            except:
                pass
    
        df = df.rename(
            index=str,
            columns={
                "Product TPN": "tpnb",
                "EAN code": "ean",
                "Product name": "pname",
                "Product status": "status",
                "Total Case Pack Size": "icase",
            },
        )
        df = pd.melt(
            df,
            id_vars=["tpnb", "ean", "pname", "status", "icase"],
            var_name="store",
            value_name="capacity",
        )
        df = df.replace(np.nan, 0)
        df["country"] = cntr_id
        df["store"] = str(cntr_code) + df["store"].str[-4:]
        df["tpnb"] = df["tpnb"].astype(str)
        df["ean"] = df["ean"].astype(str)
        df["icase"] = df["icase"].astype(int)
        df["store"] = df["store"].astype(int)
        df["capacity"] = df["capacity"].astype(int)
        # df = df[['country','tpnb', 'icase']].drop_duplicates()
        # df = df.loc[df.tpnb.str.isnumeric()]
        df = df[df.store.isin(stores)]
        return df
    
    # create a list with files in choosen folder
    onlyfiles = (
        f
        for f in listdir(place_to_save / "plano")
        if (
            isfile(join(place_to_save / "plano", f))
            and search("txt", f.lower())
            and search(f"{date}", f)
        )
    )
    
    files_list = [file for file in onlyfiles]
    planogram = pd.DataFrame()
    
    i = 1
    for index, filename in enumerate(files_list):
        df = pd.read_csv(
            join(place_to_save / "plano", filename),
            sep="|",
            encoding="unicode_escape",
            low_memory=False,
        )
        if search("CZ", filename):
            df = planogram_total(df, 1, "CZ", stores)
    
        elif search("SK", filename):
            df = planogram_total(df, 2, "SK", stores)
    
        else:
            df = planogram_total(df, 4, "HU", stores)
        print(filename + " planogram created")
    
        planogram = pd.concat([planogram, df], ignore_index=True)
        print(str(i) + " files appended")
        i += 1
    planogram = planogram[planogram["status"].str.contains("Live") == True]
    planogram = (
        (planogram[planogram.capacity > 0])
        .sort_values(by=["store"])
        .reset_index(drop=True)
    )
    
    
    planogram["pname"] = planogram["pname"]
    planogram["status"] = planogram["status"]
    planogram["country"] = planogram["country"]
    
    plano_ce = pd.concat([planogram, plano_ce])
    plano_ce = plano_ce.drop_duplicates()
    
    
    for c in countries:
        os.remove(
            os.path.join(
                place_to_save / "plano",
                f"FutureDatedShelfCap_All_Stores_{date}" + f"_{c}" + ".txt",
            )
        )
    os.rmdir(place_to_save / f"plano/{date}")
    
    print(f"planogram done with {stores}")
    





planogram_df = plano_ce.copy()


with pl.StringCache():
    print("\n###########")
    print("Repl_Dataset Build: has been started to calculate...")
    print("###########\n")

    #Creating Base for Repl_Dataset
    weekdays = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
    ]
    store_inputs_lower = store_inputs.copy()
    store_inputs_lower.columns = [i.lower() for i in store_inputs_lower.columns]
    store_inputs_lower = store_inputs_lower.rename(columns={"pmg name": "pmg_name"})

    store_inputs_lower = store_inputs_lower[
        ["store", "country", "format", "division", "pmg", "dep", "is_capping_shelf"]
    ].drop_duplicates()
    store_inputs_lower = pl.from_pandas(store_inputs_lower)

    # planogram = pl.read_csv(directory / planogram_f, sep=',', ignore_errors=True).lazy()
    planogram = pl.from_pandas(planogram_df)
    planogram = planogram.select(["store", "tpnb", "icase", "capacity"])
    planogram = planogram.unique(subset=["tpnb", "icase", "store", "capacity"])
    planogram = planogram.with_columns(
        [pl.col("tpnb").cast(pl.Int64), pl.col("store").cast(pl.Int64)]
    )

    # opsdev = pl.read_parquet(directory / ops_dev_f).lazy()
    opsdev = pl.from_pandas(opsdev_df).with_columns(pl.col('tpnb').cast(pl.Int64),
                                                 pl.col('store').cast(pl.Int64))
    opsdev = opsdev.with_columns(pl.lit(0).alias('frozen_srp')) # once there is system data it should be deleted
    opsdev = opsdev.with_columns(pl.lit(0).alias('single_pick')) # once there is system data it should be deleted

    opsdev = opsdev.select(
        [
            "store",
            "tpnb",
            "srp",
            "nsrp",
            "mu",
            "full_pallet",
            "split_pallet",
            "frozen_srp",
            "single_pick",
            "checkout_stand_flag",
            "clipstrip_flag",
            "backroom_flag",
        ]
    )

    # stock = pl.read_parquet(directory / stock_f).lazy()
    stock = pl.from_pandas(stock_df)
    stock = stock.select(["store", "day", "tpnb", "stock", "item_price"])

    # isold = pl.read_parquet(directory / items_sold_f).lazy()
    isold = pl.from_pandas(item_sold_df)
    # isold_dotcom = pl.read_parquet(directory / items_sold_dotcom).lazy()
    isold_dotcom = pl.from_pandas(item_sold_dotcom_df)
    isold_dotcom = isold_dotcom.select(["store", "day", "tpnb", "sold_units_dotcom"])

    # cases = pl.read_parquet(directory / cases_f).lazy()
    cases_delivered_df = cases_delivered_df[
        cases_delivered_df.artgld_case_capacity.notnull()
    ]
    cases = pl.from_pandas(cases_delivered_df)
    cases = cases.select(["store", "day", "tpnb", "unit", "artgld_case_capacity"])
    cases = cases.with_columns(
        [(pl.col("unit") / pl.col("artgld_case_capacity")).alias("cases_delivered")]
    )

    op_type = pl.read_excel(directory / box_op_type_f)
    # pallet_cap = pl.read_parquet(directory / pallet_capacity_f)
    pallet_cap = pl.from_pandas(pallet_cap_df)

    print("Repl_Dataset Build: Inputs are loaded into Memory!")

    Repl_Dataset = isold.select(["store", "pmg", "tpnb"]).unique()
    Repl_Dataset = Repl_Dataset.with_columns([pl.lit(None).alias("day")])
    Repl_Dataset = Repl_Dataset.with_columns(
        [pl.col("day").map(lambda s: weekdays).alias("day")]
    )
    Repl_Dataset = Repl_Dataset.explode("day")
    Repl_Dataset = Repl_Dataset.unique()
    Repl_Dataset = Repl_Dataset.join(
        isold.select(["store", "pmg", "tpnb", "day", "sold_units", "sales_excl_vat"]),
        on=["store", "pmg", "tpnb", "day"],
        how="left",
    )
    Repl_Dataset = Repl_Dataset.join(
        isold.select(
            [
                pl.all().exclude(
                    ["sold_units", "sales_excl_vat", "day", "__index_level_0__"]
                )
            ]
        ).unique(),
        on=["store", "pmg", "tpnb"],
        how="left",
    )
    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when(pl.col("pmg") == "DRY18")
            .then("DRY15")
            .otherwise(pl.col("pmg"))
            .alias("pmg")
        ]
    )
    Repl_Dataset = Repl_Dataset.with_columns([pl.col("day").cast(pl.Utf8)])
    Repl_Dataset = Repl_Dataset.join(
        store_inputs_lower, on=["store", "pmg"], how="outer"
    )
    Repl_Dataset = Repl_Dataset.join(
        isold_dotcom, on=["store", "day", "tpnb"], how="left"
    )
    Repl_Dataset = Repl_Dataset.join(stock, on=["store", "day", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.join(opsdev, on=["tpnb", "store"], how="left")
    Repl_Dataset = Repl_Dataset.join(planogram, on=["store", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.join(cases, on=["store", "day", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.join(op_type, on=["country", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.join(pallet_cap, on=["country", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.fill_null(0)

    Repl_Dataset = (
        Repl_Dataset.with_columns(
            [
                pl.when(
                    (pl.col("srp") == 0)
                    & (pl.col("nsrp") == 0)
                    & (pl.col("full_pallet") == 0)
                    & (pl.col("mu") == 0)
                    & (pl.col("split_pallet") == 0)
                    & (pl.col("frozen_srp") == 0)
                    & (pl.col("single_pick") == 0)
                )
                .then(1)
                .otherwise(pl.col("nsrp"))
                .alias("nsrp"),
                pl.when(pl.col("icase") == 0)
                .then(pl.col("case_capacity"))
                .otherwise(pl.col("icase"))
                .alias("icase"),
            ]
        )
        .drop("case_capacity")
        .rename({"icase": "case_capacity"})
    )

    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when(pl.col("case_capacity") < pl.col("artgld_case_capacity"))
            .then(pl.col("artgld_case_capacity"))
            .otherwise(pl.col("case_capacity"))
            .alias("case_capacity")
        ]
    )

    Repl_Dataset = (
        Repl_Dataset.with_columns(
            [(pl.col("unit") / pl.col("case_capacity")).alias("cases_delivered")]
        )
        .drop("artgld_case_capacity")
        .fill_null(0)
    )

    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when(pl.col("unit_type") != "KG")
            .then("SNGL")
            .otherwise(pl.col("unit_type"))
            .alias("unit_type")
        ]
    )

    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when(pl.col("dep") == "NEW")
            .then("HDL")
            .otherwise(pl.col("dep"))
            .alias("dep")
        ]
    )
    Repl_Dataset = Repl_Dataset.with_columns(
        [pl.col("pallet_capacity").round(0).alias("pallet_capacity")]
    )

    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when((pl.col("srp") > 0) & (pl.col("pmg").str.contains("FRZ")))
            .then(1)
            .otherwise(pl.col("nsrp"))
            .alias("nsrp"),
            pl.when((pl.col("srp") > 0) & (pl.col("pmg").str.contains("FRZ")))
            .then(0)
            .otherwise(pl.col("srp"))
            .alias("srp"),
        ]
    )

    if broken_case == False:

        broken_cases_list = pl.read_csv(directory / broken_cases_f)
        Repl_Dataset = Repl_Dataset.join(
            broken_cases_list, on=["store", "day", "tpnb"], how="left"
        ).fill_null(0)

    Repl_Dataset = Repl_Dataset.to_pandas()
    int_list = [
        "srp",
        "nsrp",
        "mu",
        "full_pallet",
        "split_pallet",
        "frozen_srp",
        "single_pick",
        "checkout_stand_flag",
        "backroom_flag",
        "clipstrip_flag",
        "case_capacity",
        "is_capping_shelf",
        "capacity",
    ]  #
    Repl_Dataset[int_list] = Repl_Dataset[int_list].apply(lambda x: x.astype("int32"))
    int_part = rmf.optimize_types(Repl_Dataset.select_dtypes(include=["float", "int"]))
    cat_part = Repl_Dataset.select_dtypes(exclude=["float", "int"])
    Repl_Dataset = pd.concat([int_part, cat_part], axis=1)
    Repl_Dataset['opening_type'] = Repl_Dataset['opening_type'].replace({np.nan : 'not Ownbrand'})
    
    Repl_Dataset = Repl_Dataset[Repl_Dataset.tpnb != 0]
    Repl_Dataset['cases_delivered'] = Repl_Dataset['cases_delivered'].replace(np.nan, 0)
    
    Repl_Dataset = rmf.optimize_objects(rmf.optimize_types(Repl_Dataset))



# losses parts
waste_rtc = pd.DataFrame()
for d in [express, comp, hip]:
    
    f = d[2][0]
    y = d[1][0]
    

    print("Losses_SQL has been started")


    conn = pyodbc.connect(
        "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
    )
    cursor = conn.cursor()

    del_table = """drop table if exists tesco_analysts.{saved_name}""".format(
        saved_name=f
    )
    cursor.execute(del_table)
    conn.commit()

    loss_part_1 = """CREATE TABLE tesco_analysts.{saved_name}
    
    (
     store INT, day varchar(255), tpnb INT,
    product_name varchar(255), pmg varchar(255),
     code INT, amount FLOAT
     )
    
    """.format(

        saved_name=f,

    )  #

    cursor.execute(loss_part_1)
    conn.commit()

    print(f"Table is created {f}")

    loss_part_2 = """
        INSERT INTO tesco_analysts.{saved_name}
        SELECT CAST(stores.dmst_store_code as INT) AS store, cal.dtdw_day_desc_en as day,  CAST(tpns.slad_tpnb as INT) AS tpnb,
        tpns.slad_long_des AS product_name, hier.pmg AS pmg, 1 AS code, SUM(a.slrtc_rtcunit)/{nr_weeks} AS amount 
        FROM dw.sl_rtc a 
        JOIN dm.dim_stores stores ON stores.dmst_store_id = a.slrtc_dmst_id 
        AND stores.cntr_id = a.slrtc_cntr_id 
        JOIN dm.dim_artgld_details tpns ON tpns.slad_dmat_id = a.slrtc_dmat_id 
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
        JOIN tesco_analysts.hierarchy_spm hier ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0") 
        AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
        AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
        AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
        AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
        WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
        AND a.slrtc_rc = 'RTC' 
        AND SUBSTRING(hier.pmg, 1, 3) IN {pmg} 
        AND stores.dmst_store_code in {stores}
        GROUP BY stores.dmst_store_code, cal.dtdw_day_desc_en, tpns.slad_tpnb, tpns.slad_long_des, hier.pmg""".format(
        start=y,
        end=y,
        pmg=pmg_list,
        nr_weeks=nr_weeks,
        saved_name=f,
        stores=tuple(d[0]),
    )
    cursor.execute(loss_part_2)
    conn.commit()
    print(f"RTC part is Done with {f}!")

    loss_part_3 = """
        INSERT INTO tesco_analysts.{saved_name}
        
        SELECT CAST(stores.dmst_store_code as INT) AS store, cal.dtdw_day_desc_en as day, CAST(tpns.slad_tpnb as INT) AS tpnb,
        tpns.slad_long_des AS product_name, hier.pmg AS pmg,
        CAST(a.lssls_rcode as INT) AS code, SUM(a.lssls_quantity_adj)/{nr_weeks} AS amount 
        FROM dw.ls_stock_loss a
        JOIN dm.dim_stock_loss_rc b ON a.lssls_cntr_id = b.cntr_id
        AND a.lssls_rcode = b.lsrc_code 
        JOIN dm.dim_stores stores ON stores.dmst_store_id = a.lssls_dmst_id 
        AND stores.cntr_id = a.lssls_cntr_id 
        JOIN dm.dim_artgld_details tpns ON tpns.slad_dmat_id = a.lssls_dmat_id 
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
        JOIN tesco_analysts.hierarchy_spm hier ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0") 
        AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
        AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
        AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
        AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
        WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
        AND a.lssls_rcode in ('3', '4', '544','14','104') 
        AND SUBSTRING(hier.pmg, 1, 3) IN  {pmg}
        AND stores.dmst_store_code in {stores}

        GROUP BY stores.dmst_store_code, cal.dtdw_day_desc_en, tpns.slad_tpnb, tpns.slad_long_des, hier.pmg, a.lssls_rcode """.format(
        start=y,
        end=y,
        pmg=pmg_list,
        nr_weeks=nr_weeks,
        saved_name=f,
        stores=tuple(d[0]),
    )  #

    cursor.execute(loss_part_3)
    conn.commit()
    
    print(f"stock_loss part is Done with {f}!")
    
    
    loss_part_4 = """SELECT * FROM tesco_analysts.{saved_name}""".format(
        saved_name=f
    )
    waste_rtc_ = pd.read_sql(loss_part_4, conn)
    waste_rtc = pd.concat([waste_rtc_,waste_rtc])

    del_table = """drop table if exists tesco_analysts.{saved_name}""".format(
        saved_name=f
    )
    cursor.execute(del_table)
    conn.commit()

        
        

conn.close()
waste_rtc.rename(
    columns={
        col: col.replace(f"{f}" + ".", "") for col in waste_rtc.columns
    },
    inplace=True,
)
waste_rtc.replace(np.nan, 0, inplace=True)

waste_rtc["amount"] = waste_rtc.amount.abs()
waste_rtc["dep"] = waste_rtc.pmg.astype(str).str[:3]
waste_rtc = waste_rtc.sort_values(
    "amount", ascending=False
)  # removal strange 3 records which amount is much higher than in the other stores
waste_rtc.drop(waste_rtc.index[:3], inplace=True)
waste_rtc["decimal"] = (
    (waste_rtc.amount.sub(waste_rtc.amount.astype(int))).mul(1000).astype(int)
)  # Unit type
waste_rtc["unit_type"] = np.where((waste_rtc["decimal"] > 0), "KG", "SNGL")
waste_rtc.drop(["decimal"], axis=1, inplace=True)
waste_rtc["code"] = waste_rtc.code.astype(int)
waste_rtc["RTC Lines"] = np.where(waste_rtc.code == 1, 1, 0)
waste_rtc["Waste Lines"] = np.where(
    ((waste_rtc.code == 3) | (waste_rtc.code == 4)), 1, 0
)
waste_rtc["Food Donation Lines"] = np.where(
    (waste_rtc.code == 544) & (waste_rtc.unit_type != "KG"), 1, 0
)  # just singles because I use it for scanning process
waste_rtc["RTC Items"] = np.where((waste_rtc.code == 1), waste_rtc["amount"], 0)
waste_rtc["Waste Items"] = np.where(
    (
        ((waste_rtc.code == 3) | (waste_rtc.code == 4))
        & (waste_rtc.unit_type != "KG")
        & (waste_rtc.dep == "PRO")
    ),
    waste_rtc["amount"],
    0,
)
waste_rtc["Waste Items"] = np.where(
    (((waste_rtc.code == 3) | (waste_rtc.code == 4)) & (waste_rtc.dep != "PRO")),
    waste_rtc["amount"] + waste_rtc["Waste Items"],
    waste_rtc["Waste Items"],
)
waste_rtc["Food Donation Items"] = np.where(
    (
        (waste_rtc.code == 544)
        & (waste_rtc.unit_type != "KG")
        & (waste_rtc.dep == "PRO")
    ),
    waste_rtc["amount"],
    0,
)
waste_rtc["Food Donation Items"] = np.where(
    ((waste_rtc.code == 544) & (waste_rtc.dep != "PRO")),
    waste_rtc["amount"] + waste_rtc["Food Donation Items"],
    waste_rtc["Food Donation Items"],
)
waste_rtc["Waste Bulk (one bag)"] = np.where(
    (
        ((waste_rtc.code == 3) | (waste_rtc.code == 4))
        & (waste_rtc.unit_type == "KG")
        & (waste_rtc.dep == "PRO")
    ),
    waste_rtc["amount"] / 0.9,
    0,
)  # 0.9 identifies how many part of the product you have to deal with
waste_rtc["Food Donation Bulk (one bag)"] = np.where(
    (
        (waste_rtc.code == 544)
        & (waste_rtc.unit_type == "KG")
        & (waste_rtc.dep == "PRO")
    ),
    waste_rtc["amount"],
    0,
)
condition = [
    waste_rtc["store"].astype(str).str.match("^1"),
    waste_rtc["store"].astype(str).str.match("^2"),
    waste_rtc["store"].astype(str).str.match("^4"),
]
results = ["CZ", "SK", "HU"]
waste_rtc["country"] = np.select(condition, results, 0)

"""
  In here we have a change related with yelow labeled bags. In October 2020 I have calculated that 67% of code 544
  will not be send anymore, but it will be labeled and replenished like RTC items.
  The change is called: "Yellow Labeled Bags"
  I have added 2 Drivers: RTC (Produce Bags), RTC (Produce Items)

  Chosen PMGs:
  PRO01 Bananas 
  PRO02 Apples, Peaches, Tomatoes and Peppers 
  PRO03 Citrus 
  PRO04 Melones 
  PRO05 Exotic Fruit and Vegetable 
  PRO06 Organic PRO 
  PRO07 Berries and Grapes 
  PRO08 Potatoes and Onions 
  PRO09 Root Vegetable 
"""
chosen_pmgs = [
    "PRO01",
    "PRO02",
    "PRO03",
    "PRO04",
    "PRO05",
    "PRO06",
    "PRO07",
    "PRO08",
    "PRO09",
]
yell_bag_ratio = 1  # Initial percentega was 0.67 but we use looses for P8 2021 when the process was already implemented so we do not need to decrease anything
yell_bag_capacity = 0.4
yell_bag_capacity_sm = 0.9

waste_rtc["RTC (Produce Bags)"] = np.where(
    (
        (waste_rtc["pmg"].isin(chosen_pmgs))
        & (waste_rtc.country == "HU")
        & (waste_rtc.unit_type == "KG")
    ),
    (waste_rtc["RTC Items"] * yell_bag_ratio) / yell_bag_capacity,
    0,
)
waste_rtc["RTC (Produce Bags)"] = np.where(
    (
        (waste_rtc["pmg"].isin(chosen_pmgs))
        & (waste_rtc.country == "HU")
        & (waste_rtc.unit_type == "KG")
        & (
            (waste_rtc.store.astype(str).str.match("^43"))
            | (waste_rtc.store.astype(str).str.match("^44"))
        )
    ),
    (waste_rtc["RTC Items"] * yell_bag_ratio) / yell_bag_capacity_sm,
    0,
)
waste_rtc["RTC Items"] = np.where(
    (
        (waste_rtc["pmg"].isin(chosen_pmgs))
        & (waste_rtc.country == "HU")
        & (waste_rtc.unit_type == "KG")
    ),
    waste_rtc["RTC Items"] * (1 - yell_bag_ratio),
    waste_rtc["RTC Items"],
)  # decreasing RTC Items once we have yell_bag_ratio transfered to yellow labeled bags:

# groupby
waste_rtc = (
    waste_rtc.groupby(["country", "store", "day", "tpnb", "pmg", "dep"])[[
        "RTC Lines",
        "Waste Lines",
        "Food Donation Lines",
        "RTC Items",
        "Waste Items",
        "Food Donation Items",
        "Waste Bulk (one bag)",
        "Food Donation Bulk (one bag)",
        "RTC (Produce Bags)",
    ]]
    .sum()
    .reset_index()
)

waste_rtc["Food Donation (available)"] = np.where(
    waste_rtc["RTC (Produce Bags)"] > 0, 1, 0
)
waste_rtc["RTC Lines"] = np.where(waste_rtc["RTC Lines"] > 0, 1, 0)
# waste_rtc['RTC Lines'] = 1 / waste_rtc.groupby(['store', 'tpnb'])['RTC Lines'].transform('sum')
# waste_rtc['RTC Lines'] = waste_rtc['RTC Lines'].replace(np.nan, 0).replace([np.inf, -np.inf], 0)

waste_rtc["Waste Lines"] = np.where(waste_rtc["Waste Lines"] > 0, 1, 0)
# waste_rtc['Waste Lines'] = 1 / waste_rtc.groupby(['store', 'tpnb'])['Waste Lines'].transform('sum')
# waste_rtc['Waste Lines'] = waste_rtc['Waste Lines'].replace(np.nan, 0).replace([np.inf, -np.inf], 0)

waste_rtc["Food Donation Lines"] = np.where(
    waste_rtc["Food Donation Lines"] > 0, 1, 0
)
# waste_rtc['Food Donation Lines'] = 1 / waste_rtc.groupby(['store', 'tpnb'])['Food Donation Lines'].transform('sum')
# waste_rtc['Food Donation Lines'] =  waste_rtc['Food Donation Lines'].replace(np.nan, 0).replace([np.inf, -np.inf], 0)

waste_rtc.to_parquet(
    place_to_save / f"losses_{'_'.join(set(x[2][0] for x in [express, comp, hip]))}.parquet.gz", compression="gzip"
)

print("Losses_SQL Done")




time_stop = time.time()
print("\n###########")
print(
    "Measured Stores Volumes are done - Executed Time: (sec): {:,.2f} ".format(
        time_stop - time_start
    )
)
print("###########\n")
