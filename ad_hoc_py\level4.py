# -*- coding: utf-8 -*-
"""
Created on Tue May  6 10:02:35 2025

@author: phrubos
"""

numbers = [
    112120811, 113130111, 113130112, 113130130, 113130131, 224240711, 
    224240713, 224240811, 224240911, 225250212, 225250312, 225250313, 
    225250314, 225250315, 225250316, 225250317, 225250318, 225250319, 
    225250320, 225250321, 225250322, 225250399, 225250412, 225250511, 
    225250512, 226260112, 226260113, 226260114, 226260116, 226260117
]



# Convert each number to string
numbers_as_strings = [str(num) for num in numbers]



# Assuming "a" is your DataFrame with columns DIV_ID, DEP_ID, SEC_ID, GRP_ID, SGR_ID
# This creates a new column called "combined" by concatenating the specified columns

# Convert all columns to string type first (in case they're numeric)
columns_to_combine = ['DIV_ID', 'DEP_ID', 'SEC_ID', 'GRP_ID']
for col in columns_to_combine:
    a[col] = a[col].astype(str)

# Concatenate the columns
a['level4'] = a['DIV_ID'] + a['DEP_ID'] + a['SEC_ID'] + a['GRP_ID']