import pandas as pd
import pyodbc
import numpy as np



conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()


df2 = pd.DataFrame()



for k, y in zip(['cz', 'cz', 'sk', 'sk','sk','sk', 'hu', 'hu'], [3,8, 5, 6, 17,18, 7, 8]):
                        

        
        sql = """ select a.eod, a.site, a.pon, count(distinct a.tin) as customer, z.bank,
                        Date_format(a.day, 'E') AS nap,
                                 Date_format(a.day, 'HH:mm') AS ora
                        from pos{k}.t001 a
                        join pos{k}.t_tillmap z
                        on a.pon = z.till and a.site = z.site and a.eod = z.eod
                        where a.eod between 230529 and 230903
                        and z.bank in ({y})
                        group by a.eod, a.site, a.pon, z.bank, Date_format(a.day, 'E'), Date_format(a.day, 'HH:mm')
                        
                        """.format(
                                k=k,
                                y=y
        )

        art_gold = pd.read_sql(sql, conn)
        df2 = pd.concat([df2, art_gold])
        print(f"\n15min part done with {k}\n")
