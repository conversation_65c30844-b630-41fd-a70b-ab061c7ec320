import pandas as pd
import numpy as np



        
        
def check_tag_values(df):
    tag_columns = ["Hard_Tag", "Gillette_Tag", "Electro_Tag", "Soft_Tag", 
                  "Salami_Tag", "Safer_Tag", "Bottle_Tag", "BliszterfülTag", "CrocoTag"]
    
    # Create boolean mask for values > 0 (faster than doing it multiple times)
    mask = df[tag_columns] > 0
    
    # Use the pre-computed mask to find problematic rows
    problematic_rows = mask.sum(axis=1) >= 2
    
    # Only fetch the problematic rows once
    if problematic_rows.any():
        # Get problematic rows efficiently using boolean indexing
        problem_df = df.loc[problematic_rows, tag_columns + ['store']].head()
        
        print("\n#########################")
        print("First few rows with multiple non-zero values:")
        print(problem_df)
        
        print("\nDetailed view of problematic rows:")
        # Use numpy operations instead of list comprehension
        non_zero_mask = problem_df[tag_columns] > 0
        
        for idx in problem_df.index:
            store = problem_df.loc[idx, 'store']
            print(f"\nRow {idx} (Store: {store}):")
            # Get non-zero columns efficiently
            cols_with_values = problem_df.loc[idx, tag_columns][non_zero_mask.loc[idx]]
            for col, val in cols_with_values.items():
                print(f"  {col}: {val}")
                
        print("#########################")

# Optional: If you want to return the problematic rows for further processing
def get_problematic_rows(df):
    tag_columns = ["Hard_Tag", "Gillette_Tag", "Electro_Tag", "Soft_Tag", 
                  "Salami_Tag", "Safer_Tag", "Bottle_Tag", "BliszterfülTag", "CrocoTag"]
    
    mask = df[tag_columns] > 0
    return df[mask.sum(axis=1) >= 2]

        

def tagging_on_product(Drivers: pd.DataFrame):
    
    

    
    
    
            crocotag_cond = [
                
                
                # Libamáj/Kacsamáj
                (Drivers.group.isin([ 'Duck_Prepacked', 'Goose_Prepacked']))
                & (Drivers.country == "HU") & (Drivers.subgroup == "Offals")
                & (Drivers.product_name != 'FRISS HIZOTT LIBATESTHAJ')
                & (Drivers.format.isin(["Hypermarket", "Compact"])),
                
                
                #Kávék
                (Drivers.country.isin(["HU"]))
                & (Drivers.group == "Coffee")
                & (Drivers.item_price >= 3000)
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.subgroup != "Capsules_Pods"),

                
                (Drivers.country.isin(["HU"]))
                & (Drivers.group == "Coffee")
                & (Drivers.item_price >= 5000)
                & (Drivers.format.isin(["Hypermarket", "Compact"])),
                
                #Mosókapszula, mosogatógép kapszula(tasakos)
                (Drivers.country.isin(["HU"]))
                & (Drivers.subgroup == "Capsules")
                & (Drivers.item_price >= 5000)
                & (Drivers.format.isin(["1K", "Express"])),
                
                (Drivers.country.isin(["HU"]))
                & (Drivers.subgroup == "Capsules")
                & (Drivers.item_price >= 8000)
                & (Drivers.format.isin(["Hypermarket", "Compact"])),
                
                
                
                ]
            
            
            crocotag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"]
            ]
            
            
            
            Drivers["CrocoTag"] = np.select(crocotag_cond, crocotag_result, 0)
            
    
            # Bottle Tagging
            bottle_tag_cond = [
                
                #HU
                #Borok, Pezsgők
                #Szeszesitalok
                #Alkoholmentes koktélok
                (Drivers.country == "HU")
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 5000),
                
                (Drivers.country == "HU")
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 6000),
                
                #Olivaolajok
                (Drivers.country.isin(["HU"]))
                & (Drivers.group == "Olive")
                & (Drivers.subgroup == "Olive_Oils")
                & (Drivers.item_price >= 3000)
                & (Drivers.format.isin(["1K", "Express"])),
                
                (Drivers.country.isin(["HU"]))
                & (Drivers.group == "Olive")
                & (Drivers.subgroup == "Olive_Oils")
                & (Drivers.item_price >= 5000)
                & (Drivers.format.isin(["Hypermarket", "Compact"])),
                
                
                
                #CZ
                #Alcohol
                #Spirits, Whiskey, etc
                #Bottled Wine
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 349),
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 899),
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 299),
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 299),
                
                
                #SK
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 15),
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 15),
                (Drivers.country == "SK")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 12),
                (Drivers.country == "SK")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 12),
            ]
            
            bottle_tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
            ]
    
            Drivers["Bottle_Tag"] = np.select(bottle_tag_cond, bottle_tag_result, 0)
            
            
            
            bliszterfül_tag_cond = [
                
                #HU
                #Borotvapengék
                (Drivers.country == "HU")
                & (Drivers.dep == 'HEA')
                & (Drivers.item_price >= 5000)
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.subgroup == 'System_razors'),

                (Drivers.country == "HU")
                & (Drivers.dep == 'HEA')
                & (Drivers.item_price >= 2000)
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.subgroup == 'System_razors'),

                
                #Eldobható borotvák
                (Drivers.country == "HU")
                & (Drivers.dep == 'HEA')
                & (Drivers.item_price >= 4000)
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.subgroup == 'Disposable_razors_n_blades'),

                (Drivers.country == "HU")
                & (Drivers.dep == 'HEA')
                & (Drivers.item_price >= 2000)
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.subgroup == 'Disposable_razors_n_blades'),
                
                #Borotvakészülékek
                (Drivers.subgroup == "man shaving")
                &(Drivers.country == "HU")
                & (Drivers.dep == "HDL"),
                
                
                #Fülhallgatók/Fejhallgatók 
                (Drivers.subgroup == "Headphone&Mic")
                &(Drivers.country == "HU")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 5000),
                
                (Drivers.subgroup == "Headphone&Mic")
                &(Drivers.country == "HU")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.item_price >= 7000),


                #Elektromos berendezések/Gépek tartozékai/Okosotthon, lakáskiegészítő elektronika
                (Drivers.section.isin(["CH Electrical", "CH SDA", "CS1 CH Electrical"]))
                & (Drivers.group == "SDA")
                & (Drivers.country == "HU")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 5000),
                
                (Drivers.section.isin(["CH Electrical", "CH SDA", "CS1 CH Electrical"]))
                & (Drivers.group == "SDA")
                & (Drivers.country == "HU")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.item_price >= 7000),
                
                
                #Gépek tartozékai
                ((Drivers.subgroup == "powertools access")
                  | (Drivers.group == "group")
                  | (Drivers.subgroup == "Lightbulbs"))
                & (Drivers.country == "HU")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.item_price >= 7000),
                
                (Drivers.subgroup == "powertools access")
                & (Drivers.country == "HU")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 5000),
                
                ]
            
            
            bliszterfül_tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"]
            ]
            
            
            
            
            Drivers["BliszterfülTag"] = np.select(bliszterfül_tag_cond, bliszterfül_tag_result, 0)
    
            # Safer Tagging
            safer_tag_cond = [
                
                #HU
                #Parfüm, Napolaj
                #Hajápolók (kivéve hajfesték) 
                #Krémek (arc- és bőrápolás)
                (Drivers.country == "HU")
                & (Drivers.dep == 'HEA')
                & ((Drivers.section == "Fragrances")
                | (Drivers.group == "Suncare")
                | (Drivers.group.isin(['Shampoos_n_conditoners','Styling','Toilettries','Nursery','Consign_error']))
                | (Drivers.group.isin(['Body_care','Hand_care','Face_creams'])))
                & (Drivers.item_price >= 4000)
                & (Drivers.format.isin(["Hypermarket", "Compact"])),
                
                (Drivers.country == "HU")
                & (Drivers.dep == 'HEA')
                & ((Drivers.section == "Fragrances")
                | (Drivers.group == "Suncare")
                | (Drivers.group.isin(['Shampoos_n_conditoners','Styling','Toilettries','Nursery','Consign_error']))
                | (Drivers.group.isin(['Body_care','Hand_care','Face_creams'])))
                & (Drivers.item_price >= 3000)
                & (Drivers.format.isin(["1K", "Express"])),
                
                #Sudocrem, Neogranormon, Bepanthen
                (Drivers.country == "HU")
                & (Drivers.dep == 'HEA')
                & ((Drivers.product_name.str.contains("neogranormon", case=False))
                | (Drivers.product_name.str.contains("sudocrem", case=False))
                | (Drivers.product_name.str.contains("bepanthen", case=False))),
                
                #Condoms, síkosító
                (Drivers.country == "HU")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.group == "Condoms")
                & (Drivers.item_price >= 2000),
                
                #Vitamin, Tápkieg
                (Drivers.country == "HU")
                & (Drivers.dep == 'HEA')
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.group == "Drugs")
                & (Drivers.item_price >= 5000),
                
                (Drivers.country == "HU")
                & (Drivers.dep == 'HEA')
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.group == "Drugs")
                & (Drivers.item_price >= 3000),
                
                
                #Hangszórók, Számítástechnikai kiegészítők
                (Drivers.subgroup == 'Computing Accs')
                &(Drivers.country == "HU")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 5000),
                
                (Drivers.subgroup == 'Computing Accs')
                &(Drivers.country == "HU")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.item_price >= 7000),
                
                #CZ
                #Creams, Parfumes
                (Drivers.country == "CZ")
                & (Drivers.pmg.isin( ["HEA03","HEA05", "HEA08"]))
                & (Drivers.item_price >= 299),
                
                #USB flash drive, memory cards, external media
                (Drivers.country == "CZ")
                & (Drivers.pmg == "HDL33")
                & (Drivers.item_price >= 0),
                
                #Mobile phone
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.group.str.contains("mobile", case=False)),
                
                #SK
                #Creams, Parfumes
                (Drivers.country == "SK")
                & (Drivers.pmg.isin( ["HEA03","HEA05", "HEA08"]))
                & (Drivers.item_price >= 10),
                
                #USB flash drive, memory cards, external media
                (Drivers.country == "SK")
                & (Drivers.pmg == "HDL33")
                & (Drivers.item_price >= 0),
                
                #Mobile phone
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.group.str.contains("mobile", case=False)),
                
            ]
    
            safer_tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
            ]
    
            Drivers["Safer_Tag"] = np.select(safer_tag_cond, safer_tag_result, 0).astype(
                "float32"
            )
    
            # Salami Tagging
            Drivers["Salami_Tag"] = np.where(
                (Drivers.country == "HU")
                & (Drivers.pmg == "PPD02")
                & (Drivers.item_price >= 3500),
                Drivers["Unit_for_tagging"],
                0,
            ).astype("float32")
    
            # Soft Tagging
            soft_tag_cond = [
                
                #HU
                # steak
                # premium húsok
                # Tálcás szalámik
                # Tömbsajt
                (Drivers.dep.isin([ "SFP", "SFM"]))
                & (Drivers.country == "HU")
                & (Drivers.product_name.str.contains('steak' , case=False))
                & (Drivers.format.isin(["1K", "Express"])),
                
                (Drivers.dep.isin([ "SFP", "SFM"]))
                & (Drivers.country == "HU")
                & (Drivers.item_price >= 10000)
                & (Drivers.format.isin(["1K", "Express"])),
                
                (Drivers.country == "HU")
                & (Drivers.subgroup == 'SLICED_FERMENTED_SAL')
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.srp > 0),
                
                (Drivers.country == "HU")
                & (Drivers.pmg == "DAI05")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 3000),
                
                
                #Desszertek, Táblás csokoládék
                (Drivers.country == "HU")
                & (Drivers.pmg == "DRY13")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 1500)
                & (Drivers.group == 'Chocolate_Boxes'),
                
                (Drivers.country == "HU")
                & (Drivers.pmg == "DRY13")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 2500),
                
                #Fogkrémek, Fogsorrögzítők
                (Drivers.country == "HU")
                & (Drivers.dep == "HEA")
                & (Drivers.section == "Oral_Care")
                & (Drivers.group.isin(["Kids", "Toothpastes"]))
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 2000),
                
                
                #RIO MARE
                (Drivers.country.isin(["HU"]))
                & (Drivers.product_name.str.contains("RIO MARE")
                    | Drivers.product_name.str.contains("RIOMARE"))
                & (Drivers.item_price >= 1000)
                & (Drivers.format.isin(["1K", "Express"])),
                
                #Nutella
                (Drivers.country.isin(["HU"]))
                & (Drivers.product_name.str.contains("NUTELLA"))
                & (Drivers.item_price >= 2000)
                & (Drivers.format.isin(["1K", "Express"])),
                
                #Coffee capsules
                (Drivers.country == "HU")
                & (Drivers.subgroup == "Capsules_Pods")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 1500),
                
                
                
                #SK
                (Drivers.country == "SK")
                & (Drivers.pmg == "DRY13")
                & (Drivers.item_price >= 5)
                & (Drivers.says != 0),
                
                (
                    (Drivers.country.isin(["SK"]))
                    & (
                        Drivers.product_name.str.contains("rio mare", case=False)
                        | Drivers.product_name.str.contains("riomare", case=False)
                    )
                    & (Drivers.says != 0)
                ),
                
                
                (Drivers.country == "SK")
                & (Drivers.pmg == "DRY22")
                & (Drivers.item_price >= 15)
                & (Drivers.says != 0),

            ]
            soft_tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"] / 5,
                Drivers["Unit_for_tagging"] / 5,
                Drivers["Unit_for_tagging"] / 4,
            ]
            Drivers["Soft_Tag"] = np.select(soft_tag_cond, soft_tag_result, 0).astype(
                "float32"
            )
            

    
            # Electro Tagging
            electro_tag_cond = [
                #HU
                #Szépségápolás
                (Drivers.country == "HU")
                & (Drivers.section == "CH Electrical")
                & (Drivers.group == "Personal care")
                & (Drivers.subgroup != "man shaving")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.item_price >= 10000),
                
                (Drivers.country == "HU")
                & (Drivers.section == "CH Electrical")
                & (Drivers.group == "Personal care")
                & (Drivers.subgroup != "man shaving")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 5000),
                
                
                #Barkácsgépek
                (Drivers.country == "HU")
                & (Drivers.subgroup == "powertools")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.item_price >= 10000),
                
                (Drivers.country == "HU")
                & (Drivers.subgroup == "powertools")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.item_price >= 5000),
                
                #Lego
                (Drivers.country == "HU")
                & (Drivers.format.isin(["1K","Express"]))
                & (Drivers.product_name.str.contains("lego", case=False))
                & (Drivers.item_price >= 8000),
                
                (Drivers.country == "HU")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.product_name.str.contains("lego", case=False))
                & (Drivers.item_price >= 15000),
                
                #CZ
                #Computer accessories, audio equipment
                #earphones, speakers
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg.isin(["HDL32"]))
                & (Drivers.item_price >= 599),
                
                #Hair straighteners, curling irons and hair dryers
                #Epilators, depilatories and shavers
                #Electric tooth brush
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg.isin(["HDL34"]))
                & (Drivers.item_price >= 699),
                
                #DYI machines, power tools, electrical equipment
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg.isin(["HDL19", "HDL20", "HDL21", "HDL35", "HDL36"]))
                & (~Drivers.group.str.contains("mobile", case=False))
                & (Drivers.item_price >= 999),
                
                #SK
                #Computer accessories, audio equipment
                #earphones, speakers
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg.isin(["HDL32"]))
                & (Drivers.item_price >= 20),
                
                
                #Hair straighteners, curling irons and hair dryers
                #Epilators, depilatories and shavers
                #Electric tooth brush
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg.isin(["HDL34"]))
                & (Drivers.item_price >= 30),
                
                #DYI machines, power tools, electrical equipment
                (Drivers.country == "SK")
                & (Drivers.pmg.isin(["HDL19", "HDL20", "HDL21", "HDL35", "HDL36"]))
                & (~Drivers.group.str.contains("mobile", case=False))
                & (Drivers.item_price >= 50),
                

            ]
            electro_tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"]
            ]
    
            Drivers["Electro_Tag"] = np.select(
                electro_tag_cond, electro_tag_result, 0
            ).astype("float32")
    
            # Gillette Tagging
            
            Gillette_Tag_cond = [
                #CZ
                #Razor blades, razor appliances (except disposable razors) Disposable pouch razors
                (Drivers.country == "CZ")
                & (Drivers.pmg == "HEA07")
                & (Drivers.item_price >= 299),
                
                #SK
                #Razor blades, razor appliances (except disposable razors) Disposable pouch razors
                (Drivers.country == "SK")
                & (Drivers.pmg == "HEA07")
                & (Drivers.item_price >= 10),
                

            ]
            Gillette_Tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"]
            ]
            Drivers["Gillette_Tag"] = np.select(
                Gillette_Tag_cond, Gillette_Tag_result, 0
            ).astype("float32")
    
            # Hard Tagging
            hard_tag_cond = [
                #HU
                #Elektromos fogkefék/fogkefefejek
                (Drivers.country == "HU")
                & (Drivers.subgroup == 'Electric_toothbrushes' ),
                
                #Bags, suitcases
                (Drivers.country == "HU")
                & (Drivers.group == "CH Luggage")
                & (Drivers.item_price >= 10000),
                
                
                #CZ
                
                #Backpack, bags, suitcases
                (Drivers.country == "CZ")
                & (Drivers.group == "CH Luggage")
                & (Drivers.item_price >= 699),
                
                #Engine oils
                (Drivers.country == "CZ")
                & (Drivers.pmg == "HDL22")
                & (Drivers.item_price >= 999),
                
                
                #SK
                #Backpack, bags, suitcases
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.product_name.str.contains("kufor", case=False) | Drivers.product_name.str.contains("batoh", case=False))
                & (Drivers.item_price > 50),
                
                #Engine oils
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.group.isin(['Car oils', 'CS1 Car oils'])),
                
                #Sport, toys, lego
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg.isin(["HDL10","HDL11", "HDL12","HDL14"]))
                & (Drivers.item_price >= 50),

            ]
            hard_tag_res = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"]
            ]
            Drivers["Hard_Tag"] = np.select(hard_tag_cond, hard_tag_res, 0).astype(
                "float32"
            )
            
            
            
            
            
            Drivers["Tag_total_nr"] = (
                Drivers["Hard_Tag"]
                + Drivers["Gillette_Tag"]
                + Drivers["Electro_Tag"]
                + Drivers["Soft_Tag"]
                + Drivers["Salami_Tag"]
                + Drivers["Safer_Tag"]
                + Drivers["Bottle_Tag"]
                + Drivers['BliszterfülTag']
                + Drivers["CrocoTag"]
            )
            
            
            return Drivers
        
        
        
def tagging_old_model(Drivers):
    
    
    
    
            # Bottle Tagging
            bottle_tag_cond = [
                (Drivers.country == "HU")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 5000),
                (Drivers.country == "HU")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 4000),
                (Drivers.country == "HU")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 5000),
                (Drivers.country == "HU")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 4000),
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 899),
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 349),
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 299),
                (Drivers.country == "CZ")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 199),
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 15),
                (Drivers.country == "SK")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 10),
                (Drivers.country == "SK")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS05")
                & (Drivers.item_price >= 15),
                (Drivers.country == "SK")
                & (Drivers.format.isin(["1K", "Express"]))
                & (Drivers.pmg == "BWS04")
                & (Drivers.item_price >= 10),
            ]
            bottle_tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
            ]
    
            Drivers["Bottle_Tag"] = np.select(bottle_tag_cond, bottle_tag_result, 0)
    
            # Safer Tagging
            safer_tag_cond = [
                (Drivers.country == "HU")
                & (Drivers.pmg == "HEA14")
                & (Drivers.item_price >= 1000),
                (Drivers.country == "HU")
                & (Drivers.pmg == "HEA05")
                & (Drivers.item_price >= 3000),
                (Drivers.country == "HU")
                & (Drivers.pmg == "HEA16")
                & (Drivers.item_price >= 2000),
                (Drivers.country == "HU")
                & (Drivers.pmg == "HEA15")
                & (Drivers.item_price >= 4000),
                (Drivers.country == "HU")
                & (Drivers.pmg == "HEA17")
                & (Drivers.item_price >= 2000),
                (Drivers.country == "HU")
                & (Drivers.pmg == "HEA08")
                & (Drivers.item_price >= 4000),
                (Drivers.country == "HU")
                & (Drivers.pmg == "HEA06")
                & (Drivers.item_price >= 3000),
                (Drivers.country == "HU")
                & (Drivers.pmg == "HDL33")
                & (Drivers.item_price >= 0),
                (Drivers.country == "CZ")
                & (Drivers.pmg.isin(["HEA15", "HEA08"]))
                & (Drivers.item_price >= 600),
                (Drivers.country == "CZ")
                & (Drivers.pmg.isin(["HEA14", "HEA05", "HEA16", "HEA17", "HEA06"]))
                & (Drivers.item_price >= 200),
                (Drivers.country == "CZ")
                & (Drivers.pmg == "HDL33")
                & (Drivers.item_price >= 0),
                (Drivers.country == "SK")
                & (Drivers.pmg.isin(["HEA15", "HEA08"]))
                & (Drivers.item_price >= 20),
                (Drivers.country == "SK")
                & (Drivers.pmg.isin(["HEA14", "HEA05", "HEA16", "HEA17", "HEA06"]))
                & (Drivers.item_price >= 10),
                (Drivers.country == "SK")
                & (Drivers.pmg == "HDL33")
                & (Drivers.item_price >= 0),
            ]
    
            safer_tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
            ]
    
            Drivers["Safer_Tag"] = np.select(safer_tag_cond, safer_tag_result, 0).astype(
                "float32"
            )
    
            # Salami Tagging
            Drivers["Salami_Tag"] = np.where(
                (Drivers.country == "HU")
                & (Drivers.pmg == "PPD02")
                & (Drivers.item_price >= 2500),
                Drivers["Unit_for_tagging"],
                0,
            ).astype("float32")
    
            # Soft Tagging
            soft_tag_cond = [
                (Drivers.country == "HU")
                & (Drivers.pmg == "DAI05")
                & (Drivers.item_price >= 3000)
                & (Drivers.says != 0),
                (Drivers.country == "HU")
                & (Drivers.pmg == "DRY13")
                & (Drivers.item_price >= 2500)
                & (Drivers.says != 0),
                (Drivers.country == "SK")
                & (Drivers.pmg == "DRY13")
                & (Drivers.item_price >= 5)
                & (Drivers.says != 0),
                (
                    (Drivers.country.isin(["HU", "SK"]))
                    & (
                        Drivers.product_name.str.contains("RIO MARE")
                        | Drivers.product_name.str.contains("RIOMARE")
                    )
                    & (Drivers.says != 0)
                ),
            ]
            soft_tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"] / 5,
                Drivers["Unit_for_tagging"] / 5,
                Drivers["Unit_for_tagging"] / 4,
            ]
            Drivers["Soft_Tag"] = np.select(soft_tag_cond, soft_tag_result, 0).astype(
                "float32"
            )
            

    
            # Electro Tagging
            electro_tag_cond = [
                (Drivers.country == "HU")
                & (Drivers.pmg.isin(["HDL20", "HDL32", "HDL34", "HDL35", "HDL36"]))
                & (Drivers.item_price >= 10000),
                (Drivers.country == "HU")
                & (Drivers.format.isin(["Hypermarket", "Compact"]))
                & (Drivers.product_name.str.contains("LEGO"))
                & (Drivers.item_price > 10000),
                (Drivers.country == "CZ")
                & (Drivers.pmg.isin(["HDL32", "HDL34", "HDL35", "HDL36", "HDL18"]))
                & (Drivers.item_price >= 600),
                (Drivers.country == "CZ")
                & (Drivers.pmg.isin(["HDL20", "HDL21", "HDL22"]))
                & (Drivers.item_price >= 999),
                (Drivers.country == "SK")
                & (Drivers.pmg.isin(["HDL32", "HDL34", "HDL35", "HDL36", "HDL18"]))
                & (Drivers.item_price >= 30),
                (Drivers.country == "SK")
                & (Drivers.pmg.isin(["HDL20", "HDL21", "HDL22"]))
                & (Drivers.item_price >= 20),
                (Drivers.country == "SK")
                & (Drivers.product_name.str.contains("LEGO"))
                & (Drivers.item_price > 50),
            ]
            electro_tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
            ]
    
            Drivers["Electro_Tag"] = np.select(
                electro_tag_cond, electro_tag_result, 0
            ).astype("float32")
    
            # Gillette Tagging
            Gillette_Tag_cond = [
                (Drivers.country == "HU")
                & (Drivers.pmg == "HEA07")
                & (Drivers.item_price >= 3000),
                (Drivers.country == "CZ")
                & (Drivers.pmg == "HEA07")
                & (Drivers.item_price >= 200),
                (Drivers.country == "SK")
                & (Drivers.pmg == "HEA07")
                & (Drivers.item_price >= 10),
            ]
            Gillette_Tag_result = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
            ]
            Drivers["Gillette_Tag"] = np.select(
                Gillette_Tag_cond, Gillette_Tag_result, 0
            ).astype("float32")
    
            # Hard Tagging
            hard_tag_cond = [
                (Drivers.country == "HU")
                & (Drivers.pmg.isin(["HEA14", "HEA18", "HEA15"]))
                & (Drivers.item_price >= 8000),
                (Drivers.country == "HU")
                & (Drivers.product_name.str.contains("GURULOS BOROND"))
                & (Drivers.item_price > 10000),
                (Drivers.country == "CZ")
                & (Drivers.pmg.isin(["HEA14", "HEA18", "HEA15"]))
                & (Drivers.item_price >= 599),
                (Drivers.country == "CZ")
                & (Drivers.product_name.str.contains("BATOH"))
                & (Drivers.item_price >= 599),
                (Drivers.country == "SK")
                & (Drivers.pmg.isin(["HEA14", "HEA18", "HEA15"]))
                & (Drivers.item_price >= 20),
                (Drivers.country == "SK")
                & (Drivers.product_name.str.contains("KUFOR"))
                & (Drivers.item_price > 30),
            ]
            hard_tag_res = [
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
                Drivers["Unit_for_tagging"],
            ]
            Drivers["Hard_Tag"] = np.select(hard_tag_cond, hard_tag_res, 0).astype(
                "float32"
            )
    
            Drivers["Tag_total_nr"] = (
                Drivers["Hard_Tag"]
                + Drivers["Gillette_Tag"]
                + Drivers["Electro_Tag"]
                + Drivers["Soft_Tag"]
                + Drivers["Salami_Tag"]
                + Drivers["Safer_Tag"]
                + Drivers["Bottle_Tag"]
            )
            
            
            return Drivers
