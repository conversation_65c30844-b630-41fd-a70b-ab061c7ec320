

import pandas as pd
import numpy as np
from pathlib import Path
import pyodbc


conn = pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()



catres = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\CategoryReset\Category Reset CE_hier.xlsx")

catres_ = catres[["country","DIV_ID","DEP_ID", "SEC_ID", "GRP_ID", "SGR_ID", "Category name"]].drop_duplicates()

for x in ['DIV_ID', 'DEP_ID', 'SEC_ID', 'GRP_ID', 'SGR_ID']:
    catres_[x] = catres_[x].apply(str).str.pad(width=4, side='left', fillchar='0')
    
catres_['level5'] = catres_[['DIV_ID', 'DEP_ID', 'SEC_ID', 'GRP_ID', 'SGR_ID']].apply(lambda row: ''.join(row.values.astype(str)), axis=1)

data_list = catres_.values.tolist()



products=  catres_.groupby("country")["level5"].apply(lambda s: s.tolist()).to_dict()


df2 = pd.DataFrame()
for k, v in products.items():
    
    s = list()
    for x in v:

        s.append(str(x))

    catres_tuple = tuple(s)

    sql = """ SELECT cntr_code AS country, slad_tpnb AS tpnb, slad_tpn AS tpn, hier.pmg AS pmg,
    dmat_div_des_en AS DIV_DESC,
    dmat_div_code as DIV_ID,
    dmat_dep_des_en AS DEP_DESC,
    dmat_dep_code as DEP_ID,
    dmat_sec_des_en AS SEC_DESC,
    dmat_sec_code as SEC_ID,
    dmat_grp_des_en AS GRP_DESC,
    dmat_grp_code as GRP_ID,
    dmat_sgr_des_en AS SGR_DESC,
    dmat_sgr_code as SGR_ID,
    slad_long_des as product_name
            FROM DM.dim_artgld_details mstr
            JOIN tesco_analysts.hierarchy_spm_odbc hier
            ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0")
            AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0")
            AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0")
            AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0")
            AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
    WHERE /*slad_tpnb in (tpnb)*/
    cntr_code = '{k}'
    AND CONCAT(dmat_div_code, dmat_dep_code, dmat_sec_code,dmat_grp_code, dmat_sgr_code ) in {catres}
    AND cntr_code <> "PL"
    AND dmat_sgr_des_en <> "Do not use"
    GROUP BY cntr_code, hier.pmg, slad_tpnb,slad_tpn,
    dmat_div_des_en,
    dmat_div_code,
    dmat_dep_des_en,
    dmat_dep_code,
    dmat_sec_des_en,
    dmat_sec_code,
    dmat_grp_des_en,
    dmat_grp_code,
    dmat_sgr_des_en,
    dmat_sgr_code,
    slad_long_des
    
    """.format(k=k, catres=catres_tuple)
    
    art_gold = pd.read_sql(sql, conn)
    df2 = pd.concat([df2, art_gold])
    
df2['level5'] = df2['DIV_ID'] + df2['DEP_ID'] + df2['SEC_ID']+ df2['GRP_ID']+ df2['SGR_ID']

df2 = df2.merge(catres_[['country','level5','Category name']].drop_duplicates(), on=['country','level5'], how='left')


