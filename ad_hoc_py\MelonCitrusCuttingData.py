
import pandas as pd
import pyodbc
import numpy as np
from datetime import datetime



pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)


start = '20240527'
end = '20240901'

# Define the start and end dates
start_date = datetime.strptime(start, '%Y%m%d')
end_date = datetime.strptime(end, '%Y%m%d')

# Calculate the difference between the two dates
difference = end_date - start_date

# Convert the difference into weeks
weeks = (difference.days + 6) // 7



with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:
    
    df = pd.DataFrame()
    countries_adress = ["hu", "cz", "sk"]
    countries_to_select = ['"HU"', '"CZ"', '"SK"']
    for c1, c2 in zip(countries_adress, countries_to_select):

        sql = """
        
        
                    SELECT 
                    b.cntr_code AS country,
                    CAST(a.site as INT) as store, 
                   
                    CAST(b.slad_tpnb as INT) AS tpnb,
                    b.slad_long_des AS product_name,
                    hier.pmg AS pmg,
                    b.slad_tpn AS tpn,
                    b.dmat_div_des_en AS DIV_DESC,
                    b.dmat_div_code as DIV_ID,
                    b.dmat_dep_des_en AS DEP_DESC,
                    b.dmat_dep_code as DEP_ID,
                    b.dmat_sec_des_en AS SEC_DESC,
                    b.dmat_sec_code as SEC_ID,
                    b.dmat_grp_des_en AS GRP_DESC,
                    b.dmat_grp_code as GRP_ID,
                    b.dmat_sgr_des_en AS SGR_DESC,
                    b.dmat_sgr_code as SGR_ID,
                    AVG(b.slad_net_weight) AS weight_of_product,
                    b.slad_unit AS unit_type,
                    SUM(a.qty) AS sold_kg,
                    SUM(CASE WHEN a.qty > 0 AND a.qty <= 500 THEN 1 ELSE 0 END) AS `0.5kg`,
                    SUM(CASE WHEN a.qty > 0.5 AND a.qty <= 1000 THEN 1 ELSE 0 END) AS `1kg`,
                    SUM(CASE WHEN a.qty > 1000 AND a.qty <= 1500 THEN 1 ELSE 0 END) AS `1.5kg`,
                    SUM(CASE WHEN a.qty > 1500 AND a.qty <= 2000 THEN 1 ELSE 0 END) AS `2kg`,
                    SUM(CASE WHEN a.qty > 2000 AND a.qty <= 2500 THEN 1 ELSE 0 END) AS `2.5kg`,
                    SUM(CASE WHEN a.qty > 2500 AND a.qty <= 3000 THEN 1 ELSE 0 END) AS `3kg`,
                    SUM(CASE WHEN a.qty > 3000 THEN 1 ELSE 0 END) AS `3kg+`
                     
                     
                    FROM pos{c1}.t001 a
                    LEFT JOIN DM.dim_artgld_details b
                    ON a.ean = lpad(b.slem_ean,14,0)
                    RIGHT JOIN tesco_analysts.hierarchy_spm hier ON b.dmat_div_code = LPAD(hier.div_code,4,"0") 
                    AND b.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                    AND b.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                    AND b.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                    AND b.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
                    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
                    WHERE
                    hier.pmg in ("PRO03","PRO04","PRO05","PRO06")
                    AND a.part_col between {start} and {end}
                    AND b.cntr_code = {c2}
                    AND b.slad_unit = 'KG'
                    GROUP BY 
                        b.cntr_code,
                        CAST(a.site as INT), 
                       
                        CAST(b.slad_tpnb as INT),
                        b.slad_long_des,
                        hier.pmg,
                        b.slad_tpn,
                        b.dmat_div_des_en,
                        b.dmat_div_code,
                        b.dmat_dep_des_en,
                        b.dmat_dep_code,
                        b.dmat_sec_des_en,
                        b.dmat_sec_code,
                        b.dmat_grp_des_en,
                        b.dmat_grp_code,
                        b.dmat_sgr_des_en,
                        b.dmat_sgr_code,
                        b.slad_unit
        
        
                
                """.format(weeks = weeks, start = int(start), end = int(end), c1 = c1, c2=c2 )
                
                
        b = pd.read_sql(sql, conn)
        df = pd.concat([df,b])