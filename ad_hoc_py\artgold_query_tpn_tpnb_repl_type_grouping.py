# -*- coding: utf-8 -*-
"""
Created on Tue Nov  1 11:28:50 2022

@author: phrubos
"""

import pandas as pd
import numpy as np
from pathlib import Path
import pyodbc


pd.set_option('display.max_columns', None)

conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()

# aa = """
# select * from DM.dim_artgld_details
# limit 10

# """
# art_gold = pd.read_sql(aa, conn)

# products = pd.read_excel(
#         r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_07\sold_unit_2022_wo_CAYG.xlsx")

# products = products[['MTPN','CZ', 'SK', 'HU']].melt(id_vars=['MTPN'], value_vars=['CZ', 'SK', 'HU'], var_name='country', value_name='sold_units' ).query("sold_units !=0")

products = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\w22_w34_rGM\Single_pick_list_2024.10.04.xlsx")

# dict_list = (
#     products.groupby("country")["item_tpn"]
#     .apply(lambda s: s.tolist())
#     .to_dict()
# )


products = products['item_tpn'].values




sql = f""" SELECT cntr_code AS country,
slad_tpnb AS tpnb,
slad_tpn AS tpn, 
slad_long_des as product_name
        FROM DM.dim_artgld_details mstr

WHERE slad_tpn in {tuple(products)}
AND dmat_sgr_des_en <> "Do not use"


"""

art_gold = pd.read_sql(sql, conn)



# catres = catres[["country","DIV_ID","DEP_ID", "SEC_ID", "GRP_ID", "SGR_ID"]].melt(id_vars="country")

# products=  products.groupby("country")["tpnb"].apply(lambda s: s.tolist()).to_dict()

# lista = pd.read_clipboard()
# products = tuple(lista.tpn)

# tpn = f"{products}"


df2 = pd.DataFrame()
for k, v in dict_list.items():
    
    s = list()
    for x in v:

        s.append(str(x))

    tpn = tuple(s)


    sql = """ SELECT cntr_code AS country, slad_tpnb AS tpnb, slad_tpn AS tpn, hier.pmg AS pmg,
    dmat_div_des_en AS DIV_DESC,
    dmat_div_code as DIV_ID,
    dmat_dep_des_en AS DEP_DESC,
    dmat_dep_code as DEP_ID,
    dmat_sec_des_en AS SEC_DESC,
    dmat_sec_code as SEC_ID,
    dmat_grp_des_en AS GRP_DESC,
    dmat_grp_code as GRP_ID,
    dmat_sgr_des_en AS SGR_DESC,
    dmat_sgr_code as SGR_ID,
    slad_long_des as product_name
            FROM DM.dim_artgld_details mstr
            --JOIN dw.sl_sms sunit  ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id 
            --JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id 
            JOIN tesco_analysts.hierarchy_spm hier
            ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0")
            AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0")
            AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0")
            AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0")
            AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
    WHERE slad_tpnb in {tpn}
    AND cntr_code = '{k}'
    AND dmat_sgr_des_en <> "Do not use"
    GROUP BY cntr_code, hier.pmg, slad_tpnb,slad_tpn,
    dmat_div_des_en,
    dmat_div_code,
    dmat_dep_des_en,
    dmat_dep_code,
    dmat_sec_des_en,
    dmat_sec_code,
    dmat_grp_des_en,
    dmat_grp_code,
    dmat_sgr_des_en,
    dmat_sgr_code,
    slad_long_des
    
    """.format(tpn=tpn, k=k)
    
    art_gold = pd.read_sql(sql, conn)
    df2 = pd.concat([df2, art_gold])


# art_gold = pd.read_sql(sql, conn)


# for x in ['DIV_ID', 'DEP_ID', 'SEC_ID', 'GRP_ID', 'SGR_ID']:
#     b[x] = b[x].apply(str).str.pad(width=4, side='left', fillchar='0')

import pyarrow.parquet as pq

dict_list = (
    df2.groupby("country")["tpnb"]
    .apply(lambda s: s.tolist())
    .to_dict()
)

df = pd.DataFrame()

b = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\as_is_modelDataSet_updated_14-08"

for k, v in dict_list.items():
    Repl_Dataset = pq.read_table(
        b,
        filters=[("tpnb", "in", v), ("country", "=", k)],
    ).to_pandas()
    
    df = pd.concat([Repl_Dataset, df])
    
repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]  
for r in repl_types:
    df[r] = np.where(df[r] == 1, df.sold_units, 0)


df.groupby(["country", "tpnb", "product_name"], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
