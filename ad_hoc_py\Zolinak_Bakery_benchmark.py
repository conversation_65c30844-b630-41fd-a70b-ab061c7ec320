# -*- coding: utf-8 -*-
"""
Created on Thu Jan 19 10:32:56 2023

@author: phrubos
"""

import glob, os
import pandas as pd

os.chdir(r"c:\Users\<USER>\Downloads\zolinak\HU_isb")

folder = r"c:\Users\<USER>\Downloads\zolinak\HU_isb"

# x = pd.read_csv(f"{folder}\Bakery 2022.06.27-08.07.txt", sep=';')

# for file in glob.glob("*.txt"):
#     print(file)
#     try:
#         a = pd.read_csv(f"{folder}\{file}", sep=';')
#         b = a[((a['Local Store'] == 21006) & (a['_1'] == 'ISB_Bakery'))]
#         b['Local Store'] = 21101
#         b['Unnamed: 2'] = 'Podunajske Biskupice'
#         df = pd.concat([a, b])
#         df.to_csv(f"{folder}\{file[:-4]}_new.txt", sep=';')
#     except:
#         a = pd.read_csv(f"{folder}\{file}", sep=';')
#         b = a[((a['Local_Store'] == 21006) & (a['SEC_DES_EN'] == 'ISB_Bakery'))]
#         b['Local_Store'] = 21101
#         b['Local_Store_Name'] = 'Podunajske Biskupice'
#         df = pd.concat([a, b])
#         df.to_csv(f"{folder}\{file[:-4]}_new.txt", sep=';', index=False, decimal=',')


for file in glob.glob("*.txt"):
    print(file)
    try:

        if (
            file
            == "BAKERY REFRESH!!! New Model 2021 Q1 volumes WITH EMARGING MARK+RC RTC+All RC.txt"
        ):
            a = pd.read_csv(
                f"{folder}\{file}", decimal=",", sep=";", low_memory=True, skiprows=2
            )
            b = a[
                a["Unnamed: 11"].isin(
                    [
                        "HU2004120831199",
                        "HU2004120830989",
                        "HU2004120831015",
                        "HU2004120831942",
                        "HU2004120831648",
                    ]
                )
            ]
            a = a[~(a["Country"] == "HU") & (a["Unnamed: 4"] == "ISB_Bakery")]

            df = pd.concat([a, b])

        else:
            a = pd.read_csv(
                f"{folder}\{file}", decimal=",", sep=";", low_memory=False, skiprows=2
            )
            b = a[
                a["Unnamed: 8"].isin(
                    [
                        "HU2004120831199",
                        "HU2004120830989",
                        "HU2004120831015",
                        "HU2004120831942",
                        "HU2004120831648",
                    ]
                )
            ]

            a = a[~(a["Country"] == "HU") & (a["Unnamed: 5"] == "ISB_Bakery")]

            df = pd.concat([a, b])

        df.to_csv(f"{folder}\{file[:-4]}_new.txt", sep=";")
    except:
        pass
        # a = pd.read_csv(f"{folder}\{file}", decimal=',', sep=';', low_memory=False, skiprows=2)
        # print(a.columns)
        # b = a[((a['Local_Store'] == 21006) & (a['SEC_DES_EN'] == 'ISB_Bakery'))]
        # b['Local_Store'] = 21101
        # b['Local_Store_Name'] = 'Podunajske Biskupice'
        # df = pd.concat([a, b])
        # df.to_csv(f"{folder}\{file[:-4]}_new.txt", sep=';', index=False, decimal=',')
