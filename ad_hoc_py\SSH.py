import paramiko
import pandas as pd
import numpy as np
import time
import sys
import os
from pathlib import Path
sys.path.append(os.path.dirname(Path.cwd()))
import Replenishment_Model_Functions_25 as rmf


@rmf.timeit
def ssh_downloader(what_to_download:str):
    

    folder_name = what_to_download
        

    
    
    # =============================================================================
    # Paths
    # =============================================================================
    
    output_folder = f"/home/<USER>/{folder_name}/output/"
    main_folder = f"/home/<USER>/{folder_name}/"
    
    wp_working_output = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\SSH"
    
    
    
    # =============================================================================
    # Download files
    # =============================================================================
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(hostname='hdp0430.global.tesco.org', username='phrubos', password='PetiNoemiHars2023')
        print("\nConnection is done\n")
    except:
        print("\nNo connection!\n")
        
    
        
    ftp_client = ssh_client.open_sftp()
    
    for country in ['CZ', 'HU', 'SK']:
        
        print(f"Start to process of {what_to_download} ({country})")
        
        ftp_client.get(main_folder + f"parameters_all_groups_{country}.csv", wp_working_output + "\\" + f"parameters_all_groups_{country}.csv")
        time.sleep(5)
        csv_id = pd.read_csv(wp_working_output + "\\" + f"parameters_all_groups_{country}.csv", sep="|", names=['country','id', 'stores'])['id'].count()
        print(f"\nThere will be {csv_id} rounds in {country}!\n")  
        
        stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}")
        time.sleep(5)
        
        counter = 0
        while counter <= csv_id:
        
    
            file_list = ftp_client.listdir(output_folder)
            
            try:
                output = [i for i in file_list if  i.__contains__(".zip")][0]
            except:
                output = []
                pass
            
            if output.__contains__("zip"):
                
                print(f"\n{output} done!\n")
                counter += 1
                ftp_client.get(output_folder + output, wp_working_output + "\\" + output)
                print("Copied onto local computer\n")
                time.sleep(20)
                ftp_client.remove(output_folder + output)
                
    
                
            else:
                print("Downloading.....")
                time.sleep(10)
                
            if counter == csv_id:
                
                break
                time.sleep(15)
        
    ftp_client.close()
    ssh_client.close()
    
    
    csv_files = [f for f in os.listdir(wp_working_output) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]

    ce_df = pd.concat([pd.read_csv(os.path.join(wp_working_output, f), sep=',') for f in csv_files])
    
    
    return ce_df
    
place_to_files = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\SSH"

what_to_download = "item_sold_dotcom" #set to one of ["stock", "item_sold", "item_sold_dotcom", "cases"]

df = ssh_downloader(what_to_download)
df.to_parquet(place_to_files + f"\{what_to_download}_ce", compression="gzip")

files = [f for f in os.listdir(
    place_to_files
    ) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]

for c in files:
    os.remove(
        os.path.join(
            place_to_files, f"{c}"
        )
    )
    
    
    
# =============================================================================
# part of downloading hierarchy and product names from artgold
# =============================================================================

import pyodbc



conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()

item_sold_df=pd.read_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\SSH\item_sold_ce"
    )

products = item_sold_df[['country', 'tpnb']].drop_duplicates()
products = products.groupby(["country"])['tpnb'].apply(lambda s: s.tolist()).to_dict()


for key, value in products.items():
    #print value
    print(key, len([item for item in value if item]))

df2 = pd.DataFrame()

for k, v in products.items():
                    
    s = list()
    
    for x in v:
                
        s.append(str(x))
        tpnbs = ",".join(s)
    
    sql = """ SELECT 
                    cntr_code AS country,
                    slad_tpnb AS tpnb,
                    dmat_div_des_en AS division,
                    cast(dmat_div_code as INT) as DIV_ID,
                    dmat_dep_des_en AS department,
                    cast(dmat_dep_code as INT) as DEP_ID,
                    dmat_sec_des_en AS section,
                    cast(dmat_sec_code as INT) as SEC_ID,
                    dmat_grp_des_en AS group,
                    cast(dmat_grp_code as INT) as GRP_ID,
                    dmat_sgr_des_en AS subgroup,
                    cast(dmat_sgr_code as INT) as SGR_ID,
                    slad_long_des as product_name
            FROM
                    DM.dim_artgld_details
            WHERE 
                    slad_tpnb in ({tpnbs}) 
            AND     
                    cntr_code = '{k}' 
            AND     
                    dmat_sgr_des_en <> "Do not use"
            GROUP BY 
                    cntr_code,
                    slad_tpnb,
                    dmat_div_des_en,
                    dmat_div_code,
                    dmat_dep_des_en,
                    dmat_dep_code,
                    dmat_sec_des_en,
                    dmat_sec_code,
                    dmat_grp_des_en,
                    dmat_grp_code,
                    dmat_sgr_des_en,
                    dmat_sgr_code,
                    slad_long_des
                    
                    """.format(
        tpnbs=tpnbs, k=k
    )

    art_gold = pd.read_sql(sql, conn)
    df2 = pd.concat([df2, art_gold])
    print(f"\nHierarchy part done with {k}\n")
    

df2['tpnb'] = df2['tpnb'].astype("int")


cols = ['weight', 'case_capacity']
for x in cols:    
    item_sold_df[x] = item_sold_df[x].fillna(item_sold_df.groupby(['country', 'pmg'], observed=True)[x].transform("mean"))

item_sold_df = item_sold_df.merge(df2, on=['country', 'tpnb'], how='left')

hier_cols_str = ['division',
                 'department','section',
                 'group','subgroup',
                 'product_name']

hier_cols_int = ['DIV_ID','DEP_ID',
                 'SEC_ID','GRP_ID',
                 'SGR_ID',]

item_sold_df[hier_cols_str] = item_sold_df[hier_cols_str].replace(np.nan,"no_data")
item_sold_df[hier_cols_int] = item_sold_df[hier_cols_int].replace(np.nan,0)

item_sold_df = rmf.optimize_objects(rmf.optimize_types(item_sold_df))

item_sold_df.to_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\SSH\item_sold_ce"
    )     
        
            
  







