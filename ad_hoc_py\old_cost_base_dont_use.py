# -*- coding: utf-8 -*-
"""
Created on Fri Nov  8 19:42:40 2024

@author: phrubos
"""

@timeit
def cost_base_on_TPNB(
    tpnb_store,
    tpnb_country,
    selected_tpn,
    repl_dataset_f,
    excel_inputs_f,
    act_model_cost_base,
    backstock_target,
    directory,
    stores,
):

    if (tpnb_store or tpnb_country) == True:

        # --- TPN Cost Base --- #
        ###########################################################################################################
        """
        In this case the model will calculate Cost Base on TPNB level using the Cost Base parquet file on "Actual version name"
        """
        ###########################################################################################################

        print("\nCost Base of selected TPNB calculation has been started.....\n")

        if tpnb_store == True:

            print('\n\nCalculation mode: "Store and Tpnb" level\n')

            a = pd.DataFrame()

            filter_tpn = pd.read_excel(directory / selected_tpn, "store_tpnb")
            dict_list = (
                filter_tpn.groupby("store")["tpnb"]
                .apply(lambda s: s.tolist())
                .to_dict()
            )

            # opening the Dataset based on conditions
            data = pq.read_table(
                directory / repl_dataset_f,
                filters=[("store", "in", [k for k in dict_list.keys()])],
            ).to_pandas()

            df = pd.DataFrame()
            for k, v in dict_list.items():
                a = data.loc[(data.store == k) & (data.tpnb.isin(v))]
                df = pd.concat([df, a])

            dep = df[["store", "dep"]].drop_duplicates().reset_index(drop=True)

            Repl_Dataset = pq.read_table(
                directory / repl_dataset_f,
                filters=[
                    ("store", "in", dep.store.unique().tolist()),
                    ("dep", "in", dep.dep.unique().tolist()),
                ],
            ).to_pandas()
            Repl_Dataset["is_capping_shelf"] = 0

            print("\nRepl_Dataset is loaded into the memory!\n")

        if tpnb_country == True:

            print('\n\nCalculation mode: "Country and Tpnb" level\n')

            a = pd.DataFrame()

            filter_tpn = pd.read_excel(directory / selected_tpn, "country_tpnb")
            dict_list = (
                filter_tpn.groupby("country")["tpnb"]
                .apply(lambda s: s.tolist())
                .to_dict()
            )
            for k, v in dict_list.items():
                b = pq.read_table(
                    directory / repl_dataset_f,
                    filters=[("tpnb", "in", v), ("country", "=", k)],
                ).to_pandas()

                a = pd.concat([a, b])

            dep = a[["country", "dep"]].drop_duplicates().reset_index(drop=True)

            Repl_Dataset = pq.read_table(
                directory / repl_dataset_f,
                filters=[
                    ("country", "in", dep.country.unique().tolist()),
                    ("dep", "in", dep.dep.unique().tolist()),
                ],
            ).to_pandas()

            Repl_Dataset["is_capping_shelf"] = 0

            print("\nRepl_Dataset is loaded into the memory!\n")

        def case_to_replenish_percent(
            backstock_target, directory, stores, Repl_Dataset
        ):

            Drivers = Repl_Dataset.copy()
            del Repl_Dataset
            
            ###### MU customization ########
            Drivers['pallet_capacity'] = np.where(Drivers.mu == 1, Drivers['pallet_capacity']/2, Drivers['pallet_capacity'])


            Drivers = Drivers[Drivers.store.isin(stores)]

            Drivers.drop(
                ["ownbrand", "checkout_stand_flag"], axis=1, inplace=True
            )  #'product_name',

            
            




            pro_to_del = Drivers.loc[
                (Drivers.dep == "PRO")
                & (Drivers.pmg != "PRO16")
                & (Drivers.pmg != "PRO19")
            ]["pmg"].unique()
            
            Drivers = Drivers.loc[
                (~Drivers.pmg.isin(pro_to_del)) & (Drivers.pmg != "HDL01")
            ]

            if len(Drivers) > 0:

                ###### shelf capacity customization ########

                avg_shelf_cap = (
                    Drivers.loc[Drivers.shelfCapacity > 1]
                    .groupby(["store", "pmg"], as_index=False, observed=True)
                    .agg(capacity_avg_pmg=("shelfCapacity", "mean"))
                )
                Drivers = Drivers.merge(avg_shelf_cap, on=["store", "pmg"], how="left")
                Drivers = Drivers.replace(np.nan, 0)
                Drivers["shelfCapacity"] = np.where(
                    (Drivers.shelfCapacity == 0) | (Drivers.shelfCapacity == 1),
                    Drivers["capacity_avg_pmg"],
                    Drivers["shelfCapacity"],
                )

                Drivers["stock"] = np.where(
                    ((Drivers.stock == 0) & (Drivers.sold_units > 0)),
                    Drivers.sold_units,
                    Drivers.stock,
                )  # If we have sales > 0, then stock cannot be equal 0, because: one touch + two touch + capping <> stock

                # ###### case capacity customization ########
                # Drivers["case_capacity"] = np.where(
                #     (Drivers.case_capacity == 0), 3, Drivers["case_capacity"]
                # )
                # Drivers["case_capacity"] = np.where(
                #     (Drivers.pmg.isin(["HDL06", "HDL07"]))
                #     & (Drivers["case_capacity"] < 10) & (Drivers.single_pick == 0),
                #     10,
                #     Drivers["case_capacity"],
                # )
                # Drivers["case_capacity"] = np.where(
                #     (Drivers.pmg.isin(["HDL55", "HDL35"]))
                #     & (Drivers["case_capacity"] < 3)& (Drivers.single_pick == 0),
                #     3,
                #     Drivers["case_capacity"],
                # )
                
                # Drivers["case_capacity"] = np.where(Drivers["single_pick"] > 0, 1, Drivers["case_capacity"])

                
                # ###### pallet capacity customization ########
                
                # avg_pallet_cap = (
                #     Drivers[(Drivers.pallet_capacity > 5)]
                #     .groupby(["store", "pmg"], as_index=False, observed=True)
                #     .agg(pallet_capacity_avg_pmg=("pallet_capacity", "mean"))
                #     .replace(np.nan, 5)
                # )
                # Drivers = Drivers.merge(
                #     avg_pallet_cap, on=["store", "pmg"], how="left"
                # ).replace(np.nan, 0)
                
                
                
                # Drivers["pallet_capacity"] = np.where(
                #     (Drivers.dep.isin(["HDL", "SFB", "SFM", "SFP"]))
                #     & (Drivers.pallet_capacity >= 0)
                #     & (Drivers.pallet_capacity <= 5),
                #     Drivers.pallet_capacity_avg_pmg,
                #     Drivers["pallet_capacity"],
                # )
                
                
                # Drivers["pallet_capacity"] = np.where(
                #     (Drivers.pmg.isin(["HDL06", "HDL07"]))
                #     & (Drivers["pallet_capacity"] < 100),
                #     150,
                #     Drivers["pallet_capacity"],
                # )

                ###### weight customization ########
                Drivers["heavy"] = (
                    Drivers.weight * Drivers.case_capacity
                )  # 1. Heavy & Light
                Drivers["weight_selector"] = (
                    Drivers.weight * Drivers.case_capacity
                )  # 1. Heavy & Light
                Drivers["heavy"] = np.where(Drivers.weight_selector >= 5, 1, 0).astype(
                    "int8"
                )
                Drivers["light"] = np.where(Drivers.weight_selector < 5, 1, 0).astype(
                    "int8"
                )
                Drivers.drop(["weight_selector"], axis=1, inplace=True)

                ###### Broken Items case cap ########
                Drivers["Broken Items"] = np.where(
                    Drivers["broken_case_flag"] == 1, Drivers["case_capacity"], 0
                ).astype("float32")

                Drivers["cases_delivered_on_sf"] = np.where(
                    (Drivers["broken_case_flag"] == 1),
                    Drivers["cases_delivered"] - 1,
                    Drivers["cases_delivered"],
                ).astype("float32")
                Drivers["cases_delivered_on_sf"] = np.where(
                    Drivers["cases_delivered_on_sf"] < 0,
                    0,
                    Drivers["cases_delivered_on_sf"],
                )
                Drivers.srp = np.where(
                    (Drivers.srp > 0) & (Drivers.broken_case_flag == 1), 0, Drivers.srp
                )  # 2. SRP / full_pallet / mu customizing
                Drivers.nsrp = np.where(
                    (Drivers.broken_case_flag == 1), 1, Drivers.nsrp
                )

                Drivers["backroom_cases_dotcom"] = np.where(
                    Drivers.backroom_flag == 1,
                    Drivers.sold_units_dotcom / Drivers.case_capacity,
                    0,
                ).astype("float32")
                Drivers["backroom_cases_dotcom"] = (
                    Drivers["backroom_cases_dotcom"]
                    .replace(np.nan, 0)
                    .replace([np.inf, -np.inf], 0)
                )
                Drivers["backroom_pallets"] = np.where(
                    Drivers["backroom_cases_dotcom"] > 0,
                    Drivers["backroom_cases_dotcom"] / Drivers.pallet_capacity,
                    0,
                ).astype("float32")
                Drivers["backroom_pallets"] = (
                    Drivers["backroom_pallets"]
                    .replace(np.nan, 0)
                    .replace([np.inf, -np.inf], 0)
                )

                Drivers["cases_delivered_on_sf"] = np.where(
                    Drivers["backroom_cases_dotcom"] > 0,
                    Drivers["cases_delivered_on_sf"] - Drivers["backroom_cases_dotcom"],
                    Drivers["cases_delivered_on_sf"],
                ).astype("float32")
                Drivers["cases_delivered_on_sf"] = np.where(
                    Drivers["cases_delivered_on_sf"] < 0,
                    0,
                    Drivers["cases_delivered_on_sf"],
                )
                Drivers["secondary_srp"] = np.where(
                    Drivers.sold_units > Drivers.shelfCapacity,
                    Drivers.stock - (Drivers.shelfCapacity / (1 - backstock_target)),
                    0,
                ).astype(
                    "float32"
                )  # 4. Secondary Displays -SRP
                Drivers["secondary_srp"] = (
                    Drivers["secondary_srp"]
                    .replace(np.nan, 0)
                    .replace([np.inf, -np.inf], 0)
                )
                Drivers.secondary_srp = np.where(
                    ((1 - Drivers.shelfCapacity / Drivers.stock) > 0.4),
                    Drivers.secondary_srp,
                    0,
                ).astype("float32")
                Drivers.secondary_srp = np.where(
                    Drivers.stock < Drivers.shelfCapacity, 0, Drivers.secondary_srp
                )
                Drivers.secondary_srp = np.where(
                    (Drivers.srp > 0)
                    | (Drivers.full_pallet > 0)
                    | (Drivers.mu > 0)
                    | (Drivers.split_pallet > 0)
                    | (Drivers.icream_nsrp > 0),
                    Drivers.secondary_srp / weekdays_to_divide,
                    0,
                )
                Drivers["secondary_nsrp"] = np.where(
                    Drivers.sold_units > Drivers.shelfCapacity,
                    Drivers.stock - (Drivers.shelfCapacity / (1 - backstock_target)),
                    0,
                ).astype(
                    "float32"
                )  # Secondary Displays -NSRP
                Drivers["secondary_nsrp"] = (
                    Drivers["secondary_nsrp"]
                    .replace(np.nan, 0)
                    .replace([np.inf, -np.inf], 0)
                )
                Drivers.secondary_nsrp = np.where(
                    (1 - (Drivers.shelfCapacity / Drivers.stock) > 0.4),
                    Drivers.secondary_nsrp,
                    0,
                )
                Drivers.secondary_nsrp = np.where(
                    Drivers.stock < Drivers.shelfCapacity, 0, Drivers.secondary_nsrp
                )
                Drivers.secondary_nsrp = np.where(
                    (Drivers.srp == 0)
                    & (Drivers.full_pallet == 0)
                    & (Drivers.mu == 0)
                    & (Drivers.split_pallet == 0)
                    & (Drivers.icream_nsrp == 0),
                    Drivers.secondary_nsrp / weekdays_to_divide,
                    0,
                )  # it contains full_pallet as well, is it okay?????

                ### here is the cases calculation part
                Drivers["shop_floor_capacity"] = (
                    Drivers.shelfCapacity + Drivers.secondary_nsrp + Drivers.secondary_srp
                ).astype(
                    "float32"
                )  # 6. One/Two touch

                # Drivers["stock_eod"] = Drivers.stock + Drivers.unit - Drivers.sold_units
                # Drivers["stock_eod"] = (
                #     Drivers["stock_eod"]
                #     .replace(np.nan, 0)
                #     .replace([np.inf, -np.inf], 0)
                # )

                # Drivers["cases_to_replenish"] = (
                #     Drivers.sold_units
                #     + Drivers[["stock_eod", "shop_floor_capacity"]].min(axis=1)
                #     - Drivers[["stock", "shop_floor_capacity"]].min(axis=1)
                # ) / Drivers.case_capacity
                # Drivers["cases_to_replenish"] = np.where(
                #     Drivers["cases_to_replenish"] < 0, 0, Drivers["cases_to_replenish"]
                # )
                
                
                Drivers["stock_morning"] = Drivers.stock - Drivers.unit + Drivers.sold_units + Drivers.sold_units_dotcom
                Drivers["stock_morning"] = (
                    Drivers["stock_morning"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
                )

                Drivers["cases_to_replenish"] = (
                    Drivers.sold_units
                    + Drivers[["stock", "shop_floor_capacity"]].min(axis=1)
                    - Drivers[["stock_morning", "shop_floor_capacity"]].min(axis=1)
                ) / Drivers.case_capacity
                Drivers["cases_to_replenish"] = np.where(
                    Drivers["cases_to_replenish"] < 0, 0, Drivers["cases_to_replenish"]
                )

                Drivers["cases_to_replenish_percent_to_Dep"] = Drivers[
                    "cases_to_replenish"
                ] / Drivers.groupby("dep", observed=True)[
                    "cases_to_replenish"
                ].transform(
                    "sum"
                )
                Drivers = (
                    Drivers.groupby(
                        ["country", "store", "division", "dep", "pmg", "tpnb"],
                        observed=True,
                    )["cases_to_replenish_percent_to_Dep"]
                    .sum()
                    .reset_index()
                )

                return Drivers

        case_to_replenish_percent = case_to_replenish_percent(
            backstock_target, directory, stores, Repl_Dataset
        )
        filter_tpn["flag"] = 1
        if tpnb_country == True:
            case_to_replenish_percent = (
                case_to_replenish_percent.merge(
                    filter_tpn[["country", "tpnb", "flag"]],
                    on=["country", "tpnb"],
                    how="left",
                )
                .query("flag == 1")
                .groupby(["country", "division", "dep"], observed=True)[
                    "cases_to_replenish_percent_to_Dep"
                ]
                .sum()
                .reset_index()
            )

            cost_base_div_total = pq.read_table(
                directory / act_model_cost_base,
                filters=[
                    (
                        "Division",
                        "in",
                        case_to_replenish_percent.division.unique().tolist(),
                    ),
                    (
                        "Country",
                        "in",
                        case_to_replenish_percent.country.unique().tolist(),
                    ),
                ],
            ).to_pandas()

            cost_base_div_total = cost_base_div_total.groupby(
                ["Country", "Division"], observed=True, as_index=False
            ).agg(
                total_weekly_hours=("hours", "sum"),
                total_yearly_GBP=("Yearly GBP", "sum"),
            )
            cost_base_dep = pq.read_table(
                directory / act_model_cost_base,
                filters=[
                    ("Dep", "in", case_to_replenish_percent.dep.unique().tolist()),
                    (
                        "Country",
                        "in",
                        case_to_replenish_percent.country.unique().tolist(),
                    ),
                ],
            ).to_pandas()
            cost_base_dep.columns = [x.lower() for x in cost_base_dep.columns]
            cost_base_dep = cost_base_dep.merge(
                case_to_replenish_percent, on=["country", "dep"], how="left"
            )
            cost_base_dep[
                ["costBase_hours_tpnb", "costBase_yearly_GBP_tpnb"]
            ] = cost_base_dep[["hours", "yearly gbp"]].multiply(
                cost_base_dep["cases_to_replenish_percent_to_Dep"], axis="index"
            )
            cost_base_dep.rename(
                columns={
                    "hours": "total_weekly_dep_hours",
                    "yearly gbp": "total_yearly_gbp",
                },
                inplace=True,
            )
            cost_base_dep = (
                cost_base_dep.groupby(
                    ["country", "dep", "activity_group", "suboperation"], observed=True
                )
                .agg(
                    {
                        "total_weekly_dep_hours": "sum",
                        "total_yearly_gbp": "sum",
                        "costBase_hours_tpnb": "sum",
                        "costBase_yearly_GBP_tpnb": "sum",
                    }
                )
                .reset_index()
            )
            cost_base_dep = cost_base_dep[cost_base_dep["costBase_hours_tpnb"] > 0]

            cost_base_dep_grouped = (
                cost_base_dep.groupby(["country", "activity_group"], observed=True)
                .agg(
                    {
                        "total_weekly_dep_hours": "sum",
                        "total_yearly_gbp": "sum",
                        "costBase_hours_tpnb": "sum",
                        "costBase_yearly_GBP_tpnb": "sum",
                    }
                )
                .reset_index()
            )

        if tpnb_store == True:
            case_to_replenish_percent = (
                case_to_replenish_percent.merge(
                    filter_tpn[["store", "tpnb", "flag"]],
                    on=["store", "tpnb"],
                    how="left",
                )
                .query("flag == 1")
                .groupby(["store", "dep"], observed=True)[
                    "cases_to_replenish_percent_to_Dep"
                ]
                .sum()
                .reset_index()
            )
            cost_base_dep = pq.read_table(
                directory / act_model_cost_base,
                filters=[
                    ("Dep", "in", case_to_replenish_percent.dep.unique().tolist()),
                    ("Store", "in", case_to_replenish_percent.store.unique().tolist()),
                ],
            ).to_pandas()

            cost_base_dep.columns = [x.lower() for x in cost_base_dep.columns]
            cost_base_dep = cost_base_dep.merge(
                case_to_replenish_percent, on=["store", "dep"], how="left"
            )
            cost_base_dep[
                ["costBase_hours_tpnb", "costBase_yearly_GBP_tpnb"]
            ] = cost_base_dep[["hours", "yearly gbp"]].multiply(
                cost_base_dep["cases_to_replenish_percent_to_Dep"], axis="index"
            )
            cost_base_dep.rename(
                columns={
                    "hours": "total_weekly_dep_hours",
                    "yearly gbp": "total_yearly_gbp",
                },
                inplace=True,
            )
            cost_base_dep = (
                cost_base_dep.groupby(
                    ["store", "dep", "activity_group", "suboperation"], observed=True
                )
                .agg(
                    {
                        "total_weekly_dep_hours": "sum",
                        "total_yearly_gbp": "sum",
                        "costBase_hours_tpnb": "sum",
                        "costBase_yearly_GBP_tpnb": "sum",
                    }
                )
                .reset_index()
            )
            cost_base_dep = cost_base_dep[cost_base_dep["costBase_hours_tpnb"] > 0]

            cost_base_dep_grouped = (
                cost_base_dep.groupby(["store", "activity_group"], observed=True)
                .agg(
                    {
                        "total_weekly_dep_hours": "sum",
                        "total_yearly_gbp": "sum",
                        "costBase_hours_tpnb": "sum",
                        "costBase_yearly_GBP_tpnb": "sum",
                    }
                )
                .reset_index()
            )

    return cost_base_dep, cost_base_dep_grouped, cost_base_div_total


def CostBaseFormatter(
    folder,
    file_name,
    costbase_tpnb,
    cost_base_dep_grouped,
    act_groups_blocks,
    cost_base_div_total,
):

    with pd.ExcelWriter(folder / file_name, engine="xlsxwriter") as writer:

        cost_base_dep_groups = cost_base_dep_grouped.merge(
            pd.read_excel(folder / act_groups_blocks, "ActGroup_groups"),
            on="activity_group",
            how="left",
        )
        cost_base_dep_groups = (
            cost_base_dep_groups.groupby(["country", "activity_groups"], observed=True)
            .agg(
                {
                    "total_weekly_dep_hours": "sum",
                    "total_yearly_gbp": "sum",
                    "costBase_hours_tpnb": "sum",
                    "costBase_yearly_GBP_tpnb": "sum",
                }
            )
            .reset_index()
            .sort_values(by=["costBase_yearly_GBP_tpnb"], ascending=False)
            .astype(
                {
                    "total_weekly_dep_hours": "int64",
                    "total_yearly_gbp": "int64",
                    "costBase_hours_tpnb": "int64",
                    "costBase_yearly_GBP_tpnb": "int64",
                }
            )
        )

        costbase_tpnb.to_excel(writer, sheet_name="CostBase_subop_level", index=False)
        workbook = writer.book
        worksheet = writer.sheets["CostBase_subop_level"]

        cost_base_dep_groups.rename(
            columns={
                "total_weekly_dep_hours": f"total_weekly_{costbase_tpnb.dep.unique().tolist()}_hours",
                "total_yearly_gbp": f"total_{costbase_tpnb.dep.unique().tolist()}_yearly_gbp",
            },
            inplace=True,
        )
        cost_base_dep_groups.to_excel(writer, sheet_name="ActGroup_Chart", index=False)

        worksheet2 = writer.sheets["ActGroup_Chart"]

        len_of_categories = len(cost_base_dep_groups.activity_groups.tolist()) + 1

        country_name = cost_base_dep_groups.country.unique().tolist()

        format_diff_gbp = workbook.add_format(
            {
                "num_format": "#,##0",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 0,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "fg_color": "#005581",
            }
        )
        format_diff_hours = workbook.add_format(
            {
                "num_format": "#,##0",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 0,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "fg_color": "#005581",
            }
        )

        header_format = workbook.add_format(
            {
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "black",
                "bold": 1,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
            }
        )

        total_Div = [
            f"{cost_base_div_total.Division.unique().tolist()[0]}_weekly_hours:",
            f"{cost_base_div_total.Division.unique().tolist()[0]}_yearly_GBP:",
            "Selected TPNBs CostBase_weekly_hours:",
            "Selected TPNBs CostBase_yearly_GBP:",
        ]

        worksheet2.write_row("C19", total_Div, header_format)
        worksheet2.write_column(
            "C20", cost_base_div_total.total_weekly_hours.values, format_diff_hours
        )
        worksheet2.write_column(
            "D20", cost_base_div_total.total_yearly_GBP.values, format_diff_gbp
        )

        worksheet2.write_column(
            "E20", [cost_base_dep_groups.costBase_hours_tpnb.sum()], format_diff_hours
        )
        worksheet2.write_column(
            "F20",
            [cost_base_dep_groups.costBase_yearly_GBP_tpnb.sum()],
            format_diff_gbp,
        )

        # Create a new column chart.
        #
        chart1 = workbook.add_chart({"type": "column"})

        # # Configure the first series.
        # chart1.add_series({
        #     'name':       '=ActGroup_Chart!$D$1',
        #     'categories': f'=ActGroup_Chart!$B$2:$B${len_of_categories}',
        #     'values':     f'=ActGroup_Chart!$D$2:$D${len_of_categories}',
        # })

        # Configure a second series. Note use of alternative syntax to define ranges.
        chart1.add_series(
            {
                "name": "CostBase of selected TPNBs",
                "categories": f"=ActGroup_Chart!$B$2:$B${len_of_categories}",
                "values": f"=ActGroup_Chart!$F$2:$F${len_of_categories}",
                "data_labels": {"value": True},
            }
        )

        # Add a chart title and some axis labels.
        chart1.set_title({"name": f"CostBase of the Category {country_name[0]}"})
        chart1.set_x_axis({"name": "Activity Groups"})
        chart1.set_y_axis({"name": "Yearly GBP"})

        # Set an Excel chart style.
        chart1.set_style(11)

        # Insert the chart into the worksheet (with an offset).
        worksheet2.insert_chart(
            "H2", chart1, {"x_scale": 2, "y_scale": 1.5}
        )  # {'x_offset': 450, 'y_offset': 200}

        dfs = {
            "CostBase_subop_level": costbase_tpnb,
            "ActGroup_Chart": cost_base_dep_groups,
        }

        for sheetname, df in dfs.items():
            df.to_excel(writer, sheet_name=sheetname, index=False)  # send df to writer
            worksheet = writer.sheets[sheetname]  # pull worksheet object
            for idx, col in enumerate(df):  # loop through all columns
                series = df[col]
                max_len = (
                    max(
                        (
                            series.astype(str).map(len).max(),  # len of largest item
                            len(str(series.name)),  # len of column name/header
                        )
                    )
                    + 1.5
                )  # adding a little extra space
                worksheet.set_column(idx, idx, max_len)  # set column width

        for x in [worksheet, worksheet2]:
            x.set_zoom(90)

