# -*- coding: utf-8 -*-
"""
Created on Mon Jan  9 08:46:58 2023

@author: phrubos
"""

import pandas as pd
import numpy as np
from pathlib import Path
import pyodbc

conn = pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()

start = "'f2022w40'"
end = "'f2022w45'"


dataset = pd.read_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\inputs\Repl_Dataset_2022_split_pallet_verz.parquet"
)

tpnb = dataset.tpnb.unique().tolist()


collector = pd.DataFrame()

index = 0
indicator = 1
s = list()
for x in tpnb:

    s.append(str(x))
    index += 1

    if index % 50000 == 0 or (len(tpnb) == index):

        tpnb_string = ",".join(s)

        sql = """ SELECT CAST(stores.dmst_store_code AS INT) AS store,
                 CAST(mstr.slad_tpnb AS INT) AS tpnb,
                 MAX(stock.slstks_price) as item_price
                 FROM dw.sl_stocks stock
                 JOIN dm.dim_stores stores ON stock.slstks_dmst_id = stores.dmst_store_id AND stock.slstks_cntr_id = stores.cntr_id
                 LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = stock.part_col
                 JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = stock.slstks_dmat_id AND mstr.cntr_id = stock.slstks_cntr_id
                 WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
                 AND slad_tpnb in ({tpnb})
                 GROUP BY stores.dmst_store_code,  mstr.slad_tpnb
                 ORDER BY stores.dmst_store_code,  mstr.slad_tpnb
                
                """.format(
            tpnb=tpnb_string, start=start, end=end
        )

        collector = pd.concat([collector, pd.read_sql(sql, conn)])
        print(f"Done with part: {indicator}")
        indicator += 1

        s = list()

f = collector.merge(
    dataset[["country", "store", "format"]].drop_duplicates(), on="store", how="left"
)
f["avg_format_item_price_update"] = f.groupby(["country", "tpnb", "format"])[
    "item_price"
].transform("mean")

dataset = dataset.merge(
    f[["country", "format", "tpnb", "avg_format_item_price_update"]].drop_duplicates(),
    on=["country", "format", "tpnb"],
    how="left",
)
dataset.loc[
    dataset["avg_format_item_price_update"].isnull(), "avg_format_item_price_update"
] = 0

dataset.loc[
    dataset["avg_format_item_price_update"] > dataset["item_price"], "item_price"
] = dataset["avg_format_item_price_update"]
dataset.drop("avg_format_item_price_update", axis=1, inplace=True)

# dataset.to_parquet(r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\inputs\Repl_Dataset_2022_new_14w_27w_promo_flag_item_price_update_w40_45.parquet", compression='gzip')
