import sys
import os
import pandas as pd
from pathlib import Path
import numpy as np

def SRD_to_opsdev_old(opsdev, directory, excel_inputs_f):
    foil_calc = pd.read_excel(directory / excel_inputs_f, sheet_name='foil_calc', usecols=['Country',
                                                                                            'level4',
                                                                                            'Foil Opening Reduction Opportunity',
                                                                                            'Proposed Opening %',
                                                                                            'Foil Replen Min Facing',
                                                                                            'SRP opening reduction opportunity',
                                                                                            'extra disassemble %'],
                              dtype={'level4': np.int64})
    
    # foil_calc.rename(columns={"Proposed Opening %":"foil"}, inplace=True)
    
    # foil_calc = foil_calc[foil_calc['SRP opening reduction opportunity'] == 1]
    
    
    
    opsdev_foil = opsdev.copy()
    
    opsdev_foil['level4'] = [str(int(p1)) + str(int(p2)) + str(int(p3)) + str(int(p4)) for p1, p2, p3, p4 in zip(opsdev_foil['Division'],
                                                                                             opsdev_foil['Department'],
                                                                                             opsdev_foil['Section'],
                                                                                             opsdev_foil['Group_'])]
    opsdev_foil['level4'] = opsdev_foil['level4'].astype("int64")
    
    opsdev_foil = opsdev_foil.merge(foil_calc, on=['Country', 'level4'], how='left')
    
    
    cond=[(opsdev_foil['Foil Opening Reduction Opportunity'] == 1)
          & (opsdev_foil['merchstyle_string'] == 'Unit')
          & (opsdev_foil['Pallet_info'] == ''),
          
          (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)
          & (opsdev_foil['merchstyle_string'] == 'Tray')
          & (opsdev_foil['Pallet_info'] == ''),
          
          (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)
          &  (opsdev_foil['merchstyle_string'] == 'Case')
          & (opsdev_foil['Pallet_info'] == '')]
    
    result = [opsdev_foil['Position_HFacing'],
              opsdev_foil['Position_HFacing'] * opsdev_foil['TrayNumberWide'],
              opsdev_foil['Position_HFacing'] * opsdev_foil['CaseNumberWide']]
    
    
    owise= (opsdev_foil['Position_HFacing'] * opsdev_foil['merchstyle_ID'])
            
    
    opsdev_foil['for_foil_check'] = np.select(cond,result,owise)
    
    opsdev_foil['foil'] = np.where((opsdev_foil['Foil Replen Min Facing'] < opsdev_foil['for_foil_check'])
                                   & (opsdev_foil['Pallet_info'] == ''), 
                                   opsdev_foil["Proposed Opening %"], 0)
    
    
    
    for x in [opsdev, opsdev_foil]:
    
        country_cond = [
            x["Country"] == "HU",
            x["Country"] == "SK",
            x["Country"] == "CZ",
        ]
    
        store_code_create = [
            str(4) + x.Store_Number.astype(str),
            str(2) + x.Store_Number.astype(str),
            str(1) + x.Store_Number.astype(str),
        ]
        
        x["Store_Number"] = np.select(country_cond, store_code_create, 0)
        
        
        
    
    opsdev_foil.rename(
        columns={
            "Country":  "country",
            "Store_Number": "store",
            "Product_id": "tpnb"}, inplace=True)
    
    opsdev_foil['store'] = opsdev_foil['store'].astype("int32")
    opsdev_foil = opsdev_foil[['Division',
                               'Department',
                               'Section',
                               'Group_',
                               'level4',
                               'country',
                               'store',
                               'tpnb',
                               "Product_Name",
                               # "SRP opening reduction opportunity",
                               "Pallet_info",
                               'Position_HFacing',
                               'merchstyle_ID',
                               'merchstyle_string',
                               'TrayNumberWide',
                               'Foil Replen Min Facing',
                               'foil']].query("foil > 0").drop_duplicates()
    
    opsdev_foil = opsdev_foil[['country','store','level4', 'tpnb', 'foil']].drop_duplicates()
    foil_calc.rename(columns={'Country':'country'}, inplace=True)
    opsdev_foil = opsdev_foil.merge(foil_calc[['country', 'level4', 'SRP opening reduction opportunity','extra disassemble %']],
                                    on=['country', 'level4'], how='left')
    
      
        
    # opsdev
    
    
    
    
    # list of repl_type
    srp = ["Tray", "Case"]
    nsrp = ["Unit", "Display", "Alternate", "Loose", "Log Stack"]
    pallet_info = ["Half_Pallet", "Pallet", "Split_Pallet"]
    
    # create columns for repl_type
    
    
    opsdev["full_pallet"] = np.where(opsdev.Pallet_info == "Pallet", 1, 0)
    opsdev["mu"] = np.where(opsdev.Pallet_info == "Half_Pallet", 1, 0)
    opsdev["split_pallet"] = np.where(opsdev.Pallet_info == "Split_Pallet", 1, 0)
    opsdev["nsrp"] = np.where(
        (opsdev.merchstyle_string.isin(nsrp)) & (~opsdev.Pallet_info.isin(pallet_info)),
        1,
        0,
    )
    opsdev["srp"] = np.where(opsdev.merchstyle_string.isin(srp), 1, 0)
    opsdev["srp"] = np.where((opsdev.Pallet_info == "Split_Pallet"), 0, opsdev.srp)
    opsdev["srp"] = np.where(
        (opsdev.Pallet_info == "Pallet") | (opsdev.Pallet_info == "Half_Pallet"),
        0,
        opsdev.srp,
    )
    
    opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]] = opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]].astype("int64")
    
    opsdev['icream_nsrp'] = np.where((opsdev['Division'] == 1)
                                    & (opsdev['Department'] == 17)
                                    & (opsdev['Section'] == 1702)
                                    & (opsdev.merchstyle_string.isin(['Loose', 'Alternate']))
                                    , 1, 0)
    
    for x in [ "srp","nsrp","mu","full_pallet", "split_pallet"]:
        
        opsdev[x] = np.where(opsdev['icream_nsrp'] == 1, 0, opsdev[x] )
    
    
    # rename columns
    
    opsdev.rename(
        columns={
            "Country":  "country",
            "Store_Number": "store",
            "Product_id": "tpnb",
            "Product_Name": "product_name",
            "Displaygroup_description": "display_group",
            "Displaygroup": "drg"
        },
        inplace=True,
    )
    opsdev["checkout_stand_flag"] = np.where(
        opsdev["display_group"].str.lower().str.contains("checkout"), 1, 0
    )
    opsdev["clipstrip_flag"] = np.where(
        opsdev["display_group"].str.lower().str.contains("clipstrip"), 1, 0
    )
    opsdev["backroom_flag"] = np.where(opsdev["drg"].isin(["Z2D", "L1N"]), 1, 0)
    opsdev["store"] = opsdev["store"].astype(float).astype(int)
    opsdev["tpnb"] = (
        pd.to_numeric(opsdev.tpnb, errors="coerce").fillna(0).astype(float).astype(int)
    )
    checkout_stand = opsdev[opsdev.checkout_stand_flag == 1][
        ["store", "tpnb"]
    ].drop_duplicates()
    checkout_stand["checkout_stand_flag"] = int(1)
    clipstrip_flag = opsdev[opsdev.clipstrip_flag == 1][["store", "tpnb"]].drop_duplicates()
    clipstrip_flag["clipstrip_flag"] = int(1)
    backroom_flag = opsdev[opsdev.backroom_flag == 1][["store", "tpnb"]].drop_duplicates()
    backroom_flag["backroom_flag"] = int(1)
    
    opsdev = opsdev[
        [
            "country",
            "store",
            # "level4",
            "tpnb",
            "product_name",
            "srp",
            "nsrp",
            "mu",
            "full_pallet",
            "split_pallet",
            "icream_nsrp"
    
        ]
    ]
    opsdev = opsdev.merge(checkout_stand, on=["store", "tpnb"], how="left")
    opsdev["checkout_stand_flag"].replace(np.nan, 0, inplace=True)
    opsdev = opsdev.merge(clipstrip_flag, on=["store", "tpnb"], how="left")
    opsdev["clipstrip_flag"].replace(np.nan, 0, inplace=True)
    opsdev = opsdev.merge(backroom_flag, on=["store", "tpnb"], how="left")
    opsdev["backroom_flag"].replace(np.nan, 0, inplace=True)
    
    # get final opsdev looking
    
    
    opsdev = opsdev.drop_duplicates()
    
    # filter duplicates and form the dataframe again
    
    grouped_opsdev = (
        opsdev.groupby(["country",
                        "store",
                        # "level4", 
                        "tpnb",
                        "product_name", 
                        ], observed = True)
        .agg(
            {
                "srp": "sum",
                "nsrp": "sum",
                "mu": "sum",
                "full_pallet": "sum",
                "split_pallet": "sum",
                "icream_nsrp": "sum",
                "checkout_stand_flag": "sum",
                "clipstrip_flag": "sum",
                "backroom_flag": "sum",
            }
        )
        .reset_index()
    )
    grouped_opsdev["total"] = (
        grouped_opsdev.srp
        + grouped_opsdev.nsrp
        + grouped_opsdev.mu
        + grouped_opsdev.full_pallet
        + grouped_opsdev.split_pallet
    )
    grouped_opsdev["full_pallet"] = np.where(
        (grouped_opsdev.full_pallet == 1) & (grouped_opsdev.total > 1),
        1,
        grouped_opsdev.full_pallet,
    )
    grouped_opsdev["mu"] = np.where(
        (grouped_opsdev.total > 1) & (grouped_opsdev.full_pallet == 1), 0, grouped_opsdev.mu
    )
    grouped_opsdev["split_pallet"] = np.where(
        (grouped_opsdev.total > 1) & (grouped_opsdev.full_pallet == 1)
        | (grouped_opsdev.mu == 1),
        0,
        grouped_opsdev.split_pallet,
    )
    grouped_opsdev["nsrp"] = np.where(
        (grouped_opsdev.total > 1)
        & (
            (grouped_opsdev.full_pallet == 1)
            | (grouped_opsdev.mu == 1)
            | grouped_opsdev.split_pallet
            == 1
        )
        | (grouped_opsdev.srp == 1)
        | (grouped_opsdev.icream_nsrp == 1),
        0,
        grouped_opsdev.nsrp,
    )
    grouped_opsdev["srp"] = np.where(
        (grouped_opsdev.total > 1)
        & (
            (grouped_opsdev.full_pallet == 1)
            | (grouped_opsdev.mu == 1)
            | grouped_opsdev.split_pallet
            == 1
        ),
        0,
        grouped_opsdev.srp,
    )
    
    grouped_opsdev["icream_nsrp"] = np.where(
        (grouped_opsdev.total > 1)
        & (
            (grouped_opsdev.full_pallet == 1)
            | (grouped_opsdev.mu == 1)
            | (grouped_opsdev.split_pallet == 1)
            | (grouped_opsdev.srp == 1)
        ),
        0,
        grouped_opsdev.icream_nsrp,
    )
    
    
    
    grouped_opsdev["total"] = (
        grouped_opsdev.srp
        + grouped_opsdev.nsrp
        + grouped_opsdev.mu
        + grouped_opsdev.full_pallet
        + grouped_opsdev.split_pallet
        + grouped_opsdev.icream_nsrp
    )
    
    
    # check where is duplicates
    dupl_rows = grouped_opsdev[grouped_opsdev.total > 1].shape[0]
    print(f"We have: {dupl_rows} rows duplicated")
    
    
    # final dataframe
    grouped_opsdev.drop(columns={"total"}, inplace=True)
    opsdev = grouped_opsdev.copy()
    opsdev["checkout_stand_flag"] = np.where(opsdev["checkout_stand_flag"] > 0, 1, 0)
    opsdev["clipstrip_flag"] = np.where(opsdev["clipstrip_flag"] > 0, 1, 0)
    opsdev["backroom_flag"] = np.where(opsdev["backroom_flag"] > 0, 1, 0)
    
    
    # foil_calc.rename(columns={"Country": "country"}, inplace=True)
    
    
    
    # opsdev = opsdev.merge(opsdev_foil, on=["store", "tpnb"], how="left")
    # opsdev["foil"] = opsdev["foil"].fillna(1)
    # opsdev = opsdev.drop_duplicates()

    return opsdev, opsdev_foil



opsdev = pd.read_csv(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\10-01-2024\CE_JDA_SRD.csv.gzip", compression = "gzip")

directory = (
    Path(__file__).parent if "__file__" in locals() else Path.cwd()
).parent.parent

excel_inputs_f = "inputs/Repl/Stores_Inputs_2024_Q1_wo_prepacked_hours_fluct_cust.xlsx" #_12001_back






a, b =  SRD_to_opsdev_old(opsdev, directory, excel_inputs_f)



modelDataSet_as_is = pd.read_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\Repl_Dataset_2024_SFP01_kristalycukor"
    )

opsdev = a.copy()

opsdev.columns = (opsdev.iloc[:, :4].columns.tolist()
                  + opsdev.iloc[:, 4:10].add_suffix("_new").columns.tolist()
                  + opsdev.iloc[:,10:].columns.tolist())

opsdev.drop("product_name", axis=1, inplace=True)


modelDataSet_as_is_ = modelDataSet_as_is.merge(opsdev[opsdev.iloc[:,:9].columns.tolist()], on=['country',
                                                                                              'store', 
                                                                                              'tpnb'], how='left')

for x, y in zip(["srp_new", "nsrp_new", "mu_new", "full_pallet_new", "split_pallet_new", "icream_nsrp_new"], ['srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', "icream_nsrp"]):
    cond = [modelDataSet_as_is_[x].notnull()]
    result = [modelDataSet_as_is_[x]]
    modelDataSet_as_is_[y] = np.select(cond, result, modelDataSet_as_is_[y])
    modelDataSet_as_is_.drop(x, axis=1, inplace=True)

