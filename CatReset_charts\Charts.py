import pandas as pd
from pathlib import Path
import pyarrow.parquet as pq
import plotly.express as px
import warnings
import plotly.graph_objects as go
import numpy as np
from plotly.subplots import make_subplots
import time
import plotly.io as pio
import io
from PIL import Image
import pyodbc
import re
import os
from functools import wraps

warnings.filterwarnings("ignore")
pd.set_option('display.max_columns', None)

# Python decorator to measure execution time of Functions
def timeit(func):
    @wraps(func)
    def timeit_wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        total_time = end_time - start_time
        if func.__name__ == "Replenishment_Model_Running":
            print(
                f" \n{func.__name__} is done! Elapsed time (sec & min): {total_time:.2f} sec which is {total_time/60:.2f} min"
            )
        else:
            print(f" \n{func.__name__} is done! Elapsed time: {total_time:.1f} sec which is {total_time/60:.1f} min")
        return result

    return timeit_wrapper

def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', 
                            separator=None, auto_open=False):
    with open(html_fname, 'w') as f:
        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))
        for fig in plotly_figs[1:]:
            if separator:
                f.write(separator)
            f.write(fig.to_html(full_html=False, include_plotlyjs=False))

    if auto_open:
        import pathlib, webbrowser
        uri = pathlib.Path(html_fname).absolute().as_uri()
        webbrowser.open(uri)




@timeit
def tpnb_charts(periods, period, act_dataset, store_list_path,  place_to_save, category_reset_df):
    
    
    print("\nTPNB Charts running.....\n")
    
    conn = pyodbc.connect(
        "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
    )
    



    from_last_year = []

    # Define the desired order of periods
    desired_order = ['LY_p11', 'LY_p12', 'p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']

    repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]

    store_list = pd.read_excel(store_list_path)
    store_list.columns = [x.lower() for x in store_list.columns]

    store_list = store_list['store'].unique().tolist()

    df_ce = pd.DataFrame()
    
    for x, y in zip(periods, period ):
        
        df = pd.read_parquet(x)
        
        df = df[df.tpnb > 0]
        
        df = df[df.store.isin(store_list)]

        condition = [
            df["store"].astype(str).str.match("^1"),
            df["store"].astype(str).str.match("^2"),
            df["store"].astype(str).str.match("^4"),
        ]
        results = ["CZ", "SK", "HU"]
        df["country"] = np.select(condition, results, 0)
            

            
            
        df = df[['country', 'store','tpnb','product_name', "srp", "nsrp", "full_pallet", "mu", "split_pallet"]]
            

            

        
        # for r in repl_types:
        #     df[r] = np.where(df[r] == 1, df.sold_units, 0)
        
        df['period'] = y
        
        

        df = df.groupby(['country', 'tpnb', 'product_name', 'period'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
        
        
        df_ce = pd.concat([df_ce, df])
        
        print(f"Done with processing {y}!")
        
    act_dataset_df = pd.read_parquet(act_dataset)
    
    def ownbrand_bool_func(ownbrand_bool, act_dataset_df, df_ce):
        

        
    
        if ownbrand_bool:
            act_dataset_df = act_dataset_df[act_dataset_df.ownbrand == "Y"]
    
        df_ce = df_ce.merge(act_dataset_df[['country', 'tpnb', 'pmg', 'Category name']].drop_duplicates(), on=['country', 'tpnb'], how='left')
        df_ce.loc[df_ce['Category name'] == "PRODUCE + DRN + FRESH JUICES (spring 2024)", 'Category name'] = "PRODUCE + DRN + FRESH JUICES"
   
        missing_df = df_ce[df_ce.pmg.isnull()]
        products = missing_df.groupby(["country"])['tpnb'].apply(lambda s: s.tolist()).to_dict()
        
        for key, value in products.items():
            #print value
            print(key, len([item for item in value if item]))
    
        pmg_tpn = pd.DataFrame()
        
        with pyodbc.connect(
            "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
        ) as conn:
        
            if not ownbrand_bool:
                
        
                for k, v in products.items():
                                    
                    s = list()
                    
                    for x in v:
                                
                        s.append(str(x))
                        tpnbs = ",".join(s)
                    
                    sql = """ SELECT 
                                    tpns.cntr_code AS country,
                                    cast(tpns.slad_tpnb AS INT) AS tpnb,
                                    /*tpns.dmat_div_des_en AS division,
                                    cast(tpns.dmat_div_code as INT) as DIV_ID,
                                    tpns.dmat_dep_des_en AS department,
                                    cast(tpns.dmat_dep_code as INT) as DEP_ID,
                                    tpns.dmat_sec_des_en AS section,
                                    cast(tpns.dmat_sec_code as INT) as SEC_ID,
                                    tpns.dmat_grp_des_en AS group,
                                    cast(tpns.dmat_grp_code as INT) as GRP_ID,
                                    tpns.dmat_sgr_des_en AS subgroup,
                                    cast(tpns.dmat_sgr_code as INT) as SGR_ID,*/
        
                                    hier.pmg as pmg
                            FROM
                                    DM.dim_artgld_details tpns
                                    
                            LEFT JOIN tesco_analysts.hierarchy_spm hier
                            ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0")
                            AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0")
                            AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0")
                            AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0")
                            AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
                            WHERE 
                                    slad_tpnb in ({tpnbs}) 
                            AND     
                                    cntr_code = '{k}' 
                            AND     
                                    dmat_sgr_des_en <> "Do not use"
                            GROUP BY 
                                    cntr_code,
                                    slad_tpnb,
                                    /*dmat_div_des_en,
                                    dmat_div_code,
                                    dmat_dep_des_en,
                                    dmat_dep_code,
                                    dmat_sec_des_en,
                                    dmat_sec_code,
                                    dmat_grp_des_en,
                                    dmat_grp_code,
                                    dmat_sgr_des_en,
                                    dmat_sgr_code,*/
                                    hier.pmg
                                    
                                    """.format(
                        tpnbs=tpnbs, k=k
                    )
        
                    pmg_tpn = pd.concat([pmg_tpn, pd.read_sql(sql, conn)])
                    
                    
            if ownbrand_bool:
                
                for k, v in products.items():
                                    
                    s = list()
                    
                    for x in v:
                                
                        s.append(str(x))
                        tpnbs = ",".join(s)
                    
                    sql = """ SELECT 
                                    tpns.cntr_code AS country,
                                    cast(tpns.slad_tpnb AS INT) AS tpnb,
                                    /*tpns.dmat_div_des_en AS division,
                                    cast(tpns.dmat_div_code as INT) as DIV_ID,
                                    tpns.dmat_dep_des_en AS department,
                                    cast(tpns.dmat_dep_code as INT) as DEP_ID,
                                    tpns.dmat_sec_des_en AS section,
                                    cast(tpns.dmat_sec_code as INT) as SEC_ID,
                                    tpns.dmat_grp_des_en AS group,
                                    cast(tpns.dmat_grp_code as INT) as GRP_ID,
                                    tpns.dmat_sgr_des_en AS subgroup,
                                    cast(tpns.dmat_sgr_code as INT) as SGR_ID,*/
                                    own_brand as ownbrand,
        
                                    hier.pmg as pmg
                            FROM
                                    DM.dim_artgld_details tpns
                                    
                            LEFT JOIN tesco_analysts.hierarchy_spm hier
                            ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0")
                            AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0")
                            AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0")
                            AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0")
                            AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
                            WHERE 
                                    slad_tpnb in ({tpnbs})
                            AND
                                    own_brand = 'Y'
                            AND     
                                    cntr_code = '{k}' 
                            AND     
                                    dmat_sgr_des_en <> "Do not use"
                            GROUP BY 
                                    cntr_code,
                                    slad_tpnb,
                                    /*dmat_div_des_en,
                                    dmat_div_code,
                                    dmat_dep_des_en,
                                    dmat_dep_code,
                                    dmat_sec_des_en,
                                    dmat_sec_code,
                                    dmat_grp_des_en,
                                    dmat_grp_code,
                                    dmat_sgr_des_en,
                                    dmat_sgr_code,*/
                                    own_brand,
                                    hier.pmg
                                    
                                    """.format(
                        tpnbs=tpnbs, k=k
                    )
        
                    pmg_tpn = pd.concat([pmg_tpn, pd.read_sql(sql, conn)])
                    
      
        missing_df.drop("pmg", axis=1, inplace=True)
        missing_df = missing_df.merge(pmg_tpn[['country','tpnb', 'pmg']].drop_duplicates(), on=['country','tpnb'], how='left')
        df_ce = pd.concat([df_ce[df_ce.pmg.notnull()], missing_df])
            
    
        df_ce['Category name'] = np.where(df_ce['Category name'].isnull(), "no_data", df_ce['Category name'])
        
        # Create a dictionary to store pmg values and their corresponding most frequent Category name
        pmg_categories = {}
    
        # Iterate over unique pmg values
        for pmg_value in df_ce['pmg'].unique():
            # Filter rows where pmg is equal to current pmg_value and Category name is not "no_data"
            filtered_rows = df_ce[(df_ce['pmg'] == pmg_value) & (df_ce['Category name'] != 'no_data') & (df_ce['pmg'].str[:3] != 'HDL')]
            
            # Check if there are any non-"no_data" rows matching the conditions
            if not filtered_rows.empty:
                # Find the most frequent non-"no_data" Category name for the current pmg_value
                most_frequent_category = filtered_rows['Category name'].mode().iloc[0]
                
                # Store the most frequent non-"no_data" Category name for the current pmg_value in the dictionary
                pmg_categories[pmg_value] = most_frequent_category
                    
        # Update the 'no_data' rows in the DataFrame based on the mapping
        mask = df_ce['Category name'] == 'no_data'
        df_ce.loc[mask, 'Category name'] = df_ce.loc[mask, 'pmg'].map(pmg_categories)
        # df_ce['Category name'] = df_ce.apply(lambda row: pmg_categories.get(row['pmg'], row['Category name']) if row['Category name'] == 'no_data' else row['Category name'], axis=1)
        df_ce['Category name'] = np.where(df_ce.pmg.str[:3] == 'FRZ', 'FROZEN', df_ce['Category name'] )
        df_ce['Category name'] = np.where((df_ce['Category name'] == 'no_data')&(df_ce.pmg.str[:3] == 'HDL'), 'GM', df_ce['Category name'] )
    
    
        df_ce.rename(columns={"Category name":"category"}, inplace=True)
    
    
    
    
        cat_df = pd.read_parquet(category_reset_df)[['country','category', 'DIV_DESC']].drop_duplicates()
    
        df_ce = df_ce.merge(cat_df, on=['country', 'category'], how='left')
        

    
        df_ce['DIV_DESC'] = np.where((df_ce['DIV_DESC'].isnull())&(df_ce.pmg.str[:3] == 'HDL'), 'GM', df_ce['DIV_DESC'] )
        df_ce['DIV_DESC'] = np.where((df_ce['DIV_DESC'].isnull())&(df_ce.pmg.str[:3] == 'FRZ'), 'Fresh_Froz_Food', df_ce['DIV_DESC'] )
    
        df_ce_total = df_ce.groupby([ 'DIV_DESC','category','pmg','tpnb', 'product_name', 'period'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
        df_ce_total['country'] = 'CE'
    
        df_ce = pd.concat([df_ce, df_ce_total])
    
        df_ce = df_ce[df_ce['category'] != 'no_data']
    
        # df_ce = df_ce[df_ce.category.notnull()]
        

    
    
    
                
        need_cols = [x for x in df_ce.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]  
    
        # =============================================================================
        # Charts
        # =============================================================================
        df_catres_sum = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country','pmg', 'tpnb', 'product_name', 'category', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()
    
    
    
        # non_food_food = df_ce[df_ce['DIV_DESC'].isin(['NonFood Grocery', 'Food Grocery'])]
        # non_food_food['division'] = np.where(non_food_food['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)', 'Grocery (Food)')
    
        df_catres_sum_div = df_ce.copy()
    
        # df_catres_sum_div = pd.concat([df_catres_sum_div, non_food_food])
        # df_catres_sum_div['division'] = np.where(df_catres_sum_div['division'] == 'Grocery', 'Grocery (total)', df_catres_sum_div['division'])
    
        df_catres_sum_div = df_catres_sum_div.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'DIV_DESC','pmg','tpnb', 'product_name', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()
    
    
    
    
        cols_need = [x for x in df_catres_sum.columns if x not in [1] + df_catres_sum.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]
    
        cols_need_div = [x for x in df_catres_sum_div.columns if x not in [1] + df_catres_sum_div.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]
    
        df_catres_sum_category = df_catres_sum.melt(id_vars=cols_need, var_name='period' ).groupby(['country', 'category', 'repl_types','period'], observed=True)['value'].sum().reset_index()
        df_catres_sum_category['value_%'] = df_catres_sum_category['value'] / df_catres_sum_category.groupby(['country', 'category', 'period'], observed=True)['value'].transform('sum')
    
        df_catres_sum_division = df_catres_sum_div.melt(id_vars=cols_need_div, var_name='period' ).groupby(['country', 'DIV_DESC', 'repl_types','period'], observed=True)['value'].sum().reset_index()
        df_catres_sum_division['value_%'] = df_catres_sum_division['value'] / df_catres_sum_division.groupby(['country', 'DIV_DESC', 'period'],observed=True)['value'].transform('sum')
        df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)',df_catres_sum_division['DIV_DESC'] )
        df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'Food Grocery', 'Grocery (Food)',df_catres_sum_division['DIV_DESC'] )
        df_catres_sum_division = df_catres_sum_division[df_catres_sum_division.DIV_DESC != "GM"]
    
        # df_total = df_catres_sum_division[df_catres_sum_division.country == 'CE'].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
        df_total = df_catres_sum_division.groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
    
        df_total['value_%'] = df_total['value'] / df_total.groupby(['country', 'period'],observed=True)['value'].transform('sum')
    
        df_catres_sum_category['category'] = df_catres_sum_category['category'].apply(lambda x: x.replace(";"," "))
    
    
        def rename_period_columns(df, prefix, from_last_year:list):
            df['period'] = df['period'].astype(str)  
            df['period'] = df.apply(lambda x: "LY_"+ prefix +x['period'] if x['period'] in [str(y) for y in from_last_year] else prefix + x['period'], axis = 1)    
    
    
    
        for x in [df_total, df_catres_sum_category, df_catres_sum_division ]:
            rename_period_columns(x, 'p', from_last_year)
    
            # Reorder the dataframe
            x['period'] = pd.Categorical(x['period'], categories=desired_order, ordered=True)
            x.sort_values(by=['country', 'repl_types','period'], ascending=[True,True,True],inplace=True)  
    
    
    
    
        # sorted(df_catres_sum_category['category'].unique().tolist())
        cont = []
        ind=0
        for x in df_catres_sum_category['category'].unique().tolist():
    
                
            
    
            fig = px.bar(df_catres_sum_category[df_catres_sum_category.category.isin([x])], x="period", y="value_%",
                         color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                               opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                           height=400,
                         # width = 1800,
                         category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                         title=f'{x}',facet_col_wrap=5, orientation='v')
            fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
            fig.update_yaxes(matches=None)
            fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
            fig.update_yaxes(title_font_color='white')
            for annotation in fig['layout']['annotations']: 
                annotation['textangle']= 0
            # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
            fig.update_layout(title_text=f"<b>{x}</b>")
            # fig.update_layout(bargap=0.5)
            fig.update_layout(legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.1,
                        xanchor="right",
                        x=1.0))
            cont.append(fig)
            #fig.show()
                
    
                
                
    
        for x in sorted(df_catres_sum_division['DIV_DESC'].unique().tolist()):
            
    
                
            fig = px.bar(df_catres_sum_division[df_catres_sum_division.DIV_DESC.isin([x])], x="period", y="value_%",
                          color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                                opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                           height=400,
                          # width = 1800,
                          category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                          title=f'{x}',facet_col_wrap=5, orientation='v')
            fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
            fig.update_yaxes(matches=None)
            fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
            fig.update_yaxes(title_font_color='black')
            for annotation in fig['layout']['annotations']: 
                annotation['textangle']= 0
            # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
            fig.update_layout(title_text=f"<b>{x}</b>", template = 'plotly_dark') #, bargap=0.5)
            fig.update_layout(legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.1,
                        xanchor="right",
                        x=1.0))
            cont.append(fig)
            
    
    
    
        # fig = px.histogram(df_total, x="period", y="value_%",
        #               color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
        #                     opacity=0.8, facet_row_spacing =0.1,# color_discrete_sequence=px.colors.qualitative.Alphabet,
        #               height=400,
        #               width = 1800,
        #               category_orders={"country": ["CE"]},
        #               title='CE Total',facet_col_wrap=5, orientation='v')
        # fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
        # fig.update_yaxes(matches=None)
        # fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
        # fig.update_yaxes(title_font_color='black')
        # for annotation in fig['layout']['annotations']: 
        #     annotation['textangle']= 0
        # # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
        # fig.update_layout(title_text="<b>CE Total</b>", template = 'plotly_dark', bargap=0.5) #, bargap=0.5)
        # fig.update_layout(legend=dict(
        #             orientation="h",
        #             yanchor="bottom",
        #             y=1.1,
        #             xanchor="right",
        #             x=1.1))
        # cont.append(fig)
    
    
    
        #Total CE   
        fig = px.bar(df_total, x="period", y="value_%",
                      color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                            opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                       height=400,
                      # width = 1800,
                      category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                      title='CE Total',facet_col_wrap=5, orientation='v')
        fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
        fig.update_yaxes(matches=None)
        fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
        fig.update_yaxes(title_font_color='black')
        for annotation in fig['layout']['annotations']: 
            annotation['textangle']= 0
        # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
        fig.update_layout(title_text="<b>CE Total</b>", template = 'plotly_dark') #, bargap=0.5)
        fig.update_layout(legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.1,
                    xanchor="right",
                    x=1.0))
        cont.append(fig)
            
        if not ownbrand_bool:
            html_fname = os.path.join(place_to_save,"TPNB", "Category_Reset_chart_by_TPN_numbers_CE.html")
            pdf_fname = os.path.join(place_to_save,"TPNB", "Category_Reset_chart_by_TPN_numbers_CE.pdf")
        if ownbrand_bool:
            
            html_fname = os.path.join(place_to_save,"TPNB", "Category_Reset_chart_by_TPN_numbers_CE_OWNBRAND.html")
            pdf_fname = os.path.join(place_to_save,"TPNB", "Category_Reset_chart_by_TPN_numbers_CE_OWNBRAND.pdf")
        plotly_figs = cont
        combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', 
                                        separator=None, auto_open=False)
    
    
    
    
        #PDF converter
        figures = plotly_figs
        image_list = [pio.to_image(fig, format='png', scale=1.5) for fig in figures] #width=1440, height=900, scale=1.5
        for index, image in enumerate(image_list):
            with io.BytesIO() as tmp:
                tmp.write(image)  # write the image bytes to the io.BytesIO() temporary object
                image = Image.open(tmp).convert('RGB')  # convert and overwrite 'image' to prevent creating a new variable
                image_list[index] = image  # overwrite byte image data in list, replace with PIL converted image data
    
        # pop first item from image_list, use that to access .save(). Then refer back to image_list to append the rest
        image_list.pop(0).save(pdf_fname, 'PDF',
                               save_all=True, append_images=image_list, resolution=100.0)  # TODO improve resolution
    
        # pio.write_image(fig, pdf_fname, format="pdf", engine="kaleido")
        
        return df_catres_sum
    
    

    df_branded = ownbrand_bool_func(False,act_dataset_df, df_ce)
    print("\n###############")
    print("BRANDED_TPNB Chart is ready")
    print("###############\n")        

    df_ownbrand =ownbrand_bool_func(True,act_dataset_df, df_ce)
    print("\n###############")
    print("OWNBRAND_TPNB Chart is ready")
    print("###############\n")
    
    return df_branded, df_ownbrand


@timeit
def shelfcap_charts(periods, period, place_to_save, category_reset_df):
    

    print("\nwheighted by Shelfcap Charts running.....\n")
        


    from_last_year = []

    # Define the desired order of periods
    desired_order = ['LY_p11', 'LY_p12', 'p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']

    repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]


    
    df_ce = pd.DataFrame()
    
    def ownbrand_bool_func(ownbrand_bool, df_ce):

        for x, y in zip(periods, period ):
            
            
            df = pd.read_parquet(x) #.query("division != Produce ")
            
            if ownbrand_bool:
            
                df = df[df.ownbrand == "Y"]
            
    
    
            
            for r in repl_types:
                df[r] = np.where(df[r] > 0, df.shelfCapacity * df[r], 0) #shelfCapacity
            
            df['period'] = y
            

            
            
            
            df = df.groupby(['country', 'division','pmg', 'tpnb', 'product_name', 'period', 'Category name'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
            
            
            df_ce = pd.concat([df_ce, df])
            
            print(f"Done with processing p{y}!")
            

            
        # Create a dictionary to store pmg values and their corresponding most frequent Category name
        pmg_categories = {}
    
        # Iterate over unique pmg values
        for pmg_value in df_ce['pmg'].unique():
            # Filter rows where pmg is equal to current pmg_value and Category name is not "no_data"
            filtered_rows = df_ce[(df_ce['pmg'] == pmg_value) & (df_ce['Category name'] != 'no_data') & (df_ce['pmg'].str[:3] != 'HDL')]
            
            # Check if there are any non-"no_data" rows matching the conditions
            if not filtered_rows.empty:
                # Find the most frequent non-"no_data" Category name for the current pmg_value
                most_frequent_category = filtered_rows['Category name'].mode().iloc[0]
                
                # Store the most frequent non-"no_data" Category name for the current pmg_value in the dictionary
                pmg_categories[pmg_value] = most_frequent_category
                    
        # Update the 'no_data' rows in the DataFrame based on the mapping
    
        mask = df_ce['Category name'] == 'no_data'
        df_ce.loc[mask, 'Category name'] = df_ce.loc[mask, 'pmg'].map(pmg_categories)
        df_ce['Category name'] = np.where(df_ce.pmg.str[:3] == 'FRZ', 'FROZEN', df_ce['Category name'] )
        df_ce['Category name'] = np.where((df_ce['Category name'] == 'no_data')&(df_ce.pmg.str[:3] == 'HDL'), 'GM', df_ce['Category name'] )
    
        df_ce.loc[df_ce['Category name'] == "PRODUCE + DRN + FRESH JUICES (spring 2024)", "Category name"] = "PRODUCE + DRN + FRESH JUICES"

        df_ce.rename(columns={"Category name":"category"}, inplace=True)
    
        cat_df = pd.read_excel(category_reset_df)[['country', 'category', 'DIV_DESC']].drop_duplicates()
    
    
    
    
        df_ce = df_ce.merge(cat_df, on=['country', 'category'], how='left')
        

    
        df_ce['DIV_DESC'] = np.where((df_ce['DIV_DESC'].isnull())&(df_ce.pmg.str[:3] == 'HDL'), 'GM', df_ce['DIV_DESC'] )
        df_ce['DIV_DESC'] = np.where((df_ce['DIV_DESC'].isnull())&(df_ce.pmg.str[:3] == 'FRZ'), 'Fresh_Froz_Food', df_ce['DIV_DESC'] )
    
    

    
    
        df_ce_total = df_ce.groupby([ 'division','DIV_DESC','category','pmg', 'tpnb', 'product_name', 'period'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
        df_ce_total['country'] = 'CE'
    
        df_ce = pd.concat([df_ce, df_ce_total])
    
    
    
                
        need_cols = [x for x in df_ce.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]  
    
        # =============================================================================
        # Charts
        # =============================================================================
        df_catres_sum = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'division', 'pmg', 'tpnb', 'product_name', 'category', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()
    
        non_food_food = df_ce[df_ce['DIV_DESC'].isin(['NonFood Grocery', 'Food Grocery'])]
        non_food_food['division'] = np.where(non_food_food['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)', 'Grocery (Food)')
    
    
        df_catres_sum_div = df_ce.copy()
    
        df_catres_sum_div = pd.concat([df_catres_sum_div, non_food_food])
        df_catres_sum_div['division'] = np.where(df_catres_sum_div['division'] == 'Grocery', 'Grocery (total)', df_catres_sum_div['division'])
    
        df_catres_sum_div['division'] = np.where((df_catres_sum_div['division'] == 'Prepacked Meat') |
                                                 (df_catres_sum_div['division'] == 'Prepacked Poultry'),
                                             'Fresh',
                                             df_catres_sum_div['division'])
    
        df_catres_sum_div = df_catres_sum_div[df_catres_sum_div['division'] != 'Prepacked Bakery']
    
        df_catres_sum_div = df_catres_sum_div.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'division', 'pmg', 'tpnb', 'product_name', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()
    
    
    
        cols_need = [x for x in df_catres_sum.columns if x not in [1] + df_catres_sum.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]
    
        cols_need_div = [x for x in df_catres_sum_div.columns if x not in [1] + df_catres_sum_div.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]
    
        df_catres_sum_category = df_catres_sum.melt(id_vars=cols_need, var_name='period' ).groupby(['country', 'category', 'repl_types','period'], observed=True)['value'].sum().reset_index()
        df_catres_sum_category['value_%'] = df_catres_sum_category['value'] / df_catres_sum_category.groupby(['country', 'category', 'period'], observed=True)['value'].transform('sum')
    
        df_catres_sum_division = df_catres_sum_div.melt(id_vars=cols_need_div, var_name='period' ).groupby(['country', 'division', 'repl_types','period'], observed=True)['value'].sum().reset_index()
        df_catres_sum_division['value_%'] = df_catres_sum_division['value'] / df_catres_sum_division.groupby(['country', 'division', 'period'],observed=True)['value'].transform('sum')
    
        # df_total = df_catres_sum_division[(df_catres_sum_division.country == 'CE') & (~df_catres_sum_division['division'].isin(['GM', 'Produce']))].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
        df_total = df_catres_sum_division[(~df_catres_sum_division['division'].isin(['GM', 'Produce', 'Bakery']))].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
    
        df_total['value_%'] = df_total['value'] / df_total.groupby(['country', 'period'],observed=True)['value'].transform('sum')
    
        df_catres_sum_category['category'] = df_catres_sum_category['category'].apply(lambda x: x.replace(";"," "))
    
    
    
        def rename_period_columns(df, prefix, from_last_year:list):
            df['period'] = df['period'].astype(str)  
            df['period'] = df.apply(lambda x: "LY_"+ prefix +x['period'] if x['period'] in [str(y) for y in from_last_year] else prefix + x['period'], axis = 1)    
    
    
    
    
    
    
    
    
        for x in [df_total, df_catres_sum_category, df_catres_sum_division ]:
            rename_period_columns(x, 'p', from_last_year)
    
            # Reorder the dataframe
            x['period'] = pd.Categorical(x['period'], categories=desired_order, ordered=True)
            x.sort_values(by=['country', 'repl_types','period'], ascending=[True,True,True],inplace=True)
            
            
            
            
            
        # Creating the dictionary of dataframes
        dfs_dict = {
            'total': df_total,
            'category': df_catres_sum_category,
            'division': df_catres_sum_division
        }
    
    
          
    
        cont = []
        for x in df_catres_sum_category['category'].unique().tolist():
    
                
            
    
            fig = px.histogram(df_catres_sum_category[df_catres_sum_category.category.isin([x])], x="period", y="value_%",
                          color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                                opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                            height=400,
                          # width = 1800,
                          category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                          title=f'{x}',facet_col_wrap=5, orientation='v')
            fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
            fig.update_yaxes(matches=None)
            fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
            fig.update_yaxes(title_font_color='white')
            for annotation in fig['layout']['annotations']: 
                annotation['textangle']= 0
            # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
            fig.update_layout(title_text=f"<b>{x}</b>")
            # fig.update_layout(bargap=0.5)
            fig.update_layout(legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.1,
                        xanchor="right",
                        x=1.0))
            cont.append(fig)
            #fig.show()
                
    
                
                
    
        for x in df_catres_sum_division[~df_catres_sum_division['division'].isin(['GM', 'Produce', 'Bakery'])]['division'].unique().tolist():
            

                
                fig = px.histogram(df_catres_sum_division[df_catres_sum_division.division.isin([x])], x="period", y="value_%",
                              color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                                    opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                                height=400,
                              # width = 1800,
                              category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                              title=f'{x}',facet_col_wrap=5, orientation='v')
                fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
                fig.update_yaxes(matches=None)
                fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
                fig.update_yaxes(title_font_color='black')
                for annotation in fig['layout']['annotations']: 
                    annotation['textangle']= 0
                # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
                fig.update_layout(title_text=f"<b>{x}</b>", template = 'plotly_dark') #, bargap=0.5)
                fig.update_layout(legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.1,
                            xanchor="right",
                            x=1.0))
                cont.append(fig)
                
                

    
    
        fig = px.histogram(df_total, x="period", y="value_%",
                      color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                            opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                        height=400,
                      # width = 1800,
                      category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                      title='CE Total',facet_col_wrap=5, orientation='v')
        fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
        fig.update_yaxes(matches=None)
        fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
        fig.update_yaxes(title_font_color='black')
        for annotation in fig['layout']['annotations']: 
            annotation['textangle']= 0
        # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
        fig.update_layout(title_text="<b>CE Total</b>", template = 'plotly_dark') #, bargap=0.5)
        fig.update_layout(legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.1,
                    xanchor="right",
                    x=1.0))
        cont.append(fig)
            
        if not ownbrand_bool:
            html_fname = os.path.join(place_to_save, "Wheighted_by_ShelfCapacity", "Category_Reset_chart_weighted_by_shelfCapacity_CE.html")
            pdf_fname = os.path.join(place_to_save,"Wheighted_by_ShelfCapacity", "Category_Reset_chart_weighted_by_shelfCapacity_CE.pdf")
        if ownbrand_bool:
            
            html_fname = os.path.join(place_to_save,"Wheighted_by_ShelfCapacity", "Category_Reset_chart_weighted_by_shelfCapacity_CE_OWNBRAND.html")
            pdf_fname = os.path.join(place_to_save,"Wheighted_by_ShelfCapacity", "Category_Reset_chart_weighted_by_shelfCapacity_CE_OWNBRAND.pdf")
    
        plotly_figs = cont
        combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', 
                                        separator=None, auto_open=False)
    
        #PDF converter
        figures = plotly_figs
        image_list = [pio.to_image(fig, format='png', scale=1.5) for fig in figures] #width=1440, height=900, scale=1.5
        for index, image in enumerate(image_list):
            with io.BytesIO() as tmp:
                tmp.write(image)  # write the image bytes to the io.BytesIO() temporary object
                image = Image.open(tmp).convert('RGB')  # convert and overwrite 'image' to prevent creating a new variable
                image_list[index] = image  # overwrite byte image data in list, replace with PIL converted image data
    
        # pop first item from image_list, use that to access .save(). Then refer back to image_list to append the rest
        image_list.pop(0).save(pdf_fname, 'PDF',
                                save_all=True, append_images=image_list, resolution=100.0)  # TODO improve resolution

    
        return dfs_dict
    
    df_branded = ownbrand_bool_func(False, df_ce)
    print("\n###############")
    print("BRANDED_Shelfcap wheighted Chart is ready")
    print("###############\n")        

    df_ownbrand =ownbrand_bool_func(True,df_ce)
    print("\n###############")
    print("OWNBRAND_Shelfcap wheighted Chart is ready")
    print("###############\n")
    
    return df_branded, df_ownbrand




@timeit
def Sales_charts(periods, period, place_to_save, category_reset_df):
    

    print("\nwheighted by Sales Charts running.....\n")
        


    from_last_year = []

    # Define the desired order of periods
    desired_order = ['LY_p11', 'LY_p12', 'p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']

    repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]


    
    df_ce = pd.DataFrame()
    
    def ownbrand_bool_func(ownbrand_bool, df_ce):

        for x, y in zip(periods, period ):
            
            
            df = pd.read_parquet(x) #.query("division != Produce ")
            
            if ownbrand_bool:
            
                df = df[df.ownbrand == "Y"]
            
    
    
            
            for r in repl_types:
                df[r] = np.where(df[r] > 0, df.sold_units * df[r], 0) #shelfCapacity
            
            df['period'] = y
            
            df.loc[df['Category name'] == "PRODUCE + DRN + FRESH JUICES (spring 2024)", 'Category name'] = "PRODUCE + DRN + FRESH JUICES"

            
            
            
            df = df.groupby(['country', 'division','pmg', 'tpnb', 'product_name', 'period', 'Category name'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
            
            
            df_ce = pd.concat([df_ce, df])
            
            print(f"Done with processing p{y}!")
            
   
        # Create a dictionary to store pmg values and their corresponding most frequent Category name
        pmg_categories = {}
    
        # Iterate over unique pmg values
        for pmg_value in df_ce['pmg'].unique():
            # Filter rows where pmg is equal to current pmg_value and Category name is not "no_data"
            filtered_rows = df_ce[(df_ce['pmg'] == pmg_value) & (df_ce['Category name'] != 'no_data') & (df_ce['pmg'].str[:3] != 'HDL')]
            
            # Check if there are any non-"no_data" rows matching the conditions
            if not filtered_rows.empty:
                # Find the most frequent non-"no_data" Category name for the current pmg_value
                most_frequent_category = filtered_rows['Category name'].mode().iloc[0]
                
                # Store the most frequent non-"no_data" Category name for the current pmg_value in the dictionary
                pmg_categories[pmg_value] = most_frequent_category
                    
        # Update the 'no_data' rows in the DataFrame based on the mapping
    
        mask = df_ce['Category name'] == 'no_data'
        df_ce.loc[mask, 'Category name'] = df_ce.loc[mask, 'pmg'].map(pmg_categories)
        df_ce['Category name'] = np.where(df_ce.pmg.str[:3] == 'FRZ', 'FROZEN', df_ce['Category name'] )
        df_ce['Category name'] = np.where((df_ce['Category name'] == 'no_data')&(df_ce.pmg.str[:3] == 'HDL'), 'GM', df_ce['Category name'] )
    
    
        df_ce.rename(columns={"Category name":"category"}, inplace=True)
    
        cat_df = pd.read_parquet(category_reset_df)[['country', 'category', 'DIV_DESC']].drop_duplicates()
    
    
    
    
        df_ce = df_ce.merge(cat_df, on=['country', 'category'], how='left')
        

    
        df_ce['DIV_DESC'] = np.where((df_ce['DIV_DESC'].isnull())&(df_ce.pmg.str[:3] == 'HDL'), 'GM', df_ce['DIV_DESC'] )
        df_ce['DIV_DESC'] = np.where((df_ce['DIV_DESC'].isnull())&(df_ce.pmg.str[:3] == 'FRZ'), 'Fresh_Froz_Food', df_ce['DIV_DESC'] )
    
    
    
    
    
        df_ce_total = df_ce.groupby([ 'division','DIV_DESC','category','pmg', 'tpnb', 'product_name', 'period'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
        df_ce_total['country'] = 'CE'
    
        df_ce = pd.concat([df_ce, df_ce_total])
    
    
    
                
        need_cols = [x for x in df_ce.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]  
    
        # =============================================================================
        # Charts
        # =============================================================================
        df_catres_sum = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'division', 'pmg', 'tpnb', 'product_name', 'category', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()
    
        non_food_food = df_ce[df_ce['DIV_DESC'].isin(['NonFood Grocery', 'Food Grocery'])]
        non_food_food['division'] = np.where(non_food_food['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)', 'Grocery (Food)')
    
    
        df_catres_sum_div = df_ce.copy()
    
        df_catres_sum_div = pd.concat([df_catres_sum_div, non_food_food])
        df_catres_sum_div['division'] = np.where(df_catres_sum_div['division'] == 'Grocery', 'Grocery (total)', df_catres_sum_div['division'])
    
        df_catres_sum_div['division'] = np.where((df_catres_sum_div['division'] == 'Prepacked Meat') |
                                                 (df_catres_sum_div['division'] == 'Prepacked Poultry'),
                                             'Fresh',
                                             df_catres_sum_div['division'])
    
        df_catres_sum_div = df_catres_sum_div[df_catres_sum_div['division'] != 'Prepacked Bakery']
    
        df_catres_sum_div = df_catres_sum_div.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'division', 'pmg', 'tpnb', 'product_name', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()
    
    
    
        cols_need = [x for x in df_catres_sum.columns if x not in [1] + df_catres_sum.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]
    
        cols_need_div = [x for x in df_catres_sum_div.columns if x not in [1] + df_catres_sum_div.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]
    
        df_catres_sum_category = df_catres_sum.melt(id_vars=cols_need, var_name='period' ).groupby(['country', 'category', 'repl_types','period'], observed=True)['value'].sum().reset_index()
        df_catres_sum_category['value_%'] = df_catres_sum_category['value'] / df_catres_sum_category.groupby(['country', 'category', 'period'], observed=True)['value'].transform('sum')
    
        df_catres_sum_division = df_catres_sum_div.melt(id_vars=cols_need_div, var_name='period' ).groupby(['country', 'division', 'repl_types','period'], observed=True)['value'].sum().reset_index()
        df_catres_sum_division['value_%'] = df_catres_sum_division['value'] / df_catres_sum_division.groupby(['country', 'division', 'period'],observed=True)['value'].transform('sum')
    
        # df_total = df_catres_sum_division[(df_catres_sum_division.country == 'CE') & (~df_catres_sum_division['division'].isin(['GM', 'Produce']))].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
        df_total = df_catres_sum_division[(~df_catres_sum_division['division'].isin(['GM', 'Produce', 'Bakery']))].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
    
        df_total['value_%'] = df_total['value'] / df_total.groupby(['country', 'period'],observed=True)['value'].transform('sum')
    
        df_catres_sum_category['category'] = df_catres_sum_category['category'].apply(lambda x: x.replace(";"," "))
    
    
    
        def rename_period_columns(df, prefix, from_last_year:list):
            df['period'] = df['period'].astype(str)  
            df['period'] = df.apply(lambda x: "LY_"+ prefix +x['period'] if x['period'] in [str(y) for y in from_last_year] else prefix + x['period'], axis = 1)    
    
    
    
    
    
    
    
    
        for x in [df_total, df_catres_sum_category, df_catres_sum_division ]:
            rename_period_columns(x, 'p', from_last_year)
    
            # Reorder the dataframe
            x['period'] = pd.Categorical(x['period'], categories=desired_order, ordered=True)
            x.sort_values(by=['country', 'repl_types','period'], ascending=[True,True,True],inplace=True)  
    
    
          
    
        cont = []
        for x in df_catres_sum_category['category'].unique().tolist():
    
                
            
    
            fig = px.histogram(df_catres_sum_category[df_catres_sum_category.category.isin([x])], x="period", y="value_%",
                         color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                               opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                           height=400,
                         # width = 1800,
                         category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                         title=f'{x}',facet_col_wrap=5, orientation='v')
            fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
            fig.update_yaxes(matches=None)
            fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
            fig.update_yaxes(title_font_color='white')
            for annotation in fig['layout']['annotations']: 
                annotation['textangle']= 0
            # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
            fig.update_layout(title_text=f"<b>{x}</b>")
            # fig.update_layout(bargap=0.5)
            fig.update_layout(legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.1,
                        xanchor="right",
                        x=1.0))
            cont.append(fig)
            #fig.show()
                
    
                
                
    
        for x in df_catres_sum_division[~df_catres_sum_division['division'].isin(['GM', 'Produce', 'Bakery'])]['division'].unique().tolist():
            
            # if x == 'Grocery':
            
            #     fig = px.histogram(df_catres_sum_division[df_catres_sum_division.division.isin([x])], x="period", y="value_%",
            #                  color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
            #                        opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
            #                   height=400,
            #                  width = 1200,
            #                  category_orders={"country": ["CZ", "HU", "SK", "CE"]},
            #                  title=f'{x}',facet_col_wrap=5, orientation='v')
            #     fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
            #     fig.update_yaxes(matches=None)
            #     fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
            #     fig.update_yaxes(title_font_color='black')
            #     for annotation in fig['layout']['annotations']: 
            #         annotation['textangle']= 0
            #     # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
            #     fig.update_layout(title_text=f"<b>{x}</b>", template = 'plotly_dark') #, bargap=0.5)
            #     cont.append(fig)
            
            
            #     for f in ['NonFood Grocery', 'Food Grocery']:
            #         fig = px.histogram(df_catres_sum_division[df_catres_sum_division['DIV_DESC'].isin([f])], x="period", y="value_%",
            #                      color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
            #                            opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
            #                       height=400,
            #                      width = 1200,
            #                      category_orders={"country": ["CZ", "HU", "SK", "CE"]},
            #                      title=f'{f}',facet_col_wrap=5, orientation='v')
            #         fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
            #         fig.update_yaxes(matches=None)
            #         fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
            #         fig.update_yaxes(title_font_color='white')
            #         for annotation in fig['layout']['annotations']: 
            #             annotation['textangle']= 0
            #         # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
            #         fig.update_layout(title_text=f"<b>{f}</b>")
            #         # fig.update_layout(bargap=0.5)
            #         fig.update_layout(title_text=f"<b>{f}</b>", template = 'plotly_dark')
            #         cont.append(fig)
            #         #fig.show()
            # else:
                
                fig = px.histogram(df_catres_sum_division[df_catres_sum_division.division.isin([x])], x="period", y="value_%",
                              color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                                    opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                               height=400,
                              # width = 1800,
                              category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                              title=f'{x}',facet_col_wrap=5, orientation='v')
                fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
                fig.update_yaxes(matches=None)
                fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
                fig.update_yaxes(title_font_color='black')
                for annotation in fig['layout']['annotations']: 
                    annotation['textangle']= 0
                # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
                fig.update_layout(title_text=f"<b>{x}</b>", template = 'plotly_dark') #, bargap=0.5)
                fig.update_layout(legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.1,
                            xanchor="right",
                            x=1.0))
                cont.append(fig)
                
                
        #Total CE   
        # fig = px.histogram(df_total, x="period", y="value_%",
        #               color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
        #                     opacity=0.8, facet_row_spacing =0.1,# color_discrete_sequence=px.colors.qualitative.Alphabet,
        #               height=400,
        #               width = 1800,
        #               category_orders={"country": ["CE"]},
        #               title='CE Total',facet_col_wrap=5, orientation='v')
        # fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
        # fig.update_yaxes(matches=None)
        # fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
        # fig.update_yaxes(title_font_color='black')
        # for annotation in fig['layout']['annotations']: 
        #     annotation['textangle']= 0
        # # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
        # fig.update_layout(title_text="<b>CE Total</b>", template = 'plotly_dark', bargap=0.5) #, bargap=0.5)
        # fig.update_layout(legend=dict(
        #             orientation="h",
        #             yanchor="bottom",
        #             y=1.02,
        #             xanchor="right",
        #             x=1))
        # cont.append(fig)
    
    
        fig = px.histogram(df_total, x="period", y="value_%",
                      color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                            opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                       height=400,
                      # width = 1800,
                      category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                      title='CE Total',facet_col_wrap=5, orientation='v')
        fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
        fig.update_yaxes(matches=None)
        fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
        fig.update_yaxes(title_font_color='black')
        for annotation in fig['layout']['annotations']: 
            annotation['textangle']= 0
        # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
        fig.update_layout(title_text="<b>CE Total</b>", template = 'plotly_dark') #, bargap=0.5)
        fig.update_layout(legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.1,
                    xanchor="right",
                    x=1.0))
        cont.append(fig)
            
        if not ownbrand_bool:
            html_fname = os.path.join(place_to_save, "Wheighted_by_Sales", "Category_Reset_chart_weighted_by_sales_CE.html")
            pdf_fname = os.path.join(place_to_save,"Wheighted_by_Sales", "Category_Reset_chart_weighted_by_sales_CE.pdf")
        if ownbrand_bool:
            
            html_fname = os.path.join(place_to_save,"Wheighted_by_Sales", "Category_Reset_chart_by_sales_CE_OWNBRAND.html")
            pdf_fname = os.path.join(place_to_save,"Wheighted_by_Sales", "Category_Reset_chart_by_sales_CE_OWNBRAND.pdf")
    
        plotly_figs = cont
        combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', 
                                        separator=None, auto_open=False)
    
        #PDF converter
        figures = plotly_figs
        image_list = [pio.to_image(fig, format='png', scale=1.5) for fig in figures] #width=1440, height=900, scale=1.5
        for index, image in enumerate(image_list):
            with io.BytesIO() as tmp:
                tmp.write(image)  # write the image bytes to the io.BytesIO() temporary object
                image = Image.open(tmp).convert('RGB')  # convert and overwrite 'image' to prevent creating a new variable
                image_list[index] = image  # overwrite byte image data in list, replace with PIL converted image data
    
        # pop first item from image_list, use that to access .save(). Then refer back to image_list to append the rest
        image_list.pop(0).save(pdf_fname, 'PDF',
                               save_all=True, append_images=image_list, resolution=100.0)  # TODO improve resolution

    
        return df_catres_sum
    
    df_branded = ownbrand_bool_func(False, df_ce)
    print("\n###############")
    print("BRANDED_Sales wheighted Chart is ready")
    print("###############\n")        

    df_ownbrand =ownbrand_bool_func(True,df_ce)
    print("\n###############")
    print("OWNBRAND_Sales wheighted Chart is ready")
    print("###############\n")
    
    return df_branded, df_ownbrand

    
    

act_dataset = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\plano_0103\Dataset_based_on_plano_0103"
store_list_path = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\Repl\Repl_Stores_Inputs_2024_Q2.xlsx"
place_to_save = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\CategoryReset\CatRes shared"
category_reset_df = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\CategoryReset\Category Reset CE_hier.xlsx"
period = [1,2,3,4, 5, 6, 7, 8, 9, 10, 11] #1,2,3,4, 5, 6, 7, 8, 9, 10,


# # TPNB OWNBRAND
# LY_p11 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\18-01-2024\CE_JDA_SRD_for_model"
# LY_p12 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\05-02-2024\CE_JDA_SRD_for_model"
# p1 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\04-03-2024\CE_JDA_SRD_for_model"
# p2 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\04-04-2024\CE_JDA_SRD_for_model"
# p3 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\02-05-2024\CE_JDA_SRD_for_model"
# p4 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\04-06-2024\CE_JDA_SRD_for_model"
# p5 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\05-07-2024\CE_JDA_SRD_for_model"
# p6 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\05-08-2024\CE_JDA_SRD_for_model"
# p7 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\03-09-2024\CE_JDA_SRD_for_model"
# p8 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\07-10-2024\CE_JDA_SRD_for_model"
# p9 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\JDA_SRD_Tables\04-11-2024\CE_JDA_SRD_for_model"
# p10 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\JDA_SRD_Tables\02-12-2024\CE_JDA_SRD_for_model"
# p11 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\JDA_SRD_Tables\03-01-2025\CE_JDA_SRD_for_model"

# tpnb_periods = [ p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11]


# df_branded, df_ownbrand = tpnb_charts(tpnb_periods, period, act_dataset, store_list_path,  place_to_save, category_reset_df)

################################################################################


# Shelfcap/Sales wheighted Charts
LY_p11 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_0122\Dataset_based_on_plano_0122"
LY_p12 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_0205\Dataset_based_on_0205"
p1 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_0304\Dataset_based_on_plano_0304"
p2 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_0404\Dataset_based_on_plano_0404"
p3 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_0502\Dataset_based_on_plano_0502"
p4 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_0604\Dataset_based_on_plano_0604"
p5 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_0705\Dataset_based_on_plano_0705"
p6 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_0805\Dataset_based_on_plano_0805"
p7 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_0903\Dataset_based_on_plano_0903"
p8 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\files_for_dataset\plano_1007\Dataset_based_on_plano_1007"
p9 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\plano_1104\Dataset_based_on_plano_1104"
p10 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\plano_1202\Dataset_based_on_plano_1202"
p11 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\plano_0103\Dataset_based_on_plano_0103"


#p1, p2, p3, p4, p5, p6, p7, p8, p9, p10,
shelfcap_periods = [p1, p2, p3, p4, p5, p6, p7, p8, p9, p10,  p11]

df_branded_shelfcap, df_ownbrand_shelfcap = shelfcap_charts(shelfcap_periods, period, place_to_save, category_reset_df)

# df_branded_sales, df_ownbrand_sales = Sales_charts(shelfcap_periods, period, place_to_save, category_reset_df)
