{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8ccc4cb9-c5c7-4f29-8d8a-3d7a24df0465", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import time\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "start = time.perf_counter()"]}, {"cell_type": "code", "execution_count": 2, "id": "14997b16-db37-44da-bfcf-4613903fb236", "metadata": {}, "outputs": [], "source": ["directory = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl\"\n", "excel_inputs_f = \"Stores_Inputs_2024_Q1.xlsx\"\n", "stores = list(\n", "    pd.read_excel(directory +\"\\\\\"+ excel_inputs_f, usecols=[\"Country\", \"Format\", \"Store\"])[\"Store\"]\n", "    .unique()\n", ")\n", "\n", "\n", "single_pick_f =r\"\\\\euprgvmfs01\\GMforCentralEurope\\Supply Chain\\Single Picking\\Single pick list.xlsx\" \n", "\n", "opsdev_df = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\CE_JDA_SRD_for_model_modified\"\n", "\n", "stock_df = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\SSH\\stock_ce_test_1\"\n", "\n", "item_sold_df = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\SSH\\item_sold_ce_test_hier\"\n", "\n", "item_sold_dotcom_df = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\SSH\\item_sold_dotcom_ce_test_1\"\n", "\n", "cases_delivered_df = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\SSH\\cases_ce\"\n", "\n", "pallet_cap_df =     r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\pallet_capacity_CE_2023w14_27\"\n", "\n", "box_op_type_f = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\ownbrand_opening_type_22-09-2023.xlsx\"\n", "\n", "foil_df = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl\\foil_new_new_new\"\n", "\n", "broken_cases_f = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\broken_cases_list_22w14_27.csv.gz\""]}, {"cell_type": "code", "execution_count": 3, "id": "ea9e6c55-2bc4-429b-aa70-8e31f4c23ec2", "metadata": {}, "outputs": [], "source": ["def optimize_types(dataframe):\n", "    np_types = [\n", "        np.int8,\n", "        np.int16,\n", "        np.int32,\n", "        np.int64,\n", "        np.uint8,\n", "        np.uint16,\n", "        np.uint32,\n", "        np.uint64,\n", "        np.float32,\n", "        np.float64,\n", "    ]  # , np.float16, np.float32, np.float64\n", "    np_types = [np_type.__name__ for np_type in np_types]\n", "    type_df = pd.DataFrame(data=np_types, columns=[\"class_type\"])\n", "\n", "    type_df[\"min_value\"] = type_df[type_df[\"class_type\"].str.contains(\"int\")][\n", "        \"class_type\"\n", "    ].apply(lambda row: np.iinfo(row).min)\n", "    type_df[\"max_value\"] = type_df[type_df[\"class_type\"].str.contains(\"int\")][\n", "        \"class_type\"\n", "    ].apply(lambda row: np.iinfo(row).max)\n", "    type_df[\"min_value_f\"] = type_df[type_df[\"class_type\"].str.contains(\"float\")][\n", "        \"class_type\"\n", "    ].apply(lambda row: np.finfo(row).min)\n", "    type_df[\"max_value_f\"] = type_df[type_df[\"class_type\"].str.contains(\"float\")][\n", "        \"class_type\"\n", "    ].apply(lambda row: np.finfo(row).max)\n", "    type_df[\"min_value\"] = np.where(\n", "        type_df[\"min_value\"].isna(), type_df[\"min_value_f\"], type_df[\"min_value\"]\n", "    )\n", "    type_df[\"max_value\"] = np.where(\n", "        type_df[\"max_value\"].isna(), type_df[\"max_value_f\"], type_df[\"max_value\"]\n", "    )\n", "    type_df.drop(columns=[\"min_value_f\", \"max_value_f\"], inplace=True)\n", "\n", "    type_df[\"range\"] = type_df[\"max_value\"] - type_df[\"min_value\"]\n", "    type_df.sort_values(by=\"range\", inplace=True)\n", "    try:\n", "        for col in dataframe.loc[:, dataframe.dtypes == np.integer]:\n", "            col_min = dataframe[col].min()\n", "            col_max = dataframe[col].max()\n", "            temp = type_df[\n", "                (type_df[\"min_value\"] <= col_min) & (type_df[\"max_value\"] >= col_max)\n", "            ]\n", "            optimized_class = temp.loc[temp[\"range\"].idxmin(), \"class_type\"]\n", "            # print(\"Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}\".format(col, col_min, col_max, optimized_class))\n", "            dataframe[col] = dataframe[col].astype(optimized_class)\n", "    except ValueError:\n", "        pass\n", "    try:\n", "        for col in dataframe.loc[:, (dataframe.dtypes == np.floating)]:\n", "            col_min = dataframe[col].min()\n", "            col_max = dataframe[col].max()\n", "            type_df = type_df[\n", "                type_df[\"class_type\"].astype(\"string\").str.contains(\"float\")\n", "            ]\n", "            temp = type_df[\n", "                (type_df[\"min_value\"] <= col_min) & (type_df[\"max_value\"] >= col_max)\n", "            ]\n", "            optimized_class = temp.loc[temp[\"range\"].idxmin(), \"class_type\"]\n", "            # print(\"Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}\".format(col, col_min, col_max, optimized_class))\n", "            dataframe[col] = dataframe[col].astype(optimized_class)\n", "    except ValueError:\n", "        pass\n", "    return dataframe\n", "\n", "\n", "def optimize_floats(df: pd.DataFrame) -> pd.DataFrame:\n", "    floats = df.select_dtypes(include=[\"float64\"]).columns.tolist()\n", "    df[floats] = df[floats].apply(pd.to_numeric, downcast=\"float\")\n", "    return df\n", "\n", "\n", "def optimize_ints(df: pd.DataFrame) -> pd.DataFrame:\n", "    ints = df.select_dtypes(include=[\"int64\"]).columns.tolist()\n", "    df[ints] = df[ints].apply(pd.to_numeric, downcast=\"integer\")\n", "    return df\n", "\n", "\n", "def optimize_objects(df: pd.DataFrame):\n", "    try:\n", "        for col in df.select_dtypes(include=[\"object\"]):\n", "            if not (type(df[col][0]) == list):\n", "                num_unique_values = len(df[col].unique())\n", "                num_total_values = len(df[col])\n", "                if float(num_unique_values) / num_total_values < 0.5:\n", "                    df[col] = df[col].astype(\"category\")\n", "    except IndexError:\n", "        pass\n", "    return df\n", "\n", "\n", "def optimize(df: pd.DataFrame):\n", "    return optimize_floats(optimize_ints(optimize_objects(df)))"]}, {"cell_type": "code", "execution_count": 4, "id": "41318225-0c13-4f7c-99ea-cfe469e79255", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "def Store_Inputs_Creator(directory, excel_inputs_f, stores):\n", "\n", "    # Store inputs\n", "\n", "    pmg_df = pd.read_excel(directory +\"\\\\\"+ excel_inputs_f, \"pmg\")\n", "    pmg_df = pmg_df[pmg_df.Area == \"Replenishment\"]\n", "    store_list_df = pd.read_excel(directory +\"\\\\\"+ excel_inputs_f, \"store_list\")\n", "    store_list_df = store_list_df.loc[store_list_df.Store.isin(stores)]\n", "    capping_df = pd.read_excel(directory +\"\\\\\"+ excel_inputs_f, \"capping\")\n", "    capping_df[\"is_capping_shelf\"] = 1\n", "    Dprofiles_df = pd.read_excel(directory +\"\\\\\"+ excel_inputs_f, \"Dprofiles\")\n", "    Pprofiles_df = pd.read_excel(directory +\"\\\\\"+ excel_inputs_f, \"Pprofiles\")\n", "    pmg_array = pmg_df.values\n", "    store_array = store_list_df.values\n", "    result = len(store_array) * len(pmg_array)  #\n", "    df_array = np.empty([result, 9], dtype=\"object\")  # create an empty array\n", "    counter = 0\n", "    for s in range(len(store_array)):\n", "        for p in range(len(pmg_array)):\n", "            df_array[counter][0] = store_array[s][1]  # country\n", "            df_array[counter][1] = store_array[s][0]  # store\n", "            df_array[counter][2] = store_array[s][2]  # store_name\n", "            df_array[counter][3] = store_array[s][3]  # sqm\n", "            df_array[counter][4] = store_array[s][4]  # format\n", "            df_array[counter][5] = pmg_array[p][0]  # pmg\n", "            df_array[counter][6] = pmg_array[p][1]  # pmg_name\n", "            df_array[counter][7] = pmg_array[p][2]  # dep\n", "            df_array[counter][8] = pmg_array[p][3]  # division\n", "            counter += 1\n", "    store_inputs = pd.DataFrame(\n", "        columns=[\n", "            \"Country\",\n", "            \"Store\",\n", "            \"Store Name\",\n", "            \"Plan Size\",\n", "            \"Format\",\n", "            \"Pmg\",\n", "            \"Pmg Name\",\n", "            \"Dep\",\n", "            \"Division\",\n", "        ]\n", "    )\n", "    store_inputs = pd.concat(\n", "        [store_inputs, pd.DataFrame(df_array, columns=store_inputs.columns)]\n", "    )\n", "    store_inputs[\"Dep\"] = np.where(store_inputs.Pmg == \"HDL01\", \"NEW\", store_inputs.Dep)\n", "    store_inputs = store_inputs.merge(capping_df, on=[\"Store\", \"Pmg\"], how=\"left\")\n", "    store_inputs[\"is_capping_shelf\"] = store_inputs[\"is_capping_shelf\"].replace(\n", "        np.nan, 0\n", "    )\n", "    store_inputs = store_inputs.merge(Dprofiles_df, on=[\"Store\", \"Dep\"], how=\"left\")\n", "    store_inputs = store_inputs.merge(\n", "        Pprofiles_df, on=[\"Country\", \"Format\", \"Pmg\"], how=\"left\"\n", "    )\n", "    store_inputs[\"Store\"] = store_inputs[\"Store\"].astype(\"int64\")\n", "    # store_inputs.columns = [i.lower() for i in store_inputs.columns]\n", "    return store_inputs\n", "\n", "\n", "store_inputs = Store_Inputs_Creator(directory, excel_inputs_f, stores)"]}, {"cell_type": "code", "execution_count": 5, "id": "8f55e72d-a5f3-437b-a7a9-47231a149f36", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "###########\n", "Repl_Dataset Build: has been started to calculate...\n", "###########\n", "\n", "\n", "Inputs are loaded into Memory!\n", "tpns with weekdays shape:  (48668200, 6)\n", "\n", "All Tables Combined!!\n", "Tables's combination shape (48668200, 48)\n", "\n", "Modifications on Columns are done!!\n", "Memory Usage: 16.1 gb\n", "End shape (48668200, 49)\n", "\n", "Optimization on Columns are done!!\n", "Memory Usage: 8.0 gb\n", "101.25647699999999\n"]}], "source": ["with pl.<PERSON><PERSON><PERSON>():\n", "    print(\"\\n###########\")\n", "    print(\"Repl_Dataset Build: has been started to calculate...\")\n", "    print(\"###########\\n\")\n", "\n", "    #Creating Base for Repl_Dataset\n", "    weekdays = [\n", "        \"Monday\",\n", "        \"Tuesday\",\n", "        \"Wednesday\",\n", "        \"Thursday\",\n", "        \"Friday\",\n", "        \"Saturday\",\n", "        \"Sunday\",\n", "    ]\n", "    store_inputs_lower = store_inputs.copy()\n", "    store_inputs_lower.columns = [i.lower() for i in store_inputs_lower.columns]\n", "    store_inputs_lower = store_inputs_lower.rename(columns={\"pmg name\": \"pmg_name\"})\n", "\n", "    store_inputs_lower = store_inputs_lower[\n", "        [\"store\", \"format\", \"division\", \"pmg\", \"dep\", \"is_capping_shelf\"]\n", "    ].drop_duplicates()\n", "    store_inputs_lower = pl.from_pandas(store_inputs_lower)\n", "    \n", "    \n", "    # planogram = pl.read_csv(planogram_f)\\\n", "    # .select([\"store\", \"tpnb\", \"icase\", \"capacity\"])\\\n", "    # .unique(subset=[\"tpnb\", \"icase\", \"store\", \"capacity\"])\\\n", "    # .with_columns(\n", "    #     [pl.col(\"tpnb\").cast(pl.Int64), pl.col(\"store\").cast(pl.Int64)]\n", "    # )\n", "\n", "    single_pick_df = pl.read_excel(single_pick_f).rename({'TPN':'tpn'}).with_columns(pl.lit(1).alias(\"single_pick\")).select(['tpn','single_pick'])\n", "    opsdev = pl.read_parquet(opsdev_df)\\\n", "    .with_columns(pl.col('tpnb').cast(pl.Int64),\n", "                                pl.col('store').cast(pl.Int64))\\\n", "    .select(\n", "                [\n", "                    \"store\",\n", "                    \"tpnb\",\n", "                    \"srp\",\n", "                    \"nsrp\",\n", "                    \"mu\",\n", "                    \"full_pallet\",\n", "                    \"split_pallet\",\n", "                    \"icream_nsrp\",\n", "                    \"checkout_stand_flag\",\n", "                    \"clipstrip_flag\",\n", "                    \"backroom_flag\",\n", "                    \"shelfCapacity\",\n", "                    \"icase\"\n", "                ]\n", "            )\n", "    \n", "    stock = pl.read_parquet(stock_df).select([\"store\", \"day\", \"tpnb\", \"stock\", \"item_price\"])\n", "    \n", "    isold = pl.read_parquet(item_sold_df).with_columns(\n", "    \n", "        [pl.col(\"day\").cast(pl.Utf8, strict=False),\n", "        pl.col(\"pmg\").cast(pl.Utf8, strict=False),\n", "        pl.col(\"country\").cast(pl.Utf8, strict=False)]\n", "    ).rename({\"division\": \"division_hier\"})\n", "\n", "    isold_dotcom = pl.read_parquet(item_sold_dotcom_df)\\\n", "    .select([\"store\", \"day\", \"tpnb\", \"sold_units_dotcom\"])\n", "    \n", "    cases = pl.read_parquet(cases_delivered_df)\\\n", "    .select([\"store\", \"day\", \"tpnb\", \"unit\"])\n", "\n", "    pallet_cap = pl.read_parquet(pallet_cap_df)\n", "    \n", "    op_type = pl.read_excel(box_op_type_f)\n", "    \n", "    foil = pl.read_parquet(foil_df)\\\n", "    .select(pl.all().exclude(\n", "                    [\"level4\", \"country\"]\n", "    )).with_columns(pl.col(\"store\").cast(pl.Int64))\n", "\n", "    print(\"\\nInputs are loaded into Memory!\")\n", "    \n", "\n", "    def debug(result, info=\"\"):\n", "        print(f\"{info} {result.shape}\" )\n", "        return result\n", "    \n", "    def all_tpn_all_day(isold):\n", "        \n", "        \n", "        result = isold.select([\"store\", \"pmg\", \"tpnb\"]).unique()\\\n", "        .with_columns([pl.lit(None).alias(\"day\")])\\\n", "        .with_columns(\n", "            [pl.col(\"day\").map(lambda s: weekdays).alias(\"day\")]\n", "        )\\\n", "        .explode(\"day\").unique()\\\n", "        .with_columns(\n", "        [pl.col(\"day\").cast(pl.Utf8, strict=False)]\n", "        ).join(\n", "            isold.select([\"store\", \"pmg\", \"tpnb\", \"day\", \"sold_units\", \"sales_excl_vat\"]),\n", "            on=[\"store\", \"pmg\", \"tpnb\", \"day\"],\n", "            how=\"left\",\n", "        )\n", "\n", "\n", "        return result\n", "    \n", "    def join_all_table(Repl_Dataset):\n", "        \n", "        result = Repl_Dataset.join(\n", "            isold.select(\n", "                [\n", "                    pl.all().exclude(\n", "                [\"sold_units\", \"sales_excl_vat\", \"day\"])\n", "                \n", "                ]\n", "                    ).unique(),\n", "                    on=[\"store\", \"pmg\", \"tpnb\"],\n", "                    how=\"left\")\\\n", "                .with_columns(\n", "                    [\n", "                        pl.when(pl.col(\"pmg\") == \"DRY18\")\n", "                        .then(pl.lit(\"DRY15\"))\n", "                        .otherwise(pl.col(\"pmg\"))\n", "                        .alias(\"pmg\")\n", "                    ])\\\n", "                .join(\n", "                    store_inputs_lower, on=[\"store\", \"pmg\"], how=\"left\"\n", "                )\\\n", "                .join(\n", "                    isold_dotcom, on=[\"store\", \"day\", \"tpnb\"], how=\"left\"\n", "                )\\\n", "                .join(stock, on=[\"store\", \"day\", \"tpnb\"], how=\"left\")\\\n", "                .join(opsdev, on=[\"tpnb\", \"store\"], how=\"left\")\\\n", "                .join(cases, on=[\"store\", \"day\", \"tpnb\"], how=\"left\")\\\n", "                .join(op_type, on=[\"country\", \"tpnb\"], how=\"left\")\\\n", "                .join(pallet_cap, on=[\"country\", \"tpnb\"], how=\"left\")\\\n", "                .join(foil, on=[\"store\", \"tpnb\"], how=\"left\")\\\n", "                .join(single_pick_df, on=\"tpn\", how=\"left\")\\\n", "                .fill_null(0)\n", "        print(\"\\nAll Tables Combined!!\")\n", "        return result \n", "    \n", "    def mod_columns(Repl_Dataset):\n", "        \n", "        result = Repl_Dataset.with_columns(\n", "            [\n", "                pl.when(\n", "                    (pl.col(\"srp\") == 0)\n", "                    & (pl.col(\"nsrp\") == 0)\n", "                    & (pl.col(\"full_pallet\") == 0)\n", "                    & (pl.col(\"mu\") == 0)\n", "                    & (pl.col(\"split_pallet\") == 0)\n", "                    & (pl.col(\"icream_nsrp\") == 0)\n", "                    & (pl.col(\"single_pick\") == 0)\n", "                )\n", "                .then(1)\n", "                .otherwise(pl.col(\"nsrp\"))\n", "                .alias(\"nsrp\"),\n", "                pl.when(pl.col(\"icase\") == 0)\n", "                .then(pl.col(\"case_capacity\"))\n", "                .otherwise(pl.col(\"icase\"))\n", "                .alias(\"icase\"),\n", "            ]\n", "        )\\\n", "        .drop(\"case_capacity\")\\\n", "        .rename({\"icase\": \"case_capacity\"})\\\n", "    .with_columns(\n", "            [(pl.col(\"unit\") / pl.col(\"case_capacity\")).alias(\"cases_delivered\")]\n", "        )\\\n", "        .with_columns(\n", "        [\n", "            pl.when(pl.col(\"unit_type\") != \"KG\")\n", "            .then(pl.lit(\"SNGL\"))\n", "            .otherwise(pl.col(\"unit_type\"))\n", "            .alias(\"unit_type\")\n", "        ]\n", "    )\\\n", "        .with_columns(\n", "        [\n", "            pl.when(pl.col(\"dep\") == \"NEW\")\n", "            .then(pl.lit(\"HDL\"))\n", "            .otherwise(pl.col(\"dep\"))\n", "            .alias(\"dep\")\n", "        ]\n", "    )\\\n", "        .with_columns(\n", "        [pl.col(\"pallet_capacity\").round(0).alias(\"pallet_capacity\")]\n", "    )\\\n", "        .with_columns(\n", "        [\n", "            pl.when((pl.col(\"srp\") > 0) & (pl.col(\"pmg\").str.contains(\"FRZ\")))\n", "            .then(1)\n", "            .otherwise(pl.col(\"nsrp\"))\n", "            .alias(\"nsrp\"),\n", "            pl.when((pl.col(\"srp\") > 0) & (pl.col(\"pmg\").str.contains(\"FRZ\")))\n", "            .then(0)\n", "            .otherwise(pl.col(\"srp\"))\n", "            .alias(\"srp\"),\n", "        ]\n", "    )\\\n", "        .with_columns(pl.when(pl.col(c) == 0).then(pl.col(c).mean().over(['country','pmg'])).otherwise(pl.col(c)).alias(c) for c in Repl_Dataset.columns if c in ['shelfCapacity', 'pallet_capacity'])\\\n", "        .with_columns(pl.when(pl.col(c) == 0).then(pl.col(c).mean().over(['country','tpnb'])).otherwise(pl.col(c)).alias(c) for c in Repl_Dataset.columns if c in ['item_price'])\\\n", "        .with_columns(pl.when(pl.col(\"stock\") == 0).then(pl.col('sold_units')).otherwise(pl.col(\"stock\")))\\\n", "        .with_columns(pl.when(pl.col('shelfCapacity') == 0).then(1).otherwise(pl.col('shelfCapacity')).alias(\"shelfCapacity\"))\\\n", "        .with_columns(pl.when(pl.col(\"opening_type\").is_null()).then(pl.lit(\"no_data\")).otherwise(pl.col(\"opening_type\")).alias(\"opening_type\"),\n", "                     pl.lit(\"Y\").alias('as_is_model_contains?'))\n", "        print(\"\\nModifications on Columns are done!!\")\n", "        return result\n", "\n", "\n", "    def optimization(Repl_Dataset):\n", "\n", "        result = Repl_Dataset.with_columns(\n", "            pl.col(\n", "                \n", "                [\n", "                \"srp\",\n", "                \"nsrp\",\n", "                \"mu\",\n", "                \"full_pallet\",\n", "                \"split_pallet\",\n", "                \"icream_nsrp\",\n", "                \"shelfCapacity\",\n", "                \"extra disassemble %\",\n", "                \"pallet_capacity\",\n", "                \"case_capacity\" ,\n", "                 \"sold_units\",\n", "                \"stock\",\n", "                \"sales_excl_vat\",\n", "                \"weight\",\n", "                \"item_price\",\n", "                \"unit\",\n", "                \"foil\",\n", "                \"cases_delivered\"\n", "\n", "            ]).cast(pl.Float32),\\\n", "            pl.col(\n", "                    [\n", "                \"checkout_stand_flag\",\n", "                \"backroom_flag\",\n", "                \"clipstrip_flag\",\n", "                \"is_capping_shelf\",\n", "                \"single_pick\",\n", "                \"SRP opening reduction opportunity\",\n", "\n", "                    ]\n", "                ).cast(pl.Int8),\\\n", "            pl.col(\n", "\n", "                [\n", "                    'pmg',\n", "                    'day', \n", "                    'country',\n", "                    'format',\n", "                    'division',\n", "                    'dep',\n", "                    'as_is_model_contains?',\n", "                    'opening_type'\n", "                ]\n", "            ).cast(pl.Categorical),\\\n", "            pl.col(\n", "                [\n", "                    'DIV_ID',\n", "                    'DEP_ID', \n", "                    'SEC_ID',\n", "                    'GRP_ID',\n", "                    'SGR_ID',\n", "\n", "                ]\n", "            ).cast(pl.Int16),\\\n", "            pl.col(\n", "                [\n", "                    'tpnb',\n", "                    'store' \n", "                ]\n", "            ).cast(pl.Int32)\n", "\n", "                \n", "            )\n", "\n", "            \n", "        \n", "        print(\"\\nOptimization on Columns are done!!\")\n", "        return result\n", "\n", "    \n", "    def memory_usage(Repl_Dataset):\n", "        print(\"Memory Usage: \" \"{:.1f} gb\".format(Repl_Dataset.estimated_size('gb')))\n", "        return Repl_Dataset\n", "    \n", "    #Repl_Dataset = isold.pipe(all_tpn_all_day).pipe(join_all_table).pipe(mod_columns).pipe(optimization).collect().pipe(debug, \"Dataset shape: \").pipe(memory_usage)\n", "\n", "\n", "    Repl_Dataset = isold.pipe(all_tpn_all_day)\\\n", "                    .pipe(debug, \"tpns with weekdays shape: \")\\\n", "                    .pipe(join_all_table)\\\n", "                    .pipe(debug, \"Tables's combination shape\")\\\n", "                    .pipe(mod_columns)\\\n", "                    .pipe(memory_usage)\\\n", "                    .pipe(debug, \"End shape\")\\\n", "                    .pipe(optimization)\\\n", "                    .pipe(memory_usage)\n", "\n", "\n", "# Repl_Dataset = Repl_Dataset.to_pandas()\n", "print(time.perf_counter() - start)"]}, {"cell_type": "code", "execution_count": null, "id": "95cf9b63-09d5-40d2-b073-5423c1fe6f76", "metadata": {}, "outputs": [], "source": ["import pyarrow.parquet as pq\n", "import polars as pl\n", "import pandas as pd\n", "import time\n", "from pathlib import Path\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "start = time.perf_counter()\n", "\n", "directory = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\")\n", "excel_inputs_f = \"inputs/Repl/Stores_Inputs_2023_Q1_SFM_SFB.xlsx\" #_12001_back\n", "\n", "stores = list(\n", "    pd.read_excel(directory / excel_inputs_f, usecols=[\"Country\", \"Format\", \"Store\"])[\"Store\"]\n", "    .unique()\n", ")\n", "\n", "\n", "repl_dataset_f = r\"inputs\\as_is_modelDataSet_updated_12-06_op_type_frozen_srp_test_new_method_\"\n", "\n", "\n", "\n", "# Repl_Dataset = pq.read_table(\n", "#     directory / repl_dataset_f,\n", "#     filters=[(\"store\", \"in\", stores)],\n", "# ).to_pandas()\n", "\n", "Repl_Dataset = pl.read_parquet(\n", "    directory / repl_dataset_f).filter(pl.col('store').is_in(stores))#.collect()#.to_pandas()\n", "\n", "\n", "print(time.perf_counter() - start)"]}, {"cell_type": "code", "execution_count": 10, "id": "ac452bd6-45d4-4c48-b58f-afc6904c455a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr > th,\n", ".dataframe > tbody > tr > td {\n", "  text-align: right;\n", "}\n", "</style>\n", "<small>shape: (5, 49)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>store</th><th>pmg</th><th>tpnb</th><th>day</th><th>sold_units</th><th>sales_excl_vat</th><th>country</th><th>tpn</th><th>ownbrand</th><th>unit_type</th><th>weight</th><th>division_hier</th><th>DIV_ID</th><th>department</th><th>DEP_ID</th><th>section</th><th>SEC_ID</th><th>group</th><th>GRP_ID</th><th>subgroup</th><th>SGR_ID</th><th>product_name</th><th>format</th><th>division</th><th>dep</th><th>is_capping_shelf</th><th>sold_units_dotcom</th><th>stock</th><th>item_price</th><th>srp</th><th>nsrp</th><th>mu</th><th>full_pallet</th><th>split_pallet</th><th>icream_nsrp</th><th>checkout_stand_flag</th><th>clipstrip_flag</th><th>backroom_flag</th><th>shelfCapacity</th><th>case_capacity</th><th>unit</th><th>opening_type</th><th>pallet_capacity</th><th>foil</th><th>SRP opening reduction opportunity</th><th>extra disassemble %</th><th>single_pick</th><th>cases_delivered</th><th>as_is_model_contains?</th></tr><tr><td>i32</td><td>cat</td><td>i32</td><td>cat</td><td>f32</td><td>f32</td><td>cat</td><td>i64</td><td>cat</td><td>cat</td><td>f32</td><td>cat</td><td>i16</td><td>cat</td><td>i16</td><td>cat</td><td>i16</td><td>cat</td><td>i16</td><td>cat</td><td>i16</td><td>cat</td><td>cat</td><td>cat</td><td>cat</td><td>i8</td><td>f64</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>i8</td><td>i8</td><td>i8</td><td>f32</td><td>f32</td><td>f32</td><td>cat</td><td>f32</td><td>f32</td><td>i8</td><td>f32</td><td>i8</td><td>f32</td><td>cat</td></tr></thead><tbody><tr><td>24102</td><td>&quot;HDL10&quot;</td><td>100761028</td><td>&quot;Sunday&quot;</td><td>0.714286</td><td>0.0</td><td>&quot;SK&quot;</td><td>2005100761028</td><td>&quot;N&quot;</td><td>&quot;SNGL&quot;</td><td>0.07</td><td>&quot;CH Hardlines&quot;</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Boys&quot;</td><td>35</td><td>&quot;Collectables&quot;</td><td>25</td><td>&quot;Bakugan Legend…</td><td>&quot;1K&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>0.714286</td><td>9.01</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>9.010956</td><td>8.0</td><td>0.0</td><td>&quot;no_data&quot;</td><td>48.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.0</td><td>&quot;Y&quot;</td></tr><tr><td>24102</td><td>&quot;HDL11&quot;</td><td>100107632</td><td>&quot;Thursday&quot;</td><td>9.714286</td><td>0.822857</td><td>&quot;SK&quot;</td><td>2005100107632</td><td>&quot;Y&quot;</td><td>&quot;SNGL&quot;</td><td>1.0</td><td>&quot;CH Hardlines&quot;</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Outdoor&quot;</td><td>85</td><td>&quot;Bubbles&quot;</td><td>35</td><td>&quot;CAROUSEL ROZTO…</td><td>&quot;1K&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>9.714286</td><td>2.69</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>4.424072</td><td>10.0</td><td>1.428571</td><td>&quot;no_data&quot;</td><td>75.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.142857</td><td>&quot;Y&quot;</td></tr><tr><td>24102</td><td>&quot;HDL11&quot;</td><td>100107632</td><td>&quot;Friday&quot;</td><td>9.142857</td><td>0.846429</td><td>&quot;SK&quot;</td><td>2005100107632</td><td>&quot;Y&quot;</td><td>&quot;SNGL&quot;</td><td>1.0</td><td>&quot;CH Hardlines&quot;</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Outdoor&quot;</td><td>85</td><td>&quot;Bubbles&quot;</td><td>35</td><td>&quot;CAROUSEL ROZTO…</td><td>&quot;1K&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>9.142857</td><td>2.69</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>4.424072</td><td>10.0</td><td>0.0</td><td>&quot;no_data&quot;</td><td>75.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.0</td><td>&quot;Y&quot;</td></tr><tr><td>24102</td><td>&quot;HDL11&quot;</td><td>100644796</td><td>&quot;Sunday&quot;</td><td>35.285713</td><td>0.637857</td><td>&quot;SK&quot;</td><td>2005100644796</td><td>&quot;Y&quot;</td><td>&quot;SNGL&quot;</td><td>0.04</td><td>&quot;CH Hardlines&quot;</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Outdoor&quot;</td><td>85</td><td>&quot;Balls&quot;</td><td>45</td><td>&quot;Go Play Mini B…</td><td>&quot;1K&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>35.285713</td><td>1.329286</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>4.424072</td><td>24.0</td><td>0.0</td><td>&quot;no_data&quot;</td><td>40.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.0</td><td>&quot;Y&quot;</td></tr><tr><td>24102</td><td>&quot;HDL11&quot;</td><td>100647782</td><td>&quot;Sunday&quot;</td><td>7.214286</td><td>0.36</td><td>&quot;SK&quot;</td><td>2005100647782</td><td>&quot;N&quot;</td><td>&quot;SNGL&quot;</td><td>0.05</td><td>&quot;CH Hardlines&quot;</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Outdoor&quot;</td><td>85</td><td>&quot;Beach and sand…</td><td>115</td><td>&quot;Kropiaca kanva…</td><td>&quot;1K&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>7.214286</td><td>1.507857</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>4.424072</td><td>11.0</td><td>0.0</td><td>&quot;no_data&quot;</td><td>60.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.0</td><td>&quot;Y&quot;</td></tr></tbody></table></div>"], "text/plain": ["shape: (5, 49)\n", "┌───────┬───────┬───────────┬──────────┬───┬─────────────────────┬─────────────┬─────────────────┬─────────────────────┐\n", "│ store ┆ pmg   ┆ tpnb      ┆ day      ┆ … ┆ extra disassemble % ┆ single_pick ┆ cases_delivered ┆ as_is_model_contain │\n", "│ ---   ┆ ---   ┆ ---       ┆ ---      ┆   ┆ ---                 ┆ ---         ┆ ---             ┆ s?                  │\n", "│ i32   ┆ cat   ┆ i32       ┆ cat      ┆   ┆ f32                 ┆ i8          ┆ f32             ┆ ---                 │\n", "│       ┆       ┆           ┆          ┆   ┆                     ┆             ┆                 ┆ cat                 │\n", "╞═══════╪═══════╪═══════════╪══════════╪═══╪═════════════════════╪═════════════╪═════════════════╪═════════════════════╡\n", "│ 24102 ┆ HDL10 ┆ 100761028 ┆ Sunday   ┆ … ┆ 0.0                 ┆ 0           ┆ 0.0             ┆ Y                   │\n", "│ 24102 ┆ HDL11 ┆ 100107632 ┆ Thursday ┆ … ┆ 0.0                 ┆ 0           ┆ 0.142857        ┆ Y                   │\n", "│ 24102 ┆ HDL11 ┆ 100107632 ┆ Friday   ┆ … ┆ 0.0                 ┆ 0           ┆ 0.0             ┆ Y                   │\n", "│ 24102 ┆ HDL11 ┆ 100644796 ┆ Sunday   ┆ … ┆ 0.0                 ┆ 0           ┆ 0.0             ┆ Y                   │\n", "│ 24102 ┆ HDL11 ┆ 100647782 ┆ Sunday   ┆ … ┆ 0.0                 ┆ 0           ┆ 0.0             ┆ Y                   │\n", "└───────┴───────┴───────────┴──────────┴───┴─────────────────────┴─────────────┴─────────────────┴─────────────────────┘"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["Repl_Dataset.tail()"]}, {"cell_type": "code", "execution_count": 11, "id": "58d36b5f-e03f-4aa6-aa2c-632a6b31210b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr > th,\n", ".dataframe > tbody > tr > td {\n", "  text-align: right;\n", "}\n", "</style>\n", "<small>shape: (3, 3)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>country</th><th>tpnb</th><th>avg_item_price</th></tr><tr><td>cat</td><td>i32</td><td>f64</td></tr></thead><tbody><tr><td>&quot;SK&quot;</td><td>100761028</td><td>10.367738</td></tr><tr><td>&quot;CZ&quot;</td><td>100761028</td><td>260.669193</td></tr><tr><td>&quot;HU&quot;</td><td>100761028</td><td>4508.366747</td></tr></tbody></table></div>"], "text/plain": ["shape: (3, 3)\n", "┌─────────┬───────────┬────────────────┐\n", "│ country ┆ tpnb      ┆ avg_item_price │\n", "│ ---     ┆ ---       ┆ ---            │\n", "│ cat     ┆ i32       ┆ f64            │\n", "╞═════════╪═══════════╪════════════════╡\n", "│ SK      ┆ 100761028 ┆ 10.367738      │\n", "│ CZ      ┆ 100761028 ┆ 260.669193     │\n", "│ HU      ┆ 100761028 ┆ 4508.366747    │\n", "└─────────┴───────────┴────────────────┘"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["Repl_Dataset.whith_columns(pl.when(pl.col('tpnb')==100761028)).then(pl.col('tpnb').mean().over(['country', 'tpnb'])).otherwise(0).filter(pl.col('tpnb')==100761028)"]}, {"cell_type": "code", "execution_count": 33, "id": "76790a1f-0a3b-4b4e-89dc-2f33074e05df", "metadata": {}, "outputs": [], "source": ["a = Repl_Dataset.groupby(['country','division']).agg(sales = pl.col(\"sold_units\").mean()).with_columns(pl.col(\"sales\").cast(pl.Int64)).to_pandas()"]}, {"cell_type": "code", "execution_count": 30, "id": "b459d58c-9015-46ee-9f89-6bac2a38c6aa", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go"]}, {"cell_type": "code", "execution_count": 34, "id": "843a4bc8-da5f-439d-af50-28c6a7cf78a7", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"name": "SK", "type": "bar", "visible": true, "x": ["Produce", "GM", "Grocery", "Fresh"], "y": [16, 17, 26, 24]}, {"name": "HU", "type": "bar", "visible": true, "x": ["Fresh", "GM", "Produce", "Grocery"], "y": [26, 15, 15, 30]}, {"name": "CZ", "type": "bar", "visible": true, "x": ["GM", "Produce", "Fresh", "Grocery"], "y": [16, 17, 27, 28]}], "layout": {"autosize": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Sales"}, "updatemenus": [{"active": 0, "buttons": [{"args": [{"visible": [true, true, true]}], "label": "All", "method": "update"}, {"args": [{"visible": [true, false, false]}], "label": "SK", "method": "update"}, {"args": [{"visible": [false, true, false]}], "label": "HU", "method": "update"}, {"args": [{"visible": [false, false, true]}], "label": "CZ", "method": "update"}]}], "xaxis": {"autorange": true, "range": [-0.5, 3.5], "title": {"text": "Division"}, "type": "category"}, "yaxis": {"autorange": true, "range": [0, 31.57894736842105], "title": {"text": "Sales"}, "type": "linear"}}}, "image/png": "iVBORw0KGgoAAAANSUhEUgAABrsAAAFoCAYAAADn+EAcAAAAAXNSR0IArs4c6QAAIABJREFUeF7s3X/05XV92Pn3d8RxJjoioPwYJaikCVpjaSw6bU4IkXZNhnCSkHUWk6ZaWHaCe5ojTIcyuiyHdXWmkAHXniOZshBpwmrHlmpcJ+k2JoR0E1JKQtUVmgQSNB2EioAkgiB893yu3m+/8+U783197r2vz33fz33MH4nA677v5/N4XcZxnnPvXVhcXFwsfhAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBCYQYEFsWsGt+aSCRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIEBgJilxcCAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDAzAqIXTO7OhdOgAABAgQIECBAgAABAgQIECBAgAABAgQIECAgdnkNECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIzKyA2DWzq3PhBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECYpfXAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAwMwKiF0zuzoXToAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgIHZ5DRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECMysgNg1s6tz4QQIECBAgAABAgQIECBAgAABAgQIECBAgAABAmKX1wABAgQIECBAgAABAgQIECBAgAABAgQIECBAgMDMCohdM7s6F06AAAECBAgQIECAAAECBAgQIECAAAECBAgQICB2eQ0QIECAAAECBAgQIECAAAECBAgQIECAAAECBAjMrIDYNbOrc+EECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQJil9cAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDAzAqIXTO7OhdOgAABAgQIECBAgAABAgQIECBAgAABAgQIECAgdnkNECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIzKyA2DWzq3PhBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECYpfXAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAwMwKiF0zuzoXToAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgIHZ5DRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECMysgNg1s6tz4QQIECBAgAABAgQIECBAgAABAgQIECBAgAABAmKX1wABAgQIECBAgAABAgQIECBAgAABAgQIECBAgMDMCohdM7s6F06AAAECBAgQIECAAAECBAgQIECAAAECBAgQICB2eQ0QIECAAAECBAgQIECAAAECBAgQIECAAAECBAjMrIDYNbOrc+EECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQJil9cAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDAzAqIXTO7OhdOgAABAgQIECBAgAABAgQIECBAgAABAgQIECAgdnkNECBAgAABAgQIECBAgAABAgQIECBAgAABAgQIzKyA2DWzq3PhBAgQIECAAAECBAgQIECAAAECBAgQIECAAAECYpfXAAECBAgQIECAAAECBAgQIECAAAECBAgQIECAwMwKiF0zuzoXToAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgIHZ5DRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECMysgNg1s6tz4QQIECBAgAABAgQIECBAgAABAgQIECBAgAABAmKX1wABAgQIECBAgAABAgQIECBAgAABAgQIECBAgMDMCohdM7s6F06AAAECBAgQIECAAAECBAgQIECAAAECBAgQICB2eQ0QIECAAAECBAgQIECAAAECBAgQIECAAAECBAjMrIDYNYOru/XA7eUjN3+q7Lt6Rzn1lM0zeAcumQABAgQIECBAgAABAgQIECBAgAABAgQIECAwGQGxazKORzyliVNXXH3T82Y++qHLyxmnn9b6CsSu1mQeQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECPRUQOxKXOyTTz1drrzmpvKHX/iT570L69p9+8uNHztQ3n/ZBeW8rWe2ugqxqxWXYQIECBAgQIAAAQIECBAgQIAAAQIECBAgQKDHAmJX4nLvvPve8q737CmHewfXfQ8cLH/6Z39R3nbWm1tdhdjVisswAQIECBAgQIAAAQIECBAgQIAAAQIECBAg0GMBsStxuW2j1Gofd3jSCcc9711hhzv30cefKBdffl35/D33L93VytA2fLfZZz57x9LMOWdvKVftvKBs3LA+UcPRBAgQIECAAAECBAgQIECAAAECBAgQIECAAIHJC4hdkzddOnH4zq5oTGoiVvNj+ccarha2Vvt7w+da/rGIzTvHtl+2t7z7nT8xOHMYuprnGMatYSB78+mnlUu3b0vUcDQBAgQIECBAgAABAgQIECBAgAABAgQIECBAYPICYtfkTQ85MfpurcNdxjBGbTv3rKUItjJ2DSPWiccf+7xg1Xw32H+4+95y/Z5Lytcee+KQ+DV8zubxt99xd+uPU0ymczwBAgQIECBAgAABAgQIECBAgAABAgQIECBAYE0BsWtNoskMDN9l9eBDjywduNpHFK421zxg+Tu2Vsau4WN277qonHH6aYdccPOOr127bxh8FOKxL9u09DGHh/sescncrVMIECBAgAABAgQIECBAgAABAgQIECBAgAABAt0IiF3dOD/vWYaB6gfe8NeWPlKweRfWjR87cEjYiryza/gRhoe7leVRbbXv9fr+17128M6vY47eNCUNT0uAAAECBAgQIECAAAECBAgQIECAAAECBAgQGE1A7BrNbSKPinzEYCR2HemdXWtdaNvvFVvrPP+cAAECBAgQIECAAAECBAgQIECAAAECBAgQINClgNiVqP1vb/sP5Xte86py6imbn/csw+/Zav7BVTsvKF+49/7yrvfsKSs/XjASu4Yzbz79tOd9Z9fyJ26i2JNPfbO84ftec8j1LI9u3t2V+IJwNAECBAgQIECAAAECBAgQIECAAAECBAgQIDBxAbFr4qT/7cDmu7WuuPqmcs7ZW5Y+qrD5p8PQ9Ydf+JPBd2k1MWy1YLX8IweP9J1dzZnDd2hd+I6thwSv5ozdH76l7PqFny1fe+yJsv2yveXd7/yJct7WMwcXGg1liUyOJkCAAAECBAgQIECAAAECBAgQIECAAAECBAiMLCB2jUwXe+Dhvk9rZZRaHp4+f8/9g8Ob79L64K6Lynt331C2nXvWUqBqItpHbv7UUigbXslq38fV/LPloWy161n+z2N3ZYoAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUIeA2FXHHlwFAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDACAJi1whoHkKAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFCHgNhVxx5cBQECBAgQIECAAAECBAgQIECAAAECBAgQIECAwAgCYtcIaB5CgAABAgQIECBAgAABAgQIECBAgAABAgQIECBQh4DYVcceXAUBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMAIAmLXCGgeQoAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUIeA2FXHHlwFAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDACAJi1whoHkKAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFCHgNhVxx5cBQECBAgQIECAAAECBAgQIECAAAECBAgQIECAwAgCYtcIaB5CgAABAgQIECBAgAABAgQIECBAgAABAgQIECBQh4DYVcceXAUBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMAIAmLXCGgeQoAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUIeA2FXHHlwFAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDACAJi1whoHkKAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFCHgNhVxx5cBQECBAgQIECAAAECBAgQIECAAAECBAgQIECAwAgCYtcIaB5CgAABAgQIECBAgAABAgQIECBAgAABAgQIECBQh4DYVcceXAUBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMAIAmLXCGgeQoAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUIeA2FXHHlwFAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDACAJi1whoHkKAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFCHgNhVxx5cBQECBAgQIECAAAECBAgQIECAAAECBAgQIECAwAgCYtcIaB5CgAABAgQIECBAgAABAgQIECBAgAABAgQIECBQh4DYVcceXAUBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMAIAmLXCGgeQoAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUIeA2FXHHlwFAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDACAJi1whoHkKAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFCHgNhVxx5cBQECBAgQIECAAAECBAgQIECAAAECBAgQIECAwAgCYtcIaB5CgAABAgQIECBAgAABAgQIECBAgAABAgQIECBQh4DYVcceXAUBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMAIAmLXCGgeQoAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUIeA2FXHHlwFAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDACAJi1whoHkKAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFCHgNhVxx5cBQECBAgQIECAAAECBAgQIECAAAECBAgQIECAwAgCYtcIaB5CgAABAgQIECBAgAABAgQIECBAgAABAgQIECBQh4DYVcceXAUBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMAIAmLXCGgeQoAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUIeA2FXHHlwFAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDACAJi1whoHkKAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFCHgNhVxx5cBQECBAgQIECAAAECBAgQIECAAAECBAgQIECAwAgCYtcIaB5CgAABAgQIECBAgAABAgQIECBAgAABAgQIECBQh4DYVcceXAUBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMAIAmLXCGgeQoAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUIeA2FXHHlwFAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDACAJi1whoHkKAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFCHgNhVxx5cBQECBAgQIECAAAECBAgQIECAAAECBAgQIECAwAgCYtcIaB5CgAABAgQIECBAgAABAgQIECBAgAABAgQIECBQh4DYVcceXAUBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMAIAmLXCGgeQoAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUIeA2FXHHlwFAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDACAJi1whoHkKAAAECBAgQIECAAAECBAgQIECAAAECBAgQIFCHgNhVxx5m+ioOPvLkTF+/iydAgAABAgQIECBAgAABAgQIECBAgAABAocT2HzcRjiVC4wUu377D/648tua3OX9yFu+d3KH9fQksauni3VbBAgQIECAAAECBAgQIECAAAECBAgQIFDErvpfBCPHrnmIQE3Um4f7HPdlKnaNK+jxBAgQIECAAAECBAgQIECAAAECBAgQIFCrgNhV62b+23WJXWvsSPBa+0Usdq1tZIIAAQIECBAgQIAAAQIECBAgQIAAAQIEZlNA7Kp/b2KX2DUQuHbf/nLjxw4saXz0Q5eXM04/bemvbz1we7ni6psGf33O2VvKVTsvKBs3rB/8tdhV/7/orpAAAQIECBAgQIAAAQIECBAgQIAAAQIERhMQu0Zz6/JRYtca2vPwzq5HH3+i/PLHf71c/M6fHASs+x44WN63+4bygV0XlVNP2VzuvPvesnff/nL9nkvKMUdvGoSx5sel27eJXV3+2+q5CBAgQIAAAQIECBAgQIAAAQIECBAgQKBzAbGrc/LWTyh2iV3PE2ji18WXX1d2bN82eHdXE7deffKJ5bytZw5mV8Yv7+xq/e+dBxAgQIAAAQIECBAgQIAAAQIECBAgQIDAjAiIXfUvSuwSu54n0MSsXbtvKPuu3lE2n/DycuU1N5Utb3r9Uuxa+c4vsav+f9FdIQECBAgQIECAAAECBAgQIECAAAECBAiMJiB2jebW5aPELrFrSaCJWNsv21sefOiRMvzOriefenoQu95+7llL3+G1MnY98eS3unzNei4CBAgQmJDA4uJiWVhYmNBpjiFAgACBrgT8/N2V9Ow9z+JiKc81/8eP6QosLJQX+CXWdHdQ6bM3/3r65Xely3FZBAgQWENg08ajZsbovi99qzz6ePzXhMcdW8prXvnCmbm/w12o2LXGCufhO7tWEiz/GMM3nPbaNd/Z9cQ3npn5fxHcAAECBOZRoPllj9+HmcfNu2cCBGZdYLEslIUS/x+vs36/rj8u8NQff7G84F//81IErzhawuSzP/hjZf2ZbyvrVI0E3dk+cnFhoSz493O2l+jqCRCYW4FN3zU7Mej373q63PgvYr/j84J1pWy/sJQfeMPs3J/YNeK/hvMYuxqq5d/T5Tu7RnzxeBgBAgQIECBAgAABAgQ6FFj3J58vG67bIXZ1aL7aUz393/98eebsn/aHiqa8B09PgAABAgQmKTBLH2PYdey69cDt5YqrbzqEe/jJccvfWHPG6acNZoafJnfi8ceWS7dvm9iaqnhn1/Dmht8L1cSV5sckb3RUsXmIXc3HEn72d+8q/9PfP3fANPw4w927Lhp8dGHzHV579+0v1++5pBxz9KZBCFu+H9/ZNeqry+MIECBAgAABAgQIECAwOQGxa3KW45wkdo2j57EECBAgQKBOAbFr9b2sbAfDvjDsDStj18oWNMltdxq7hjfypYMPL4WT5mYyY9cQ8/P33L+q2/e/7rWHXMvKoXmIXUP/z3z2jqXbH5bX4d9YXmfPOXtLuWrnBWXjhvWDfyx2TfJfSWcRIECAAAECBAgQIEBgNAGxazS3ST9K7Jq0qPMIECBAgMD0BcSu1Xew1huXVsaulZ8iN8nNdhq7mncM7fsXv1a+/pffKBe+Y+vgXUPNj8zY1Zzf1MV3vWfPqm4ro87KoXmIXeO+oMSucQU9ngABAgQIECBAgAABAuMLiF3jG07iBLFrEorOIECAAAECdQmIXavvY/gmmcN1luWx68sHHy5//uWvpH2iX6exq7nx4Y/lN5Udu1Z751JzHSvfobTausSutX9SEbvWNjJBgAABAgQIECBAgACBbAGxK1s4dr7YFXMyRYAAAQIEZklA7Dr8tlZ+Z9fyT9Mbxq43n35aOfBbf1D2Xb2jnHrK5pTVdxa7muB0zfUfLz973t8d3Mg1H/l42f3eiwbfAZUdu5rnW+3dXWu9q6t5nNi19utO7FrbyAQBAgQIECBAgAABAgSyBcSubOHY+WJXzMkUAQIECBCYJQGxK7atYetpppuvQnrqm98sF19+XWli16tPPrHs//RtR/xaqdizrD7VWexqPsLwllt/s+y8+PzBlVx5zU1ly5teX87bemYnsWvlu7si7+oSu2IvLbEr5mSKAAECBAgQIECAAAECmQJiV6Zu/GyxK25lkgABAgQIzIqA2BXfVPPGo7379g+iVvOjiV07tm8bfK1V851dX3n4a4MQtnHD+vihgcnOYtfKLx5r3tp2x11fHNxU82N5/FrrS80C97XqyPJ3d0Xe1dUc4p1da2uLXWsbmSBAgAABAgQIECBAgEC2gNiVLRw7X+yKOZkiQIAAAQKzJCB2rb6tpuX80FveOAhZwx/L+87y7+xqZoZvSjrx+GMn/t1dncSu4Q19/p77DxE56YTjBp/RuPmEl3cSu1a+hS5SDsWutX/KEbvWNjJBgAABAgQIECBAgACBbAGxK1s4dr7YFXMyRYAAAQIEZklA7Fp9W6t9fdSF79i6FLJWxq7mlOHf++7Nx0/0HV6dxK7lb1trvqNreeFrPqfxx966pZPY1Txvcy3Nj+Wl8Uj/Uolda/+UI3atbWSCAAECBAgQIECAAAEC2QJiV7Zw7Pyn335xeeatP10WYuOm0gQW0052MAECBAjMn4DYVf/OO4ldh/tYwiY8feLTt5XL/9HfL3v+2a8ufYdX1scYjrIOsWttNbFrbSMTBAgQIECAAAECBAgQyBYQu7KFY+d/6R+9v9zz2lPKs4tiS0wsZ+ol69aXv/Wil+cc7lQCBAgQmDuBWYpd933pW+XRx+O/Djnu2FJe88oXzvxOO4lds6wkdq29PbFrbSMTBAgQIECAAAECBAgQyBYQu7KFY+f/p3/8wfKO9X9WHnz2G7EHmEoR+PEXv6b80it+2DvsUnQdSoAAgfkTmKXYNX/b+fYdi11rbF7sWvtfDbFrbSMTBAgQIECAAAECBAgQyBYQu7KFY+eLXTGn7CmxK1vY+QQIEJgvAbGr/n2LXWLX2K9SsWtsQgcQIECAAAECBAgQIEBgbAGxa2zCiRwgdk2EcexDxK6xCR1AgAABAssExK76Xw5il9g19qtU7Bqb0AEECBAgQIAAAQIECBAYW0DsGptwIgeIXRNhHPsQsWtsQgcQIECAgNg1U68BsUvsGvsFK3aNTegAAgQIECBAgAABAgQIjC0gdo1NOJEDxK6JMI59iNg1NqEDCBAgMDWBhYVSFhen9vSrPrF3dtW1j9WuRuwSu8Z+lYpdYxM6gAABAgQIECBAgAABAmMLiF1jE07kALFrIoxjHyJ2jU3oAAJzJ/D04nNl96N3lfu/9fW5u/eabviEo76r/MKmN5RXvXBTTZdVxK6q1rHqxYhdYtfYr1Kxa2xCBxAgQIAAAQIECBAgQGBsAbFrbMKJHCB2TYRx7EPErrEJHUBg7gSeWVwsb//Kr5c7v/nw3N17TTf81154dPnVE/678qqjXlzTZc1U7Pr6/X9Wnv3aV8N+R73ihLLplO8Oz9c6KHaJXWO/NsWusQkdQIAAAQIECBAgQIAAgbEFxK6xCSdygNg1EcaxDxG7xiZ0AIG5ExC76li52DX+Hh79vX9fFj50eeygF7yglJ3Xlpf9wJti8xVPiV1i19gvT7FrbEIHECBAgAABAgQIECBAYGwBsWtswokcIHZNhHHsQ8SusQkdQGDuBMSuOlYudo2/hy5j151331v27ttfrt9zSTnm6G9/9OSjjz9RLr78urJj+7ZyxumnlVsP3F7uuOuL5aqdF5SNG9avOjP+XZcidoldY7+OxK6xCR1AgAABAgQIECBAgACBsQXErrEJJ3KA2DURxrEPEbvGJnQAgbkTELvqWLnYNf4exK4Whr/9B39cfuQt39viEbM7Ok/3OuqWxK5R5TyOAAECBAgQIECAQD8EHntsoXzrmYV+3MwM38Wxj/yn8pIP7yhlcXGG72L2L13sqmOHYlcde3AVQYFvPlXWPfpwKX76DoLljD2zYUP56af+o+/syuENnyp2hakOOyh2tTCclwA0L/fZYvWrjopd4wp6PAECBAgQIECAAIHZFvjcF9aV3/4dsWvaW/z5M+8ux1wvdk17D2LXtDfw7ecXu+rYg6uICaz7rwfLC2/50LeDlx9TE/jGD20tP/XXXyJ2TW0D335isWv8BYhdLQybCNT86PO7u+bhHlus/IijYtekJJ1DgAABAgQIECBAYDYF/uMfrSu/9ul1s3nxPbrq9/3kH5VjfknsmvZKxa5pb+Dbzy921bEHVxETWHj4v5QXfXhXWffIg7EHmEoR+MaPnl9+8s0niV0puvFDxa641eEmu45d73rPnlUv5aMfurz+7+waXvkwCI3PX98JfQ55k9YWuyYt6jwCBAgQIECAAAECsyUgdtWxL7Grjj2IXXXsQeyqYw+uIiYgdsWcsqfErmzh2PliV8zpSFNdx669+/aX6/dcUo45etPgsh59/Ily8eXXlR3btx0xdu364A1l57vPL6eesnn8my6lLCwu+jDviUjO8SFi1xwv360TIECAAAECBAgQKKWIXXW8DMSuOvYgdtWxB7Grjj24ipiA2BVzyp4Su7KFY+eLXTGnWYpdd959b/nEp28rV+28oGzcsH5w6fc9cLC8b/cN5QO7LhK7xl+5EyYlIHZNStI5BAgQIECAAAECBGZTQOyqY29iVx17ELvq2IPYVcceXEVMQOyKOWVPiV3ZwrHzxa6Y0yzFruE7vbade1Y5b+uZg0u/dt/+8pWHv3ZIABv3zr2za1xBjy9ilxcBAQIECBAgQIAAgfkWELvq2L/YVccexK469iB21bEHVxETELtiTtlTYle2cOx8sSvmNEuxq7nW5p1c2y/bWx586JHBpZ9z9paJhq7mTLFr/NfO3J8gds39SwAAAQIECBAgQIDAnAuIXXW8AMSuOvYgdtWxB7Grjj24ipiA2BVzyp4Su7KFY+eLXTGnWmLX+Fc7uRPErslZzu1JYtfcrt6NEyBAgAABAgQIEBgIiF11vBDErjr2IHbVsQexq449uIqYgNgVc8qeEruyhWPni10xpyNNPX7/n5XnvvbV8EFHveKEsumU7w7P1zoodtW6mRm6LrFrhpblUgkQIECAAAECBAgkCIhdCagjHCl2jYCW8BCxKwF1hCPFrhHQPGRqAmLX1OgPeWKxq449iF117GEWr0LsmsWtVXbNYldlC3E5BAgQIECAAAECBDoWELs6Bj/M04lddexB7KpjD2JXHXtwFTEBsSvmlD0ldmULx84Xu2JOpp4vIHZ5VYwtIHaNTegAAgQIECBAgADjtH0zAAAgAElEQVQBAjMtIHbVsT6xq449iF117EHsiu1hoSyUxdioqUSBhYf/orzow7vKukceTHwWR68lIHatJdTNPxe7unHu47OIXX3casf3JHZ1DO7pCBAgQIAAAQIECFQmIHbVsRCxq449iF117EHsiu3h4f+6UO65d115TvGKgSVNvfEVXy6vvOVysSvJN3qs2BWVyp0Tu3J9+3y62NXn7XZ0b2JXR9CehgABAgQIECBAgEClAmJXHYsRu+rYg9hVxx7ErtgevvSlhXLzr76gPPOt2LypHIELfuxL5bRPiV05uvFTxa64Veak2JWp2++zxa5+77eTuxO7OmH2JAQIECBAgAABAgSqFRC76liN2FXHHsSuOvYgdsX2IHbFnLKnxK5s4dj5YlfMKXtK7MoW7u/5Yld/d9vZnYldnVF7IgIECBAgQIAAAQJVCohddaxF7KpjD2JXHXsQu2J7ELtiTtlTYle2cOx8sSvmlD0ldo0vfNdjj5SDT/9V+KBTNmwqb3zpMeH5WgfFrlo3M0PXJXbN0LJcKgECBAgQIECAAIEEAbErAXWEI8WuEdASHiJ2JaCOcKTYFUMTu2JO2VNiV7Zw7HyxK+aUPSV2jS/8ia/cX7b9l38bOuiFC+vKJ0/eWra+4uTQfM1DYlfN25mRaxO7ZmRRLpMAAQIECBAgQIBAkoDYlQTb8lixqyVY0rjYlQTb8lixKwYmdsWcsqfErmzh2PliV8wpe0rsGl94GrHr2n37y40fO7B08eecvaVctfOCcvChr5btl+0tDz70yPNu7KMfuryccfpp49/wd04QuyZGOb8HiV3zu3t3ToAAAQIECBAgQKARELvqeB2IXXXsQeyqYw9iV2wPYlfMKXtK7MoWjp0vdsWcsqfErvGFu4xdTz71dLnympsGF93ErY0b1g/+860Hbi8nbz5+1Zh15933lk98+rZD5se/61LErkkozvkZYtecvwDcPgECBAgQIECAwNwLiF11vATErjr2IHbVsQexK7YHsSvmlD0ldmULx84Xu2JO2VNi1/jCXcauJlzt3be/XL/nknLM0ZvWvPj7HjhY3rf7hvKBXReVU0/ZvOZ8mwGxq42W2VUFxC4vDAIECBAgQIAAAQLzLSB21bF/sauOPYhddexB7IrtQeyKOWVPiV3ZwrHzxa6YU/aU2DW+cJexq/n4wubHpdu3rXnhjz7+RLn48uvKju3bJvrxhcMnFrvWXIGBtQTErrWE/HMCBAgQIECAAAEC/RYQu+rYr9hVxx7Erjr2IHbF9iB2xZyyp8SubOHY+WJXzCl7SuwaX7jr2PXqk08s5209c80Lb8JYdHbNw1YZELtGUfOYQwTELi8IAgQIECBAgAABAvMtIHbVsX+xq449iF117EHsiu1B7Io5ZU+JXdnCsfPFrphT9pTYNb5w17GrueK13tnVfIfXHXd9ceLf07VcS+wa/7Uz9yeIXXP/EgBAgAABAgQIECAw5wJiVx0vALGrjj2IXXXsQeyK7UHsijllT4ld2cKx88WumFP2lNg1vnCXsSvynV2RmfHvuhSxaxKKc36G2DXnLwC3T4AAAQIECBAgMPcCYlcdLwGxq449iF117EHsiu1B7Io5ZU+JXdnCsfPFrphT9pTYNb5wl7HryaeeLldec9Pgoq/aeUHZuGH94D837+Q6efPx5Xte88qy64M3lJ3vPr+cesrm8W/uCCeIXam883G42DUfe3aXBAgQIECAAAECBA4nIHbV8doQu+rYg9hVxx7ErtgexK6YU/aU2JUtHDtf7Io5ZU+JXeMLdxm7hlfbfB/XjR87sHTx55y9ZRC/fv237ihXXP3tGLbyx0c/dHk54/TTxr/h75wgdk2Mcn4PErvmd/funAABAgQIECBAgEAjIHbV8ToQu+rYg9hVxx7ErtgexK6YU/aU2JUtHDtf7Io5ZU+JXeMLTyN2jX/V458gdo1vOPcniF1z/xIAQIAAAQIECBAgMOcCYlcdLwCxq449iF117EHsiu1B7Io5ZU+JXdnCsfPFrphT9pTYNb7wXY8+Ug4+81fhg07ZsKm88aXHhOdrHRS7at3MDF2X2DVDy3KpBAgQIECAAAECBBIExK4E1BGOFLtGQEt4iNiVgDrCkWJXDE3sijllT4ld2cKx88WumFP2lNiVLdzf88Wu/u62szsTuzqj9kQECBAgQIAAAQIEqhQQu+pYi9hVxx7Erjr2IHbF9iB2xZyyp8SubOHY+WJXzCl7SuzKFu7v+WJXf3cbvrMnn3q6XHnNTeUzn71j6TErvxzu1gO3L32R3PDL5TZuWD+YF7vC1AYJECBAgAABAgQI9FJA7KpjrWJXHXsQu+rYg9gV24PYFXPKnhK7soVj54tdMafsKbErW7i/54td/d1t+M4effyJ8ssf//Vy8Tt/sjQB68677y27dt9Q9l29o5x6yubBX+/dt79cv+eScszRm8q1+/YPzr50+zaxK6xskAABAgQIECBAgEB/BcSuOnYrdtWxB7Grjj2IXbE9iF0xp+wpsStbOHa+2BVzyp4Su7KF+3u+2NXf3Y58Z038uvjy68qO7dvKGaefNohbrz75xHLe1jMHZ66MX97ZNTK1BxIgQIAAAQIECBDohYDYVccaxa469iB21bEHsSu2B7Er5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9uR7+y+Bw6W9+2+oXxg10Vl8wkvH3zE4ZY3vX4pdi3/5807v8Sukak9kAABAgQIECBAgEAvBMSuOtYodtWxB7Grjj2IXbE9iF0xp+wpsStbOHa+2BVzyp4Su7KF+3u+2NXf3Y50Z8Pv7xrGreFfv/3cswbv8mp+rIxdTzz5rZGey4MIECBAYLoCi4uLZWFhYboX4dkJECBAoLVAbT9/Ly6W8ru//1z5N7/mv1NaL3PCD3jfT/1ROeb6HaU0S/FjagKf27m7nP/C+8uDz35jatfgiUs598WvKTeffHY5al09Pzc1/2rW9svv//ynz5UbP7pQnvFbO1P91+bCrV8u3/fJf1LWPfLgVK9j3p9c7KrjFdDErn/1qh8tr9n40jou6DtXsWnjUVVdj4t5voDY5VWxJDAMWycef+zS93GtjF/N8PNi1zeeoUiAAAECMyjQ/DZYPf/TfwYBXTIBAgSmJLBYFspCqSdmDGLXHYti15ReD8ufVuyqYAmllM/9493l/PVi17S3MYhdr3prXbFrYaEsVBajB7Hr5nVi15RfsGLXlBfwnacXu+rYw1Ls2rCpjgsaxq7vemFV1+NixC6vgcMIrBa6hqO+s8vLhgABAgQIECBAgACBIwn4GMM6Xh8+xrCOPfgYwzr24GMMY3vwMYYxp+wpH2OYLRw7X+yKOWVP+RjDbOH+nu+dXf3dbfjOVnv31vIH33n3vWXvvv3l+j2XlGOO3lSa+NX8uHT7tsH/951dYWqDBAgQIECAAAECBHopIHbVsVaxq449iF117EHsiu1B7Io5ZU+JXdnCsfPFrphT9pTYlS3c3/PFrv7uNnxnzccSbr9sb3nwoUcOecyF79i6FLRuPXB7ueLqmwb//Jyzt5Srdl5QNm5YL3aFlQ0SIECAAAECBAgQ6K+A2FXHbsWuOvYgdtWxB7ErtgexK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbzu7MO7s6o/ZEBAgQIECAAAECBKoUELvqWIvYVccexK469iB2xfYgdsWcsqfErmzh2PliV8wpe0rsyhbu7/liV39329mdiV2dUXsiAgQIECBAgAABAlUKiF11rEXsqmMPYlcdexC7YnsQu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq7+77ezOxK7OqD0RAQIECBAgQIAAgSoFxK461iJ21bEHsauOPYhdsT2IXTGn7CmxK1s4dr7YFXPKnhK7soX7e77YNeXdXrtvf/nKw18bfAdW8+PKa24qn/nsHeWkE44r+67eUU49ZfOUr3Dtpxe71jYyQYAAAQIECBAgQKDPAmJXHdsVu+rYg9hVxx7ErtgexK6YU/aU2JUtHDtf7Io5ZU+JXdnC/T1f7Jribh99/Ily8eXXlR3bt5UzTj+t3Hn3veUTn75tEL6+cO/9S/9544b1U7zKtZ9a7FrbyAQBAgQIECBAgACBPguIXXVsV+yqYw9iVx17ELtiexC7Yk7ZU2JXtnDsfLEr5pQ9JXZlC/f3fLFrirttYteuD95Qdr77/ME7uJp3eTU/Lt2+rdz3wMFyzUc+Xna/96JyzNGbpniVaz+12LW2kQkCBAgQIECAAAECfRYQu+rYrthVxx7Erjr2IHbF9iB2xZyyp8SubOHY+WJXzCl7SuzKFu7v+WLXFHf75FNPDz628O3nnlW+5zWvfN67vPbu21+u33OJ2DXFHXlqAgQIECBAgAABAgTWFhC71jbqYkLs6kJ57ecQu9Y26mJC7Iopi10xp+wpsStbOHa+2BVzyp4Su7KF+3u+2DXl3Tbv4Np+2d7y4EOPlAvfsXXwrq7hxxu++fTTBn9d+w/v7Kp9Q66PAAECBAgQIECAQK6A2JXrGz1d7IpK5c6JXbm+0dPFrpiU2BVzyp4Su7KFY+eLXTGn7CmxK1u4v+eLXf3dbWd3JnZ1Ru2JCBAgQIAAAQIECFQpIHbVsRaxq449iF117EHsiu1B7Io5ZU+JXdnCsfPFrphT9pTYlS3c3/PFrv7utrM7E7s6o/ZEBAgQIECAAAECBKoUELvqWIvYVccexK469iB2xfYgdsWcsqfErmzh2PliV8wpe0rsyhbu7/li15R3O/zers989o5y0gnHlX1X7yibT3j54Lu8trzp9eW8rWdO+QrXfnqxa20jEwQIECBAgAABAgT6LCB21bFdsauOPYhddexB7IrtQeyKOWVPiV3ZwrHzxa6YU/aU2JUt3N/zxa4p7/baffvLq08+sfzYW7eUa67/ePnZ8/5uOfWUzeXOu+8tn/j0beWqnReUjRvWT/kqj/z0YlfV63FxBAgQIECAAAECBNIFxK504tATiF0hpvQhsSudOPQEYleIqYhdMafsKbErWzh2vtgVc8qeEruyhft7vtg1xd0++vgTZdcHbyg7333+4N1cy2PXfQ8cLNd85ONl93svKsccvWmKV7n2U4tdaxuZIECAQFlYKGVxEQSBGRFYKKV4vc7IslwmgSoExK4q1lDErjr2IHbVsQexK7YHsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu6a42yPFLu/smuJiPDWBHgk0beVX//KPy28++eUe3dXs3coLF9aVize9obxpwytm7+K7vOLFxfLbv3tUOXiwCS1+TEtg44bF8oN/57lywvFi17R24HkJzKKA2FXH1sSuOvYgdtWxB7ErtgexK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7przbWw/cXu6464tl1y/8bPlnN/2bwccYHvuyTeXiy68r2849y3d2TXk/np5AHwSufezusvexu/twKzN7DxsWXlA+duLbyptfdPzM3kNXF77/X7+gfOH/E7u68l7teV760sXycz8jdk1zB56bwCwKiF11bE3sqmMPYlcdexC7YnsQu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq4LdNu/ietd79hxyJR/90OXljNNPq+Dq1r4EH2O4tpEJAtMUELumqf/t5xa74jsQu+JWWZNiV5ascwn0W0DsqmO/YlcdexC76tiD2BXbg9gVc8qeEruyhWPni10xp+wpsStbuL/ni1393W1ndyZ2dUbtiQiMJCB2jcQ20QeJXXFOsStulTUpdmXJOpdAvwXErjr2K3bVsQexq449iF2xPYhdMafsKbErWzh2vtgVc8qeEruyhft7vtjV3912dmdiV2fUnojASAJi10hsE32Q2BXnFLviVlmTYleWrHMJ9FtA7Kpjv2JXHXsQu+rYg9gV24PYFXPKnhK7soVj54tdMafsKbErW7i/54td/d1tZ3dWW+z61jML5bHHfN9LZy+AwzxRs4HjXvFsKcUupr0LsWvaG/Axhm02IHa10cqZFbtyXEc59aFnnyxff+7pUR7qMRMU2FheUF71wpdM8MR+HiV21bFXsauOPYhddexB7IrtQeyKOWVPiV3ZwrHzxa6YU/aU2JUt3N/zxa6Od/vo40+Uiy+/rnz+nvvXfObvf91ry/V7LinHHL1pzdlpDtQWux59rJRPf2ahPPrYummyzP1zb968WH76J54r66xh6q8FsWvqK/CdXS1WIHa1wEoaFbuSYEc49vee/Er5J1/7vREe6SGTFLjk6DeW8158aikL/gDPkVzFrkm+6kY/S+wa3W6SjxS7Jqk5+lliV8xO7Io5ZU+JXdnCsfPFrphT9pTYlS3c3/PFrv7utrM7qzF2/cot68pXH1FZOnsRrPJEp3z3YvmH/+BZsWuaS/jOc4td01+CjzGM70DsiltlTYpdWbLtz/2dJw+Wn3no/2n/QI+YqMA/Pe5vl7//ku8Vu9ZQFbsm+rIb+TCxa2S6iT5Q7Joo58iHiV0xOrEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9vO7kzs6ox6pp5I7KpnXWLX9HchdsV3IHbFrbImxa4s2fbnil3tzTIeIXbFVMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7umvNv7HjhYtl+2tzz40CPPuxIfYzjacpqPMfTOrtHsJvkosWuSmuOdJXaN5zeJR4tdcUWxK26VNSl2Zcm2P1fsam+W8QixK6YqdsWcsqfErmzh2PliV8wpe0rsigmLXTGn7CmxK1s4dr7YFXPKnhK7soX7e77YNcXdPvnU0+XKa24qW970+vI3/vr3lFtu/c2y8+Lzy8YN68u1+/aXH3rLG8sZp582xSuMPbV3dsWc5m1K7Kpn42LX9HchdsV3IHbFrbImxa4s2fbnil3tzTIeIXbFVMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7umuNtHH3+i7PrgDWXnu88fXMU1H/l42f3ei8oxR28qd959b/nEp28rV+28YBC/av4hdtW8neld2+tf91x5xxlfLOue/ub0LsIzl/KijeUXX/qX5Rcf/xyNKQqIXXF8sStulTX58pcvlp9/25+Ujc8+kfUUzg0K/NZJLy0/88jvBKeNZQmIXTFZsSvmlD0ldmULx84Xu2JO2VNiV0xY7Io5ZU+JXdnCsfPFrphT9pTYlS3c3/PFrinudnnsOvZlm8ruD99Sdv3Czw5iV/Pxhsvj1xQvc82nFrvWJJrLgbec8Vz56T+9rKz74l1zef+13PRz3/c3yu6fO7/84hNfqOWS5vI6xK742sWuuFXW5Cs3P1e2n7y/fNcn92U9hXMjAi84qvz6//Z/lJ/5+u9Fps0kCohdMVyxK+aUPSV2ZQvHzhe7Yk7ZU2JXTFjsijllT4ld2cKx88WumFP2lNiVLdzf88WuKe52+ccYnrf1zMFHF7765BNL859vPXB7ueOuL3pn1wj78Z1dI6AlPETsSkAd4UixawS0hIeIXXFUsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88XuyrabfNOr4svv658/p77y0knHFf2Xb2jnHrK5oqucPVL8c6u6lc0lQsUu6bC/rwnFbvq2IPYFd+D2BW3ypoUu7JkW54rdrUEyxsXu2K2YlfMKXtK7MoWjp0vdsWcsqfErpiw2BVzyp4Su7KFY+eLXTGn7CmxK1u4v+eLXf3dbWd3JnZ1Rj1TTyR21bEusauOPYhd8T2IXXGrrEmxK0u25bliV0uwvHGxK2YrdsWcsqfErmzh2PliV8wpe0rsigmLXTGn7CmxK1s4dr7YFXPKnhK7soX7e77Y1d/ddnZnYldn1DP1RGJXHesSu+rYg9gV34PYFbfKmhS7smRbnit2tQTLGxe7YrZiV8wpe0rsyhaOnS92xZyyp8SumLDYFXPKnhK7soVj54tdMafsKbErW7i/54tdU9pt851cH7n5U4d8VOGdd99b3vWePYMrev9lFwy+u2sWfohds7Cl7q9R7OrefLVnFLvq2IPYFd+D2BW3ypoUu7JkW54rdrUEyxsXu2K2YlfMKXtK7MoWjp0vdsWcsqfErpiw2BVzyp4Su7KFY+eLXTGn7CmxK1u4v+eLXVPa7bX79g+e+dLt2wb/v/m+rl0fvKHsfPf5ZfMJLy9XXnNTefu5Z5UzTj9tSlcYf1qxK241T5NiVx3bFrvq2IPYFd+D2BW3ypoUu7JkW54rdrUEyxsXu2K2YlfMKXtK7MoWjp0vdsWcsqfErpiw2BVzyp4Su7KFY+eLXTGn7CmxK1u4v+eLXVPYbRO2Lr78urJj+7almNW8q+sTn76tXLXzgrJxw/qy8q+ncJnhpxS7wlRzNSh21bFusauOPYhd8T2IXXGrrEmxK0u25bliV0uwvHGxK2YrdsWcsqfErmzh2PliV8wpe0rsigmLXTGn7CmxK1s4dr7YFXPKnhK7soX7e77YNYXdLn8X16mnbB5cwcp3et33wMFyzUc+Xna/96JyzNGbpnCV8acUu+JW8zQpdtWxbbGrjj2IXfE9iF1xq6xJsStLtuW5YldLsLxxsStmK3bFnLKnxK5s4dj5YlfMKXtK7IoJi10xp+wpsStbOHa+2BVzyp4Su7KF+3u+2DWF3R4udr365BOXvqdL7Bp9MY8+Vsqv3LKufPWRdaMf4pFjC4hdYxNO5ACxayKMYx8idsUJxa64Vdak2JUl2/JcsaslWN642BWzFbtiTtlTYle2cOx8sSvmlD0ldsWExa6YU/aU2JUtHDtf7Io5ZU+JXdnC/T1f7JrCbp986ulDvpNr5V83l9R8jOHeffvL9Xsu8c6uljsSu1qCJY2LXUmwLY8Vu1qCJY2LXXFYsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu6a021sP3F7uuOuLg+/o+sK99z8vbK38WMMpXWboaX2MYYhp7obErjpWLnbVsQexK74HsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu6a42yZo3fixA4Mr+OiHLi9nnH7a4D837+p613v2HPL3pniZaz612LUm0VwOiF11rF3sqmMPYld8D2JX3CprUuzKkm15rtjVEixvXOyK2YpdMafsKbErWzh2vtgVc8qeErtiwmJXzCl7SuzKFo6dL3bFnLKnxK5s4f6eL3b1d7ed3ZnY1Rn1TD2R2FXHusSuOvYgdsX3IHbFrbImxa4s2Zbnil0twfLGxa6YrdgVc8qeEruyhWPni10xp+wpsSsmLHbFnLKnxK5s4dj5YlfMKXtK7MoW7u/5Yld/d9vZnYldnVHP1BOJXXWsS+yqYw9iV3wPYlfcKmtS7MqSbXmu2NUSLG9c7IrZil0xp+wpsStbOHa+2BVzyp4Su2LCYlfMKXtK7MoWjp0vdsWcsqfErmzh/p4vdvV3t53dmdjVGfVMPZHYVce6xK469iB2xfcgdsWtsibFrizZlueKXS3B8sbFrpit2BVzyp4Su7KFY+eLXTGn7CmxKyYsdsWcsqfErmzh2PliV8wpe0rsyhbu7/liV39329mdiV2dUc/UE4lddaxL7KpjD2JXfA9iV9wqa1LsypJtea7Y1RIsb1zsitmKXTGn7CmxK1s4dr7YFXPKnhK7YsJiV8wpe0rsyhaOnS92xZyyp8SubOH+ni929Xe3nd2Z2NUZ9Uw9kdhVx7rErjr2IHbF9yB2xa2yJsWuLNmW54pdLcHyxsWumK3YFXPKnhK7soVj54tdMafsKbErJix2xZyyp8SubOHY+WJXzCl7SuzKFu7v+WJXf3fb2Z2JXZ1Rz9QTiV11rEvsqmMPYld8D2JX3CprUuzKkm15rtjVEixvXOyK2YpdMafsKbErWzh2vtgVc8qeErtiwmJXzCl7SuzKFo6dL3bFnLKnxK5s4f6eL3b1d7ed3ZnY1Rn1TD2R2FXHusSuOvYgdsX3IHbFrbImxa4s2Zbnil0twfLGxa6YrdgVc8qeEruyhWPni10xp+wpsSsmLHbFnLKnxK5s4dj5YlfMKXtK7MoW7u/5Yld/d9vZnYldnVHP1BOJXXWsS+yqYw9iV3wPYlfcKmtS7MqSbXmu2NUSLG9c7IrZil0xp+wpsStbOHa+2BVzyp4Su2LCYlfMKXtK7MoWjp0vdsWcsqfErmzh/p4vdvV3t53dmdjVGfVMPZHYVce6xK469iB2xfcgdsWtsibFrizZlueKXS3B8sbFrpit2BVzyp4Su7KFY+eLXTGn7CmxKyYsdsWcsqfErmzh2PliV8wpe0rsyhbu7/liV39329mdiV2dUc/UE4lddaxL7KpjD2JXfA9iV9wqa1LsypJtea7Y1RIsb1zsitmKXTGn7CmxK1s4dr7YFXPKnhK7YsJiV8wpe0rsyhaOnS92xZyyp8SubOH+ni929Xe3I93Ztfv2l1effGI5b+uZhzz+1gO3lyuuvmnw9845e0u5aucFZeOG9YO/FrtGou79g8SuOlYsdtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbVne2PGa9/7ILDoldd959b9m7b3+5fs8l5ZijN5UmiDU/Lt2+bfD/xa5W1HMzLHbVsWqxq449iF3xPYhdcausSbErS7bluWJXS7C8cbErZit2xZyyp8SubOHY+WJXzCl7SuyKCYtdMaUyce0AACAASURBVKfsKbErWzh2vtgVc8qeEruyhft7vtjV392OdGervbNr5d9bGb/ErpGoe/8gsauOFYtddexB7IrvQeyKW2VNil1Zsi3PFbtaguWNi10xW7Er5pQ9JXZlC8fOF7tiTtlTYldMWOyKOWVPiV3ZwrHzxa6YU/aU2JUt3N/zxa7+7nakO1sZtp586uly5TU3lS1vev3Su73ue+Bged/uG8oHdl1UTj1ls3d2jSTd/weJXXXsWOyqYw9iV3wPYlfcKmtS7MqSbXmu2NUSLG9c7IrZil0xp+wpsStbOHa+2BVzyp4Su2LCYlfMKXtK7MoWjp0vdsWcsqfErmzh/p4vdvV3tyPd2eFi19vPPauccfppgzNXxq4nnvzWSM+V9aCHvvpc+T9/ebF89ZF1WU/h3ICA2BVA6mBkELv+wfnlF7/+hQ6ezVMcTqCJXZ945Y+WH37p5qqQFhcXy8LCQjXX9Oxzi+Xm/2uxfP4L9VxTNTgdXkgTu37+5E+UjZ/8pQ6f1VM9T+AFR5XfeP+Hyzse/3/hTFngF1/+d8pFL399WVhXz89Ntf38vbhYyu/+/nPl3/xaPUZTftlM7enf91N/VI65fkcpzVL8mJrA53buLue/8P7y4LPfmNo1eOJSzn3xa8rNJ59djqrq5+9SKvrl9+Bl8p//9Lly40cXyjN1/dbO3L2EL9z65fJ9n/wnZd0jD87dvdd0w2JXHdtoYte/etWPltdsfGkdF/Sdq9i08aiqrsfFPF9A7PKqOERglHd2PfGNZ6pSfOiRRbGrgo2IXRUsoZQidtWxh6XYtemkOi7oO1fR/DZYTb8tOYhdHyti15RfJWLXlBcwfHqxq5JFlDKIXce9rq7YVRbKQqknZgxi1x2LYlcFr1qxq4IllFI+9493l/PXi13T3sYgdr3qrXXFroWFslBZjB7ErpvXiV1TfsGKXVNewHeeXuyqYw9LsWvDpjouaBi7vuuFVV2PixG7vAbWEPCdXV4ikxIQuyYlOd45PsZwPL9JPdrHGMYlfYxh3Cpr0scYZsm2PNfHGLYEyxv3MYYxWx9jGHPKnvIxhtnCsfN9jGHMKXvKxxjGhH2MYcwpe8rHGGYLx84Xu2JO2VM+xjBbuL/ne2dXf3c70p2tFrvuvPvesnff/nL9nkvKMUdvKs1M8+PS7dsG///gI0+O9FxZD3r0sVJ+5ZZ1PsYwCzh4rtgVhEoeE7uSgYPHi11BqFKK2BW3ypoUu7JkW54rdrUEyxsXu2K2YlfMKXtK7MoWjp0vdsWcsqfErpiw2BVzyp4Su7KFY+eLXTGn7CmxK1u4v+eLXf3dbas7u/XA7eWKq29aesxJJxxX9l29o5x6yre/X2b5Pz/n7C3lqp0XlI0b1g/+mdjVinpuhsWuOlYtdtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbzu5M7OqMeqaeSOyqY11iVx17ELviexC74lZZk2JXlmzLc8WulmB542JXzFbsijllT4ld2cKx88WumFP2lNgVExa7Yk7ZU2JXtnDsfLEr5pQ9JXZlC/f3fLGrv7vt7M7Ers6oZ+qJxK461iV21bEHsSu+B7ErbpU1KXZlybY8V+xqCZY3LnbFbMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9vO7kzs6ox6pp5I7KpjXWJXHXsQu+J7ELviVlmTYleWbMtzxa6WYHnjYlfMVuyKOWVPiV3ZwrHzxa6YU/aU2BUTFrtiTtlTYle2cOx8sSvmlD0ldmUL9/d8sau/u+3szsSuzqhn6onErjrWJXbVsQexK74HsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu/q7287uTOzqjHqmnkjsqmNdYlcdexC74nsQu+JWWZNiV5Zsy3PFrpZgeeNiV8xW7Io5ZU+JXdnCsfPFrphT9pTYFRMWu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq7+77ezOxK7OqGfqicSuOtYldtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbzu5M7OqMeqaeSOyqY11iVx17ELviexC74lZZk2JXlmzLc8WulmB542JXzFbsijllT4ld2cKx88WumFP2lNgVExa7Yk7ZU2JXtnDsfLEr5pQ9JXZlC/f3fLGrv7vt7M7Ers6oZ+qJxK461iV21bEHsSu+B7ErbpU1KXZlybY8V+xqCZY3LnbFbMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9vO7kzs6ox6pp5I7KpjXWJXHXsQu+J7ELviVlmTYleWbMtzxa6WYHnjYlfMVuyKOWVPiV3ZwrHzxa6YU/aU2BUTFrtiTtlTYle2cOx8sSvmlD0ldmUL9/d8sau/u+3szsSuzqhn6onErjrWJXbVsQexK74HsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu/q7287uTOzqjHqmnkjsqmNdYlcdexC74nsQu+JWWZNiV5Zsy3PFrpZgeeNiV8xW7Io5ZU+JXdnCsfPFrphT9pTYFRMWu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq7+77ezOxK7OqGfqicSuOtYldtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbzu5M7OqMeqaeSOyqY11iVx17ELviexC74lZZk2JXlmzLc8WulmB542JXzFbsijllT4ld2cKx88WumFP2lNgVExa7Yk7ZU2JXtnDsfLEr5pQ9JXZlC/f3fLGrv7vt7M7Ers6oZ+qJxK461iV21bEHsSu+B7ErbpU1KXZlybY8V+xqCZY3LnbFbMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9vO7kzs6ox6pp5I7KpjXWJXHXsQu+J7ELviVlmTYleWbMtzxa6WYHnjYlfMVuyKOWVPiV3ZwrHzxa6YU/aU2BUTFrtiTtlTYle2cOx8sSvmlD0ldmUL9/d8sau/u+3szsSuzqhn6onErjrWJXbVsQexK74HsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu/q7287uTOzqjHqmnkjsqmNdYlcdexC74nsQu+JWWZNiV5Zsy3PFrpZgeeNiV8xW7Io5ZU+JXdnCsfPFrphT9pTYFRMWu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq7+77ezOxK7OqGfqicSuOtYldtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbzu5M7OqMeqaeSOyqY11iVx17ELviexC74lZZk2JXlmzLc8WulmB542JXzFbsijllT4ld2cKx88WumFP2lNgVExa7Yk7ZU2JXtnDsfLEr5pQ9JXZlC/f3fLGrv7vt7M7Ers6oZ+qJxK461iV21bEHsSu+B7ErbpU1KXZlybY8V+xqCZY3LnbFbMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9vO7kzs6ox6pp5I7KpjXWJXHXsQu+J7ELviVlmTYleWbMtzxa6WYHnjYlfMVuyKOWVPiV3ZwrHzxa6YU/aU2BUTFrtiTtlTYle2cOx8sSvmlD0ldmUL9/d8sau/u+3szsSuzqhn6onErjrWJXbVsQexK74HsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu/q7287uTOzqjHqmnkjsqmNdYlcdexC74nsQu+JWWZNiV5Zsy3PFrpZgeeNiV8xW7Io5ZU+JXdnCsfPFrphT9pTYFRMWu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq7+77ezOxK7OqGfqicSuOtYldtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbzu5M7OqMeqaeSOyqY11iVx17ELviexC74lZZk2JXlmzLc8WulmB542JXzFbsijllT4ld2cKx88WumFP2lNgVExa7Yk7ZU2JXtnDsfLEr5pQ9JXZlC/f3fLGrv7vt7M7Ers6oZ+qJxK461iV21bEHsSu+B7ErbpU1KXZlybY8V+xqCZY3LnbFbMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9vO7kzs6ox6pp5I7KpjXWJXHXsQu+J7ELviVlmTYleWbMtzxa6WYHnjYlfMVuyKOWVPiV3ZwrHzxa6YU/aU2BUTFrtiTtlTYle2cOx8sSvmlD0ldmUL9/d8sau/u+3szsSuzqhn6onErjrWJXbVsQexK74HsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu/q7287uTOzqjHqmnkjsqmNdYlcdexC74nsQu+JWWZNiV5Zsy3PFrpZgeeNiV8xW7Io5ZU+JXdnCsfPFrphT9pTYFRMWu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq7+77ezOxK7OqGfqicSuOtYldtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbzu5M7OqMeqaeSOyqY11iVx17ELviexC74lZZk2JXlmzLc8WulmB542JXzFbsijllT4ld2cKx88WumFP2lNgVExa7Yk7ZU2JXtnDsfLEr5pQ9JXZlC/f3fLGrv7vt7M7Ers6oZ+qJxK461iV21bEHsSu+B7ErbpU1KXZlybY8V+xqCZY3LnbFbMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9vO7kzs6ox6pp5I7KpjXWJXHXsQu+J7ELviVlmTYleWbMtzxa6WYHnjYlfMVuyKOWVPiV3ZwrHzxa6YU/aU2BUTFrtiTtlTYle2cOx8sSvmlD0ldmUL9/d8sau/u+3szsSuzqhn6onErjrWJXbVsQexK74HsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu/q7287uTOzqjHqmnkjsqmNdYlcdexC74nsQu+JWWZNiV5Zsy3PFrpZgeeNiV8xW7Io5ZU+JXdnCsfPFrphT9pTYFRMWu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq7+77ezOxK7OqGfqicSuOtYldtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbzu5M7OqMeqaeSOyqY11iVx17ELviexC74lZZk2JXlmzLc8WulmB542JXzFbsijllT4ld2cKx88WumFP2lNgVExa7Yk7ZU2JXtnDsfLEr5pQ9JXZlC/f3fLGrv7vt7M7Ers6oZ+qJxK461iV21bEHsSu+B7ErbpU1KXZlybY8V+xqCZY3LnbFbMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9vO7kzs6ox6pp5I7KpjXWJXHXsQu+J7ELviVlmTYleWbMtzxa6WYHnjYlfMVuyKOWVPiV3ZwrHzxa6YU/aU2BUTFrtiTtlTYle2cOx8sSvmlD0ldmUL9/d8sau/u+3szsSuzqhn6onErjrWJXbVsQexK74HsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu/q7287uTOzqjHqmnkjsqmNdYlcdexC74nsQu+JWWZNiV5Zsy3PFrpZgeeNiV8xW7Io5ZU+JXdnCsfPFrphT9pTYFRMWu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq7+77ezOxK7OqGfqicSuOtYldtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbzu5M7OqMeqaeSOyqY11iVx17ELviexC74lZZk2JXlmzLc8WulmB542JXzFbsijllT4ld2cKx88WumFP2lNgVExa7Yk7ZU2JXtnDsfLEr5pQ9JXZlC/f3fLGrv7vt7M7Ers6oZ+qJxK461iV21bEHsSu+B7ErbpU1KXZlybY8V+xqCZY3LnbFbMWumFP2lNiVLRw7X+yKOWVPiV0xYbEr5pQ9JXZlC8fOF7tiTtlTYle2cH/PF7v6u9vO7kzs6ox6pp5I7KpjXWJXHXsQu+J7ELviVlmTYleWbMtzxa6WYHnjYlfMVuyKOWVPiV3ZwrHzxa6YU/aU2BUTFrtiTtlTYle2cOx8sSvmlD0ldmUL9/d8sau/u+3szsSuzqhn6onErjrWJXbVsQexK74HsStulTUpdmXJtjxX7GoJljcudsVsxa6YU/aU2JUtHDtf7Io5ZU+JXTFhsSvmlD0ldmULx84Xu2JO2VNiV7Zwf88Xu/q7287uTOzqjHqmnkjsqmNdYlcdexC74nsQu+JWWZNiV5Zsy3PFrpZgeeNiV8xW7Io5ZU+JXdnCsfPFrphT9pTYFRMWu2JO2VNiV7Zw7HyxK+aUPSV2ZQv393yxq7+77ezOxK7OqGfqicSuOtYldtWxB7ErvgexK26VNSl2Zcm2PFfsagmWNy52xWzFrphT9pTYlS0cO1/sijllT4ldMWGxK+aUPSV2ZQvHzhe7Yk7ZU2JXtnB/zxe7+rvbid7ZrQduL1dcfdPgzHPO3lKu2nlB2bhh/eCvxa6JUvfmMLGrjlWKXXXsQeyK70HsiltlTYpdWbItzxW7WoLljYtdMVuxK+aUPSV2ZQvHzhe7Yk7ZU2JXTFjsijllT4ld2cKx88WumFP2lNiVLdzf88Wu/u52Ynd25933lr379pfr91xSjjl6U7l23/7B2Zdu3zb4/2LXxKh7dZDYVcc6xa469iB2xfcgdsWtsibFrizZlueKXS3B8sbFrpit2BVzyp4Su7KFY+eLXTGn7CmxKyYsdsWcsqfErmzh2PliV8wpe0rsyhbu7/liV393O7E7a+LWq08+sZy39czBmSvjl9g1MepeHSR21bFOsauOPYhd8T2IXXGrrEmxK0u25bliV0uwvHGxK2YrdsWcsqfErmzh2PliV8wpe0rsigmLXTGn7CmxK1s4dr7YFXPKnhK7soX7e77Y1d/dTuTOnnzq6XLlNTeVLW96/VLsuu+Bg+V9u28oH9h1UTn1lM3e2TUR6f4dInbVsVOxq449iF3xPYhdcausSbErS7bluWJXS7C8cbErZit2xZyyp8SubOHY+WJXzCl7SuyKCYtdMafsKbErWzh2vtgVc8qeEruyhft7vtjV391O5M6Gsevt555Vzjj9tMGZK2PXRJ5ogof8xUPfKp/8v58tj399YYKnOqqtwOtOWyw//BfXl4UH/rjtQ81PUOC5k7+nfPRtP1Ju/cafT/BUR7UVWL+wrlzxijPK2S/f3PahczX/zLeeK/t/7Vvlzx+Yq9uu7maPO26x/MSx/6686I4D1V3bXF3QC15QfveC/7n807+6Z65uu8ab/R+Pfl1510nfV9at82vLw+3nucVSfueOZ8rv3bFY4wrn6pp+bssXy0sO3FDKol1Mc/H3bLuw7Fn/cHnkuaemeRlz/9xvedGJ5Z+e8pay/qh1c29xJIDP3/NM+cy/WyzPPotpmgLn/K2D5eTfv7EsfP2RaV7G3D/3U2e8tfwvr395ufeZR+feYpoArzrqxeV/f+WW8r0vfuk0L8Nzz6CA2DWDS+vykiPv7OryejwXAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQGC5gNjl9bCmwFrf2bXmAQYIECBAgAABAgQIECBAgAABAgQIECBAgAABAkkCYlcSbJ+OvfPue8veffvL9XsuKcccvak08av5cen2bX26TfdCgAABAgQIECBAgAABAgQIECBAgAABAgQIzKCA2DWDS5vGJd964PZyxdU3DZ76nLO3lKt2XlA2blg/jUvxnAQ6FXj08SfKxZdfV3Zs37b0vXWdXoAnI0CAAAECBAgQIECAAAECBAj0ROC+Bw6W9+2+oXxg10Xl1FN8r3VP1uo2CFQhIHZVsQYXQYDAqALD75X7zGfvWDriwndsndg7D8WuUTfjcQQIEMgXaP6H8vbL9pYHH/pvX+Q9/O+A4X8/fOngw0vvTm+uaPjzevOfh+9az79Sz0CAAIF+Cgx/Tv38Pfcv3aA/HNnPXbsrAgTmQ6D5dKd3vWfPITd70gnHlX1X75hYmBK75uO15C4JTENA7JqGuuckQGBiAsPfzNzypteX87aeWYZ/feLxx04keIldE1uVgwgQIDBRgeZd5x+5+VPP+x/ezcct/9Bb3ljecNpry5XX3FQef+KvytvOOmPw3xHNj+Zx//a2Owd/X+ya6EocRoDAnAkMf0P0ox+6/JBPQGh+nm1+DH/enTMWt0uAAIGZFlj5VSYZNyN2Zag6kwCBRkDs8jogQGCmBVbGruZmml+cfeLTtw0+bvP6mz9Z/vIbT5W//MtvlObdX++/7ILB//A+0kdzrvZOgeH/iF/5nXXLn6v5aM+Vf7p1+Hwr34E2/Pszje/iCRAgMCWByB9EGP68e/YP/UD57O/+Ydn1Cz87uNrdH76lNH/vl//lb4hdU9qfpyVAYPYFVvs1+Gp31fzaue2vxdv+eno4/+N/92+Xj+7/jcFlvO89P1f23fypQz4iq4vfwJ39zboDAgTmXeBIP1ce7uf05e8G+/7XvXbp19grfz4fvvP34ENfHXyM4Y//vb9Tdv+zWwbkk/yEnnnfofsnMM8CYtc8b9+9E+iBwGr/Q7sJWXfc9cWl2HXgt/7gkD/5v/IXb80v2L7y8NcG809985uD7+jadu5Zgyi28jdUjxS7Vj62ubbb77i7nLnl9MG7C4bvNmvO3PXBG8rOd58/sY8B6MEq3QIBAgTCApHfsBz+98Pbzz2rfPngw4OzT958/OA/N/9/7779YldY3CABAgQOFYj+qfzm187j/Fo88uvpY1+2afDr9+/efPwh3y3dPPerTz7xkE9/GH4ahH0SIECAwOoCa8Wu1X5O37X7hqXfc2l+P+bPv/yVwSftLP95uHm2W279d2Xr2VvK1x57YvBR5Fvf+pbBXOQPstkXAQIEIgJiV0TJDAEC1QqsjF3DXyQNY9XKONXcyMq/t/x/rH/t0a8f8hugbWLXF+69f9XfPF3tNwNW/qKvWmAXRoAAgQoFVr6rduU7cpt34w4/xrCJXd/zmlcO/pDBS1/yXYN3eP3pn/0XsavCvbokAgRmR6D5efeaj3y87H7vReWYozcd9tMNxv21+FDkSL+e/pEf/JuD2LVj+7ZDPk5x+TU2v7G6/HpnR9qVEiBAoFuB1b6za/jJNNGf05ufb//XS99ZPvTPP7H0h36X38XKn9OX/yG1M04/rdsb9mwECPRKQOzq1TrdDIH5E1j58YCNwPKPCDzcL8aGf8qzmV/+Tqsmdg0/AnH5xxIO/8fzkd7Z1cSu5Y9d/j/Omz+19OBDjxyyIB9lOH+vV3dMgMBkBA73J06X/wGF5bGr+R/Ny/+QQeSdYZO5UqcQIECgnwKHe2fXyj+INu6vxSO/nj5c7Fr5Dt/hOw36uRF3RYAAgckIrPXOruZZmndjDX80P8/f+LEDhzz58KMMm7/Z/GGEz99z/+CfD38PROyazK6cQoDA8wXELq8KAgRmWmCt7wuI/smj5vOiP7DropL1zi5/knSmX2YungCBygQO91EnR4pdy29B7KpsoS6HAIGZEzjcr8GjsWv5b5Ye6VMWhjAr30m2HOxIH381+Pn+l/7lYLz5tf6pp2yeOWsXTIAAgS4FRoldy/8w8eGudfnP9c3M8Pdgmp+XvbOryw17LgL9FhC7+r1fd0eg9wKjxK7mF2/LP1N6te/sGr6Ta/gW/uYjsZp3Biz/PrAGt/kurubH4b7v68Bn7yjnbf3hQ76zq5lvfqH3p3/2F+VtZ7259ztygwQIEMgQaH4+/sjNnzrkOxnFrgxpZxIgQGB1gZW/Tm6mIrEr8mvx5d+fu9avp9/8N1+36scYNtcz/O+Fld/nZacECBAgcPif2w/33bar/WHilT+nN6c23831Iz/4A+W3/v0flp8+54dL86k5YpdXHAECXQiIXV0oew4CBNIERoldzcU0v0l6xdXfDlXnnL3lkC+zXv4Z1c0/+9LBh5e+A2D5xyaedMJx5V3bmN7f+gAADFdJREFUfrR87ov3LT1+5ffGDN+mv/LjFpvH7rt6hz9dmvbKcDABAvMgsPLn3Oaeh3844Uh/QtQ7u+bh1eEeCRDoQmAYk4YfUdU851ofKb7Wr8Xb/nr62JdtOmzs8m6BLl4FnoMAgT4JtH1nV3PvK7/n68J3bB181OHKjzgc/jrdxxj26RXjXgjUJSB21bUPV0OAAAECBAgQIECAAAECBAhMQKD5DdVbbv3NsvPi8wfvLPCDAAECBAgQIECgvwJiV393684IECBAgAABAgQIECBAgMDcCjTvKoh8l8zcArlxAgQIECBAgECPBMSuHi3TrRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIE5k1A7Jq3jbtfAgQIECBAgAABAgQIECBAgAABAgQIECBAgECPBMSuHi3TrRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIE5k1A7Jq3jbtfAgQIECBAgAABAgQIECBAgAABAgQIECBAgECPBMSuHi3TrRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIE5k1A7Jq3jbtfAgQIECBAgAABAgQIECBAgAABAgQIECBAgECPBMSuHi3TrRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIE5k1A7Jq3jbtfAgQIECBAgAABAgQIECBAgAABAgQIECBAgECPBMSuHi3TrRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIE5k1A7Jq3jbtfAgQIECBAgAABAgQIECBAgAABAgQIECBAgECPBMSuHi3TrRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIE5k1A7Jq3jbtfAgQIECBAgAABAgQIECBAgAABAgQIECBAgECPBMSuHi3TrRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIE5k1A7Jq3jbtfAgQIECBAgAABAgQIECBAgAABAgQIECBAgECPBMSuHi3TrRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIE5k1A7Jq3jbtfAgQIECBAgAABAgQIECBAgAABAgQIECBAgECPBMSuHi3TrRAgQIAAAQIECBAgMD2Ba/ftLzd+7MAhF3DSCceVfVfvKKeesnnp7z/6+BPl4suvK9vOPauct/XMNS+4zfyTTz1drrzmpnLi8ceWS7dvW/NsAwQIECBAgAABAgQIEOiDgNjVhy26BwIECBAgQIAAAQIEpi7QxK6vPPy1ctXOC8rGDesH13Pn3feWd71nT7nwHVvFp6lvyAUQIECAAAECBAgQINBXAbGrr5t1XwQIECBAgAABAgQIdCqwWuxqLuC+Bw6W7ZftLe9+508M3sk1fKfWju3byhtOe+3gnVhb3vT6Q97l1TzmfbtvKB/YdVE59mWbBu8Ea+bPOP20QyLa8Aa//3WvLdfvuaRseNGLnnfe8N1en/nsHYPx4ewxR28a/PWtB24vd9z1xfLjf+9vD56n+bHaO9I6xfRkBAgQIECAAAECBAgQaCEgdrXAMkqAAAECBAgQIECAAIHDCRwudjXzy//ZU9/85iHxahiblr8jbPnfWzm/PIQNPx6xmT958/HPi2erfaxhM7v/07cN4lgTvJq/vuLqmw5599mR7sUrgAABAgQIECBAgAABArUJiF21bcT1ECBAgAABAgQIECAwkwJHCkTLA1Nzc8vfqTV859fuXRcN3rk1DFRvP/eswV8vfydY89fNRyPu3bd/KVYtxxo+dvhOsdXC2MrzVottR3qOmVyOiyZAgAABAgQIECBAoNcCYlev1+vmCBAgQIAAAQIECBDoSmDU2NVcX/PY5sel27c9L2atjFPDv/78PfcPHvPRD12+9PGGK2NXE60+8enbDvkeseHzvfrkEwcfnSh2dfUK8TwECBAgQIAAAQIECGQJiF1Zss4lQIAAAQIECBAgQGCuBEb9GMMGafk7qX7547++FL6a/7Aydg1RV4teK78DTOyaq5egmyVAgAABAgQIECAwtwJi19yu3o0TIECAAAECBAgQIDBJgcPFrpUfU7havBr+vX/4P/xo+eV/+Rtlx/ZtS+/WOlzsGl778ndz/dhbt5Qrr7mp+BjDSW7WWQQIECBAgAABAgQI1C4gdtW+IddHgAABAgQIECBAgMBMCKwWu5p3Vr3rPXvKhe/YOviIwubH4eJV83GCV1x9Uznn7C2HfOzgat+x1ZzTfAThyvNWvrNrGMJOPP7Ypedf/v1hxxy9yccYzsSry0USIECAAAECBAgQIHAkAbHL64MAAQIECBAgQIAAAQITEGhi140fO3DISSedcFzZd/WOcuopm5f+/uFi1/AdYO9+508shazV4thw7sGHHlk68/2XXTB4zMrv7GoGhn/vM5+9YzD//a97bbl+zyWlCV3ND9/ZNYHlO4IAAQIECBAgQIAAgakKiF1T5ffkBAgQIECAAAECBAgQIECAAAECBAgQIECAAAEC4wiIXePoeSwBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMBUBcSuqfJ7cgIECBAgQIAAAQIECBAgQIAAAQIECBAgQIAAgXEExK5x9DyWAAECBAgQIECAAAECBAgQIECAAAECBAgQIEBgqgJi11T5PTkBAgQIECBAgAABAgQIECBAgAABAgQIECBAgMA4AmLXOHoeS4AAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgMFUBsWuq/J6cAAECBAgQIECAAAECBAgQIECAAAECBAgQIEBgHAGxaxw9jyVAgAABAgQIECBAgAABAgQIECBAgAABAgQIEJiqgNg1VX5PToAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgMI6A2DWOnscSIECAAAECBAgQIECAAAECBAgQIECAAAECBAhMVUDsmiq/JydAgAABAgQIECBAgAABAgQIECBAgAABAgQIEBhHQOwaR89jCRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIEpiogdk2V35MTIECAAAECBAgQIECAAAECBAgQIECAAAECBAiMIyB2jaPnsQQIECBAgAABAgQIECBAgAABAgQIECBAgAABAlMVELumyu/JCRAgQIAAAQIECBAgQIAAAQIECBAgQIAAAQIExhEQu8bR81gCBAgQIECAAAECBAgQIECAAAECBAgQIECAAIGpCohdU+X35AQIECBAgAABAgQIECBAgAABAgQIECBAgAABAuMIiF3j6HksAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDAVAXErqnye3ICBAgQIECAAAECBAgQIECAAAECBAgQIECAAIFxBMSucfQ8lgABAgQIECBAgAABAgQIECBAgAABAgQIECBAYKoCYtdU+T05AQIECBAgQIAAAQIECBAgQIAAAQL/f3t2TAMAAIAwzL9rROzgqQBC0ncECBAgQIBAERC7ip4tAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQIDAVUDsuvI7J0CAAAECBAgQIECAAAECBAgQIECAAAECBAgQKAJiV9GzJUCAAAECBAgQIECAAAECBAgQIECAAAECBAgQuAqIXVd+5wQIECBAgAABAgQIECBAgAABAgQIECBAgAABAkVA7Cp6tgQIECBAgAABAgQIECBAgAABAgQIECBAgAABAlcBsevK75wAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQKAIiF1Fz5YAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgQOAqIHZd+Z0TIECAAAECBAgQIECAAAECBAgQIECAAAECBAgUAbGr6NkSIECAAAECBAgQIECAAAECBAgQIECAAAECBAhcBcSuK79zAgQIECBAgAABAgQIECBAgAABAgQIECBAgACBIiB2FT1bAgQIECBAgAABAgQIECBAgAABAgQIECBAgACBq4DYdeV3ToAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgUATErqJnS4AAAQIECBAgQIAAAQIECBAgQIAAAQIECBAgcBUQu678zgkQIECAAAECBAgQIECAAAECBAgQIECAAAECBIqA2FX0bAkQIECAAAECBAgQIECAAAECBAgQIECAAAECBK4CYteV3zkBAgQIECBAgAABAgQIECBAgAABAgQIECBAgEARELuKni0BAgQIECBAgAABAgQIECBAgAABAgQIECBAgMBVQOy68jsnQIAAAQIECBAgQIAAAQIECBAgQIAAAQIECBAoAgNGmufk0ZQBbQAAAABJRU5ErkJggg==", "text/html": ["<div>                            <div id=\"a39b9140-0e52-40c0-99fa-89e0322cdfaa\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"a39b9140-0e52-40c0-99fa-89e0322cdfaa\")) {                    Plotly.newPlot(                        \"a39b9140-0e52-40c0-99fa-89e0322cdfaa\",                        [{\"name\":\"SK\",\"x\":[\"Produce\",\"GM\",\"Grocery\",\"Fresh\"],\"y\":[16,17,26,24],\"type\":\"bar\"},{\"name\":\"HU\",\"x\":[\"Fresh\",\"GM\",\"Produce\",\"Grocery\"],\"y\":[26,15,15,30],\"type\":\"bar\"},{\"name\":\"C<PERSON>\",\"x\":[\"GM\",\"Produce\",\"Fresh\",\"Grocery\"],\"y\":[16,17,27,28],\"type\":\"bar\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"updatemenus\":[{\"buttons\":[{\"args\":[{\"visible\":[true,true,true]}],\"label\":\"All\",\"method\":\"update\"},{\"args\":[{\"visible\":[true,false,false]}],\"label\":\"SK\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,true,false]}],\"label\":\"HU\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,false,true]}],\"label\":\"CZ\",\"method\":\"update\"}]}],\"title\":{\"text\":\"Sales\"},\"xaxis\":{\"title\":{\"text\":\"Division\"}},\"yaxis\":{\"title\":{\"text\":\"Sales\"}}},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('a39b9140-0e52-40c0-99fa-89e0322cdfaa');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "countries = a['country'].unique().tolist()\n", "\n", "plotly_figs = []\n", "# Create a line chart with dropdown\n", "fig = go.Figure()\n", "\n", "for country in countries:\n", "    fig.add_trace(\n", "        go.Bar(x=a[a['country'] == country]['division'],\n", "                   y=a[a['country'] == country]['sales'],\n", "                   name=country)\n", "    )\n", "# Create dropdown buttons\n", "dropdown_buttons = [\n", "    dict(label='All', method='update', args=[{'visible': [True for _ in countries]}]),\n", "    *[dict(label=country, method='update', args=[{'visible': [country==c for c in countries]}]) for country in countries]\n", "]\n", "# Add dropdown menu to layout\n", "fig.update_layout(\n", "    updatemenus=[\n", "        go.layout.Updatemenu(buttons=dropdown_buttons)\n", "    ],\n", "    title='Sales',\n", "    xaxis_title='Division',\n", "    yaxis_title='Sales'\n", ")\n", "fig.show()\n", "plotly_figs.append(fig)"]}, {"cell_type": "code", "execution_count": 24, "id": "09977e9a-ebfe-490e-aa2f-9b27969ea14d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr > th,\n", ".dataframe > tbody > tr > td {\n", "  text-align: right;\n", "}\n", "</style>\n", "<small>shape: (5, 50)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>store</th><th>pmg</th><th>tpnb</th><th>day</th><th>sold_units</th><th>sales_excl_vat</th><th>country</th><th>tpn</th><th>ownbrand</th><th>unit_type</th><th>weight</th><th>division_hier</th><th>DIV_ID</th><th>department</th><th>DEP_ID</th><th>section</th><th>SEC_ID</th><th>group</th><th>GRP_ID</th><th>subgroup</th><th>SGR_ID</th><th>product_name</th><th>format</th><th>division</th><th>dep</th><th>is_capping_shelf</th><th>sold_units_dotcom</th><th>stock</th><th>item_price</th><th>srp</th><th>nsrp</th><th>mu</th><th>full_pallet</th><th>split_pallet</th><th>icream_nsrp</th><th>checkout_stand_flag</th><th>clipstrip_flag</th><th>backroom_flag</th><th>shelfCapacity</th><th>case_capacity</th><th>unit</th><th>opening_type</th><th>pallet_capacity</th><th>foil</th><th>SRP opening reduction opportunity</th><th>extra disassemble %</th><th>single_pick</th><th>cases_delivered</th><th>as_is_model_contains?</th><th>item_price_new</th></tr><tr><td>i32</td><td>cat</td><td>i32</td><td>cat</td><td>f32</td><td>f32</td><td>cat</td><td>i64</td><td>cat</td><td>cat</td><td>f32</td><td>cat</td><td>i16</td><td>cat</td><td>i16</td><td>cat</td><td>i16</td><td>cat</td><td>i16</td><td>cat</td><td>i16</td><td>cat</td><td>cat</td><td>cat</td><td>cat</td><td>i8</td><td>f64</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>f32</td><td>i8</td><td>i8</td><td>i8</td><td>f32</td><td>f32</td><td>f32</td><td>cat</td><td>f32</td><td>f32</td><td>i8</td><td>f32</td><td>i8</td><td>f32</td><td>cat</td><td>f32</td></tr></thead><tbody><tr><td>41005</td><td>&quot;HDL10&quot;</td><td>100761028</td><td>&quot;Sunday&quot;</td><td>11.5</td><td>0.0</td><td>&quot;HU&quot;</td><td>2005100761028</td><td>&quot;N&quot;</td><td>&quot;SNGL&quot;</td><td>0.07</td><td>&quot;Centralized Ha…</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Boys&quot;</td><td>35</td><td>&quot;Collectables&quot;</td><td>25</td><td>&quot;Bakugan Legend…</td><td>&quot;Hypermarket&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>11.5</td><td>4699.0</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>7.0</td><td>8.0</td><td>0.0</td><td>&quot;no_data&quot;</td><td>48.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.0</td><td>&quot;Y&quot;</td><td>4508.365234</td></tr><tr><td>41005</td><td>&quot;HDL10&quot;</td><td>100761028</td><td>&quot;Wednesday&quot;</td><td>12.285714</td><td>309.392151</td><td>&quot;HU&quot;</td><td>2005100761028</td><td>&quot;N&quot;</td><td>&quot;SNGL&quot;</td><td>0.07</td><td>&quot;Centralized Ha…</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Boys&quot;</td><td>35</td><td>&quot;Collectables&quot;</td><td>25</td><td>&quot;Bakugan Legend…</td><td>&quot;Hypermarket&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>12.285714</td><td>4699.0</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>7.0</td><td>8.0</td><td>0.0</td><td>&quot;no_data&quot;</td><td>48.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.0</td><td>&quot;Y&quot;</td><td>4508.365234</td></tr><tr><td>41005</td><td>&quot;HDL10&quot;</td><td>100761028</td><td>&quot;Saturday&quot;</td><td>11.5</td><td>1388.864258</td><td>&quot;HU&quot;</td><td>2005100761028</td><td>&quot;N&quot;</td><td>&quot;SNGL&quot;</td><td>0.07</td><td>&quot;Centralized Ha…</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Boys&quot;</td><td>35</td><td>&quot;Collectables&quot;</td><td>25</td><td>&quot;Bakugan Legend…</td><td>&quot;Hypermarket&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>11.5</td><td>4699.0</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>7.0</td><td>8.0</td><td>0.0</td><td>&quot;no_data&quot;</td><td>48.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.0</td><td>&quot;Y&quot;</td><td>4508.365234</td></tr><tr><td>41005</td><td>&quot;HDL10&quot;</td><td>100761028</td><td>&quot;Thursday&quot;</td><td>11.928571</td><td>1180.821411</td><td>&quot;HU&quot;</td><td>2005100761028</td><td>&quot;N&quot;</td><td>&quot;SNGL&quot;</td><td>0.07</td><td>&quot;Centralized Ha…</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Boys&quot;</td><td>35</td><td>&quot;Collectables&quot;</td><td>25</td><td>&quot;Bakugan Legend…</td><td>&quot;Hypermarket&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>11.928571</td><td>4699.0</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>7.0</td><td>8.0</td><td>0.0</td><td>&quot;no_data&quot;</td><td>48.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.0</td><td>&quot;Y&quot;</td><td>4508.365234</td></tr><tr><td>41005</td><td>&quot;HDL10&quot;</td><td>100761028</td><td>&quot;Friday&quot;</td><td>11.928571</td><td>0.0</td><td>&quot;HU&quot;</td><td>2005100761028</td><td>&quot;N&quot;</td><td>&quot;SNGL&quot;</td><td>0.07</td><td>&quot;Centralized Ha…</td><td>35</td><td>&quot;CH Home&quot;</td><td>195</td><td>&quot;CH Toys &amp;nurse…</td><td>771</td><td>&quot;Boys&quot;</td><td>35</td><td>&quot;Collectables&quot;</td><td>25</td><td>&quot;Bakugan Legend…</td><td>&quot;Hypermarket&quot;</td><td>&quot;GM&quot;</td><td>&quot;HDL&quot;</td><td>1</td><td>0.0</td><td>11.928571</td><td>4699.0</td><td>0.0</td><td>1.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0.0</td><td>0</td><td>0</td><td>0</td><td>7.0</td><td>8.0</td><td>0.0</td><td>&quot;no_data&quot;</td><td>48.0</td><td>0.0</td><td>0</td><td>0.0</td><td>0</td><td>0.0</td><td>&quot;Y&quot;</td><td>4508.365234</td></tr></tbody></table></div>"], "text/plain": ["shape: (5, 50)\n", "┌───────┬───────┬───────────┬───────────┬───┬─────────────┬─────────────────┬───────────────────────┬────────────────┐\n", "│ store ┆ pmg   ┆ tpnb      ┆ day       ┆ … ┆ single_pick ┆ cases_delivered ┆ as_is_model_contains? ┆ item_price_new │\n", "│ ---   ┆ ---   ┆ ---       ┆ ---       ┆   ┆ ---         ┆ ---             ┆ ---                   ┆ ---            │\n", "│ i32   ┆ cat   ┆ i32       ┆ cat       ┆   ┆ i8          ┆ f32             ┆ cat                   ┆ f32            │\n", "╞═══════╪═══════╪═══════════╪═══════════╪═══╪═════════════╪═════════════════╪═══════════════════════╪════════════════╡\n", "│ 41005 ┆ HDL10 ┆ 100761028 ┆ Sunday    ┆ … ┆ 0           ┆ 0.0             ┆ Y                     ┆ 4508.365234    │\n", "│ 41005 ┆ HDL10 ┆ 100761028 ┆ Wednesday ┆ … ┆ 0           ┆ 0.0             ┆ Y                     ┆ 4508.365234    │\n", "│ 41005 ┆ HDL10 ┆ 100761028 ┆ Saturday  ┆ … ┆ 0           ┆ 0.0             ┆ Y                     ┆ 4508.365234    │\n", "│ 41005 ┆ HDL10 ┆ 100761028 ┆ Thursday  ┆ … ┆ 0           ┆ 0.0             ┆ Y                     ┆ 4508.365234    │\n", "│ 41005 ┆ HDL10 ┆ 100761028 ┆ Friday    ┆ … ┆ 0           ┆ 0.0             ┆ Y                     ┆ 4508.365234    │\n", "└───────┴───────┴───────────┴───────────┴───┴─────────────┴─────────────────┴───────────────────────┴────────────────┘"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["Repl_Dataset.with_columns(pl.when(pl.col('tpnb')==100761028).then(pl.col('item_price').mean().over(['country', 'tpnb'])).otherwise(0).alias(\"item_price_new\").cast(pl.Float32)).filter((pl.col('store')==41005)&(pl.col('tpnb')==100761028)).head()#.filter(pl.when(pl.col('tpnb')==100761028)).head()"]}, {"cell_type": "code", "execution_count": 35, "id": "45d00d2b-6385-46d8-90a2-820d53e883ce", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"name": "tan", "showlegend": true, "type": "scatter", "visible": true, "x": [-3.141592653589793, -2.443460952792061, -1.7453292519943295, -1.0471975511965979, -0.34906585039886595, 0.34906585039886595, 1.0471975511965974, 1.7453292519943293, 2.443460952792061, 3.141592653589793], "y": [1.2246467991473532e-16, 0.8390996311772804, 5.671281819617711, -1.7320508075688776, -0.3639702342662024, 0.3639702342662024, 1.7320508075688759, -5.671281819617718, -0.8390996311772804, -1.2246467991473532e-16]}, {"name": "tan - 1", "showlegend": true, "type": "scatter", "visible": true, "x": [-3.141592653589793, -2.443460952792061, -1.7453292519943295, -1.0471975511965979, -0.34906585039886595, 0.34906585039886595, 1.0471975511965974, 1.7453292519943293, 2.443460952792061, 3.141592653589793], "y": [-0.9999999999999999, -0.16090036882271963, 4.671281819617711, -2.7320508075688776, -1.3639702342662023, -0.6360297657337977, 0.7320508075688759, -6.671281819617718, -1.8390996311772803, -1.0000000000000002]}, {"name": "tan", "showlegend": true, "type": "scatter", "visible": false, "x": [-3.141592653589793, -2.443460952792061, -1.7453292519943295, -1.0471975511965979, -0.34906585039886595, 0.34906585039886595, 1.0471975511965974, 1.7453292519943293, 2.443460952792061, 3.141592653589793], "y": [1.2246467991473532e-16, 0.8390996311772804, 5.671281819617711, -1.7320508075688776, -0.3639702342662024, 0.3639702342662024, 1.7320508075688759, -5.671281819617718, -0.8390996311772804, -1.2246467991473532e-16]}, {"name": "tan - 1", "showlegend": true, "type": "scatter", "visible": false, "x": [-3.141592653589793, -2.443460952792061, -1.7453292519943295, -1.0471975511965979, -0.34906585039886595, 0.34906585039886595, 1.0471975511965974, 1.7453292519943293, 2.443460952792061, 3.141592653589793], "y": [-0.9999999999999999, -0.16090036882271963, 4.671281819617711, -2.7320508075688776, -1.3639702342662023, -0.6360297657337977, 0.7320508075688759, -6.671281819617718, -1.8390996311772803, -1.0000000000000002]}], "layout": {"autosize": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Tangent"}, "updatemenus": [{"active": -1, "buttons": [{"args": [{"name": ["sin", "sin - 1"], "visible": true, "y": [[-1.2246467991473532e-16, -0.6427876096865395, -0.984807753012208, -0.8660254037844387, -0.34202014332566877, 0.34202014332566877, 0.8660254037844385, 0.9848077530122081, 0.6427876096865395, 1.2246467991473532e-16], [-1.0000000000000002, -1.6427876096865395, -1.9848077530122081, -1.8660254037844388, -1.3420201433256689, -0.6579798566743312, -0.13397459621556151, -0.015192246987791869, -0.3572123903134605, -0.9999999999999999]]}, {"title": "<PERSON><PERSON>"}, [0, 1]], "args2": [{"name": ["tan", "tan - 1"], "visible": true, "y": [[1.2246467991473532e-16, 0.8390996311772804, 5.671281819617711, -1.7320508075688776, -0.3639702342662024, 0.3639702342662024, 1.7320508075688759, -5.671281819617718, -0.8390996311772804, -1.2246467991473532e-16], [-0.9999999999999999, -0.16090036882271963, 4.671281819617711, -2.7320508075688776, -1.3639702342662023, -0.6360297657337977, 0.7320508075688759, -6.671281819617718, -1.8390996311772803, -1.0000000000000002]]}, {"title": "Tangent"}, [0, 1]], "label": "Toggle Sine / <PERSON>ent", "method": "update"}], "direction": "down", "showactive": true, "type": "buttons"}], "xaxis": {"autorange": true, "range": [-3.512179209931869, 3.512179209931869], "type": "linear"}, "yaxis": {"autorange": true, "range": [-7.689842896642001, 6.689842896641994], "type": "linear"}}}, "image/png": "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*****************************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", "text/html": ["<div>                            <div id=\"1424166c-900b-4e21-8114-361edfe061a5\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"1424166c-900b-4e21-8114-361edfe061a5\")) {                    Plotly.newPlot(                        \"1424166c-900b-4e21-8114-361edfe061a5\",                        [{\"name\":\"sin\",\"showlegend\":true,\"visible\":true,\"x\":[-3.141592653589793,-2.443460952792061,-1.7453292519943295,-1.0471975511965979,-0.34906585039886595,0.34906585039886595,1.0471975511965974,1.7453292519943293,2.443460952792061,3.141592653589793],\"y\":[-1.2246467991473532e-16,-0.6427876096865395,-0.984807753012208,-0.8660254037844387,-0.34202014332566877,0.34202014332566877,0.8660254037844385,0.9848077530122081,0.6427876096865395,1.2246467991473532e-16],\"type\":\"scatter\"},{\"name\":\"sin - 1\",\"showlegend\":true,\"visible\":true,\"x\":[-3.141592653589793,-2.443460952792061,-1.7453292519943295,-1.0471975511965979,-0.34906585039886595,0.34906585039886595,1.0471975511965974,1.7453292519943293,2.443460952792061,3.141592653589793],\"y\":[-1.0000000000000002,-1.6427876096865395,-1.9848077530122081,-1.8660254037844388,-1.3420201433256689,-0.6579798566743312,-0.13397459621556151,-0.015192246987791869,-0.3572123903134605,-0.9999999999999999],\"type\":\"scatter\"},{\"name\":\"tan\",\"showlegend\":true,\"visible\":false,\"x\":[-3.141592653589793,-2.443460952792061,-1.7453292519943295,-1.0471975511965979,-0.34906585039886595,0.34906585039886595,1.0471975511965974,1.7453292519943293,2.443460952792061,3.141592653589793],\"y\":[1.2246467991473532e-16,0.8390996311772804,5.671281819617711,-1.7320508075688776,-0.3639702342662024,0.3639702342662024,1.7320508075688759,-5.671281819617718,-0.8390996311772804,-1.2246467991473532e-16],\"type\":\"scatter\"},{\"name\":\"tan - 1\",\"showlegend\":true,\"visible\":false,\"x\":[-3.141592653589793,-2.443460952792061,-1.7453292519943295,-1.0471975511965979,-0.34906585039886595,0.34906585039886595,1.0471975511965974,1.7453292519943293,2.443460952792061,3.141592653589793],\"y\":[-0.9999999999999999,-0.16090036882271963,4.671281819617711,-2.7320508075688776,-1.3639702342662023,-0.6360297657337977,0.7320508075688759,-6.671281819617718,-1.8390996311772803,-1.0000000000000002],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"title\":{\"text\":\"Sine\"},\"updatemenus\":[{\"buttons\":[{\"args\":[{\"y\":[[-1.2246467991473532e-16,-0.6427876096865395,-0.984807753012208,-0.8660254037844387,-0.34202014332566877,0.34202014332566877,0.8660254037844385,0.9848077530122081,0.6427876096865395,1.2246467991473532e-16],[-1.0000000000000002,-1.6427876096865395,-1.9848077530122081,-1.8660254037844388,-1.3420201433256689,-0.6579798566743312,-0.13397459621556151,-0.015192246987791869,-0.3572123903134605,-0.9999999999999999]],\"name\":[\"sin\",\"sin - 1\"],\"visible\":true},{\"title\":\"Sine\"},[0,1]],\"args2\":[{\"y\":[[1.2246467991473532e-16,0.8390996311772804,5.671281819617711,-1.7320508075688776,-0.3639702342662024,0.3639702342662024,1.7320508075688759,-5.671281819617718,-0.8390996311772804,-1.2246467991473532e-16],[-0.9999999999999999,-0.16090036882271963,4.671281819617711,-2.7320508075688776,-1.3639702342662023,-0.6360297657337977,0.7320508075688759,-6.671281819617718,-1.8390996311772803,-1.0000000000000002]],\"name\":[\"tan\",\"tan - 1\"],\"visible\":true},{\"title\":\"Tangent\"},[0,1]],\"label\":\"Toggle Sine / Tangent\",\"method\":\"update\"}],\"direction\":\"down\",\"showactive\":true,\"type\":\"buttons\"}]},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('1424166c-900b-4e21-8114-361edfe061a5');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# imports\n", "import plotly.graph_objects as go\n", "import numpy as np\n", "\n", "# data\n", "x = np.linspace(-np.pi, np.pi, 10)\n", "\n", "y1 = np.sin(x)\n", "y1b = y1-1\n", "\n", "y2 = np.tan(x)\n", "y2b = y2-1\n", "\n", "# plotly setup\n", "fig = go.Figure()\n", "\n", "# Add one ore more traces\n", "fig.add_traces(go.<PERSON><PERSON>(x=x, y=y1,name='sin', visible=True, showlegend=True))\n", "fig.add_traces(go.<PERSON><PERSON>(x=x, y=y1b,name='sin - 1', visible=True, showlegend=True))\n", "\n", "fig.add_traces(go.<PERSON><PERSON>(x=x, y=y2,name='tan', visible=False, showlegend=True))\n", "fig.add_traces(go.<PERSON><PERSON>(x=x, y=y2b,name='tan - 1', visible=False, showlegend=True))\n", "fig.update_layout({'title' : 'Sine'})\n", "\n", "# construct menus\n", "updatemenus = [{\n", "#                 'active':1,\n", "                'buttons': [{'method': 'update',\n", "                             'label': 'Toggle Sine / Tangent',\n", "                             'args': [\n", "                                      # 1. updates to the traces\n", "                                      {'y': [y1, y1b],\n", "                                       'name':['sin', 'sin - 1'],\n", "                                       'visible': True}, \n", "                                      \n", "                                      # 2. updates to the layout\n", "                                      {'title':'<PERSON>e'},\n", "                                      \n", "                                      # 3. which traces are affected \n", "                                      [0, 1],\n", "                                      \n", "                                      ],\n", "                             'args2': [\n", "                                       # 1. updates to the traces  \n", "                                       {'y': [y2, y2b],\n", "                                       'name':['tan', 'tan - 1'],\n", "                                       'visible':True},\n", "                                      \n", "                                       # 2. updates to the layout\n", "                                       {'title':'Tangent'},\n", "                                       \n", "                                       # 3. which traces are affected\n", "                                       [0, 1]\n", "                                      ]\n", "                              },\n", "                            ],\n", "                'type':'buttons',\n", "#                 'type':'dropdown',\n", "                'direction': 'down',\n", "                'showactive': True,}]\n", "\n", "# update layout with buttons, and show the figure\n", "fig.update_layout(updatemenus=updatemenus)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 6, "id": "9c44bf62-15e3-48a1-adef-6421cffa297a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr > th,\n", ".dataframe > tbody > tr > td {\n", "  text-align: right;\n", "}\n", "</style>\n", "<small>shape: (60, 4)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>division</th><th>country</th><th>merch_style</th><th>value</th></tr><tr><td>cat</td><td>cat</td><td>str</td><td>f32</td></tr></thead><tbody><tr><td>&quot;Grocery&quot;</td><td>&quot;CZ&quot;</td><td>&quot;srp&quot;</td><td>2.11097104e8</td></tr><tr><td>&quot;Produce&quot;</td><td>&quot;CZ&quot;</td><td>&quot;srp&quot;</td><td>2.863308e6</td></tr><tr><td>&quot;Fresh&quot;</td><td>&quot;HU&quot;</td><td>&quot;srp&quot;</td><td>2.6715262e7</td></tr><tr><td>&quot;GM&quot;</td><td>&quot;HU&quot;</td><td>&quot;srp&quot;</td><td>1.0335815e7</td></tr><tr><td>&quot;Grocery&quot;</td><td>&quot;SK&quot;</td><td>&quot;srp&quot;</td><td>1.86357664e8</td></tr><tr><td>&quot;Produce&quot;</td><td>&quot;SK&quot;</td><td>&quot;srp&quot;</td><td>2.038407e6</td></tr><tr><td>&quot;Produce&quot;</td><td>&quot;HU&quot;</td><td>&quot;srp&quot;</td><td>2.010302e6</td></tr><tr><td>&quot;Fresh&quot;</td><td>&quot;CZ&quot;</td><td>&quot;srp&quot;</td><td>3.2137568e7</td></tr><tr><td>&quot;Grocery&quot;</td><td>&quot;HU&quot;</td><td>&quot;srp&quot;</td><td>1.81244224e8</td></tr><tr><td>&quot;GM&quot;</td><td>&quot;SK&quot;</td><td>&quot;srp&quot;</td><td>5.318082e6</td></tr><tr><td>&quot;Fresh&quot;</td><td>&quot;SK&quot;</td><td>&quot;srp&quot;</td><td>3.0140564e7</td></tr><tr><td>&quot;GM&quot;</td><td>&quot;CZ&quot;</td><td>&quot;srp&quot;</td><td>6.460468e6</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;Grocery&quot;</td><td>&quot;CZ&quot;</td><td>&quot;split_pallet&quot;</td><td>5.466167e6</td></tr><tr><td>&quot;Produce&quot;</td><td>&quot;CZ&quot;</td><td>&quot;split_pallet&quot;</td><td>0.0</td></tr><tr><td>&quot;Fresh&quot;</td><td>&quot;HU&quot;</td><td>&quot;split_pallet&quot;</td><td>30730.0</td></tr><tr><td>&quot;GM&quot;</td><td>&quot;HU&quot;</td><td>&quot;split_pallet&quot;</td><td>218120.0</td></tr><tr><td>&quot;Grocery&quot;</td><td>&quot;SK&quot;</td><td>&quot;split_pallet&quot;</td><td>3.322333e6</td></tr><tr><td>&quot;Produce&quot;</td><td>&quot;SK&quot;</td><td>&quot;split_pallet&quot;</td><td>0.0</td></tr><tr><td>&quot;Produce&quot;</td><td>&quot;HU&quot;</td><td>&quot;split_pallet&quot;</td><td>0.0</td></tr><tr><td>&quot;Fresh&quot;</td><td>&quot;CZ&quot;</td><td>&quot;split_pallet&quot;</td><td>0.0</td></tr><tr><td>&quot;Grocery&quot;</td><td>&quot;HU&quot;</td><td>&quot;split_pallet&quot;</td><td>9.420992e6</td></tr><tr><td>&quot;GM&quot;</td><td>&quot;SK&quot;</td><td>&quot;split_pallet&quot;</td><td>224455.0</td></tr><tr><td>&quot;Fresh&quot;</td><td>&quot;SK&quot;</td><td>&quot;split_pallet&quot;</td><td>0.0</td></tr><tr><td>&quot;GM&quot;</td><td>&quot;CZ&quot;</td><td>&quot;split_pallet&quot;</td><td>283423.0</td></tr></tbody></table></div>"], "text/plain": ["shape: (60, 4)\n", "┌──────────┬─────────┬──────────────┬──────────────┐\n", "│ division ┆ country ┆ merch_style  ┆ value        │\n", "│ ---      ┆ ---     ┆ ---          ┆ ---          │\n", "│ cat      ┆ cat     ┆ str          ┆ f32          │\n", "╞══════════╪═════════╪══════════════╪══════════════╡\n", "│ Grocery  ┆ CZ      ┆ srp          ┆ 2.11097104e8 │\n", "│ Produce  ┆ CZ      ┆ srp          ┆ 2.863308e6   │\n", "│ Fresh    ┆ HU      ┆ srp          ┆ 2.6715262e7  │\n", "│ GM       ┆ HU      ┆ srp          ┆ 1.0335815e7  │\n", "│ …        ┆ …       ┆ …            ┆ …            │\n", "│ Grocery  ┆ HU      ┆ split_pallet ┆ 9.420992e6   │\n", "│ GM       ┆ SK      ┆ split_pallet ┆ 224455.0     │\n", "│ Fresh    ┆ SK      ┆ split_pallet ┆ 0.0          │\n", "│ GM       ┆ CZ      ┆ split_pallet ┆ 283423.0     │\n", "└──────────┴─────────┴──────────────┴──────────────┘"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import plotly.express as px\n", "\n", "\n", "def pie_chart(df, name_hoover, values):\n", "    return px.pie(df.to_pandas(),                      # Pandas DataFrame\n", "           names = name_hoover,\n", "           values = values, \n", "           hover_name = name_hoover,\n", "            facet_col='division',\n", "            facet_row='country',\n", "            width=1200,\n", "            height=500,\n", "    \n", "           color_discrete_sequence= px.colors.colorbrewer.Accent)\n", "\n", "repl_types = ['srp','nsrp','full_pallet','mu','split_pallet']\n", "Repl_Dataset.with_columns(pl.col(c)*pl.col(\"shelfCapacity\") for c in Repl_Dataset.columns if c in repl_types).groupby(['division', 'country']).agg(pl.col(col).sum() for col in Repl_Dataset.columns if col in repl_types).melt(id_vars=['division','country'], value_vars=repl_types, variable_name='merch_style')#.pipe(pie_chart, 'merch_style','value')\n"]}, {"cell_type": "code", "execution_count": null, "id": "27927882-f98b-45c4-b6a9-7b53ef72a0ea", "metadata": {}, "outputs": [], "source": ["Repl_Dataset.with_columns(pl.when(pl.col(column.name) == 0).then(pl.col(column.name).mean().over(['country','pmg'])).otherwise(pl.col(column.name)) for column in Repl_Dataset if column.name in ['shelfCapacity','pallet_capacity']) .with_columns(pl.when(pl.col(column.name) == 0).then(pl.col(column.name).mean().over(['country','tpnb'])).otherwise(pl.col(column.name)) for column in Repl_Dataset if column.name in ['item_price']).filter((pl.col(\"store\") == 11012) & (pl.col(\"tpnb\") == 213815313))"]}, {"cell_type": "code", "execution_count": null, "id": "661ce0fc-e5b9-4146-88b9-62c3b7dd0bb9", "metadata": {}, "outputs": [], "source": ["Repl_Dataset.with_columns(pl.when(pl.col(\"opening_type\").is_null()).then(pl.lit(\"no_data\")).otherwise(pl.col(\"opening_type\")).alias(\"opening_type\"))"]}, {"cell_type": "code", "execution_count": null, "id": "b76371fd-eb77-44fa-bd07-84f6a0c75d2f", "metadata": {}, "outputs": [], "source": ["Repl_Dataset = Repl_Dataset.to_pandas()\n", "int_list = [\n", "    \"srp\",\n", "    \"nsrp\",\n", "    \"mu\",\n", "    \"full_pallet\",\n", "    \"split_pallet\",\n", "    \"icream_nsrp\",\n", "    \"single_pick\",\n", "    \"checkout_stand_flag\",\n", "    \"backroom_flag\",\n", "    \"clipstrip_flag\",\n", "    \"case_capacity\",\n", "    \"is_capping_shelf\",\n", "    \"shelfCapacity\",\n", "]  #\n", "Repl_Dataset[int_list] = Repl_Dataset[int_list].apply(lambda x: x.astype(\"int32\"))\n", "int_part = optimize_types(Repl_Dataset.select_dtypes(include=[\"float\", \"int\"]))\n", "cat_part =  optimize_objects(Repl_Dataset.select_dtypes(exclude=[\"float\", \"int\"]))\n", "Repl_Dataset = pd.concat([int_part, cat_part], axis=1)\n", "Repl_Dataset['opening_type'] = Repl_Dataset['opening_type'].replace({np.nan : 'not Ownbrand'})"]}, {"cell_type": "code", "execution_count": null, "id": "95cf4f98-4c7e-4232-893f-f3e4387479bc", "metadata": {}, "outputs": [], "source": ["Repl_Dataset.info()"]}, {"cell_type": "code", "execution_count": null, "id": "dfa14b49-144a-4e1b-aecd-0b8365afc0a0", "metadata": {}, "outputs": [], "source": ["Repl_Dataset[(Repl_Dataset.single_pick > 0)&(Repl_Dataset.store == 41520)].product_name.unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "cd8403db-bbb4-4c71-814c-d6574fc9323a", "metadata": {}, "outputs": [], "source": ["def Broken_Case_Creator(\n", "    directory, df, how_many_HU, how_many_CZ, how_many_SK, saved_name\n", "):\n", "\n", "    dry_optional = [\"DRY23\", \"DRY25\", \"DRY29\", \"DRY30\", \"DRY31\", \"DRY32\", \"DRY33\"]\n", "    broken_stores_hu = [\n", "        41710,\n", "        41700,\n", "        41400,\n", "        41730,\n", "        41790,\n", "        41006,\n", "        41005,\n", "        41670,\n", "        41440,\n", "        41560,\n", "        41390,\n", "        41760,\n", "        41600,\n", "        41015,\n", "    ]\n", "    broken_stores_cz = [11011, 11013, 11033]\n", "    broken_stores_sk = [21004, 21014, 21019, 21002]\n", "\n", "    CE_broken_list = broken_stores_hu + broken_stores_cz + broken_stores_sk\n", "\n", "    no_need_dry = df[(df.dep == \"DRY\") & (~df.pmg.isin(dry_optional))][\"pmg\"].unique()\n", "\n", "    broken_case_df = df.loc[\n", "        (df.store.isin(CE_broken_list))\n", "        & (df.division.isin([\"GM\", \"Grocery\"]))\n", "        & (df.pmg != \"HDL01\")\n", "        & (~df.pmg.isin(no_need_dry))\n", "        & (df.full_pallet == 0)\n", "        & (df.mu == 0)\n", "        & (df.cases_delivered >= 0)\n", "        & (df.day == \"Thursday\")\n", "    ][[\"country\", \"store\", \"tpnb\", \"day\", \"pmg\", \"dep\", \"division\"]]\n", "    broken_case_df[\"helper_broken_case\"] = np.random.uniform(\n", "        0, 1, broken_case_df.shape[0]\n", "    )\n", "    broken_case_df = broken_case_df.sort_values(\n", "        by=[\"store\", \"helper_broken_case\"], ascending=[True, False]\n", "    )\n", "\n", "    def broken_case_by_country(stores, how_many_tpn):\n", "        a = pd.DataFrame()\n", "        for s in stores:\n", "            df_inner = broken_case_df[broken_case_df.store == s][:how_many_tpn]\n", "            a = pd.concat([a, df_inner])\n", "\n", "        a[\"broken_case_flag\"] = 1\n", "        a[\"broken_case_flag\"] = a[\"broken_case_flag\"].astype(\"int8\")\n", "        a[\"day\"] = \"Thursday\"\n", "        a = a[[\"store\", \"tpnb\", \"day\", \"broken_case_flag\"]]\n", "\n", "        return a\n", "\n", "    hu = broken_case_by_country(broken_stores_hu, how_many_HU)\n", "    sk = broken_case_by_country(broken_stores_sk, how_many_CZ)\n", "    cz = broken_case_by_country(broken_stores_cz, how_many_SK)\n", "\n", "    ce = pd.concat([hu, sk, cz])\n", "    ce.to_csv(\n", "        directory + \"\\\\\" +  f\"inputs/files_for_dataset/broken_cases_list_{saved_name}.csv.gz\",\n", "        index=False,\n", "        compression=\"gzip\",\n", "    )\n", "\n", "    df = df.merge(ce, on=[\"store\", \"day\", \"tpnb\"], how=\"left\").replace(np.nan, 0)\n", "\n", "    print(\"Broken Cases are calculated and added to Repl_Dataset!\")\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "b18f99c3-cd7c-4c26-ad2e-30e42651713a", "metadata": {}, "outputs": [], "source": ["directory = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\"\n", "\n", "Repl_Dataset = Broken_Case_Creator(\n", "    directory, Repl_Dataset, 68, 115, 160, '2023w14_27'\n", ")\n"]}, {"cell_type": "markdown", "id": "119e9bf1-3818-4f35-af69-c4dfac276052", "metadata": {}, "source": ["# Opsdev part"]}, {"cell_type": "code", "execution_count": null, "id": "3bab46a9-eae9-40b2-ba9d-5da095cb96b6", "metadata": {}, "outputs": [], "source": ["pd.__version__"]}, {"cell_type": "code", "execution_count": null, "id": "f2aa7785-c860-4358-8b98-ed7d04996017", "metadata": {}, "outputs": [], "source": ["!pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org  -U bottleneck"]}, {"cell_type": "code", "execution_count": null, "id": "6ebd0812-7a50-44b6-9cf9-b37671f7fc1f", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import time\n", "pd.set_option(\"display.max_columns\", None)\n"]}, {"cell_type": "code", "execution_count": null, "id": "edd88f29-39ab-40ee-a4ee-59d0043abfc2", "metadata": {}, "outputs": [], "source": ["!python"]}, {"cell_type": "code", "execution_count": null, "id": "38e88120-71a1-47b6-880f-c3525c6c36c3", "metadata": {}, "outputs": [], "source": ["# Path\n", "\n", "excel_inputs_f = \"inputs/Repl/Stores_Inputs_2023_Q1_SFM_SFB.xlsx\"\n", "directory = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\")\n", "\n", "a = pd.read_csv(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\new_10_12\",compression=\"gzip\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "ca632e5f-0d9e-4007-801a-efa709bd56f6", "metadata": {}, "outputs": [], "source": ["a.head()"]}, {"cell_type": "code", "execution_count": null, "id": "abfd3e8c-c44b-433b-829b-b7ea11f41713", "metadata": {}, "outputs": [], "source": ["# FOIL\n", "\n", "opsdev = a.copy()\n", "\n", "opsdev['Planogram_on_Store'] = np.where(opsdev['Planogram_on_Store'] == 0, 1, opsdev['Planogram_on_Store'])\n", "opsdev['Position_Capacity_X_Planogram_on_Store'] =opsdev['Position_Capacity'] *  opsdev['Planogram_on_Store']\n", "\n", "foil_calc = pd.read_excel(directory / excel_inputs_f, sheet_name='foil_calc', usecols=['Country',\n", "                                                                                        'level4',\n", "                                                                                        'Foil Opening Reduction Opportunity',\n", "                                                                                        'Proposed Opening %',\n", "                                                                                        'Foil Replen <PERSON> Facing',\n", "                                                                                        'SRP opening reduction opportunity',\n", "                                                                                        'extra disassemble %'],\n", "                          dtype={'level4': np.int64})\n", "\n", "\n", "opsdev_foil = opsdev.copy()\n", "\n", "opsdev_foil['level4'] = [str(int(p1)) + str(int(p2)) + str(int(p3)) + str(int(p4)) for p1, p2, p3, p4 in zip(opsdev_foil['Division'],\n", "                                                                                         opsdev_foil['Department'],\n", "                                                                                         opsdev_foil['Section'],\n", "                                                                                         opsdev_foil['Group_'])]\n", "\n", "\n", "opsdev_foil['level4'] = opsdev_foil['level4'].astype(\"int64\")\n", "opsdev_foil = opsdev_foil.merge(foil_calc, on=['Country', 'level4'], how='left')\n", "\n", "cond=[(opsdev_foil['Foil Opening Reduction Opportunity'] == 1)\n", "      & (opsdev_foil['merchstyle_string'] == 'Unit')\n", "      & (opsdev_foil['Pallet_info'].isnull()),\n", "\n", "      (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)\n", "      & (opsdev_foil['merchstyle_string'] == 'Tray')\n", "      & (opsdev_foil['Pallet_info'].isnull()),\n", "\n", "      (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)\n", "      &  (opsdev_foil['merchstyle_string'] == 'Case')\n", "      & (opsdev_foil['Pallet_info'].isnull())]\n", "\n", "result = [opsdev_foil['Position_HFacing'],\n", "          opsdev_foil['Position_HFacing'] * opsdev_foil['TrayNumberWide'],\n", "          opsdev_foil['Position_HFacing'] * opsdev_foil['CaseNumberWide']]\n", "\n", "\n", "owise= (opsdev_foil['Position_HFacing'] * opsdev_foil['merchstyle_ID'])\n", "\n", "\n", "opsdev_foil['for_foil_check'] = np.select(cond,result,owise)\n", "\n", "opsdev_foil['foil'] = np.where((opsdev_foil['Foil Replen Min Facing'] < opsdev_foil['for_foil_check'])\n", "                               & (opsdev_foil['Pallet_info'].isnull()), \n", "                               opsdev_foil[\"Proposed Opening %\"], 0)\n", "\n", "for x in [opsdev, opsdev_foil]:\n", "\n", "    country_cond = [\n", "        x[\"Country\"] == \"HU\",\n", "        x[\"Country\"] == \"SK\",\n", "        x[\"Country\"] == \"CZ\",\n", "    ]\n", "\n", "    store_code_create = [\n", "        str(4) + x.Store_Number.astype(str),\n", "        str(2) + x.Store_Number.astype(str),\n", "        str(1) + x.Store_Number.astype(str),\n", "    ]\n", "\n", "    x[\"Store_Number\"] = np.select(country_cond, store_code_create, 0)\n", "\n", "\n", "\n", "\n", "opsdev_foil.rename(\n", "    columns={\n", "        \"Country\":  \"country\",\n", "        \"Store_Number\": \"store\",\n", "        \"Product_id\": \"tpnb\"}, inplace=True)\n", "\n", "opsdev_foil['store'] = opsdev_foil['store'].astype(\"int32\")\n", "\n", "opsdev_foil = opsdev_foil[['Division',\n", "                           'Department',\n", "                           'Section',\n", "                           'Group_',\n", "                           'level4',\n", "                           'country',\n", "                           'store',\n", "                           'tpnb',\n", "                           \"Product_Name\",\n", "                           # \"SRP opening reduction opportunity\",\n", "                           \"Pallet_info\",\n", "                           'Position_HFacing',\n", "                           'merchstyle_ID',\n", "                           'merchstyle_string',\n", "                           'TrayNumberWide',\n", "                           'Foil Replen <PERSON> Facing',\n", "                           'foil']].query(\"foil > 0\").drop_duplicates()\n", "\n", "opsdev_foil = opsdev_foil[['country','store','level4', 'tpnb', 'foil']].drop_duplicates()\n", "\n", "foil_calc.rename(columns={'Country':'country'}, inplace=True)\n", "\n", "opsdev_foil = opsdev_foil.merge(foil_calc[['country', 'level4', 'SRP opening reduction opportunity','extra disassemble %']],\n", "                                on=['country', 'level4'], how='left')"]}, {"cell_type": "code", "execution_count": null, "id": "9b42948a-e83f-40ed-851c-cd9164a5f535", "metadata": {}, "outputs": [], "source": ["#opsdev part\n", "\n", "\n", "# icase\n", "\n", "opsdev['icase'] = opsdev['CaseTotalNumber'] + opsdev['TrayTotalNumber']\n", "\n", "# calculate shelf_cap ratio\n", "opsdev['shelf_cap_ratio'] = opsdev['Position_Capacity_X_Planogram_on_Store'] / opsdev.groupby(['Store_Number', 'Product_id'])['Position_Capacity_X_Planogram_on_Store'].transform('sum')\n", "\n", "# list of repl_type\n", "srp = [\"Tray\", \"<PERSON>\"]\n", "nsrp = [\"Unit\", \"Display\", \"Alternate\", \"Loose\", \"Log Stack\"]\n", "pallet_info = [\"Half_Pallet\", \"Palle<PERSON>\", \"Split_Pallet\"]\n", "\n", "# create columns for repl_type\n", "opsdev[\"full_pallet\"] = np.where(opsdev.Pallet_info == \"Pallet\", opsdev['shelf_cap_ratio'], 0)\n", "opsdev[\"mu\"] = np.where(opsdev.Pallet_info == \"Half_Pallet\", opsdev['shelf_cap_ratio'], 0)\n", "opsdev[\"split_pallet\"] = np.where(opsdev.Pallet_info == \"Split_Pallet\", opsdev['shelf_cap_ratio'], 0)\n", "opsdev[\"nsrp\"] = np.where(\n", "    (opsdev.merchstyle_string.isin(nsrp)) & (~opsdev.Pallet_info.isin(pallet_info)),\n", "    opsdev['shelf_cap_ratio'],\n", "    0,\n", ")\n", "opsdev[\"srp\"] = np.where(opsdev.merchstyle_string.isin(srp), opsdev['shelf_cap_ratio'], 0)\n", "opsdev[\"srp\"] = np.where((opsdev.Pallet_info == \"Split_Pallet\"), 0, opsdev.srp)\n", "opsdev[\"srp\"] = np.where(\n", "    (opsdev.Pallet_info == \"Pallet\") | (opsdev.Pallet_info == \"Half_Pallet\"),\n", "    0,\n", "    opsdev.srp,\n", ")\n", "\n", "opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]] = opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]].astype(\"int64\")\n", "\n", "opsdev['icream_nsrp'] = np.where((opsdev['Division'] == 1)\n", "                                & (opsdev['Department'] == 17)\n", "                                & (opsdev['Section'] == 1702)\n", "                                & (opsdev.merchstyle_string.isin(['Loose', 'Alternate']))\n", "                                , opsdev['shelf_cap_ratio'], 0)\n", "\n", "for x in [ \"srp\",\"nsrp\",\"mu\",\"full_pallet\", \"split_pallet\"]:\n", "\n", "    opsdev[x] = np.where(opsdev['icream_nsrp'] >0, 0, opsdev[x] )\n", "    \n", "    \n", "opsdev.rename(\n", "    columns={\n", "        \"Country\":  \"country\",\n", "        \"Store_Number\": \"store\",\n", "        \"Product_id\": \"tpnb\",\n", "        \"Product_Name\": \"product_name\",\n", "        \"Displaygroup_description\": \"display_group\",\n", "        \"Displaygroup\": \"drg\",\n", "        'Position_Capacity_X_Planogram_on_Store':\"shelfCapacity\"\n", "    },\n", "    inplace=True,\n", ")\n", "opsdev[\"checkout_stand_flag\"] = np.where(\n", "    opsdev[\"display_group\"].str.lower().str.contains(\"checkout\"), 1, 0\n", ")\n", "opsdev[\"clipstrip_flag\"] = np.where(\n", "    opsdev[\"display_group\"].str.lower().str.contains(\"clipstrip\"), 1, 0\n", ")\n", "opsdev[\"backroom_flag\"] = np.where(opsdev[\"drg\"].isin([\"Z2D\", \"L1N\"]), 1, 0)\n", "opsdev[\"store\"] = opsdev[\"store\"].astype(float).astype(int)\n", "opsdev[\"tpnb\"] = (\n", "    pd.to_numeric(opsdev.tpnb, errors=\"coerce\").fillna(0).astype(float).astype(int)\n", ")\n", "checkout_stand = opsdev[opsdev.checkout_stand_flag == 1][\n", "    [\"store\", \"tpnb\"]\n", "].drop_duplicates()\n", "checkout_stand[\"checkout_stand_flag\"] = int(1)\n", "clipstrip_flag = opsdev[opsdev.clipstrip_flag == 1][[\"store\", \"tpnb\"]].drop_duplicates()\n", "clipstrip_flag[\"clipstrip_flag\"] = int(1)\n", "backroom_flag = opsdev[opsdev.backroom_flag == 1][[\"store\", \"tpnb\"]].drop_duplicates()\n", "backroom_flag[\"backroom_flag\"] = int(1)\n", "\n", "opsdev = opsdev[\n", "    [\n", "        \"country\",\n", "        \"store\",\n", "        # \"level4\",\n", "        \"tpnb\",\n", "        \"product_name\",\n", "        \"srp\",\n", "        \"nsrp\",\n", "        \"mu\",\n", "        \"full_pallet\",\n", "        \"split_pallet\",\n", "        \"icream_nsrp\",\n", "        \"shelfCapacity\",\n", "        \"icase\"\n", "\n", "    ]\n", "]\n", "opsdev = opsdev.merge(checkout_stand, on=[\"store\", \"tpnb\"], how=\"left\")\n", "opsdev[\"checkout_stand_flag\"].replace(np.nan, 0, inplace=True)\n", "opsdev = opsdev.merge(clipstrip_flag, on=[\"store\", \"tpnb\"], how=\"left\")\n", "opsdev[\"clipstrip_flag\"].replace(np.nan, 0, inplace=True)\n", "opsdev = opsdev.merge(backroom_flag, on=[\"store\", \"tpnb\"], how=\"left\")\n", "opsdev[\"backroom_flag\"].replace(np.nan, 0, inplace=True)\n", "\n", "\n", "\n", "\n", "# filter duplicates and form the dataframe again\n", "grouped_opsdev = (\n", "    opsdev.groupby([\"country\",\n", "                    \"store\",\n", "                    # \"level4\", \n", "                    \"tpnb\",\n", "                    \"product_name\",\n", "                    \n", "                    ], observed = True)\n", "    .agg(\n", "        {\n", "            \"srp\": \"sum\",\n", "            \"nsrp\": \"sum\",\n", "            \"mu\": \"sum\",\n", "            \"full_pallet\": \"sum\",\n", "            \"split_pallet\": \"sum\",\n", "            \"icream_nsrp\": \"sum\",\n", "            \"checkout_stand_flag\": \"sum\",\n", "            \"clipstrip_flag\": \"sum\",\n", "            \"backroom_flag\": \"sum\",\n", "            'shelfCapacity':\"sum\",\n", "            \"icase\":\"mean\"\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "grouped_opsdev[\"total\"] = (\n", "    grouped_opsdev.srp\n", "    + grouped_opsdev.nsrp\n", "    + grouped_opsdev.mu\n", "    + grouped_opsdev.full_pallet\n", "    + grouped_opsdev.split_pallet\n", "    + grouped_opsdev.icream_nsrp\n", ")\n", "\n", "# check where is duplicates\n", "dupl_rows = grouped_opsdev[grouped_opsdev.total > 1.1].shape[0]\n", "print(f\"We have: {dupl_rows} rows duplicated\")\n", "\n", "\n", "# final dataframe\n", "grouped_opsdev.drop(columns={\"total\"}, inplace=True)\n", "opsdev = grouped_opsdev.copy()\n", "opsdev[\"checkout_stand_flag\"] = np.where(opsdev[\"checkout_stand_flag\"] > 0, 1, 0)\n", "opsdev[\"clipstrip_flag\"] = np.where(opsdev[\"clipstrip_flag\"] > 0, 1, 0)\n", "opsdev[\"backroom_flag\"] = np.where(opsdev[\"backroom_flag\"] > 0, 1, 0)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "74615ea8-94bf-4afc-98d2-0003c86e4bdc", "metadata": {}, "outputs": [], "source": ["opsdev_foil.head()"]}, {"cell_type": "code", "execution_count": null, "id": "66fc80bd-64d7-4807-b292-90dc01b9bf65", "metadata": {}, "outputs": [], "source": ["opsdev.to_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\CE_JDA_SRD_for_model_modified\",compression=\"gzip\")"]}, {"cell_type": "code", "execution_count": null, "id": "dcbf2645-a8c2-4a23-90e1-2a0eff3be80e", "metadata": {}, "outputs": [], "source": ["opsdev[opsdev.product_name.str.contains(\"COCA\")].head()"]}, {"cell_type": "code", "execution_count": null, "id": "96d11f66-7561-4363-80cc-fd1ded535876", "metadata": {}, "outputs": [], "source": ["opsdev[(opsdev.mu>0)&(opsdev.full_pallet >0)].head()"]}, {"cell_type": "code", "execution_count": null, "id": "481588df-6c19-4ffb-9ec4-b43075511901", "metadata": {}, "outputs": [], "source": ["b = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\09-10-2023\\CE_JDA_SRD_for_model\")"]}, {"cell_type": "code", "execution_count": null, "id": "00b60d96-7628-4536-8b7c-1f66c6fcd706", "metadata": {}, "outputs": [], "source": ["b.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0ef21149-e64c-4ebc-a2b0-476928abfc63", "metadata": {}, "outputs": [], "source": ["\n", "opsdev[(opsdev.icream_nsrp>0)&(opsdev.nsrp>0)].head()"]}, {"cell_type": "markdown", "id": "b341da27-a068-4cd3-b382-891ae0e5d99f", "metadata": {}, "source": ["## OLD opsdev"]}, {"cell_type": "code", "execution_count": null, "id": "c80dc3af-4b00-4dcc-aa7c-9879c44765f4", "metadata": {"tags": []}, "outputs": [], "source": ["foil_calc = pd.read_excel(directory / excel_inputs_f, sheet_name='foil_calc', usecols=['Country',\n", "                                                                                        'level4',\n", "                                                                                        'Foil Opening Reduction Opportunity',\n", "                                                                                        'Proposed Opening %',\n", "                                                                                        'Foil Replen <PERSON> Facing',\n", "                                                                                        'SRP opening reduction opportunity',\n", "                                                                                        'extra disassemble %'],\n", "                          dtype={'level4': np.int64})\n", "\n", "# foil_calc.rename(columns={\"Proposed Opening %\":\"foil\"}, inplace=True)\n", "\n", "# foil_calc = foil_calc[foil_calc['SRP opening reduction opportunity'] == 1]\n", "\n", "opsdev = a.copy()\n", "\n", "opsdev_foil = opsdev.copy()\n", "\n", "opsdev_foil['level4'] = [str(int(p1)) + str(int(p2)) + str(int(p3)) + str(int(p4)) for p1, p2, p3, p4 in zip(opsdev_foil['Division'],\n", "                                                                                         opsdev_foil['Department'],\n", "                                                                                         opsdev_foil['Section'],\n", "                                                                                         opsdev_foil['Group_'])]\n", "opsdev_foil['level4'] = opsdev_foil['level4'].astype(\"int64\")\n", "\n", "opsdev_foil = opsdev_foil.merge(foil_calc, on=['Country', 'level4'], how='left')\n", "\n", "\n", "cond=[(opsdev_foil['Foil Opening Reduction Opportunity'] == 1)\n", "      & (opsdev_foil['merchstyle_string'] == 'Unit')\n", "      & (opsdev_foil['Pallet_info'] == ''),\n", "\n", "      (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)\n", "      & (opsdev_foil['merchstyle_string'] == 'Tray')\n", "      & (opsdev_foil['Pallet_info'] == ''),\n", "\n", "      (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)\n", "      &  (opsdev_foil['merchstyle_string'] == 'Case')\n", "      & (opsdev_foil['Pallet_info'] == '')]\n", "\n", "result = [opsdev_foil['Position_HFacing'],\n", "          opsdev_foil['Position_HFacing'] * opsdev_foil['TrayNumberWide'],\n", "          opsdev_foil['Position_HFacing'] * opsdev_foil['CaseNumberWide']]\n", "\n", "\n", "owise= (opsdev_foil['Position_HFacing'] * opsdev_foil['merchstyle_ID'])\n", "\n", "\n", "opsdev_foil['for_foil_check'] = np.select(cond,result,owise)\n", "\n", "opsdev_foil['foil'] = np.where((opsdev_foil['Foil Replen Min Facing'] < opsdev_foil['for_foil_check'])\n", "                               & (opsdev_foil['Pallet_info'] == ''), \n", "                               opsdev_foil[\"Proposed Opening %\"], 0)\n", "\n", "\n", "\n", "for x in [opsdev, opsdev_foil]:\n", "\n", "    country_cond = [\n", "        x[\"Country\"] == \"HU\",\n", "        x[\"Country\"] == \"SK\",\n", "        x[\"Country\"] == \"CZ\",\n", "    ]\n", "\n", "    store_code_create = [\n", "        str(4) + x.Store_Number.astype(str),\n", "        str(2) + x.Store_Number.astype(str),\n", "        str(1) + x.Store_Number.astype(str),\n", "    ]\n", "\n", "    x[\"Store_Number\"] = np.select(country_cond, store_code_create, 0)\n", "\n", "\n", "\n", "\n", "opsdev_foil.rename(\n", "    columns={\n", "        \"Country\":  \"country\",\n", "        \"Store_Number\": \"store\",\n", "        \"Product_id\": \"tpnb\"}, inplace=True)\n", "\n", "opsdev_foil['store'] = opsdev_foil['store'].astype(\"int32\")\n", "opsdev_foil = opsdev_foil[['Division',\n", "                           'Department',\n", "                           'Section',\n", "                           'Group_',\n", "                           'level4',\n", "                           'country',\n", "                           'store',\n", "                           'tpnb',\n", "                           \"Product_Name\",\n", "                           # \"SRP opening reduction opportunity\",\n", "                           \"Pallet_info\",\n", "                           'Position_HFacing',\n", "                           'merchstyle_ID',\n", "                           'merchstyle_string',\n", "                           'TrayNumberWide',\n", "                           'Foil Replen <PERSON> Facing',\n", "                           'foil']].query(\"foil > 0\").drop_duplicates()\n", "\n", "opsdev_foil = opsdev_foil[['country','store','level4', 'tpnb', 'foil']].drop_duplicates()\n", "foil_calc.rename(columns={'Country':'country'}, inplace=True)\n", "opsdev_foil = opsdev_foil.merge(foil_calc[['country', 'level4', 'SRP opening reduction opportunity','extra disassemble %']],\n", "                                on=['country', 'level4'], how='left')\n", "\n", "\n", "\n", "# opsdev\n", "\n", "\n", "\n", "\n", "# list of repl_type\n", "srp = [\"Tray\", \"<PERSON>\"]\n", "nsrp = [\"Unit\", \"Display\", \"Alternate\", \"Loose\", \"Log Stack\"]\n", "pallet_info = [\"Half_Pallet\", \"Palle<PERSON>\", \"Split_Pallet\"]\n", "\n", "# create columns for repl_type\n", "\n", "\n", "opsdev[\"full_pallet\"] = np.where(opsdev.Pallet_info == \"Pallet\", 1, 0)\n", "opsdev[\"mu\"] = np.where(opsdev.Pallet_info == \"Half_Pallet\", 1, 0)\n", "opsdev[\"split_pallet\"] = np.where(opsdev.Pallet_info == \"Split_Pallet\", 1, 0)\n", "opsdev[\"nsrp\"] = np.where(\n", "    (opsdev.merchstyle_string.isin(nsrp)) & (~opsdev.Pallet_info.isin(pallet_info)),\n", "    1,\n", "    0,\n", ")\n", "opsdev[\"srp\"] = np.where(opsdev.merchstyle_string.isin(srp), 1, 0)\n", "opsdev[\"srp\"] = np.where((opsdev.Pallet_info == \"Split_Pallet\"), 0, opsdev.srp)\n", "opsdev[\"srp\"] = np.where(\n", "    (opsdev.Pallet_info == \"Pallet\") | (opsdev.Pallet_info == \"Half_Pallet\"),\n", "    0,\n", "    opsdev.srp,\n", ")\n", "\n", "opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]] = opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]].astype(\"int64\")\n", "\n", "opsdev['icream_nsrp'] = np.where((opsdev['Division'] == 1)\n", "                                & (opsdev['Department'] == 17)\n", "                                & (opsdev['Section'] == 1702)\n", "                                & (opsdev.merchstyle_string.isin(['Loose', 'Alternate']))\n", "                                , 1, 0)\n", "\n", "for x in [ \"srp\",\"nsrp\",\"mu\",\"full_pallet\", \"split_pallet\"]:\n", "\n", "    opsdev[x] = np.where(opsdev['icream_nsrp'] == 1, 0, opsdev[x] )\n", "\n", "\n", "# rename columns\n", "\n", "opsdev.rename(\n", "    columns={\n", "        \"Country\":  \"country\",\n", "        \"Store_Number\": \"store\",\n", "        \"Product_id\": \"tpnb\",\n", "        \"Product_Name\": \"product_name\",\n", "        \"Displaygroup_description\": \"display_group\",\n", "        \"Displaygroup\": \"drg\"\n", "    },\n", "    inplace=True,\n", ")\n", "opsdev[\"checkout_stand_flag\"] = np.where(\n", "    opsdev[\"display_group\"].str.lower().str.contains(\"checkout\"), 1, 0\n", ")\n", "opsdev[\"clipstrip_flag\"] = np.where(\n", "    opsdev[\"display_group\"].str.lower().str.contains(\"clipstrip\"), 1, 0\n", ")\n", "opsdev[\"backroom_flag\"] = np.where(opsdev[\"drg\"].isin([\"Z2D\", \"L1N\"]), 1, 0)\n", "opsdev[\"store\"] = opsdev[\"store\"].astype(float).astype(int)\n", "opsdev[\"tpnb\"] = (\n", "    pd.to_numeric(opsdev.tpnb, errors=\"coerce\").fillna(0).astype(float).astype(int)\n", ")\n", "checkout_stand = opsdev[opsdev.checkout_stand_flag == 1][\n", "    [\"store\", \"tpnb\"]\n", "].drop_duplicates()\n", "checkout_stand[\"checkout_stand_flag\"] = int(1)\n", "clipstrip_flag = opsdev[opsdev.clipstrip_flag == 1][[\"store\", \"tpnb\"]].drop_duplicates()\n", "clipstrip_flag[\"clipstrip_flag\"] = int(1)\n", "backroom_flag = opsdev[opsdev.backroom_flag == 1][[\"store\", \"tpnb\"]].drop_duplicates()\n", "backroom_flag[\"backroom_flag\"] = int(1)\n", "\n", "opsdev = opsdev[\n", "    [\n", "        \"country\",\n", "        \"store\",\n", "        # \"level4\",\n", "        \"tpnb\",\n", "        \"product_name\",\n", "        \"srp\",\n", "        \"nsrp\",\n", "        \"mu\",\n", "        \"full_pallet\",\n", "        \"split_pallet\",\n", "        \"icream_nsrp\"\n", "\n", "    ]\n", "]\n", "opsdev = opsdev.merge(checkout_stand, on=[\"store\", \"tpnb\"], how=\"left\")\n", "opsdev[\"checkout_stand_flag\"].replace(np.nan, 0, inplace=True)\n", "opsdev = opsdev.merge(clipstrip_flag, on=[\"store\", \"tpnb\"], how=\"left\")\n", "opsdev[\"clipstrip_flag\"].replace(np.nan, 0, inplace=True)\n", "opsdev = opsdev.merge(backroom_flag, on=[\"store\", \"tpnb\"], how=\"left\")\n", "opsdev[\"backroom_flag\"].replace(np.nan, 0, inplace=True)\n", "\n", "# get final opsdev looking\n", "\n", "\n", "opsdev = opsdev.drop_duplicates()\n", "\n", "# filter duplicates and form the dataframe again\n", "\n", "grouped_opsdev = (\n", "    opsdev.groupby([\"country\",\n", "                    \"store\",\n", "                    # \"level4\", \n", "                    \"tpnb\",\n", "                    \"product_name\", \n", "                    ], observed = True)\n", "    .agg(\n", "        {\n", "            \"srp\": \"sum\",\n", "            \"nsrp\": \"sum\",\n", "            \"mu\": \"sum\",\n", "            \"full_pallet\": \"sum\",\n", "            \"split_pallet\": \"sum\",\n", "            \"icream_nsrp\": \"sum\",\n", "            \"checkout_stand_flag\": \"sum\",\n", "            \"clipstrip_flag\": \"sum\",\n", "            \"backroom_flag\": \"sum\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "grouped_opsdev[\"total\"] = (\n", "    grouped_opsdev.srp\n", "    + grouped_opsdev.nsrp\n", "    + grouped_opsdev.mu\n", "    + grouped_opsdev.full_pallet\n", "    + grouped_opsdev.split_pallet\n", ")\n", "grouped_opsdev[\"full_pallet\"] = np.where(\n", "    (grouped_opsdev.full_pallet == 1) & (grouped_opsdev.total > 1),\n", "    1,\n", "    grouped_opsdev.full_pallet,\n", ")\n", "grouped_opsdev[\"mu\"] = np.where(\n", "    (grouped_opsdev.total > 1) & (grouped_opsdev.full_pallet == 1), 0, grouped_opsdev.mu\n", ")\n", "grouped_opsdev[\"split_pallet\"] = np.where(\n", "    (grouped_opsdev.total > 1) & (grouped_opsdev.full_pallet == 1)\n", "    | (grouped_opsdev.mu == 1),\n", "    0,\n", "    grouped_opsdev.split_pallet,\n", ")\n", "grouped_opsdev[\"nsrp\"] = np.where(\n", "    (grouped_opsdev.total > 1)\n", "    & (\n", "        (grouped_opsdev.full_pallet == 1)\n", "        | (grouped_opsdev.mu == 1)\n", "        | grouped_opsdev.split_pallet\n", "        == 1\n", "    )\n", "    | (grouped_opsdev.srp == 1)\n", "    | (grouped_opsdev.icream_nsrp == 1),\n", "    0,\n", "    grouped_opsdev.nsrp,\n", ")\n", "grouped_opsdev[\"srp\"] = np.where(\n", "    (grouped_opsdev.total > 1)\n", "    & (\n", "        (grouped_opsdev.full_pallet == 1)\n", "        | (grouped_opsdev.mu == 1)\n", "        | grouped_opsdev.split_pallet\n", "        == 1\n", "    ),\n", "    0,\n", "    grouped_opsdev.srp,\n", ")\n", "\n", "grouped_opsdev[\"icream_nsrp\"] = np.where(\n", "    (grouped_opsdev.total > 1)\n", "    & (\n", "        (grouped_opsdev.full_pallet == 1)\n", "        | (grouped_opsdev.mu == 1)\n", "        | (grouped_opsdev.split_pallet == 1)\n", "        | (grouped_opsdev.srp == 1)\n", "    ),\n", "    0,\n", "    grouped_opsdev.icream_nsrp,\n", ")\n", "\n", "\n", "\n", "grouped_opsdev[\"total\"] = (\n", "    grouped_opsdev.srp\n", "    + grouped_opsdev.nsrp\n", "    + grouped_opsdev.mu\n", "    + grouped_opsdev.full_pallet\n", "    + grouped_opsdev.split_pallet\n", "    + grouped_opsdev.icream_nsrp\n", ")\n", "\n", "\n", "# check where is duplicates\n", "dupl_rows = grouped_opsdev[grouped_opsdev.total > 1].shape[0]\n", "print(f\"We have: {dupl_rows} rows duplicated\")\n", "\n", "\n", "# final dataframe\n", "grouped_opsdev.drop(columns={\"total\"}, inplace=True)\n", "opsdev = grouped_opsdev.copy()\n", "opsdev[\"checkout_stand_flag\"] = np.where(opsdev[\"checkout_stand_flag\"] > 0, 1, 0)\n", "opsdev[\"clipstrip_flag\"] = np.where(opsdev[\"clipstrip_flag\"] > 0, 1, 0)\n", "opsdev[\"backroom_flag\"] = np.where(opsdev[\"backroom_flag\"] > 0, 1, 0)\n", "\n", "\n", "# foil_calc.rename(columns={\"Country\": \"country\"}, inplace=True)\n", "\n", "\n", "\n", "# opsdev = opsdev.merge(opsdev_foil, on=[\"store\", \"tpnb\"], how=\"left\")\n", "# opsdev[\"foil\"] = opsdev[\"foil\"].fillna(1)\n", "# opsdev = opsdev.drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "cb3b7199-a128-4e34-a057-892d88615951", "metadata": {}, "outputs": [], "source": ["opsdev.columns = (opsdev.iloc[:, :4].columns.tolist()\n", "                  + opsdev.iloc[:, 4:10].add_suffix(\"_new\").columns.tolist()\n", "                  + opsdev.iloc[:,10:].columns.tolist())\n", "\n", "opsdev.drop(\"product_name\", axis=1, inplace=True)\n", "\n", "modelDataSet_as_is= pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\as_is_modelDataSet_updated_12-06_op_type_frozen_srp\")\n", "modelDataSet_as_is_ = modelDataSet_as_is.merge(opsdev[opsdev.iloc[:,:9].columns.tolist()], on=['country',\n", "                                                                                              'store', \n", "                                                                                              'tpnb'], how='left')\n", "modelDataSet_as_is_.rename(columns={'frozen_srp':'icream_nsrp'},inplace=True)\n", "\n", "for x, y in zip([\"srp_new\", \"nsrp_new\", \"mu_new\", \"full_pallet_new\", \"split_pallet_new\", \"icream_nsrp_new\"], ['srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', \"icream_nsrp\"]):\n", "    cond = [modelDataSet_as_is_[x].notnull()]\n", "    result = [modelDataSet_as_is_[x]]\n", "    modelDataSet_as_is_[y] = np.select(cond, result, modelDataSet_as_is_[y])\n", "    modelDataSet_as_is_.drop(x, axis=1, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e424785b-dd32-4655-a2d1-b6266e344e23", "metadata": {}, "outputs": [], "source": ["modelDataSet_as_is_.to_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\as_is_modelDataSet_updated_12-06_op_type_frozen_srp_test_new_method_\",compression=\"gzip\")"]}, {"cell_type": "code", "execution_count": null, "id": "080cc7c2-b197-4b17-8128-a0257a5778c5", "metadata": {}, "outputs": [], "source": ["del modelDataSet_as_is_"]}, {"cell_type": "code", "execution_count": null, "id": "f6dcba65-6c38-4605-82a3-484650c50a3c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1a6d7fe3-4d76-49de-92f7-19572e098e5a", "metadata": {}, "outputs": [], "source": ["opsdev.head()"]}, {"cell_type": "code", "execution_count": null, "id": "8be6a1f4-d4e6-4ea1-a153-16969d3d97ee", "metadata": {}, "outputs": [], "source": ["grouped_opsdev.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b4f144bf-9c4a-482e-ae6f-af15efe71391", "metadata": {}, "outputs": [], "source": ["grouped_opsdev.drop_duplicates().shape"]}, {"cell_type": "code", "execution_count": null, "id": "812267aa-f0f1-4ce1-a004-636ab6e65cbc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "89e84b9a-9a7a-43cc-bef7-9d5a0dfa9ce4", "metadata": {}, "outputs": [], "source": ["grouped_opsdev[(grouped_opsdev.tpnb ==100380270 )&(grouped_opsdev.store == 21003)].head()"]}, {"cell_type": "code", "execution_count": null, "id": "4b55d6f7-c1f7-4e2d-9e9e-34d77553a7bf", "metadata": {}, "outputs": [], "source": ["opsdev[(opsdev.Product_id == 100380270)&(opsdev.Store_Number == '21003')].head()"]}, {"cell_type": "code", "execution_count": null, "id": "c69a7647-8e18-4b07-8ef7-8b6677d2c52c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}