import pandas as pd
import numpy as np
import xlwings as xw




a = pd.read_csv(r"c:\Users\<USER>\Downloads\hadoopce-uk-prod_run_7_stmt_1_0.csv", sep = ',')

columns_to_concat = ['div_code_rep', 'dep_code_rep', 'sec_code_rep', 'grp_code_rep', 'sgr_code_rep']
a['5level'] = a[columns_to_concat].fillna(0).astype(int).astype(str).sum(axis=1)

a["opc"] = a['tpnlocal'] + a['tpnlocalback']
a["5level"] = a["5level"].astype("int64")



b = pd.read_excel(r"\\huprgvmfs03.hu.tesco-europe.com\CE-Productivity-Team\Volume Data\Hierarchy\CE_hierarchy.xlsx", usecols=['5level','PMG'], sheet_name = "CE")





c = a.merge(b, on=["5level"], how = "left")

d = c[(c.opc > 0) & (c.PMG.isna())]

d.drop("PMG", axis=1, inplace=True)






c.loc[c['PMG'].isna(), 'PMG'] = c.loc[c['PMG'].isna(), '5level'].map(e.set_index('5level')['PMG'])

c = c[c.opc > 0]



from datetime import datetime, timedelta
import pandas as pd

# Convert day column to datetime
c['day'] = pd.to_datetime(c['day'])

# Set up reference points
reference_date = datetime(2025, 5, 12)
reference_week = 12
week_1_start = reference_date - timedelta(weeks=reference_week - 1)

# Vectorized calculation (faster for large datasets)
days_diff = (c['day'] - week_1_start).dt.days
week_nums = (days_diff // 7) + 1
c['financial_week'] = 'w' + week_nums.astype(str).str.zfill(2)





country_mapping = {1: 'CZ', 2: 'SK', 4: 'HU'}
c['country'] = c['cntr_id'].map(country_mapping)