"""
BackStock activities
"""




bcs_tasks = [
    "Walk, get and place manual pallet truck - Backstock Pallets & Rollcages",
    "Selecting Stock in WH - Get and switch places of rollcages - backstock rollcages",
    "Release manual pallet truck and bring back to WH - Backstock Pallets & Rollcages",
    "Get rollcage - Backstock Rollcages",
    "Open & Close WH Chiller Door - Backstock Rollcages",
    "Open & Close WH Chiller Door - Backstock Pallets",
    "Stock Movement - Walk through WH door with RC (for backstock 2nd time)",
    "Stock Movement - Walk through WH door with pallet (for backstock 2nd time)",
    "Move backstock rollcage (from SF to WH - first time)",
    "Move backstock shelf trolley (from SF to WH - first time)",
    "Move backstock pallet (from SF to WH - first time)",
    "Move backstock rollcage (from WH to SF)",
    "Move backstock shelf trolley (from WH to SF)",
    "Move backstock rollcage (from SF to WH - second time)",
    "Move backstock shelf trolley (from SF to WH - second time)",
    "Move backstock pallet (from WH to SF)",
    "Move backstock pallet (from SF to WH - second time)",
    "Move rollcage from SF to Secondary Displays",
    "Move shelf trolley from SF to Secondary Displays",
    "Move rollcage from Secondary Displays to SF",
    "Move shelf trolley from Secondary Displays to SF",
    "Move pallet from SF to Secondary Displays",
    "Move pallet from Secondary Displays to SF",
    "Additional rollcage movements during Replenishment",
    "Additional rollcage movements during Replenishment - backstock",
    "Backstock grocery - pushing PBL rollcage through the isle",
    "Backstock grocery - grab the rollcage while moving through the isle",
    "Backstock grocery - walking with the cases from rollcage to TPN location",
    "Replenish Backstock - Additional Activities",
    "Get rollcage into post-sort area",
    "Transfer light case to cage",
    "Transfer heavy case to cage",
    "Mark THU - last repl, name GA",
    "Get the post-sorted RC and move it back",
    "Get the post-sorted pallet and move it back"
]