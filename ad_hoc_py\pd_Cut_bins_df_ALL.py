import pandas as pd
import numpy as np
import warnings

warnings.filterwarnings("ignore")

# Create a sample DataFrame
df = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_07\Important_drivers_profiles_2023_all.xlsx")

df_repl = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_07\main_drivers_profiles_Repl_WH_CE.xlsx", sheet_name='Repl')
df_warehouse = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_07\main_drivers_profiles_Repl_WH_CE.xlsx", sheet_name='Warehouse')


def for_repl_and_wh(df):
    
    df= df.melt(df.iloc[:,:4].columns, var_name='driver_profile')
    df.rename(columns={'Division':'Area'}, inplace=True)
    
    return df

repl_wh = pd.concat([for_repl_and_wh(df_repl), for_repl_and_wh(df_warehouse)])

repl_wh.drop(['Country', 'Format'], axis=1, inplace=True)

repl_wh = repl_wh.merge(df[['Store Number', 'Store Name', 'COUNTRY']].drop_duplicates(), on=['Store Number'], how='left')
    
    

df.melt(df.iloc[:,:4].columns)

# Define the number of bins/groups
num_groups = 5

# Define alphabet labels for the groups
alphabet_labels = ['E', 'D', 'C', 'B', 'A']




split_columns = [col.split('.') for col in df.columns]

# Extract the parts before the dot
new_column_parts = [col[0] for col in split_columns]

# Combine the parts before the dot with the second row
new_column_names = [new_col + '.' + str(val) for new_col, val in zip(new_column_parts, df.iloc[0])]

new_column_names_wo_nan = [ x for x in new_column_names if not ".nan" in x ]

df.columns = new_column_names

df = df[new_column_names_wo_nan].iloc[1:,:]

df = df.melt(df.iloc[:,:3].columns.tolist())

df[['Area', 'driver_profile', ]] = df['variable'].str.split('.', n=1,  expand=True)

split_columns_first_3_columns = [col.split('.')[1] for col in df.columns[:3]]

columns_dict = dict(zip(df.columns[:3], split_columns_first_3_columns))
df = df.rename(columns=columns_dict)
df.drop("variable", axis=1, inplace=True)

df.value = pd.to_numeric(df.value, errors="coerce")

df = df[df.value.notnull()]

df = df[(~df.Area.isin(['Fresh', 'GM', 'Grocery', 'Produce', 'Warehouse']))
        & (~df.driver_profile.isin(['BAKERY OPERATION'])) ]

# df = df.pivot_table(index=df.iloc[:,:3].columns.tolist() + ['Area'], columns='driver_profile', values='value' , fill_value=0).reset_index()

df = pd.concat([df, repl_wh])

# df = df[(df.Area == 'Front End') & (df.driver_profile == 'OPENING HOURS')]


try:

    ce = pd.DataFrame()    
    
    # for country in df.Country.unique().tolist():
    #     a_country = a.query("Country == @country")
        
    for x in df.Area.unique().tolist():
        a_div = df.query("Area == @x")
        for y in a_div.driver_profile.unique().tolist():
            b = a_div[a_div['driver_profile'] == y]
            

            mean = b['value'].mean()
            sig = b['value'].std()
            max_ = mean + 3*sig

            
            bins = np.linspace(b['value'].min(), max_ , num_groups + 1)
            bins[-1] = np.inf
            labels = alphabet_labels[:num_groups]
            b['grouping'] = pd.cut(b['value'], bins=bins, labels=labels, include_lowest=True, )
    
    
            ce = pd.concat([ce, b])
        
except: print(x, y)


ce.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_07\Important_drivers_profiles_2023_all_areas_newer.xlsx",index=False)










