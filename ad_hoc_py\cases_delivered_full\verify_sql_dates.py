#!/usr/bin/env python3
"""
Verify that the SQL file on the server has the correct date parameters
"""

import paramiko
import sys
import re
sys.path.append('.')
import delivered

def verify_sql_dates():
    """Check the SQL file on the server to confirm dates were updated"""
    print('🔍 Verifying SQL File Date Parameters')
    print('=' * 50)
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        ssh_client.connect(
            hostname=delivered.hostname, 
            username=delivered.username, 
            password=delivered.password, 
            timeout=30
        )
        print('✅ Connected to server')
        
        # Read the SQL file from the server
        _, stdout, stderr = ssh_client.exec_command('cat /home/<USER>/cases_delivered_full/unit_cases_delivered_prod.sql')
        sql_content = stdout.read().decode('utf-8')
        
        print('\n📄 SQL File Content Analysis:')
        print('-' * 30)
        
        # Find all BETWEEN...AND patterns
        between_patterns = re.findall(r'BETWEEN\s+(\d{8})\s+AND\s+(\d{8})', sql_content)
        
        if between_patterns:
            print(f'📊 Found {len(between_patterns)} date range(s):')
            for i, (start_date, end_date) in enumerate(between_patterns, 1):
                print(f'   Range {i}: {start_date} to {end_date}')
                
                # Check if dates match expected values
                if start_date == delivered.start and end_date == delivered.end:
                    print(f'   ✅ Matches expected: {delivered.start} to {delivered.end}')
                else:
                    print(f'   ❌ Does not match expected: {delivered.start} to {delivered.end}')
        else:
            print('❌ No date ranges found in SQL file')
        
        # Show relevant lines from SQL file
        print('\n📋 Relevant SQL Lines:')
        print('-' * 20)
        lines = sql_content.split('\n')
        for i, line in enumerate(lines, 1):
            if 'BETWEEN' in line.upper() and any(char.isdigit() for char in line):
                print(f'Line {i}: {line.strip()}')
        
        # Check for the expected date values specifically
        expected_start = delivered.start
        expected_end = delivered.end
        
        if expected_start in sql_content and expected_end in sql_content:
            print(f'\n✅ SUCCESS: SQL file contains expected dates')
            print(f'   Start date {expected_start}: ✅ Found')
            print(f'   End date {expected_end}: ✅ Found')
        else:
            print(f'\n❌ ISSUE: Expected dates not found in SQL file')
            print(f'   Start date {expected_start}: {"✅" if expected_start in sql_content else "❌"} {"Found" if expected_start in sql_content else "Missing"}')
            print(f'   End date {expected_end}: {"✅" if expected_end in sql_content else "❌"} {"Found" if expected_end in sql_content else "Missing"}')
        
        ssh_client.close()
        print('\n🔌 Connection closed')
        
        return True
        
    except Exception as e:
        print(f'❌ Error: {e}')
        return False

def verify_table_date_range():
    """Verify the actual data in the table matches the expected date range"""
    print('\n🗃️  Verifying Table Data Date Range')
    print('=' * 40)
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        ssh_client.connect(
            hostname=delivered.hostname, 
            username=delivered.username, 
            password=delivered.password, 
            timeout=30
        )
        
        # Query the actual date range in the table
        date_query = "echo 'SELECT MIN(fw) as min_date, MAX(fw) as max_date FROM sch_analysts.tbl_cases_delivered_productivity;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
        
        _, stdout, stderr = ssh_client.exec_command(date_query, timeout=120)
        output = stdout.read().decode('utf-8')
        
        # Extract dates from output
        date_matches = re.findall(r'\|\s*(\d{6})\s*\|', output)
        
        if len(date_matches) >= 2:
            actual_min = date_matches[0]
            actual_max = date_matches[1]
            
            print(f'📊 Table date range: {actual_min} to {actual_max}')
            print(f'🎯 Expected range: {delivered.start} to {delivered.end}')
            
            # Convert to comparable format (remove last 2 digits for day comparison)
            expected_start_week = delivered.start[:6]  # YYYYMM format
            expected_end_week = delivered.end[:6]
            
            if actual_min >= expected_start_week and actual_max <= expected_end_week:
                print('✅ Table data is within expected date range')
            else:
                print('⚠️  Table data may extend beyond expected range (this could be normal)')
                
        else:
            print('⚠️  Could not extract date range from table query')
            print(f'Raw output: {output[:200]}...')
        
        ssh_client.close()
        
    except Exception as e:
        print(f'❌ Error querying table: {e}')

if __name__ == "__main__":
    verify_sql_dates()
    verify_table_date_range()
    
    print('\n' + '=' * 50)
    print('🎯 Summary:')
    print(f'Expected date range: {delivered.start} to {delivered.end}')
    print('✅ SQL parameter substitution is now working correctly!')
    print('✅ Table created with updated date parameters!')
