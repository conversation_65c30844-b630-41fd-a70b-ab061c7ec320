#!/usr/bin/env python3
"""
Verification script for the cases_delivered table
"""

import paramiko
import sys
import re
sys.path.append('.')
import delivered

def verify_table():
    """Verify the created table and get detailed information"""
    print('🔍 Verifying table creation and getting detailed information...')
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        ssh_client.connect(
            hostname=delivered.hostname, 
            username=delivered.username, 
            password=delivered.password, 
            timeout=30
        )
        print('✅ Connected to server')
        
        # Get row count
        cmd = "echo 'SELECT COUNT(*) FROM sch_analysts.tbl_cases_delivered_productivity;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
        
        _, stdout, stderr = ssh_client.exec_command(cmd, timeout=120)
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        
        print('📊 Row count query output:')
        print(output)
        if error:
            print('❌ Error output:')
            print(error)
        
        # Extract row count
        numbers = re.findall(r'\b\d{1,}\b', output)
        if numbers:
            # Find the largest number (likely the row count)
            row_counts = [int(n) for n in numbers if len(n) >= 1]
            if row_counts:
                max_count = max(row_counts)
                print(f'📈 Estimated row count: {max_count:,}')
        
        # Get table schema
        schema_cmd = "echo 'DESCRIBE sch_analysts.tbl_cases_delivered_productivity;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
        
        _, stdout, stderr = ssh_client.exec_command(schema_cmd, timeout=60)
        schema_output = stdout.read().decode('utf-8')
        
        print('📋 Table schema:')
        print(schema_output)
        
        # Get sample data
        sample_cmd = "echo 'SELECT * FROM sch_analysts.tbl_cases_delivered_productivity LIMIT 5;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
        
        _, stdout, stderr = ssh_client.exec_command(sample_cmd, timeout=60)
        sample_output = stdout.read().decode('utf-8')
        
        print('📄 Sample data (first 5 rows):')
        print(sample_output)
        
        ssh_client.close()
        print('🔌 Connection closed')
        
        return True
        
    except Exception as e:
        print(f'❌ Error during verification: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_table()
