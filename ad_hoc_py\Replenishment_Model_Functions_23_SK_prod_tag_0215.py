import pandas as pd
import numpy as np
from pathlib import Path
import pyarrow.parquet as pq
import time
from functools import wraps
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from tqdm import tqdm


# for end of the Calculation
def CurrentTime():
    now = datetime.now()
    current_time = now.strftime("%H:%M:%S")
    h = int(current_time[0:2])  # time format: '11:53:12'
    m = int(current_time[3:5])
    s = int(current_time[6:8])
    sec_time = h * 3600 + m * 60 + s
    return sec_time


# Python decorator to measure execution time of Functions
def timeit(func):
    @wraps(func)
    def timeit_wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        total_time = end_time - start_time
        if func.__name__ == "Replenishment_Model_Running":
            print(
                f" \n{func.__name__} is done! Elapsed time (sec & min): {total_time:.2f} sec which is {total_time/60:.2f} min"
            )
        else:
            print(f" \n{func.__name__} is done! Elapsed time (sec): {total_time:.2f}")
        return result

    return timeit_wrapper


def optimize_types(dataframe):
    np_types = [
        np.int8,
        np.int16,
        np.int32,
        np.int64,
        np.uint8,
        np.uint16,
        np.uint32,
        np.uint64,
        np.float32,
        np.float64,
    ]  # , np.float16, np.float32, np.float64
    np_types = [np_type.__name__ for np_type in np_types]
    type_df = pd.DataFrame(data=np_types, columns=["class_type"])

    type_df["min_value"] = type_df[type_df["class_type"].str.contains("int")][
        "class_type"
    ].apply(lambda row: np.iinfo(row).min)
    type_df["max_value"] = type_df[type_df["class_type"].str.contains("int")][
        "class_type"
    ].apply(lambda row: np.iinfo(row).max)
    type_df["min_value_f"] = type_df[type_df["class_type"].str.contains("float")][
        "class_type"
    ].apply(lambda row: np.finfo(row).min)
    type_df["max_value_f"] = type_df[type_df["class_type"].str.contains("float")][
        "class_type"
    ].apply(lambda row: np.finfo(row).max)
    type_df["min_value"] = np.where(
        type_df["min_value"].isna(), type_df["min_value_f"], type_df["min_value"]
    )
    type_df["max_value"] = np.where(
        type_df["max_value"].isna(), type_df["max_value_f"], type_df["max_value"]
    )
    type_df.drop(columns=["min_value_f", "max_value_f"], inplace=True)

    type_df["range"] = type_df["max_value"] - type_df["min_value"]
    type_df.sort_values(by="range", inplace=True)
    try:
        for col in dataframe.loc[:, dataframe.dtypes == np.integer]:
            col_min = dataframe[col].min()
            col_max = dataframe[col].max()
            temp = type_df[
                (type_df["min_value"] <= col_min) & (type_df["max_value"] >= col_max)
            ]
            optimized_class = temp.loc[temp["range"].idxmin(), "class_type"]
            # print("Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}".format(col, col_min, col_max, optimized_class))
            dataframe[col] = dataframe[col].astype(optimized_class)
    except ValueError:
        pass
    try:
        for col in dataframe.loc[:, (dataframe.dtypes == np.floating)]:
            col_min = dataframe[col].min()
            col_max = dataframe[col].max()
            type_df = type_df[
                type_df["class_type"].astype("string").str.contains("float")
            ]
            temp = type_df[
                (type_df["min_value"] <= col_min) & (type_df["max_value"] >= col_max)
            ]
            optimized_class = temp.loc[temp["range"].idxmin(), "class_type"]
            # print("Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}".format(col, col_min, col_max, optimized_class))
            dataframe[col] = dataframe[col].astype(optimized_class)
    except ValueError:
        pass
    return dataframe


def optimize_floats(df: pd.DataFrame) -> pd.DataFrame:
    floats = df.select_dtypes(include=["float64"]).columns.tolist()
    df[floats] = df[floats].apply(pd.to_numeric, downcast="float")
    return df


def optimize_ints(df: pd.DataFrame) -> pd.DataFrame:
    ints = df.select_dtypes(include=["int64"]).columns.tolist()
    df[ints] = df[ints].apply(pd.to_numeric, downcast="integer")
    return df


def optimize_objects(df: pd.DataFrame):
    try:
        for col in df.select_dtypes(include=["object"]):
            if not (type(df[col][0]) == list):
                num_unique_values = len(df[col].unique())
                num_total_values = len(df[col])
                if float(num_unique_values) / num_total_values < 0.5:
                    df[col] = df[col].astype("category")
    except IndexError:
        pass
    return df


def optimize(df: pd.DataFrame):
    return optimize_floats(optimize_ints(optimize_objects(df)))


@timeit
def Store_Inputs_Creator(directory, excel_inputs_f, stores):

    # Store inputs

    pmg_df = pd.read_excel(directory / excel_inputs_f, "pmg")
    pmg_df = pmg_df[pmg_df.Area == "Replenishment"]
    store_list_df = pd.read_excel(directory / excel_inputs_f, "store_list")
    store_list_df = store_list_df.loc[store_list_df.Store.isin(stores)]
    capping_df = pd.read_excel(directory / excel_inputs_f, "capping")
    capping_df["is_capping_shelf"] = 1
    Dprofiles_df = pd.read_excel(directory / excel_inputs_f, "Dprofiles")
    Pprofiles_df = pd.read_excel(directory / excel_inputs_f, "Pprofiles")
    pmg_array = pmg_df.values
    store_array = store_list_df.values
    result = len(store_array) * len(pmg_array)  #
    df_array = np.empty([result, 9], dtype="object")  # create an empty array
    counter = 0
    for s in range(len(store_array)):
        for p in range(len(pmg_array)):
            df_array[counter][0] = store_array[s][1]  # country
            df_array[counter][1] = store_array[s][0]  # store
            df_array[counter][2] = store_array[s][2]  # store_name
            df_array[counter][3] = store_array[s][3]  # sqm
            df_array[counter][4] = store_array[s][4]  # format
            df_array[counter][5] = pmg_array[p][0]  # pmg
            df_array[counter][6] = pmg_array[p][1]  # pmg_name
            df_array[counter][7] = pmg_array[p][2]  # dep
            df_array[counter][8] = pmg_array[p][3]  # division
            counter += 1
    store_inputs = pd.DataFrame(
        columns=[
            "Country",
            "Store",
            "Store Name",
            "Plan Size",
            "Format",
            "Pmg",
            "Pmg Name",
            "Dep",
            "Division",
        ]
    )
    store_inputs = pd.concat(
        [store_inputs, pd.DataFrame(df_array, columns=store_inputs.columns)]
    )
    store_inputs["Dep"] = np.where(store_inputs.Pmg == "HDL01", "NEW", store_inputs.Dep)
    store_inputs = store_inputs.merge(capping_df, on=["Store", "Pmg"], how="left")
    store_inputs["is_capping_shelf"] = store_inputs["is_capping_shelf"].replace(
        np.nan, 0
    )
    store_inputs = store_inputs.merge(Dprofiles_df, on=["Store", "Dep"], how="left")
    store_inputs = store_inputs.merge(
        Pprofiles_df, on=["Country", "Format", "Pmg"], how="left"
    )
    store_inputs["Store"] = store_inputs["Store"].astype("int64")
    # store_inputs.columns = [i.lower() for i in store_inputs.columns]
    return store_inputs


@timeit
def Repl_DataSet_Creator(
    store_inputs,
    directory,
    planogram_f,
    stock_f,
    ops_dev_f,
    items_sold_f,
    items_sold_dotcom,
    cases_f,
    box_op_type_f,
    pallet_capacity_f,
):

    print("Repl_Dataset Build: has been started to calculate...")

    # Creating Base for Repl_Dataset
    weekdays = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
    ]
    store_inputs_lower = store_inputs.copy()
    store_inputs_lower.columns = [i.lower() for i in store_inputs_lower.columns]
    store_inputs_lower = store_inputs_lower.rename(columns={"pmg name": "pmg_name"})

    store_inputs_lower = optimize_objects(
        optimize_types(
            store_inputs_lower[
                ["store", "format", "country", "division", "pmg", "is_capping_shelf"]
            ].drop_duplicates()
        )
    )
    # store_inputs_lower = dd.from_pandas(store_inputs_lower, npartitions = 1)
    planogram = optimize_objects(
        optimize_types(pd.read_csv(directory / planogram_f, sep=",",))
    )
    planogram = planogram[["store", "tpnb", "icase", "capacity"]]
    planogram = planogram.drop_duplicates(subset=["tpnb", "icase", "store", "capacity"])
    opsdev = optimize_objects(
        optimize_types(pq.read_table(directory / ops_dev_f).to_pandas())
    )
    opsdev = opsdev[
        [
            "store",
            "tpnb",
            "srp",
            "nsrp",
            "mu",
            "full_pallet",
            "split_pallet",
            "checkout_stand_flag",
            "clipstrip_flag",
            "backroom_flag",
        ]
    ]
    stock = optimize_objects(
        optimize_types(pq.read_table(directory / stock_f).to_pandas())
    )
    stock = stock[["store", "day", "tpnb", "stock", "item_price"]]
    isold = optimize_objects(
        optimize_types(pq.read_table(directory / items_sold_f).to_pandas())
    )
    # isold = isold.sort_values(['consigment_type'], ascending = False).drop_duplicates(subset=['store', 'tpnb', 'day'], keep = "first")
    isold_dotcom = optimize_objects(
        optimize_types(pq.read_table(directory / items_sold_dotcom).to_pandas())
    )
    isold_dotcom = isold_dotcom[["store", "day", "tpnb", "sold_units_dotcom"]]
    cases = optimize_objects(
        optimize_types(pq.read_table(directory / cases_f).to_pandas())
    )
    cases = cases[["store", "day", "tpnb", "unit", "artgld_case_capacity"]]
    cases["cases_delivered"] = cases["unit"] / cases["artgld_case_capacity"]
    op_type = optimize_objects(optimize_types(pd.read_excel(directory / box_op_type_f)))
    # op_type = dd.from_pandas(op_type, 1)
    pallet_cap = optimize_objects(
        optimize_types(pq.read_table(directory / pallet_capacity_f).to_pandas())
    )

    print("Repl_Dataset Build: Inputs are loaded into Memory!")

    Repl_Dataset = isold[["store", "pmg", "tpnb"]].drop_duplicates()
    # Repl_Dataset = Repl_Dataset.loc[Repl_Dataset.store.isin(list(store_inputs_lower.store.unique())) ]
    Repl_Dataset["day"] = ""
    Repl_Dataset["day"] = Repl_Dataset["day"].apply(lambda x: weekdays)
    Repl_Dataset = Repl_Dataset.explode("day").drop_duplicates()
    Repl_Dataset = Repl_Dataset.merge(
        isold[["store", "pmg", "tpnb", "day", "sold_units", "sales_excl_vat"]],
        on=["store", "pmg", "tpnb", "day"],
        how="left",
    )
    Repl_Dataset = Repl_Dataset.merge(
        isold[
            isold.columns[~isold.columns.isin(["sold_units", "sales_excl_vat", "day"])]
        ].drop_duplicates(),
        on=["store", "pmg", "tpnb"],
        how="left",
    )
    Repl_Dataset["pmg"] = np.where(
        Repl_Dataset["pmg"] == "DRY18", "DRY15", Repl_Dataset["pmg"]
    )  # we dont use DRY18 anymore we should use DRY15 ('Crisps Nuts and Snacks') instead
    Repl_Dataset = Repl_Dataset.merge(
        store_inputs_lower, on=["store", "pmg"], how="left"
    )
    Repl_Dataset = Repl_Dataset.merge(
        isold_dotcom, on=["store", "day", "tpnb"], how="left"
    )
    Repl_Dataset = Repl_Dataset.merge(stock, on=["store", "day", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.merge(opsdev, on=["tpnb", "store"], how="left")
    Repl_Dataset = Repl_Dataset.merge(planogram, on=["store", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.merge(cases, on=["store", "day", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.merge(op_type, on=["country", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.merge(pallet_cap, on=["country", "tpnb"], how="left")

    Repl_Dataset.replace(np.nan, 0, inplace=True)
    Repl_Dataset["dep"] = Repl_Dataset.pmg.str[:3].astype("category")
    Repl_Dataset["day"] = Repl_Dataset["day"].astype("category")

    int_list = [
        "srp",
        "nsrp",
        "mu",
        "full_pallet",
        "split_pallet",
        "checkout_stand_flag",
        "backroom_flag",
        "clipstrip_flag",
        "case_capacity",
        "Perforated box",
        "Shrink",
        "Tray",
        "Tray + Hood",
        "Tray + Shrink",
        "is_capping_shelf",
        "capacity",
        "icase",
    ]  #
    Repl_Dataset[int_list] = Repl_Dataset[int_list].apply(lambda x: x.astype("int32"))
    int_part = optimize_types(Repl_Dataset.select_dtypes(include=["float", "int"]))
    cat_part = Repl_Dataset.select_dtypes(exclude=["float", "int"])
    Repl_Dataset = pd.concat([int_part, cat_part], axis=1)

    Repl_Dataset["nsrp"] = np.where(
        (Repl_Dataset.srp == 0)
        & (Repl_Dataset.nsrp == 0)
        & (Repl_Dataset.full_pallet == 0)
        & (Repl_Dataset.mu == 0)
        & (Repl_Dataset.split_pallet == 0),
        1,
        Repl_Dataset["nsrp"],
    )
    Repl_Dataset["icase"] = np.where(
        Repl_Dataset.icase == 0, Repl_Dataset.case_capacity, Repl_Dataset["icase"]
    )
    Repl_Dataset.drop(["case_capacity"], axis=1, inplace=True)
    Repl_Dataset = Repl_Dataset.rename(columns={"icase": "case_capacity"})
    Repl_Dataset["case_capacity"] = np.where(
        Repl_Dataset.case_capacity < Repl_Dataset.artgld_case_capacity,
        Repl_Dataset.artgld_case_capacity,
        Repl_Dataset["case_capacity"],
    )
    Repl_Dataset["cases_delivered"] = (
        Repl_Dataset["unit"] / Repl_Dataset["case_capacity"]
    )
    Repl_Dataset.drop(["artgld_case_capacity"], axis=1, inplace=True)
    Repl_Dataset.replace(np.nan, 0, inplace=True)
    Repl_Dataset["unit_type"] = np.where(
        Repl_Dataset.unit_type != "KG", "SNGL", Repl_Dataset["unit_type"]
    )
    Repl_Dataset["pallet_capacity"] = round(Repl_Dataset["pallet_capacity"], 0)
    # Repl_Dataset.clipstrip_flag = np.where((Repl_Dataset.product_name.str.contains("HELL") | Repl_Dataset.product_name.str.contains("XIXO")), 0,  Repl_Dataset.clipstrip_flag)
    Repl_Dataset = optimize_objects(Repl_Dataset)
    print("Repl_Dataset Build: Done!!")

    return Repl_Dataset


def Broken_Case_Creator(
    directory, df, how_many_HU, how_many_CZ, how_many_SK, saved_name
):

    dry_optional = ["DRY23", "DRY25", "DRY29", "DRY30", "DRY31", "DRY32", "DRY33"]
    broken_stores_hu = [
        41710,
        41700,
        41400,
        41730,
        41790,
        41006,
        41005,
        41670,
        41440,
        41560,
        41390,
        41760,
        41600,
        41015,
    ]
    broken_stores_cz = [11011, 11013, 11033]
    broken_stores_sk = [21004, 21014, 21019, 21002]

    CE_broken_list = broken_stores_hu + broken_stores_cz + broken_stores_sk

    no_need_dry = df[(df.dep == "DRY") & (~df.pmg.isin(dry_optional))]["pmg"].unique()

    single_pick_df = df.loc[
        (df.store.isin(CE_broken_list))
        & (df.division.isin(["GM", "Grocery"]))
        & (df.pmg != "HDL01")
        & (~df.pmg.isin(no_need_dry))
        & (df.full_pallet == 0)
        & (df.mu == 0)
        & (df.cases_delivered >= 0)
        & (df.day == "Thursday")
    ][["country", "store", "tpnb", "day", "pmg", "dep", "division"]]
    single_pick_df["helper_single_pick"] = np.random.uniform(
        0, 1, single_pick_df.shape[0]
    )
    single_pick_df = single_pick_df.sort_values(
        by=["store", "helper_single_pick"], ascending=[True, False]
    )

    def broken_case_by_country(stores, how_many_tpn):
        a = pd.DataFrame()
        for s in stores:
            df_inner = single_pick_df[single_pick_df.store == s][:how_many_tpn]
            a = pd.concat([a, df_inner])

        a["broken_case_flag"] = 1
        a["broken_case_flag"] = a["broken_case_flag"].astype("int8")
        a["day"] = "Thursday"
        a = a[["store", "tpnb", "day", "broken_case_flag"]]

        return a

    hu = broken_case_by_country(broken_stores_hu, how_many_HU)
    sk = broken_case_by_country(broken_stores_sk, how_many_CZ)
    cz = broken_case_by_country(broken_stores_cz, how_many_SK)

    ce = pd.concat([hu, sk, cz])
    ce.to_csv(
        directory / f"inputs/files_for_dataset/broken_cases_list_{saved_name}.csv.gz",
        index=False,
        compression="gzip",
    )

    df = df.merge(ce, on=["store", "day", "tpnb"], how="left").replace(np.nan, 0)

    print("Broken Cases are calculated and added to Repl_Dataset!")

    return df


def Repl_Drivers_Calculation(
    directory,
    Repl_Dataset,
    store_inputs,
    backstock_target,
    RC_Capacity_Ratio,
    shelf_trolley_cap_ratio_to_pallet,
    shelf_trolley_cap_ratio_to_rollcage,
    excel_inputs_f,
    MODULE_CRATES,
    TABLE_CRATES,
    FULFILL_TARGET,
    SALES_CYCLE,
    RC_CAPACITY,
    RC_DELIVERY,
    RC_VS_PAL_CAPACITY,
    only_tpn,
    tpnb_store,
    tpnb_country,
    selected_tpn,
    capping_shelves_ratio,
    stores,
):

    Drivers = Repl_Dataset.copy()
    del Repl_Dataset

    Drivers = Drivers[Drivers.store.isin(stores)]

    Drivers.drop(
        ["ownbrand", "checkout_stand_flag"], axis=1, inplace=True
    )  #'product_name',

    # foil
    foil = pd.read_excel(
        directory / "inputs/Repl/foil_tpnb.xlsx", usecols=["country", "tpnb", "foil"]
    )
    Drivers = Drivers.merge(foil, on=["country", "tpnb"], how="left")
    Drivers["foil"].replace(np.nan, 1, inplace=True)

    Drivers_produce = Drivers.loc[
        (Drivers.dep == "PRO") & (Drivers.pmg != "PRO16") & (Drivers.pmg != "PRO19")
    ]
    pro_to_del = Drivers.loc[
        (Drivers.dep == "PRO") & (Drivers.pmg != "PRO16") & (Drivers.pmg != "PRO19")
    ]["pmg"].unique()
    Drivers = Drivers.loc[(~Drivers.pmg.isin(pro_to_del)) & (Drivers.pmg != "HDL01")]

    if len(Drivers) > 0:

        ###### shelf capacity customization ########

        avg_shelf_cap = (
            Drivers.loc[Drivers.capacity > 1]
            .groupby(["store", "pmg"], as_index=False, observed=True)
            .agg(capacity_avg_pmg=("capacity", "mean"))
        )
        Drivers = Drivers.merge(avg_shelf_cap, on=["store", "pmg"], how="left")
        Drivers = Drivers.replace(np.nan, 0)
        Drivers["capacity"] = np.where(
            (Drivers.capacity == 0) | (Drivers.capacity == 1),
            Drivers["capacity_avg_pmg"],
            Drivers["capacity"],
        )

        Drivers["stock"] = np.where(
            ((Drivers.stock == 0) & (Drivers.sold_units > 0)),
            Drivers.sold_units,
            Drivers.stock,
        )  # If we have sales > 0, then stock cannot be equal 0, because: one touch + two touch + capping <> stock

        ###### case capacity customization ########
        Drivers["case_capacity"] = np.where(
            (Drivers.case_capacity == 0), 3, Drivers["case_capacity"]
        )
        Drivers["case_capacity"] = np.where(
            (Drivers.pmg.isin(["HDL06", "HDL07"])) & (Drivers["case_capacity"] < 10),
            10,
            Drivers["case_capacity"],
        )
        Drivers["case_capacity"] = np.where(
            (Drivers.pmg.isin(["HDL55", "HDL35"])) & (Drivers["case_capacity"] < 3),
            3,
            Drivers["case_capacity"],
        )

        ###### pallet capacity customization ########
        avg_pallet_cap = (
            Drivers[(Drivers.pallet_capacity > 5)]
            .groupby(["store", "pmg"], as_index=False, observed=True)
            .agg(pallet_capacity_avg_pmg=("pallet_capacity", "mean"))
            .replace(np.nan, 5)
        )
        Drivers = Drivers.merge(
            avg_pallet_cap, on=["store", "pmg"], how="left"
        ).replace(np.nan, 0)
        Drivers["pallet_capacity"] = np.where(
            (Drivers.dep == "HDL")
            & (Drivers.pallet_capacity >= 0)
            & (Drivers.pallet_capacity <= 5),
            Drivers.pallet_capacity_avg_pmg,
            Drivers["pallet_capacity"],
        )
        Drivers["pallet_capacity"] = np.where(
            (Drivers.pmg.isin(["HDL06", "HDL07"])) & (Drivers["pallet_capacity"] < 100),
            150,
            Drivers["pallet_capacity"],
        )

        ###### weight customization ########
        Drivers["heavy"] = Drivers.weight * Drivers.case_capacity  # 1. Heavy & Light
        Drivers["weight_selector"] = (
            Drivers.weight * Drivers.case_capacity
        )  # 1. Heavy & Light
        Drivers["heavy"] = np.where(Drivers.weight_selector >= 5, 1, 0).astype("int8")
        Drivers["light"] = np.where(Drivers.weight_selector < 5, 1, 0).astype("int8")
        Drivers.drop(["weight_selector"], axis=1, inplace=True)

        ###### Broken Items case cap ########
        Drivers["Broken Items"] = np.where(
            Drivers["broken_case_flag"] == 1, Drivers["case_capacity"], 0
        ).astype("float32")

        Drivers["cases_delivered_on_sf"] = np.where(
            (Drivers["broken_case_flag"] == 1),
            Drivers["cases_delivered"] - 1,
            Drivers["cases_delivered"],
        ).astype("float32")
        Drivers["cases_delivered_on_sf"] = np.where(
            Drivers["cases_delivered_on_sf"] < 0, 0, Drivers["cases_delivered_on_sf"]
        )
        Drivers.srp = np.where(
            (Drivers.srp > 0) & (Drivers.broken_case_flag == 1), 0, Drivers.srp
        )  # 2. SRP / full_pallet / mu customizing
        Drivers.nsrp = np.where((Drivers.broken_case_flag == 1), 1, Drivers.nsrp)
        Drivers.nsrp = np.where(
            (Drivers.srp > 0) & (Drivers["pmg"].str.contains("FRZ")), 1, Drivers.nsrp
        )
        Drivers.srp = np.where(
            (Drivers.srp > 0) & (Drivers["pmg"].str.contains("FRZ")), 0, Drivers.srp
        )

        Drivers["backroom_cases_dotcom"] = np.where(
            Drivers.backroom_flag == 1,
            Drivers.sold_units_dotcom / Drivers.case_capacity,
            0,
        ).astype("float32")
        Drivers["backroom_cases_dotcom"] = (
            Drivers["backroom_cases_dotcom"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )
        Drivers["backroom_pallets"] = np.where(
            Drivers["backroom_cases_dotcom"] > 0,
            Drivers["backroom_cases_dotcom"] / Drivers.pallet_capacity,
            0,
        ).astype("float32")
        Drivers["backroom_pallets"] = (
            Drivers["backroom_pallets"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )

        Drivers["cases_delivered_on_sf"] = np.where(
            Drivers["backroom_cases_dotcom"] > 0,
            Drivers["cases_delivered_on_sf"] - Drivers["backroom_cases_dotcom"],
            Drivers["cases_delivered_on_sf"],
        ).astype("float32")
        Drivers["cases_delivered_on_sf"] = np.where(
            Drivers["cases_delivered_on_sf"] < 0, 0, Drivers["cases_delivered_on_sf"]
        )
        Drivers["secondary_srp"] = np.where(
            Drivers.sold_units > Drivers.capacity,
            Drivers.stock - (Drivers.capacity / (1 - backstock_target)),
            0,
        ).astype(
            "float32"
        )  # 4. Secondary Displays -SRP
        Drivers["secondary_srp"] = (
            Drivers["secondary_srp"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )
        Drivers.secondary_srp = np.where(
            ((1 - Drivers.capacity / Drivers.stock) > 0.4), Drivers.secondary_srp, 0
        ).astype("float32")
        Drivers.secondary_srp = np.where(
            Drivers.stock < Drivers.capacity, 0, Drivers.secondary_srp
        )
        Drivers.secondary_srp = np.where(
            (Drivers.srp > 0)
            | (Drivers.full_pallet > 0)
            | (Drivers.mu > 0)
            | (Drivers.split_pallet > 0),
            Drivers.secondary_srp / 7,
            0,
        )
        Drivers["secondary_nsrp"] = np.where(
            Drivers.sold_units > Drivers.capacity,
            Drivers.stock - (Drivers.capacity / (1 - backstock_target)),
            0,
        ).astype(
            "float32"
        )  # Secondary Displays -NSRP
        Drivers["secondary_nsrp"] = (
            Drivers["secondary_nsrp"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )
        Drivers.secondary_nsrp = np.where(
            (1 - (Drivers.capacity / Drivers.stock) > 0.4), Drivers.secondary_nsrp, 0
        )
        Drivers.secondary_nsrp = np.where(
            Drivers.stock < Drivers.capacity, 0, Drivers.secondary_nsrp
        )
        Drivers.secondary_nsrp = np.where(
            (Drivers.srp == 0)
            & (Drivers.full_pallet == 0)
            & (Drivers.mu == 0)
            & (Drivers.split_pallet == 0),
            Drivers.secondary_nsrp / 7,
            0,
        )  # it contains full_pallet as well, is it okay?????

        ### here is the cases calculation part
        Drivers["shop_floor_capacity"] = (
            Drivers.capacity + Drivers.secondary_nsrp + Drivers.secondary_srp
        ).astype(
            "float32"
        )  # 6. One/Two touch

        Drivers["stock_eod"] = Drivers.stock + Drivers.unit - Drivers.sold_units
        Drivers["stock_eod"] = (
            Drivers["stock_eod"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )

        Drivers["cases_to_replenish"] = (
            Drivers.sold_units
            + Drivers[["stock_eod", "shop_floor_capacity"]].min(axis=1)
            - Drivers[["stock", "shop_floor_capacity"]].min(axis=1)
        ) / Drivers.case_capacity
        Drivers["cases_to_replenish"] = np.where(
            Drivers["cases_to_replenish"] < 0, 0, Drivers["cases_to_replenish"]
        )

        ###### ClipStrip Flag ######
        Drivers.clipstrip_flag = np.where(
            (
                Drivers.product_name.str.contains("HELL")
                | Drivers.product_name.str.contains("XIXO")
            ),
            0,
            Drivers.clipstrip_flag,
        )
        Drivers["Clip Strip Items"] = np.where(
            Drivers.clipstrip_flag == 1,
            Drivers["cases_to_replenish"] * Drivers["case_capacity"],
            0,
        )

        Drivers["Clip Strip Cases"] = (
            Drivers["Clip Strip Items"] / Drivers.case_capacity
        ).astype("float32")
        Drivers["Clip Strip Cases"] = (
            Drivers["Clip Strip Cases"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )

        Drivers["cases_to_replenish"] = np.where(
            Drivers.clipstrip_flag == 1, 0, Drivers["cases_to_replenish"]
        )

        Drivers["o_touch"] = np.where(
            Drivers.stock > Drivers.shop_floor_capacity,
            Drivers.shop_floor_capacity,
            Drivers.stock,
        ).astype("float32")

        Drivers["c_touch"] = np.where(
            (Drivers.is_capping_shelf == 1)
            & (
                Drivers.stock - (capping_shelves_ratio * Drivers.shop_floor_capacity)
                > Drivers.shop_floor_capacity
            ),
            capping_shelves_ratio * Drivers.shop_floor_capacity,
            0,
        )

        Drivers["t_touch"] = np.where(
            Drivers["stock"] > Drivers.shop_floor_capacity,
            Drivers["stock"] - Drivers.c_touch - Drivers.shop_floor_capacity,
            0,
        ).astype("float32")

        Drivers["Capping Shelf Cases"] = (
            (Drivers.c_touch / Drivers.case_capacity) / 7
        ).astype("float32")
        Drivers["Capping Shelf Cases"] = (
            Drivers["Capping Shelf Cases"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )
        Drivers["Capping Shelf Cases"] = np.where(
            (Drivers.full_pallet > 0) | (Drivers.mu > 0),
            0,
            Drivers["Capping Shelf Cases"],
        )

        Drivers["cases_to_repl_excl_cap_cases"] = np.where(
            Drivers.cases_to_replenish - Drivers["Capping Shelf Cases"] > 0,
            Drivers.cases_to_replenish - Drivers["Capping Shelf Cases"],
            Drivers.cases_to_replenish,
        )

        Drivers["Capping Shelf Cases"] = np.where(
            Drivers.cases_to_replenish - Drivers["Capping Shelf Cases"] > 0,
            Drivers["Capping Shelf Cases"],
            0,
        )

        ###### merging store_inputs df to drivers ########
        stores_df = store_inputs[
            ["Store", "Format", "Plan Size"]
        ].drop_duplicates()  # 7. Drivers calc part
        stores_df.columns = [i.lower() for i in stores_df.columns]
        dep_df = store_inputs[
            [
                "Store",
                "Dep",
                "Racking",
                "Pallets Delivery Ratio",
                "Backstock Pallet Ratio",
                "says",
            ]
        ].drop_duplicates()
        dep_df.columns = [i.lower() for i in dep_df.columns]
        pmg_df = store_inputs[
            ["Country", "Format", "Pmg", "presortPerc", "prack"]
        ].drop_duplicates()
        pmg_df.columns = [i.lower() for i in pmg_df.columns]

        Drivers = Drivers.merge(stores_df, on=["store", "format"], how="inner")
        Drivers = Drivers.merge(pmg_df, on=["country", "format", "pmg"], how="left")
        Drivers = Drivers.merge(dep_df, on=["store", "dep"], how="left")
        Drivers.prack = np.where(Drivers.racking == 1, Drivers.prack, 0)

        Drivers["New Delivery - Rollcages"] = (
            (Drivers.cases_delivered_on_sf / Drivers.pallet_capacity)
            * RC_Capacity_Ratio
        ) * (1 - Drivers["pallets delivery ratio"]).astype("float32")
        Drivers["New Delivery - Rollcages"] = (
            Drivers["New Delivery - Rollcages"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )
        Drivers["New Delivery - Pallets"] = (
            Drivers.cases_delivered_on_sf / Drivers.pallet_capacity
        ) * Drivers["pallets delivery ratio"]
        Drivers["New Delivery - Pallets"] = (
            Drivers["New Delivery - Pallets"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
            .astype("float32")
        )
        Drivers["New Delivery - Shelf Trolley"] = (
            Drivers.cases_delivered_on_sf / Drivers.pallet_capacity
        ) * shelf_trolley_cap_ratio_to_pallet
        Drivers["New Delivery - Shelf Trolley"] = (
            Drivers["New Delivery - Shelf Trolley"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
            .astype("float32")
        )
        Drivers.rename(columns={"presortperc": "Pre-sorted Cases"}, inplace=True)

        Drivers["Pre-sorted Cases"] = (
            Drivers["Pre-sorted Cases"]
            * Drivers["cases_delivered_on_sf"]
            * Drivers["pallets delivery ratio"]
        )  # Drivers.presortperc*Drivers.cases_delivered_on_sf*Drivers['pallets delivery ratio'] # we dont know on TPN level                                       # we dont know on TPN level

        Drivers["L_Pre-sorted Cases"] = np.where(
            Drivers.light == 1, Drivers["Pre-sorted Cases"], 0
        ).astype("float32")
        Drivers["H_Pre-sorted Cases"] = np.where(
            Drivers.heavy == 1, Drivers["Pre-sorted Cases"], 0
        ).astype("float32")
        Drivers["L_Pre-sorted Cases"] = (
            Drivers["L_Pre-sorted Cases"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )
        Drivers["H_Pre-sorted Cases"] = (
            Drivers["H_Pre-sorted Cases"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )
        Drivers["Full Pallet Cases"] = Drivers.cases_to_replenish * np.where(
            Drivers.full_pallet > 0, Drivers.full_pallet, 0
        ).astype("float32")
        Drivers["MU cases"] = Drivers.cases_to_replenish * np.where(
            Drivers.mu > 0, Drivers.mu, 0
        ).astype("float32")

        Drivers["Racking Pallets"] = Drivers.prack * Drivers["New Delivery - Pallets"]

        Drivers["Replenished Rollcages"] = (
            Drivers["New Delivery - Rollcages"]
            + (
                Drivers["Pre-sorted Cases"]
                / Drivers.pallet_capacity
                * Drivers["pallets delivery ratio"]
            )
            * RC_Capacity_Ratio
        )
        Drivers["Replenished Rollcages"] = (
            Drivers["Replenished Rollcages"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
            .astype("float32")
        )
        Drivers["Replenished Pallets"] = np.where(
            (
                Drivers["New Delivery - Pallets"]
                - (
                    Drivers["Pre-sorted Cases"]
                    / Drivers.pallet_capacity
                    * Drivers["pallets delivery ratio"]
                )
            )
            <= 0,
            0,
            Drivers["New Delivery - Pallets"]
            - (
                Drivers["Pre-sorted Cases"]
                / Drivers.pallet_capacity
                * Drivers["pallets delivery ratio"]
            ),
        )
        Drivers["Replenished Pallets"] = (
            Drivers["Replenished Pallets"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
            .astype("float32")
        )

        Drivers["Replenished Shelf Trolley"] = (
            Drivers["Replenished Rollcages"] * shelf_trolley_cap_ratio_to_rollcage
        ) + (Drivers["Replenished Pallets"] * shelf_trolley_cap_ratio_to_pallet).astype(
            "float32"
        )

        Drivers["Replenished Rollcages"] = np.where(
            (Drivers.full_pallet > 0) | (Drivers.mu > 0),
            0,
            Drivers["Replenished Rollcages"],
        )
        Drivers["Replenished Pallets"] = np.where(
            (Drivers.full_pallet > 0) | (Drivers.mu > 0),
            0,
            Drivers["Replenished Pallets"],
        )

        Drivers["Unit_for_tagging"] = (
            Drivers["cases_to_repl_excl_cap_cases"] + Drivers["Capping Shelf Cases"]
        ) * Drivers["case_capacity"]
        Drivers["Unit_for_tagging"] = np.where(
            Drivers["Unit_for_tagging"] < 0, 0, Drivers["Unit_for_tagging"]
        )

        Drivers["Backstock_ratio"] = Drivers["t_touch"] / Drivers.stock
        Drivers["Backstock_ratio"] = (
            Drivers["Backstock_ratio"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )
        Drivers["Backstock Cases"] = (
            Drivers["cases_to_replenish"] * Drivers.Backstock_ratio
        )

        Drivers["Backstock Pallets"] = np.where(
            (Drivers.full_pallet > 0) | (Drivers.mu > 0),
            Drivers["Backstock Cases"] / Drivers.pallet_capacity,
            Drivers["Backstock Cases"]
            / Drivers.pallet_capacity
            * Drivers["backstock pallet ratio"],
        ).astype(
            "float32"
        )  # if it is back we should calculate with c_touch variable
        Drivers["Backstock Pallets"] = (
            Drivers["Backstock Pallets"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )
        Drivers["Backstock Cases"] = np.where(
            (Drivers.full_pallet > 0) | (Drivers.mu > 0), 0, Drivers["Backstock Cases"]
        )
        Drivers["L_Backstock Cases"] = np.where(
            (Drivers.light == 1), Drivers["Backstock Cases"], 0
        ).astype("float32")
        Drivers["H_Backstock Cases"] = np.where(
            (Drivers.heavy == 1), Drivers["Backstock Cases"], 0
        ).astype("float32")

        Drivers["Backstock Rollcages"] = np.where(
            (Drivers.full_pallet > 0) | (Drivers.mu > 0),
            0,
            (
                Drivers["Backstock Cases"]
                / Drivers.pallet_capacity
                * (1 - Drivers["backstock pallet ratio"])
                * RC_Capacity_Ratio
            ),
        )  # (1.3 means  Fullfil backstock ratio) if it is back we should calculate with c_touch variable
        Drivers["Backstock Rollcages"] = (
            Drivers["Backstock Rollcages"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
            .astype("float32")
        )
        Drivers["Backstock Shelf Trolley"] = (
            Drivers["Backstock Rollcages"] * shelf_trolley_cap_ratio_to_rollcage
            + Drivers["Backstock Pallets"] * shelf_trolley_cap_ratio_to_pallet
        )
        Drivers["Pre-sorted Rollcages"] = (
            (Drivers["Pre-sorted Cases"] / Drivers.pallet_capacity) * RC_Capacity_Ratio
        ).astype(
            "float32"
        )  # removed  *pallet delivery ratio
        Drivers["Pre-sorted Rollcages"] = (
            Drivers["Pre-sorted Rollcages"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )
        Drivers["Pre-sorted Shelf Trolley"] = (
            Drivers["Pre-sorted Cases"] / Drivers.pallet_capacity
        ) * shelf_trolley_cap_ratio_to_pallet  # removed  *pallet delivery ratio
        Drivers["Pre-sorted Shelf Trolley"] = (
            Drivers["Pre-sorted Shelf Trolley"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )

        Drivers["Full Pallet"] = (
            Drivers["Full Pallet Cases"] / Drivers.pallet_capacity
        ).astype("float32")
        Drivers["Full Pallet"] = (
            Drivers["Full Pallet"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )
        Drivers["MU Pallet"] = (Drivers["MU cases"] / Drivers.pallet_capacity).astype(
            "float32"
        )
        Drivers["MU Pallet"] = (
            Drivers["MU Pallet"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )

        Drivers["One Touch Cases"] = (Drivers.o_touch / Drivers.case_capacity).astype(
            "float32"
        )
        Drivers["One Touch Cases"] = (
            Drivers["One Touch Cases"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )

        Drivers["srp_split_pallet"] = Drivers.srp + Drivers.split_pallet
        Drivers["L_SRP"] = np.where(
            (Drivers.srp_split_pallet > 0) & (Drivers.light == 1),
            (
                (
                    Drivers["cases_to_repl_excl_cap_cases"]
                    + Drivers["Capping Shelf Cases"]
                )
                * Drivers.srp_split_pallet
            )
            * Drivers.foil,
            0,
        ).astype("float32")
        Drivers["H_SRP"] = np.where(
            (Drivers.srp_split_pallet > 0) & (Drivers.heavy == 1),
            (
                (
                    Drivers["cases_to_repl_excl_cap_cases"]
                    + Drivers["Capping Shelf Cases"]
                )
                * Drivers.srp_split_pallet
            )
            * Drivers.foil,
            0,
        ).astype("float32")
        Drivers["L_NSRP"] = np.where(
            (Drivers.nsrp > 0) & (Drivers.light == 1),
            (Drivers["cases_to_repl_excl_cap_cases"] + Drivers["Capping Shelf Cases"])
            * Drivers.nsrp,
            0,
        ).astype("float32")
        Drivers["H_NSRP"] = np.where(
            (Drivers.nsrp > 0) & (Drivers.heavy == 1),
            (Drivers["cases_to_repl_excl_cap_cases"] + Drivers["Capping Shelf Cases"])
            * Drivers.nsrp,
            0,
        ).astype("float32")
        Drivers["Foil_Cases"] = np.where(
            Drivers.foil != 1,
            (1 - Drivers.foil)
            * (
                Drivers["cases_to_repl_excl_cap_cases"] + Drivers["Capping Shelf Cases"]
            ),
            0,
        )
        Drivers["Foil_Cases"] = np.where(
            (Drivers["L_NSRP"] > 0) | (Drivers["H_NSRP"] > 0), 0, Drivers["Foil_Cases"]
        )

        Drivers["L_NSRP"] = np.where(
            (Drivers.srp_split_pallet > 0) & (Drivers.light == 1),
            Drivers["Foil_Cases"] + Drivers["L_NSRP"],
            Drivers["L_NSRP"],
        ).astype("float32")
        Drivers["H_NSRP"] = np.where(
            (Drivers.srp_split_pallet > 0) & (Drivers.heavy == 1),
            Drivers["Foil_Cases"] + Drivers["H_NSRP"],
            Drivers["H_NSRP"],
        ).astype("float32")

        Drivers["Sec_SRP_cases"] = (
            Drivers.secondary_srp / Drivers.case_capacity
        ).astype("float32")
        Drivers["Sec_SRP_cases"] = (
            Drivers["Sec_SRP_cases"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )
        Drivers["Sec_NSRP_cases"] = (
            Drivers.secondary_nsrp / Drivers.case_capacity
        ).astype("float32")
        Drivers["Sec_NSRP_cases"] = (
            Drivers["Sec_NSRP_cases"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )

        Drivers["Full Pallet Cases"] = (
            Drivers.cases_to_replenish
            * np.where(Drivers.full_pallet > 0, Drivers.full_pallet, 0)
            * Drivers.foil
        ).astype("float32")
        Drivers["MU cases"] = (
            Drivers.cases_to_replenish
            * np.where(Drivers.mu > 0, Drivers.mu, 0)
            * Drivers.foil
        ).astype("float32")

        Drivers["L_NSRP"] = np.where(
            (Drivers.full_pallet > 0) & (Drivers.light == 1),
            Drivers["Foil_Cases"] + Drivers["L_NSRP"],
            Drivers["L_NSRP"],
        ).astype("float32")
        Drivers["H_NSRP"] = np.where(
            (Drivers.full_pallet > 0) & (Drivers.heavy == 1),
            Drivers["Foil_Cases"] + Drivers["H_NSRP"],
            Drivers["H_NSRP"],
        ).astype("float32")

        Drivers["L_NSRP"] = np.where(
            (Drivers.mu > 0) & (Drivers.light == 1),
            Drivers["Foil_Cases"] + Drivers["L_NSRP"],
            Drivers["L_NSRP"],
        ).astype("float32")
        Drivers["H_NSRP"] = np.where(
            (Drivers.mu > 0) & (Drivers.heavy == 1),
            Drivers["Foil_Cases"] + Drivers["H_NSRP"],
            Drivers["H_NSRP"],
        ).astype("float32")

        Drivers["L_NSRP_Items"] = Drivers.L_NSRP * Drivers.case_capacity
        Drivers["H_NSRP_Items"] = Drivers.H_NSRP * Drivers.case_capacity

        Drivers["L_Hook Fill Cases"] = np.where(
            (Drivers.pmg == "DRY13")
            | (Drivers.pmg == "HDL21")
            | (Drivers.pmg == "PPD02"),
            Drivers.L_NSRP,
            0,
        ).astype("float32")
        Drivers["H_Hook Fill Cases"] = np.where(
            (Drivers.pmg == "DRY13")
            | (Drivers.pmg == "HDL21")
            | (Drivers.pmg == "PPD02"),
            Drivers.H_NSRP,
            0,
        ).astype("float32")
        Drivers["Hook Fill Items"] = np.where(
            (Drivers.pmg == "DRY13")
            | (Drivers.pmg == "HDL21")
            | (Drivers.pmg == "PPD02"),
            (Drivers.L_NSRP_Items + Drivers.H_NSRP_Items),
            0,
        ).astype("float32")
        Drivers["L_NSRP"] = np.where(
            (Drivers.pmg == "DRY13")
            | (Drivers.pmg == "HDL21")
            | (Drivers.pmg == "PPD02"),
            0,
            Drivers.L_NSRP,
        ).astype("float32")
        Drivers["H_NSRP"] = np.where(
            (Drivers.pmg == "DRY13")
            | (Drivers.pmg == "HDL21")
            | (Drivers.pmg == "PPD02"),
            0,
            Drivers.H_NSRP,
        ).astype("float32")
        Drivers["L_NSRP_Items"] = np.where(
            (Drivers.pmg == "DRY13")
            | (Drivers.pmg == "HDL21")
            | (Drivers.pmg == "PPD02"),
            0,
            Drivers.L_NSRP_Items,
        ).astype("float32")
        Drivers["H_NSRP_Items"] = np.where(
            (Drivers.pmg == "DRY13")
            | (Drivers.pmg == "HDL21")
            | (Drivers.pmg == "PPD02"),
            0,
            Drivers.H_NSRP_Items,
        ).astype("float32")

        Drivers["L_SRP"] = np.where(
            (Drivers.srp_split_pallet > 0) & (Drivers.light == 1),
            Drivers.L_SRP + Drivers.Sec_SRP_cases,
            Drivers["L_SRP"],
        )
        Drivers["H_SRP"] = np.where(
            (Drivers.srp_split_pallet > 0) & (Drivers.heavy == 1),
            Drivers.H_SRP + Drivers.Sec_SRP_cases,
            Drivers["H_SRP"],
        )

        Drivers["L_NSRP"] = np.where(
            (Drivers.nsrp > 0) & (Drivers.light == 1),
            Drivers.L_NSRP + Drivers.Sec_NSRP_cases,
            Drivers["L_NSRP"],
        )
        Drivers["H_NSRP"] = np.where(
            (Drivers.nsrp > 0) & (Drivers.heavy == 1),
            Drivers.H_NSRP + Drivers.Sec_NSRP_cases,
            Drivers["H_NSRP"],
        )

        Drivers["H_CASE_L_NSRP_items"] = np.where(
            Drivers.weight <= 1.5, Drivers.H_NSRP * Drivers.case_capacity, 0
        )
        Drivers["H_CASE_H_NSRP_items"] = np.where(
            Drivers.weight > 1.5, Drivers.H_NSRP * Drivers.case_capacity, 0
        )

        Drivers["High_pallet_cases_on_Dry30_and_DRY24"] = np.where(
            (Drivers.full_pallet > 0) & (Drivers.pmg.isin(["DRY30", "DRY24"])),
            Drivers["Full Pallet Cases"] * 0.2,
            0,
        )  # Expectations: pallet is too high so not all cases are fitting into their places
        Drivers["High_pallets_on_Dry30_and_DRY24"] = np.where(
            (Drivers.full_pallet > 0) & (Drivers.pmg.isin(["DRY30", "DRY24"])),
            Drivers["Full Pallet"],
            0,
        )  # Expectations: needs to be an extra pallett to carry on products that have no places to put out

        Drivers["High_half_pallet_cases_on_Dry30_and_DRY24"] = np.where(
            (Drivers.mu > 0) & (Drivers.pmg.isin(["DRY30", "DRY24"])),
            Drivers["MU cases"] * 0.2,
            0,
        )  # Expectations: pallet is too high so not all cases are fitting into their places
        Drivers["High_half_pallets_on_Dry30_and_DRY24"] = np.where(
            (Drivers.mu > 0) & (Drivers.pmg.isin(["DRY30", "DRY24"])),
            Drivers["MU Pallet"],
            0,
        )  # Expectations: needs to be an extra pallett to carry on products that have no places to put out

        # Bottle Tagging
        bottle_tag_cond = [
            (Drivers.country == "HU")
            & (Drivers.format.isin(["Hypermarket", "Compact"]))
            & (Drivers.pmg == "BWS05")
            & (Drivers.item_price >= 5000),
            (Drivers.country == "HU")
            & (Drivers.format.isin(["Hypermarket", "Compact"]))
            & (Drivers.pmg == "BWS04")
            & (Drivers.item_price >= 4000),
            (Drivers.country == "HU")
            & (Drivers.format.isin(["1K", "Express"]))
            & (Drivers.pmg == "BWS05")
            & (Drivers.item_price >= 5000),
            (Drivers.country == "HU")
            & (Drivers.format.isin(["1K", "Express"]))
            & (Drivers.pmg == "BWS04")
            & (Drivers.item_price >= 4000),
            (Drivers.country == "CZ")
            & (Drivers.format.isin(["Hypermarket", "Compact"]))
            & (Drivers.pmg == "BWS05")
            & (Drivers.item_price >= 899),
            (Drivers.country == "CZ")
            & (Drivers.format.isin(["Hypermarket", "Compact"]))
            & (Drivers.pmg == "BWS04")
            & (Drivers.item_price >= 349),
            (Drivers.country == "CZ")
            & (Drivers.format.isin(["1K", "Express"]))
            & (Drivers.pmg == "BWS05")
            & (Drivers.item_price >= 299),
            (Drivers.country == "CZ")
            & (Drivers.format.isin(["1K", "Express"]))
            & (Drivers.pmg == "BWS04")
            & (Drivers.item_price >= 199),
            (Drivers.country == "SK")
            & (Drivers.format.isin(["Hypermarket", "Compact"]))
            & (Drivers.pmg == "BWS05")
            & (Drivers.item_price >= 15),
            (Drivers.country == "SK")
            & (Drivers.format.isin(["Hypermarket", "Compact"]))
            & (Drivers.pmg == "BWS04")
            & (Drivers.item_price >= 10),
            (Drivers.country == "SK")
            & (Drivers.format.isin(["1K", "Express"]))
            & (Drivers.pmg == "BWS05")
            & (Drivers.item_price >= 15),
            (Drivers.country == "SK")
            & (Drivers.format.isin(["1K", "Express"]))
            & (Drivers.pmg == "BWS04")
            & (Drivers.item_price >= 12),
        ]
        bottle_tag_result = [
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
        ]

        Drivers["Bottle_Tag"] = np.select(bottle_tag_cond, bottle_tag_result, 0)

        # Safer Tagging
        safer_tag_cond = [
            (Drivers.country == "HU")
            & (Drivers.pmg == "HEA14")
            & (Drivers.item_price >= 1000),
            (Drivers.country == "HU")
            & (Drivers.pmg == "HEA05")
            & (Drivers.item_price >= 3000),
            (Drivers.country == "HU")
            & (Drivers.pmg == "HEA16")
            & (Drivers.item_price >= 2000),
            (Drivers.country == "HU")
            & (Drivers.pmg == "HEA15")
            & (Drivers.item_price >= 4000),
            (Drivers.country == "HU")
            & (Drivers.pmg == "HEA17")
            & (Drivers.item_price >= 2000),
            (Drivers.country == "HU")
            & (Drivers.pmg == "HEA08")
            & (Drivers.item_price >= 4000),
            (Drivers.country == "HU")
            & (Drivers.pmg == "HEA06")
            & (Drivers.item_price >= 3000),
            (Drivers.country == "HU")
            & (Drivers.pmg == "HDL33")
            & (Drivers.item_price >= 0),
            (Drivers.country == "CZ")
            & (Drivers.pmg.isin(["HEA15", "HEA08"]))
            & (Drivers.item_price >= 600),
            (Drivers.country == "CZ")
            & (Drivers.pmg.isin(["HEA14", "HEA05", "HEA16", "HEA17", "HEA06"]))
            & (Drivers.item_price >= 200),
            (Drivers.country == "CZ")
            & (Drivers.pmg == "HDL33")
            & (Drivers.item_price >= 0),
            (Drivers.country == "SK")
            & (Drivers.pmg.isin(["HEA15", "HEA08"]))
            & (Drivers.item_price >= 20),
            (Drivers.country == "SK")
            & (Drivers.pmg.isin(["HEA14", "HEA05", "HEA16", "HEA17", "HEA06"]))
            & (Drivers.item_price >= 10),
            (Drivers.country == "SK")
            & (Drivers.pmg == "HDL33")
            & (Drivers.item_price >= 0),
        ]

        safer_tag_result = [
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
        ]

        Drivers["Safer_Tag"] = np.select(safer_tag_cond, safer_tag_result, 0).astype(
            "float32"
        )

        # Salami Tagging
        Drivers["Salami_Tag"] = np.where(
            (Drivers.country == "HU")
            & (Drivers.pmg == "PPD02")
            & (Drivers.item_price >= 2500),
            Drivers["Unit_for_tagging"],
            0,
        ).astype("float32")

        # Soft Tagging
        soft_tag_cond = [
            (Drivers.country == "HU")
            & (Drivers.pmg == "DAI05")
            & (Drivers.item_price >= 3000)
            & (Drivers.says != 0),
            (Drivers.country == "HU")
            & (Drivers.pmg == "DRY13")
            & (Drivers.item_price >= 2500)
            & (Drivers.says != 0),
            (Drivers.country == "SK")
            & (Drivers.pmg == "DRY13")
            & (Drivers.item_price >= 5)
            & (Drivers.says != 0),
            (
                (Drivers.country.isin(["HU", "SK"]))
                & (
                    Drivers.product_name.str.contains("RIO MARE")
                    | Drivers.product_name.str.contains("RIOMARE")
                )
                & (Drivers.says != 0)
            ),
        ]
        soft_tag_result = [
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"] / 5,
            Drivers["Unit_for_tagging"] / 5,
            Drivers["Unit_for_tagging"] / 4,
        ]
        Drivers["Soft_Tag"] = np.select(soft_tag_cond, soft_tag_result, 0).astype(
            "float32"
        )

        # Electro Tagging
        electro_tag_cond = [
            (Drivers.country == "HU")
            & (Drivers.pmg.isin(["HDL20", "HDL32", "HDL34", "HDL35", "HDL36"]))
            & (Drivers.item_price >= 10000),
            (Drivers.country == "HU")
            & (Drivers.format.isin(["Hypermarket", "Compact"]))
            & (Drivers.product_name.str.contains("LEGO"))
            & (Drivers.item_price > 10000),
            (Drivers.country == "CZ")
            & (Drivers.pmg.isin(["HDL32", "HDL34", "HDL35", "HDL36"]))
            & (Drivers.item_price >= 600),
            (Drivers.country == "CZ")
            & (Drivers.pmg.isin(["HDL20", "HDL21", "HDL22"]))
            & (Drivers.item_price >= 999),
            (Drivers.country == "SK")
            & (Drivers.pmg.isin(["HDL32", "HDL34", "HDL35", "HDL36"]))
            & (Drivers.item_price >= 20),
            (Drivers.country == "SK")
            & (Drivers.pmg.isin(["HDL21", "HDL22"]))
            & (Drivers.item_price >= 20),
            (Drivers.country == "SK")
            & (Drivers.pmg.isin(["HDL20"]))
            & (Drivers.item_price >= 50),
            (Drivers.country == "SK")
            & (Drivers.product_name.str.contains("LEGO"))
            & (Drivers.item_price > 50),
        ]
        electro_tag_result = [
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
        ]

        Drivers["Electro_Tag"] = np.select(
            electro_tag_cond, electro_tag_result, 0
        ).astype("float32")

        # Gillette Tagging
        Gillette_Tag_cond = [
            (Drivers.country == "HU")
            & (Drivers.pmg == "HEA07")
            & (Drivers.item_price >= 3000),
            (Drivers.country == "CZ")
            & (Drivers.pmg == "HEA07")
            & (Drivers.item_price >= 200),
            (Drivers.country == "SK")
            & (Drivers.pmg == "HEA07")
            & (Drivers.item_price >= 10),
        ]
        Gillette_Tag_result = [
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
        ]
        Drivers["Gillette_Tag"] = np.select(
            Gillette_Tag_cond, Gillette_Tag_result, 0
        ).astype("float32")

        # Hard Tagging
        hard_tag_cond = [
            (Drivers.country == "HU")
            & (Drivers.pmg.isin(["HEA14", "HEA18", "HEA15"]))
            & (Drivers.item_price >= 8000),
            (Drivers.country == "HU")
            & (Drivers.product_name.str.contains("GURULOS BOROND"))
            & (Drivers.item_price > 10000),
            (Drivers.country == "CZ")
            & (Drivers.pmg.isin(["HEA14", "HEA18", "HEA15"]))
            & (Drivers.item_price >= 599),
            (Drivers.country == "CZ")
            & (Drivers.product_name.str.contains("BATOH"))
            & (Drivers.item_price >= 599),
            (Drivers.country == "SK")
            & (Drivers.pmg.isin(["HEA14", "HEA18", "HEA15"]))
            & (Drivers.item_price >= 20),
            (Drivers.country == "SK")
            & (Drivers.product_name.str.contains("KUFOR"))
            & (Drivers.item_price > 50),
        ]
        hard_tag_res = [
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
            Drivers["Unit_for_tagging"],
        ]
        Drivers["Hard_Tag"] = np.select(hard_tag_cond, hard_tag_res, 0).astype(
            "float32"
        )

        Drivers["Tag_total_nr"] = (
            Drivers["Hard_Tag"]
            + Drivers["Gillette_Tag"]
            + Drivers["Electro_Tag"]
            + Drivers["Soft_Tag"]
            + Drivers["Salami_Tag"]
            + Drivers["Safer_Tag"]
            + Drivers["Bottle_Tag"]
        )

        Drivers["Bulk Pallets"] = Drivers["Full Pallet"] + Drivers["MU Pallet"]
        Drivers["Total RC's and Pallets"] = (
            Drivers["Replenished Rollcages"]
            + Drivers["Replenished Pallets"]
            + Drivers["Backstock Rollcages"]
            + Drivers["Backstock Pallets"]
            + Drivers["Bulk Pallets"]
        )

        tray_hood_cond = [
            (Drivers["Tray + Hood"] == 1)
            & (Drivers.light == 1)
            & (Drivers.srp_split_pallet > 0),
            (Drivers["Tray + Hood"] == 1) & (Drivers.light == 1) & (Drivers.nsrp > 0),
            (Drivers["Tray + Hood"] == 1)
            & (Drivers.heavy == 1)
            & (Drivers.srp_split_pallet > 0),
            (Drivers["Tray + Hood"] == 1) & (Drivers.heavy == 1) & (Drivers.nsrp > 0),
        ]
        perf_box_cond = [
            (Drivers["Perforated box"] == 1)
            & (Drivers.light == 1)
            & (Drivers.srp_split_pallet > 0),
            (Drivers["Perforated box"] == 1)
            & (Drivers.light == 1)
            & (Drivers.nsrp > 0),
            (Drivers["Perforated box"] == 1)
            & (Drivers.heavy == 1)
            & (Drivers.srp_split_pallet > 0),
            (Drivers["Perforated box"] == 1)
            & (Drivers.heavy == 1)
            & (Drivers.nsrp > 0),
        ]
        shrink_cond = [
            (Drivers["Shrink"] == 1)
            & (Drivers.light == 1)
            & (Drivers.srp_split_pallet == 1),
            (Drivers["Shrink"] == 1) & (Drivers.light == 1) & (Drivers.nsrp == 1),
            (Drivers["Shrink"] == 1)
            & (Drivers.heavy == 1)
            & (Drivers.srp_split_pallet == 1),
            (Drivers["Shrink"] == 1) & (Drivers.heavy == 1) & (Drivers.nsrp == 1),
        ]
        tray_shrink_cond = [
            (Drivers["Tray + Shrink"] == 1)
            & (Drivers.light == 1)
            & (Drivers.srp_split_pallet > 0),
            (Drivers["Tray + Shrink"] == 1) & (Drivers.light == 1) & (Drivers.nsrp > 0),
            (Drivers["Tray + Shrink"] == 1)
            & (Drivers.heavy == 1)
            & (Drivers.srp_split_pallet > 0),
            (Drivers["Tray + Shrink"] == 1) & (Drivers.heavy == 1) & (Drivers.nsrp > 0),
        ]
        tray_cond = [
            (Drivers["Tray"] == 1)
            & (Drivers.light == 1)
            & (Drivers.srp_split_pallet > 0),
            (Drivers["Tray"] > 0) & (Drivers.light == 1) & (Drivers.nsrp > 0),
            (Drivers["Tray"] == 1)
            & (Drivers.heavy == 1)
            & (Drivers.srp_split_pallet > 0),
            (Drivers["Tray"] == 1) & (Drivers.heavy == 1) & (Drivers.nsrp > 0),
        ]
        results = [
            Drivers.L_SRP + Drivers["Foil_Cases"],
            Drivers.L_NSRP + Drivers["Foil_Cases"],
            Drivers.H_SRP + Drivers["Foil_Cases"],
            Drivers.H_NSRP + Drivers["Foil_Cases"],
        ]

        Drivers["Ownbrand_tray_with_hood_cases"] = np.select(
            tray_hood_cond, results, 0
        ).astype("float32")
        Drivers["Ownbrand_perforated_box_cases"] = np.select(
            perf_box_cond, results, 0
        ).astype("float32")
        Drivers["Ownbrand_shrink_cases"] = np.select(shrink_cond, results, 0).astype(
            "float32"
        )
        Drivers["Ownbrand_tray_with_shrink_cases"] = np.select(
            tray_shrink_cond, results, 0
        ).astype("float32")
        Drivers["Ownbrand_tray_cases"] = np.select(tray_cond, results, 0).astype(
            "float32"
        )
        Drivers["total_ownbrand_op_type"] = (
            Drivers["Tray + Hood"]
            + Drivers["Perforated box"]
            + Drivers["Shrink"]
            + Drivers["Tray + Shrink"]
            + Drivers["Tray"]
        )
        Drivers["total_ownbrand_op_cases"] = (
            Drivers["Ownbrand_tray_with_hood_cases"]
            + Drivers["Ownbrand_perforated_box_cases"]
            + Drivers["Ownbrand_shrink_cases"]
            + Drivers["Ownbrand_tray_with_shrink_cases"]
            + Drivers["Ownbrand_tray_cases"]
        )

        # split_pallet opening cases customization
        L_split_p_cond = [
            (Drivers.pmg.isin(["DRY24", "BWS01"]))
            & (Drivers.split_pallet > 0)
            & (Drivers.light == 1),
            (Drivers.division.isin(["Grocery"]))
            & (~Drivers.pmg.isin(["DRY24", "BWS01"]))
            & (Drivers.split_pallet > 0)
            & (Drivers.light == 1),
            (Drivers.division.isin(["GM"]))
            & (Drivers.split_pallet > 0)
            & (Drivers.light == 1),
        ]
        L_split_p_res = [Drivers.L_SRP * 0, Drivers.L_SRP * 0.25, Drivers.L_SRP * 1]
        Drivers["L_split_pallet_cases_for_opening"] = np.select(
            L_split_p_cond, L_split_p_res, 0
        )

        H_split_p_cond = [
            (Drivers.pmg.isin(["DRY24", "BWS01"]))
            & (Drivers.split_pallet > 0)
            & (Drivers.heavy == 1),
            (Drivers.division.isin(["Grocery"]))
            & (~Drivers.pmg.isin(["DRY24", "BWS01"]))
            & (Drivers.split_pallet > 0)
            & (Drivers.heavy == 1),
            (Drivers.division.isin(["GM"]))
            & (Drivers.split_pallet > 0)
            & (Drivers.heavy == 1),
        ]
        H_split_p_res = [Drivers.H_SRP * 0, Drivers.H_SRP * 0.25, Drivers.H_SRP * 1]
        Drivers["H_split_pallet_cases_for_opening"] = np.select(
            H_split_p_cond, H_split_p_res, 0
        )

        Drivers["L_SRP_for_opening_type"] = np.where(
            (Drivers.srp > 0)
            & (Drivers.light == 1)
            & (Drivers["total_ownbrand_op_type"] == 0),
            Drivers.L_SRP + Drivers["Foil_Cases"],
            0,
        ).astype("float32")
        Drivers["L_SRP_for_opening_type"] = np.where(
            (Drivers.split_pallet > 0)
            & (Drivers.light == 1)
            & (Drivers["total_ownbrand_op_type"] == 0),
            Drivers["Foil_Cases"] + Drivers["L_split_pallet_cases_for_opening"],
            Drivers["L_SRP_for_opening_type"],
        ).astype("float32")

        Drivers["H_SRP_for_opening_type"] = np.where(
            (Drivers.srp > 0)
            & (Drivers.heavy == 1)
            & (Drivers["total_ownbrand_op_type"] == 0),
            Drivers.H_SRP + Drivers["Foil_Cases"],
            0,
        ).astype("float32")
        Drivers["H_SRP_for_opening_type"] = np.where(
            (Drivers.split_pallet > 0)
            & (Drivers.heavy == 1)
            & (Drivers["total_ownbrand_op_type"] == 0),
            Drivers["Foil_Cases"] + Drivers["H_split_pallet_cases_for_opening"],
            Drivers["H_SRP_for_opening_type"],
        ).astype("float32")

        Drivers["L_NSRP_for_opening_type"] = np.where(
            (Drivers.nsrp > 0)
            & (Drivers.light == 1)
            & (Drivers["total_ownbrand_op_type"] == 0),
            Drivers.L_NSRP,
            0,
        ).astype("float32")
        Drivers["H_NSRP_for_opening_type"] = np.where(
            (Drivers.nsrp > 0)
            & (Drivers.heavy == 1)
            & (Drivers["total_ownbrand_op_type"] == 0),
            Drivers.H_NSRP,
            0,
        ).astype("float32")

        Drivers.drop(
            ["total_ownbrand_op_type", "total_ownbrand_op_cases"], axis=1, inplace=True
        )

        Drivers["Empty Pallets"] = (
            Drivers["Bulk Pallets"] + Drivers["Replenished Pallets"]
        )
        Drivers["Empty Rollcages"] = Drivers["Replenished Rollcages"]
        Drivers["Empty Shelf Trolley"] = (
            Drivers["Empty Pallets"] * shelf_trolley_cap_ratio_to_pallet
            + Drivers["Empty Rollcages"] * shelf_trolley_cap_ratio_to_rollcage
        )

        Drivers["Add_Walking Backstock Cages"] = Drivers["Backstock Rollcages"].astype(
            "float32"
        )
        Drivers["Add_Walking Cages"] = Drivers["Replenished Rollcages"].astype(
            "float32"
        )
        Drivers["Add_Walking Pallets"] = np.where(
            (Drivers.full_pallet > 0) | (Drivers.mu > 0),
            Drivers["Bulk Pallets"],
            Drivers["Replenished Pallets"],
        ).astype("float32")

        Drivers["sold_cases"] = Drivers["sold_units"] / Drivers["case_capacity"]
        Drivers["sold_cases"] = (
            Drivers["sold_cases"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
        )

        Drivers.rename(columns={"sales_excl_vat": "sales"}, inplace=True)

        #'Backstock_ratio',

        Drivers = Drivers[
            [
                "country",
                "store",
                "day",
                "dep",
                "pmg",
                "t_touch",
                "cases_to_replenish",
                "full_pallet",
                "mu",
                "light",
                "heavy",
                "backstock pallet ratio",
                "Replenished Rollcages",
                "Replenished Pallets",
                "tpnb",
                "sold_units",
                "sold_cases",
                "sales",
                "stock",
                "cases_delivered",
                "cases_delivered_on_sf",
                "Add_Walking Cages",
                "pallet_capacity",
                "Add_Walking Backstock Cages",
                "Add_Walking Pallets",
                "Backstock Cases",
                "Backstock Pallets",
                "Backstock Rollcages",
                "Backstock Shelf Trolley",
                "backroom_pallets",
                "Bottle_Tag",
                "Broken Items",
                "broken_case_flag",
                "Bulk Pallets",
                "Capping Shelf Cases",
                "Clip Strip Cases",
                "Clip Strip Items",
                "Electro_Tag",
                "Empty Pallets",
                "Empty Rollcages",
                "Empty Shelf Trolley",
                "Full Pallet",
                "Full Pallet Cases",
                "Gillette_Tag",
                "H_Backstock Cases",
                "H_CASE_H_NSRP_items",
                "H_CASE_L_NSRP_items",
                "H_Hook Fill Cases",
                "H_NSRP_for_opening_type",
                "H_Pre-sorted Cases",
                "H_SRP",
                "H_NSRP",
                "H_SRP_for_opening_type",
                "Hard_Tag",
                "Hook Fill Items",
                "L_Backstock Cases",
                "L_NSRP",
                "L_NSRP_for_opening_type",
                "L_NSRP_Items",
                "L_Pre-sorted Cases",
                "L_SRP",
                "L_SRP_for_opening_type",
                "MU Pallet",
                "New Delivery - Pallets",
                "New Delivery - Rollcages",
                "New Delivery - Shelf Trolley",
                "Ownbrand_perforated_box_cases",
                "Ownbrand_shrink_cases",
                "Ownbrand_tray_cases",
                "Ownbrand_tray_with_hood_cases",
                "Ownbrand_tray_with_shrink_cases",
                "Pre-sorted Rollcages",
                "Pre-sorted Shelf Trolley",
                "Racking Pallets",
                "Safer_Tag",
                "Salami_Tag",
                "Soft_Tag",
                "Total RC's and Pallets",
                "High_pallet_cases_on_Dry30_and_DRY24",
                "High_pallets_on_Dry30_and_DRY24",
                "High_half_pallet_cases_on_Dry30_and_DRY24",
                "High_half_pallets_on_Dry30_and_DRY24",
                "Tag_total_nr",
            ]
        ]

        Drivers = optimize_objects(optimize_types(Drivers))

    # PRODUCE

    if len(Drivers_produce) > 0:

        Drivers_produce = Drivers_produce[
            [
                "country",
                "store",
                "day",
                "tpnb",
                "dep",
                "pmg",
                "cases_delivered",
                "case_capacity",
                "pallet_capacity",
                "stock",
                "sold_units",
                "sales_excl_vat",
                "unit_type",
                "weight",
            ]
        ]
        produce_df = pd.read_excel(directory / excel_inputs_f, "produce_dataframe")
        produce_df.columns = [i.lower() for i in produce_df.columns]
        produce_modules = pd.read_excel(directory / excel_inputs_f, "produce_modules")
        produce_modules.columns = [i.lower() for i in produce_modules.columns]
        RC_table = produce_df[["pmg", "replenishment_type", "rc_capacity"]].copy()
        Drivers_produce = Drivers_produce.merge(
            produce_df[produce_df.columns[~produce_df.columns.isin(["rc_capacity"])]],
            on="pmg",
            how="left",
        )
        # =============================================================================
        # Crates Customizing + Average Items in Case
        # - custom crates shows a total amount of LARGE CRATES. So, if we have 4 small crates then we treat them as 2 large
        # - daily_crates_on_stock = stock crates + sold crates
        # - items in case is necesary for categories replenished as an item
        # =============================================================================
        # Drivers_produce['stock'] = Drivers_produce['cases_delivered']
        # Drivers_produce['stock'] = ((Drivers_produce.groupby(['store', 'tpn'])['stock'].transform("max"))/7).astype("float32")
        Drivers_produce["sold_units"] = np.where(
            (Drivers_produce.pmg == "PRO04") | (Drivers_produce.pmg == "PRO01"),
            Drivers_produce["sold_units"] * 1,
            Drivers_produce["sold_units"],
        )
        Drivers_produce["crates_on_stock"] = (
            Drivers_produce.stock / Drivers_produce.case_capacity
        )
        Drivers_produce["custom_sold_crates"] = np.where(
            Drivers_produce.unit_type == "KG",
            Drivers_produce.sold_units / (Drivers_produce.case_capacity * 1),
            Drivers_produce.sold_units / Drivers_produce.case_capacity,
        )
        Drivers_produce["custom_sold_crates"] = np.where(
            (Drivers_produce.crate_size == "Small"),
            Drivers_produce.custom_sold_crates / 2,
            Drivers_produce.custom_sold_crates,
        ).astype("float32")
        Drivers_produce["custom_sold_crates"] = np.where(
            (Drivers_produce.crate_size == "Other"),
            Drivers_produce.custom_sold_crates / 4,
            Drivers_produce.custom_sold_crates,
        ).astype("float32")
        Drivers_produce["custom_stock_crates"] = np.where(
            (Drivers_produce.crate_size == "Small"),
            Drivers_produce.crates_on_stock / 2,
            Drivers_produce.crates_on_stock,
        ).astype("float32")
        Drivers_produce["custom_stock_crates"] = np.where(
            (Drivers_produce.crate_size == "Other"),
            Drivers_produce.custom_stock_crates / 4,
            Drivers_produce.custom_stock_crates,
        ).astype("float32")
        # Drivers_produce['daily_crates_on_stock'] = Drivers_produce.custom_sold_crates + Drivers_produce.custom_stock_crates # daily_stock = stock on the end of a day + what they sold this day
        Drivers_produce["total_sales_per_repl_type"] = Drivers_produce.groupby(
            ["store", "day", "replenishment_type"], observed=True
        )["sold_units"].transform("sum")
        # =============================================================================
        # Capacity
        # - custom_crates_on_stock = total of daily crates on stock (daily sold crates + daily stock)
        # - custom_tpn_on_stock = total amount of TPNs per pmg and store
        # - multideck_group = in here we check how many crates can be filled on modules. We take into consideration:
        #     1. stock ratio per pmg/store
        #     2. amount of crates per tpn. If we have more than 4 crates per TPN then we put it to warehouse (we have a place just for 4 tpns per one TPN)
        # - backstock = is calculated based on custom_crates_on_stock. So it is daily sold crates + daily stock
        # =============================================================================
        # Drivers_produce['crates_per_tpn'] = Drivers_produce.daily_crates_on_stock / dataset_group.tpn
        # Drivers_produce['daily_crates_on_stock_tpn'] = np.where(Drivers_produce.daily_crates_on_stock > 4, 4 * Drivers_produce.tpn, Drivers_produce.daily_crates_on_stock)
        Drivers_produce = Drivers_produce.merge(
            produce_modules[["store", "tables", "multidecks"]], on="store", how="left"
        )
        Drivers_produce["one_touch"] = np.where(
            Drivers_produce.replenishment_type == "Multideck",
            MODULE_CRATES * Drivers_produce.multidecks,
            0,
        )  # dataset_group (banana: 3 crates on hammock + 4 below)
        Drivers_produce["one_touch"] = np.where(
            Drivers_produce.replenishment_type == "Produce_table",
            TABLE_CRATES * Drivers_produce.tables,
            Drivers_produce.one_touch,
        )  # calculate shop-floor capacity based on knowledge about total amount of modules
        Drivers_produce["one_touch"] = np.where(
            Drivers_produce.replenishment_type == "Stand", 50, Drivers_produce.one_touch
        )
        Drivers_produce["one_touch"] = np.where(
            Drivers_produce.replenishment_type == "Hammok", 7, Drivers_produce.one_touch
        )
        Drivers_produce["one_touch"] = np.where(
            Drivers_produce.replenishment_type == "Bin", 50, Drivers_produce.one_touch
        )

        Drivers_produce["sales_repl_type_tpn_sales_ratio"] = (
            Drivers_produce["sold_units"] / Drivers_produce["total_sales_per_repl_type"]
        )
        Drivers_produce["sales_repl_type_tpn_sales_ratio"] = (
            Drivers_produce["sales_repl_type_tpn_sales_ratio"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )
        Drivers_produce["one_touch_for_tpns"] = np.ceil(
            Drivers_produce["one_touch"]
            * Drivers_produce["sales_repl_type_tpn_sales_ratio"]
        )

        Drivers_produce["backstock"] = np.where(
            Drivers_produce.one_touch_for_tpns <= Drivers_produce.custom_stock_crates,
            Drivers_produce.custom_stock_crates - Drivers_produce.one_touch_for_tpns,
            0,
        ).astype("float32")

        Drivers_produce.rename(columns={"sales_excl_vat": "sales"}, inplace=True)
        Drivers_produce = Drivers_produce[
            [
                "country",
                "store",
                "day",
                "dep",
                "pmg",
                "tpnb",
                "stock",
                "weight",
                "replenishment_type",
                "unit_type",
                "pallet_capacity",
                "one_touch",
                "backstock",
                "cases_delivered",
                "sold_units",
                "sales",
                "custom_sold_crates",
                "case_capacity",
            ]
        ]

        Drivers_produce["total_unit_to_fill"] = np.where(
            (
                Drivers_produce.cases_delivered * Drivers_produce.case_capacity
                + Drivers_produce.stock
            )
            > Drivers_produce.sold_units,
            Drivers_produce.sold_units,
            (
                Drivers_produce.cases_delivered * Drivers_produce.case_capacity
                + Drivers_produce.stock
            ),
        )

        cycle_list = 5
        cycle_list_2 = 2
        cycle_list_1 = 1

        unit_cond = [
            Drivers_produce["custom_sold_crates"] >= cycle_list,
            (Drivers_produce["custom_sold_crates"] < cycle_list)
            & (Drivers_produce["custom_sold_crates"] > cycle_list_1),
            (Drivers_produce["custom_sold_crates"] < cycle_list)
            & (Drivers_produce["custom_sold_crates"] < cycle_list_2)
            & (Drivers_produce["custom_sold_crates"] < cycle_list_1),
        ]
        unit_result = [
            Drivers_produce.sold_units / cycle_list,
            Drivers_produce.sold_units / cycle_list_2,
            Drivers_produce.sold_units / cycle_list_1,
        ]

        Drivers_produce["UNIT_to_replenish_per_rounds"] = np.select(
            unit_cond, unit_result, 0
        )

        crates_cond = [
            Drivers_produce["custom_sold_crates"] >= cycle_list,
            (Drivers_produce["custom_sold_crates"] < cycle_list)
            & (Drivers_produce["custom_sold_crates"] > cycle_list_1),
            (Drivers_produce["custom_sold_crates"] < cycle_list)
            & (Drivers_produce["custom_sold_crates"] < cycle_list_2)
            & (Drivers_produce["custom_sold_crates"] > 0.5),
            (Drivers_produce["custom_sold_crates"] < 0.5),
        ]

        crates_result = [
            (
                (
                    np.ceil(
                        Drivers_produce["UNIT_to_replenish_per_rounds"]
                        / Drivers_produce.case_capacity
                    )
                )
                * cycle_list
            ),
            (
                (
                    np.ceil(
                        Drivers_produce["UNIT_to_replenish_per_rounds"]
                        / Drivers_produce.case_capacity
                    )
                )
                * cycle_list_2
            ),
            (
                (
                    np.ceil(
                        Drivers_produce["UNIT_to_replenish_per_rounds"]
                        / Drivers_produce.case_capacity
                    )
                )
                * cycle_list_1
            ),
            0,
        ]

        Drivers_produce["CRATES_to_replenish"] = np.select(
            crates_cond, crates_result, 0
        )

        # Drivers_produce['shelf_filling'] = Drivers_produce.one_touch
        # Drivers_produce['crates_to_replenish'] = 0

        # for x, y in zip(range(1,cycle_list+1),range(cycle_list)):

        #     Drivers_produce[f'cycle_{x}']=np.where((((Drivers_produce['shelf_filling']-(SALES_CYCLE[y]*Drivers_produce['custom_sold_crates']))/Drivers_produce['shelf_filling'])<FULFILL_TARGET),1,0)
        #     Drivers_produce['crates_to_replenish']=np.where(Drivers_produce[f'cycle_{x}']>0, ((Drivers_produce['crates_to_replenish']+(Drivers_produce['one_touch']-Drivers_produce['shelf_filling']))+(Drivers_produce['custom_sold_crates']*SALES_CYCLE[y])), Drivers_produce['crates_to_replenish'])
        #     Drivers_produce['shelf_filling']=np.where(Drivers_produce[f'cycle_{x}']>0, Drivers_produce['one_touch'], Drivers_produce['shelf_filling']-(Drivers_produce['custom_sold_crates']*SALES_CYCLE[y]))

        # =============================================================================
        # Weekly Drivers calculation
        # - backstock_cases_replenished is required just to know how many times I need to move
        # - backstock_rc shows amount of RCs which have to be moved on Shop-Floor (sometimes the same RC needs to be moved more than once). So it is NOT amount of RCs bout amout of stock movements
        # =============================================================================

        Drivers_produce = Drivers_produce.merge(
            RC_table, on=["pmg", "replenishment_type"], how="left"
        )
        # Drivers_produce['one_touch_cases'] = (Drivers_produce.one_touch/(Drivers_produce.one_touch+Drivers_produce.backstock))*Drivers_produce.cases_delivered
        Drivers_produce["Backstock Cases"] = Drivers_produce["backstock"]

        Drivers_produce["backstock_cases_frequency"] = cycle_list
        Drivers_produce["backstock_cases_replenished"] = Drivers_produce[
            "backstock"
        ]  # ((Drivers_produce.CRATES_to_replenish/Drivers_produce.backstock)*Drivers_produce['Backstock Cases'])
        Drivers_produce["pre_sorted_cases"] = (
            Drivers_produce.backstock_cases_replenished * 0.25
        )  # presort% from Store_Inputs table
        Drivers_produce["Pre-sorted Rollcages"] = (
            Drivers_produce.pre_sorted_cases / Drivers_produce.rc_capacity
        )
        Drivers_produce["one_touch_rc"] = (
            Drivers_produce.cases_delivered / Drivers_produce.rc_capacity
        )
        Drivers_produce["Backstock Rollcages"] = 0
        Drivers_produce["Backstock Pallets"] = np.where(
            Drivers_produce.replenishment_type != "Other",
            (
                (
                    Drivers_produce.backstock_cases_replenished
                    / Drivers_produce.rc_capacity
                )
                * RC_CAPACITY
            )
            * (1 - RC_DELIVERY),
            0,
        ).astype("float32")
        Drivers_produce["backstock_rc_incl_frequencies"] = (
            Drivers_produce.backstock_cases_replenished
            / Drivers_produce.rc_capacity
            * Drivers_produce.backstock_cases_frequency
        )
        Drivers_produce = Drivers_produce.replace(np.nan, 0)
        Drivers_produce["weight_selector"] = (
            Drivers_produce.weight * Drivers_produce.case_capacity
        )  # 1. Heavy & Light
        Drivers_produce["heavy_crates"] = np.where(
            Drivers_produce.weight_selector >= 5, 1, 0
        ).astype("int8")
        Drivers_produce["light_crates"] = np.where(
            Drivers_produce.weight_selector < 5, 1, 0
        ).astype("int8")
        Drivers_produce.drop(["weight_selector"], axis=1, inplace=True)
        Drivers_produce["L_Pre-sorted Crates"] = np.where(
            Drivers_produce["light_crates"] == 1, Drivers_produce["pre_sorted_cases"], 0
        ).astype("float32")
        Drivers_produce["H_Pre-sorted Crates"] = np.where(
            Drivers_produce["heavy_crates"] == 1, Drivers_produce["pre_sorted_cases"], 0
        ).astype("float32")
        Drivers_produce["L_Backstock Cases"] = np.where(
            Drivers_produce["light_crates"] == 1, Drivers_produce["Backstock Cases"], 0
        ).astype("float32")
        Drivers_produce["H_Backstock Cases"] = np.where(
            Drivers_produce["heavy_crates"] == 1, Drivers_produce["pre_sorted_cases"], 0
        ).astype("float32")
        Drivers_produce["New Delivery - Rollcages"] = (
            Drivers_produce.cases_delivered / Drivers_produce.rc_capacity
        ) * RC_DELIVERY
        Drivers_produce["New Delivery - Pallets"] = np.where(
            Drivers_produce.replenishment_type != "Other",
            (Drivers_produce["New Delivery - Rollcages"] * RC_CAPACITY)
            * (1 - RC_DELIVERY),
            0,
        ).astype("float32")
        # Drivers_produce['New Delivery - Pallet_cases'] = Drivers_produce['New Delivery - Pallets'] * Drivers_produce.case_capacity
        # Drivers_produce['New Delivery - Rollcages'] = (Drivers_produce.cases_delivered-Drivers_produce['New Delivery - Pallet_cases']) /Drivers_produce.rc_capacity
        Drivers_produce["Replenished Rollcages"] = Drivers_produce[
            "New Delivery - Rollcages"
        ]  # Replenished Rollcages and Pallets - it is different than on Repl as we do not pre-sort new delivery on produce (just backstock)
        Drivers_produce["Replenished Pallets"] = Drivers_produce[
            "New Delivery - Pallets"
        ]
        Drivers_produce["Green crates case fill"] = np.where(
            (
                (Drivers_produce.replenishment_type == "Multideck")
                | (Drivers_produce.replenishment_type == "Produce_table")
                | (Drivers_produce.replenishment_type == "Hammok")
            )
            & (Drivers_produce.unit_type == "KG"),
            Drivers_produce.CRATES_to_replenish,
            0,
        )
        Drivers_produce["L_Green crates case fill"] = np.where(
            Drivers_produce["light_crates"] == 1,
            Drivers_produce["Green crates case fill"],
            0,
        )
        Drivers_produce["H_Green crates case fill"] = np.where(
            Drivers_produce["heavy_crates"] == 1,
            Drivers_produce["Green crates case fill"],
            0,
        )
        Drivers_produce["Green crates unit fill"] = np.where(
            (
                (Drivers_produce.replenishment_type == "Multideck")
                | (Drivers_produce.replenishment_type == "Produce_table")
                | (Drivers_produce.replenishment_type == "Hammok")
            )
            & (Drivers_produce.unit_type != "KG"),
            Drivers_produce.CRATES_to_replenish,
            0,
        )
        Drivers_produce["Green crates unit fill items"] = np.where(
            (
                (Drivers_produce.replenishment_type == "Multideck")
                | (Drivers_produce.replenishment_type == "Produce_table")
                | (Drivers_produce.replenishment_type == "Hammok")
            )
            & (Drivers_produce.unit_type != "KG"),
            Drivers_produce.total_unit_to_fill,
            0,
        )
        Drivers_produce["Bulk Product Cases"] = np.where(
            Drivers_produce.pmg == "PRO14", Drivers_produce.CRATES_to_replenish, 0
        )  # Bulk Seeds / Nuts / Dried Fruits - CASE
        Drivers_produce["Backstock_Frequency"] = np.where(
            Drivers_produce.replenishment_type != "Other",
            Drivers_produce.backstock_rc_incl_frequencies / 1,
            0,
        ).astype("float32")
        Drivers_produce["Empty Rollcages"] = Drivers_produce[
            "Backstock Rollcages"
        ]  # we calculate it in wrong way but we did not use it in the model as we calculated it in the model's Drivers sheet
        Drivers_produce["Empty Pallets"] = Drivers_produce["Backstock Pallets"]
        Drivers_produce["Potted Plants Cases"] = np.where(
            Drivers_produce.pmg == "PRO13", Drivers_produce.CRATES_to_replenish, 0
        )  # Flower + Garden - Tray fill & Unit Fill (potted plant) - herbs
        Drivers_produce["Potted Plants Items"] = np.where(
            Drivers_produce.pmg == "PRO13", Drivers_produce.total_unit_to_fill, 0
        )
        Drivers_produce["Banana Cases"] = np.where(
            Drivers_produce.pmg == "PRO01", Drivers_produce.CRATES_to_replenish, 0
        )
        Drivers_produce["Banana Shelves Cases"] = np.where(
            Drivers_produce.pmg == "PRO01",
            Drivers_produce.CRATES_to_replenish * 0.57,
            0,
        )  # Banana_Cases & Banana shelves cases below hammock & Banana Hammock - BUNCH
        Drivers_produce["Banana Hammock Bunch"] = np.where(
            Drivers_produce.pmg == "PRO01", Drivers_produce.total_unit_to_fill, 0
        )
        Drivers_produce["Cut Flowers"] = np.where(
            Drivers_produce.pmg == "PRO17", Drivers_produce.CRATES_to_replenish, 0
        )  # Flower + Garden - Cut Flower - CASE= Bucket; Cut Flowers
        Drivers_produce["Flowers Rollcages"] = np.where(
            Drivers_produce.pmg == "PRO17",
            RC_DELIVERY * Drivers_produce["one_touch_rc"],
            0,
        ).astype("float32")
        Drivers_produce["Flowers Pallets"] = np.where(
            Drivers_produce.pmg == "PRO17",
            (
                Drivers_produce["one_touch_rc"]
                - (Drivers_produce["one_touch_rc"] * RC_DELIVERY)
            )
            * RC_VS_PAL_CAPACITY,
            0,
        ).astype("float32")
        Drivers_produce["Bulk Pallets"] = 0
        Drivers_produce["Total RC's and Pallets"] = (
            Drivers_produce["Replenished Rollcages"]
            + Drivers_produce["Replenished Pallets"]
            + Drivers_produce["Backstock Rollcages"]
            + Drivers_produce["Backstock Pallets"]
            + Drivers_produce["Bulk Pallets"]
        ).astype("float32")
        Drivers_produce["Total Green Crates"] = (
            Drivers_produce["Green crates case fill"]
            + Drivers_produce["Green crates unit fill"]
        )

        Drivers_produce["Add_Walking Backstock Cages"] = (
            Drivers_produce["Backstock Rollcages"]
        ).astype("float32")
        Drivers_produce["Add_Walking Cages"] = (
            Drivers_produce["Replenished Rollcages"]
        ).astype("float32")
        Drivers_produce["Add_Walking Pallets"] = Drivers_produce["Replenished Pallets"]

        Drivers_produce.replace([np.inf, -np.inf], 0, inplace=True)
        Drivers_produce.replace(np.nan, 0, inplace=True)

        Drivers_produce = Drivers_produce[
            [
                "country",
                "store",
                "day",
                "dep",
                "pmg",
                "tpnb",
                "sold_units",
                "sales",
                "stock",
                "cases_delivered",
                "Backstock Rollcages",
                "Backstock Cases",
                "L_Backstock Cases",
                "H_Backstock Cases",
                "Backstock Pallets",
                "Banana Cases",
                "Banana Shelves Cases",
                "Banana Hammock Bunch",
                "Cut Flowers",
                "Flowers Rollcages",
                "Flowers Pallets",
                "Bulk Pallets",
                "Total RC's and Pallets",
                "Total Green Crates",
                "Add_Walking Backstock Cages",
                "Add_Walking Cages",
                "Add_Walking Pallets",
                "Potted Plants Cases",
                "Potted Plants Items",
                "Empty Rollcages",
                "Empty Pallets",
                "New Delivery - Rollcages",
                "New Delivery - Pallets",
                "Green crates case fill",
                "L_Green crates case fill",
                "H_Green crates case fill",
                "Green crates unit fill",
                "Green crates unit fill items",
                "Bulk Product Cases",
                "Pre-sorted Rollcages",
                "L_Pre-sorted Crates",
                "H_Pre-sorted Crates",
            ]
        ]
        Drivers_produce = optimize_objects(optimize_types(Drivers_produce))

    if len(Drivers_produce) == 0:
        Drivers_produce = Drivers_produce[["store", "tpnb"]]

    return Drivers_produce, Drivers


def Repl_Drivers_Calculation_TPN(
    directory,
    Repl_Dataset,
    store_inputs,
    backstock_target,
    RC_Capacity_Ratio,
    shelf_trolley_cap_ratio_to_pallet,
    shelf_trolley_cap_ratio_to_rollcage,
    excel_inputs_f,
    MODULE_CRATES,
    TABLE_CRATES,
    FULFILL_TARGET,
    SALES_CYCLE,
    RC_CAPACITY,
    RC_DELIVERY,
    RC_VS_PAL_CAPACITY,
    only_tpn,
    tpnb_store,
    tpnb_country,
    selected_tpn,
    capping_shelves_ratio,
    stores,
):
    # Run the Replenishment Model:

    time_start = time.time()

    # Finalizing Drivers
    Drivers = pd.DataFrame()
    Drivers_produce = pd.DataFrame()

    index = 0
    stores_to_iterate = list()
    for x in stores:

        stores_to_iterate.append(x)
        index += 1

        if index % 200 == 0 or (len(stores) == index):

            Drivers_produce_part, Drivers_part = Repl_Drivers_Calculation(
                directory,
                Repl_Dataset,
                store_inputs,
                backstock_target,
                RC_Capacity_Ratio,
                shelf_trolley_cap_ratio_to_pallet,
                shelf_trolley_cap_ratio_to_rollcage,
                excel_inputs_f,
                MODULE_CRATES,
                TABLE_CRATES,
                FULFILL_TARGET,
                SALES_CYCLE,
                RC_CAPACITY,
                RC_DELIVERY,
                RC_VS_PAL_CAPACITY,
                only_tpn,
                tpnb_store,
                tpnb_country,
                selected_tpn,
                capping_shelves_ratio,
                stores_to_iterate,
            )

            Drivers = pd.concat([Drivers, Drivers_part])
            Drivers_produce = pd.concat([Drivers_produce, Drivers_produce_part])

            # print("{} are done!".format(stores_to_iterate))
            stores_to_iterate = list()

    time_stop = time.time()
    print(
        "Replenishment Drivers table is ready. Executed Time (sec & min): {:,.2f} sec which is {:,.1f} min".format(
            time_stop - time_start, (time_stop - time_start) / 60
        )
    )

    return Drivers, Drivers_produce


@timeit
def RTC_Waste_Food_Donation(directory, losses_f, Repl_Dataset):
    ## waste_calc tpn to dep

    rtc_waste_foodbank = optimize_objects(
        optimize_types(pq.read_table(directory / losses_f).to_pandas())
    )
    rtc_waste_foodbank = rtc_waste_foodbank[
        [
            "store",
            "day",
            "dep",
            "tpnb",
            "RTC Lines",
            "Waste Lines",
            "Food Donation Lines",
            "RTC Items",
            "Waste Items",
            "Food Donation Items",
            "Waste Bulk (one bag)",
            "Food Donation Bulk (one bag)",
            "RTC (Produce Bags)",
            "Food Donation (available)",
        ]
    ]

    # lines
    rtc_lines = (
        (
            rtc_waste_foodbank[rtc_waste_foodbank["RTC Lines"] == 1]
            .groupby(["store", "dep", "day"])["tpnb"]
            .nunique()
            / 14
        )
        .reset_index()
        .rename(columns={"tpnb": "RTC Lines"})
    )
    waste_lines = (
        (
            rtc_waste_foodbank[rtc_waste_foodbank["Waste Lines"] == 1]
            .groupby(["store", "dep", "day",])["tpnb"]
            .nunique()
            / 14
        )
        .reset_index()
        .rename(columns={"tpnb": "Waste Lines"})
    )
    food_donation_lines = (
        (
            rtc_waste_foodbank[rtc_waste_foodbank["Food Donation Lines"] == 1]
            .groupby(["store", "dep", "day"])["tpnb"]
            .nunique()
            / 14
        )
        .reset_index()
        .rename(columns={"tpnb": "Food Donation Lines"})
    )
    food_donation_available = rtc_waste_foodbank[
        rtc_waste_foodbank["Food Donation (available)"] == 1
    ][["store", "dep", "day"]].drop_duplicates()
    food_donation_available["Food Donation (available)"] = 1

    # items
    rtc_items = (
        rtc_waste_foodbank[rtc_waste_foodbank["RTC Items"] > 0]
        .groupby(["store", "dep", "day"])["RTC Items"]
        .sum()
        .reset_index()
    )
    waste_items = (
        rtc_waste_foodbank[rtc_waste_foodbank["Waste Items"] > 0]
        .groupby(["store", "dep", "day",])["Waste Items"]
        .sum()
        .reset_index()
    )
    food_donation_items = (
        rtc_waste_foodbank[rtc_waste_foodbank["Food Donation Items"] > 0]
        .groupby(["store", "dep", "day"])["Food Donation Items"]
        .sum()
        .reset_index()
    )
    waste_bulk_one_bag = (
        rtc_waste_foodbank[rtc_waste_foodbank["Waste Bulk (one bag)"] > 0]
        .groupby(["store", "dep", "day"])["Waste Bulk (one bag)"]
        .sum()
        .reset_index()
    )
    food_bulk_one_bag = (
        rtc_waste_foodbank[rtc_waste_foodbank["Food Donation Bulk (one bag)"] > 0]
        .groupby(["store", "dep", "day"])["Food Donation Bulk (one bag)"]
        .sum()
        .reset_index()
    )
    RTC_produce_bag = (
        rtc_waste_foodbank[rtc_waste_foodbank["RTC (Produce Bags)"] > 0]
        .groupby(["store", "dep", "day"])["RTC (Produce Bags)"]
        .sum()
        .reset_index()
    )

    waste_var = [
        rtc_lines,
        waste_lines,
        food_donation_lines,
        food_donation_available,
        rtc_items,
        waste_items,
        food_donation_items,
        waste_bulk_one_bag,
        food_bulk_one_bag,
        RTC_produce_bag,
    ]

    active_lines = (
        Repl_Dataset.groupby(["store", "dep", "day",])["tpnb"]
        .nunique()
        .apply(lambda x: int(x / 7))
        .reset_index()
        .rename(columns={"tpnb": "Active_Lines"})
    )

    return waste_var, active_lines


@timeit
def TPN_level_To_Dep_level(
    directory,
    excel_inputs_f,
    store_inputs,
    Drivers,
    Drivers_produce,
    RC_Capacity_Ratio,
    backstock_target,
    active_lines,
    waste_var,
):
    # TPN TO dep
    weekdays = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
    ]
    store_dep_day_base = store_inputs[["Store", "Dep"]].drop_duplicates()
    store_dep_day_base = store_dep_day_base[
        store_dep_day_base.Store.isin(list(Drivers.store.unique()))
    ]
    store_dep_day_base["day"] = ""
    store_dep_day_base["day"] = store_dep_day_base["day"].apply(lambda x: weekdays)
    store_dep_day_base = store_dep_day_base.explode("day").drop_duplicates()
    store_dep_day_base.columns = [x.lower() for x in store_dep_day_base.columns]

    # pallet_cap_dep_avg = Drivers.groupby(['country', 'store', 'pmg'])['pallet_capacity'].mean().reset_index()

    # Drivers.drop(['pallet_capacity', 'backstock pallet ratio'], axis=1, inplace=True)

    driver_repl = Drivers.groupby(
        list(Drivers.columns[:5]), as_index=False, observed=True
    ).sum()
    driver_pro = Drivers_produce.groupby(
        list(Drivers_produce.columns[:5]), as_index=False, observed=True
    ).sum()

    drivers_dep = pd.concat([driver_repl, driver_pro]).replace(np.nan, 0)

    drivers_dep = store_dep_day_base.merge(
        drivers_dep, on=["store", "dep", "day"], how="left"
    ).replace(np.nan, 0)
    drivers_dep["pmg"] = np.where(
        drivers_dep["dep"] == "NEW", "HDL01", drivers_dep["pmg"]
    )

    drivers_dep.drop(["tpnb"], axis=1, inplace=True)
    drivers_dep.rename(
        columns={"sold_units": "Items Sold", "cases_delivered": "Cases Delivered"},
        inplace=True,
    )

    drivers_dep = drivers_dep.groupby(
        ["store", "day", "dep"], as_index=False, observed=True
    ).sum()

    ## Import Remittenda SK/CZ cases_delivered, Active_Lines because only HU can be OK in system
    remittenda = pd.read_excel(
        directory / excel_inputs_f,
        "Remittenda",
        usecols=["Store", "Dep", "cases_delivered_per_7", "Active_Lines_per_7"],
    )
    remittenda = remittenda.loc[remittenda.Store.isin(list(Drivers.store.unique()))]
    remittenda["day"] = ""
    remittenda["day"] = remittenda["day"].apply(lambda x: weekdays)
    remittenda = remittenda.explode("day").drop_duplicates()
    remittenda.rename(
        columns={"cases_delivered_per_7": "cases_delivered"}, inplace=True
    )
    remittenda.pallet_capacity = 1000
    remittenda["L_NSRP_Items"] = remittenda["cases_delivered"]
    remittenda["New Delivery - Rollcages"] = (
        (remittenda.cases_delivered / remittenda.pallet_capacity) * RC_Capacity_Ratio
    ).astype("float32")
    remittenda["Store Replenished Cases"] = remittenda["cases_delivered"]
    remittenda["Backstock Rollcages"] = (
        remittenda["Store Replenished Cases"]
        * backstock_target
        / remittenda.pallet_capacity
        * RC_Capacity_Ratio
    )
    remittenda["Total RC's and Pallets"] = (
        remittenda["New Delivery - Rollcages"] + remittenda["Backstock Rollcages"]
    )
    remittenda["Empty Rollcages"] = remittenda["Backstock Rollcages"]
    remittenda["Add_Walking Backstock Cages"] = (
        remittenda["Backstock Rollcages"]
    ).astype("float32")
    remittenda["Add_Walking Cages"] = remittenda["New Delivery - Rollcages"]

    remittenda.rename(columns={"Store": "store", "Dep": "dep"}, inplace=True)

    drivers_dep = drivers_dep[drivers_dep.dep != "NEW"]
    drivers_dep = pd.concat([drivers_dep, remittenda]).replace(np.nan, 0)

    dep_profiles = store_inputs[
        [
            "Store",
            "Dep",
            "Division",
            "Techincal Driver",
            "Trading Days",
            "Fridge Doors",
            "Eggs displayed at UHT Milks",
            "Advertising Headers",
            "Racking",
            "Day Fill",
            "Cardboard Baller",
            "Capping Shelves",
            "Lift Allowance",
            "Distance: WH to SF",
            "Distance: WH to Yard",
            "Steps: SF-Printer",
            "CS_DIST_CSD_2_WH",
            "Steps Dotcom-WH",
            "Cut Melones",
            "Fridge Door Modules",
            "Number of Warehouse Fridges",
            "Number of Modules",
            "promo moduls",
            "HealthyBioFreeFrom modul",
            "Number of Flour Modules",
            "Number of Scales",
            "Number of Pallettes (plano)",
            "Nr. Of broken palletts",
            "Promo Displays",
            "Pallets Delivery Ratio",
            "Nr of car battery",
            "Nr of faulty product (electronic)",
            "Backstock Pallet Ratio",
            "Customers",
            "Fluctuation %",
            "Steps (gates - work)",
            "Time for customers",
            "ProductReturns_factor",
            "Online price changes",
            "Banana Hammock",
            "Fresh CCLB TPN",
            "Night Fill",
            "Red Labels",
            "GBP_rates",
            "Multifloor allowance",
            "Pre-sort by other depts",
            "Stock Movement for Bakery and Counter",
            "Stores without counters",
            "Check Fridge Temperature",
            "MULTIFLOOR",
            "EPW items",
            "EPW Lines",
            "MelonCitrus",
            "Expired Newpapers (TPN)",
            "Remitenda",
            "HU Flags Ratio",
            "HU Flags",
            "Scan and Shop Labels",
            "GM_FREE_SPACE_MODS",
            "GM_FREE_SPACE_AVG_TPN",
            "Weekly non-plano pallett displays",
            "1K_stores_for_ShelfTrolley_extra",
        ]
    ].drop_duplicates()  # , 'BWS_wo_wine_moduls', 'wine_moduls'

    dep_profiles.rename(columns={"Store": "store", "Dep": "dep"}, inplace=True)
    # mods_no = dep_profiles[['store', 'dep','ProductReturns_factor']].drop_duplicates()
    # drivers_dep = drivers_dep.merge(mods_no, on =['store', 'dep'], how = 'left' )
    drivers_dep = drivers_dep.merge(dep_profiles, on=["store", "dep"], how="left")

    drivers_dep["Product Returns"] = np.where(
        drivers_dep.ProductReturns_factor > 0,
        drivers_dep["Items Sold"] / (drivers_dep.ProductReturns_factor),
        0,
    )
    drivers_dep["Modules to go"] = np.where(
        drivers_dep["Number of Modules"] > 66, 66, drivers_dep["Number of Modules"]
    )  # (11*2*3)
    drivers_dep["Modules to go"] = drivers_dep["Modules to go"]
    # drivers_dep.drop(['Number of Modules','ProductReturns_factor' ], axis = 1, inplace = True)
    drivers_dep["Case on Pallet"] = drivers_dep["Cases Delivered"] / (
        drivers_dep["New Delivery - Pallets"]
        + drivers_dep["New Delivery - Rollcages"] * 0.62
    )
    drivers_dep["Case on Pallet"] = drivers_dep["Case on Pallet"].replace(
        np.nan, 7
    )  # if zero then 7 cases on pallet
    drivers_dep["New Delivery - Rollcages"] = np.where(
        drivers_dep.dep == "NEW", 5 / 7, drivers_dep["New Delivery - Rollcages"]
    )

    drivers_dep = drivers_dep.groupby(
        ["store", "day", "dep"], as_index=False, observed=True
    ).sum()

    drivers_dep.rename(columns={"store": "Store", "dep": "Dep"}, inplace=True)

    avg_gap_scan = pd.read_excel(directory / excel_inputs_f, "gapscan")
    avg_gap_scan.rename(columns={"store": "Store", "dep": "Dep"}, inplace=True)
    drivers_dep = drivers_dep.merge(
        avg_gap_scan, on=["Store", "Dep", "day"], how="left"
    ).replace(np.nan, 0)

    # drivers_dep.drop(['RTC Lines', 'Waste Lines', 'Food Donation Lines','Food Donation (available)', 'Active_Lines' ],  axis=1, inplace = True)

    for lines in waste_var:
        lines.rename(columns={"store": "Store", "dep": "Dep"}, inplace=True)
        drivers_dep = drivers_dep.merge(
            lines, on=["Store", "day", "Dep"], how="left"
        ).fillna(0)

    active_lines.rename(columns={"store": "Store", "dep": "Dep"}, inplace=True)
    drivers_dep = drivers_dep.merge(
        active_lines, on=["Store", "day", "Dep"], how="left"
    ).fillna(0)

    drivers_dep["Active_Lines"] = np.where(
        drivers_dep["Active_Lines_per_7"] > 0,
        drivers_dep["Active_Lines_per_7"],
        drivers_dep["Active_Lines"],
    )
    drivers_dep.drop("Active_Lines_per_7", axis=1, inplace=True)

    # New Stores to Benchmark Stores
    news_stores = [11083, 11084, 14218, 14219, 14221, 24164, 24165, 24166]
    benchmark_stores = [11052, 11081, 14200, 14154, 14058, 24152, 24042, 24101]

    benchmark_stores_df = drivers_dep.query("Store in @benchmark_stores")

    for a, b in zip(news_stores, benchmark_stores):

        benchmark_stores_df.loc[benchmark_stores_df.Store == b, "Store"] = a

    drivers_dep = pd.concat([drivers_dep, benchmark_stores_df])

    sales_repl = (
        drivers_dep[["Store", "day", "Dep", "sales"]]
        .drop_duplicates()
        .rename(columns={"sales": "Sales"})
    )

    return drivers_dep, sales_repl


@timeit
def TimeValues_Calculation(directory, store_inputs, drivers_dep, most_f):
    # TIMEVALUE TO DRIVERS

    stores_df = store_inputs[
        ["Country", "Store", "Format", "Store Name", "Plan Size"]
    ].drop_duplicates()
    stores_df = stores_df[stores_df.Store.isin(list(drivers_dep.Store.values))]
    storelist_array = stores_df[["Country", "Store", "Format"]].drop_duplicates().values
    shelftrolley_extra_stores = store_inputs[
        ["Country", "Store", "1K_stores_for_ShelfTrolley_extra"]
    ].drop_duplicates()

    most_file = pd.ExcelFile(directory / most_f, engine="pyxlsb")
    activity_list = pd.read_excel(most_file, "Time Values", skiprows=3)

    new_header = activity_list.iloc[0]  # grab the first row for the header
    activity_list = activity_list[1:]  # take the data less the header row
    activity_list.columns = new_header  # set the header row as the df header

    cols = [
        "Activity_key_activities",
        "Suboperation Description",
        "Activity group",
        "V F",
        "DRIVER_1",
        "DRIVER_2",
        "FREQ2",
        "DRIVER_3",
        "DRIVER_4",
        "PROFILE",
        "RA",
        "Head",
        "Newspaper_Activity",
    ]
    cols2 = [
        "Activity_key_activities",
        "Suboperation",
        "Activity_Group",
        "V_F",
        "Driver_1",
        "Driver_2",
        "Freq_Driver_2",
        "Driver_3",
        "Driver_4",
        "Profile",
        "RA",
        "Head",
        "Newspaper_Activity",
    ]
    activity_list = activity_list[cols]
    for x, y in zip(cols, cols2):
        activity_list.rename(columns={x: y}, inplace=True)

    activity_list.dropna(subset=["Activity_key_activities"], inplace=True)
    activity_list.rename(
        columns={"Activity_key_activities": "Activity_key"}, inplace=True
    )
    activity_list["Freq_Driver_2"] = activity_list["Freq_Driver_2"].replace(np.nan, 0)
    activity_list = activity_list.replace(np.nan, "no_driver")

    activities = activity_list[["Activity_key"]].copy()
    activities["Country"] = ""
    activities["Format"] = ""
    activities["Dep"] = ""
    activities["Store"] = 0
    activities["day"] = ""

    times = pd.read_excel(most_file, "TimeValues_Py", usecols="M:R")
    times.dropna(subset=["Activity_key_times"], inplace=True)
    times.rename(columns={"Activity_key_times": "Activity_key"}, inplace=True)
    # times.drop(times[times.basic_time == 0].index, inplace = True)
    # times.drop(times[times.freq == 0].index, inplace = True)

    times_array = activities.values
    departments = pd.DataFrame(
        ["DRY", "HEA", "BWS", "DAI", "PPD", "FRZ", "PRO", "HDL", "NEW"], columns=["Dep"]
    )
    dep_array = departments.values

    week_df = pd.DataFrame([a for a in drivers_dep["day"].unique()], columns=["day"])
    weekdays_array = week_df.values

    df_times = pd.DataFrame(columns=activities.columns)
    result = (
        len(storelist_array) * len(weekdays_array) * len(dep_array) * len(times_array)
    )
    df_array = np.empty([result, 6], dtype="object")  # create an empty array
    counter = 0
    for a in range(len(times_array)):
        for d in range(len(dep_array)):
            for s in range(len(storelist_array)):
                for w in range(len(weekdays_array)):
                    df_array[counter][0] = times_array[a][0]  # activity name
                    df_array[counter][3] = dep_array[d][0]  # department
                    df_array[counter][1] = storelist_array[s][0]  # country
                    df_array[counter][2] = storelist_array[s][2]  # format
                    df_array[counter][4] = storelist_array[s][1]  # store
                    df_array[counter][5] = weekdays_array[w][0]  # day
                    counter += 1
    df_times = pd.concat([df_times, pd.DataFrame(df_array, columns=df_times.columns)])
    df_times = df_times.merge(
        times, on=["Activity_key", "Country", "Format", "Dep"], how="left"
    )
    df_times = df_times.merge(activity_list, on=["Activity_key"], how="left")
    df_times.Store = pd.to_numeric(df_times.Store, errors="coerce")

    ######### ShelfTrolley 1K stores extra hours settings #########

    freq_Shelftrolley_extra = times[
        (times["Activity_key"].str.contains("shelf trolley"))
        & (times["Activity_key"].str.contains("Stock Movement"))
        & (times["Format"] == "Express")
    ][["Activity_key", "Country", "Dep", "freq"]].drop_duplicates()

    df_times = df_times.merge(
        shelftrolley_extra_stores, on=["Country", "Store"], how="left"
    )

    only_1k_stores_for_shelfTrolley = df_times[
        df_times["1K_stores_for_ShelfTrolley_extra"] == 1
    ]

    freq_Shelftrolley_extra = freq_Shelftrolley_extra[
        freq_Shelftrolley_extra.Country.isin(df_times.Country.unique().tolist())
    ]
    df_times = df_times[df_times["1K_stores_for_ShelfTrolley_extra"] == 0]
    dict_list = (
        freq_Shelftrolley_extra.groupby(["Activity_key", "Country", "Dep"])["freq"]
        .apply(lambda s: s.tolist())
        .to_dict()
    )

    for key, value in dict_list.items():
        only_1k_stores_for_shelfTrolley.loc[
            (only_1k_stores_for_shelfTrolley["Activity_key"] == key[0])
            & (only_1k_stores_for_shelfTrolley["Country"] == key[1])
            & (only_1k_stores_for_shelfTrolley["Dep"] == key[2]),
            "freq",
        ] = value[0]

    df_times = pd.concat([df_times, only_1k_stores_for_shelfTrolley])

    df_times.drop("1K_stores_for_ShelfTrolley_extra", axis=1, inplace=True)

    #### MERGING PART
    drivers_dep = drivers_dep.groupby(
        ["Store", "day", "Dep"], as_index=False, observed=True
    ).sum()
    drivers_dep_ = drivers_dep.melt(
        id_vars=["Store", "Dep", "day"], var_name=["drivers"]
    )
    drivers_dep_.value = pd.to_numeric(drivers_dep_.value, errors="coerce").replace(
        np.nan, 0
    )

    d_values = [
        1,
        2,
        3,
        4,
    ]  # Here we VLOOKUP driver values between df_times and drivers_df. We have 4 drivers
    driver_initial_name = "drivers"
    value_initial_name = "value"
    for x in d_values:
        driver_new_name = "Driver_" + str(x)
        value_new_name = "Driver_" + str(x) + "_value"
        drivers_dep_.rename(
            columns={driver_initial_name: driver_new_name}, inplace=True
        )
        drivers_dep_.rename(columns={value_initial_name: value_new_name}, inplace=True)
        df_times = df_times.merge(
            drivers_dep_, on=["Store", "Dep", "day", driver_new_name], how="left"
        )
        df_times[value_new_name] = df_times[value_new_name].replace(
            np.nan, 0
        )  # it seems we need NaN there
        driver_initial_name = driver_new_name
        value_initial_name = value_new_name
    driver_new_name = "Profile"  # Profiles
    value_new_name = "Profile_value"
    drivers_dep_.rename(columns={driver_initial_name: driver_new_name}, inplace=True)
    drivers_dep_.rename(columns={value_initial_name: value_new_name}, inplace=True)

    df_times = df_times.merge(
        drivers_dep_, on=["Store", "Dep", "day", driver_new_name], how="left"
    )
    df_times[value_new_name] = df_times[value_new_name].replace(
        np.nan, 0
    )  # it seems we need NaN there
    drivers_dep_.rename(columns={driver_new_name: "drivers"}, inplace=True)
    drivers_dep_.rename(columns={value_new_name: "value"}, inplace=True)

    df_times = df_times.loc[(df_times.basic_time > 0)]
    df_times = df_times.loc[(df_times.freq > 0)]

    return df_times


@timeit
def Model_Hours_Calculation(
    directory, excel_inputs_f, store_inputs, df_times, REX_ALLOWANCE
):
    def CalcModelHours(calc_hours):

        calc_hours.Driver_3_value = np.where(
            (calc_hours.Driver_3_value == 0) & (calc_hours.Driver_3 == "no_driver"),
            1,
            calc_hours.Driver_3_value,
        )  # here we have multiplicators and as we cannot divide by 0, I changed the zeros to 1
        calc_hours.Driver_4_value = np.where(
            (calc_hours.Driver_4_value == 0) & (calc_hours.Driver_4 == "no_driver"),
            1,
            calc_hours.Driver_4_value,
        )
        calc_hours["hours"] = (
            (
                (
                    calc_hours.Driver_1_value
                    + (calc_hours.Driver_2_value * calc_hours.Freq_Driver_2 / 100)
                )
                * calc_hours.Driver_3_value
                * calc_hours.Driver_4_value
            )
            * calc_hours.basic_time
            / 60
            * calc_hours.freq
            / 100
        )
        calc_hours["hours"] = np.where(
            (calc_hours.Profile_value == 0) & (calc_hours.Profile != "no_driver"),
            0,
            calc_hours["hours"],
        )

        return calc_hours

    hours_df = df_times.copy()
    hours_df["RA_time"] = np.where(
        hours_df.RA == "Y", hours_df.basic_time * (REX_ALLOWANCE / 100), 0
    )
    hours_df["basic_time"] = hours_df.basic_time + hours_df.RA_time
    hours_df.drop(columns={"RA_time"}, axis=1, inplace=True)

    divide_by_7_drivers = pd.read_excel(
        directory / excel_inputs_f, "drivers_to_divide_7"
    )
    col_name_1 = ["Driver_1", "Driver_2"]
    col_name_2 = ["Driver_1_value", "Driver_2_value"]
    for x, y in zip(col_name_1, col_name_2):
        hours_df = hours_df.merge(divide_by_7_drivers, on=x, how="left").replace(
            np.nan, 0
        )
        hours_df[y] = np.where(hours_df.flag == 1, hours_df[y] / 7, hours_df[y])
        hours_df.drop(["flag"], axis=1, inplace=True)
        divide_by_7_drivers.rename(columns={x: "Driver_2"}, inplace=True)

    hours_df = CalcModelHours(hours_df)
    df = hours_df.loc[
        hours_df.Activity_Group == "Stock Movement",
        ["Store", "Dep", "day", "Activity_Group", "hours"],
    ].copy()
    df["add_hours"] = np.where(
        ((df.Activity_Group == "Stock Movement") & (df.Dep != "PRO")), df.hours * 0.2, 0
    )  # for Movement without an equipment
    df = df[["Store", "Dep", "day", "add_hours"]]
    df = df.groupby(["Store", "Dep", "day"])["add_hours"].sum().reset_index()
    hours_df = hours_df.merge(df, on=["Store", "Dep", "day"], how="left")
    hours_df["Driver_1_value"] = np.where(
        hours_df.Driver_1 == "Movement without an equipment",
        hours_df.add_hours,
        hours_df["Driver_1_value"],
    )
    hours_df["Driver_2_value"] = np.where(
        hours_df.Driver_2 == "Movement without an equipment",
        hours_df.add_hours,
        hours_df["Driver_2_value"],
    )
    hours_df["Driver_3_value"] = np.where(
        hours_df.Driver_3 == "Movement without an equipment",
        hours_df.add_hours,
        hours_df["Driver_3_value"],
    )
    hours_df["Driver_4_value"] = np.where(
        hours_df.Driver_4 == "Movement without an equipment",
        hours_df.add_hours,
        hours_df["Driver_4_value"],
    )
    hours_df.drop(columns={"add_hours"}, axis=1, inplace=True)
    hours_df = CalcModelHours(hours_df)

    # Headcount calculation
    headcount_hrs = hours_df.loc[
        hours_df.Head == 1, ("Store", "Dep", "day", "Head", "hours")
    ]
    headcount_hrs = (
        headcount_hrs.groupby(["Store", "Dep", "day"])["hours"].sum().reset_index()
    )
    headcount_hrs["Headcount"] = np.where(
        (((headcount_hrs.hours / 8) - round(headcount_hrs.hours / 8)) > 0.05),
        np.ceil(headcount_hrs.hours / 8) / 7,
        round(headcount_hrs.hours / 8) / 7,
    )
    headcount_hrs.drop(columns={"hours"}, axis=1, inplace=True)
    hours_df = hours_df.merge(headcount_hrs, on=["Store", "Dep", "day"], how="left")
    hours_df["Driver_1_value"] = np.where(
        hours_df.Driver_1 == "Headcount", hours_df.Headcount, hours_df["Driver_1_value"]
    )
    hours_df["Driver_2_value"] = np.where(
        hours_df.Driver_2 == "Headcount", hours_df.Headcount, hours_df["Driver_2_value"]
    )
    hours_df["Driver_3_value"] = np.where(
        hours_df.Driver_3 == "Headcount", hours_df.Headcount, hours_df["Driver_3_value"]
    )
    hours_df["Driver_4_value"] = np.where(
        hours_df.Driver_4 == "Headcount", hours_df.Headcount, hours_df["Driver_4_value"]
    )
    hours_df.drop(columns={"Headcount"}, axis=1, inplace=True)
    hours_df = CalcModelHours(hours_df)
    dep_profiles = store_inputs[
        [
            "Store",
            "Dep",
            "Division",
            "Techincal Driver",
            "Trading Days",
            "Fridge Doors",
            "Eggs displayed at UHT Milks",
            "Advertising Headers",
            "Racking",
            "Day Fill",
            "Cardboard Baller",
            "Capping Shelves",
            "Lift Allowance",
            "Distance: WH to SF",
            "Distance: WH to Yard",
            "Steps: SF-Printer",
            "CS_DIST_CSD_2_WH",
            "Steps Dotcom-WH",
            "Cut Melones",
            "Fridge Door Modules",
            "Number of Warehouse Fridges",
            "Number of Modules",
            "promo moduls",
            "HealthyBioFreeFrom modul",
            "Number of Flour Modules",
            "Number of Scales",
            "Number of Pallettes (plano)",
            "Nr. Of broken palletts",
            "Promo Displays",
            "Pallets Delivery Ratio",
            "Nr of car battery",
            "Nr of faulty product (electronic)",
            "Backstock Pallet Ratio",
            "Customers",
            "Fluctuation %",
            "Steps (gates - work)",
            "Time for customers",
            "ProductReturns_factor",
            "Online price changes",
            "Banana Hammock",
            "Fresh CCLB TPN",
            "Night Fill",
            "Red Labels",
            "GBP_rates",
            "Multifloor allowance",
            "Pre-sort by other depts",
            "Stock Movement for Bakery and Counter",
            "Stores without counters",
            "Check Fridge Temperature",
            "MULTIFLOOR",
            "EPW items",
            "EPW Lines",
            "MelonCitrus",
            "Expired Newpapers (TPN)",
            "Remitenda",
            "HU Flags Ratio",
            "HU Flags",
            "Scan and Shop Labels",
            "GM_FREE_SPACE_MODS",
            "GM_FREE_SPACE_AVG_TPN",
            "Weekly non-plano pallett displays",
            "1K_stores_for_ShelfTrolley_extra",
        ]
    ].drop_duplicates()  # , 'BWS_wo_wine_moduls', 'wine_moduls'
    division_df = dep_profiles[
        ["Store", "Dep", "Division", "GBP_rates"]
    ].drop_duplicates()
    hours_df = hours_df.merge(division_df, on=["Store", "Dep"], how="left")
    hours_df["Yearly GBP"] = hours_df.GBP_rates * hours_df.hours * 52

    return hours_df


def move_column_inplace(df, col, pos):
    col = df.pop(col)
    df.insert(pos, col.name, col)


def TimeValues_Calculation_TPN(
    directory, store_inputs, driver_pro, driver_repl, most_f
):
    # TIMEVALUE TO DRIVERS

    # TIMEVALUE TO DRIVERS

    drivers_tpnb = pd.concat([driver_repl, driver_pro]).replace(np.nan, 0)
    drivers_tpnb["Case on Pallet"] = drivers_tpnb["cases_delivered"] / (
        drivers_tpnb["New Delivery - Pallets"]
        + drivers_tpnb["New Delivery - Rollcages"] * 0.62
    )
    drivers_tpnb["Case on Pallet"] = drivers_tpnb["Case on Pallet"].replace(
        np.nan, 7
    )  # if zero then 7 cases on pallet
    drivers_tpnb["Modules to go"] = 1

    drivers_tpnb.rename(columns={"cases_delivered": "Cases Delivered"}, inplace=True)

    dep_profiles = store_inputs[
        [
            "Store",
            "Dep",
            "Fridge Doors",
            "Eggs displayed at UHT Milks",
            "Racking",
            "Cardboard Baller",
            "Capping Shelves",
            "Lift Allowance",
            "Distance: WH to SF",
            "Distance: WH to Yard",
            "Steps: SF-Printer",
            "CS_DIST_CSD_2_WH",
            "Steps Dotcom-WH",
            "Steps (gates - work)",
            "Banana Hammock",
            "GBP_rates",
        ]
    ].drop_duplicates()

    dep_profiles.rename(columns={"Store": "store", "Dep": "dep"}, inplace=True)
    drivers_tpnb = drivers_tpnb.merge(dep_profiles, on=["store", "dep"], how="left")

    Final_drivers_for_TPN_level = drivers_tpnb.copy()

    stores_df = store_inputs[
        ["Country", "Store", "Format", "Store Name", "Plan Size"]
    ].drop_duplicates()
    stores_df = stores_df[stores_df.Store.isin(list(set(drivers_tpnb.store.values)))]
    storelist_array = stores_df[["Country", "Store", "Format"]].drop_duplicates().values
    shelftrolley_extra_stores = store_inputs[
        ["Country", "Store", "1K_stores_for_ShelfTrolley_extra"]
    ].drop_duplicates()

    most_file = pd.ExcelFile(directory / most_f, engine="pyxlsb")
    activity_list = pd.read_excel(most_file, "Time Values", skiprows=3)

    new_header = activity_list.iloc[0]  # grab the first row for the header
    activity_list = activity_list[1:]  # take the data less the header row
    activity_list.columns = new_header  # set the header row as the df header

    cols = [
        "Activity_key_activities",
        "Suboperation Description",
        "Activity group",
        "V F",
        "DRIVER_1",
        "DRIVER_2",
        "FREQ2",
        "DRIVER_3",
        "DRIVER_4",
        "PROFILE",
        "RA",
        "Head",
        "Newspaper_Activity",
    ]
    cols2 = [
        "Activity_key_activities",
        "Suboperation",
        "Activity_Group",
        "V_F",
        "Driver_1",
        "Driver_2",
        "Freq_Driver_2",
        "Driver_3",
        "Driver_4",
        "Profile",
        "RA",
        "Head",
        "Newspaper_Activity",
    ]
    activity_list = activity_list[cols]
    for x, y in zip(cols, cols2):
        activity_list.rename(columns={x: y}, inplace=True)

    activity_list.dropna(subset=["Activity_key_activities"], inplace=True)
    activity_list.rename(
        columns={"Activity_key_activities": "Activity_key"}, inplace=True
    )
    activity_list["Freq_Driver_2"] = activity_list["Freq_Driver_2"].replace(np.nan, 0)
    activity_list = activity_list.replace(np.nan, "no_driver")

    activities = activity_list[["Activity_key"]].copy()
    activities["Country"] = ""
    activities["Format"] = ""
    activities["Dep"] = ""
    activities["Store"] = 0
    activities["day"] = ""

    times = pd.read_excel(most_file, "TimeValues_Py", usecols="M:R")
    times.dropna(subset=["Activity_key_times"], inplace=True)
    times.rename(columns={"Activity_key_times": "Activity_key"}, inplace=True)
    # times.drop(times[times.basic_time == 0].index, inplace = True)
    # times.drop(times[times.freq == 0].index, inplace = True)

    freq_Shelftrolley_extra = times[
        (times["Activity_key"].str.contains("shelf trolley"))
        & (times["Format"] == "Express")
    ][["Activity_key", "Country", "Dep", "freq"]].drop_duplicates()

    times_array = activities.values
    departments = pd.DataFrame([x for x in drivers_tpnb.dep.unique()], columns=["Dep"])
    dep_array = departments.values

    week_df = pd.DataFrame([a for a in drivers_tpnb["day"].unique()], columns=["day"])
    weekdays_array = week_df.values

    df_times = pd.DataFrame(columns=activities.columns)
    result = (
        len(storelist_array) * len(weekdays_array) * len(dep_array) * len(times_array)
    )
    df_array = np.empty([result, 6], dtype="object")  # create an empty array
    counter = 0
    for a in range(len(times_array)):
        for d in range(len(dep_array)):
            for s in range(len(storelist_array)):
                for w in range(len(weekdays_array)):
                    df_array[counter][0] = times_array[a][0]  # activity name
                    df_array[counter][3] = dep_array[d][0]  # department
                    df_array[counter][1] = storelist_array[s][0]  # country
                    df_array[counter][2] = storelist_array[s][2]  # format
                    df_array[counter][4] = storelist_array[s][1]  # store
                    df_array[counter][5] = weekdays_array[w][0]  # day
                    counter += 1
    df_times = pd.concat([df_times, pd.DataFrame(df_array, columns=df_times.columns)])
    df_times = df_times.merge(
        times, on=["Activity_key", "Country", "Format", "Dep"], how="left"
    )
    df_times = df_times.merge(activity_list, on=["Activity_key"], how="left")
    df_times.Store = pd.to_numeric(df_times.Store, errors="coerce")

    ######### ShelfTrolley 1K stores extra hours settings #########

    df_times = df_times.merge(
        shelftrolley_extra_stores, on=["Country", "Store"], how="left"
    )

    only_1k_stores_for_shelfTrolley = df_times[
        df_times["1K_stores_for_ShelfTrolley_extra"] == 1
    ]

    freq_Shelftrolley_extra = freq_Shelftrolley_extra[
        freq_Shelftrolley_extra.Country.isin(df_times.Country.unique().tolist())
    ]
    df_times = df_times[df_times["1K_stores_for_ShelfTrolley_extra"] == 0]
    dict_list = (
        freq_Shelftrolley_extra.groupby(["Activity_key", "Country", "Dep"])["freq"]
        .apply(lambda s: s.tolist())
        .to_dict()
    )

    for key, value in dict_list.items():
        only_1k_stores_for_shelfTrolley.loc[
            (only_1k_stores_for_shelfTrolley["Activity_key"] == key[0])
            & (only_1k_stores_for_shelfTrolley["Country"] == key[1])
            & (only_1k_stores_for_shelfTrolley["Dep"] == key[2]),
            "freq",
        ] = value[0]

    df_times = pd.concat([df_times, only_1k_stores_for_shelfTrolley])

    df_times.drop("1K_stores_for_ShelfTrolley_extra", axis=1, inplace=True)

    #### MERGING PART

    drivers_tpnb.rename(
        columns={
            "store": "Store",
            "dep": "Dep",
            "pmg": "Pmg",
            "tpnb": "Tpnb",
            "country": "Country",
        },
        inplace=True,
    )

    # drivers_tpnb = drivers_tpnb.groupby(['Store','day','Dep', 'Pmg', 'Tpnb'], as_index = False,observed=True ).sum()
    drivers_tpnb = drivers_tpnb.melt(
        id_vars=["Country", "Store", "Dep", "Pmg", "Tpnb", "day"], var_name=["drivers"]
    )
    drivers_tpnb.value = pd.to_numeric(drivers_tpnb.value, errors="coerce").replace(
        np.nan, 0
    )

    flag_driver = pd.DataFrame({"Driver": drivers_tpnb.drivers.unique(), "flag": 1})
    d_values = [
        1,
        2,
        3,
        4,
    ]  # Here we VLOOKUP driver values between df_times and drivers_df to filter only what we got in drivers_tpnb
    driver_initial_name = "Driver"
    value_initial_name = "flag"
    for x in d_values:
        driver_new_name = "Driver_" + str(x)
        value_new_name = "flag_" + str(x)
        flag_driver.rename(columns={driver_initial_name: driver_new_name}, inplace=True)
        flag_driver.rename(columns={value_initial_name: value_new_name}, inplace=True)
        df_times = df_times.merge(flag_driver, on=[driver_new_name], how="left")
        df_times[value_new_name] = df_times[value_new_name].replace(
            np.nan, 0
        )  # it seems we need NaN there
        driver_initial_name = driver_new_name
        value_initial_name = value_new_name

    df_times["flag_total"] = (
        df_times.flag_1 + df_times.flag_2 + df_times.flag_3 + df_times.flag_4
    )
    df_times = df_times[df_times.flag_total > 0]
    df_times = df_times[df_times.flag_1 > 0]
    df_times.drop(
        ["flag_1", "flag_2", "flag_3", "flag_4", "flag_total"], axis=1, inplace=True
    )

    #### TPN to df_times
    store_tpnb_to_merge = drivers_tpnb.iloc[:, :5].drop_duplicates()
    df_times = df_times.merge(
        store_tpnb_to_merge, on=["Country", "Store", "Dep"], how="left"
    )
    df_times = df_times[df_times.Tpnb.notnull()]

    #### Arrange Columns

    columns_to_move = ["Country", "Pmg", "Tpnb"]

    for c, p in zip(columns_to_move, [0, 5, 6]):

        move_column_inplace(df_times, c, p)

    d_values = [
        1,
        2,
        3,
        4,
    ]  # Here we VLOOKUP driver values between df_times and drivers_df. We have 4 drivers
    driver_initial_name = "drivers"
    value_initial_name = "value"
    for x in d_values:
        driver_new_name = "Driver_" + str(x)
        value_new_name = "Driver_" + str(x) + "_value"
        drivers_tpnb.rename(
            columns={driver_initial_name: driver_new_name}, inplace=True
        )
        drivers_tpnb.rename(columns={value_initial_name: value_new_name}, inplace=True)
        df_times = df_times.merge(
            drivers_tpnb,
            on=["Country", "Store", "Tpnb", "Dep", "Pmg", "day", driver_new_name],
            how="left",
        )
        df_times[value_new_name] = df_times[value_new_name].replace(
            np.nan, 0
        )  # it seems we need NaN there
        driver_initial_name = driver_new_name
        value_initial_name = value_new_name
    driver_new_name = "Profile"  # Profiles
    value_new_name = "Profile_value"
    drivers_tpnb.rename(columns={driver_initial_name: driver_new_name}, inplace=True)
    drivers_tpnb.rename(columns={value_initial_name: value_new_name}, inplace=True)

    df_times = df_times.merge(
        drivers_tpnb,
        on=["Country", "Store", "Tpnb", "Dep", "Pmg", "day", driver_new_name],
        how="left",
    )
    df_times[value_new_name] = df_times[value_new_name].replace(
        np.nan, 0
    )  # it seems we need NaN there
    drivers_tpnb.rename(columns={driver_new_name: "drivers"}, inplace=True)
    drivers_tpnb.rename(columns={value_new_name: "value"}, inplace=True)

    df_times = df_times.loc[(df_times.basic_time > 0)]
    df_times = df_times.loc[(df_times.freq > 0)]

    return df_times, Final_drivers_for_TPN_level


def Model_Hours_Calculation_TPN(
    directory, excel_inputs_f, store_inputs, df_times, REX_ALLOWANCE
):
    def CalcModelHours(calc_hours):

        calc_hours.Driver_3_value = np.where(
            (calc_hours.Driver_3_value == 0) & (calc_hours.Driver_3 == "no_driver"),
            1,
            calc_hours.Driver_3_value,
        )  # here we have multiplicators and as we cannot divide by 0, I changed the zeros to 1
        calc_hours.Driver_4_value = np.where(
            (calc_hours.Driver_4_value == 0) & (calc_hours.Driver_4 == "no_driver"),
            1,
            calc_hours.Driver_4_value,
        )
        calc_hours["hours"] = (
            (
                (
                    calc_hours.Driver_1_value
                    + (calc_hours.Driver_2_value * calc_hours.Freq_Driver_2 / 100)
                )
                * calc_hours.Driver_3_value
                * calc_hours.Driver_4_value
            )
            * calc_hours.basic_time
            / 60
            * calc_hours.freq
            / 100
        )
        calc_hours["hours"] = np.where(
            (calc_hours.Profile_value == 0) & (calc_hours.Profile != "no_driver"),
            0,
            calc_hours["hours"],
        )

        return calc_hours

    hours_df = df_times.copy()
    hours_df["RA_time"] = np.where(
        hours_df.RA == "Y", hours_df.basic_time * (REX_ALLOWANCE / 100), 0
    )
    hours_df["basic_time"] = hours_df.basic_time + hours_df.RA_time
    hours_df.drop(columns={"RA_time"}, axis=1, inplace=True)

    divide_by_7_drivers = pd.read_excel(
        directory / excel_inputs_f, "drivers_to_divide_7"
    )
    col_name_1 = ["Driver_1", "Driver_2"]
    col_name_2 = ["Driver_1_value", "Driver_2_value"]
    for x, y in zip(col_name_1, col_name_2):
        hours_df = hours_df.merge(divide_by_7_drivers, on=x, how="left").replace(
            np.nan, 0
        )
        hours_df[y] = np.where(hours_df.flag == 1, hours_df[y] / 7, hours_df[y])
        hours_df.drop(["flag"], axis=1, inplace=True)
        divide_by_7_drivers.rename(columns={x: "Driver_2"}, inplace=True)

    hours_df = CalcModelHours(hours_df)
    df = hours_df.loc[
        hours_df.Activity_Group == "Stock Movement",
        ["Store", "Dep", "day", "Activity_Group", "hours"],
    ].copy()
    df["add_hours"] = np.where(
        ((df.Activity_Group == "Stock Movement") & (df.Dep != "PRO")), df.hours * 0.2, 0
    )  # for Movement without an equipment
    df = df[["Store", "Dep", "day", "add_hours"]]
    df = df.groupby(["Store", "Dep", "day"])["add_hours"].sum().reset_index()
    hours_df = hours_df.merge(df, on=["Store", "Dep", "day"], how="left")
    hours_df["Driver_1_value"] = np.where(
        hours_df.Driver_1 == "Movement without an equipment",
        hours_df.add_hours,
        hours_df["Driver_1_value"],
    )
    hours_df["Driver_2_value"] = np.where(
        hours_df.Driver_2 == "Movement without an equipment",
        hours_df.add_hours,
        hours_df["Driver_2_value"],
    )
    hours_df["Driver_3_value"] = np.where(
        hours_df.Driver_3 == "Movement without an equipment",
        hours_df.add_hours,
        hours_df["Driver_3_value"],
    )
    hours_df["Driver_4_value"] = np.where(
        hours_df.Driver_4 == "Movement without an equipment",
        hours_df.add_hours,
        hours_df["Driver_4_value"],
    )
    hours_df.drop(columns={"add_hours"}, axis=1, inplace=True)
    hours_df = CalcModelHours(hours_df)

    # Headcount calculation
    headcount_hrs = hours_df.loc[
        hours_df.Head == 1, ("Store", "Dep", "day", "Head", "hours")
    ]
    headcount_hrs = (
        headcount_hrs.groupby(["Store", "Dep", "day"])["hours"].sum().reset_index()
    )
    headcount_hrs["Headcount"] = np.where(
        (((headcount_hrs.hours / 8) - round(headcount_hrs.hours / 8)) > 0.05),
        np.ceil(headcount_hrs.hours / 8) / 7,
        round(headcount_hrs.hours / 8) / 7,
    )
    headcount_hrs.drop(columns={"hours"}, axis=1, inplace=True)
    hours_df = hours_df.merge(headcount_hrs, on=["Store", "Dep", "day"], how="left")
    hours_df["Driver_1_value"] = np.where(
        hours_df.Driver_1 == "Headcount", hours_df.Headcount, hours_df["Driver_1_value"]
    )
    hours_df["Driver_2_value"] = np.where(
        hours_df.Driver_2 == "Headcount", hours_df.Headcount, hours_df["Driver_2_value"]
    )
    hours_df["Driver_3_value"] = np.where(
        hours_df.Driver_3 == "Headcount", hours_df.Headcount, hours_df["Driver_3_value"]
    )
    hours_df["Driver_4_value"] = np.where(
        hours_df.Driver_4 == "Headcount", hours_df.Headcount, hours_df["Driver_4_value"]
    )
    hours_df.drop(columns={"Headcount"}, axis=1, inplace=True)
    hours_df = CalcModelHours(hours_df)
    dep_profiles = store_inputs[
        [
            "Store",
            "Dep",
            "Division",
            "Techincal Driver",
            "Trading Days",
            "Fridge Doors",
            "Eggs displayed at UHT Milks",
            "Advertising Headers",
            "Racking",
            "Day Fill",
            "Cardboard Baller",
            "Capping Shelves",
            "Lift Allowance",
            "Distance: WH to SF",
            "Distance: WH to Yard",
            "Steps: SF-Printer",
            "CS_DIST_CSD_2_WH",
            "Steps Dotcom-WH",
            "Cut Melones",
            "Fridge Door Modules",
            "Number of Warehouse Fridges",
            "Number of Modules",
            "promo moduls",
            "HealthyBioFreeFrom modul",
            "Number of Flour Modules",
            "Number of Scales",
            "Number of Pallettes (plano)",
            "Nr. Of broken palletts",
            "Promo Displays",
            "Pallets Delivery Ratio",
            "Nr of car battery",
            "Nr of faulty product (electronic)",
            "Backstock Pallet Ratio",
            "Customers",
            "Fluctuation %",
            "Steps (gates - work)",
            "Time for customers",
            "ProductReturns_factor",
            "Online price changes",
            "Banana Hammock",
            "Fresh CCLB TPN",
            "Night Fill",
            "Red Labels",
            "GBP_rates",
            "Multifloor allowance",
            "Pre-sort by other depts",
            "Stock Movement for Bakery and Counter",
            "Stores without counters",
            "Check Fridge Temperature",
            "MULTIFLOOR",
            "EPW items",
            "EPW Lines",
            "MelonCitrus",
            "Expired Newpapers (TPN)",
            "Remitenda",
            "HU Flags Ratio",
            "HU Flags",
            "Scan and Shop Labels",
            "GM_FREE_SPACE_MODS",
            "GM_FREE_SPACE_AVG_TPN",
            "Weekly non-plano pallett displays",
            "1K_stores_for_ShelfTrolley_extra",
        ]
    ].drop_duplicates()  # , 'BWS_wo_wine_moduls', 'wine_moduls'
    division_df = dep_profiles[
        ["Store", "Dep", "Division", "GBP_rates"]
    ].drop_duplicates()
    hours_df = hours_df.merge(division_df, on=["Store", "Dep"], how="left")
    hours_df["GBP_rates"].fillna(method="ffill", inplace=True)
    hours_df["Yearly GBP"] = hours_df.GBP_rates * hours_df.hours * 52
    cats = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
    ]
    hours_df["day"] = pd.Categorical(hours_df["day"], categories=cats, ordered=True)
    hours_df.sort_values(
        by=["Store", "Division", "Activity_Group", "Suboperation", "day"], inplace=True
    )
    return hours_df


@timeit
def OperationProductivityBasics_ON_TPN(
    folder,
    prev_insight,
    repl,
    wh,
    drivers,
    store_tpnb,
    country_tpnb,
    product_names,
    sales_repl,
    sales_wh,
):

    if not (country_tpnb or store_tpnb) == True:
        GBP_rates = (
            repl[["Country", "Format", "GBP_rates"]]
            .drop_duplicates()
            .reset_index(drop=True)
        )
        GBP_rates = GBP_rates[GBP_rates.GBP_rates.notnull()]
        repl.drop(["GBP_rates"], axis=1, inplace=True)
        repl_wh_hours = pd.concat([repl, wh]).reset_index(drop=True)
        repl_wh_hours = repl_wh_hours.merge(
            GBP_rates, on=["Country", "Format"], how="left"
        )
        repl_wh_hours["Yearly GBP"] = (
            repl_wh_hours["GBP_rates"] * repl_wh_hours["hours"]
        ) * 52

        sales_df = pd.concat([sales_repl, sales_wh]).reset_index(drop=True)
        sales_df = sales_df.groupby(["Store", "Dep"])["Sales"].sum().reset_index()
        opb = (
            repl_wh_hours.groupby(
                ["Country", "Store", "Format", "Dep", "Division", "V_F"]
            )
            .agg({"hours": "sum", "Yearly GBP": "sum"})
            .sort_values(["Store", "Division"])
            .reset_index()
        )
        opb_fix = opb[opb.V_F == "F"].rename(
            columns={"hours": "Fix Hours", "Yearly GBP": "Yearly_GBP_fix"}
        )
        opb_fix.drop("V_F", axis=1, inplace=True)
        opb_var = opb[opb.V_F == "V"].rename(
            columns={"hours": "Variable Hours", "Yearly GBP": "Yearly_GBP_var"}
        )
        opb_var.drop("V_F", axis=1, inplace=True)
        opb_dep = opb_fix.merge(
            opb_var, on=["Country", "Store", "Format", "Dep", "Division"], how="inner"
        )
        opb_dep["Total Weekly Hours"] = opb_dep["Fix Hours"] + opb_dep["Variable Hours"]
        opb_dep["Yearly GBP"] = opb_dep.Yearly_GBP_fix + opb_dep.Yearly_GBP_var
        opb_dep.drop(["Yearly_GBP_fix", "Yearly_GBP_var"], axis=1, inplace=True)
        opb_dep = opb_dep.merge(sales_df, on=["Store", "Dep"], how="inner")
        opb_dep["Division"] = opb_dep["Division"].apply(
            lambda x: "General Merchandise" if x == "GM" else x
        )
        opb_dep["Division"] = opb_dep["Division"].apply(
            lambda x: "Prepacked Fresh" if x == "Fresh" else x
        )
        opb_dep["Variable Currency"] = opb_dep["Sales"] / opb_dep["Variable Hours"]

        opb_div = (
            opb_dep.groupby(["Country", "Store", "Format", "Division"])
            .agg(
                {
                    "Fix Hours": "sum",
                    "Variable Hours": "sum",
                    "Total Weekly Hours": "sum",
                    "Yearly GBP": "sum",
                    "Sales": "sum",
                }
            )
            .reset_index()
        )
        opb_div["Variable Currency"] = opb_div["Sales"] / opb_div["Variable Hours"]

        opb_dep.drop("Sales", axis=1, inplace=True)
        opb_div.drop("Sales", axis=1, inplace=True)

        groups = (
            repl_wh_hours.groupby(
                ["Country", "Format", "Store", "Dep", "Activity_Group"]
            )["hours"]
            .sum()
            .reset_index()
            .pivot_table(
                index=["Country", "Format", "Store", "Dep"],
                values="hours",
                columns="Activity_Group",
                aggfunc="sum",
            )
            .reset_index()
        )

        insight_dep = (
            repl_wh_hours.groupby(
                [
                    "Country",
                    "Store",
                    "Format",
                    "Dep",
                    "Division",
                    "Activity_Group",
                    "Suboperation",
                    "Driver_1",
                    "Driver_2",
                    "Driver_3",
                    "Driver_4",
                ]
            )[
                "Driver_1_value",
                "Driver_2_value",
                "Driver_3_value",
                "Driver_4_value",
                "hours",
                "Yearly GBP",
            ]
            .sum()
            .reset_index()
        )
        insight_dep["Model"] = np.where(insight_dep["Dep"] == "Warehouse", "WH", "Repl")
        insight_dep = insight_dep[insight_dep.hours > 0]
        driver_dep = insight_dep[
            [
                "Country",
                "Store",
                "Format",
                "Dep",
                "Division",
                "Suboperation",
                "Driver_1",
                "Driver_1_value",
            ]
        ].drop_duplicates()
        driver_dep = driver_dep[
            [
                "Country",
                "Store",
                "Format",
                "Dep",
                "Division",
                "Driver_1",
                "Driver_1_value",
            ]
        ].drop_duplicates()
        driver_dep = driver_dep.pivot_table(
            index=["Country", "Store", "Format", "Dep", "Division"],
            columns="Driver_1",
            values="Driver_1_value",
            aggfunc="sum",
            fill_value=0,
        ).reset_index()

        opb = opb_dep.merge(
            groups, on=["Country", "Format", "Store", "Dep"], how="left"
        ).fillna(0)
        # opb = opb.merge(driver_dep, on=['Store', 'Dep'], how='left', suffixes=('_activity_group', '_driver'))

        groups_div = (
            repl_wh_hours.groupby(
                ["Country", "Format", "Store", "Division", "Activity_Group"]
            )["hours"]
            .sum()
            .reset_index()
            .pivot_table(
                index=["Country", "Format", "Store", "Division"],
                values="hours",
                columns="Activity_Group",
                aggfunc="sum",
            )
            .reset_index()
        )
        groups_div["Division"] = groups_div["Division"].apply(
            lambda x: "General Merchandise" if x == "GM" else x
        )
        groups_div["Division"] = groups_div["Division"].apply(
            lambda x: "Prepacked Fresh" if x == "Fresh" else x
        )
        opb_div_extended = opb_div.merge(
            groups_div, on=["Country", "Format", "Store", "Division"], how="left"
        ).fillna(0)

        insight = (
            repl_wh_hours.groupby(
                [
                    "Country",
                    "Store",
                    "Format",
                    "Division",
                    "Activity_Group",
                    "Suboperation",
                    "Driver_1",
                    "Driver_2",
                    "Driver_3",
                    "Driver_4",
                ]
            )[
                "Driver_1_value",
                "Driver_2_value",
                "Driver_3_value",
                "Driver_4_value",
                "hours",
                "Yearly GBP",
            ]
            .sum()
            .reset_index()
        )

        insight["Model"] = np.where(insight["Division"] == "Warehouse", "WH", "Repl")

        # insight_newspaper = repl_wh_hours[repl_wh_hours.Dep == "NEW"].groupby(['Country','Store','Format','Division','Activity_Group','Suboperation','Driver_1', 'Driver_2', 'Driver_3', 'Driver_4' ])[ 'Driver_1_value', 'Driver_2_value',
        #                          'Driver_3_value', 'Driver_4_value','hours'].sum().reset_index()
        insight = insight[insight.hours > 0]

        insight_prev = pd.read_parquet(folder / prev_insight)
        insight_prev = insight_prev[
            insight_prev.Store.isin(insight.Store.unique().tolist())
        ]
        insight_diff = insight.merge(
            insight_prev,
            on=[
                "Country",
                "Model",
                "Store",
                "Format",
                "Division",
                "Activity_Group",
                "Suboperation",
                "Driver_1",
                "Driver_2",
                "Driver_3",
                "Driver_4",
            ],
            how="outer",
        ).fillna(0)

        for x in range(1, 5):
            insight_diff[f"diff_driver_{x}"] = (
                insight_diff[f"Driver_{x}_value_x"]
                - insight_diff[f"Driver_{x}_value_y"]
            )
        insight_diff["DIFF_IN_HOURS"] = (
            insight_diff["hours_x"] - insight_diff["hours_y"]
        )
        insight_diff["DIFF_IN_GBP"] = (
            insight_diff["Yearly GBP_x"] - insight_diff["Yearly GBP_y"]
        )

        insight_diff.columns = insight_diff.columns.str.replace("_x", "_new")
        insight_diff.columns = insight_diff.columns.str.replace("_y", "_old")

        insight_diff_act_groups = (
            insight_diff.groupby(["Model", "Country", "Activity_Group"])[
                "DIFF_IN_HOURS"
            ]
            .sum()
            .reset_index()
        )

        # i_repl = insight_diff.query("Activity_Group == 'Replenishment'").groupby(['Country', 'Activity_Group', 'Suboperation'])['DIFF_IN_HOURS'].sum().reset_index()

        final_drivers = drivers.melt(
            id_vars=["Store", "Dep"],
            var_name=["Driver_Names"],
            value_name="Driver_Values",
        ).sort_values(["Store", "Dep"])
        final_drivers = final_drivers[
            final_drivers.Driver_Values != 0
        ]  # remember that 0 means 'No' in the profiles

    if country_tpnb or store_tpnb == True:

        sales_df = drivers[["country", "store", "day", "tpnb", "sales"]].copy()
        sales_df.columns = [
            i.capitalize() if i != "day" else "day" for i in sales_df.columns
        ]
        opb = (
            repl.groupby(
                [
                    "Country",
                    "Format",
                    "Store",
                    "day",
                    "Tpnb",
                    "Pmg",
                    "Dep",
                    "Division",
                    "V_F",
                ],
                observed=True,
            )
            .agg({"hours": "sum", "Yearly GBP": "sum"})
            .sort_values(["Store", "Division"])
            .reset_index()
        )
        opb_fix = opb[opb.V_F == "F"].rename(
            columns={"hours": "Fix Hours", "Yearly GBP": "Yearly_GBP_fix"}
        )
        opb_fix.drop("V_F", axis=1, inplace=True)
        opb_var = opb[opb.V_F == "V"].rename(
            columns={"hours": "Variable Hours", "Yearly GBP": "Yearly_GBP_var"}
        )
        opb_var.drop("V_F", axis=1, inplace=True)
        opb_tpnb = opb_fix.merge(
            opb_var,
            on=["Country", "Store", "day", "Tpnb", "Pmg", "Format", "Dep", "Division"],
            how="inner",
        )
        opb_tpnb["Total Weekly Hours"] = (
            opb_tpnb["Fix Hours"] + opb_tpnb["Variable Hours"]
        )
        opb_tpnb["Yearly GBP"] = opb_tpnb.Yearly_GBP_fix + opb_tpnb.Yearly_GBP_var
        opb_tpnb.drop(["Yearly_GBP_fix", "Yearly_GBP_var"], axis=1, inplace=True)
        opb_tpnb = opb_tpnb.merge(
            sales_df, on=["Country", "Store", "Tpnb", "day"], how="inner"
        )
        opb_tpnb = opb_tpnb[
            [
                "Country",
                "Store",
                "Format",
                "day",
                "Tpnb",
                "Pmg",
                "Dep",
                "Division",
                "Variable Hours",
                "Fix Hours",
                "Yearly GBP",
                "Total Weekly Hours",
                "Sales",
            ]
        ]
        opb_tpnb["Division"] = opb_tpnb["Division"].apply(
            lambda x: "General Merchandise" if x == "GM" else x
        )
        opb_tpnb["Division"] = opb_tpnb["Division"].apply(
            lambda x: "Prepacked Fresh" if x == "Fresh" else x
        )
        opb_tpnb["Variable Currency"] = opb_tpnb["Sales"] / opb_tpnb["Variable Hours"]
        opb_tpnb["Variable Currency"] = (
            opb_tpnb["Variable Currency"]
            .replace(np.nan, 0)
            .replace([np.inf, -np.inf], 0)
        )
        opb_tpnb.drop("Sales", axis=1, inplace=True)
        cats = [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday",
        ]
        opb_tpnb["day"] = pd.Categorical(opb_tpnb["day"], categories=cats, ordered=True)
        opb_tpnb = opb_tpnb.sort_values(by=["Country", "Store", "Tpnb", "day"])
        product_names.columns = [
            i.capitalize() if i != "day" else "day" for i in product_names.columns
        ]
        opb_tpnb = opb_tpnb.merge(
            product_names, on=["Country", "Store", "day", "Tpnb"], how="inner"
        )

        output_tpnb = (
            opb_tpnb.groupby(
                [
                    "Country",
                    "Store",
                    "Format",
                    "Pmg",
                    "Dep",
                    "Division",
                    "Tpnb",
                    "Product_name",
                    "Repl_type",
                ],
                observed=True,
            )
            .agg(
                {
                    "Sold_units": "sum",
                    "Pallet_capacity": "mean",
                    "Case_capacity": "mean",
                    "Total Weekly Hours": "sum",
                    "Yearly GBP": "sum",
                }
            )
            .reset_index()
        )
        for x, y in zip(
            [
                "Product_name",
                "Repl_type",
                "Pallet_capacity",
                "Case_capacity",
                "Sold_units",
            ],
            [5, 6, 7, 8, 9],
        ):
            move_column_inplace(opb_tpnb, x, y)

        tpnb_activity_groups = (
            repl[["Store", "Tpnb", "Activity_Group", "hours"]]
            .groupby(["Store", "Tpnb", "Activity_Group"])
            .sum()
            .reset_index()
            .pivot_table(
                index=["Store", "Tpnb"],
                values="hours",
                columns="Activity_Group",
                aggfunc="sum",
            )
            .reset_index()
            .fillna(0)
        )

        output_tpnb = output_tpnb.merge(
            tpnb_activity_groups, on=["Store", "Tpnb"], how="left"
        )

        activity_groups = repl.Activity_Group.unique().tolist()

        # a = repl.query("Activity_Group == 'Stock Movement'")

    try:
        return (
            repl_wh_hours,
            opb_dep,
            opb_div,
            insight,
            insight_diff,
            insight_diff_act_groups,
            final_drivers,
            opb,
            opb_div_extended,
            driver_dep,
        )
    except UnboundLocalError:
        return output_tpnb, activity_groups


@timeit
def OutputsComparison(folder, repl_wh_hours, current_outputs):

    new_hrs = (
        repl_wh_hours.groupby(["Country", "Division"])
        .agg({"hours": "sum", "Yearly GBP": "sum"})
        .reset_index()
    )
    new_hrs.rename(
        columns={"hours": "New_Hours", "Yearly GBP": "New_Yearly GBP"}, inplace=True
    )
    new_hrs["Division"] = new_hrs["Division"].apply(
        lambda x: "General Merchandise" if x == "GM" else x
    )
    new_hrs["Division"] = new_hrs["Division"].apply(
        lambda x: "Prepacked Fresh" if x == "Fresh" else x
    )

    previous_hrs = pd.read_excel(
        folder / current_outputs,
        usecols=["Country", "Store", "Division", "Total Weekly Hours", "Yearly GBP"],
    )
    previous_hrs = previous_hrs[
        previous_hrs.Store.isin(repl_wh_hours.Store.unique().tolist())
    ]
    previous_hrs = (
        previous_hrs.groupby(["Country", "Division"])
        .agg({"Total Weekly Hours": "sum", "Yearly GBP": "sum"})
        .reset_index()
    )
    hrs_comparison = previous_hrs.merge(new_hrs, on=["Country", "Division"], how="left")
    hrs_comparison["diff_hours"] = (
        hrs_comparison["New_Hours"] - hrs_comparison["Total Weekly Hours"]
    )
    hrs_comparison["diff_%"] = (
        hrs_comparison.diff_hours / hrs_comparison["Total Weekly Hours"]
    ) * 100
    hrs_comparison["diff_in_GBP"] = (
        hrs_comparison["New_Yearly GBP"] - hrs_comparison["Yearly GBP"]
    )

    new_hrs_dep = (
        repl_wh_hours.groupby(["Country", "Store", "Format", "Division", "Dep"])
        .agg({"hours": "sum", "Yearly GBP": "sum"})
        .reset_index()
    )
    new_hrs_dep.rename(
        columns={"hours": "New_Hours", "Yearly GBP": "New_Yearly GBP"}, inplace=True
    )
    new_hrs_dep["Division"] = new_hrs_dep["Division"].apply(
        lambda x: "General Merchandise" if x == "GM" else x
    )
    new_hrs_dep["Division"] = new_hrs_dep["Division"].apply(
        lambda x: "Prepacked Fresh" if x == "Fresh" else x
    )

    previous_hrs_dep = pd.read_excel(folder / current_outputs)
    previous_hrs_dep = previous_hrs_dep[
        previous_hrs_dep.Store.isin(repl_wh_hours.Store.unique().tolist())
    ]
    previous_hrs_dep = (
        previous_hrs_dep.groupby(["Country", "Store", "Format", "Division", "Dep"])
        .agg({"Total Weekly Hours": "sum", "Yearly GBP": "sum"})
        .reset_index()
    )
    hrs_comparison_dep = previous_hrs_dep.merge(
        new_hrs_dep, on=["Country", "Store", "Format", "Division", "Dep"], how="left"
    )
    hrs_comparison_dep["diff_hours"] = (
        hrs_comparison_dep["New_Hours"] - hrs_comparison_dep["Total Weekly Hours"]
    )
    hrs_comparison_dep["diff_%"] = (
        hrs_comparison_dep.diff_hours / hrs_comparison_dep["Total Weekly Hours"]
    ) * 100
    hrs_comparison_dep["diff_in_GBP"] = (
        hrs_comparison_dep["New_Yearly GBP"] - hrs_comparison_dep["Yearly GBP"]
    )

    pd.options.display.float_format = "{:.1f}".format
    return hrs_comparison, hrs_comparison_dep


def ReplType_changer_in_Repl_Dataset(
    Repl_Dataset, sheet_name, new_repl_type, directory, selected_tpn
):

    input = pd.read_excel(directory / selected_tpn, sheet_name=sheet_name)
    # tpn_lista = list(set(input['tpnb']))
    repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]

    if sheet_name == "store_tpnb":
        if new_repl_type in ["nsrp", "full_pallet", "mu"]:
            dict_list = (
                input.groupby("store")["tpnb"].apply(lambda s: s.tolist()).to_dict()
            )
            for repl_type in repl_types:
                for k, v in dict_list.items():
                    Repl_Dataset.loc[
                        (Repl_Dataset.store == k) & (Repl_Dataset.tpnb.isin(v)),
                        repl_type,
                    ] = (1 if repl_type == new_repl_type else 0)

    if sheet_name == "store_tpnb":
        if new_repl_type not in ["nsrp", "full_pallet", "mu"]:
            dict_list = (
                input.groupby("store")["tpnb"].apply(lambda s: s.tolist()).to_dict()
            )
            for repl_type in repl_types:
                for k, v in dict_list.items():
                    Repl_Dataset.loc[
                        (Repl_Dataset.store == k)
                        & (Repl_Dataset.tpnb.isin(v))
                        & (Repl_Dataset.full_pallet == 0)
                        & (Repl_Dataset.mu == 0)
                        & (Repl_Dataset.split_pallet == 0),
                        repl_type,
                    ] = (1 if repl_type == new_repl_type else 0)

    if sheet_name == "country_tpnb":
        if new_repl_type in ["nsrp", "full_pallet", "mu"]:
            input["country"] = input["country"].apply(lambda x: x.upper())
            dict_list = (
                input.groupby("country")["tpnb"].apply(lambda s: s.tolist()).to_dict()
            )
            for repl_type in repl_types:
                for k, v in dict_list.items():
                    Repl_Dataset.loc[
                        (Repl_Dataset.country == k) & (Repl_Dataset.tpnb.isin(v)),
                        repl_type,
                    ] = (1 if repl_type == new_repl_type else 0)

        else:
            input["country"] = input["country"].apply(lambda x: x.upper())
            dict_list = (
                input.groupby("country")["tpnb"].apply(lambda s: s.tolist()).to_dict()
            )
            for repl_type in repl_types:
                for k, v in dict_list.items():
                    Repl_Dataset.loc[
                        (Repl_Dataset.country == k)
                        & (Repl_Dataset.tpnb.isin(v))
                        & (Repl_Dataset.full_pallet == 0)
                        & (Repl_Dataset.mu == 0)
                        & (Repl_Dataset.split_pallet == 0),
                        repl_type,
                    ] = (1 if repl_type == new_repl_type else 0)

    repl_type_cond = [
        Repl_Dataset.srp == 1,
        Repl_Dataset.nsrp == 1,
        Repl_Dataset.full_pallet == 1,
        Repl_Dataset.mu == 1,
        Repl_Dataset.split_pallet == 1,
    ]
    repl_type_result = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]
    Repl_Dataset["repl_type"] = np.select(repl_type_cond, repl_type_result, 0)
    product_details = Repl_Dataset[
        [
            "country",
            "store",
            "tpnb",
            "product_name",
            "day",
            "sold_units",
            "repl_type",
            "pallet_capacity",
            "case_capacity",
        ]
    ].drop_duplicates()

    return Repl_Dataset, product_details


def DiffOutputFormatter(folder, file_name, df):

    with pd.ExcelWriter(folder / file_name, engine="xlsxwriter") as writer:

        df["diff_%"] = df["diff_%"] / 100
        df.rename(
            columns={
                "diff_hours": "diff_hours_weekly",
                "diff_in_GBP": "diff_in_GBP_yearly",
            },
            inplace=True,
        )
        df.to_excel(writer, sheet_name="Stores_Hours_GBP", index=False)
        workbook = writer.book
        worksheet = writer.sheets["Stores_Hours_GBP"]
        # Add some cell formats.

        format0 = workbook.add_format({"num_format": "0.00"})
        format0.set_align("center_across")

        format1 = workbook.add_format({"num_format": "#,##0"})
        format1.set_align("center_across")

        format2 = workbook.add_format({"num_format": "0.0%"})
        format2.set_align("center_across")

        format3 = workbook.add_format()
        format3.set_center_across()

        format_diff_hours = workbook.add_format(
            {
                "num_format": "0.00",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 0,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "fg_color": "#005581",
            }
        )

        format_diff_percent = workbook.add_format(
            {
                "num_format": "0.0%",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 0,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "fg_color": "#005581",
            }
        )

        format_diff_gbp = workbook.add_format(
            {
                "num_format": "#,##0",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 0,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "fg_color": "#005581",
            }
        )

        df2 = (
            df.groupby(["Country", "Division"])[
                "diff_hours_weekly", "diff_in_GBP_yearly"
            ]
            .sum()
            .reset_index()
        )
        df2.to_excel(writer, sheet_name="Country_Hours_GBP", index=False)
        worksheet2 = writer.sheets["Country_Hours_GBP"]

        # Note: It isn't possible to format any cells that already have a format such
        # as the index or headers or any cells that contain dates or datetimes.

        # Set the column width and format.
        worksheet.set_column(6, 6, 18, format1)
        worksheet.set_column(8, 8, 18, format1)
        worksheet.set_column(11, 11, 18, format_diff_gbp)
        worksheet.set_column(10, 10, 18, format_diff_percent)
        worksheet.set_column(5, 5, 18, format0)
        worksheet.set_column(7, 7, 18, format0)
        worksheet.set_column(9, 9, 18, format_diff_hours)
        worksheet.set_column(0, 4, 18, format3)

        (max_row, max_col) = df.shape
        worksheet.autofilter(0, 0, max_row, max_col - 1)

        # Worksheet2
        worksheet2.set_column(0, 1, 18, format3)
        worksheet2.set_column(3, 3, 18, format_diff_gbp)
        worksheet2.set_column(2, 2, 18, format_diff_hours)

        # Set tab colors
        worksheet.set_tab_color("red")
        worksheet2.set_tab_color("green")

        dfs = {"Stores_Hours_GBP": df, "Country_Hours_GBP": df2}

        for sheetname, df in dfs.items():
            df.to_excel(writer, sheet_name=sheetname, index=False)  # send df to writer
            worksheet = writer.sheets[sheetname]  # pull worksheet object
            for idx, col in enumerate(df):  # loop through all columns
                series = df[col]
                max_len = (
                    max(
                        (
                            series.astype(str).map(len).max(),  # len of largest item
                            len(str(series.name)),  # len of column name/header
                        )
                    )
                    + 1
                )  # adding a little extra space
                worksheet.set_column(idx, idx, max_len)  # set column width

        # # Close the Pandas Excel writer and output the Excel file.
        # writer.save()
        # writer.close()


def DiffOutputFormatter_TPN(folder, file_name, df, activity_groups):

    with pd.ExcelWriter(folder / file_name, engine="xlsxwriter") as writer:

        df["diff_%"] = df["Total_Diff_weekly"] / df["Total Weekly Hours"]
        df["diff_%"] = df["diff_%"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)

        for x, y in zip(
            [
                "Repl_type_new",
                "Sold_units_new",
                "Pallet_capacity_new",
                "Case_capacity_new",
            ],
            [12, 13, 14, 15],
        ):
            move_column_inplace(df, x, y)

        total_asis_column_pos = df.columns.get_loc("Total Weekly Hours_new") - 1
        move_column_inplace(df, "Total Weekly Hours", total_asis_column_pos)
        total_asisGBP_column_pos = df.columns.get_loc("Yearly GBP_new") - 1
        move_column_inplace(df, "Yearly GBP", total_asisGBP_column_pos)

        activities_columns_new = [x + "_new" for x in activity_groups]

        column_list = []
        for x in activity_groups:
            column_idx = df.columns.get_loc(x)
            column_list.append(column_idx)

        needed_numbers_column_list = [
            column_list[-1] + x for x in range(1, 1 + len(column_list))
        ]

        for x, y in zip(activities_columns_new, needed_numbers_column_list):
            move_column_inplace(df, x, y)

        main_sheet = df[df.columns[:8].tolist() + df.columns[-7:].tolist()]
        info_groups = df[df.columns[:-7].tolist()]

        main_sheet.to_excel(writer, sheet_name="ReplType_Changing", index=False)
        workbook = writer.book
        worksheet = writer.sheets["ReplType_Changing"]

        info_groups.to_excel(writer, sheet_name="Activity_Groups_Infos", index=False)
        worksheet2 = writer.sheets["Activity_Groups_Infos"]

        df3 = (
            main_sheet.groupby(
                ["Country", "Tpnb", "Product_name", "Division"], observed=True
            )
            .agg({"Total_Diff_weekly": "sum", "Total_Diff_Yearly_GBP": "sum"})
            .reset_index()
        )
        df3.to_excel(writer, sheet_name="Country_Hours_GBP", index=False)
        worksheet3 = writer.sheets["Country_Hours_GBP"]

        # Add some cell formats.
        format0 = workbook.add_format({"num_format": "0.00"})
        format0.set_align("center_across")

        format1 = workbook.add_format({"num_format": "#,##0"})
        format1.set_align("center_across")

        format2 = workbook.add_format({"num_format": "0.0%"})
        format2.set_align("center_across")

        format3 = workbook.add_format()
        format3.set_center_across()

        format_A_H = workbook.add_format(
            {"align": "center_across", "valign": "vcenter"}
        )

        format_I_J = workbook.add_format(
            {"num_format": "0.00", "align": "center_across", "valign": "vcenter"}
        )

        format_diff_hours = workbook.add_format(
            {
                "num_format": "0.00",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 0,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "fg_color": "#005581",
            }
        )

        format_diff_percent = workbook.add_format(
            {
                "num_format": "0.0%",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 0,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "fg_color": "#005581",
            }
        )

        format_diff_gbp = workbook.add_format(
            {
                "num_format": "#,##0",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 0,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "fg_color": "#005581",
            }
        )

        format_info_part_a = workbook.add_format(
            {
                "num_format": "0.0",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 1,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "bg_color": "#006633",
            }
        )

        format_info_part_b = workbook.add_format(
            {
                "num_format": "0.0",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 1,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "bg_color": "#005581",
            }
        )

        format_info_part_c = workbook.add_format(
            {
                "num_format": "0.00",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 1,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "bg_color": "#006633",
            }
        )

        format_info_part_d = workbook.add_format(
            {
                "num_format": "0.00",
                "font_name": "Arial",
                "font_size": 11,
                "font_color": "white",
                "bold": 1,
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
                "bg_color": "#005581",
            }
        )

        # Note: It isn't possible to format any cells that already have a format such
        # as the index or headers or any cells that contain dates or datetimes.

        # Set the column width and format.
        # worksheet.set_column(9, 30, 18, format1)
        # worksheet.set_column(8, 8, 18, format1)
        # worksheet.set_column(11, 11, 18, format_diff_gbp)
        worksheet.set_column(
            0, main_sheet.columns.get_loc("Product_name"), 18, format_A_H
        )
        worksheet.set_column(
            main_sheet.columns.get_loc("Product_name"),
            len(main_sheet.columns) + 1,
            18,
            format_I_J,
        )
        worksheet.set_column(
            main_sheet.columns.get_loc("Total_Diff_weekly"),
            main_sheet.columns.get_loc("Total_Diff_weekly"),
            18,
            format_diff_hours,
        )
        worksheet.set_column(
            main_sheet.columns.get_loc("diff_%"),
            main_sheet.columns.get_loc("diff_%"),
            18,
            format_diff_percent,
        )
        worksheet.set_column(
            main_sheet.columns.get_loc("Total_Diff_Yearly_GBP"),
            main_sheet.columns.get_loc("Total_Diff_Yearly_GBP"),
            18,
            format_diff_gbp,
        )
        # worksheet.set_column(5, 5, 18, format0)
        # worksheet.set_column(7, 7, 18, format0)
        # worksheet.set_column(10,10, 18, format_diff_hours)
        # worksheet.set_column(0, 4, 18, format3)
        worksheet.set_zoom(90)
        (max_row, max_col) = df.shape
        worksheet.autofilter(0, 0, max_row, max_col - 1)

        # Worksheet2
        worksheet2.set_column(
            0, info_groups.columns.get_loc("Product_name"), 18, format_A_H
        )
        worksheet2.set_column(
            info_groups.columns.get_loc("Repl_type"),
            len(info_groups.columns) + 1,
            18,
            format_I_J,
        )
        worksheet2.set_column(
            info_groups.columns.get_loc("Repl_type"),
            info_groups.columns.get_loc("Case_capacity"),
            18,
            format_info_part_a,
        )
        worksheet2.set_column(
            info_groups.columns.get_loc("Repl_type_new"),
            info_groups.columns.get_loc("Case_capacity_new"),
            18,
            format_info_part_b,
        )
        worksheet2.set_column(
            info_groups.columns.get_loc("Backroom DotCom"),
            info_groups.columns.get_loc("Torn packaging"),
            18,
            format_info_part_c,
        )
        worksheet2.set_column(
            info_groups.columns.get_loc("Backroom DotCom_new"),
            info_groups.columns.get_loc("Torn packaging_new"),
            18,
            format_info_part_d,
        )
        worksheet2.set_zoom(90)

        # Worksheet3
        worksheet3.set_column(0, df3.columns.get_loc("Division"), 18, format_A_H)
        # worksheet3.set_column(5, 5, 18, format_diff_gbp)
        worksheet3.set_column(4, 4, 18, format_diff_hours)
        worksheet3.set_column(5, 5, 18, format_diff_gbp)

        # Set tab colors
        worksheet.set_tab_color("red")
        worksheet2.set_tab_color("blue")
        worksheet3.set_tab_color("green")

        dfs = {
            "ReplType_Changing": main_sheet,
            "Activity_Groups_Infos": info_groups,
            "Country_Hours_GBP": df3,
        }

        for sheetname, df in dfs.items():
            df.to_excel(writer, sheet_name=sheetname, index=False)  # send df to writer
            worksheet = writer.sheets[sheetname]  # pull worksheet object
            for idx, col in enumerate(df):  # loop through all columns
                series = df[col]
                max_len = (
                    max(
                        (
                            series.astype(str).map(len).max(),  # len of largest item
                            len(str(series.name)),  # len of column name/header
                        )
                    )
                    + 1
                )  # adding a little extra space
                worksheet.set_column(idx, idx, max_len)  # set column width

        # worksheet.set_column(df.columns.get_loc('Repl_type'), df.columns.get_loc('Pallet_capacity_new'), None, {'level': 1, 'hidden': True})
        # worksheet.set_column('O:O',None, None, {'collapsed': True})

        # Close the Pandas Excel writer and output the Excel file.
        # writer.save()
        # writer.close()


@timeit
def Replenishment_Insight(act_version_name, directory, stores):
    def insight_formatter(df):
        with pd.ExcelWriter(
            directory / f"outputs/Replenishment_Insight_{act_version_name}.xlsx",
            engine="xlsxwriter",
        ) as writer:

            df.to_excel(writer, sheet_name=f"Insight_{x}", index=False)
            workbook = writer.book
            worksheet = writer.sheets[f"Insight_{x}"]
            formating_info = workbook.add_format(
                {"num_format": "0.00", "align": "center_across", "valign": "vcenter"}
            )
            formating_A_C = workbook.add_format(
                {"align": "center_across", "valign": "vcenter"}
            )

            max_column_size = len(df.columns) - 1
            worksheet.set_column(0, 2, 18, formating_A_C)
            worksheet.set_column(3, max_column_size, 18, formating_info)
            worksheet.set_tab_color("green")
            worksheet.set_zoom(90)

    print("Starting to gather data and build up the Insight table....")

    data = pq.read_table(
        Path(directory / f"outputs/INSIGHT_{act_version_name}.parquet.gz"),
        filters=[("Store", "in", stores)],
    ).to_pandas()
    data["model"] = np.where(data.Division == "Warehouse", "Warehouse", "Replenishment")
    # df['combined'] = df['model'] + '_' + df['Activity_Group'] + '_' + df['Suboperation']
    # df = df.sort_values(by[])

    with pd.ExcelWriter(
        directory / f"outputs/Replenishment_Insight_{act_version_name}.xlsx",
        engine="xlsxwriter",
    ) as writer:

        for div in data.Division.unique().tolist():
            df = data[data.Division == div]
            df = (
                df[
                    [
                        "Country",
                        "Store",
                        "Format",
                        "Division",
                        "model",
                        "Activity_Group",
                        "Suboperation",
                        "hours",
                    ]
                ]
                .sort_values(
                    by=["model", "Activity_Group", "Suboperation"], ascending=True
                )
                .pivot(
                    index=["Country", "Store", "Format", "Division"],
                    columns=["model", "Activity_Group", "Suboperation"],
                    values="hours",
                )
                .T.reset_index()
                .T.reset_index()
                .fillna(0)
            )
            for x in df.columns[:4].tolist():
                for y in range(3):
                    df.loc[df.index[y], x] = x
            df = df.rename(columns=df.iloc[0]).drop(df.index[0])

            df.to_excel(writer, sheet_name=f"Insight_{div}", index=False)
            workbook = writer.book
            worksheet = writer.sheets[f"Insight_{div}"]
            formating_info = workbook.add_format(
                {"num_format": "0.00", "align": "center_across", "valign": "vcenter"}
            )
            formating_A_C = workbook.add_format(
                {"align": "center_across", "valign": "vcenter"}
            )

            max_column_size = len(df.columns) - 1
            worksheet.set_column(0, 2, 18, formating_A_C)
            worksheet.set_column(3, max_column_size, 18, formating_info)
            worksheet.set_tab_color("green")
            worksheet.set_zoom(90)

        # df = df.sort_values(by=['Country', 'Store', 'Division'], ascending=True)
        # df.to_excel(directory / f"outputs/Replenishment_Insight_{act_version_name}.xlsx", index = False)

    # insight_formatter(df)


def Plot_Activity_Groups_by_Divisions(a):

    ###########################
    #### HOURS Calculation ####
    ###########################

    a_hours = (
        a.groupby(["Country", "Division", "Activity_Group"], observed=True)["hours"]
        .sum()
        .reset_index()
    )
    a_hours["hours"] = round(a_hours["hours"], 0)

    sns.set()
    i = 0
    f = 0

    row_number = len(set(a_hours.Country))
    col_number = len(set(a_hours.Division))

    fig, axes = plt.subplots(
        row_number, col_number, figsize=(22, 12), constrained_layout=True
    )
    fig.suptitle("Biggest Activity Groups by Divisions Weekly Hours")

    if len(set(a_hours.Country)) > 1:
        for country in sorted(set(a_hours.Country)):
            for x in sorted(set(a_hours.Division)):
                c = a_hours.loc[
                    (a_hours.Division == x) & (a_hours.Country == country)
                ].sort_values(by=["Country", "Division", "hours"], ascending=False)[:5]

                plt.setp(
                    axes[f, i].get_xticklabels(),
                    rotation=30,
                    ha="right",
                    rotation_mode="anchor",
                )
                sns.barplot(x="Activity_Group", y="hours", data=c, ax=axes[f, i])

                axes[f, i].set(xlabel="", ylabel="")
                axes[f, i].set_title(f"{country} {x}")
                y_axis = axes[f, i].get_yaxis()
                y_axis.set_visible(False)

                for bars in axes[f, i].containers:
                    axes[f, i].bar_label(bars, color="white", label_type="center")

                i += 1
                if i == len(set(a_hours.Division)):
                    i = 0
            f += 1
    else:
        for country in sorted(set(a_hours.Country)):
            for x in sorted(set(a_hours.Division)):
                c = a_hours.loc[
                    (a_hours.Division == x) & (a_hours.Country == country)
                ].sort_values(by=["Country", "Division", "hours"], ascending=False)[:5]

                plt.setp(
                    axes[i].get_xticklabels(),
                    rotation=30,
                    ha="right",
                    rotation_mode="anchor",
                )
                sns.barplot(x="Activity_Group", y="hours", data=c, ax=axes[i])

                axes[i].set(xlabel="", ylabel="")
                axes[i].set_title(f"{country} {x}")
                y_axis = axes[i].get_yaxis()
                y_axis.set_visible(False)

                for bars in axes[i].containers:
                    axes[i].bar_label(bars, color="white", label_type="center")

                i += 1
                if i == len(set(a_hours.Division)):
                    i = 0

    #########################
    #### GBP Calculation ####
    #########################
    sns.set()

    a_gbp = (
        a.groupby(["Country", "Division", "Activity_Group"], observed=True)[
            "Yearly GBP"
        ]
        .sum()
        .reset_index()
    )
    a_gbp["Yearly GBP"] = round(a_gbp["Yearly GBP"], 0)
    i = 0
    f = 0

    row_number = len(set(a_gbp.Country))
    col_number = len(set(a_gbp.Division))
    fig, axes = plt.subplots(
        row_number, col_number, figsize=(22, 12), sharey=False, constrained_layout=True
    )

    fig.suptitle("Biggest Activity Groups by Divisions Yearly GBP ")

    if len(set(a_gbp.Country)) > 1:
        for country in sorted(set(a_gbp.Country)):
            for x in sorted(set(a_gbp.Division)):
                c = a_gbp.loc[
                    (a_gbp.Division == x) & (a_gbp.Country == country)
                ].sort_values(
                    by=["Country", "Division", "Yearly GBP"], ascending=False
                )[
                    :4
                ]

                plt.setp(
                    axes[f, i].get_xticklabels(),
                    rotation=30,
                    ha="right",
                    rotation_mode="anchor",
                )
                sns.barplot(x="Activity_Group", y="Yearly GBP", data=c, ax=axes[f, i])

                axes[f, i].set(xlabel="", ylabel="")
                axes[f, i].set_title(f"{country} {x}")
                y_axis = axes[f, i].get_yaxis()
                y_axis.set_visible(False)

                for bars in axes[f, i].containers:
                    axes[f, i].bar_label(
                        bars,
                        labels=[f"£{x:,.0f}" for x in bars.datavalues],
                        color="white",
                        label_type="center",
                        fontsize=10,
                    )

                i += 1
                if i == len(set(a_gbp.Division)):
                    i = 0
            f += 1
    else:
        for country in sorted(set(a_gbp.Country)):
            for x in sorted(set(a_gbp.Division)):
                c = a_gbp.loc[
                    (a_gbp.Division == x) & (a_gbp.Country == country)
                ].sort_values(
                    by=["Country", "Division", "Yearly GBP"], ascending=False
                )[
                    :4
                ]

                plt.setp(
                    axes[i].get_xticklabels(),
                    rotation=30,
                    ha="right",
                    rotation_mode="anchor",
                )
                sns.barplot(x="Activity_Group", y="Yearly GBP", data=c, ax=axes[i])

                axes[i].set(xlabel="", ylabel="")
                axes[i].set_title(f"{country} {x}")
                y_axis = axes[i].get_yaxis()
                y_axis.set_visible(False)

                for bars in axes[i].containers:
                    axes[i].bar_label(
                        bars,
                        labels=[f"£{x:,.0f}" for x in bars.datavalues],
                        color="white",
                        label_type="center",
                        fontsize=10,
                    )

                i += 1
                if i == len(set(a_gbp.Division)):
                    i = 0


def plotly_CE_chart_cost_base_and_repl_type_percent(
    act_version_name, directory, repl_dataset_f, stores
):

    print("\nCE Charts has started to be created....\n")

    def combine_plotly_figs_to_html(
        plotly_figs, html_fname, include_plotlyjs="cdn", separator=None, auto_open=False
    ):
        with open(html_fname, "w") as f:
            f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))
            for fig in plotly_figs[1:]:
                if separator:
                    f.write(separator)
                f.write(fig.to_html(full_html=False, include_plotlyjs=False))

        if auto_open:
            import pathlib, webbrowser

            uri = pathlib.Path(html_fname).absolute().as_uri()
            webbrowser.open(uri)

    ##### Open Insight table #####
    insight = pq.read_table(
        directory / f"outputs/INSIGHT_{act_version_name}.parquet.gz",
        filters=[("Store", "in", stores)],
    ).to_pandas()

    insight_gr = (
        insight.groupby(["Division", "Activity_Group"], observed=True)["Yearly GBP"]
        .sum()
        .reset_index()
        .sort_values(by=["Division", "Yearly GBP"], ascending=[True, False])
    )

    top_4_act_group = pd.DataFrame()

    for x in sorted(set(insight_gr.Division)):
        top_4_act_group = pd.concat(
            [top_4_act_group, insight_gr[insight_gr.Division == x][:4]]
        )

    ##### Open Repl Dataset for Repl Types info #####
    repl_types = pq.read_table(
        directory / repl_dataset_f, filters=[("store", "in", stores)]
    ).to_pandas()

    for x in tqdm(["srp", "nsrp", "full_pallet", "mu", "split_pallet"]):
        repl_types.loc[repl_types[x] > 0, x] = repl_types["sold_units"]

    repl_types = (
        repl_types.groupby(["country", "division"], observed=True)[
            "srp", "nsrp", "full_pallet", "mu", "split_pallet"
        ]
        .sum()
        .reset_index()
    )
    repl_types = repl_types.melt(
        id_vars=["country", "division"],
        value_vars=["srp", "nsrp", "full_pallet", "mu", "split_pallet"],
        var_name="repl_type",
        value_name="total",
    )

    repl_types = repl_types.groupby(["division", "repl_type"]).sum().reset_index()
    repl_types["percent"] = (
        repl_types.total
        / repl_types.groupby(["repl_type"])["total"].transform("sum")
        * 100
    )

    ##### Creating Charts on subplots #####

    cont = []

    for div in tqdm(top_4_act_group.Division.unique()):

        top_4_act_groups = top_4_act_group[top_4_act_group.Division == div]

        fig = make_subplots(
            rows=4,
            cols=2,
            shared_xaxes=False,
            shared_yaxes=False,
            vertical_spacing=0.1,
            column_widths=[0.4, 0.6],
            row_heights=[0.1, 0.1, 0.1, 0.7],
            # vertical_spacing = [0.8],
            specs=[
                [None, {"type": "bar", "rowspan": 2}],
                [{"type": "table", "rowspan": 3}, None],
                [None, None],
                [None, {"type": "pie"}],
            ],
            subplot_titles=(
                f"Top 4 Activities and Replenishment Types distribution weighted by Sales in <b>{div}</b> area",
                "",
                "",
            ),
        )

        fig.add_trace(
            go.Pie(
                labels=repl_types[repl_types.division == div]["repl_type"].tolist(),
                values=repl_types[repl_types.division == div]["total"].tolist(),
                name="",
                textinfo="label+percent",
                insidetextorientation="radial",
                textfont=dict(color="#000000"),
                marker_colors=px.colors.sequential.deep,
                hole=0.4,
                direction="clockwise",
            ),
            4,
            2,
        )

        fig.add_trace(
            go.Bar(
                x=[
                    int(x)
                    for x in top_4_act_groups.sort_values(by="Yearly GBP")["Yearly GBP"]
                ],
                y=[
                    f"<b>{x}</b>"
                    for x in top_4_act_groups.sort_values(by="Yearly GBP")[
                        "Activity_Group"
                    ]
                ],
                orientation="h",
                name="Top Activities' Cost Base",
                text=[
                    int(x)
                    for x in top_4_act_groups.sort_values(by="Yearly GBP")["Yearly GBP"]
                ],
                texttemplate="%{x:,.20}",
                marker=dict(
                    color=[
                        int(x)
                        for x in top_4_act_groups.sort_values(by="Yearly GBP")[
                            "Yearly GBP"
                        ]
                    ],
                    colorscale="Blugrn",
                ),
            ),
            row=1,
            col=2,
        )

        fig.add_trace(
            go.Table(
                columnwidth=[8, 15, 20],
                header=dict(
                    values=[f"<b>{x}</b>" for x in top_4_act_groups.columns],
                    font=dict(color="white", size=16),
                    fill_color="darkcyan",
                    align="center",
                ),
                cells=dict(
                    values=top_4_act_groups.values.T,
                    align="center",
                    format=["", "", ",.0f"],
                    fill_color="mediumaquamarine",
                    font=dict(color="white", size=14),
                    height=40,
                ),
            ),
            row=2,
            col=1,
        )
        fig.update_layout(
            legend=dict(x=1, y=1, font_size=10),
            paper_bgcolor="rgb(248, 248, 240)",
            plot_bgcolor="rgb(171,217,233)",
        )

        annotations = []

        # cost_base = [int(x) for x in top_4_act_groups.sort_values(by='Yearly GBP')['Yearly GBP']]
        # act_group = [f'<b>{x}</b>' for x in top_4_act_groups.sort_values(by='Yearly GBP')['Activity_Group']]

        # Adding labels
        # for yd, xd in zip(cost_base, act_group):
        #     # labeling the scatter savings
        #     annotations.append(dict(xref='x1', yref='y1',
        #                             y=xd, x=yd/2,
        #                             text='{:,}'.format(yd) + '',
        #                             font=dict(family='Arial', size=14,
        #                                       color="white"),
        #                             showarrow=False))

        fig.update_layout(
            annotations=annotations,
            title={"text": f"Division: <b>{div}</b>"},
            title_font_size=30,
            height=600,
            showlegend=False,
        )

        fig.update_annotations(yshift=10, xshift=0)
        cont.append(fig)
        # fig.show()

    html_fname = directory / f"outputs/Charts/CE_summary_{act_version_name}.html"
    plotly_figs = cont
    combine_plotly_figs_to_html(
        plotly_figs, html_fname, include_plotlyjs="cdn", separator=None, auto_open=False
    )

    print("\nCE Charts has been done and saved into 'outputs' folder \n")


def OPB_DEP_DIV_formatter(df, directory, act_version_name, dep, driver_dep):

    if dep == True:
        file_name = "OPB_Dep_"
        end_col_number = 5
        grey_col_end = 9
        sheet = "OPB_DEPARMENTS"

    if dep == False:
        file_name = "OPB_Div_"
        end_col_number = 4
        grey_col_end = 8
        sheet = "OPB_DIVISIONS"

    with pd.ExcelWriter(
        directory / f"outputs/{file_name}{act_version_name}.xlsx", engine="xlsxwriter"
    ) as writer:
        df.to_excel(writer, sheet_name=f"{sheet}", index=False)
        workbook = writer.book
        worksheet = writer.sheets[f"{sheet}"]
        formating_numbers = workbook.add_format(
            {
                "num_format": "0.0",
                "align": "center_across",
                "valign": "vcenter",
                "bg_color": "#ddebda",
                "right": 1,
            }
        )
        formating_A_D = workbook.add_format(
            {
                "align": "center_across",
                "valign": "vcenter",
                "border": 1,
                "align": "center_across",
                "valign": "vcenter",
                "text_wrap": 1,
            }
        )

        formating_summary = workbook.add_format(
            {
                "num_format": "0.0",
                "align": "center_across",
                "valign": "vcenter",
                "bg_color": "#e8ebed",
                "right": 1,
            }
        )

        formating_drivers = workbook.add_format(
            {"num_format": "0.0", "align": "center_across", "valign": "vcenter"}
        )

        max_column_size = len(df.columns) - 1
        worksheet.set_column(0, end_col_number, 18, formating_A_D)
        worksheet.set_column(end_col_number, max_column_size, 18, formating_numbers)
        worksheet.set_column(end_col_number, grey_col_end, 18, formating_summary)

        worksheet.set_tab_color("green")
        worksheet.set_zoom(90)

        driver_dep.to_excel(writer, sheet_name="Drivers", index=False)
        worksheet2 = writer.sheets["Drivers"]
        worksheet2.set_tab_color("blue")
        worksheet2.set_column(3, len(driver_dep.columns) - 1, 18, formating_drivers)
        worksheet2.set_zoom(90)

        dfs = {sheet: df, "Drivers": driver_dep}

        for sheetname, df in dfs.items():
            df.to_excel(writer, sheet_name=sheetname, index=False)  # send df to writer
            worksheet = writer.sheets[sheetname]  # pull worksheet object
            for idx, col in enumerate(df):  # loop through all columns
                series = df[col]
                max_len = (
                    max(
                        (
                            series.astype(str).map(len).max(),  # len of largest item
                            len(str(series.name)),  # len of column name/header
                        )
                    )
                    + 1.5
                )  # adding a little extra space
                worksheet.set_column(idx, idx, max_len)  # set column width
