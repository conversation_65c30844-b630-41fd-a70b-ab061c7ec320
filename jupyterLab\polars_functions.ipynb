{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0c373e4f-4f0f-4a71-8c42-09bb0cf5fe9f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import pyarrow.parquet as pq\n", "import time\n", "from functools import wraps\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "from tqdm import tqdm\n", "import polars as pl\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": 4, "id": "ea6b88f2-475d-4bd5-af29-243e35a53770", "metadata": {}, "outputs": [], "source": ["directory = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\apps\\ReplTypeChanger_app_split_pallet_version\")\n", "repl_dataset_f = 'inputs/Repl_Dataset_2022_23_Q1_vol3_new_prices.parquet'\n", "selected_pmg = [\"DRY04\"]"]}, {"cell_type": "code", "execution_count": 26, "id": "c64c093c-66d5-4dc1-8c43-90e7aef4a2fa", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_13552\\1494065244.py:6: FutureWarning: Indexing with multiple keys (implicitly converted to a tuple of keys) will be deprecated, use a list instead.\n", "  as_is_data_pmg = (as_is_data.groupby(['country','division', 'dep', 'pmg', 'tpnb', 'product_name' ], observed=True)['srp', 'nsrp','full_pallet', 'mu', 'split_pallet'].sum()/7).reset_index()\n"]}], "source": ["%timeit\n", "\n", "as_is_data = pq.read_table(directory / repl_dataset_f, filters=[(\"pmg\", \"in\", selected_pmg)],\n", "                           columns=['country','store', 'division', 'dep', 'pmg', 'tpnb', 'product_name', 'srp', 'nsrp','full_pallet', 'mu', 'split_pallet', 'sold_units']).to_pandas()\n", "\n", "as_is_data_pmg = (as_is_data.groupby(['country','division', 'dep', 'pmg', 'tpnb', 'product_name' ], observed=True)['srp', 'nsrp','full_pallet', 'mu', 'split_pallet'].sum()/7).reset_index()\n", "as_is_data_pmg = as_is_data_pmg.loc[as_is_data_pmg['pmg'].isin(selected_pmg)]\n", "as_is_data_pmg = as_is_data_pmg.astype(\n", "    {'srp': 'int', 'nsrp': 'int', 'full_pallet': 'int', 'mu': 'int', 'split_pallet': 'int'})\n", "sold_units_pmg = as_is_data[as_is_data.pmg.isin(selected_pmg)].groupby(['country', 'tpnb'], observed=True)['sold_units'].sum().reset_index().astype({'sold_units' : 'int'})\n", "as_is_data_pmg = as_is_data_pmg.merge(sold_units_pmg, on=['country', 'tpnb'], how='left').sort_values(by=['sold_units'], ascending=False)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 27, "id": "be8c651a-f3e6-4c3d-bfae-27a3b513ca34", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>division</th>\n", "      <th>dep</th>\n", "      <th>pmg</th>\n", "      <th>tpnb</th>\n", "      <th>product_name</th>\n", "      <th>srp</th>\n", "      <th>nsrp</th>\n", "      <th>full_pallet</th>\n", "      <th>mu</th>\n", "      <th>split_pallet</th>\n", "      <th>sold_units</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>SK</td>\n", "      <td>Grocery</td>\n", "      <td>DRY</td>\n", "      <td>DRY04</td>\n", "      <td>121560206</td>\n", "      <td>Kofola Originál 2 l VO PET</td>\n", "      <td>0</td>\n", "      <td>46</td>\n", "      <td>109</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>111637</td>\n", "    </tr>\n", "    <tr>\n", "      <th>284</th>\n", "      <td>HU</td>\n", "      <td>Grocery</td>\n", "      <td>DRY</td>\n", "      <td>DRY04</td>\n", "      <td>120546180</td>\n", "      <td>COCA-COLA 1,75L</td>\n", "      <td>0</td>\n", "      <td>85</td>\n", "      <td>113</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>75521</td>\n", "    </tr>\n", "    <tr>\n", "      <th>368</th>\n", "      <td>HU</td>\n", "      <td>Grocery</td>\n", "      <td>DRY</td>\n", "      <td>DRY04</td>\n", "      <td>220206845</td>\n", "      <td>PEPSI COLA 2.25l</td>\n", "      <td>0</td>\n", "      <td>54</td>\n", "      <td>112</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>62622</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>CZ</td>\n", "      <td>Grocery</td>\n", "      <td>DRY</td>\n", "      <td>DRY04</td>\n", "      <td>220035549</td>\n", "      <td>COCA-COLA 1,75l</td>\n", "      <td>0</td>\n", "      <td>97</td>\n", "      <td>86</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>62297</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>CZ</td>\n", "      <td>Grocery</td>\n", "      <td>DRY</td>\n", "      <td>DRY04</td>\n", "      <td>209255932</td>\n", "      <td>KOFOLA 2L PET</td>\n", "      <td>0</td>\n", "      <td>104</td>\n", "      <td>79</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>60394</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    country division  dep    pmg       tpnb                product_name  srp  \\\n", "143      SK  Grocery  DRY  DRY04  121560206  Kofola Originál 2 l VO PET    0   \n", "284      HU  Grocery  DRY  DRY04  120546180             COCA-COLA 1,75L    0   \n", "368      HU  Grocery  DRY  DRY04  220206845            PEPSI COLA 2.25l    0   \n", "49       CZ  Grocery  DRY  DRY04  220035549             COCA-COLA 1,75l    0   \n", "25       CZ  Grocery  DRY  DRY04  209255932               KOFOLA 2L PET    0   \n", "\n", "     nsrp  full_pallet  mu  split_pallet  sold_units  \n", "143    46          109   0             0      111637  \n", "284    85          113   0             0       75521  \n", "368    54          112   0             0       62622  \n", "49     97           86   0             0       62297  \n", "25    104           79   0             0       60394  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["as_is_data_pmg.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "ad71a557-aa12-49d1-8c03-6574e0f8943e", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "a = pl.read_parquet(directory / repl_dataset_f, columns=['country','store', 'division', 'dep', 'pmg', 'tpnb', 'product_name', 'srp', 'nsrp','full_pallet', 'mu', 'split_pallet', 'sold_units'])"]}, {"cell_type": "code", "execution_count": null, "id": "2b007804-b3e8-4aea-814d-c5093f3e6f1b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f5050edf-703b-4d7f-a47a-c5926185863c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 50, "id": "f8031000-bdf4-4de4-9c9d-75d283348fdb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.31 s ± 47 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%%timeit\n", "with pl.<PERSON><PERSON><PERSON>(): \n", "    a = pl.read_parquet(directory / repl_dataset_f, columns=['country','store', 'division', 'dep', 'pmg', 'tpnb', 'product_name', 'srp', 'nsrp','full_pallet', 'mu', 'split_pallet', 'sold_units']).lazy()\n", "    a = a.filter(pl.col(\"pmg\").is_in(selected_pmg))\n", "    a = a.groupby([\n", "        'country','division', 'dep', 'pmg', 'tpnb', 'product_name'\n", "    ]).agg([\n", "        (pl.col('srp').sum()/7).cast(pl.Int32),\n", "        (pl.col('nsrp').sum()/7).cast(pl.Int32),\n", "        (pl.col('full_pallet').sum()/7).cast(pl.Int32),\n", "        (pl.col('mu').sum()/7).cast(pl.Int32),\n", "        (pl.col('split_pallet').sum()/7).cast(pl.Int32),\n", "        pl.col(\"sold_units\").sum().cast(pl.Int64)\n", "    ]).sort([\"sold_units\"], reverse=True).collect().to_pandas()\n"]}, {"cell_type": "code", "execution_count": null, "id": "9898188c-7d4a-4cd6-8236-0baa9e9c6cba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 47, "id": "13485b15-036a-4d10-a7c6-1f931389a9dc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe td {\n", "        white-space: pre;\n", "    }\n", "\n", "    .dataframe td {\n", "        padding-top: 0;\n", "    }\n", "\n", "    .dataframe td {\n", "        padding-bottom: 0;\n", "    }\n", "\n", "    .dataframe td {\n", "        line-height: 95%;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "<small>shape: (437, 12)</small>\n", "<thead>\n", "<tr>\n", "<th>\n", "country\n", "</th>\n", "<th>\n", "division\n", "</th>\n", "<th>\n", "dep\n", "</th>\n", "<th>\n", "pmg\n", "</th>\n", "<th>\n", "tpnb\n", "</th>\n", "<th>\n", "product_name\n", "</th>\n", "<th>\n", "srp\n", "</th>\n", "<th>\n", "nsrp\n", "</th>\n", "<th>\n", "full_pallet\n", "</th>\n", "<th>\n", "mu\n", "</th>\n", "<th>\n", "split_pallet\n", "</th>\n", "<th>\n", "sold_units\n", "</th>\n", "</tr>\n", "<tr>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "i64\n", "</td>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "i32\n", "</td>\n", "<td>\n", "i32\n", "</td>\n", "<td>\n", "i32\n", "</td>\n", "<td>\n", "i32\n", "</td>\n", "<td>\n", "i32\n", "</td>\n", "<td>\n", "i64\n", "</td>\n", "</tr>\n", "</thead>\n", "<tbody>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121560206\n", "</td>\n", "<td>\n", "&quot;Kofola Originá...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "46\n", "</td>\n", "<td>\n", "109\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "111637\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120546180\n", "</td>\n", "<td>\n", "&quot;COCA-COLA 1,75...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "85\n", "</td>\n", "<td>\n", "113\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "75521\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220206845\n", "</td>\n", "<td>\n", "&quot;PEPSI COLA 2.2...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "54\n", "</td>\n", "<td>\n", "112\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "62622\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220035549\n", "</td>\n", "<td>\n", "&quot;COCA-COLA 1,75...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "97\n", "</td>\n", "<td>\n", "86\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "62297\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "209255932\n", "</td>\n", "<td>\n", "&quot;KOFOLA 2L PET&quot;\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "104\n", "</td>\n", "<td>\n", "79\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "60394\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121563612\n", "</td>\n", "<td>\n", "&quot;Coca-Cola Orig...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "72\n", "</td>\n", "<td>\n", "83\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "47343\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121563324\n", "</td>\n", "<td>\n", "&quot;Pepsi Cola Ori...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "136\n", "</td>\n", "<td>\n", "19\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "46966\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220035565\n", "</td>\n", "<td>\n", "&quot;COCA-COLA 2,25...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "160\n", "</td>\n", "<td>\n", "23\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "41459\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121563537\n", "</td>\n", "<td>\n", "&quot;Coca-Cola Orig...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "92\n", "</td>\n", "<td>\n", "63\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "38515\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120546220\n", "</td>\n", "<td>\n", "&quot;COKE ZERO 1,75...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "120\n", "</td>\n", "<td>\n", "78\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "31538\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120546168\n", "</td>\n", "<td>\n", "&quot;COCA-COLA 2,25...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "70\n", "</td>\n", "<td>\n", "100\n", "</td>\n", "<td>\n", "1\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "30424\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121562521\n", "</td>\n", "<td>\n", "&quot;Vinea Biel<PERSON> 1,...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "141\n", "</td>\n", "<td>\n", "14\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "30110\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120758741\n", "</td>\n", "<td>\n", "&quot;PEPSI BLACK 2,...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "2\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220321466\n", "</td>\n", "<td>\n", "&quot;Fuzetea Zero M...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "1\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120077014\n", "</td>\n", "<td>\n", "&quot;XIXO LIMO BODZ...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "3\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220201986\n", "</td>\n", "<td>\n", "&quot;FUZETEA GT JAH...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "1\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220314073\n", "</td>\n", "<td>\n", "&quot;<PERSON><PERSON><PERSON><PERSON>...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "1\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220035558\n", "</td>\n", "<td>\n", "&quot;KINLEY TONIC 1...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "1\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "211810348\n", "</td>\n", "<td>\n", "&quot;PF.Z.CAJ.CITR....\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "1\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220297870\n", "</td>\n", "<td>\n", "&quot;AQUILA MÉNĚ CU...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "1\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "212545355\n", "</td>\n", "<td>\n", "&quot;LIPTON ICE TEA...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "2\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "219460203\n", "</td>\n", "<td>\n", "&quot;COCA-COLA ZERO...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "2\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "210355912\n", "</td>\n", "<td>\n", "&quot;COKE ZERO 2X2L...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "2\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "130508809\n", "</td>\n", "<td>\n", "&quot;ORIGINAL RIVER...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "2\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "</tr>\n", "</tbody>\n", "</table>\n", "</div>"], "text/plain": ["shape: (437, 12)\n", "┌─────────┬──────────┬─────┬───────┬─────┬─────────────┬─────┬──────────────┬────────────┐\n", "│ country ┆ division ┆ dep ┆ pmg   ┆ ... ┆ full_pallet ┆ mu  ┆ split_pallet ┆ sold_units │\n", "│ ---     ┆ ---      ┆ --- ┆ ---   ┆     ┆ ---         ┆ --- ┆ ---          ┆ ---        │\n", "│ cat     ┆ cat      ┆ cat ┆ cat   ┆     ┆ i32         ┆ i32 ┆ i32          ┆ i64        │\n", "╞═════════╪══════════╪═════╪═══════╪═════╪═════════════╪═════╪══════════════╪════════════╡\n", "│ SK      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 109         ┆ 0   ┆ 0            ┆ 111637     │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ HU      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 113         ┆ 0   ┆ 0            ┆ 75521      │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ HU      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 112         ┆ 0   ┆ 0            ┆ 62622      │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ CZ      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 86          ┆ 0   ┆ 0            ┆ 62297      │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ ...     ┆ ...      ┆ ... ┆ ...   ┆ ... ┆ ...         ┆ ... ┆ ...          ┆ ...        │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ SK      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 0           ┆ 0   ┆ 0            ┆ 0          │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ CZ      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 0           ┆ 0   ┆ 0            ┆ 0          │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ HU      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 0           ┆ 0   ┆ 0            ┆ 0          │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ CZ      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 0           ┆ 0   ┆ 0            ┆ 0          │\n", "└─────────┴──────────┴─────┴───────┴─────┴─────────────┴─────┴──────────────┴────────────┘"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["\n"]}, {"cell_type": "code", "execution_count": 43, "id": "a7114518-62bc-4074-a4da-0bba3ddaa098", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe td {\n", "        white-space: pre;\n", "    }\n", "\n", "    .dataframe td {\n", "        padding-top: 0;\n", "    }\n", "\n", "    .dataframe td {\n", "        padding-bottom: 0;\n", "    }\n", "\n", "    .dataframe td {\n", "        line-height: 95%;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "<small>shape: (437, 12)</small>\n", "<thead>\n", "<tr>\n", "<th>\n", "country\n", "</th>\n", "<th>\n", "division\n", "</th>\n", "<th>\n", "dep\n", "</th>\n", "<th>\n", "pmg\n", "</th>\n", "<th>\n", "tpnb\n", "</th>\n", "<th>\n", "product_name\n", "</th>\n", "<th>\n", "srp\n", "</th>\n", "<th>\n", "nsrp\n", "</th>\n", "<th>\n", "full_pallet\n", "</th>\n", "<th>\n", "mu\n", "</th>\n", "<th>\n", "split_pallet\n", "</th>\n", "<th>\n", "sold_units\n", "</th>\n", "</tr>\n", "<tr>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "i64\n", "</td>\n", "<td>\n", "cat\n", "</td>\n", "<td>\n", "i64\n", "</td>\n", "<td>\n", "i64\n", "</td>\n", "<td>\n", "i64\n", "</td>\n", "<td>\n", "i64\n", "</td>\n", "<td>\n", "i64\n", "</td>\n", "<td>\n", "f32\n", "</td>\n", "</tr>\n", "</thead>\n", "<tbody>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121560206\n", "</td>\n", "<td>\n", "&quot;Kofola Originá...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "322\n", "</td>\n", "<td>\n", "763\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "111637.515625\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120546180\n", "</td>\n", "<td>\n", "&quot;COCA-COLA 1,75...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "595\n", "</td>\n", "<td>\n", "791\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "75521.6875\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220206845\n", "</td>\n", "<td>\n", "&quot;PEPSI COLA 2.2...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "378\n", "</td>\n", "<td>\n", "784\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "62622.390625\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220035549\n", "</td>\n", "<td>\n", "&quot;COCA-COLA 1,75...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "679\n", "</td>\n", "<td>\n", "602\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "62297.480469\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "209255932\n", "</td>\n", "<td>\n", "&quot;KOFOLA 2L PET&quot;\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "728\n", "</td>\n", "<td>\n", "553\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "60394.558594\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121563612\n", "</td>\n", "<td>\n", "&quot;Coca-Cola Orig...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "504\n", "</td>\n", "<td>\n", "581\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "47343.390625\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121563324\n", "</td>\n", "<td>\n", "&quot;Pepsi Cola Ori...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "952\n", "</td>\n", "<td>\n", "133\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "46966.078125\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220035565\n", "</td>\n", "<td>\n", "&quot;COCA-COLA 2,25...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "1120\n", "</td>\n", "<td>\n", "161\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "41459.476562\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121563537\n", "</td>\n", "<td>\n", "&quot;Coca-Cola Orig...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "644\n", "</td>\n", "<td>\n", "441\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "38515.667969\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120546220\n", "</td>\n", "<td>\n", "&quot;COKE ZERO 1,75...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "840\n", "</td>\n", "<td>\n", "546\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "31538.132812\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;HU&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120546168\n", "</td>\n", "<td>\n", "&quot;COCA-COLA 2,25...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "490\n", "</td>\n", "<td>\n", "700\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "30424.207031\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "121562521\n", "</td>\n", "<td>\n", "&quot;Vinea Biel<PERSON> 1,...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "987\n", "</td>\n", "<td>\n", "98\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "30110.007812\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "<td>\n", "...\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "216208983\n", "</td>\n", "<td>\n", "&quot;TVN LIMO GREP ...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120283357\n", "</td>\n", "<td>\n", "&quot;MIRINDA ORANGE...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220201986\n", "</td>\n", "<td>\n", "&quot;FUZETEA GT JAH...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220314073\n", "</td>\n", "<td>\n", "&quot;<PERSON><PERSON><PERSON><PERSON>...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220035567\n", "</td>\n", "<td>\n", "&quot;FANTA 2,25 l&quot;\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220321466\n", "</td>\n", "<td>\n", "&quot;Fuzetea Zero M...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220284665\n", "</td>\n", "<td>\n", "&quot;KINLEY BITTER ...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "120283363\n", "</td>\n", "<td>\n", "&quot;7UP PET 1.5l&quot;\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;CZ&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "219173776\n", "</td>\n", "<td>\n", "&quot;AQUILA CAJ CER...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220173569\n", "</td>\n", "<td>\n", "&quot;TS GFS ZERO LA...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220035551\n", "</td>\n", "<td>\n", "&quot;COCA-COLA ZERO...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "<tr>\n", "<td>\n", "&quot;SK&quot;\n", "</td>\n", "<td>\n", "&quot;Grocery&quot;\n", "</td>\n", "<td>\n", "&quot;DRY&quot;\n", "</td>\n", "<td>\n", "&quot;DRY04&quot;\n", "</td>\n", "<td>\n", "220035565\n", "</td>\n", "<td>\n", "&quot;COCA-COLA 2,25...\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "7\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0\n", "</td>\n", "<td>\n", "0.071429\n", "</td>\n", "</tr>\n", "</tbody>\n", "</table>\n", "</div>"], "text/plain": ["shape: (437, 12)\n", "┌─────────┬──────────┬─────┬───────┬─────┬─────────────┬─────┬──────────────┬───────────────┐\n", "│ country ┆ division ┆ dep ┆ pmg   ┆ ... ┆ full_pallet ┆ mu  ┆ split_pallet ┆ sold_units    │\n", "│ ---     ┆ ---      ┆ --- ┆ ---   ┆     ┆ ---         ┆ --- ┆ ---          ┆ ---           │\n", "│ cat     ┆ cat      ┆ cat ┆ cat   ┆     ┆ i64         ┆ i64 ┆ i64          ┆ f32           │\n", "╞═════════╪══════════╪═════╪═══════╪═════╪═════════════╪═════╪══════════════╪═══════════════╡\n", "│ SK      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 763         ┆ 0   ┆ 0            ┆ 111637.515625 │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ HU      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 791         ┆ 0   ┆ 0            ┆ 75521.6875    │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ HU      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 784         ┆ 0   ┆ 0            ┆ 62622.390625  │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ CZ      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 602         ┆ 0   ┆ 0            ┆ 62297.480469  │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ ...     ┆ ...      ┆ ... ┆ ...   ┆ ... ┆ ...         ┆ ... ┆ ...          ┆ ...           │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ CZ      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 0           ┆ 0   ┆ 0            ┆ 0.071429      │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ SK      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 0           ┆ 0   ┆ 0            ┆ 0.071429      │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ SK      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 0           ┆ 0   ┆ 0            ┆ 0.071429      │\n", "├╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌┼╌╌╌╌╌╌╌╌╌╌╌╌╌╌╌┤\n", "│ SK      ┆ Grocery  ┆ DRY ┆ DRY04 ┆ ... ┆ 0           ┆ 0   ┆ 0            ┆ 0.071429      │\n", "└─────────┴──────────┴─────┴───────┴─────┴─────────────┴─────┴──────────────┴───────────────┘"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["b"]}, {"cell_type": "code", "execution_count": 8, "id": "5d6c1a9b-aa6f-4249-ba7b-87d453ffa9ba", "metadata": {"tags": []}, "outputs": [{"ename": "ComputeError", "evalue": "Joins/or comparisons on categorical dtypes can only happen if they are created under the same global string cache.Hint: set a global StringCache", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mComputeError\u001b[0m                              Traceback (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_13552\\3022616818.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mget_ipython\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrun_cell_magic\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'timeit'\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m''\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m'\\nwith pl.StringCache():  \\n    a = pl.read_parquet(directory / repl_dataset_f, columns=[\\'country\\',\\'store\\', \\'division\\', \\'dep\\', \\'pmg\\', \\'tpnb\\', \\'product_name\\', \\'srp\\', \\'nsrp\\',\\'full_pallet\\', \\'mu\\', \\'split_pallet\\', \\'sold_units\\']).lazy()\\n    a = a.filter(pl.col(\"pmg\").is_in(selected_pmg))\\na = a.collect()\\n'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\IPython\\core\\interactiveshell.py\u001b[0m in \u001b[0;36mrun_cell_magic\u001b[1;34m(self, magic_name, line, cell)\u001b[0m\n\u001b[0;32m   2404\u001b[0m             \u001b[1;32mwith\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mbuiltin_trap\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   2405\u001b[0m                 \u001b[0margs\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;33m(\u001b[0m\u001b[0mmagic_arg_s\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mcell\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 2406\u001b[1;33m                 \u001b[0mresult\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mfn\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   2407\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0mresult\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   2408\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\decorator.py\u001b[0m in \u001b[0;36mfun\u001b[1;34m(*args, **kw)\u001b[0m\n\u001b[0;32m    230\u001b[0m             \u001b[1;32mif\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[0mkwsyntax\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    231\u001b[0m                 \u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mkw\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mfix\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mkw\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0msig\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 232\u001b[1;33m             \u001b[1;32mreturn\u001b[0m \u001b[0mcaller\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mfunc\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m*\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mextras\u001b[0m \u001b[1;33m+\u001b[0m \u001b[0margs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkw\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    233\u001b[0m     \u001b[0mfun\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mfunc\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    234\u001b[0m     \u001b[0mfun\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__doc__\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mfunc\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__doc__\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\IPython\\core\\magic.py\u001b[0m in \u001b[0;36m<lambda>\u001b[1;34m(f, *a, **k)\u001b[0m\n\u001b[0;32m    185\u001b[0m     \u001b[1;31m# but it's overkill for just that one bit of state.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    186\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mmagic_deco\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0marg\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 187\u001b[1;33m         \u001b[0mcall\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;32mlambda\u001b[0m \u001b[0mf\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m*\u001b[0m\u001b[0ma\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mk\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mf\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0ma\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mk\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    188\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    189\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mcallable\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0marg\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\IPython\\core\\magics\\execution.py\u001b[0m in \u001b[0;36mtimeit\u001b[1;34m(self, line, cell, local_ns)\u001b[0m\n\u001b[0;32m   1167\u001b[0m             \u001b[1;32mfor\u001b[0m \u001b[0mindex\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;36m0\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m10\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1168\u001b[0m                 \u001b[0mnumber\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;36m10\u001b[0m \u001b[1;33m**\u001b[0m \u001b[0mindex\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1169\u001b[1;33m                 \u001b[0mtime_number\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mtimer\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtimeit\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnumber\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1170\u001b[0m                 \u001b[1;32mif\u001b[0m \u001b[0mtime_number\u001b[0m \u001b[1;33m>=\u001b[0m \u001b[1;36m0.2\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1171\u001b[0m                     \u001b[1;32mbreak\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\IPython\\core\\magics\\execution.py\u001b[0m in \u001b[0;36mtimeit\u001b[1;34m(self, number)\u001b[0m\n\u001b[0;32m    167\u001b[0m         \u001b[0mgc\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdisable\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    168\u001b[0m         \u001b[1;32mtry\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 169\u001b[1;33m             \u001b[0mtiming\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0minner\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mit\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtimer\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    170\u001b[0m         \u001b[1;32mfinally\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    171\u001b[0m             \u001b[1;32mif\u001b[0m \u001b[0mgcold\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m<magic-timeit>\u001b[0m in \u001b[0;36minner\u001b[1;34m(_it, _timer)\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\polars\\utils.py\u001b[0m in \u001b[0;36mwrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    327\u001b[0m         \u001b[1;32mdef\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mP\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mP\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mT\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    328\u001b[0m             \u001b[0m_rename_kwargs\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mfn\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0maliases\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 329\u001b[1;33m             \u001b[1;32mreturn\u001b[0m \u001b[0mfn\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    330\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    331\u001b[0m         \u001b[1;32mreturn\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\polars\\internals\\lazyframe\\frame.py\u001b[0m in \u001b[0;36mcollect\u001b[1;34m(self, type_coercion, predicate_pushdown, projection_pushdown, simplify_expression, no_optimization, slice_pushdown, common_subplan_elimination, streaming)\u001b[0m\n\u001b[0;32m   1166\u001b[0m             \u001b[0mstreaming\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1167\u001b[0m         )\n\u001b[1;32m-> 1168\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mpli\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mwrap_df\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mldf\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcollect\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1169\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1170\u001b[0m     def sink_parquet(\n", "\u001b[1;31mComputeError\u001b[0m: Joins/or comparisons on categorical dtypes can only happen if they are created under the same global string cache.Hint: set a global StringCache"]}], "source": ["%%timeit\n", "\n", "with pl.<PERSON><PERSON><PERSON>():  \n", "    a = pl.read_parquet(directory / repl_dataset_f, columns=['country','store', 'division', 'dep', 'pmg', 'tpnb', 'product_name', 'srp', 'nsrp','full_pallet', 'mu', 'split_pallet', 'sold_units'])\n", "    a = a.filter(pl.col(\"pmg\").is_in(selected_pmg))\n", "a = a.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "19467dd2-6695-460a-bad3-d0c761096e7f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e8abe98c-8689-4f62-97dc-e13e98044b4d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "c59e410d-83bb-4b36-bf09-b9062a4f4926", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["# for end of the Calculation\n", "def CurrentTime():\n", "    now = datetime. now()\n", "    current_time = now. strftime(\"%H:%M:%S\")\n", "    h = int(current_time[0:2]) # time format: '11:53:12'\n", "    m = int(current_time[3:5])\n", "    s = int(current_time[6:8])\n", "    sec_time = h*3600+m*60+s\n", "    return sec_time\n", "\n", "\n", "# Python decorator to measure execution time of Functions\n", "def timeit(func):\n", "    @wraps(func)\n", "    def timeit_wrapper(*args, **kwargs):\n", "        start_time = time.perf_counter()\n", "        result = func(*args, **kwargs)\n", "        end_time = time.perf_counter()\n", "        total_time = end_time - start_time\n", "        #if func.__name__ == \n", "        print(f' \\n{func.__name__} is done! Elapsed time (sec): {total_time:.2f}')\n", "        return result\n", "    return timeit_wrapper\n", "\n", "\n", "def optimize_types(dataframe):\n", "    np_types = [np.int8 ,np.int16 ,np.int32, np.int64, np.uint8 ,np.uint16, np.uint32, np.uint64, np.float32, np.float64] #, np.float16, np.float32, np.float64\n", "    np_types = [np_type.__name__ for np_type in np_types]\n", "    type_df = pd.DataFrame(data=np_types, columns=['class_type'])\n", "\n", "    type_df['min_value'] = type_df[type_df['class_type'].str.contains(\"int\")]['class_type'].apply(lambda row: np.iinfo(row).min)\n", "    type_df['max_value'] = type_df[type_df['class_type'].str.contains(\"int\")]['class_type'].apply(lambda row: np.iinfo(row).max)\n", "    type_df['min_value_f'] = type_df[type_df['class_type'].str.contains(\"float\")]['class_type'].apply(lambda row: np.finfo(row).min)\n", "    type_df['max_value_f'] = type_df[type_df['class_type'].str.contains(\"float\")]['class_type'].apply(lambda row: np.finfo(row).max)\n", "    type_df['min_value'] = np.where(type_df['min_value'].isna(), type_df['min_value_f'],type_df['min_value'] )\n", "    type_df['max_value'] = np.where(type_df['max_value'].isna(), type_df['max_value_f'],type_df['max_value'] )\n", "    type_df.drop(columns=['min_value_f','max_value_f'], inplace = True)\n", "\n", "\n", "    type_df['range'] = type_df['max_value'] - type_df['min_value']\n", "    type_df.sort_values(by='range', inplace=True)\n", "    try:\n", "        for col in dataframe.loc[:, dataframe.dtypes == np.integer]:\n", "            col_min = dataframe[col].min()\n", "            col_max = dataframe[col].max()\n", "            temp = type_df[(type_df['min_value'] <= col_min) & (type_df['max_value'] >= col_max)]\n", "            optimized_class = temp.loc[temp['range'].idxmin(), 'class_type']\n", "            #print(\"Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}\".format(col, col_min, col_max, optimized_class))\n", "            dataframe[col] = dataframe[col].astype(optimized_class)\n", "    except ValueError:\n", "        pass\n", "    try:\n", "        for col in dataframe.loc[:, (dataframe.dtypes == np.floating)]:\n", "            col_min = dataframe[col].min()\n", "            col_max = dataframe[col].max()\n", "            type_df = type_df[type_df['class_type'].astype(\"string\").str.contains(\"float\")]\n", "            temp = type_df[(type_df['min_value'] <= col_min) & (type_df['max_value'] >= col_max)]\n", "            optimized_class = temp.loc[temp['range'].idxmin(), 'class_type']\n", "            #print(\"Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}\".format(col, col_min, col_max, optimized_class))\n", "            dataframe[col] = dataframe[col].astype(optimized_class)\n", "    except ValueError:\n", "        pass\n", "    return dataframe\n", "\n", "def optimize_floats(df: pd.DataFrame) -> pd.DataFrame:\n", "    floats = df.select_dtypes(include=['float64']).columns.tolist()\n", "    df[floats] = df[floats].apply(pd.to_numeric, downcast='float')\n", "    return df\n", "\n", "def optimize_ints(df: pd.DataFrame) -> pd.DataFrame:\n", "    ints = df.select_dtypes(include=['int64']).columns.tolist()\n", "    df[ints] = df[ints].apply(pd.to_numeric, downcast='integer')\n", "    return df\n", "\n", "def optimize_objects(df: pd.DataFrame) :\n", "    try:\n", "        for col in df.select_dtypes(include=['object']):\n", "            if not (type(df[col][0])==list):\n", "                num_unique_values = len(df[col].unique())\n", "                num_total_values = len(df[col])\n", "                if float(num_unique_values) / num_total_values < 0.5:\n", "                    df[col] = df[col].astype('category')\n", "    except IndexError:\n", "        pass\n", "    return df\n", "\n", "def optimize(df: pd.DataFrame):\n", "    return optimize_floats(optimize_ints(optimize_objects(df)))"]}, {"cell_type": "code", "execution_count": 3, "id": "1477b02a-6271-485e-8279-f98419ba3d46", "metadata": {"tags": []}, "outputs": [], "source": ["act_version_name = '23_Q1_vol2_rep_wh_new_wh_rtv_transfer_' \n", "new_version_name = 'what_if_+10%_casecap' \n", "\n", "saved_filename = \"nov\" # it is important when you want to run SQL part too\n", "directory = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\")\n", "repl_dataset_f = 'inputs/Repl_Dataset_2022_new_14w_27w_promo_flag.parquet' \n", "\n", "# --- For creating Replenishment Dataset ---\n", "planogram_f = 'inputs/files_for_dataset/plano/planogram_22_14w_27w.csv.gz'\n", "items_sold_f = 'inputs/files_for_dataset/item_22_14w_27w.parquet'\n", "items_sold_dotcom = 'inputs/files_for_dataset/isold_dotcom_22_14w_27w.parquet'\n", "stock_f = 'inputs/files_for_dataset/stock_22_14w_27w.parquet'\n", "ops_dev_f = 'inputs/files_for_dataset/opsdev_verz_0119_.parquet'\n", "box_op_type_f = 'inputs/files_for_dataset/ownbrand_opening_type_2022.xlsx'\n", "cases_f = 'inputs/files_for_dataset/cases_22_14w_27w.parquet'\n", "pallet_capacity_f = 'inputs/files_for_dataset/pallet_capacity_CE.parquet'\n", "broken_cases_f = 'inputs/files_for_dataset/broken_cases_list_22w14_27.csv.gz'\n", "\n", "# --- For rest of the model calculating ---\n", "excel_inputs_f = 'inputs/Repl/Stores_Inputs_2023_Q1_Vol1.xlsx'\n", "losses_f = 'inputs/files_for_dataset/losses_22_14w_27w.parquet.gz'\n", "most_f = 'inputs\\MOST\\Repl\\MOST_Repl_2023.xlsb'\n", "add_hrs_produce = \"ReplModel_2021/Summary/Q1 2021/Additional HoursProduce.xlsx\"\n", "\n", "selected_tpn = 'inputs\\Repl\\selected_tpns.xlsx'\n", "\n", "act_model_outputs = f'outputs/OPB_DEP_{act_version_name}.xlsx' \n", "act_model_insights = f'outputs/INSIGHT_{act_version_name}.parquet.gz'\n", "\n", "# --- Warehouse Datasets\n", "wh_prof_drivers = \"inputs/WH/WH_profile_drivers_profiles23.xlsx\"\n", "wh_most = \"inputs/MOST/WH/WAREHOUSE MOST_Repl_2023.xlsb\"\n", "wh_cases_pmg = \"inputs/WH/wh_cases_pmg.parquet.gz\"\n", "wh_pattisserie = \"inputs/WH/wh_pattisserie.parquet.gz\"\n", "wh_deliveries = \"inputs/WH/wh_deliveries_data.parquet.gz\"\n", "\n", "\n", "\n", "####### Store Selector #############\n", "\n", "stores = list(pd.read_excel(directory / excel_inputs_f, usecols=['Store'])['Store'].unique())"]}, {"cell_type": "code", "execution_count": 4, "id": "7fac0609-d8c5-4e7d-92c6-badf816a493e", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" \n", "Store_Inputs_Creator is done! Elapsed time (sec): 6.67\n"]}], "source": ["@timeit\n", "def Store_Inputs_Creator(directory,excel_inputs_f, stores):\n", "    \n", "    # Store inputs\n", "\n", "\n", "   \n", "    pmg_df = pd.read_excel(directory / excel_inputs_f, 'pmg')\n", "    pmg_df = pmg_df[pmg_df.Area=='Replenishment']\n", "    store_list_df = pd.read_excel(directory / excel_inputs_f, 'store_list')\n", "    store_list_df = store_list_df.loc[store_list_df.Store.isin(stores)]\n", "    capping_df = pd.read_excel(directory / excel_inputs_f, 'capping')\n", "    capping_df['is_capping_shelf'] = 1\n", "    Dprofiles_df = pd.read_excel(directory / excel_inputs_f, 'Dprofiles')\n", "    Pprofiles_df = pd.read_excel(directory / excel_inputs_f, 'Pprofiles')\n", "    pmg_array = pmg_df.values\n", "    store_array = store_list_df.values\n", "    result = len(store_array) * len(pmg_array) #\n", "    df_array = np.empty([result,9], dtype='object') # create an empty array\n", "    counter = 0\n", "    for s in range(len(store_array)):\n", "        for p in range(len(pmg_array)):\n", "            df_array[counter][0] = store_array[s][1] # country\n", "            df_array[counter][1] = store_array[s][0] # store\n", "            df_array[counter][2] = store_array[s][2] # store_name\n", "            df_array[counter][3] = store_array[s][3] # sqm\n", "            df_array[counter][4] = store_array[s][4] # format\n", "            df_array[counter][5] = pmg_array[p][0] # pmg\n", "            df_array[counter][6] = pmg_array[p][1] # pmg_name\n", "            df_array[counter][7] = pmg_array[p][2] # dep\n", "            df_array[counter][8] = pmg_array[p][3] # division\n", "            counter += 1\n", "    store_inputs = pd.DataFrame(columns=['Country', 'Store', 'Store Name', 'Plan Size', 'Format', 'Pmg', 'Pmg Name', 'Dep', 'Division'])\n", "    store_inputs = pd.concat([store_inputs,pd.DataFrame(df_array, columns=store_inputs.columns)])\n", "    store_inputs['Dep'] = np.where(store_inputs.Pmg=='HDL01', 'NEW', store_inputs.Dep)\n", "    store_inputs = store_inputs.merge(capping_df, on=['Store', 'Pmg'], how='left')\n", "    store_inputs['is_capping_shelf'] = store_inputs['is_capping_shelf'].replace(np.nan,0)\n", "    store_inputs = store_inputs.merge(Dprofiles_df, on=['Store', 'Dep'], how='left')\n", "    store_inputs = store_inputs.merge(Pprofiles_df, on=['Country', 'Format', 'Pmg'], how='left')\n", "    store_inputs[\"Store\"] = store_inputs[\"Store\"].astype(\"int64\")\n", "    # store_inputs.columns = [i.lower() for i in store_inputs.columns]\n", "    return store_inputs\n", "\n", "store_inputs = Store_Inputs_Creator(directory, excel_inputs_f, stores)"]}, {"cell_type": "code", "execution_count": null, "id": "16e55725-62c3-4b9a-933f-ddaa16bd6ae6", "metadata": {}, "outputs": [], "source": ["store_inputs.Dep.unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "4605cb1b-b683-4525-a53a-b93aeb8a9401", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "with pl.<PERSON><PERSON><PERSON>():   \n", "    print('Repl_Dataset Build: has been started to calculate...')\n", "\n", "    #Creating Base for Repl_Dataset\n", "    weekdays = ['Monday',\"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\" , \"Saturday\", \"Sunday\"]\n", "    store_inputs_lower = store_inputs.copy()\n", "    store_inputs_lower.columns = [i.lower() for i in store_inputs_lower.columns ]\n", "    store_inputs_lower = store_inputs_lower.rename(columns={'pmg name':'pmg_name'})\n", "\n", "    store_inputs_lower = store_inputs_lower[['store','country','format', 'division', 'pmg','dep', 'is_capping_shelf']].drop_duplicates()\n", "    store_inputs_lower = pl.from_pandas(store_inputs_lower).lazy()\n", "    \n", "    planogram = pl.read_csv(directory / planogram_f, sep=',', ignore_errors=True).lazy()\n", "    planogram = planogram.select(['store', 'tpnb', 'icase', 'capacity'])\n", "    planogram = planogram.unique(subset=['tpnb', 'icase', 'store', 'capacity'])\n", "\n", "    opsdev = pl.read_parquet(directory / ops_dev_f).lazy()\n", "    opsdev = opsdev.select(['store', 'tpnb', 'srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', 'checkout_stand_flag', 'clipstrip_flag', 'backroom_flag' ])\n", "    \n", "    stock = pl.read_parquet(directory / stock_f ).lazy()\n", "    stock = stock.select(['store', 'day', 'tpnb', 'stock', 'item_price'])\n", "    \n", "    isold = pl.read_parquet(directory / items_sold_f ).lazy()\n", "    isold_dotcom = pl.read_parquet(directory / items_sold_dotcom ).lazy()\n", "    isold_dotcom = isold_dotcom.select(['store', 'day', 'tpnb', 'sold_units_dotcom'])\n", "    \n", "    cases = pl.read_parquet(directory / cases_f ).lazy()\n", "    cases = cases.select(['store', 'day', 'tpnb', 'unit','artgld_case_capacity'])\n", "    cases = cases.with_columns([\n", "        (pl.col('unit') / pl.col(\"artgld_case_capacity\")).alias(\"cases_delivered\")])\n", "    \n", "    op_type = pl.read_excel(directory / box_op_type_f).lazy()\n", "    pallet_cap = pl.read_parquet(directory / pallet_capacity_f ).lazy()\n", "    \n", "\n", "\n", "    print('Repl_Dataset Build: Inputs are loaded into Memory!')\n", "\n", "\n", "    Repl_Dataset = isold.select(['store','pmg', 'tpnb']).unique()\n", "    Repl_Dataset = Repl_Dataset.with_columns([  pl.lit(None).alias(\"day\")])\n", "    Repl_Dataset = Repl_Dataset.with_columns([pl.col(\"day\").map(lambda s: weekdays).alias(\"day\")])\n", "    Repl_Dataset = Repl_Dataset.explode(\"day\")\n", "    Repl_Dataset = Repl_Dataset.unique()\n", "    Repl_Dataset = Repl_Dataset.join(isold.select(['store','pmg','tpnb', 'day','sold_units', 'sales_excl_vat']), on=['store','pmg','tpnb', 'day'], how='left')\n", "    Repl_Dataset = Repl_Dataset.join(isold.select([pl.all().exclude(['sold_units', 'sales_excl_vat', 'day','__index_level_0__'])]).unique(), on=['store','pmg','tpnb'], how='left')\n", "    Repl_Dataset = Repl_Dataset.with_columns([pl.when(pl.col(\"pmg\") == 'DRY18').then('DRY15').otherwise(pl.col(\"pmg\")).alias(\"pmg\")])\n", "    Repl_Dataset = Repl_Dataset.with_columns([pl.col(\"day\").cast(pl.Utf8) ])\n", "    Repl_Dataset = Repl_Dataset.join(store_inputs_lower, on = ['store', 'pmg'], how = 'left')\n", "    Repl_Dataset = Repl_Dataset.join(isold_dotcom, on=['store','day', 'tpnb'], how='left') \n", "    Repl_Dataset = Repl_Dataset.join(stock, on=['store','day', 'tpnb'], how='left')\n", "    Repl_Dataset = Repl_Dataset.join(opsdev, on=['tpnb', 'store'], how='left')\n", "    Repl_Dataset = Repl_Dataset.join(planogram, on=['store', 'tpnb'], how='left')\n", "    Repl_Dataset = Repl_Dataset.join(cases, on = ['store', 'day', 'tpnb'], how = 'left')\n", "    Repl_Dataset = Repl_Dataset.join(op_type, on = ['country', 'tpnb'], how = 'left')\n", "    Repl_Dataset = Repl_Dataset.join(pallet_cap, on = ['country', 'tpnb'], how = 'left')\n", "    Repl_Dataset = Repl_Dataset.fill_null(0)\n", "\n", "    \n", "    Repl_Dataset = Repl_Dataset.with_columns([\n", "                    pl.when((pl.col(\"srp\") == 0) & (pl.col(\"nsrp\") == 0) & (pl.col(\"full_pallet\") == 0) & (pl.col(\"mu\") == 0) & (pl.col(\"split_pallet\") == 0)).then(1).otherwise(pl.col('nsrp')).alias('nsrp'),\n", "                    pl.when(pl.col(\"icase\") == 0).then(pl.col(\"case_capacity\")).otherwise(pl.col('icase')).alias('icase'),   \n", "                                                ]).drop(\"case_capacity\").rename({\"icase\": \"case_capacity\"})\n", "    \n", "    Repl_Dataset = Repl_Dataset.with_columns([\n", "                        pl.when(pl.col(\"case_capacity\") < pl.col(\"artgld_case_capacity\")).then(pl.col(\"artgld_case_capacity\")).otherwise(pl.col('case_capacity')).alias('case_capacity')\n", "                                            ]).drop(\"artgld_case_capacity\").fill_null(0)\n", "    \n", "    Repl_Dataset = Repl_Dataset.with_columns([\n", "        (pl.col(\"unit\") / pl.col(\"case_capacity\")).alias(\"cases_delivered\")\n", "    ])\n", "    \n", "    Repl_Dataset = Repl_Dataset.with_columns([\n", "        pl.when(pl.col(\"unit_type\") != 'KG').then(\"SNGL\").otherwise(pl.col('unit_type')).alias('unit_type'),\n", "        pl.col(\"pallet_capacity\").round(0).alias(\"pallet_capacity\")\n", "                                                ])\n", "    \n", "    Repl_Dataset = Repl_Dataset.with_columns([\n", "    pl.when(pl.col('dep') == 'NEW').then('HDL').otherwise(pl.col('dep')).alias('dep')\n", "    ])\n", "    \n", "    Repl_Dataset = Repl_Dataset.collect().to_pandas()\n", "    Repl_Dataset = optimize_objects(optimize_types(Repl_Dataset))\n", "    print('Repl_Dataset Build: Done!!')\n", "    \n", "    \n", "broken_cases_list = pd.read_csv(directory /broken_cases_f) \n", "Repl_Dataset = Repl_Dataset.merge(broken_cases_list, on = ['store', 'day', 'tpnb'], how = 'left').replace(np.nan, 0)\n", "\n", "\n", "\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "ce9ec806-7772-4fc4-92b0-41a45554ce42", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.float_format', lambda x: '%.3f' % x)\n", "pallet_cap.select([pl.col('pallet_capacity')]).collect().to_pandas().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "11878813-876e-45c9-93da-2cda1a4f4098", "metadata": {}, "outputs": [], "source": ["pallet_cap.with_columns([pl.col('pallet_capacity')]).collect().head()"]}, {"cell_type": "code", "execution_count": null, "id": "f28fb2b7-5260-4b41-bd6c-211ac1bf799b", "metadata": {}, "outputs": [], "source": ["pallet_cap.pallet_capacity.sum()"]}, {"cell_type": "code", "execution_count": null, "id": "3aaef211-4a34-47ee-a3a4-59cb732a587c", "metadata": {}, "outputs": [], "source": ["a.filter(pl.col(\"dep\") == 'NEW')"]}, {"cell_type": "code", "execution_count": null, "id": "17aef263-8b36-4520-ac58-8bfa0ffbbbb1", "metadata": {}, "outputs": [], "source": ["a.select([pl.col(\"pallet_capacity\").round(0).alias(\"pallet_capacity\").cast(pl.Int64)]).sum()"]}, {"cell_type": "code", "execution_count": null, "id": "341487eb-66f8-4b68-8138-88e7d01b6125", "metadata": {}, "outputs": [], "source": ["b = pl.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\apps\\ReplModel_App_2023\\inputs\\Repl_Dataset_2022_23_Q1_vol3_new_prices.parquet\").lazy()\n", "b.select([pl.col(\"pallet_capacity\").round(0).alias(\"pallet_capacity\").cast(pl.Int64)]).sum().collect()"]}, {"cell_type": "code", "execution_count": null, "id": "9c68efeb-adc9-4e60-93ff-9e91c3ded045", "metadata": {}, "outputs": [], "source": ["b.select([pl.col(\"pallet_capacity\")]).sum().collect()"]}, {"cell_type": "code", "execution_count": null, "id": "0390f862-1bce-4e7c-b5e4-5a7cd4d8d8e6", "metadata": {}, "outputs": [], "source": ["a = Repl_Dataset.collect()"]}, {"cell_type": "code", "execution_count": null, "id": "81d164af-7201-4bc4-a983-625a035dab8e", "metadata": {"tags": []}, "outputs": [], "source": ["print('Repl_Dataset Build: has been started to calculate...')\n", "\n", "#Creating Base for Repl_Dataset\n", "weekdays = ['Monday',\"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\" , \"Saturday\", \"Sunday\"]\n", "store_inputs_lower = store_inputs.copy()\n", "store_inputs_lower.columns = [i.lower() for i in store_inputs_lower.columns ]\n", "store_inputs_lower = store_inputs_lower.rename(columns={'pmg name':'pmg_name'})\n", "\n", "\n", "store_inputs_lower = optimize_objects(optimize_types(store_inputs_lower[['store','format','country', 'division', 'pmg','dep','is_capping_shelf']].drop_duplicates()))\n", "# store_inputs_lower = dd.from_pandas(store_inputs_lower, npartitions = 1)\n", "planogram = optimize_objects(optimize_types(pd.read_csv(directory / planogram_f, sep=',', )))\n", "planogram = planogram[['store', 'tpnb', 'icase', 'capacity']]\n", "planogram = planogram.drop_duplicates(subset=['tpnb', 'icase', 'store', 'capacity'])\n", "opsdev = optimize_objects(optimize_types(pq.read_table(directory / ops_dev_f ).to_pandas()))\n", "opsdev = opsdev[['store', 'tpnb', 'srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', 'checkout_stand_flag', 'clipstrip_flag', 'backroom_flag' ]]\n", "stock = optimize_objects(optimize_types(pq.read_table(directory / stock_f ).to_pandas()))\n", "stock = stock[['store', 'day', 'tpnb', 'stock', 'item_price']]\n", "isold = optimize_objects(optimize_types(pq.read_table(directory / items_sold_f ).to_pandas()))\n", "#isold = isold.sort_values(['consigment_type'], ascending = False).drop_duplicates(subset=['store', 'tpnb', 'day'], keep = \"first\")\n", "isold_dotcom = optimize_objects(optimize_types(pq.read_table(directory / items_sold_dotcom ).to_pandas()))\n", "isold_dotcom = isold_dotcom[['store', 'day', 'tpnb', 'sold_units_dotcom']]\n", "cases = optimize_objects(optimize_types(pq.read_table(directory / cases_f ).to_pandas()))\n", "cases = cases[['store', 'day', 'tpnb', 'unit','artgld_case_capacity']]\n", "cases['cases_delivered'] = cases['unit'] / cases['artgld_case_capacity']\n", "op_type = optimize_objects(optimize_types(pd.read_excel(directory / box_op_type_f)))\n", "# op_type = dd.from_pandas(op_type, 1)\n", "pallet_cap = optimize_objects(optimize_types(pq.read_table(directory / pallet_capacity_f ).to_pandas()))\n", "\n", "print('Repl_Dataset Build: Inputs are loaded into Memory!')\n", "\n", "Repl_Dataset = isold[['store','pmg', 'tpnb']].drop_duplicates()\n", "#Repl_Dataset = Repl_Dataset.loc[Repl_Dataset.store.isin(list(store_inputs_lower.store.unique())) ]\n", "Repl_Dataset['day'] = \"\"\n", "Repl_Dataset['day'] = Repl_Dataset['day'].apply(lambda x: weekdays)\n", "Repl_Dataset = Repl_Dataset.explode('day').drop_duplicates()\n", "Repl_Dataset = Repl_Dataset.merge(isold[['store','pmg','tpnb', 'day','sold_units', 'sales_excl_vat' ]], on=['store','pmg','tpnb', 'day'], how='left')\n", "Repl_Dataset = Repl_Dataset.merge(isold[isold.columns[~isold.columns.isin(['sold_units', 'sales_excl_vat', 'day'])]].drop_duplicates(), on=['store','pmg','tpnb'], how='left')\n", "Repl_Dataset['pmg'] = np.where(Repl_Dataset['pmg'] == 'DRY18', 'DRY15', Repl_Dataset['pmg']) # we dont use DRY18 anymore we should use DRY15 ('Crisps Nuts and Snacks') instead\n", "Repl_Dataset = Repl_Dataset.merge(store_inputs_lower, on = ['store', 'pmg'], how = 'left')\n", "Repl_Dataset = Repl_Dataset.merge(isold_dotcom, on=['store','day', 'tpnb'], how='left')\n", "Repl_Dataset = Repl_Dataset.merge(stock, on=['store','day', 'tpnb'], how='left')\n", "Repl_Dataset = Repl_Dataset.merge(opsdev, on=['tpnb', 'store'], how='left')\n", "Repl_Dataset = Repl_Dataset.merge(planogram, on=['store', 'tpnb'], how='left')\n", "Repl_Dataset = Repl_Dataset.merge(cases, on = ['store', 'day', 'tpnb'], how = 'left')\n", "Repl_Dataset = Repl_Dataset.merge(op_type, on = ['country', 'tpnb'], how = 'left')\n", "Repl_Dataset = Repl_Dataset.merge(pallet_cap, on = ['country', 'tpnb'], how = 'left')\n", "\n", "\n", "Repl_Dataset.replace(np.nan, 0, inplace = True)\n", "# Repl_Dataset['dep'] = Repl_Dataset.pmg.str[:3].astype(\"category\")\n", "Repl_Dataset['day'] = Repl_Dataset['day'].astype(\"category\")\n", "\n", "\n", "int_list = ['srp','nsrp','mu','full_pallet','split_pallet','checkout_stand_flag',\n", "            'backroom_flag', 'clipstrip_flag' ,'case_capacity', 'Perforated box','Shrink','Tray','Tray + <PERSON>','Tray + Shrink',\n", "            'is_capping_shelf', 'capacity', 'icase'] #\n", "Repl_Dataset[int_list] = Repl_Dataset[int_list].apply(lambda x: x.astype(\"int32\"))\n", "int_part = optimize_types(Repl_Dataset.select_dtypes(include = [\"float\", \"int\"]))\n", "cat_part = Repl_Dataset.select_dtypes(exclude = [\"float\", \"int\"])\n", "Repl_Dataset = pd.concat([int_part,cat_part], axis = 1 )\n", "\n", "\n", "\n", "\n", "Repl_Dataset['nsrp'] = np.where((Repl_Dataset.srp==0)&(Repl_Dataset.nsrp==0)&(Repl_Dataset.full_pallet==0)&(Repl_Dataset.mu==0)&(Repl_Dataset.split_pallet==0), 1, Repl_Dataset['nsrp'])\n", "Repl_Dataset['icase'] = np.where(Repl_Dataset.icase==0, Repl_Dataset.case_capacity, Repl_Dataset['icase'])\n", "Repl_Dataset.drop(['case_capacity'], axis=1, inplace = True)\n", "Repl_Dataset =  Repl_Dataset.rename(columns={'icase': 'case_capacity'})\n", "Repl_Dataset['case_capacity'] = np.where(Repl_Dataset.case_capacity < Repl_Dataset.artgld_case_capacity, Repl_Dataset.artgld_case_capacity, Repl_Dataset['case_capacity'])\n", "Repl_Dataset['cases_delivered'] = Repl_Dataset['unit'] / Repl_Dataset['case_capacity']\n", "Repl_Dataset.drop(['artgld_case_capacity'], axis=1, inplace = True)\n", "Repl_Dataset.replace(np.nan, 0, inplace = True)\n", "Repl_Dataset['unit_type'] = np.where(Repl_Dataset.unit_type!='KG', \"SNGL\", Repl_Dataset['unit_type'])\n", "# Repl_Dataset['pallet_capacity'] = round(Repl_Dataset['pallet_capacity'], 0)"]}, {"cell_type": "code", "execution_count": null, "id": "1ec91e03-5a09-4328-a2e8-f23e903cd3b9", "metadata": {}, "outputs": [], "source": ["Repl_Dataset['pallet_capacity'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "15e860c7-4a1b-4f86-a6c8-abf08034ed80", "metadata": {}, "outputs": [], "source": ["round(Repl_Dataset['pallet_capacity'], 0).sum()"]}, {"cell_type": "code", "execution_count": null, "id": "68ea6adc-29ab-4eb2-ab99-f86290fc5171", "metadata": {}, "outputs": [], "source": ["a.columns"]}, {"cell_type": "code", "execution_count": null, "id": "e2de677b-d21c-4134-9c1e-507f5d2da17f", "metadata": {}, "outputs": [], "source": ["b = a.to_pandas()"]}, {"cell_type": "code", "execution_count": null, "id": "7cc36068-2a72-4e0a-a77e-93d3f29af23f", "metadata": {}, "outputs": [], "source": ["(b.unit / b.case_capacity).sum()"]}, {"cell_type": "code", "execution_count": null, "id": "92f9a5fc-8a57-449d-bd72-d1e68e92d408", "metadata": {}, "outputs": [], "source": ["a = a['cases_delivered'].fill_null(0)"]}, {"cell_type": "code", "execution_count": null, "id": "93fca8bd-dcb6-4fa2-81ad-a5ab4e6aa5b7", "metadata": {}, "outputs": [], "source": ["a.filter(\n", "    pl.col(\"cases_delivered\").is_infinite(),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f7b63bc0-ceef-43e1-8b01-c979c53608df", "metadata": {}, "outputs": [], "source": ["a['cases_delivered'].is_infinite()"]}, {"cell_type": "code", "execution_count": null, "id": "681d5fa3-6b78-4420-9b79-c60cdd978c84", "metadata": {}, "outputs": [], "source": ["a[:,['cases_delivered']].fill_null(0).sum()"]}, {"cell_type": "code", "execution_count": null, "id": "c5b2c1dd-0d6c-4ca7-9e9f-812e351b81d6", "metadata": {}, "outputs": [], "source": ["a.filter(pl.col(\"pmg\").str.contains(\"DRY1\")).select(['pmg']).unique()"]}, {"cell_type": "code", "execution_count": null, "id": "aa590c7d-8c87-4315-be53-fc75a6504cec", "metadata": {}, "outputs": [], "source": ["file_name = f'inputs/Repl_Dataset_2022_polars.parquet'\n", "Repl_Dataset.to_parquet(directory / file_name, index=False,compression = 'gzip')"]}, {"cell_type": "code", "execution_count": null, "id": "c0dfaa29-8407-4de2-8e47-8dfb1b60862d", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_columns', None)\n", "Repl_Dataset.shape"]}, {"cell_type": "code", "execution_count": null, "id": "893fcba9-8994-4f88-938a-c89d629ca332", "metadata": {}, "outputs": [], "source": ["Repl_Dataset = optimize_objects(optimize_types(Repl_Dataset))"]}, {"cell_type": "code", "execution_count": null, "id": "d7b5e522-15be-41b5-96ad-c9341cc4551b", "metadata": {}, "outputs": [], "source": ["broken_cases_list = pd.read_csv(directory /broken_cases_f) "]}, {"cell_type": "code", "execution_count": null, "id": "5c82071e-135d-4937-9277-54ed57a2d11b", "metadata": {}, "outputs": [], "source": ["broken_cases_list.head()"]}, {"cell_type": "code", "execution_count": null, "id": "40e977f2-eb56-44dd-b5b7-5d2214a68fe9", "metadata": {}, "outputs": [], "source": ["Repl_Dataset.merge(broken_cases_list, on = ['store', 'day', 'tpnb'], how = 'left').replace(np.nan, 0).head()"]}, {"cell_type": "code", "execution_count": null, "id": "948d021c-fd02-42ca-987c-62dd032b38d8", "metadata": {}, "outputs": [], "source": ["broken_cases_list = pd.read_csv(directory /broken_cases_f) \n", "Repl_Dataset = Repl_Dataset.merge(broken_cases_list, on = ['store', 'day', 'tpnb'], how = 'left').replace(np.nan, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "67ded09f-3c60-42c3-a233-51d4ff89a115", "metadata": {}, "outputs": [], "source": ["Repl_Dataset.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2d7a2f2f-8d30-47c5-add8-5cb9ab4038e3", "metadata": {}, "outputs": [], "source": ["a.columns = list(map(lambda x: x.replace(\"icase\", \"case_capacity\"), a.columns))"]}, {"cell_type": "code", "execution_count": null, "id": "2f870ae4-652a-4b8b-87ba-ddabe581dee2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "6402ff49-1828-4a1c-8765-628fe1f55046", "metadata": {}, "source": ["## TPN level with Polar"]}, {"cell_type": "code", "execution_count": 5, "id": "7aa5f9d2-40ea-4438-b89a-5ef586ffb3be", "metadata": {}, "outputs": [], "source": ["store_inputs = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_feb\\TPNs_withPolars\\store_inputs\")\n", "driver_pro = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_feb\\TPNs_withPolars\\driver_pro\")\n", "driver_repl = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_feb\\TPNs_withPolars\\driver_repl\")\n", "directory = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\")\n", "most_f = 'inputs\\MOST\\Repl\\MOST_Repl_2023_Q1_Vol3.xlsb'\n", "excel_inputs_f = 'inputs/Repl/Stores_Inputs_2023_Q1_Vol1.xlsx'\n", "REX_ALLOWANCE = 4"]}, {"cell_type": "code", "execution_count": null, "id": "1f433ac3-d1dd-42f2-bbc2-4a69065ca1cc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "f24d4223-f467-4d67-afb3-347e03989de0", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 13 s\n"]}], "source": ["%%time\n", "\n", "\n", "\n", "def move_column_inplace(df, col, pos):\n", "    col = df.pop(col)\n", "    df.insert(pos, col.name, col)\n", "\n", "\n", "def TimeValues_Calculation_TPN(directory, store_inputs, driver_pro, driver_repl, most_f ):    \n", "    drivers_tpnb = pd.concat([driver_repl, driver_pro]).replace(np.nan,0)\n", "    drivers_tpnb['Case on Pallet'] = drivers_tpnb['cases_delivered']/(drivers_tpnb['New Delivery - Pallets']+drivers_tpnb['New Delivery - Rollcages']*0.62)\n", "    drivers_tpnb['Case on Pallet'] = drivers_tpnb['Case on Pallet'].replace(np.nan,7) # if zero then 7 cases on pallet\n", "    drivers_tpnb['Modules to go'] = 1\n", "\n", "    drivers_tpnb.rename(columns={'cases_delivered' : 'Cases Delivered'}, inplace = True)\n", "\n", "    dep_profiles = store_inputs[['Store','Dep','Fridge Doors','Eggs displayed at UHT Milks',\n", "                                 'Racking','Cardboard Baller','Capping Shelves',\n", "                                 'Lift Allowance','Distance: WH to SF','Distance: WH to Yard','Steps: SF-Printer','CS_DIST_CSD_2_WH','Steps Dotcom-WH',\n", "                                 'Steps (gates - work)','<PERSON><PERSON>','GBP_rates'\n", "                                 ]].drop_duplicates()\n", "\n", "    dep_profiles.rename(columns={'Store' : 'store', 'Dep' : 'dep'}, inplace = True)\n", "    drivers_tpnb = drivers_tpnb.merge(dep_profiles, on = ['store', 'dep'], how = 'left')\n", "\n", "    Final_drivers_for_TPN_level = drivers_tpnb.copy()\n", "\n", "\n", "\n", "\n", "\n", "    stores_df = store_inputs[['Country','Store','Format','Store Name','Plan Size']].drop_duplicates()\n", "    stores_df = stores_df[stores_df.Store.isin(list(set(drivers_tpnb.store.values)))]\n", "    storelist_array = stores_df[['Country', 'Store', 'Format']].drop_duplicates().values\n", "    shelftrolley_extra_stores = store_inputs[['Country','Store', '1K_stores_for_ShelfTrolley_extra']].drop_duplicates()\n", "\n", "\n", "    most_file = pd.ExcelFile( directory / most_f, engine='pyxlsb')\n", "    activity_list = pd.read_excel(most_file,'Time Values',skiprows=3)\n", "\n", "    new_header = activity_list.iloc[0] #grab the first row for the header\n", "    activity_list = activity_list[1:] #take the data less the header row\n", "    activity_list.columns = new_header #set the header row as the df header\n", "\n", "    cols = ['Activity_key_activities','Suboperation Description','Activity group','V F','DRIVER_1','DRIVER_2','FREQ2','DRIVER_3','DRIVER_4','PROFILE','RA','Head','Newspaper_Activity']\n", "    cols2 = ['Activity_key_activities','Suboperation','Activity_Group','V_F','Driver_1','Driver_2','Freq_Driver_2','Driver_3','Driver_4','Profile','RA','Head','Newspaper_Activity']\n", "    activity_list = activity_list[cols]\n", "    for x, y in zip(cols, cols2):\n", "        activity_list.rename(columns={x:y},inplace=True)\n", "\n", "    activity_list.dropna(subset=['Activity_key_activities'],inplace=True)\n", "    activity_list.rename(columns={'Activity_key_activities':'Activity_key'},inplace=True)\n", "    activity_list['Freq_Driver_2'] = activity_list['Freq_Driver_2'].replace(np.nan,0)\n", "    activity_list = activity_list.replace(np.nan,'no_driver')\n", "\n", "    activities = activity_list[['Activity_key']].copy()\n", "    activities['Country'] = ''\n", "    activities['Format'] = ''\n", "    activities['Dep'] = ''\n", "    activities['Store'] = 0\n", "    activities['day'] = ''\n", "\n", "    times = pd.read_excel(most_file,'TimeValues_Py',usecols='M:R')\n", "    times.dropna(subset=['Activity_key_times'],inplace=True)\n", "    times.rename(columns={'Activity_key_times':'Activity_key'},inplace=True)\n", "    # times.drop(times[times.basic_time == 0].index, inplace = True)\n", "    # times.drop(times[times.freq == 0].index, inplace = True)\n", "\n", "    freq_Shelftrolley_extra = times[(times['Activity_key'].str.contains(\"shelf trolley\")) & (times['Format'] == 'Express')][['Activity_key', 'Country', 'Dep', 'freq']].drop_duplicates()\n", "\n", "\n", "    times_array = activities.values\n", "    departments = pd.DataFrame([x for x in drivers_tpnb.dep.unique()], columns=['Dep'])\n", "    dep_array = departments.values\n", "\n", "    week_df = pd.DataFrame([a for a in drivers_tpnb['day'].unique()], columns=['day'])\n", "    weekdays_array = week_df.values\n", "\n", "\n", "\n", "    df_times = pd.DataFrame(columns=activities.columns)\n", "    result = len(storelist_array) * len(weekdays_array) * len(dep_array) * len(times_array)\n", "    df_array = np.empty([result,6], dtype='object') # create an empty array\n", "    counter = 0\n", "    for a in range(len(times_array)):\n", "        for d in range(len(dep_array)):\n", "            for s in range(len(storelist_array)):\n", "                for w in range(len(weekdays_array)):\n", "                    df_array[counter][0] = times_array[a][0] # activity name\n", "                    df_array[counter][3] = dep_array[d][0] # department\n", "                    df_array[counter][1] = storelist_array[s][0] # country\n", "                    df_array[counter][2] = storelist_array[s][2] # format\n", "                    df_array[counter][4] = storelist_array[s][1] # store\n", "                    df_array[counter][5] = weekdays_array[w][0] # day\n", "                    counter += 1\n", "    df_times = pd.concat([df_times,pd.DataFrame(df_array, columns=df_times.columns)])\n", "    df_times = df_times.merge(times, on=['Activity_key','Country','Format', 'Dep'], how='left')\n", "    df_times = df_times.merge(activity_list, on=['Activity_key'], how='left')\n", "    df_times.Store = pd.to_numeric(df_times.Store, errors='coerce')\n", "\n", "    ######### ShelfTrolley 1K stores extra hours settings #########\n", "\n", "    df_times = df_times.merge(shelftrolley_extra_stores, on=['Country', 'Store'], how='left')\n", "\n", "    only_1k_stores_for_shelfTrolley = df_times[df_times[\"1K_stores_for_ShelfTrolley_extra\"] == 1]\n", "\n", "    freq_Shelftrolley_extra = freq_Shelftrolley_extra[freq_Shelftrolley_extra.Country.isin(df_times.Country.unique().tolist())]\n", "    df_times = df_times[df_times[\"1K_stores_for_ShelfTrolley_extra\"] == 0]\n", "    dict_list = freq_Shelftrolley_extra.groupby([\"Activity_key\", 'Country', 'Dep'])[\"freq\"].apply(lambda s: s.tolist()).to_dict()\n", "\n", "    for key, value in dict_list.items():\n", "        only_1k_stores_for_shelfTrolley.loc[(only_1k_stores_for_shelfTrolley['Activity_key'] == key[0])\n", "                      & (only_1k_stores_for_shelfTrolley['Country'] == key[1])\n", "                      & (only_1k_stores_for_shelfTrolley['Dep'] == key[2]), 'freq'] = value[0]\n", "\n", "\n", "    df_times = pd.concat([df_times,only_1k_stores_for_shelfTrolley ])\n", "\n", "    df_times.drop(\"1K_stores_for_ShelfTrolley_extra\", axis=1, inplace=True)\n", "\n", "\n", "\n", "    #### MERGING PART\n", "\n", "    drivers_tpnb.rename(columns={'store' : 'Store', 'dep' : 'Dep', 'pmg' : 'Pmg', 'tpnb' : 'Tpnb', 'country' : 'Country' }, inplace = True)\n", "\n", "\n", "\n", "    # drivers_tpnb = drivers_tpnb.groupby(['Store','day','Dep', 'Pmg', 'Tpnb'], as_index = False,observed=True ).sum()\n", "    drivers_tpnb = drivers_tpnb.melt(id_vars=['Country','Store', 'Dep', 'Pmg', 'Tpnb', 'day'], var_name=['drivers'])\n", "    drivers_tpnb.value = pd.to_numeric(drivers_tpnb.value, errors='coerce').replace(np.nan, 0)\n", "\n", "    flag_driver = pl.from_pandas(pd.DataFrame({'Driver' : drivers_tpnb.drivers.unique(), 'flag' : 1})).lazy()\n", "    d_values = [1,2,3,4] # Here we VLOOKUP driver values between df_times and drivers_df to filter only what we got in drivers_tpnb\n", "    driver_initial_name = 'Driver'\n", "    value_initial_name = 'flag'\n", "    df_times = pl.from_pandas(df_times).lazy()\n", "    for x in d_values:\n", "        driver_new_name = 'Driver_' + str(x)\n", "        value_new_name = 'flag_' + str(x)\n", "        flag_driver = flag_driver.rename({driver_initial_name: driver_new_name})\n", "        flag_driver = flag_driver.rename({value_initial_name: value_new_name})\n", "        df_times = df_times.join(flag_driver, on=[driver_new_name], how='left').with_columns([pl.col(value_new_name).fill_null(0)])\n", "        driver_initial_name = driver_new_name\n", "        value_initial_name = value_new_name\n", "\n", "\n", "    df_times = df_times.with_columns([\n", "        (pl.col('flag_1') + pl.col('flag_2') + pl.col('flag_3') + pl.col('flag_4')).alias(\"flag_total\")\n", "            ]).filter(pl.col(\"flag_total\") > 0).filter(pl.col(\"flag_1\") > 0).drop(['flag_1', 'flag_2', 'flag_3', 'flag_4', 'flag_total'])\n", "\n", "\n", "\n", "    # #### TPN to df_times\n", "\n", "    store_tpnb_to_merge = pl.from_pandas(drivers_tpnb.iloc[:,:5].drop_duplicates()).lazy()\n", "    store_tpnb_to_merge = store_tpnb_to_merge.with_columns([pl.col(\"Country\").cast(pl.Utf8),\n", "                                                           pl.col(\"Pmg\").cast(pl.Utf8)])\n", "    df_times = df_times.join(store_tpnb_to_merge, on = ['Country','Store', 'Dep'], how = 'left')\n", "    df_times = df_times.filter(pl.any(pl.col(\"Tpnb\").is_not_null()))\n", "\n", "\n", "    #### A<PERSON><PERSON>\n", "\n", "\n", "\n", "    # columns_to_move = ['Country','Pmg', 'Tpnb']\n", "\n", "    # for c, p in zip(columns_to_move,[0,5,6]):\n", "\n", "    #     move_column_inplace(df_times, c, p)\n", "\n", "    drivers_tpnb = pl.from_pandas(drivers_tpnb).lazy()\n", "    drivers_tpnb = drivers_tpnb.with_columns([pl.col(\"Country\").cast(pl.Utf8),\n", "                                            pl.col(\"Pmg\").cast(pl.Utf8),\n", "                                            pl.col(\"day\").cast(pl.Utf8)])\n", "\n", "    d_values = [1,2,3,4] # Here we VLOOKUP driver values between df_times and drivers_df. We have 4 drivers\n", "    driver_initial_name = 'drivers'\n", "    value_initial_name = 'value'\n", "    for x in d_values:\n", "        driver_new_name = 'Driver_' + str(x)\n", "        value_new_name = 'Driver_' + str(x) + '_value'\n", "        drivers_tpnb = drivers_tpnb.rename({driver_initial_name: driver_new_name})\n", "        drivers_tpnb = drivers_tpnb.rename({value_initial_name: value_new_name})\n", "        df_times = df_times.join(drivers_tpnb, on=['Country','Store', 'Tpnb', 'Dep', 'Pmg', 'day', driver_new_name], how='left').with_columns([pl.col(value_new_name).fill_null(0)])\n", "        driver_initial_name = driver_new_name\n", "        value_initial_name = value_new_name\n", "    driver_new_name = 'Profile' # Profiles\n", "    value_new_name = 'Profile_value'\n", "    drivers_tpnb = drivers_tpnb.rename({driver_initial_name: driver_new_name})\n", "    drivers_tpnb = drivers_tpnb.rename({value_initial_name: value_new_name})\n", "\n", "    df_times = df_times.join(drivers_tpnb, on=['Country','Store', 'Tpnb', 'Dep', 'Pmg', 'day', driver_new_name], how='left').with_columns([pl.col(value_new_name).fill_null(0)]).filter(pl.col(\"basic_time\") > 0).filter(pl.col(\"freq\") > 0)\n", "    drivers_tpnb = drivers_tpnb.rename({driver_new_name: 'drivers'})\n", "    drivers_tpnb = drivers_tpnb.rename({value_new_name: 'value'})\n", "\n", "\n", "    return df_times, Final_drivers_for_TPN_level\n", "\n", "\n", "\n", "def Model_Hours_Calculation_TPN(directory, excel_inputs_f, store_inputs, df_times, REX_ALLOWANCE):\n", "    def CalcModelHours(calc_hours):\n", "\n", "        calc_hours = calc_hours.with_columns([\n", "            pl.when((pl.col(\"Driver_3_value\") == 0) & (pl.col(\"Driver_3\") == 'no_driver')).then(1).otherwise(pl.col(\"Driver_3_value\")).alias('Driver_3_value'),\n", "            pl.when((pl.col(\"Driver_4_value\") == 0) & (pl.col(\"Driver_4\") == 'no_driver')).then(1).otherwise(pl.col(\"Driver_4_value\")).alias('Driver_4_value')\n", "        ])\n", "\n", "        calc_hours = calc_hours.with_columns([\n", "            (((pl.col(\"Driver_1_value\")+(pl.col(\"Driver_2_value\")*pl.col(\"Freq_Driver_2\")/100))*pl.col(\"Driver_3_value\")*pl.col(\"Driver_4_value\"))*pl.col(\"basic_time\")/60*pl.col(\"freq\")/100).alias(\"hours\")\n", "        ])\n", "\n", "        calc_hours = calc_hours.with_columns([\n", "            pl.when((pl.col(\"Profile_value\") == 0) & (pl.col(\"Profile\")!= 'no_driver')).then(0).otherwise(pl.col(\"hours\")).alias(\"hours\")\n", "        ])\n", "\n", "\n", "\n", "        return calc_hours\n", "\n", "\n", "    df_times = df_times.with_columns([\n", "        pl.when(pl.col(\"RA\") == \"Y\").then(pl.col(\"basic_time\")*(REX_ALLOWANCE/100)).otherwise(0).alias('RA_time')\n", "    ])\n", "    df_times = df_times.with_columns([pl.col(\"basic_time\")+pl.col(\"RA_time\").alias(\"basic_time\")]).drop(\"RA_time\")\n", "\n", "\n", "    divide_by_7_drivers = pl.read_excel(directory / excel_inputs_f, sheet_name=\"drivers_to_divide_7\").lazy()\n", "    col_name_1 = [\"Driver_1\", \"Driver_2\"]\n", "    col_name_2 = [\"Driver_1_value\", \"Driver_2_value\"]\n", "\n", "    for x, y in zip(col_name_1,col_name_2):\n", "        df_times = df_times.join(divide_by_7_drivers, on= x,  how = \"left\" ).fill_null(0)\n", "        df_times = df_times.with_columns([\n", "            pl.when(pl.col(\"flag\") == 1).then(pl.col(y)/7).otherwise(pl.col(y)).alias(y)\n", "        ]).drop(\"flag\")\n", "        divide_by_7_drivers = divide_by_7_drivers.rename({x : \"Driver_2\"})\n", "\n", "    df_times = CalcModelHours(df_times)\n", "\n", "\n", "    df = df_times.filter(pl.col(\"Activity_Group\") == \"Stock Movement\").select(['Store','Dep','day','Activity_Group','hours'])\n", "    df = df.with_columns([\n", "        pl.when((pl.col(\"Activity_Group\") == \"Stock Movement\") & (pl.col(\"Dep\")!=\"PRO\")).then(pl.col(\"hours\")*0.2).otherwise(0).alias(\"add_hours\")\n", "    ]).select(['Store','Dep','day','add_hours'])\n", "\n", "    df = df.groupby(['Store','Dep','day']).agg([pl.col(\"add_hours\").sum()])\n", "    df_times = df_times.join(df, on=['Store', 'Dep','day'], how='left')\n", "\n", "    df_times = df_times.with_columns([\n", "        pl.when(pl.col(\"Driver_1\") == 'Movement without an equipment').then(pl.col(\"add_hours\")).otherwise(pl.col(\"Driver_1_value\")).alias(\"Driver_1_value\"),\n", "        pl.when(pl.col(\"Driver_2\") == 'Movement without an equipment').then(pl.col(\"add_hours\")).otherwise(pl.col(\"Driver_2_value\")).alias(\"Driver_2_value\"),\n", "        pl.when(pl.col(\"Driver_3\") == 'Movement without an equipment').then(pl.col(\"add_hours\")).otherwise(pl.col(\"Driver_3_value\")).alias(\"Driver_3_value\"),\n", "        pl.when(pl.col(\"Driver_4\") == 'Movement without an equipment').then(pl.col(\"add_hours\")).otherwise(pl.col(\"Driver_4_value\")).alias(\"Driver_4_value\"),\n", "    ]).drop(\"add_hours\")\n", "\n", "    df_times = CalcModelHours(df_times)\n", "\n", "    # Headcount calculation\n", "\n", "    headcount_hrs = df_times.filter(pl.col(\"Head\") == 1).select(['Store', 'Dep','day','Head','hours'])\n", "    headcount_hrs = headcount_hrs.groupby(['Store', 'Dep', 'day']).agg([pl.col('hours').sum()])\n", "    headcount_hrs = headcount_hrs.with_columns([\n", "        pl.when(((pl.col(\"hours\")/8)-(pl.col(\"hours\")/8).round(0))>0.05).then(((pl.col(\"hours\")/8).ceil())/7).otherwise((pl.col(\"hours\")/8)/7).alias(\"Headcount\")\n", "    ]).drop(\"hours\")\n", "    df_times = df_times.join(headcount_hrs, on=['Store', 'Dep', 'day'], how='left')\n", "\n", "    df_times = df_times.with_columns([\n", "        pl.when(pl.col(\"Driver_1\") == 'Headcount').then(pl.col(\"Headcount\")).otherwise(pl.col(\"Driver_1_value\")).alias(\"Driver_1_value\"),\n", "        pl.when(pl.col(\"Driver_2\") == 'Headcount').then(pl.col(\"Headcount\")).otherwise(pl.col(\"Driver_2_value\")).alias(\"Driver_2_value\"),\n", "        pl.when(pl.col(\"Driver_3\") == 'Headcount').then(pl.col(\"Headcount\")).otherwise(pl.col(\"Driver_3_value\")).alias(\"Driver_3_value\"),\n", "        pl.when(pl.col(\"Driver_4\") == 'Headcount').then(pl.col(\"Headcount\")).otherwise(pl.col(\"Driver_4_value\")).alias(\"Driver_4_value\"),\n", "    ]).drop(\"Headcount\")\n", "\n", "    df_times = CalcModelHours(df_times)\n", "    store_inputsCol = ['Store','Dep','Division','Techincal Driver','Trading Days','Fridge Doors','Eggs displayed at UHT Milks',\n", "                                 'Advertising Headers','Racking','Day Fill','Cardboard Baller','Capping Shelves',\n", "                                 'Lift Allowance','Distance: WH to SF','Distance: WH to Yard','Steps: SF-Printer','CS_DIST_CSD_2_WH','Steps Dotcom-WH','Cut Melones','Fridge Door Modules',\n", "                                 'Number of Warehouse Fridges','Number of Modules','promo moduls','HealthyBioFreeFrom modul','Number of Flour Modules','Number of Scales','Number of Pallettes (plano)','Nr. Of broken palletts',\n", "                                 'Promo Displays','Pallets Delivery Ratio','Nr of car battery', 'Nr of faulty product (electronic)',\n", "                                 'Backstock Pallet Ratio','Customers', 'Fluctuation %','Steps (gates - work)',\n", "                                 'Time for customers','ProductReturns_factor','Online price changes','Banana Hammock','Fresh CCLB TPN',\n", "                                 'Night Fill','Red Labels','GBP_rates','Multifloor allowance','Pre-sort by other depts',\n", "                                 'Stock Movement for Bakery and Counter','Stores without counters',\n", "                                 'Check Fridge Temperature','MULTIFLOOR','EPW items','EPW Lines','MelonCitrus', 'Expired Newpapers (TPN)','Remitenda',\n", "                                 'HU Flags Ratio','HU Flags','Scan and Shop Labels', 'GM_FREE_SPACE_MODS',\t'GM_FREE_SPACE_AVG_TPN', 'Weekly non-plano pallett displays', '1K_stores_for_ShelfTrolley_extra']\n", "\n", "\n", "    dep_profiles = pl.from_pandas(store_inputs).lazy().select(store_inputsCol).unique(subset=store_inputsCol)\n", "    division_df = dep_profiles.select(['Store','Dep','Division','GBP_rates']).unique(subset=['Store','Dep','Division','GBP_rates'])\n", "    df_times = df_times.join(division_df, on=['Store','Dep'], how='left').fill_null(strategy=\"forward\")\n", "    df_times = df_times.with_columns([\n", "        (pl.col('GBP_rates') * pl.col(\"hours\") * 52).alias(\"Yearly GBP\")\n", "    ])\n", "\n", "\n", "\n", "    # cats = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']\n", "    # hours_df['day'] = pd.Categorical(hours_df['day'], categories = cats, ordered = True )\n", "    # hours_df.sort_values(by=['Store', 'Division', 'Activity_Group', 'Suboperation', 'day' ], inplace = True)\n", "\n", "    return df_times\n", "\n", "df_times ,Final_drivers_for_TPN_level = TimeValues_Calculation_TPN(directory, store_inputs, driver_pro, driver_repl, most_f)\n", "\n", "# hours_df = Model_Hours_Calculation_TPN(directory, excel_inputs_f, store_inputs, df_times, REX_ALLOWANCE)\n", "\n", "# hours_df = hours_df.collect()\n"]}, {"cell_type": "code", "execution_count": 38, "id": "a663e72c-01e6-4c05-bfee-12991370f017", "metadata": {}, "outputs": [], "source": ["a = df_times.collect()"]}, {"cell_type": "code", "execution_count": 39, "id": "70dbe1b5-3c91-45f9-836d-2672e0b8ae41", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "\n", "index = 0\n", "stores_to_iterate = list()\n", "for x in a.select(['Store']).unique():\n", "\n", "    stores_to_iterate.append(x)\n", "    index += 1\n", "\n", "    if index % 50 == 0 or (a.select(['Store']).n_unique() == index):\n", "        print(stores_to_iterate)"]}, {"cell_type": "code", "execution_count": 41, "id": "d13c735a-af0d-4ed4-a950-66c64be1ed5b", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["index"]}, {"cell_type": "code", "execution_count": 18, "id": "54b89a93-094a-42d8-a339-4f5060cf623d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21001\n"]}], "source": ["for x in a.select(['Store']).unique():\n", "    print(x[0])"]}, {"cell_type": "code", "execution_count": 28, "id": "b84230e2-cdd1-472e-b4b2-6ae7080efb25", "metadata": {}, "outputs": [], "source": ["b = a.select(['Store']).unique()"]}, {"cell_type": "code", "execution_count": 32, "id": "a19a628f-d39c-471d-81b2-de143930d4b3", "metadata": {}, "outputs": [{"data": {"text/plain": ["155"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["a.select(['Store']).n_unique()"]}, {"cell_type": "code", "execution_count": 25, "id": "07d1e154-c040-4b80-b78f-25f6553aff5a", "metadata": {}, "outputs": [{"ename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "evalue": "Series of dtype: Int64 != List", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mSchemaError\u001b[0m                               <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_22196\\1897565555.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0ma\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mwith_column\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mpl\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcol\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"Store\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0marr\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mlengths\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\polars\\internals\\dataframe\\frame.py\u001b[0m in \u001b[0;36mwith_column\u001b[1;34m(self, column)\u001b[0m\n\u001b[0;32m   4298\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   4299\u001b[0m         \"\"\"\n\u001b[1;32m-> 4300\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mlazy\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mwith_column\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mcolumn\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcollect\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mno_optimization\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mTrue\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   4301\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   4302\u001b[0m     def hstack(\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\polars\\utils.py\u001b[0m in \u001b[0;36mwrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    327\u001b[0m         \u001b[1;32mdef\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mP\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mP\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mT\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    328\u001b[0m             \u001b[0m_rename_kwargs\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mfn\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0maliases\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 329\u001b[1;33m             \u001b[1;32mreturn\u001b[0m \u001b[0mfn\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    330\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    331\u001b[0m         \u001b[1;32mreturn\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\polars\\internals\\lazyframe\\frame.py\u001b[0m in \u001b[0;36mcollect\u001b[1;34m(self, type_coercion, predicate_pushdown, projection_pushdown, simplify_expression, no_optimization, slice_pushdown, common_subplan_elimination, streaming)\u001b[0m\n\u001b[0;32m   1166\u001b[0m             \u001b[0mstreaming\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1167\u001b[0m         )\n\u001b[1;32m-> 1168\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mpli\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mwrap_df\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mldf\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcollect\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1169\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1170\u001b[0m     def sink_parquet(\n", "\u001b[1;31mSchemaError\u001b[0m: Series of dtype: Int64 != List"]}], "source": ["a.with_column(pl.col(\"Store\").arr.lengths())"]}, {"cell_type": "code", "execution_count": 23, "id": "cc285736-e566-4eac-801e-b6b22f8bffce", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'DataFrame' object has no attribute 'arr'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_22196\\2972185029.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mb\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0marr\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mlengths\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m: 'DataFrame' object has no attribute 'arr'"]}], "source": ["b.arr.lengths()"]}, {"cell_type": "code", "execution_count": 92, "id": "5b61e88c-1e64-4316-a210-04bd30b2a1ac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Activity_key - 0\n", "Country - 0\n", "Format - 0\n", "Dep - 0\n", "Store - 0\n", "day - 0\n", "basic_time - 0\n", "freq - 0\n", "Suboperation - 0\n", "Activity_Group - 0\n", "V_F - 0\n", "Driver_1 - 0\n", "Driver_2 - 0\n", "Freq_Driver_2 - 0\n", "Driver_3 - 0\n", "Driver_4 - 0\n", "Profile - 0\n", "RA - 0\n", "Head - 0\n", "Newspaper_Activity - 0\n", "Pmg - 0\n", "Tpnb - 0\n", "Driver_1_value - 0\n", "Driver_2_value - 0\n", "Driver_3_value - 0\n", "Driver_4_value - 0\n", "Profile_value - 0\n", "hours - 0\n", "Division - 0\n", "GBP_rates - 0\n", "Yearly GBP - 0\n"]}], "source": ["for col in hours_df.get_columns():\n", "    print(f'{col.name} - {col.is_null().sum()}')\n"]}, {"cell_type": "code", "execution_count": 101, "id": "6266a231-dec7-4b90-abf7-d2f56f4fdbec", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dep\n", "HDL     49.021952\n", "HEA    130.051920\n", "Name: hours, dtype: float64"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["hours_df.to_pandas().groupby(\"Dep\")['hours'].sum()"]}, {"cell_type": "code", "execution_count": 108, "id": "a4175b26-58b9-44e9-a9d4-e6351178a2df", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["\n", "\n", "def TimeValues_Calculation_TPN_pandas(directory, store_inputs, driver_pro, driver_repl, most_f ):\n", "    #TIMEVALUE TO DRIVERS\n", "\n", "\n", "    #TIMEVALUE TO DRIVERS\n", "    \n", "    \n", "    drivers_tpnb = pd.concat([driver_repl, driver_pro]).replace(np.nan,0)\n", "    drivers_tpnb['Case on Pallet'] = drivers_tpnb['cases_delivered']/(drivers_tpnb['New Delivery - Pallets']+drivers_tpnb['New Delivery - Rollcages']*0.62)\n", "    drivers_tpnb['Case on Pallet'] = drivers_tpnb['Case on Pallet'].replace(np.nan,7) # if zero then 7 cases on pallet\n", "    drivers_tpnb['Modules to go'] = 1\n", "    \n", "    drivers_tpnb.rename(columns={'cases_delivered' : 'Cases Delivered'}, inplace = True)\n", "    \n", "    dep_profiles = store_inputs[['Store','Dep','Fridge Doors','Eggs displayed at UHT Milks',\n", "                                 'Racking','Cardboard Baller','Capping Shelves',\n", "                                 'Lift Allowance','Distance: WH to SF','Distance: WH to Yard','Steps: SF-Printer','CS_DIST_CSD_2_WH','Steps Dotcom-WH',\n", "                                 'Steps (gates - work)','<PERSON><PERSON>','GBP_rates'\n", "                                 ]].drop_duplicates()\n", "    \n", "    dep_profiles.rename(columns={'Store' : 'store', 'Dep' : 'dep'}, inplace = True)\n", "    drivers_tpnb = drivers_tpnb.merge(dep_profiles, on = ['store', 'dep'], how = 'left')\n", "    \n", "    Final_drivers_for_TPN_level = drivers_tpnb.copy()\n", "    \n", "    \n", "\n", "\n", "    \n", "    stores_df = store_inputs[['Country','Store','Format','Store Name','Plan Size']].drop_duplicates()\n", "    stores_df = stores_df[stores_df.Store.isin(list(set(drivers_tpnb.store.values)))]\n", "    storelist_array = stores_df[['Country', 'Store', 'Format']].drop_duplicates().values\n", "    shelftrolley_extra_stores = store_inputs[['Country','Store', '1K_stores_for_ShelfTrolley_extra']].drop_duplicates()\n", "\n", "    \n", "    most_file = pd.ExcelFile( directory / most_f, engine='pyxlsb')\n", "    activity_list = pd.read_excel(most_file,'Time Values',skiprows=3)\n", "    \n", "    new_header = activity_list.iloc[0] #grab the first row for the header\n", "    activity_list = activity_list[1:] #take the data less the header row\n", "    activity_list.columns = new_header #set the header row as the df header\n", "    \n", "    cols = ['Activity_key_activities','Suboperation Description','Activity group','V F','DRIVER_1','DRIVER_2','FREQ2','DRIVER_3','DRIVER_4','PROFILE','RA','Head','Newspaper_Activity']\n", "    cols2 = ['Activity_key_activities','Suboperation','Activity_Group','V_F','Driver_1','Driver_2','Freq_Driver_2','Driver_3','Driver_4','Profile','RA','Head','Newspaper_Activity']\n", "    activity_list = activity_list[cols]\n", "    for x, y in zip(cols, cols2):\n", "        activity_list.rename(columns={x:y},inplace=True)\n", "    \n", "    activity_list.dropna(subset=['Activity_key_activities'],inplace=True)\n", "    activity_list.rename(columns={'Activity_key_activities':'Activity_key'},inplace=True)\n", "    activity_list['Freq_Driver_2'] = activity_list['Freq_Driver_2'].replace(np.nan,0)\n", "    activity_list = activity_list.replace(np.nan,'no_driver')\n", "    \n", "    activities = activity_list[['Activity_key']].copy()\n", "    activities['Country'] = ''\n", "    activities['Format'] = ''\n", "    activities['Dep'] = ''\n", "    activities['Store'] = 0\n", "    activities['day'] = ''\n", "    \n", "    times = pd.read_excel(most_file,'TimeValues_Py',usecols='M:R')\n", "    times.dropna(subset=['Activity_key_times'],inplace=True)\n", "    times.rename(columns={'Activity_key_times':'Activity_key'},inplace=True)\n", "    # times.drop(times[times.basic_time == 0].index, inplace = True)\n", "    # times.drop(times[times.freq == 0].index, inplace = True)\n", "    \n", "    freq_Shelftrolley_extra = times[(times['Activity_key'].str.contains(\"shelf trolley\")) & (times['Format'] == 'Express')][['Activity_key', 'Country', 'Dep', 'freq']].drop_duplicates()\n", "\n", "    \n", "    times_array = activities.values\n", "    departments = pd.DataFrame([x for x in drivers_tpnb.dep.unique()], columns=['Dep'])\n", "    dep_array = departments.values\n", "    \n", "    week_df = pd.DataFrame([a for a in drivers_tpnb['day'].unique()], columns=['day'])\n", "    weekdays_array = week_df.values\n", "    \n", "    \n", "    df_times = pd.DataFrame(columns=activities.columns)\n", "    result = len(storelist_array) * len(weekdays_array) * len(dep_array) * len(times_array)\n", "    df_array = np.empty([result,6], dtype='object') # create an empty array\n", "    counter = 0\n", "    for a in range(len(times_array)):\n", "        for d in range(len(dep_array)):\n", "            for s in range(len(storelist_array)):\n", "                for w in range(len(weekdays_array)):\n", "                    df_array[counter][0] = times_array[a][0] # activity name\n", "                    df_array[counter][3] = dep_array[d][0] # department\n", "                    df_array[counter][1] = storelist_array[s][0] # country\n", "                    df_array[counter][2] = storelist_array[s][2] # format\n", "                    df_array[counter][4] = storelist_array[s][1] # store\n", "                    df_array[counter][5] = weekdays_array[w][0] # day\n", "                    counter += 1\n", "    df_times = pd.concat([df_times,pd.DataFrame(df_array, columns=df_times.columns)])\n", "    df_times = df_times.merge(times, on=['Activity_key','Country','Format', 'Dep'], how='left')\n", "    df_times = df_times.merge(activity_list, on=['Activity_key'], how='left')\n", "    df_times.Store = pd.to_numeric(df_times.Store, errors='coerce')\n", "    \n", "    ######### ShelfTrolley 1K stores extra hours settings #########\n", "    \n", "    df_times = df_times.merge(shelftrolley_extra_stores, on=['Country', 'Store'], how='left')\n", "    \n", "    only_1k_stores_for_shelfTrolley = df_times[df_times[\"1K_stores_for_ShelfTrolley_extra\"] == 1]\n", "    \n", "    freq_Shelftrolley_extra = freq_Shelftrolley_extra[freq_Shelftrolley_extra.Country.isin(df_times.Country.unique().tolist())]\n", "    df_times = df_times[df_times[\"1K_stores_for_ShelfTrolley_extra\"] == 0]\n", "    dict_list = freq_Shelftrolley_extra.groupby([\"Activity_key\", 'Country', 'Dep'])[\"freq\"].apply(lambda s: s.tolist()).to_dict()\n", "    \n", "    for key, value in dict_list.items():\n", "        only_1k_stores_for_shelfTrolley.loc[(only_1k_stores_for_shelfTrolley['Activity_key'] == key[0])\n", "                      & (only_1k_stores_for_shelfTrolley['Country'] == key[1])\n", "                      & (only_1k_stores_for_shelfTrolley['Dep'] == key[2]), 'freq'] = value[0]\n", "\n", "    \n", "    df_times = pd.concat([df_times,only_1k_stores_for_shelfTrolley ])\n", "    \n", "    df_times.drop(\"1K_stores_for_ShelfTrolley_extra\", axis=1, inplace=True)\n", "    \n", "\n", "    \n", "    #### MERGING PART\n", "    \n", "    drivers_tpnb.rename(columns={'store' : 'Store', 'dep' : 'Dep', 'pmg' : 'Pmg', 'tpnb' : 'Tpnb', 'country' : 'Country' }, inplace = True)\n", "    \n", "\n", "    \n", "    # drivers_tpnb = drivers_tpnb.groupby(['Store','day','Dep', 'Pmg', 'Tpnb'], as_index = False,observed=True ).sum()\n", "    drivers_tpnb = drivers_tpnb.melt(id_vars=['Country','Store', 'Dep', 'Pmg', 'Tpnb', 'day'], var_name=['drivers'])\n", "    drivers_tpnb.value = pd.to_numeric(drivers_tpnb.value, errors='coerce').replace(np.nan, 0)\n", "    \n", "    flag_driver = pd.DataFrame({'Driver' : drivers_tpnb.drivers.unique(), 'flag' : 1})\n", "    d_values = [1,2,3,4] # Here we VLOOKUP driver values between df_times and drivers_df to filter only what we got in drivers_tpnb\n", "    driver_initial_name = 'Driver'\n", "    value_initial_name = 'flag'\n", "    for x in d_values:\n", "        driver_new_name = 'Driver_' + str(x)\n", "        value_new_name = 'flag_' + str(x)\n", "        flag_driver.rename(columns={driver_initial_name: driver_new_name}, inplace=True)\n", "        flag_driver.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "        df_times = df_times.merge(flag_driver, on=[driver_new_name], how='left')\n", "        df_times[value_new_name] = df_times[value_new_name].replace(np.nan,0) # it seems we need NaN there\n", "        driver_initial_name = driver_new_name\n", "        value_initial_name = value_new_name\n", "        \n", "    df_times['flag_total'] = df_times.flag_1 + df_times.flag_2 + df_times.flag_3 + df_times.flag_4\n", "    df_times = df_times[df_times.flag_total > 0]\n", "    df_times = df_times[df_times.flag_1 > 0]\n", "    df_times.drop(['flag_1', 'flag_2', 'flag_3', 'flag_4', 'flag_total'], axis = 1, inplace = True)\n", "    \n", "\n", "    \n", "    #### TPN to df_times\n", "    store_tpnb_to_merge = drivers_tpnb.iloc[:,:5].drop_duplicates()\n", "    df_times = df_times.merge(store_tpnb_to_merge, on = ['Country','Store', 'Dep'], how = 'left')\n", "    df_times = df_times[df_times.Tpnb.notnull()]\n", "    \n", "    \n", "    #### A<PERSON><PERSON>\n", "    \n", "\n", "        \n", "    columns_to_move = ['Country','Pmg', 'Tpnb']\n", "    \n", "    for c, p in zip(columns_to_move,[0,5,6]):\n", "        \n", "        move_column_inplace(df_times, c, p)\n", "        \n", "    \n", "    d_values = [1,2,3,4] # Here we VLOOKUP driver values between df_times and drivers_df. We have 4 drivers\n", "    driver_initial_name = 'drivers'\n", "    value_initial_name = 'value'\n", "    for x in d_values:\n", "        driver_new_name = 'Driver_' + str(x)\n", "        value_new_name = 'Driver_' + str(x) + '_value'\n", "        drivers_tpnb.rename(columns={driver_initial_name: driver_new_name}, inplace=True)\n", "        drivers_tpnb.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "        df_times = df_times.merge(drivers_tpnb, on=['Country','Store', 'Tpnb', 'Dep', 'Pmg', 'day', driver_new_name], how='left')\n", "        df_times[value_new_name] = df_times[value_new_name].replace(np.nan,0) # it seems we need NaN there\n", "        driver_initial_name = driver_new_name\n", "        value_initial_name = value_new_name\n", "    driver_new_name = 'Profile' # Profiles\n", "    value_new_name = 'Profile_value'\n", "    drivers_tpnb.rename(columns={driver_initial_name: driver_new_name}, inplace=True)\n", "    drivers_tpnb.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "    \n", "    df_times = df_times.merge(drivers_tpnb, on=['Country','Store', 'Tpnb', 'Dep', 'Pmg', 'day', driver_new_name], how='left')\n", "    df_times[value_new_name] = df_times[value_new_name].replace(np.nan,0) # it seems we need NaN there\n", "    drivers_tpnb.rename(columns={driver_new_name: 'drivers'}, inplace=True)\n", "    drivers_tpnb.rename(columns={value_new_name: 'value'}, inplace=True)\n", "    \n", "    df_times = df_times.loc[(df_times.basic_time > 0)]\n", "    df_times = df_times.loc[(df_times.freq > 0)]\n", "    \n", "    \n", "    return df_times, Final_drivers_for_TPN_level\n", "\n", "\n", "def Model_Hours_Calculation_TPN_pandas(directory, excel_inputs_f, store_inputs, df_times, REX_ALLOWANCE):\n", "    \n", "    def CalcModelHours(calc_hours):\n", "      \n", "\n", "\n", "        calc_hours.Driver_3_value = np.where((calc_hours.Driver_3_value==0)&(calc_hours.Driver_3=='no_driver'),1,calc_hours.Driver_3_value) # here we have multiplicators and as we cannot divide by 0, I changed the zeros to 1\n", "        calc_hours.Driver_4_value = np.where((calc_hours.Driver_4_value==0)&(calc_hours.Driver_4=='no_driver'),1,calc_hours.Driver_4_value)\n", "        calc_hours['hours'] = (((calc_hours.Driver_1_value+(calc_hours.Driver_2_value*calc_hours.Freq_Driver_2/100))*calc_hours.Driver_3_value*calc_hours.Driver_4_value)*calc_hours.basic_time/60*calc_hours.freq/100)\n", "        calc_hours['hours'] = np.where((calc_hours.Profile_value==0)&(calc_hours.Profile!='no_driver'), 0, calc_hours['hours'])\n", "  \n", "      \n", "        return calc_hours\n", "    \n", "    hours_df = df_times.copy()\n", "    hours_df['RA_time'] = np.where(hours_df.RA=='Y',hours_df.basic_time*(REX_ALLOWANCE/100),0)\n", "    hours_df['basic_time'] = hours_df.basic_time+hours_df.RA_time\n", "    hours_df.drop(columns={'RA_time'}, axis=1, inplace=True)\n", "    \n", "    divide_by_7_drivers = pd.read_excel(directory / excel_inputs_f, \"drivers_to_divide_7\")\n", "    col_name_1 = [\"Driver_1\", \"Driver_2\"]\n", "    col_name_2 = [\"Driver_1_value\", \"Driver_2_value\"]\n", "    for x, y in zip(col_name_1,col_name_2):\n", "        hours_df = hours_df.merge(divide_by_7_drivers, on= x,  how = \"left\" ).replace(np.nan,0)\n", "        hours_df[y] = np.where(hours_df.flag == 1, hours_df[y] / 7,hours_df[y])\n", "        hours_df.drop(['flag'], axis = 1, inplace = True)\n", "        divide_by_7_drivers.rename(columns={x : \"Driver_2\"},inplace = True) \n", "    \n", "    \n", "    hours_df = CalcModelHours(hours_df)\n", "    df = hours_df.loc[hours_df.Activity_Group=='Stock Movement',['Store','Dep','day','Activity_Group','hours']].copy()\n", "    df['add_hours'] = np.where(((df.Activity_Group=='Stock Movement')&(df.Dep!=\"PRO\")), df.hours * 0.2, 0) # for Movement without an equipment\n", "    df = df[['Store','Dep','day','add_hours']]\n", "    df = df.groupby(['Store','Dep','day'])['add_hours'].sum().reset_index()\n", "    hours_df = hours_df.merge(df, on=['Store', 'Dep','day'], how='left')\n", "    hours_df['Driver_1_value'] = np.where(hours_df.Driver_1=='Movement without an equipment', hours_df.add_hours, hours_df['Driver_1_value'])\n", "    hours_df['Driver_2_value'] = np.where(hours_df.Driver_2=='Movement without an equipment', hours_df.add_hours, hours_df['Driver_2_value'])\n", "    hours_df['Driver_3_value'] = np.where(hours_df.Driver_3=='Movement without an equipment', hours_df.add_hours, hours_df['Driver_3_value'])\n", "    hours_df['Driver_4_value'] = np.where(hours_df.Driver_4=='Movement without an equipment', hours_df.add_hours, hours_df['Driver_4_value'])\n", "    hours_df.drop(columns={'add_hours'}, axis=1, inplace=True)\n", "    hours_df = CalcModelHours(hours_df)\n", "    \n", "    \n", "    # Headcount calculation\n", "    headcount_hrs = hours_df.loc[hours_df.Head==1, ('Store', 'Dep','day','Head','hours')]\n", "    headcount_hrs = headcount_hrs.groupby(['Store', 'Dep', 'day'])['hours'].sum().reset_index()\n", "    headcount_hrs['Headcount'] = np.where((((headcount_hrs.hours/8)-round(headcount_hrs.hours/8))>0.05),np.ceil(headcount_hrs.hours/8) / 7,round(headcount_hrs.hours/8) / 7)\n", "    headcount_hrs.drop(columns={'hours'}, axis=1, inplace=True)\n", "    hours_df = hours_df.merge(headcount_hrs, on=['Store', 'Dep', 'day'], how='left')\n", "    hours_df['Driver_1_value'] = np.where(hours_df.Driver_1=='Headcount', hours_df.Headcount, hours_df['Driver_1_value'])\n", "    hours_df['Driver_2_value'] = np.where(hours_df.Driver_2=='Headcount', hours_df.Headcount, hours_df['Driver_2_value'])\n", "    hours_df['Driver_3_value'] = np.where(hours_df.Driver_3=='Headcount', hours_df.Headcount, hours_df['Driver_3_value'])\n", "    hours_df['Driver_4_value'] = np.where(hours_df.Driver_4=='Headcount', hours_df.Headcount, hours_df['Driver_4_value'])\n", "    hours_df.drop(columns={'Headcount'}, axis=1, inplace=True)\n", "    hours_df = CalcModelHours(hours_df)\n", "    dep_profiles = store_inputs[['Store','Dep','Division','Techincal Driver','Trading Days','Fridge Doors','Eggs displayed at UHT Milks',\n", "                                 'Advertising Headers','Racking','Day Fill','Cardboard Baller','Capping Shelves',\n", "                                 'Lift Allowance','Distance: WH to SF','Distance: WH to Yard','Steps: SF-Printer','CS_DIST_CSD_2_WH','Steps Dotcom-WH','Cut Melones','Fridge Door Modules',\n", "                                 'Number of Warehouse Fridges','Number of Modules','promo moduls','HealthyBioFreeFrom modul','Number of Flour Modules','Number of Scales','Number of Pallettes (plano)','Nr. Of broken palletts',\n", "                                 'Promo Displays','Pallets Delivery Ratio','Nr of car battery', 'Nr of faulty product (electronic)',\n", "                                 'Backstock Pallet Ratio','Customers', 'Fluctuation %','Steps (gates - work)',\n", "                                 'Time for customers','ProductReturns_factor','Online price changes','Banana Hammock','Fresh CCLB TPN',\n", "                                 'Night Fill','Red Labels','GBP_rates','Multifloor allowance','Pre-sort by other depts',\n", "                                 'Stock Movement for Bakery and Counter','Stores without counters',\n", "    \t\t\t\t\t\t\t 'Check Fridge Temperature','MULTIFLOOR','EPW items','EPW Lines','MelonCitrus', 'Expired Newpapers (TPN)','Remitenda',\n", "    \t\t\t\t\t\t\t 'HU Flags Ratio','HU Flags','Scan and Shop Labels', 'GM_FREE_SPACE_MODS',\t'GM_FREE_SPACE_AVG_TPN', 'Weekly non-plano pallett displays', '1K_stores_for_ShelfTrolley_extra']].drop_duplicates()  #, 'BWS_wo_wine_moduls', 'wine_moduls'\n", "    division_df=dep_profiles[['Store','Dep','Division','GBP_rates']].drop_duplicates()\n", "    hours_df = hours_df.merge(division_df, on=['Store','Dep'], how='left')\n", "    hours_df['GBP_rates'].fillna(method='ffill', inplace=True)\n", "    hours_df['Yearly GBP'] = hours_df.GBP_rates*hours_df.hours*52\n", "    cats = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']\n", "    hours_df['day'] = pd.Categorical(hours_df['day'], categories = cats, ordered = True )\n", "    hours_df.sort_values(by=['Store', 'Division', 'Activity_Group', 'Suboperation', 'day' ], inplace = True)\n", "    return hours_df\n", "\n"]}, {"cell_type": "code", "execution_count": 109, "id": "773d25bf-14af-4c3b-ac54-05123b1f114b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 4min 32s\n"]}], "source": ["%%time\n", "df_times, b = TimeValues_Calculation_TPN_pandas(directory, store_inputs, driver_pro, driver_repl, most_f )\n", "hours_df_2 = Model_Hours_Calculation_TPN_pandas(directory, excel_inputs_f, store_inputs, df_times, REX_ALLOWANCE)"]}, {"cell_type": "code", "execution_count": 110, "id": "677f38b5-bbfa-4c44-92d4-912e4b1aa7a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dep\n", "HDL     49.021952\n", "HEA    130.051920\n", "Name: hours, dtype: float64"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["hours_df_2.groupby(\"Dep\")['hours'].sum()"]}, {"cell_type": "code", "execution_count": 11, "id": "3259c8d9-fa9e-4f62-8b61-b0c1e417e606", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>store</th>\n", "      <th>day</th>\n", "      <th>dep</th>\n", "      <th>pmg</th>\n", "      <th>t_touch</th>\n", "      <th>cases_to_replenish</th>\n", "      <th>full_pallet</th>\n", "      <th>mu</th>\n", "      <th>light</th>\n", "      <th>heavy</th>\n", "      <th>backstock pallet ratio</th>\n", "      <th>Replenished Rollcages</th>\n", "      <th>Replenished <PERSON></th>\n", "      <th>tpnb</th>\n", "      <th>sold_units</th>\n", "      <th>sold_cases</th>\n", "      <th>sales</th>\n", "      <th>stock</th>\n", "      <th>Cases Delivered</th>\n", "      <th>cases_delivered_on_sf</th>\n", "      <th>Add_Walking Cages</th>\n", "      <th>pallet_capacity</th>\n", "      <th>Add_Walking Backstock Cages</th>\n", "      <th>Add_Walking Pallets</th>\n", "      <th>Backstock Cases</th>\n", "      <th>Backstock Pallets</th>\n", "      <th>Backstock Rollcages</th>\n", "      <th><PERSON><PERSON> <PERSON><PERSON></th>\n", "      <th>backroom_pallets</th>\n", "      <th><PERSON><PERSON>_Tag</th>\n", "      <th>Broken Items</th>\n", "      <th>broken_case_flag</th>\n", "      <th>Bulk Pallets</th>\n", "      <th>Capping <PERSON><PERSON></th>\n", "      <th>Clip Strip Cases</th>\n", "      <th>Clip Strip Items</th>\n", "      <th>Electro_Tag</th>\n", "      <th>Empty Pallets</th>\n", "      <th>Empty Rollcages</th>\n", "      <th><PERSON> Shelf <PERSON>rolley</th>\n", "      <th><PERSON>t</th>\n", "      <th>Full Pallet Cases</th>\n", "      <th><PERSON><PERSON>_<PERSON></th>\n", "      <th>H_Backstock Cases</th>\n", "      <th>H_CASE_H_NSRP_items</th>\n", "      <th>H_CASE_L_NSRP_items</th>\n", "      <th><PERSON>_<PERSON> Fill Cases</th>\n", "      <th>H_NSRP_for_opening_type</th>\n", "      <th>H_Pre-sorted Cases</th>\n", "      <th>H_SRP</th>\n", "      <th>H_NSRP</th>\n", "      <th>H_SRP_for_opening_type</th>\n", "      <th>Hard_Tag</th>\n", "      <th>Hook Fill Items</th>\n", "      <th>L_Backstock Cases</th>\n", "      <th>L_NSRP</th>\n", "      <th>L_NSRP_for_opening_type</th>\n", "      <th>L_NSRP_Items</th>\n", "      <th>L_Pre-sorted Cases</th>\n", "      <th>L_SRP</th>\n", "      <th>L_SRP_for_opening_type</th>\n", "      <th>MU Pallet</th>\n", "      <th>New Delivery - Pallets</th>\n", "      <th>New Delivery - Rollcages</th>\n", "      <th>New Delivery - <PERSON><PERSON></th>\n", "      <th>Ownbrand_perforated_box_cases</th>\n", "      <th>Ownbrand_shrink_cases</th>\n", "      <th>Ownbrand_tray_cases</th>\n", "      <th>Ownbrand_tray_with_hood_cases</th>\n", "      <th>Ownbrand_tray_with_shrink_cases</th>\n", "      <th>Pre-sorted Rollcages</th>\n", "      <th>Pre-sorted <PERSON><PERSON></th>\n", "      <th>Racking <PERSON></th>\n", "      <th>Safer_Tag</th>\n", "      <th>Salami_Tag</th>\n", "      <th>Soft_Tag</th>\n", "      <th>Total RC's and Pallets</th>\n", "      <th>High_pallet_cases_on_Dry30_and_DRY24</th>\n", "      <th>High_pallets_on_Dry30_and_DRY24</th>\n", "      <th>High_half_pallet_cases_on_Dry30_and_DRY24</th>\n", "      <th>High_half_pallets_on_Dry30_and_DRY24</th>\n", "      <th>Tag_total_nr</th>\n", "      <th>Case on Pallet</th>\n", "      <th><PERSON><PERSON><PERSON> to go</th>\n", "      <th><PERSON><PERSON> Doors</th>\n", "      <th>Eggs displayed at UHT Milks</th>\n", "      <th>Racking</th>\n", "      <th>Cardboard Baller</th>\n", "      <th>Capping <PERSON></th>\n", "      <th>Lift Allowance</th>\n", "      <th>Distance: WH to SF</th>\n", "      <th>Distance: WH to Yard</th>\n", "      <th>Steps: SF-Printer</th>\n", "      <th>CS_DIST_CSD_2_WH</th>\n", "      <th>Steps Dotcom-WH</th>\n", "      <th>Steps (gates - work)</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>GBP_rates</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SK</td>\n", "      <td>21001</td>\n", "      <td>Monday</td>\n", "      <td>HDL</td>\n", "      <td>HDL12</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>220232969</td>\n", "      <td>0.285714</td>\n", "      <td>0.035714</td>\n", "      <td>0.640714</td>\n", "      <td>7.571429</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>144.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>150.0</td>\n", "      <td>0.0</td>\n", "      <td>75.0</td>\n", "      <td>45.0</td>\n", "      <td>30.0</td>\n", "      <td>365.0</td>\n", "      <td>1.0</td>\n", "      <td>6.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SK</td>\n", "      <td>21001</td>\n", "      <td>Tuesday</td>\n", "      <td>HDL</td>\n", "      <td>HDL12</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>220232969</td>\n", "      <td>0.142857</td>\n", "      <td>0.017857</td>\n", "      <td>0.296429</td>\n", "      <td>8.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>144.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>150.0</td>\n", "      <td>0.0</td>\n", "      <td>75.0</td>\n", "      <td>45.0</td>\n", "      <td>30.0</td>\n", "      <td>365.0</td>\n", "      <td>1.0</td>\n", "      <td>6.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SK</td>\n", "      <td>21001</td>\n", "      <td>Wednesday</td>\n", "      <td>HDL</td>\n", "      <td>HDL12</td>\n", "      <td>0.0</td>\n", "      <td>0.071429</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.000006</td>\n", "      <td>0.000492</td>\n", "      <td>220232969</td>\n", "      <td>0.571429</td>\n", "      <td>0.071429</td>\n", "      <td>1.377143</td>\n", "      <td>8.000000</td>\n", "      <td>0.071429</td>\n", "      <td>0.071429</td>\n", "      <td>0.000006</td>\n", "      <td>144.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000492</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000492</td>\n", "      <td>0.000006</td>\n", "      <td>0.004456</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.071429</td>\n", "      <td>0.071429</td>\n", "      <td>0.571429</td>\n", "      <td>0.000586</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000496</td>\n", "      <td>0.0</td>\n", "      <td>0.004464</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000006</td>\n", "      <td>0.000037</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000498</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>144.0</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>150.0</td>\n", "      <td>0.0</td>\n", "      <td>75.0</td>\n", "      <td>45.0</td>\n", "      <td>30.0</td>\n", "      <td>365.0</td>\n", "      <td>1.0</td>\n", "      <td>6.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SK</td>\n", "      <td>21001</td>\n", "      <td>Thursday</td>\n", "      <td>HDL</td>\n", "      <td>HDL12</td>\n", "      <td>0.0</td>\n", "      <td>0.071429</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.000006</td>\n", "      <td>0.000492</td>\n", "      <td>220232969</td>\n", "      <td>0.285714</td>\n", "      <td>0.035714</td>\n", "      <td>0.688571</td>\n", "      <td>7.714286</td>\n", "      <td>0.071429</td>\n", "      <td>0.071429</td>\n", "      <td>0.000006</td>\n", "      <td>144.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000492</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000492</td>\n", "      <td>0.000006</td>\n", "      <td>0.004456</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.071429</td>\n", "      <td>0.071429</td>\n", "      <td>0.571429</td>\n", "      <td>0.000586</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000496</td>\n", "      <td>0.0</td>\n", "      <td>0.004464</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000006</td>\n", "      <td>0.000037</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000498</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>144.0</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>150.0</td>\n", "      <td>0.0</td>\n", "      <td>75.0</td>\n", "      <td>45.0</td>\n", "      <td>30.0</td>\n", "      <td>365.0</td>\n", "      <td>1.0</td>\n", "      <td>6.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SK</td>\n", "      <td>21001</td>\n", "      <td>Friday</td>\n", "      <td>HDL</td>\n", "      <td>HDL12</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>220232969</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>7.714286</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>144.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>150.0</td>\n", "      <td>0.0</td>\n", "      <td>75.0</td>\n", "      <td>45.0</td>\n", "      <td>30.0</td>\n", "      <td>365.0</td>\n", "      <td>1.0</td>\n", "      <td>6.63</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  country  store        day  dep    pmg  t_touch  cases_to_replenish  \\\n", "0      SK  21001     Monday  HDL  HDL12      0.0            0.000000   \n", "1      SK  21001    Tuesday  HDL  HDL12      0.0            0.000000   \n", "2      SK  21001  Wednesday  HDL  HDL12      0.0            0.071429   \n", "3      SK  21001   Thursday  HDL  HDL12      0.0            0.071429   \n", "4      SK  21001     Friday  HDL  HDL12      0.0            0.000000   \n", "\n", "   full_pallet   mu  light  heavy  backstock pallet ratio  \\\n", "0          0.0  0.0    1.0    0.0                     1.0   \n", "1          0.0  0.0    1.0    0.0                     1.0   \n", "2          0.0  0.0    1.0    0.0                     1.0   \n", "3          0.0  0.0    1.0    0.0                     1.0   \n", "4          0.0  0.0    1.0    0.0                     1.0   \n", "\n", "   Replenished Rollcages  Replenished Pallets       tpnb  sold_units  \\\n", "0               0.000000             0.000000  220232969    0.285714   \n", "1               0.000000             0.000000  220232969    0.142857   \n", "2               0.000006             0.000492  220232969    0.571429   \n", "3               0.000006             0.000492  220232969    0.285714   \n", "4               0.000000             0.000000  220232969    0.000000   \n", "\n", "   sold_cases     sales     stock  Cases Delivered  cases_delivered_on_sf  \\\n", "0    0.035714  0.640714  7.571429         0.000000               0.000000   \n", "1    0.017857  0.296429  8.000000         0.000000               0.000000   \n", "2    0.071429  1.377143  8.000000         0.071429               0.071429   \n", "3    0.035714  0.688571  7.714286         0.071429               0.071429   \n", "4    0.000000  0.000000  7.714286         0.000000               0.000000   \n", "\n", "   Add_Walking Cages  pallet_capacity  Add_Walking Backstock Cages  \\\n", "0           0.000000            144.0                          0.0   \n", "1           0.000000            144.0                          0.0   \n", "2           0.000006            144.0                          0.0   \n", "3           0.000006            144.0                          0.0   \n", "4           0.000000            144.0                          0.0   \n", "\n", "   Add_Walking Pallets  Backstock Cases  Backstock Pallets  \\\n", "0             0.000000              0.0                0.0   \n", "1             0.000000              0.0                0.0   \n", "2             0.000492              0.0                0.0   \n", "3             0.000492              0.0                0.0   \n", "4             0.000000              0.0                0.0   \n", "\n", "   Backstock Rollcages  Backstock Shelf Trolley  backroom_pallets  Bottle_Tag  \\\n", "0                  0.0                      0.0               0.0         0.0   \n", "1                  0.0                      0.0               0.0         0.0   \n", "2                  0.0                      0.0               0.0         0.0   \n", "3                  0.0                      0.0               0.0         0.0   \n", "4                  0.0                      0.0               0.0         0.0   \n", "\n", "   Broken Items  broken_case_flag  Bulk Pallets  Capping Shelf <PERSON>s  \\\n", "0           0.0               0.0           0.0                  0.0   \n", "1           0.0               0.0           0.0                  0.0   \n", "2           0.0               0.0           0.0                  0.0   \n", "3           0.0               0.0           0.0                  0.0   \n", "4           0.0               0.0           0.0                  0.0   \n", "\n", "   Clip Strip Cases  Clip Strip Items  Electro_Tag  Empty Pallets  \\\n", "0               0.0               0.0          0.0       0.000000   \n", "1               0.0               0.0          0.0       0.000000   \n", "2               0.0               0.0          0.0       0.000492   \n", "3               0.0               0.0          0.0       0.000492   \n", "4               0.0               0.0          0.0       0.000000   \n", "\n", "   Empty Rollcages  Empty Shelf Trolley  Full Pallet  Full Pallet Cases  \\\n", "0         0.000000             0.000000          0.0                0.0   \n", "1         0.000000             0.000000          0.0                0.0   \n", "2         0.000006             0.004456          0.0                0.0   \n", "3         0.000006             0.004456          0.0                0.0   \n", "4         0.000000             0.000000          0.0                0.0   \n", "\n", "   Gillette_Tag  H_Backstock Cases  H_CASE_H_NSRP_items  H_CASE_L_NSRP_items  \\\n", "0           0.0                0.0                  0.0                  0.0   \n", "1           0.0                0.0                  0.0                  0.0   \n", "2           0.0                0.0                  0.0                  0.0   \n", "3           0.0                0.0                  0.0                  0.0   \n", "4           0.0                0.0                  0.0                  0.0   \n", "\n", "   H_Hook Fill Cases  H_NSRP_for_opening_type  H_Pre-sorted Cases  H_SRP  \\\n", "0                0.0                      0.0                 0.0    0.0   \n", "1                0.0                      0.0                 0.0    0.0   \n", "2                0.0                      0.0                 0.0    0.0   \n", "3                0.0                      0.0                 0.0    0.0   \n", "4                0.0                      0.0                 0.0    0.0   \n", "\n", "   H_NSRP  H_SRP_for_opening_type  Hard_Tag  Hook Fill Items  \\\n", "0     0.0                     0.0       0.0              0.0   \n", "1     0.0                     0.0       0.0              0.0   \n", "2     0.0                     0.0       0.0              0.0   \n", "3     0.0                     0.0       0.0              0.0   \n", "4     0.0                     0.0       0.0              0.0   \n", "\n", "   L_Backstock Cases    L_NSRP  L_NSRP_for_opening_type  L_NSRP_Items  \\\n", "0                0.0  0.000000                 0.000000      0.000000   \n", "1                0.0  0.000000                 0.000000      0.000000   \n", "2                0.0  0.071429                 0.071429      0.571429   \n", "3                0.0  0.071429                 0.071429      0.571429   \n", "4                0.0  0.000000                 0.000000      0.000000   \n", "\n", "   L_Pre-sorted Cases  L_SRP  L_SRP_for_opening_type  MU Pallet  \\\n", "0            0.000000    0.0                     0.0        0.0   \n", "1            0.000000    0.0                     0.0        0.0   \n", "2            0.000586    0.0                     0.0        0.0   \n", "3            0.000586    0.0                     0.0        0.0   \n", "4            0.000000    0.0                     0.0        0.0   \n", "\n", "   New Delivery - Pallets  New Delivery - Rollcages  \\\n", "0                0.000000                       0.0   \n", "1                0.000000                       0.0   \n", "2                0.000496                       0.0   \n", "3                0.000496                       0.0   \n", "4                0.000000                       0.0   \n", "\n", "   New Delivery - <PERSON><PERSON>_perforated_box_cases  \\\n", "0                      0.000000                            0.0   \n", "1                      0.000000                            0.0   \n", "2                      0.004464                            0.0   \n", "3                      0.004464                            0.0   \n", "4                      0.000000                            0.0   \n", "\n", "   Ownbrand_shrink_cases  Ownbrand_tray_cases  Ownbrand_tray_with_hood_cases  \\\n", "0                    0.0                  0.0                            0.0   \n", "1                    0.0                  0.0                            0.0   \n", "2                    0.0                  0.0                            0.0   \n", "3                    0.0                  0.0                            0.0   \n", "4                    0.0                  0.0                            0.0   \n", "\n", "   Ownbrand_tray_with_shrink_cases  Pre-sorted Rollcages  \\\n", "0                              0.0              0.000000   \n", "1                              0.0              0.000000   \n", "2                              0.0              0.000006   \n", "3                              0.0              0.000006   \n", "4                              0.0              0.000000   \n", "\n", "   Pre-sorted <PERSON><PERSON>  Racking Pallets  Safer_Tag  Salami_Tag  Soft_Tag  \\\n", "0                  0.000000              0.0        0.0         0.0       0.0   \n", "1                  0.000000              0.0        0.0         0.0       0.0   \n", "2                  0.000037              0.0        0.0         0.0       0.0   \n", "3                  0.000037              0.0        0.0         0.0       0.0   \n", "4                  0.000000              0.0        0.0         0.0       0.0   \n", "\n", "   Total RC's and Pallets  High_pallet_cases_on_Dry30_and_DRY24  \\\n", "0                0.000000                                   0.0   \n", "1                0.000000                                   0.0   \n", "2                0.000498                                   0.0   \n", "3                0.000498                                   0.0   \n", "4                0.000000                                   0.0   \n", "\n", "   High_pallets_on_Dry30_and_DRY24  High_half_pallet_cases_on_Dry30_and_DRY24  \\\n", "0                              0.0                                        0.0   \n", "1                              0.0                                        0.0   \n", "2                              0.0                                        0.0   \n", "3                              0.0                                        0.0   \n", "4                              0.0                                        0.0   \n", "\n", "   High_half_pallets_on_Dry30_and_DRY24  Tag_total_nr  Case on Pallet  \\\n", "0                                   0.0           0.0             7.0   \n", "1                                   0.0           0.0             7.0   \n", "2                                   0.0           0.0           144.0   \n", "3                                   0.0           0.0           144.0   \n", "4                                   0.0           0.0             7.0   \n", "\n", "   Modules to go  Fridge Doors  Eggs displayed at UHT Milks  Racking  \\\n", "0              1           0.0                          0.0      0.0   \n", "1              1           0.0                          0.0      0.0   \n", "2              1           0.0                          0.0      0.0   \n", "3              1           0.0                          0.0      0.0   \n", "4              1           0.0                          0.0      0.0   \n", "\n", "   Cardboard Baller  Capping Shelves  Lift Allowance  Distance: WH to SF  \\\n", "0               1.0              0.0             0.0               150.0   \n", "1               1.0              0.0             0.0               150.0   \n", "2               1.0              0.0             0.0               150.0   \n", "3               1.0              0.0             0.0               150.0   \n", "4               1.0              0.0             0.0               150.0   \n", "\n", "   Distance: WH to Yard  Steps: SF-Printer  CS_DIST_CSD_2_WH  Steps Dotcom-WH  \\\n", "0                   0.0               75.0              45.0             30.0   \n", "1                   0.0               75.0              45.0             30.0   \n", "2                   0.0               75.0              45.0             30.0   \n", "3                   0.0               75.0              45.0             30.0   \n", "4                   0.0               75.0              45.0             30.0   \n", "\n", "   Steps (gates - work)  Banana Hammock  GBP_rates  \n", "0                 365.0             1.0       6.63  \n", "1                 365.0             1.0       6.63  \n", "2                 365.0             1.0       6.63  \n", "3                 365.0             1.0       6.63  \n", "4                 365.0             1.0       6.63  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["hours_df = Model_Hours_Calculation_TPN(data_paths.directory, data_paths.excel_inputs_f, store_inputs, df_times, REX_ALLOWANCE)"]}, {"cell_type": "code", "execution_count": 8, "id": "e1f21fac-fc58-4c46-a630-2a8414c8fbf1", "metadata": {}, "outputs": [{"data": {"text/plain": ["(9195837, 27)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["c.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "ed56ad5a-3aab-4cf1-ba69-1ed2a582d06b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 2min 32s\n"]}], "source": ["%%time\n", "c ,d = TimeValues_Calculation_TPN_pandas(directory, store_inputs, driver_pro, driver_repl, most_f)"]}, {"cell_type": "code", "execution_count": 6, "id": "ba3b55d0-10da-473e-b38a-5f2add91418a", "metadata": {}, "outputs": [], "source": ["def TimeValues_Calculation_TPN_pandas(directory, store_inputs, driver_pro, driver_repl, most_f ):\n", "    #TIMEVALUE TO DRIVERS\n", "\n", "\n", "    #TIMEVALUE TO DRIVERS\n", "    \n", "    \n", "    drivers_tpnb = pd.concat([driver_repl, driver_pro]).replace(np.nan,0)\n", "    drivers_tpnb['Case on Pallet'] = drivers_tpnb['cases_delivered']/(drivers_tpnb['New Delivery - Pallets']+drivers_tpnb['New Delivery - Rollcages']*0.62)\n", "    drivers_tpnb['Case on Pallet'] = drivers_tpnb['Case on Pallet'].replace(np.nan,7) # if zero then 7 cases on pallet\n", "    drivers_tpnb['Modules to go'] = 1\n", "    \n", "    drivers_tpnb.rename(columns={'cases_delivered' : 'Cases Delivered'}, inplace = True)\n", "    \n", "    dep_profiles = store_inputs[['Store','Dep','Fridge Doors','Eggs displayed at UHT Milks',\n", "                                 'Racking','Cardboard Baller','Capping Shelves',\n", "                                 'Lift Allowance','Distance: WH to SF','Distance: WH to Yard','Steps: SF-Printer','CS_DIST_CSD_2_WH','Steps Dotcom-WH',\n", "                                 'Steps (gates - work)','<PERSON><PERSON>','GBP_rates'\n", "                                 ]].drop_duplicates()\n", "    \n", "    dep_profiles.rename(columns={'Store' : 'store', 'Dep' : 'dep'}, inplace = True)\n", "    drivers_tpnb = drivers_tpnb.merge(dep_profiles, on = ['store', 'dep'], how = 'left')\n", "    \n", "    Final_drivers_for_TPN_level = drivers_tpnb.copy()\n", "    \n", "    \n", "\n", "\n", "    \n", "    stores_df = store_inputs[['Country','Store','Format','Store Name','Plan Size']].drop_duplicates()\n", "    stores_df = stores_df[stores_df.Store.isin(list(set(drivers_tpnb.store.values)))]\n", "    storelist_array = stores_df[['Country', 'Store', 'Format']].drop_duplicates().values\n", "    shelftrolley_extra_stores = store_inputs[['Country','Store', '1K_stores_for_ShelfTrolley_extra']].drop_duplicates()\n", "\n", "    \n", "    most_file = pd.ExcelFile( directory / most_f, engine='pyxlsb')\n", "    activity_list = pd.read_excel(most_file,'Time Values',skiprows=3)\n", "    \n", "    new_header = activity_list.iloc[0] #grab the first row for the header\n", "    activity_list = activity_list[1:] #take the data less the header row\n", "    activity_list.columns = new_header #set the header row as the df header\n", "    \n", "    cols = ['Activity_key_activities','Suboperation Description','Activity group','V F','DRIVER_1','DRIVER_2','FREQ2','DRIVER_3','DRIVER_4','PROFILE','RA','Head','Newspaper_Activity']\n", "    cols2 = ['Activity_key_activities','Suboperation','Activity_Group','V_F','Driver_1','Driver_2','Freq_Driver_2','Driver_3','Driver_4','Profile','RA','Head','Newspaper_Activity']\n", "    activity_list = activity_list[cols]\n", "    for x, y in zip(cols, cols2):\n", "        activity_list.rename(columns={x:y},inplace=True)\n", "    \n", "    activity_list.dropna(subset=['Activity_key_activities'],inplace=True)\n", "    activity_list.rename(columns={'Activity_key_activities':'Activity_key'},inplace=True)\n", "    activity_list['Freq_Driver_2'] = activity_list['Freq_Driver_2'].replace(np.nan,0)\n", "    activity_list = activity_list.replace(np.nan,'no_driver')\n", "    \n", "    activities = activity_list[['Activity_key']].copy()\n", "    activities['Country'] = ''\n", "    activities['Format'] = ''\n", "    activities['Dep'] = ''\n", "    activities['Store'] = 0\n", "    activities['day'] = ''\n", "    \n", "    times = pd.read_excel(most_file,'TimeValues_Py',usecols='M:R')\n", "    times.dropna(subset=['Activity_key_times'],inplace=True)\n", "    times.rename(columns={'Activity_key_times':'Activity_key'},inplace=True)\n", "    # times.drop(times[times.basic_time == 0].index, inplace = True)\n", "    # times.drop(times[times.freq == 0].index, inplace = True)\n", "    \n", "    freq_Shelftrolley_extra = times[(times['Activity_key'].str.contains(\"shelf trolley\")) & (times['Format'] == 'Express')][['Activity_key', 'Country', 'Dep', 'freq']].drop_duplicates()\n", "\n", "    \n", "    times_array = activities.values\n", "    departments = pd.DataFrame([x for x in drivers_tpnb.dep.unique()], columns=['Dep'])\n", "    dep_array = departments.values\n", "    \n", "    week_df = pd.DataFrame([a for a in drivers_tpnb['day'].unique()], columns=['day'])\n", "    weekdays_array = week_df.values\n", "    \n", "    \n", "    df_times = pd.DataFrame(columns=activities.columns)\n", "    result = len(storelist_array) * len(weekdays_array) * len(dep_array) * len(times_array)\n", "    df_array = np.empty([result,6], dtype='object') # create an empty array\n", "    counter = 0\n", "    for a in range(len(times_array)):\n", "        for d in range(len(dep_array)):\n", "            for s in range(len(storelist_array)):\n", "                for w in range(len(weekdays_array)):\n", "                    df_array[counter][0] = times_array[a][0] # activity name\n", "                    df_array[counter][3] = dep_array[d][0] # department\n", "                    df_array[counter][1] = storelist_array[s][0] # country\n", "                    df_array[counter][2] = storelist_array[s][2] # format\n", "                    df_array[counter][4] = storelist_array[s][1] # store\n", "                    df_array[counter][5] = weekdays_array[w][0] # day\n", "                    counter += 1\n", "    df_times = pd.concat([df_times,pd.DataFrame(df_array, columns=df_times.columns)])\n", "    df_times = df_times.merge(times, on=['Activity_key','Country','Format', 'Dep'], how='left')\n", "    df_times = df_times.merge(activity_list, on=['Activity_key'], how='left')\n", "    df_times.Store = pd.to_numeric(df_times.Store, errors='coerce')\n", "    \n", "    ######### ShelfTrolley 1K stores extra hours settings #########\n", "    \n", "    df_times = df_times.merge(shelftrolley_extra_stores, on=['Country', 'Store'], how='left')\n", "    \n", "    only_1k_stores_for_shelfTrolley = df_times[df_times[\"1K_stores_for_ShelfTrolley_extra\"] == 1]\n", "    \n", "    freq_Shelftrolley_extra = freq_Shelftrolley_extra[freq_Shelftrolley_extra.Country.isin(df_times.Country.unique().tolist())]\n", "    df_times = df_times[df_times[\"1K_stores_for_ShelfTrolley_extra\"] == 0]\n", "    dict_list = freq_Shelftrolley_extra.groupby([\"Activity_key\", 'Country', 'Dep'])[\"freq\"].apply(lambda s: s.tolist()).to_dict()\n", "    \n", "    for key, value in dict_list.items():\n", "        only_1k_stores_for_shelfTrolley.loc[(only_1k_stores_for_shelfTrolley['Activity_key'] == key[0])\n", "                      & (only_1k_stores_for_shelfTrolley['Country'] == key[1])\n", "                      & (only_1k_stores_for_shelfTrolley['Dep'] == key[2]), 'freq'] = value[0]\n", "\n", "    \n", "    df_times = pd.concat([df_times,only_1k_stores_for_shelfTrolley ])\n", "    \n", "    df_times.drop(\"1K_stores_for_ShelfTrolley_extra\", axis=1, inplace=True)\n", "    \n", "\n", "    \n", "    #### MERGING PART\n", "    \n", "    drivers_tpnb.rename(columns={'store' : 'Store', 'dep' : 'Dep', 'pmg' : 'Pmg', 'tpnb' : 'Tpnb', 'country' : 'Country' }, inplace = True)\n", "    \n", "\n", "    \n", "    # drivers_tpnb = drivers_tpnb.groupby(['Store','day','Dep', 'Pmg', 'Tpnb'], as_index = False,observed=True ).sum()\n", "    drivers_tpnb = drivers_tpnb.melt(id_vars=['Country','Store', 'Dep', 'Pmg', 'Tpnb', 'day'], var_name=['drivers'])\n", "    drivers_tpnb.value = pd.to_numeric(drivers_tpnb.value, errors='coerce').replace(np.nan, 0)\n", "    \n", "    flag_driver = pd.DataFrame({'Driver' : drivers_tpnb.drivers.unique(), 'flag' : 1})\n", "    d_values = [1,2,3,4] # Here we VLOOKUP driver values between df_times and drivers_df to filter only what we got in drivers_tpnb\n", "    driver_initial_name = 'Driver'\n", "    value_initial_name = 'flag'\n", "    for x in d_values:\n", "        driver_new_name = 'Driver_' + str(x)\n", "        value_new_name = 'flag_' + str(x)\n", "        flag_driver.rename(columns={driver_initial_name: driver_new_name}, inplace=True)\n", "        flag_driver.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "        df_times = df_times.merge(flag_driver, on=[driver_new_name], how='left')\n", "        df_times[value_new_name] = df_times[value_new_name].replace(np.nan,0) # it seems we need NaN there\n", "        driver_initial_name = driver_new_name\n", "        value_initial_name = value_new_name\n", "        \n", "    df_times['flag_total'] = df_times.flag_1 + df_times.flag_2 + df_times.flag_3 + df_times.flag_4\n", "    df_times = df_times[df_times.flag_total > 0]\n", "    df_times = df_times[df_times.flag_1 > 0]\n", "    df_times.drop(['flag_1', 'flag_2', 'flag_3', 'flag_4', 'flag_total'], axis = 1, inplace = True)\n", "    \n", "\n", "    \n", "    #### TPN to df_times\n", "    store_tpnb_to_merge = drivers_tpnb.iloc[:,:5].drop_duplicates()\n", "    df_times = df_times.merge(store_tpnb_to_merge, on = ['Country','Store', 'Dep'], how = 'left')\n", "    df_times = df_times[df_times.Tpnb.notnull()]\n", "    \n", "    \n", "    #### A<PERSON><PERSON>\n", "    \n", "\n", "        \n", "    columns_to_move = ['Country','Pmg', 'Tpnb']\n", "    \n", "    for c, p in zip(columns_to_move,[0,5,6]):\n", "        \n", "        move_column_inplace(df_times, c, p)\n", "        \n", "    \n", "    d_values = [1,2,3,4] # Here we VLOOKUP driver values between df_times and drivers_df. We have 4 drivers\n", "    driver_initial_name = 'drivers'\n", "    value_initial_name = 'value'\n", "    for x in d_values:\n", "        driver_new_name = 'Driver_' + str(x)\n", "        value_new_name = 'Driver_' + str(x) + '_value'\n", "        drivers_tpnb.rename(columns={driver_initial_name: driver_new_name}, inplace=True)\n", "        drivers_tpnb.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "        df_times = df_times.merge(drivers_tpnb, on=['Country','Store', 'Tpnb', 'Dep', 'Pmg', 'day', driver_new_name], how='left')\n", "        df_times[value_new_name] = df_times[value_new_name].replace(np.nan,0) # it seems we need NaN there\n", "        driver_initial_name = driver_new_name\n", "        value_initial_name = value_new_name\n", "    driver_new_name = 'Profile' # Profiles\n", "    value_new_name = 'Profile_value'\n", "    drivers_tpnb.rename(columns={driver_initial_name: driver_new_name}, inplace=True)\n", "    drivers_tpnb.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "    \n", "    df_times = df_times.merge(drivers_tpnb, on=['Country','Store', 'Tpnb', 'Dep', 'Pmg', 'day', driver_new_name], how='left')\n", "    df_times[value_new_name] = df_times[value_new_name].replace(np.nan,0) # it seems we need NaN there\n", "    drivers_tpnb.rename(columns={driver_new_name: 'drivers'}, inplace=True)\n", "    drivers_tpnb.rename(columns={value_new_name: 'value'}, inplace=True)\n", "    \n", "    df_times = df_times.loc[(df_times.basic_time > 0)]\n", "    df_times = df_times.loc[(df_times.freq > 0)]\n", "    \n", "    \n", "    return df_times, Final_drivers_for_TPN_level\n"]}, {"cell_type": "code", "execution_count": null, "id": "4b4fac5c-1861-4d25-911b-02477efd15ea", "metadata": {}, "outputs": [], "source": ["store_tpnb_to_merge.with_columns([pl.col(\"Country\").cast(pl.Utf8),\n", "                                                       pl.col(\"Pmg\").cast(pl.Utf8)])"]}, {"cell_type": "code", "execution_count": null, "id": "fe51c0fc-9dfc-431a-b94f-91807fc4379f", "metadata": {}, "outputs": [], "source": ["store_tpnb_to_merge.head()"]}, {"cell_type": "code", "execution_count": null, "id": "363b3caf-d099-46c3-9d5d-13b3d7fa12e6", "metadata": {}, "outputs": [], "source": ["df_times.collect().head()"]}, {"cell_type": "code", "execution_count": null, "id": "5c916678-618c-4aa0-9f73-3d2a3faa94bd", "metadata": {}, "outputs": [], "source": ["df_times"]}, {"cell_type": "code", "execution_count": null, "id": "ef1e93be-4217-481e-8819-2e13ccc6f2a0", "metadata": {}, "outputs": [], "source": ["storelist_array = stores_df[['Country', 'Store', 'Format']].drop_duplicates().values.tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "2b309d77-c679-4770-8cb0-2fad61d171e4", "metadata": {}, "outputs": [], "source": ["a = pl.from_pandas(activity_list[['Activity_key']].copy())"]}, {"cell_type": "code", "execution_count": null, "id": "ca03dc94-0fec-499d-b113-43b636a08a69", "metadata": {"tags": []}, "outputs": [], "source": ["a = a.with_columns([\n", "    pl.lit(None).alias(\"x\")])\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d4c96260-44ce-44c9-be14-b5d5b80b6c26", "metadata": {"tags": []}, "outputs": [], "source": ["storelist_array[0][1]"]}, {"cell_type": "code", "execution_count": null, "id": "7246e66b-ad71-47d0-a3b4-e896be948436", "metadata": {}, "outputs": [], "source": ["a.select([\n", "        pl.all().exclude([\"x\"]),\n", "        pl.col(\"x\").arr().get(0).alias(\"Country\"),\n", "        pl.col(\"x\").arr().get(1).alias(\"Store\"),\n", "        pl.col(\"x\").arr().get(2).alias(\"Format\"),\n", "    ])"]}, {"cell_type": "code", "execution_count": null, "id": "951827c1-299c-4f80-91ab-45d0a1c9b5bd", "metadata": {"tags": []}, "outputs": [], "source": ["a.with_columns([\n", "   pl.col(\"x\").map(lambda s: storelist_array[0]).alias(\"x\").cast(pl.list) ])"]}, {"cell_type": "code", "execution_count": null, "id": "0ea35306-3837-41f3-a618-df045e2463d9", "metadata": {}, "outputs": [], "source": ["x.head()"]}, {"cell_type": "code", "execution_count": null, "id": "39975ff2-b266-4303-8f9d-19944e18c3a7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "979010d5-2679-4f7a-a87c-d1609d88b3b5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5d012176-5cf8-4d7b-a4fc-513bd0913096", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5af9de7b-54af-4208-a1b3-061a8e79284c", "metadata": {}, "outputs": [], "source": ["country = list(stores_df['Country'].drop_duplicates())\n", "stores = list(stores_df['Store'].drop_duplicates())\n", "format_ = list(stores_df['Format'].drop_duplicates())"]}, {"cell_type": "code", "execution_count": null, "id": "73932221-aa80-4f23-a04a-4705ae68db5e", "metadata": {}, "outputs": [], "source": ["a = a.with_columns([\n", "    pl.lit(None).alias(\"Country\"),\n", "    pl.lit(None).alias(\"Store\"),\n", "    pl.lit(None).alias(\"Dep\"),\n", "    pl.lit(None).alias(\"Format\"),\n", "    pl.lit(None).alias(\"day\"),\n", "\n", "    \n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "50315bcf-1c54-4790-b8b5-e67d0b11c5a8", "metadata": {}, "outputs": [], "source": ["dep_array"]}, {"cell_type": "code", "execution_count": null, "id": "623b93b9-1b84-41cd-92c9-588ba594edc6", "metadata": {"tags": []}, "outputs": [], "source": ["   a.with_columns([\n", "       pl.col(\"Country\").map(lambda s: country).alias(\"Country\"),\n", "       pl.col(\"Store\").map(lambda s: stores).alias(\"Store\"),\n", "       pl.col(\"Format\").map(lambda s: format_).alias(\"Format\")\n", "   ]).explode(\"Country\").explode(\"Store\").explode(\"Store\")"]}, {"cell_type": "code", "execution_count": null, "id": "f7f261ca-d431-4983-83ad-2b0d31c35efe", "metadata": {"tags": []}, "outputs": [], "source": ["storelist_array"]}, {"cell_type": "code", "execution_count": null, "id": "a389742b-260f-4721-a1ef-0a44f9fd1eda", "metadata": {}, "outputs": [], "source": ["Repl_Dataset = isold.select(['store','pmg', 'tpnb']).unique()\n", "Repl_Dataset = Repl_Dataset.with_columns([  pl.lit(None).alias(\"day\")])\n", "Repl_Dataset = Repl_Dataset.with_columns([pl.col(\"day\").map(lambda s: weekdays).alias(\"day\")])\n", "Repl_Dataset = Repl_Dataset.explode(\"day\")"]}, {"cell_type": "code", "execution_count": null, "id": "1b9b14b0-6552-497b-924e-e6d91458042d", "metadata": {}, "outputs": [], "source": ["activities.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e2713867-bfaf-4754-b330-149b94247cdf", "metadata": {}, "outputs": [], "source": ["a = pl.DataFrame({\n", "    'Activity_key': list(activities.Activity_key)\n", "})"]}, {"cell_type": "code", "execution_count": null, "id": "3ce5c0f1-2947-49ba-8cc4-d73c1c21ed57", "metadata": {}, "outputs": [], "source": ["a = pl.from_pandas(activities)"]}, {"cell_type": "code", "execution_count": null, "id": "c98cde92-b29f-4389-9783-e695d4997f28", "metadata": {}, "outputs": [], "source": ["a = a.with_columns([\n", "    pl.lit(None).alias(\"x\")])"]}, {"cell_type": "code", "execution_count": null, "id": "44ca2d00-6607-43ec-9460-80432278b8b6", "metadata": {}, "outputs": [], "source": ["\n", "\n", "a = a.with_columns([\n", "    pl.col(\"x\").map(lambda s: weekdays).alias(\"x\")\n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "909fc17f-4aaf-4dfc-9449-5b00f254dc34", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7af41e00-cba7-474f-9e1c-600820eb5c1a", "metadata": {}, "outputs": [], "source": ["c = storelist_array[:6]"]}, {"cell_type": "code", "execution_count": null, "id": "bff3de8b-c8ff-44c5-b06b-f139ddd03a52", "metadata": {"tags": []}, "outputs": [], "source": ["weekdays = ['Monday',\"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\" , \"Saturday\", \"Sunday\"]"]}, {"cell_type": "code", "execution_count": null, "id": "523bdb75-62ef-4397-bd1a-abab7f19556f", "metadata": {"tags": []}, "outputs": [], "source": ["a.select([\n", "        pl.all().exclude([\"x\"]),\n", "        pl.col(\"x\").arr().get(0).alias(\"Country\"),\n", "        pl.col(\"x\").arr().get(1).alias(\"Store\"),\n", "        pl.col(\"x\").arr().get(2).alias(\"Format\"),\n", "    ])\n"]}, {"cell_type": "code", "execution_count": null, "id": "0d689354-6ade-4098-8b4a-9c60d174a104", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7d9b8794-d6c7-4c51-a97c-568bc44d6812", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}