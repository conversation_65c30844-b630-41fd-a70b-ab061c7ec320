import sys
import os
import pandas as pd
from pathlib import Path
import numpy as np
sys.path.append(os.path.dirname(Path.cwd()))
import re
import json
import paramiko
import time



start = '20250526'
end = '20250831'

# Define missing variables
saved_filename = f"cases_delivered_{start}_{end}"
directory = Path(__file__).parent.parent.parent if "__file__" in locals() else Path.cwd()
place_to_save = Path(directory / "ad_hoc_py/cases_delivered_full")

# Create directory if it doesn't exist
place_to_save.mkdir(parents=True, exist_ok=True)
(place_to_save / saved_filename).mkdir(parents=True, exist_ok=True)




# Construct the path to the config file one level up from the script directory or current working directory
config_path = os.path.join(directory, 'config.json')

# Load the configuration from the JSON file
with open(config_path, 'r') as file:
    config = json.load(file)

# Extract the necessary details from the configuration
hostname = config['SSH_hostname']
username = config['SSH_username']
password = config['SSH_password']


ODBC_CONN = config['ODBC_connection']






        
        
def update_cases_delivered_sql(hostname, password, start_date, end_date):
    """
    Update the SQL file on the server with the correct date parameters
    """
    print(f"📝 Updating SQL file with dates: {start_date} to {end_date}")

    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        # Connect to server
        ssh_client.connect(hostname=hostname, username="phrubos", password=password)

        # Setup FTP client to download the SQL file
        ftp_client = ssh_client.open_sftp()

        # Download the SQL file
        remote_sql_path = "/home/<USER>/cases_delivered_full/unit_cases_delivered_prod.sql"
        local_sql_path = place_to_save / saved_filename / "unit_cases_delivered_prod.sql"

        # Ensure local directory exists
        local_sql_path.parent.mkdir(parents=True, exist_ok=True)

        ftp_client.get(remote_sql_path, str(local_sql_path))
        print("📥 Downloaded SQL file from server")

        # Read and modify the SQL file
        with open(local_sql_path, 'r') as file:
            sql_content = file.read()

        # Apply date parameter substitution
        start_pattern = r"(?<=BETWEEN\s)(\d{8})"
        end_pattern = r"(?<=AND\s)(\d{8})"

        modified_content = re.sub(start_pattern, start_date, sql_content)
        modified_content = re.sub(end_pattern, end_date, modified_content)

        print(f"🔄 Updated SQL: BETWEEN {start_date} AND {end_date}")

        # Write the modified content back
        with open(local_sql_path, 'w') as file:
            file.write(modified_content)

        # Upload the modified file back to server
        ftp_client.put(str(local_sql_path), remote_sql_path)
        print("📤 Uploaded modified SQL file to server")

        ftp_client.close()
        ssh_client.close()

        return True

    except Exception as e:
        print(f"❌ Error updating SQL file: {e}")
        if 'ftp_client' in locals():
            ftp_client.close()
        if ssh_client:
            ssh_client.close()
        return False


def ssh_table_create(what_to_create, start, end, pmg, nr_weeks, wp_working_output, saved_name):
    """
    Create tables via SSH connection to Hadoop server
    """
    if what_to_create == "cases_delivered":
        # First, update the SQL file with the correct date parameters
        success = update_cases_delivered_sql(hostname, password, start, end)

        if success:
            # Then run the script
            success = run_cases_delivered_script(hostname, password)

            if success:
                flag = 0
                print("=" * 50)
                print("🎉 All done! Your cases_delivered table is ready.")
            else:
                flag = 1
                print("💥 Something went wrong. Check the server logs.")
                print("=" * 50)
        else:
            flag = 1
            print("💥 Failed to update SQL parameters.")
            print("=" * 50)

        return flag

    else:
        flag = 0
        ssh_client = None
        ftp_client = None

        try:
            # Setup the connection
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh_client.connect(hostname=hostname, username="phrubos", password=password)
            print("\nConnection is done\n")

            # Setup FTP client
            ftp_client = ssh_client.open_sftp()

            ftp_client.get(f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql",
                          wp_working_output / saved_name / f"{what_to_create}_create_table.sql")

            file_path = wp_working_output / saved_name / f"{what_to_create}_create_table.sql"

            # Clean start and end parameters
            start_clean = start.strip("'") if start else start
            end_clean = end.strip("'") if end else end

            # parameter the SQL file
            with open(file_path, 'r') as file:
                sql_content = file.read()

                # Updated patterns to match the actual SQL format: part_col BETWEEN 20250630 AND 20250630
                start_pattern = r"(?<=BETWEEN\s)(\d{8})"
                end_pattern = r"(?<=AND\s)(\d{8})"

                modified_content = re.sub(start_pattern, start_clean, sql_content)
                modified_content = re.sub(end_pattern, end_clean, modified_content)

                # weeks_pattern = r"/\d+\sAS"
                # modified_content = re.sub(weeks_pattern, f"/{nr_weeks} AS", modified_content)

                if pmg is not None:
                    # New pattern to replace PMG values
                    pmg_pattern = r"WHERE SUBSTRING\(pmg, 1, 3\) IN \([^)]+\)"
                    modified_content = re.sub(pmg_pattern, f"WHERE SUBSTRING(pmg, 1, 3) IN {pmg}", modified_content)

            with open(file_path, 'w') as file:
                file.write(modified_content)

            ftp_client.put(wp_working_output / saved_name / f"{what_to_create}_create_table.sql",
                          f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql")

            print(f"\nSQL file Modification complete. Saved to\n{file_path}\n")
            print(f"\nScript ({what_to_create}) is being started.....\n")

            _, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{what_to_create}/start_q")
            exit_status = stdout.channel.recv_exit_status()

            if exit_status == 0:
                print(f"\nScript ({what_to_create}) finished successfully.\n")
            else:
                print(f"Script failed with an error:\n{stderr.read().decode('utf-8')}")
                flag += 1

        except Exception as e:
            print(f"\nError occurred: {str(e)}\n")
            flag += 1
        finally:
            if ftp_client:
                ftp_client.close()
            if ssh_client:
                ssh_client.close()

        return flag


def run_cases_delivered_script(hostname, password, timeout_minutes=90):
            """
            Simple SSH monitor for cases_delivered SQL script execution
            """
            
            print("=" * 50)
            print("🏪 Cases_delivered Analysis Script Monitor")
            print("=" * 50)
            
            
            
            print(f"🔌 Connecting to {hostname}...")
            
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            try:
                # Connect
                ssh_client.connect(hostname=hostname, username="phrubos", password=password, timeout=30)
                print("✅ Connected successfully")
                
                # Quick file check
                _, stdout, stderr = ssh_client.exec_command("ls -la /home/<USER>/cases_delivered_full/unit_cases_delivered_prod.sql")
                if 'unit_cases_delivered_prod.sql' not in stdout.read().decode('utf-8'):
                    print("❌ SQL file not found!")
                    return False

                # Start execution
                print(f"🚀 Starting cases_delivered script execution...")
                start_time = time.time()

                _, stdout, stderr = ssh_client.exec_command("sh /home/<USER>/cases_delivered_full/start_q 2>&1", get_pty=True)
                
                # Monitor progress
                last_update = time.time()
                while True:
                    if stdout.channel.exit_status_ready():
                        break
                        
                    if stdout.channel.recv_ready():
                        chunk = stdout.channel.recv(1024).decode('utf-8', errors='ignore')
                        if chunk:
                            last_update = time.time()
                            
                            # Show only important progress
                            for line in chunk.split('\n'):
                                line = line.strip()
                                if not line:
                                    continue
                                    
                                # Key progress indicators
                                if 'CREATE TABLE' in line.upper():
                                    print("📋 Creating table...")
                                elif 'DROP TABLE' in line.upper():
                                    print("🗑️  Dropping old table...")
                                elif 'SELECT DISTINCT' in line.upper():
                                    print("🔍 Processing data...")
                                elif 'Job' in line and 'finished' in line:
                                    print("✨ Stage completed")
                                elif 'Exception' in line or 'ERROR' in line.upper():
                                    print(f"⚠️  Warning: {line[:80]}...")
                    
                    # Timeout check
                    elapsed = time.time() - start_time
                    idle_time = time.time() - last_update
                    
                    if elapsed > (timeout_minutes * 60):
                        print(f"⏰ Timeout after {timeout_minutes} minutes")
                        return False
                        
                    if idle_time > 60:  # Show progress every minute of silence
                        print(f"⏳ Still running... ({elapsed/60:.1f} minutes elapsed)")
                        last_update = time.time()
                        
                    time.sleep(2)
                
                # Check result
                exit_status = stdout.channel.recv_exit_status()
                total_time = time.time() - start_time
                
                print(f"⏱️  Execution time: {total_time/60:.1f} minutes")
                
                if exit_status == 0:
                    print("✅ Script completed successfully!")
                    
                    # Table repair and refresh to fix corrupted files
                    print("� Refreshing and repairing table...")

                    # First, refresh the table metadata
                    refresh_cmd = "echo 'REFRESH TABLE sch_analysts.tbl_cases_delivered_productivity;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"

                    _, stdout, stderr = ssh_client.exec_command(refresh_cmd, timeout=120)
                    refresh_output = stdout.read().decode('utf-8')
                    refresh_error = stderr.read().decode('utf-8')

                    if 'Error' not in refresh_output and 'Exception' not in refresh_output:
                        print("✅ Table refreshed successfully")
                    else:
                        print("⚠️  Table refresh had warnings (this is normal)")

                    # Try to repair the table if it's a Hive table
                    print("🔧 Attempting table repair...")
                    repair_cmd = "echo 'MSCK REPAIR TABLE sch_analysts.tbl_cases_delivered_productivity;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"

                    _, stdout, stderr = ssh_client.exec_command(repair_cmd, timeout=120)
                    repair_output = stdout.read().decode('utf-8')

                    if 'Error' not in repair_output and 'Exception' not in repair_output:
                        print("✅ Table repair completed")
                    else:
                        print("ℹ️  Table repair not needed (Delta/Iceberg table)")

                    # Quick verification with basic query
                    print("� Verifying table accessibility...")
                    verify_cmd = "echo 'SELECT 1 as test_query LIMIT 1;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"

                    _, stdout, stderr = ssh_client.exec_command(verify_cmd, timeout=60)
                    verify_output = stdout.read().decode('utf-8')

                    if '1' in verify_output and 'Error' not in verify_output:
                        print("✅ Table is accessible and ready for queries!")
                    else:
                        print("⚠️  Table created but may need manual refresh")

                    return True
                else:
                    print(f"❌ Script failed with exit code: {exit_status}")
                    return False
                    
            except paramiko.AuthenticationException:
                print("❌ Authentication failed - check credentials")
                return False
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                return False
            finally:
                ssh_client.close()
                print("🔌 Connection closed")


# Main execution
if __name__ == "__main__":
    print("Starting cases_delivered table creation process...")
    result = ssh_table_create("cases_delivered", start, end, None, None, place_to_save, saved_filename)

    if result == 0:
        print("✅ Process completed successfully!")
    else:
        print("❌ Process completed with errors.")

    print(f"Result code: {result}")