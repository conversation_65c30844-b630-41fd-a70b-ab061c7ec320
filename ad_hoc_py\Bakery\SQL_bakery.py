import pandas as pd
import warnings
import pyodbc
import polars as pl
from datetime import datetime
from pathlib import Path
import numpy as np 
import os

warnings.filterwarnings("ignore")
pd.set_option('display.max_columns', None)


def optimize_types(dataframe):
    np_types = [
        np.int8,
        np.int16,
        np.int32,
        np.int64,
        np.uint8,
        np.uint16,
        np.uint32,
        np.uint64,
        np.float32,
        np.float64,
    ]  # , np.float16, np.float32, np.float64
    np_types = [np_type.__name__ for np_type in np_types]
    type_df = pd.DataFrame(data=np_types, columns=["class_type"])

    type_df["min_value"] = type_df[type_df["class_type"].str.contains("int")][
        "class_type"
    ].apply(lambda row: np.iinfo(row).min)
    type_df["max_value"] = type_df[type_df["class_type"].str.contains("int")][
        "class_type"
    ].apply(lambda row: np.iinfo(row).max)
    type_df["min_value_f"] = type_df[type_df["class_type"].str.contains("float")][
        "class_type"
    ].apply(lambda row: np.finfo(row).min)
    type_df["max_value_f"] = type_df[type_df["class_type"].str.contains("float")][
        "class_type"
    ].apply(lambda row: np.finfo(row).max)
    type_df["min_value"] = np.where(
        type_df["min_value"].isna(), type_df["min_value_f"], type_df["min_value"]
    )
    type_df["max_value"] = np.where(
        type_df["max_value"].isna(), type_df["max_value_f"], type_df["max_value"]
    )
    type_df.drop(columns=["min_value_f", "max_value_f"], inplace=True)

    type_df["range"] = type_df["max_value"] - type_df["min_value"]
    type_df.sort_values(by="range", inplace=True)
    try:
        for col in dataframe.loc[:, dataframe.dtypes == np.integer]:
            col_min = dataframe[col].min()
            col_max = dataframe[col].max()
            temp = type_df[
                (type_df["min_value"] <= col_min) & (type_df["max_value"] >= col_max)
            ]
            optimized_class = temp.loc[temp["range"].idxmin(), "class_type"]
            # print("Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}".format(col, col_min, col_max, optimized_class))
            dataframe[col] = dataframe[col].astype(optimized_class)
    except ValueError:
        pass
    try:
        for col in dataframe.loc[:, (dataframe.dtypes == np.floating)]:
            col_min = dataframe[col].min()
            col_max = dataframe[col].max()
            type_df = type_df[
                type_df["class_type"].astype("string").str.contains("float")
            ]
            temp = type_df[
                (type_df["min_value"] <= col_min) & (type_df["max_value"] >= col_max)
            ]
            optimized_class = temp.loc[temp["range"].idxmin(), "class_type"]
            # print("Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}".format(col, col_min, col_max, optimized_class))
            dataframe[col] = dataframe[col].astype(optimized_class)
    except ValueError:
        pass
    return dataframe


def optimize_floats(df: pd.DataFrame) -> pd.DataFrame:
    floats = df.select_dtypes(include=["float64"]).columns.tolist()
    df[floats] = df[floats].apply(pd.to_numeric, downcast="float")
    return df


def optimize_ints(df: pd.DataFrame) -> pd.DataFrame:
    ints = df.select_dtypes(include=["int64"]).columns.tolist()
    df[ints] = df[ints].apply(pd.to_numeric, downcast="integer")
    return df


def optimize_objects(df: pd.DataFrame):
    try:
        for col in df.select_dtypes(include=["object"]):
            if not (type(df[col][0]) == list):
                num_unique_values = len(df[col].unique())
                num_total_values = len(df[col])
                if float(num_unique_values) / num_total_values < 0.5:
                    df[col] = df[col].astype("category")
    except IndexError:
        pass
    return df


def optimize(df: pd.DataFrame):
    return optimize_floats(optimize_ints(optimize_objects(df)))




def weeks_calc(start_date, end_date):
    start_date = start_date-1
    start_date = datetime.strptime(str(start_date), '%Y%m%d')
    end_date = end_date
    end_date = datetime.strptime(str(end_date), '%Y%m%d')
    # Calculate the number of weeks
    num_weeks = (end_date - start_date).days / 7
    return num_weeks


saved_name = '2024_0610_0722_rc11'

output_dir = f'{saved_name}'

directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd()).parent

start_date = 20240610
end_date = 20240722

nr_weeks =  weeks_calc(start_date, end_date)


country = "('HU','CZ','SK')"  #'HU','CZ','SK'

sec_code = "(1101, 1102, 1103, 1104, 1105, 1107)"

Rc_codes = "(11, 3, 4, 14, 544)" #"(3, 4, 14, 544)"


print('\n###################')
print('Bakery Waste Downloading started....')
print('###################\n')

# =============================================================================
# Downloading Waste data from Hadoop
# =============================================================================
# with pyodbc.connect(
#     "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
# ) as conn:
    

    
    
#     sql = """
#     select
#     a2.cntr_code as Country
#     ,a2.dmst_store_code as `Local Store`
#     ,a2.dmst_store_des as `Unnamed: 2`
#     ,a3.dmat_sec_code as `Section (Gold)`
#     ,a3.dmat_sec_des_en as _1
#     ,a3.slad_tpn as `TPN item`
#     ,a3.slad_long_des as _2
#     ,concat(substr(a1.part_col,1,4),".",substr(a1.part_col,5,2),".",substr(a1.part_col,7,2)) as Day
#     ,a1.lssls_rcode as `Local RC stock correction RC stock loss`
#     from dw.ls_stock_loss a1
#     join dm.dim_stores a2 on a1.lssls_dmst_id = dmst_store_id and a1.lssls_cntr_id = a2.cntr_id
#     join dm.dim_artgld_details a3 on a1.lssls_dmat_id = a3.slad_dmat_id and a1.lssls_cntr_id = a3.cntr_id
#     /*join dw.ls_stock_loss_corr a4 on a1.lssls_cntr_id = a4.lsslsc_cntr_id and a1.lssls_dmst_id = a4.lsslsc_dmst_id and a1.lssls_dmat_id = a4.lsslsc_dmat_id and a1.lssls_rcode = a4.lsslsc_rcode and a1.lssls_day = a4.lsslsc_day*/
#     where
#     a1.lssls_rcode in {Rc_codes} and
#     a2.cntr_code in {country} and
#     a1.part_col between {start_date} and {end_date}
#     and a3.dmat_sec_code in {sec_code}
#     order by a2.dmst_store_code, a1.lssls_rcode, a3.slad_tpn;
    
    
#     """.format(country = country, start_date=start_date, end_date=end_date, sec_code = sec_code, Rc_codes=Rc_codes)
       
#     waste_bakery =  pd.read_sql(sql, conn)
    
# waste_bakery = optimize_objects(optimize_types(waste_bakery))



# os.makedirs(directory / 'inputs/files_for_dataset'/ output_dir, exist_ok=True)

# unique_values = waste_bakery['Local RC stock correction RC stock loss'].unique()

# # Process each unique value
# for value in unique_values:
#     # Filter the DataFrame for the current value
#     filtered_df = waste_bakery[waste_bakery['Local RC stock correction RC stock loss'] == value]
    
#     # Create the output filename
#     output_filename = f"bakery_inputs_rc_{int(value)}.txt"
#     output_path = directory / 'inputs/files_for_dataset' / output_dir /  output_filename
    
#     # Save the filtered DataFrame to a text file with semicolon separator
#     filtered_df.to_csv(output_path, sep=';', index=False)






    
# print('\n###################')
# print('Bakery Waste Downloading done!!')
# print('###################\n')
# # =============================================================================
# # Customize the Waste data   
# # =============================================================================
# waste_df = waste_bakery.groupby(['country', 'store', 'store_name','sec_name', 'rc_type'], observed=True)['tpnb'].count().reset_index()
# waste_df['tpnb'] = waste_df['tpnb'] / nr_weeks
# waste_df = waste_df.pivot_table(observed=True, index=['country', 'store', 'store_name', 'rc_type'],columns='sec_name',aggfunc = 'sum', fill_value=0).reset_index()
# waste_df.columns = [string.replace('tpnb', '') for string in list(waste_df.columns.map(''.join))]
# waste_df['Plant_central'] = waste_df['Plant_central'] + waste_df['Emergn_markets']
# waste_df.drop("Emergn_markets", axis=1, inplace=True)
# waste_df.rename(columns={'Plant_local': 'DFD'},  inplace=True)

# # =============================================================================
# # Weekly Waste TPN
# # =============================================================================

# waste_df_544 = waste_df[waste_df.rc_type == 544]
# waste_df_544.rename(columns={'Bake-off':'Bake-off_544', 'ISB_Bakery':'ISB_Bakery_544', 'DFD':'DFD_544'}, inplace=True)

# waste_df_rest = waste_df[waste_df.rc_type != 544]

# weekly_waste_tpn = pd.DataFrame()

# for y in [waste_df_544, waste_df_rest ]:
#     df = y[[x for x in y.columns if x not in ('rc_type','store_name')]].groupby(['country', 'store'], observed=True).sum().reset_index()
    
#     weekly_waste_tpn = pd.concat([df, weekly_waste_tpn]).replace(np.nan, 0)

# weekly_waste_tpn = weekly_waste_tpn.groupby(['country', 'store'], observed=True).sum().reset_index()

# weekly_waste_tpn.drop("Patisserie_Cake", axis=1, inplace=True)


# print('\n###################')
# print('Bakery Waste DataFrame Transformation Done!!')
# print('###################\n')

# sql="""

# REFRESH TABLE dw.ls_stock_loss_corr


# """

# a = pd.read_sql(sql, conn)

# sql = """

# select * from dw.sl_sms
# limit 10
# """
# a = pd.read_sql(sql, conn)


"""----Country Codes: '##', '##' (example 'CZ', 'SK')

# ----List Of Sec: '####', '####' (example '0210', '0230')

# --- List Of Dates: 'yyyymmdd' (example '20210801', '20210802')

# --- RTC Codes: '#', '#' (example 'RTC', 'CLI')"""


print('\n###################')
print('Bakery Sold_Units Etc. Downloading started.....')
print('###################\n')

with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:

    sql= """
    
    select *
    
    from (
    
    select distinct a13.CNTR_Code Country,
    
    a13.dmst_store_code Local_Store,
    
    a13.dmst_store_des Local_Store_Name,
    
    coalesce(sal.Section_Gold, trg.Section_Gold, rtc.Section_Gold, los.Section_Gold) Section_Gold,
    
    coalesce(sal.SEC_DES_EN, trg.SEC_DES_EN, rtc.SEC_DES_EN, los.SEC_DES_EN) SEC_DES_EN,
    
    coalesce(sal.Group_Gold, trg.Group_Gold, rtc.Group_Gold, los.Group_Gold) Group_Gold,
    
    coalesce(sal.GRP_DES_EN, trg.GRP_DES_EN, rtc.GRP_DES_EN, los.GRP_DES_EN) GRP_DES_EN,
    
    coalesce(sal.Subgroup_Gold, trg.Subgroup_Gold, rtc.Subgroup_Gold, los.Subgroup_Gold) Subgroup_Gold,
    
    coalesce(sal.SGR_DES_EN, trg.SGR_DES_EN, rtc.SGR_DES_EN, los.SGR_DES_EN) SGR_DES_EN,
    
    coalesce(sal.slad_tpn, trg.slad_tpn, rtc.slad_tpn, los.slad_tpn) TPN_item,
    
    coalesce(sal.slad_tpnb, trg.slad_tpnb, rtc.slad_tpnb, los.slad_tpnb) tpnb,
    
    coalesce(sal.SLAD_LONG_DES, trg.SLAD_LONG_DES, rtc.SLAD_LONG_DES, los.SLAD_LONG_DES) TPN_item_Name,
    
    coalesce(sal.TPNCE_ID, trg.TPNCE_ID, rtc.TPNCE_ID, los.TPNCE_ID) TPNCE_ID,
    
    coalesce(sal.rtc_rc, trg.rtc_rc, rtc.rtc_rc, los.rtc_rc) Local_RC_rtc,
    
    sal.LNASS_CART Carton_size,
    
    nvl(sal.SLSMS_UNIT_CS,0) Sold_units,
    
    nvl(sal.SLSMS_SALEX_CS,0) Sales_excl_VAT,
    
    nvl(sal.SLSMS_SALEX_CS_GBP,0) Sales_excl_VAT_GBP,
    
    nvl(los.RC2_units,0) RC2_units,
    
    nvl(los.RC3_units,0) RC3_units,
    
    nvl(los.RC4_units,0) RC4_units,
    
    nvl(los.RC544_units,0) RC544_units,
    
    nvl(los.RC13_units,0) RC13_units,
    
    nvl(los.RC14_units,0) RC14_units,
    
    nvl(sal.SLSMS_TR_TRNUM,0) Transaction_count,
    
    nvl(trg.SLTRG_TR_UNIT,0) GHS_excl_GIAB_Sold_units,
    
    nvl(trg.SLTRG_TR_TRNUM,0) GHS_excl_GIAB_Transaction_count,
    
    nvl(rtc.SLRTC_RTCUNIT,0) RTC_units
    
    from (
    
    select a11.SLSMS_cntr_id CNTR_ID,
    
    a11.SLSMS_dmst_id dmst_store_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE Section_Gold,
    
    a12.DMAT_SEC_DES_EN SEC_DES_EN,
    
    a12.DMAT_GRP_CODE Group_Gold,
    
    a12.DMAT_GRP_DES_EN GRP_DES_EN,
    
    a12.DMAT_SGR_CODE Subgroup_Gold,
    
    a12.DMAT_SGR_DES_EN SGR_DES_EN,
    
    a13.rtc_rc,
    
    LNASS_CART,
    
    sum(a11.SLSMS_TR_TRNUM) SLSMS_TR_TRNUM,
    
    sum(a11.SLSMS_SALEX_CS) SLSMS_SALEX_CS,
    
    sum(a11.SLSMS_UNIT_CS) SLSMS_UNIT_CS,
    
    sum(a11.SLSMS_SALEX_CS / nvl(DMEXR_RATE,1)) SLSMS_SALEX_CS_GBP
    
    from dw.sl_sms a11
    
    join dm.dim_time_d tim on a11.part_col = tim.dmtm_d_code
    
    join dm.DIM_ARTGLD_DETAILS a12 on (a11.SLSMS_cntr_id = a12.CNTR_ID and a11.SLSMS_dmat_id = a12.slad_dmat_id)
    
    join dm.rtc_rc a13 on (a11.SLSMS_cntr_id = a13.CNTR_ID)
    
    left join DW.DIM_EXCHANGE_RATES rat on a11.SLSMS_cntr_id = rat.DMEXR_CNTR_ID and rat.DMEXR_CRNCY_TO = 'GBP' and tim.DMTM_FY_CODE = rat.DMEXR_DMTM_FY_CODE
    
    left join (select a12.CNTR_ID, a12.SLAD_DMAT_ID, max(coalesce(cart1.LNASS_CART,cart2.LNASS_CART,cart4.LNASS_CART)) LNASS_CART
    
    from dm.DIM_ARTGLD_DETAILS a12
    
    left join sylncz.ln_art_sup_st cart1 on a12.CNTR_ID = 1 and a12.SLAD_DMAT_ID = cart1.LNASS_DMAT_ID
    
    left join sylnsk.ln_art_sup_st cart2 on a12.CNTR_ID = 2 and a12.SLAD_DMAT_ID = cart2.LNASS_DMAT_ID
    
    left join sylnhu.ln_art_sup_st cart4 on a12.CNTR_ID = 4 and a12.SLAD_DMAT_ID = cart4.LNASS_DMAT_ID
    
    where a12.CNTR_CODE in {country}
    
    and a12.DMAT_SEC_CODE in {sec_code}
    
    group by a12.CNTR_ID, a12.SLAD_DMAT_ID
    
    ) cart on a12.CNTR_ID = cart.CNTR_ID and a12.SLAD_DMAT_ID = cart.SLAD_DMAT_ID
    
    where (a12.CNTR_CODE in {country}
    
    and a12.DMAT_SEC_CODE in {sec_code}
    
    and a11.part_col between {start_date} and {end_date}
    
    and a13.rtc_rc in ('RTC') )
    
    group by a11.SLSMS_cntr_id,
    
    a11.SLSMS_dmst_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE,
    
    a12.DMAT_SEC_DES_EN,
    
    a12.DMAT_GRP_CODE,
    
    a12.DMAT_GRP_DES_EN,
    
    a12.DMAT_SGR_CODE,
    
    a12.DMAT_SGR_DES_EN,
    
    a13.rtc_rc,
    
    LNASS_CART
    
    --having sum(a11.SLSMS_UNIT_CS) > 0
    
    ) sal
    
    full outer join (
    
    select a11.SLTRG_cntr_id CNTR_ID,
    
    a11.SLTRG_dmst_id dmst_store_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE Section_Gold,
    
    a12.DMAT_SEC_DES_EN SEC_DES_EN,
    
    a12.DMAT_GRP_CODE Group_Gold,
    
    a12.DMAT_GRP_DES_EN GRP_DES_EN,
    
    a12.DMAT_SGR_CODE Subgroup_Gold,
    
    a12.DMAT_SGR_DES_EN SGR_DES_EN,
    
    a13.rtc_rc,
    
    sum(a11.SLTRG_TR_TRNUM) SLTRG_TR_TRNUM,
    
    sum(a11.SLTRG_TR_UNIT) SLTRG_TR_UNIT
    
    from DW.SL_TRG a11
    
    join dm.DIM_ARTGLD_DETAILS a12 on (a11.SLTRG_cntr_id = a12.CNTR_ID and a11.SLTRG_dmat_id = a12.slad_dmat_id)
    
    join dm.rtc_rc a13 on (a11.SLTRG_cntr_id = a13.CNTR_ID)
    
    where (a12.CNTR_CODE in {country}
    
    and a12.DMAT_SEC_CODE in {sec_code}
    
    and a11.part_col between {start_date} and {end_date}
    
    and a13.rtc_rc in ('RTC')
    
    and a11.SLTRG_TYPE = 'GHS')
    
    group by a11.SLTRG_cntr_id,
    
    a11.SLTRG_dmst_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE,
    
    a12.DMAT_SEC_DES_EN,
    
    a12.DMAT_GRP_CODE,
    
    a12.DMAT_GRP_DES_EN,
    
    a12.DMAT_SGR_CODE,
    
    a12.DMAT_SGR_DES_EN,
    
    a13.rtc_rc
    
    ) trg on sal.CNTR_ID = trg.CNTR_ID and sal.dmst_store_id = trg.dmst_store_id and sal.slad_dmat_id = trg.slad_dmat_id and sal.rtc_rc = trg.rtc_rc
    
    full outer join (
    
    select a11.SLRTC_cntr_id CNTR_ID,
    
    a11.SLRTC_dmst_id dmst_store_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE Section_Gold,
    
    a12.DMAT_SEC_DES_EN SEC_DES_EN,
    
    a12.DMAT_GRP_CODE Group_Gold,
    
    a12.DMAT_GRP_DES_EN GRP_DES_EN,
    
    a12.DMAT_SGR_CODE Subgroup_Gold,
    
    a12.DMAT_SGR_DES_EN SGR_DES_EN,
    
    a11.SLRTC_RC rtc_rc,
    
    sum(a11.SLRTC_RTCUNIT) SLRTC_RTCUNIT
    
    from DW.SL_RTC a11
    
    join dm.DIM_ARTGLD_DETAILS a12 on (a11.SLRTC_cntr_id = a12.CNTR_ID and a11.SLRTC_dmat_id = a12.slad_dmat_id)
    
    where (a12.CNTR_CODE in {country}
    
    and a12.DMAT_SEC_CODE in {sec_code}
    
    and a11.part_col between {start_date} and {end_date}
    
    and a11.SLRTC_RC in ('RTC') )
    
    group by a11.SLRTC_cntr_id,
    
    a11.SLRTC_dmst_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE,
    
    a12.DMAT_SEC_DES_EN,
    
    a12.DMAT_GRP_CODE,
    
    a12.DMAT_GRP_DES_EN,
    
    a12.DMAT_SGR_CODE,
    
    a12.DMAT_SGR_DES_EN,
    
    a11.SLRTC_RC
    
    ) rtc on coalesce(sal.CNTR_ID, trg.CNTR_ID) = rtc.CNTR_ID and coalesce(sal.dmst_store_id, trg.dmst_store_id) = rtc.dmst_store_id and coalesce(sal.slad_dmat_id, trg.slad_dmat_id) = rtc.slad_dmat_id and coalesce(sal.rtc_rc, trg.rtc_rc) = rtc.rtc_rc
    
    full outer join (
    
    select coalesce(loss.CNTR_ID, corr.CNTR_ID) CNTR_ID,
    
    coalesce(loss.dmst_store_id, corr.dmst_store_id) dmst_store_id,
    
    coalesce(loss.slad_dmat_id, corr.slad_dmat_id) slad_dmat_id,
    
    coalesce(loss.slad_tpn, corr.slad_tpn) slad_tpn,
    
    coalesce(loss.slad_tpnb, corr.slad_tpnb) slad_tpnb,
    
    coalesce(loss.SLAD_LONG_DES, corr.SLAD_LONG_DES) SLAD_LONG_DES,
    
    coalesce(loss.TPNCE_ID, corr.TPNCE_ID) TPNCE_ID,
    
    coalesce(loss.DMAT_SEC_CODE, corr.DMAT_SEC_CODE) Section_Gold,
    
    coalesce(loss.DMAT_SEC_DES_EN, corr.DMAT_SEC_DES_EN) SEC_DES_EN,
    
    coalesce(loss.DMAT_GRP_CODE, corr.DMAT_GRP_CODE) Group_Gold,
    
    coalesce(loss.DMAT_GRP_DES_EN, corr.DMAT_GRP_DES_EN) GRP_DES_EN,
    
    coalesce(loss.DMAT_SGR_CODE, corr.DMAT_SGR_CODE) Subgroup_Gold,
    
    coalesce(loss.DMAT_SGR_DES_EN, corr.DMAT_SGR_DES_EN) SGR_DES_EN,
    
    coalesce(loss.rtc_rc, corr.rtc_rc) rtc_rc,
    
    nvl(loss.RC544_units,0) + nvl(corr.RC544_units,0) RC544_units,
    
    nvl(loss.RC3_units,0) + nvl(corr.RC3_units,0) RC3_units,
    
    nvl(loss.RC4_units,0) + nvl(corr.RC4_units,0) RC4_units,
    
    nvl(loss.RC14_units,0) + nvl(corr.RC14_units,0) RC14_units,
    
    nvl(loss.RC2_units,0) + nvl(corr.RC2_units,0) RC2_units,
    
    nvl(loss.RC13_units,0) + nvl(corr.RC13_units,0) RC13_units
    
    from (
    
    select a11.LSSLS_cntr_id CNTR_ID,
    
    a11.LSSLS_dmst_id dmst_store_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE,
    
    a12.DMAT_SEC_DES_EN,
    
    a12.DMAT_GRP_CODE,
    
    a12.DMAT_GRP_DES_EN,
    
    a12.DMAT_SGR_CODE,
    
    a12.DMAT_SGR_DES_EN,
    
    a13.rtc_rc,
    
    sum(Case when a11.LSSLS_RCODE = 544 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC544_units,
    
    sum(Case when a11.LSSLS_RCODE in (3, 11) then a11.LSSLS_QUANTITY_ADJ else 0 end) RC3_units,
    
    sum(Case when a11.LSSLS_RCODE = 4 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC4_units,
    
    sum(Case when a11.LSSLS_RCODE = 14 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC14_units,
    
    sum(Case when a11.LSSLS_RCODE = 2 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC2_units,
    
    sum(Case when a11.LSSLS_RCODE = 13 then a11.LSSLS_QUANTITY_ADJ else 0 end) RC13_units
    
    from dw.LS_STOCK_LOSS a11
    
    join dm.dim_time_d tim on a11.part_col = tim.dmtm_d_code
    
    join dm.DIM_ARTGLD_DETAILS a12 on (a11.LSSLS_cntr_id = a12.CNTR_ID and a11.LSSLS_dmat_id = a12.slad_dmat_id)
    
    join dm.rtc_rc a13 on (a11.LSSLS_cntr_id = a13.CNTR_ID)
    
    where (a12.CNTR_CODE in {country}
    
    and a12.DMAT_SEC_CODE in {sec_code}
    
    and a11.part_col between {start_date} and {end_date}
    
    and a13.rtc_rc in ('RTC') )
    
    group by a11.LSSLS_cntr_id,
    
    a11.LSSLS_dmst_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE,
    
    a12.DMAT_SEC_DES_EN,
    
    a12.DMAT_GRP_CODE,
    
    a12.DMAT_GRP_DES_EN,
    
    a12.DMAT_SGR_CODE,
    
    a12.DMAT_SGR_DES_EN,
    
    a13.rtc_rc ) loss
    
    full outer join (
    
    select a11.LSSLSC_cntr_id CNTR_ID,
    
    a11.LSSLSC_dmst_id dmst_store_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE,
    
    a12.DMAT_SEC_DES_EN,
    
    a12.DMAT_GRP_CODE,
    
    a12.DMAT_GRP_DES_EN,
    
    a12.DMAT_SGR_CODE,
    
    a12.DMAT_SGR_DES_EN,
    
    a13.rtc_rc,
    
    sum(Case when a11.LSSLSC_RCODE = 544 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC544_units,
    
    sum(Case when a11.LSSLSC_RCODE in (3, 11) then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC3_units,
    
    sum(Case when a11.LSSLSC_RCODE = 4 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC4_units,
    
    sum(Case when a11.LSSLSC_RCODE = 14 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC14_units,
    
    sum(Case when a11.LSSLSC_RCODE = 2 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC2_units,
    
    sum(Case when a11.LSSLSC_RCODE = 13 then a11.LSSLSC_QUANTITY_ADJ else 0 end) RC13_units
    
    from dw.LS_STOCK_LOSS_CORR a11
    
    join dm.dim_time_d tim on a11.LSSLSC_DAY = tim.dmtm_value
    
    join dm.DIM_ARTGLD_DETAILS a12 on (a11.LSSLSC_cntr_id = a12.CNTR_ID and a11.LSSLSC_dmat_id = a12.slad_dmat_id)
    
    join dm.rtc_rc a13 on (a11.LSSLSC_cntr_id = a13.CNTR_ID)
    
    where (a12.CNTR_CODE in {country}
    
    and a12.DMAT_SEC_CODE in {sec_code}
    
    and tim.dmtm_d_code in (20230601, 20230602, 20230603, 20230604)
    
    and a13.rtc_rc in ('RTC') )
    
    group by a11.LSSLSC_cntr_id,
    
    a11.LSSLSC_dmst_id,
    
    a12.slad_dmat_id,
    
    a12.slad_tpn,
    
    a12.slad_tpnb,
    
    a12.SLAD_LONG_DES,
    
    a12.TPNCE_ID,
    
    a12.DMAT_SEC_CODE,
    
    a12.DMAT_SEC_DES_EN,
    
    a12.DMAT_GRP_CODE,
    
    a12.DMAT_GRP_DES_EN,
    
    a12.DMAT_SGR_CODE,
    
    a12.DMAT_SGR_DES_EN,
    
    a13.rtc_rc
    
    ) corr on loss.CNTR_ID = corr.CNTR_ID and loss.dmst_store_id = corr.dmst_store_id and loss.slad_dmat_id = corr.slad_dmat_id and loss.rtc_rc = corr.rtc_rc
    
    ) los on coalesce(sal.CNTR_ID, trg.CNTR_ID, rtc.CNTR_ID) = los.CNTR_ID and coalesce(sal.dmst_store_id, trg.dmst_store_id, rtc.dmst_store_id) = los.dmst_store_id and coalesce(sal.slad_dmat_id, trg.slad_dmat_id, rtc.slad_dmat_id) = los.slad_dmat_id
    
    and coalesce(sal.rtc_rc, trg.rtc_rc, rtc.rtc_rc) = los.rtc_rc
    
    join dm.DIM_STORES a13 on (coalesce(sal.CNTR_ID, trg.CNTR_ID, rtc.CNTR_ID, los.CNTR_ID) = a13.CNTR_ID and coalesce(sal.dmst_store_id, trg.dmst_store_id, rtc.dmst_store_id, los.dmst_store_id) = a13.dmst_store_id)
    
    ) x
    
    where Sold_units > 0
    
    ;
    
    """.format(country = country, start_date=start_date, end_date=end_date, sec_code=sec_code)
    
    total_bakery = pd.read_sql(sql, conn)
    
total_bakery = optimize_objects(optimize_types(total_bakery))

output_path = directory / 'inputs/files_for_dataset' / output_dir /  f"bakery_inputs_{saved_name}.csv"
total_bakery.to_csv(output_path, index=False)





# total_bakery.to_parquet(directory / f'inputs/files_for_dataset/Bakery_Inputs_{saved_name}', compression='gzip')

    
print('\n###################')
print('Bakery Sold_Units Etc. Downloading done!!')
print('###################\n')
    












