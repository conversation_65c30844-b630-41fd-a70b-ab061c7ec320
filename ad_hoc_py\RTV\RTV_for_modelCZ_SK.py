import os
import pandas as pd
import warnings
import re

# Suppress warnings
warnings.filterwarnings("ignore")

def is_store_column(col):
    return bool(re.match(r'^\d{5}', str(col)))

def process_excel_files(root_dir):
    all_data = []
    
    for dirpath, dirnames, filenames in os.walk(root_dir):
        if os.path.basename(dirpath).startswith(('23', '24')):
            for filename in filenames:
                if filename.endswith('.xlsx'):
                    file_path = os.path.join(dirpath, filename)
                    
                    try:
                        xlsx = pd.ExcelFile(file_path)
                        
                        for sheet_name in xlsx.sheet_names:
                            try:
                                df_header = pd.read_excel(file_path, sheet_name=sheet_name, nrows=2, header=None)
                                
                                if not df_header.empty and df_header.shape[1] > 0:
                                    if "TPN" in df_header.iloc[0].astype(str).values:
                                        df_header = df_header.astype(str)
                                        headers = df_header.iloc[0].fillna('') + '-' + df_header.iloc[1].fillna('')
                                        headers = headers.str.strip('-')
                                        
                                        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None, skiprows=2)
                                        
                                        if len(headers) < df.shape[1]:
                                            headers = headers.tolist() + [f'Unnamed_{i}' for i in range(len(headers), df.shape[1])]
                                        elif len(headers) > df.shape[1]:
                                            headers = headers[:df.shape[1]]
                                        
                                        df.columns = headers
                                        
                                        df['Filename'] = filename
                                        df['Sheet Name'] = sheet_name
                                        
                                        # Identify store columns
                                        store_columns = [col for col in df.columns if is_store_column(col)]
                                        non_store_columns = [col for col in df.columns if col not in store_columns]
                                        
                                        # Melt the DataFrame
                                        df_melted = df.melt(id_vars=non_store_columns, 
                                                            value_vars=store_columns, 
                                                            var_name='stores', 
                                                            value_name='value')
                                        
                                        all_data.append(df_melted)
                            except Exception as e:
                                print(f"Error processing sheet {sheet_name} in file {filename}: {str(e)}")
                    
                    except Exception as e:
                        print(f"Error processing file {filename}: {str(e)}")
    
    if all_data:
        final_df = pd.concat(all_data, ignore_index=True)
        return final_df
    else:
        return pd.DataFrame()

# Set the root directory
root_dir = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\RTV\CZ_SK\TÁBLÁZATOK_1"

# Process the files and get the final DataFrame
result_df = process_excel_files(root_dir)



try:
    df = result_df[["Filename", "Sheet Name", "store",
                    "GOLD Číslo-GOLD Number","Szállító szám-Gold number",
                    "Dodavatel-Supplier", "Szállító név-Supplier", 
                    "Logistika dodania-Delivery logistic (PBL, cross dock, direct)",
                    "Tipus PBL / cross dock / direkt-Delivery logistic (PBL, cross dock, direct)",
                    "Begyűjtés időpontja (week)-Picking up timelines by NDC ",
                    "Časová osa vyzvednutí-Pick up timeline",
                    "Anglický popis-English Description", "Megnevezés-Product name","TPN-TPN","TPNB-TPNB", "value" ]].drop_duplicates()
    
    
    
    
    # Combine similar columns
    df['Combined_Gold_Number'] = df[['GOLD Číslo-GOLD Number', 'Szállító szám-Gold number']].bfill(axis=1).iloc[:, 0]
    df['Combined_Supplier'] = df[['Dodavatel-Supplier', 'Szállító név-Supplier']].bfill(axis=1).iloc[:, 0]
    df['Combined_delivery_type'] = df[['Logistika dodania-Delivery logistic (PBL, cross dock, direct)', 'Tipus PBL / cross dock / direkt-Delivery logistic (PBL, cross dock, direct)']].bfill(axis=1).iloc[:, 0]
    df['Combined_pick_up_time'] = df[['Begyűjtés időpontja (week)-Picking up timelines by NDC ', 'Časová osa vyzvednutí-Pick up timeline']].bfill(axis=1).iloc[:, 0]
    df['Combined_product_name'] = df[['Anglický popis-English Description', 'Megnevezés-Product name']].bfill(axis=1).iloc[:, 0]

    
    
    
    # Drop the original columns
    df = df.drop(columns=['GOLD Číslo-GOLD Number', 'Szállító szám-Gold number', 'Dodavatel-Supplier', 'Szállító név-Supplier',
                          'Logistika dodania-Delivery logistic (PBL, cross dock, direct)', 'Tipus PBL / cross dock / direkt-Delivery logistic (PBL, cross dock, direct)',
                          'Begyűjtés időpontja (week)-Picking up timelines by NDC ', 'Časová osa vyzvednutí-Pick up timeline',
                          'Anglický popis-English Description', 'Megnevezés-Product name'])
    
    # Rename the new columns if desired
    df = df.rename(columns={'Combined_Gold_Number': 'Gold_Number',
                            'Combined_Supplier': 'Supplier',
                            "Combined_delivery_type":"delivery_type",
                            "Combined_pick_up_time": "pick_up_time",
                            "Combined_product_name": "product_name"
                            })
    
    
    df['value'] = pd.to_numeric(df['value'], errors='coerce')

    # Optionally, you can replace NaN values with a specific value or drop them
    # Replace NaN with 0
    df['value'] = df['value'].fillna(0)
    
    df.loc[df['value'] < 0, "value"] = 0
    
    df["pick_up_time"] = df["pick_up_time"].astype(str)
    
    
    df["id"] = df["Filename"] + "_" + df['Gold_Number'].astype("str")
    
    
    df.to_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\RTV\CZ_SK\TÁBLÁZATOK_1\23w33_24w33RTV_CZ_SK_with_id", compression = "gzip")
    print(df[df.value != 0].groupby(["store", "delivery_type"], as_index = False).agg({"id":"nunique", "TPNB-TPNB" : "nunique", "value" : "sum"}))
    
except:
    df = result_df.copy()
    
    
    
# # Save the result to a new Excel file
# if not result_df.empty:
#     result_df.to_excel("compiled_data_melted.xlsx", index=False)
#     print("Data compiled, melted, and saved to 'compiled_data_melted.xlsx'")
# else:
#     print("No data found matching the criteria")





