UNCACHE TABLE IF EXISTS cal;
DROP VIEW IF EXISTS cal;
CACHE TABLE cal AS
SELECT dmtm_d_code, dtdw_day_desc_en FROM dm.dim_time_d WHERE dmtm_fw_code BETWEEN 'f2025w21'  AND 'f2025w24';

-- select count(*) from cal;
-- select * from cal limit 10;


UNCACHE TABLE IF EXISTS sal;
DROP VIEW IF EXISTS sal;
CACHE TABLE sal AS
SELECT
  cases.store,
  cases.int_cntr_id,
	cases.product,
	cal.dtdw_day_desc_en,
	SUM(cases.qty) AS cases_unit
FROM
	cal AS cal
join
	stg_go.go_106_order_receiving cases
	ON
		cal.dmtm_d_code = cases.part_col AND
		--sunit.slsms_cntr_id IN (${COUNTRY_ID_LIST})
		cases.int_cntr_id IN (1,2,4)
GROUP BY
  cases.store,
  cases.int_cntr_id,
	cases.product,
	cal.dtdw_day_desc_en;

-- select count(*) from sal;
-- select * from sal limit 10;


UNCACHE TABLE IF EXISTS hier;
DROP VIEW IF EXISTS hier;
CACHE TABLE hier AS
SELECT
	LPAD(div_code,4,"0") AS div_code,
	LPAD(dep_code,4,"0") AS dep_code,
	LPAD(sec_code,4,"0") AS sec_code,
	LPAD(grp_code,4,"0") AS grp_code,
	LPAD(sgr_code,4,"0") AS sgr_code,
	pmg
FROM tesco_analysts.hierarchy_spm
WHERE SUBSTRING(pmg, 1, 3) IN ('BWS','DAI','DRY','FRZ','HDL','HEA','PPD','PRO','SFB','SFM', 'SFP');

-- select count(*) from hier;
-- select * from hier limit 10;


DROP TABLE IF EXISTS sch_analysts.tbl_ce_daily_avg_cases_phrubos;

CREATE EXTERNAL TABLE sch_analysts.tbl_ce_daily_avg_cases_phrubos
TBLPROPERTIES('external.table.purge'='TRUE')
STORED AS ORC
LOCATION "s3a://cep-sch-analysts-db/sch_analysts_external/tbl_ce_daily_avg_cases_phrubos" AS
SELECT 
  CONCAT(cases.int_cntr_id,cases.store) as store,
      CASE 
        WHEN cases.int_cntr_id = 4 THEN 'HU'
        WHEN cases.int_cntr_id = 2 THEN 'SK'
        WHEN cases.int_cntr_id = 1 THEN 'CZ'
        ELSE NULL -- Add an else clause to handle other cases if needed
    END AS country,
  --cases.store as store,
  --cases.int_cntr_id as country_id,
	cases.dtdw_day_desc_en as day,
	hier.pmg AS pmg,
	cast(mstr.slad_tpnb as INT) AS tpnb,
--	mstr.slad_long_des AS product_name,

	cases.cases_unit/1 AS unit
FROM
	sal AS cases
JOIN
	dm.dim_artgld_details mstr
	ON
		--mstr.cntr_id IN (${COUNTRY_ID_LIST}) AND
		mstr.cntr_id IN (1,2,4) AND
		mstr.cntr_id = cases.int_cntr_id AND
		mstr.slad_tpnb = cases.product
JOIN
	hier AS hier
	ON
		mstr.dmat_div_code = hier.div_code AND
		mstr.dmat_dep_code = hier.dep_code AND
		mstr.dmat_sec_code = hier.sec_code AND
		mstr.dmat_grp_code = hier.grp_code AND
		mstr.dmat_sgr_code = hier.sgr_code

WHERE
	cases.cases_unit > 0 
ORDER BY
	hier.pmg,
	mstr.slad_tpnb;




-- select country, store, count(*) from sch_analysts.tbl_ce_daily_avg_sales_phrubos group by country, store order by country, store;