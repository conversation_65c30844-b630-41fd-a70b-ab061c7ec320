import pandas as pd
import re
import paramiko
import time
import warnings
import numpy as np
import sys
import os
from pathlib import Path
sys.path.append(os.path.dirname(Path.cwd()))

import Replenishment_Model_Functions_25 as rmf
import Get_System_Data_SQL as gsd
import pyodbc
from datetime import datetime
import json


try:
    # Try to use __file__ to get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    # Fallback to using the current working directory
    script_dir = os.getcwd()

# Construct the path to the config file one level up from the script directory or current working directory
config_path = os.path.join(script_dir, os.pardir, 'config.json')

# Load the configuration from the JSON file
with open(config_path, 'r') as file:
    config = json.load(file)

# Extract the necessary details from the configuration
hostname = config['SSH_hostname']
username = config['SSH_username']
password = config['SSH_password']
ODBC_CONN = config['ODBC_connection']


whatifs_dataset_calc_full = True
whatifs_dataset_calc_tpnb = False


time_start = time.time()

warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)

conn = pyodbc.connect(
    ODBC_CONN, autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()



def date_to_mmdd_string():
    # Get today's date
    today = datetime.now()
    
    # Format as "MMDD"
    formatted_date = today.strftime("%m%d")
    
    return formatted_date

# Get and print the formatted date
formatted_date = date_to_mmdd_string()

######### for SQL queries ##########
saved_filename = f"plano_{formatted_date}"  # it will be the dataset name

start_date = "'f2025w21'"
end_date = "'f2025w24'"
nr_weeks = int(end_date[7:9]) - int(start_date[7:9]) + 1

directory = (
    Path(__file__).parent if "__file__" in locals() else Path.cwd()
).parent.parent

place_to_save = Path(directory / "inputs/files_for_dataset")

rmf.create_a_folder(directory, f"{place_to_save}/{saved_filename}")

SRD_tables = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\JDA_SRD_Tables\11-08-2025_full\srd_db"
as_is_dataset_f = r"inputs\Repl_Dataset_25_nsrpChanged_newer_foil_benchmark"
excel_inputs_f = "inputs/Repl/Repl_Stores_Inputs_2025_Q1_v7_GBPratesUpdate (1)_Zamky&others.xlsx"
box_op_type_f = "inputs/files_for_dataset/ownbrand_opening_type_14-07-2025.xlsx"
broken_cases_f = "inputs/Dataset_24/files_for_dataset/broken_cases_list_2023w14_27.csv.gz"
catres_path = r"others\CategoryReset\Category Reset CE_hier.xlsx"

# tpnb file source
tpnb_df = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\CategoryReset\Categories\Bakery\BAKERY - SRP to calc.xlsx"



stores = list(
    pd.read_excel(directory / excel_inputs_f, usecols=["Country", "Format", "Store"])[
        "Store"
    ].unique()
)

if whatifs_dataset_calc_full:
    print("\nYou chose: 'whatifs_dataset_calc_full' version to run!!\n")
    # =============================================================================
    # SRD/JDA db downloading
    # =============================================================================
    # if SRD_tables:
    SRD_tables = pd.read_parquet(SRD_tables)
        
    # if not SRD_tables:
    #     SRD_tables = gsd.SRD_database()
    # =============================================================================
    # Downloaded only TPNB what model does not include
    # =============================================================================
    df = SRD_tables[["Country", "Product_id"]].drop_duplicates()
    df = df[pd.to_numeric(df["Product_id"], errors="coerce").notnull()].astype(
        {"Product_id": "int64"}
    )
    
    df.rename(columns={"Product_id": "tpnb", "Country": "country"}, inplace=True)
    as_is_model = pd.read_parquet(directory / as_is_dataset_f)
    check_df = as_is_model[["country", "tpnb"]].drop_duplicates()
    check_df["flag_to_check"] = 1
    
    df_match = df.merge(check_df, on=["country", "tpnb"], how="left").query(
        "flag_to_check.notnull()"
    )
    df_missing = df.merge(check_df, on=["country", "tpnb"], how="left").query(
        "flag_to_check.isnull()"
    )
    
    products = df_missing.groupby(["country"])["tpnb"].apply(lambda s: s.tolist()).to_dict()

    tpnb_list = df_missing['tpnb'].unique().tolist()
    
if whatifs_dataset_calc_tpnb:
    print("\nYou chose: 'whatifs_dataset_calc_tpnb' version to run!!\n")
    # =============================================================================
    # Input of TPNBS
    # =============================================================================
    df_missing = pd.read_excel(tpnb_df)
    df_missing.columns = [x.lower() for x in df_missing.columns]
    
    products = df_missing.groupby(["country"])["tpnb"].apply(lambda s: s.tolist()).to_dict()

    tpnb_list = df_missing['tpnb'].unique().tolist()
    
    
    # =============================================================================
    # SRP/NSRP % and "Foil calculation" info creating
    # =============================================================================
    SRD_tables = gsd.SRD_database_TPNB(products)
    



# =============================================================================
# Functions
# =============================================================================

@rmf.timeit
def ssh_table_create(what_to_create, start, end, pmg, nr_weeks, wp_working_output, saved_name):
    
    if what_to_create == "srd":
        
        success = run_srd_script(hostname, password)
        
        if success:
            flag = 0
            print("=" * 50)
            print("🎉 All done! Your SRD table is ready.")
        else:
            print("💥 Something went wrong. Check the server logs.")
            print("=" * 50)
        
    else:
    
        # Setup the connection
        try:
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh_client.connect(hostname=hostname, username="phrubos", password=password)
            print("\nConnection is done\n")
        except:
            print("\nNo connection!\n")  
        
        # Setup FTP client
        ftp_client = ssh_client.open_sftp()      
            
        ftp_client.get(f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql", wp_working_output / saved_name / f"{what_to_create}_create_table.sql")
        
        file_path = wp_working_output / saved_name /  f"{what_to_create}_create_table.sql"
        
        start = start.strip("'")
        end = end.strip("'")
        
        # parameter the SQL file
        with open(file_path, 'r') as file:
            sql_content = file.read()
    
            start_pattern = r"(?<=BETWEEN\s)'f(\d{4}w\d{2})'"
            end_pattern = r"(?<=AND\s)'f(\d{4}w\d{2})'"
            
            modified_content = re.sub(start_pattern, f"'{start}'", sql_content)
            modified_content = re.sub(end_pattern, f"'{end}'", modified_content)
            
            
            # weeks_pattern = r"/\d+\sAS"
    
            # modified_content = re.sub(weeks_pattern, f"/{nr_weeks} AS", modified_content)
            
            if pmg != None:
            
                # New pattern to replace PMG values
                pmg_pattern = r"WHERE SUBSTRING\(pmg, 1, 3\) IN \([^)]+\)"
                modified_content = re.sub(pmg_pattern, f"WHERE SUBSTRING(pmg, 1, 3) IN {pmg}", modified_content)
            
    
    
        with open(file_path, 'w') as file:
            file.write(modified_content)
            
            
        ftp_client.put(wp_working_output / saved_name / f"{what_to_create}_create_table.sql", f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql")
        
    
        
        print(f"\nSQL file Modification complete. Saved to\n{file_path}\n", )
            
            
        print(f"\nScript ({what_to_create}) is being started.....\n")
            
        stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{what_to_create}/start_q")
        
        exit_status = stdout.channel.recv_exit_status()
        
        flag = 0
        if exit_status == 0:
            print(f"\nScript ({what_to_create}) finished successfully.\n")
        else:
            print(f"Script failed with an error:\n{stderr.read().decode('utf-8')}")
            flag+=1
            
        ssh_client.close()
    
    return flag



@rmf.timeit
def ssh_table_create_tpnb(what_to_create, start, end, pmg, nr_weeks, wp_working_output, saved_name, tpnb_list):
    
    # Setup the connection
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(hostname='hdp0430.global.tesco.org', username="phrubos", password=password)
        print("\nConnection is done\n")
    except:
        print("\nNo connection!\n")  
    
    # Setup FTP client
    ftp_client = ssh_client.open_sftp()      
        
    ftp_client.get(f"/home/<USER>/{what_to_create}/{what_to_create}_create_table_tpnb.sql", wp_working_output / saved_name / f"{what_to_create}_create_table_tpnb.sql")
    
    file_path = wp_working_output / saved_name /  f"{what_to_create}_create_table_tpnb.sql"
    
    start = start.strip("'")
    end = end.strip("'")
    
    # parameter the SQL file
    with open(file_path, 'r') as file:
        sql_content = file.read()
        
        pattern = r'mstr\.slad_tpnb IN \((.*?)\)'
        
        # Find and replace the values in the SQL content
        modified_content = re.sub(pattern, f'mstr.slad_tpnb IN ({", ".join(map(str, tpnb_list))})', sql_content)
        
    
        start_pattern = r"(?<=BETWEEN\s)'f(\d{4}w\d{2})'"
        end_pattern = r"(?<=AND\s)'f(\d{4}w\d{2})'"
        
        modified_content = re.sub(start_pattern, f"'{start}'", modified_content)
        modified_content = re.sub(end_pattern, f"'{end}'", modified_content)
        
        
        weeks_pattern = r"/\d+\sAS"
    
        modified_content = re.sub(weeks_pattern, f"/{nr_weeks} AS", modified_content)
        


    with open(file_path, 'w') as file:
        file.write(modified_content)
        
        
    ftp_client.put(wp_working_output / saved_name / f"{what_to_create}_create_table_tpnb.sql", f"/home/<USER>/{what_to_create}/{what_to_create}_create_table_tpnb.sql")
    

    
    print(f"\nSQL file Modification complete. Saved to\n{file_path}\n", )
        
        
    print(f"\nScript ({what_to_create}) is being started.....\n")
        
    stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{what_to_create}/start_tpnb")
    
    exit_status = stdout.channel.recv_exit_status()
    
    flag = 0
    if exit_status == 0:
        print(f"\nScript ({what_to_create}) finished successfully.\n")
    else:
        print(f"Script failed with an error:\n{stderr.read().decode('utf-8')}")
        flag+=1
        
    ssh_client.close()
    
    return flag


@rmf.timeit
def ssh_downloader_tpnb(what_to_download: str, wp_working_output: str, saved_name: str, tpnb_dict: dict):
    

    folder_name = what_to_download
        
    # =============================================================================
    # Paths
    # =============================================================================
    
    output_folder = f"/home/<USER>/{folder_name}/output/"
    main_folder = f"/home/<USER>/{folder_name}/"
    
    # wp_working_output = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\SSH"

        
    
    def download_part(countries):    
        # =============================================================================
        # Download files
        # =============================================================================
        try:
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh_client.connect(hostname='hdp0430.global.tesco.org', username='phrubos', password=password)
            print("\nConnection is done\n")
        except:
            print("\nNo connection!\n")
            
        
            
        ftp_client = ssh_client.open_sftp()
        
        if what_to_download == 'losses':
            ftp_client.put(wp_working_output / saved_name / "select.sql", main_folder + "select.sql")

        for country in countries:
            
            print(f"Start to process of {what_to_download} ({country})")
            
            ftp_client.get(main_folder + f"parameters_all_groups_{country}.csv", wp_working_output / saved_name / f"parameters_all_groups_{country}.csv")
            time.sleep(5)
            csv_id = pd.read_csv(wp_working_output / saved_name / f"parameters_all_groups_{country}.csv", sep="|", names=['country','id', 'stores'])['id'].count()
            
            if what_to_download in ['losses', 'item_sold_dotcom', 'item_sold', 'cases', 'stock']:
                csv_id = csv_id-1
            
            print(f"\nThere will be {csv_id} rounds in {country}!\n")
            
            del_files = f'rm -f {main_folder}output/*'
            stdin, stdout, stderr = ssh_client.exec_command(del_files)
            
            stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}")
            
    
            
            # print(stdout.readlines())
            time.sleep(5)
            
            counter = 0
            while counter <= csv_id:
            
        
                file_list = ftp_client.listdir(output_folder)
                
                try:
                    output = [i for i in file_list if  i.__contains__(".zip")][0]
                except:
                    output = []
                    pass
                
                if output.__contains__("zip"):
                    
                    print(f"\n{output} done!\n")
                    counter += 1
                    ftp_client.get(output_folder + output, wp_working_output / saved_name / output)
                    print("Copied onto local computer\n")
                    time.sleep(15)
                    ftp_client.remove(output_folder + output)
                    
        
                    
                else:
                    print("Downloading.....")
                    time.sleep(10)
                    
                if counter == csv_id:
                    
                    break
                    time.sleep(15)
            
        ftp_client.close()
        ssh_client.close()
    
    for k, v in tpnb_dict.items():
        download_part([k])
    
    print("\nData are downloaded! Check if it is needed to re-download again...")
    
    
    check_to_redownload=[]
    for root, dirs, files in os.walk(wp_working_output / saved_name):
        for file in files:
            file_path = os.path.join(root, file)
            if file.endswith('.zip') and os.path.getsize(file_path) < 400 and "dotcom" not in file:  # Check for 1KB size
                check_to_redownload.append(file)
                
    found_elements = set()           
    for element in check_to_redownload:
        for sub_element in  ['HU', 'SK', 'CZ']:
            if sub_element in element:
                found_elements.add(sub_element)
            
    if found_elements:
                
        print("\nThis country needs to download again!:", found_elements)
        download_part(list(found_elements))   

    if not found_elements:
        
        print("\nNo need to re-download any files!!")
    

    
    csv_files = [f for f in os.listdir(wp_working_output/ saved_name) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]
    
    if what_to_download == "losses":
        ce_df = pd.concat([pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',', skiprows=6, skipfooter=1) for f in csv_files])        

        
    if not what_to_download == "losses":
        ce_df = pd.concat([pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',') for f in csv_files])
        
    # saving the file
    ce_df.to_parquet(wp_working_output / saved_name / f"{what_to_download}_{saved_name}", compression="gzip")
        
    # removing the zip files
    files = [f for f in os.listdir(
        wp_working_output / saved_name
        ) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]
    
    for c in files:
        os.remove(
            os.path.join(
                wp_working_output / saved_name, f"{c}"
            )
        )
    return ce_df




@rmf.timeit
def pallet_capacity_creator(products):
    
   pallet_cap_df = pd.DataFrame()
   print("\n###########")
   print("Pallet Capacity Downloading for new Products")
   print("###########\n")
   for k, v in products.items():

       s = list()
       for x in v:

           s.append(str(x))

       tpnbs = tuple(s)

       pallet_cap = """
               SELECT CAST(table1.tpnb AS INT) AS tpnb,
               table1.pmg AS pmg,
               table1.supplier_id AS supplier_id,
               table1.case_size AS case_size,
               table1.case_type AS case_type,
               table1.country AS country,
               table1.pallet_capacity AS pallet_capacity,
               MAX(year) AS year
               FROM
               (SELECT 
                tpns.slad_tpnb AS tpnb,
                hier.pmg AS pmg,
                pc.lnass_dmsup_id AS supplier_id, 
                pc.lnass_case_size AS case_size,
                pc.lnass_pur_unit AS case_type, 
                pc.lnass_no_cart_p AS pallet_capacity,
               tpns.cntr_code AS country,
               max(date_format(pc.lnass_upd_date,'yyyy')) AS year
               FROM syln{k}.ln_art_sup_st pc
               LEFT JOIN dm.dim_artgld_details tpns ON pc.lnass_dmat_id = tpns.slad_dmat_id
               LEFT JOIN tesco_analysts.hierarchy_spm hier
               ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0")
               AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0")
               AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0")
               AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0")
               AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
               WHERE pc.lnass_no_cart_p > 1
               AND tpns.cntr_code = '{k}'
               AND tpns.slad_tpnb in {tpnbs}
               GROUP BY tpns.slad_tpnb, hier.pmg, pc.lnass_dmsup_id, pc.lnass_case_size, pc.lnass_pur_unit, pc.lnass_no_cart_p, tpns.cntr_code) table1
               WHERE table1.year IS NOT NULL
               AND table1.pmg IS NOT NULL
               GROUP BY table1.tpnb, table1.pmg, table1.supplier_id, table1.case_size, table1.case_type, table1.country, table1.pallet_capacity
               ORDER BY table1.country, table1.pmg, table1.tpnb, table1.pallet_capacity
               
               
               
           
               """.format(
           k=k, tpnbs=tpnbs
       )

       pallet = pd.read_sql(pallet_cap, conn)
       pallet_cap_df = pd.concat([pallet_cap_df, pallet])
       pallet_cap_df = (
           pallet_cap_df.groupby(["country", "tpnb"])["pallet_capacity"]
           .mean()
           .reset_index()
       )

       print(f"\nPallet Capacity Done with {k}\n")
   return pallet_cap_df



def Search_most_frequently_category_for_no_data_pmg(df):
    # Create a dictionary to store pmg values and their corresponding most frequent Category name
    pmg_categories = {}

    # Iterate over unique pmg values
    for pmg_value in df['pmg'].unique():
        # Filter rows where pmg is equal to current pmg_value and Category name is not "no_data" and pmg is not containing HDL
        filtered_rows = df[(df['pmg'] == pmg_value) & (df['Category name'] != 'no_data') & (df['pmg'].str[:3] != 'HDL')][['country','pmg','tpnb', 'Category name']].drop_duplicates()
        
        # Check if there are any non-"no_data" rows matching the conditions
        if not filtered_rows.empty:
            # Find the most frequent non-"no_data" Category name for the current pmg_value
            most_frequent_category = filtered_rows['Category name'].mode().iloc[0]
            
            # Store the most frequent non-"no_data" Category name for the current pmg_value in the dictionary
            pmg_categories[pmg_value] = most_frequent_category
                
    # Update the 'no_data' rows in the DataFrame based on the mapping
    
    
    mask = df['Category name'] == 'no_data'
    df.loc[mask, 'Category name'] = df.loc[mask, 'pmg'].map(pmg_categories)
    # df['Category name'] = df.apply(lambda row: pmg_categories.get(row['pmg'], row['Category name']) if row['Category name'] == 'no_data' else row['Category name'], axis=1)
    df['Category name'] = np.where(df.pmg.str[:3] == 'FRZ', 'FROZEN', df['Category name'] )
    df['Category name'] = np.where((df['Category name'].isnull())&(df['Category name'] == 'no_data')&(df.pmg.str[:3] == 'HDL'), 'GM', df['Category name'] )
    return df
 

# =============================================================================
# SSH downloading ('item_sold', 'item_sold_dotcom', 'stock', 'cases')
# =============================================================================
for what_to_create in ['item_sold', 'item_sold_dotcom', 'stock', 'cases']: #'item_sold', 'item_sold_dotcom', 'stock', 

    sql = ssh_table_create_tpnb(what_to_create, start_date, end_date, None, nr_weeks, place_to_save, saved_filename, tpnb_list)        
    if sql ==0:
        ssh_downloader_tpnb(what_to_create, place_to_save, saved_filename, products)
        
# =============================================================================
# Pallet Capacity downloading
# =============================================================================
pallet_cap_df = pallet_capacity_creator(products)
# =============================================================================
# Store related Infos gathering
# =============================================================================
store_inputs = rmf.Store_Inputs_Creator(directory, excel_inputs_f, stores)
# =============================================================================
# SRP/NSRP % and "Foil calculation" info creating
# =============================================================================
opsdev_df, foil_df = gsd.SRD_to_opsdev(SRD_tables, directory, excel_inputs_f)
# Hierarchy and product names downloading
gsd.hierarchy_to_sold_item(directory/f"{place_to_save}/{saved_filename}/item_sold_{saved_filename}", f"{place_to_save}/{saved_filename}/cases_{saved_filename}", conn)
# ShelfCapacity downloading
def find_latest_single_pick_list(directory):
    # Define the regex pattern to match the filename with date
    pattern = r"Single_pick_list_(\d{4}\.\d{2}\.\d{2}).*\.xlsx"
    
    # Initialize a variable to store the latest file details
    latest_file = None
    latest_date = None
    
    # Iterate over the files in the specified directory
    for filename in os.listdir(r"\\euprgvmfs01\GMforCentralEurope\Supply Chain\Single Picking"):
        match = re.match(pattern, filename)
        if match:
            # Extract the date from the filename
            file_date = datetime.strptime(match.group(1), "%Y.%m.%d")
            # Check if this is the latest date we've found so far
            if latest_date is None or file_date > latest_date:
                latest_date = file_date
                latest_file = filename
            
            return latest_file   
def get_today_as_integer():
    today = datetime.now().date()
    today_integer = int(today.strftime('%Y%m%d'))
    return today_integer

date_plan = get_today_as_integer()
try:
    shelfcap_df = gsd.planogram_compiling(date_plan, saved_filename, place_to_save, False)
except:
    shelfcap_df = opsdev_df[['store','tpnb','shelfCapacity', 'icase']].drop_duplicates()
    shelfcap_df.rename(columns={'shelfCapacity' : "capacity"}, inplace=True)
# =============================================================================
# Creating Dataset with missing tpnb-s
# =============================================================================
items_sold_f = f"{place_to_save}/{saved_filename}/item_sold_{saved_filename}_hier"
items_sold_dotcom = f"{place_to_save}/{saved_filename}/item_sold_dotcom_{saved_filename}"
stock_f = f"{place_to_save}/{saved_filename}/stock_{saved_filename}"
cases_f = f"{place_to_save}/{saved_filename}/cases_{saved_filename}"
pallet_capacity_f = None
ops_dev_f = None
foil_f = None
broken_case = False
broken_cases_f = r"inputs\files_for_dataset\Dataset_25\broken_cases_list_2023w14_27.csv.gz"
single_pick_f_latest = find_latest_single_pick_list(r"\\euprgvmfs01\GMforCentralEurope\Supply Chain\Single Picking")
single_pick_f = r"\\euprgvmfs01\GMforCentralEurope\Supply Chain\Single Picking\\" +  single_pick_f_latest
box_op_type_f = r"inputs\files_for_dataset\ownbrand_opening_type_20250306.xlsx"



repl_dataset = rmf.Repl_DataSet_Creator(
    store_inputs,
    directory,
    stock_f,
    ops_dev_f,
    items_sold_f,
    items_sold_dotcom,
    cases_f,
    box_op_type_f,
    pallet_capacity_f,
    broken_case,
    broken_cases_f,
    single_pick_f,
    foil_f,
    None,
    opsdev_df,
    pallet_cap_df,
    foil_df,
    shelfcap_df).to_pandas()
    
# # Save directly to parquet instead of converting to pandas
# output_path = directory / f"{place_to_save}/{saved_filename}/final_dataset_{saved_filename}.parquet"
# repl_dataset.write_parquet(output_path, compression="gzip")
# print(f"Dataset saved to: {output_path}")




if whatifs_dataset_calc_full:
    # =============================================================================
    # Filtering datasets then concating them together
    # =============================================================================
    products_match = (
        df_match.groupby(["country"])["tpnb"].apply(lambda s: s.tolist()).to_dict()
    )
    as_is_model_based_on_SRD_table = pd.DataFrame()
    
    for k, v in products_match.items():
        a = as_is_model.query("country == @k and tpnb.isin(@v)")
        as_is_model_based_on_SRD_table = pd.concat([as_is_model_based_on_SRD_table, a])

    as_is_model_based_on_SRD_table["as_is_model_contains?"] = "Y"
    
repl_dataset["as_is_model_contains?"] = "N"
repl_dataset['shelfservice_flag'] = 0

if whatifs_dataset_calc_full:
    repl_dataset = repl_dataset[repl_dataset.country.notna()]
    # repl_dataset.drop('capacity', axis=1, inplace=True)
    
    repl_dataset_df = pd.DataFrame()
    for k, v in products_match.items():
        a = repl_dataset[(repl_dataset.country == k) & (~repl_dataset.tpnb.isin(v))]
        repl_dataset_df = pd.concat([repl_dataset_df, a])
    
    # =============================================================================
    # Switching all the products SRP/NSRP% based on newest planogram db
    # =============================================================================
    opsdev_df.drop(
        ["checkout_stand_flag", "clipstrip_flag", "backroom_flag", "shelfCapacity", 'icase'], axis=1, inplace=True
    )
    
    opsdev_df.columns = (opsdev_df.iloc[:, :3].columns.tolist()
                      + opsdev_df.iloc[:, 3:].add_suffix("_new").columns.tolist())
    
    
    new_model_dataset_based_on_SRD = pd.concat(
        [as_is_model_based_on_SRD_table, repl_dataset_df]
    )
    
    
    
    new_model_dataset_based_on_SRD = new_model_dataset_based_on_SRD.merge(opsdev_df, on=['store','country', 'tpnb'], how='left')
    
    for x, y in zip(["srp_new", "nsrp_new", "mu_new", "full_pallet_new", "split_pallet_new", "icream_nsrp_new", ], ['srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', "icream_nsrp"]):
        cond = [new_model_dataset_based_on_SRD[x].notnull()]
        result = [new_model_dataset_based_on_SRD[x]]
        new_model_dataset_based_on_SRD[y] = np.select(cond, result, new_model_dataset_based_on_SRD[y])
        new_model_dataset_based_on_SRD.drop(x, axis=1, inplace=True)
    
    new_model_dataset_based_on_SRD = new_model_dataset_based_on_SRD[new_model_dataset_based_on_SRD.format.notnull()]
    
    print(f"\nDuplicated rows of Dataset: {new_model_dataset_based_on_SRD[new_model_dataset_based_on_SRD[['store', 'tpnb','day']].duplicated() == True].shape[0]}\n")

# =============================================================================
# Category Reset "categories" downloading and matching them
# =============================================================================
catres_df = gsd.category_reset_to_df(directory, catres_path)
catres_df[['tpnb', 'tpn']] = catres_df[['tpnb','tpn']].astype("int64")

if whatifs_dataset_calc_full:
    try:
        new_model_dataset_based_on_SRD.drop("Category name", axis=1, inplace=True)
    except:
        pass
    c = c.merge(catres_df[['country','tpnb', 'Category name']].drop_duplicates(), on=['country','tpnb'], how='left')
    c.loc[c['Category name'].isnull(), 'Category name'] = 'no_data'
    c = c[c.tpnb != 0]
    print("Search_most_frequently_category_for_no_data_pmg\n")
    c = Search_most_frequently_category_for_no_data_pmg(c)

if whatifs_dataset_calc_tpnb:
    repl_dataset = repl_dataset.merge(catres_df[['country','tpnb', 'Category name']].drop_duplicates(), on=['country','tpnb'], how='left')
    repl_dataset.loc[repl_dataset['Category name'].isnull(), 'Category name'] = 'no_data'
    repl_dataset = repl_dataset[repl_dataset.tpnb != 0]
    repl_dataset = Search_most_frequently_category_for_no_data_pmg(repl_dataset)

# =============================================================================
# Saving the new Dataset
# =============================================================================
print(
    f"\n\nSaving the file: Dataset_based_on_plano_{saved_filename} into the folder....."
)

if whatifs_dataset_calc_full:
    
    new_model_dataset_based_on_SRD['tpn'] = new_model_dataset_based_on_SRD['tpn'].astype(str)
    
    rmf.optimize_objects(rmf.optimize_types(new_model_dataset_based_on_SRD)).to_parquet(
        place_to_save/ f"{saved_filename}/Dataset_based_on_{saved_filename}",
        compression="gzip",
    )
    
    # rmf.optimize_objects(rmf.optimize_types(new_model_dataset_based_on_SRD)).to_parquet(
    # fr"\\huprgvmfs07\TBS_Enterprise_Analytics\SRP_NSRP_dataset\Dataset_based_on_{saved_filename}",compression="gzip")
    
if whatifs_dataset_calc_tpnb:
    rmf.optimize_objects(rmf.optimize_types(repl_dataset)).to_parquet(
        place_to_save/ f"{saved_filename}/Dataset_based_on_TPNB_{saved_filename}",
        compression="gzip",
    )
    

time_stop = time.time()
print("\n###########")
print(
    "New products and add to model are done - Executed Time: (min): {:,.1f} ".format(
        (time_stop - time_start)/60
    )
)
print("###########\n")








import pandas as pd
import paramiko
import os

# === Settings ===
sftp_host = 'tpc84599bg1112.tgrc.tesco.org'
sftp_user = 'ftpuser'
sftp_pass = 'DhG62kFJxq0'
remote_path = 'upload/'  # Must end with /
# local_parquet = fr"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\{saved_filename}\Dataset_based_on_{saved_filename}_"

local_parquet = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\plano_0812\Dataset_based_on_plano_0813"
sftp_port = 22

# === Upload via FTP ===
def upload_via_sftp(local_file, remote_dir, host, port, username, password):
    transport = paramiko.Transport((host, port))
    transport.connect(username=username, password=password)
    
    sftp = paramiko.SFTPClient.from_transport(transport)
    remote_filename = os.path.basename(local_file)
    remote_file_path = os.path.join(remote_dir, remote_filename).replace("\\", "/")  # Always use POSIX path
    
    sftp.put(local_file, remote_file_path)  # Uploads without temp file

    sftp.close()
    transport.close()

upload_via_sftp(local_parquet, remote_path, sftp_host, sftp_port, sftp_user, sftp_pass)


