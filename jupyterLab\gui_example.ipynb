{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["{\n", " \"cells\": [\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": 1,\n", "   \"id\": \"a4da4046\",\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"from taipy.gui import <PERSON><PERSON>, <PERSON><PERSON>\\n\",\n", "    \"\\n\",\n", "    \"page=Markdown(\\\"\\\"\\\"\\n\",\n", "    \"# Taipy in a Notebook\\n\",\n", "    \"\\n\",\n", "    \"Value: <|{value}|>\\n\",\n", "    \"\\n\",\n", "    \"Set: <|{value}|slider|>\\n\",\n", "    \"\\\"\\\"\\\")\\n\",\n", "    \"\\n\",\n", "    \"value = 10\\n\",\n", "    \"\\n\",\n", "    \"gui=Gui(page)\\n\",\n", "    \"gui.run()\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": 2,\n", "   \"id\": \"17c6cfb8\",\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"import math\\n\",\n", "    \"\\n\",\n", "    \"xrange= range(0, 720)\\n\",\n", "    \"\\n\",\n", "    \"def compute_data(a):\\n\",\n", "    \"    return [   a    * math.cos(2 * math.pi * x / 100) +\\n\",\n", "    \"            (100-a) * math.sin(2 * math.pi * x / 50)\\n\",\n", "    \"            for x in xrange]\\n\",\n", "    \"\\n\",\n", "    \"data = compute_data(value)\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": 3,\n", "   \"id\": \"dbdeade6\",\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"page.set_content(\\\"\\\"\\\"\\n\",\n", "    \"# Taipy in a Notebook\\n\",\n", "    \"\\n\",\n", "    \"Value: <|{value}|>\\n\",\n", "    \"\\n\",\n", "    \"Set: <|{value}|slider|>\\n\",\n", "    \"\\n\",\n", "    \"<|{data}|chart|>\\n\",\n", "    \"\\\"\\\"\\\")\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": 4,\n", "   \"id\": \"99513315\",\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"def on_change(state, var_name, var_value):\\n\",\n", "    \"  if var_name == \\\"value\\\":\\n\",\n", "    \"    state.data = compute_data(state.value)\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": 5,\n", "   \"id\": \"c9e64ca4\",\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"gui.stop()\\n\",\n", "    \"gui.run()\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"id\": \"98ce7ad9\",\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": []\n", "  }\n", " ],\n", " \"metadata\": {\n", "  \"kernelspec\": {\n", "   \"display_name\": \"Python 3 (ipykernel)\",\n", "   \"language\": \"python\",\n", "   \"name\": \"python3\"\n", "  },\n", "  \"language_info\": {\n", "   \"codemirror_mode\": {\n", "    \"name\": \"i<PERSON>thon\",\n", "    \"version\": 3\n", "   },\n", "   \"file_extension\": \".py\",\n", "   \"mimetype\": \"text/x-python\",\n", "   \"name\": \"python\",\n", "   \"nbconvert_exporter\": \"python\",\n", "   \"pygments_lexer\": \"ipython3\",\n", "   \"version\": \"3.9.5\"\n", "  }\n", " },\n", " \"nbformat\": 4,\n", " \"nbformat_minor\": 5\n", "}\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}