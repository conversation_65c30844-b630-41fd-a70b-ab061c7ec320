import duckdb
import pandas as pd
import numpy as np


db_path = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\CategoryReset\repl_type_dashboard.duckdb"
tpn_df_branded = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\CategoryReset\df_tpn_no_wheiting_branded"
tpn_df_ownbrand = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\CategoryReset\df_tpn_no_wheiting_ownbrand"
shelfcap_df = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\CategoryReset\df_shelfcap_wheighting"
sales_df = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\CategoryReset\df_sales_wheighting"

create_db = False
new_period_add = False


# if create_db:
#     # DataFramek mentése DuckDB-be
#     with duckdb.connect(db_place) as conn:
#         conn.execute(f""" CREATE TABLE tpn_b AS SELECT * EXCLUDE "__index_level_0__" FROM parquet_scan('{tpn_df_branded}')""")
#         conn.execute(f""" CREATE TABLE tpn_o AS  SELECT * EXCLUDE "__index_level_0__" FROM parquet_scan('{tpn_df_ownbrand}') """)
#         conn.execute(f"""  CREATE TABLE shelfcap AS  SELECT * EXCLUDE "__index_level_0__"  FROM parquet_scan('{shelfcap_df}') """)
#         conn.execute(f""" CREATE TABLE sales AS  SELECT * EXCLUDE "__index_level_0__"  FROM parquet_scan('{sales_df}')""")

        # print("Eredeti table1:", conn.execute("SELECT * FROM table1").df())

# # Később: új adatok hozzáadása table1-hez
# new_data = pd.DataFrame({'id': [4, 5], 'name': ['Dániel', 'Emma']})



# with duckdb.connect(db_place) as conn:
#     conn.execute("INSERT INTO table1 SELECT * FROM new_data")
#     print("\nFrissített table1:", conn.execute("SELECT * FROM table1").df())
    
    
#     z = conn.execute("SELECT * FROM table1").df()




def load_data(df):
    
    # with duckdb.connect(db_place) as conn:
    #     df_ce = conn.execute(f"SELECT * FROM {table}").df()
    
    
    df_ce = pd.read_parquet(df)
        
    
    from_last_year = []

    # Define the desired order of periods
    desired_order = ['LY_p11', 'LY_p12', 'p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']

    repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]

        
    need_cols = [x for x in df_ce.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]  

    # =============================================================================
    # Charts
    # =============================================================================
    df_catres_sum = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'division', 'pmg', 'tpnb', 'product_name', 'category', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()

    non_food_food = df_ce[df_ce['DIV_DESC'].isin(['NonFood Grocery', 'Food Grocery'])]
    non_food_food['division'] = np.where(non_food_food['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)', 'Grocery (Food)')


    df_catres_sum_div = df_ce.copy()

    df_catres_sum_div = pd.concat([df_catres_sum_div, non_food_food])
    df_catres_sum_div['division'] = np.where(df_catres_sum_div['division'] == 'Grocery', 'Grocery (total)', df_catres_sum_div['division'])

    df_catres_sum_div['division'] = np.where((df_catres_sum_div['division'] == 'Prepacked Meat') |
                                              (df_catres_sum_div['division'] == 'Prepacked Poultry'),
                                          'Fresh',
                                          df_catres_sum_div['division'])

    df_catres_sum_div = df_catres_sum_div[df_catres_sum_div['division'] != 'Prepacked Bakery']

    df_catres_sum_div = df_catres_sum_div.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'division', 'pmg', 'tpnb', 'product_name', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()



    cols_need = [x for x in df_catres_sum.columns if x not in [1] + df_catres_sum.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]

    cols_need_div = [x for x in df_catres_sum_div.columns if x not in [1] + df_catres_sum_div.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]

    df_catres_sum_category = df_catres_sum.melt(id_vars=cols_need, var_name='period' ).groupby(['country', 'category', 'repl_types','period'], observed=True)['value'].sum().reset_index()
    df_catres_sum_category['value_%'] = df_catres_sum_category['value'] / df_catres_sum_category.groupby(['country', 'category', 'period'], observed=True)['value'].transform('sum')

    df_catres_sum_division = df_catres_sum_div.melt(id_vars=cols_need_div, var_name='period' ).groupby(['country', 'division', 'repl_types','period'], observed=True)['value'].sum().reset_index()
    df_catres_sum_division['value_%'] = df_catres_sum_division['value'] / df_catres_sum_division.groupby(['country', 'division', 'period'],observed=True)['value'].transform('sum')

    # df_total = df_catres_sum_division[(df_catres_sum_division.country == 'CE') & (~df_catres_sum_division['division'].isin(['GM', 'Produce']))].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
    df_total = df_catres_sum_division[(~df_catres_sum_division['division'].isin(['GM', 'Produce', 'Bakery']))].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()

    df_total['value_%'] = df_total['value'] / df_total.groupby(['country', 'period'],observed=True)['value'].transform('sum')

    df_catres_sum_category['category'] = df_catres_sum_category['category'].apply(lambda x: x.replace(";"," "))



    def rename_period_columns(df, prefix, from_last_year:list):
        df['period'] = df['period'].astype(str)  
        df['period'] = df.apply(lambda x: "LY_"+ prefix +x['period'] if x['period'] in [str(y) for y in from_last_year] else prefix + x['period'], axis = 1)    


    for x in [df_total, df_catres_sum_category, df_catres_sum_division ]:
        rename_period_columns(x, 'p', from_last_year)

        # Reorder the dataframe
        x['period'] = pd.Categorical(x['period'], categories=desired_order, ordered=True)
        x.sort_values(by=['country', 'repl_types','period'], ascending=[True,True,True],inplace=True)  
    

        
    return {
        'total': df_total,
        'category': df_catres_sum_category,
        'division': df_catres_sum_division
    }



dict_sales_df = load_data(sales_df)



def store_dataframes(df_dict, db_path, schema_name):
    """
    Store all three dataframes in DuckDB under one schema
    """
    with duckdb.connect(db_path) as conn:
        # Create schema
        conn.execute(f"CREATE SCHEMA IF NOT EXISTS {schema_name}")
        
        # Store each dataframe using FROM clause with df reference
        for table_name, df in df_dict.items():
            conn.execute(f"CREATE OR REPLACE TABLE {schema_name}.{table_name} AS SELECT * FROM df")


store_dataframes(dict_sales_df, db_path, "sales")




def load_dataframes(db_path, schema_name):
    """
    Load all dataframes back from DuckDB
    """
    with duckdb.connect(db_path) as conn:
        df_dict = {}
        for table_name in ['total', 'category', 'division']:
            df_dict[table_name] = conn.execute(f"SELECT * FROM {schema_name}.{table_name}").df()
            
        # Restore the categorical period column
        desired_order = ['LY_p11', 'LY_p12', 'p1', 'p2', 'p3', 'p4', 'p5', 
                        'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']
        
        for df in df_dict.values():
            df['period'] = pd.Categorical(df['period'], 
                                        categories=desired_order, 
                                        ordered=True)
            
    return df_dict




loaded_dfs = load_dataframes(db_path, "sales")



df_total = loaded_dfs['total']