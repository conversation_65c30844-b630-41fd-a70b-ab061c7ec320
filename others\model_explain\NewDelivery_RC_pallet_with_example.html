<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Calculations Explanation for Lay's BBQ Chips</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background-color: #f5f7fa;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        .card {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.08);
            padding: 25px;
            margin-bottom: 25px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.12);
        }
        .formula-box {
            background-color: #f0f4f8;
            border-left: 5px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 16px;
            overflow-x: auto;
        }
        .code-box {
            background-color: #2c3e50;
            color: #302f2f;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 16px;
            overflow-x: auto;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            border-radius: 8px;
            overflow: hidden;
        }
        .data-table th, .data-table td {
            border: 1px solid #e6e6e6;
            padding: 15px;
            text-align: left;
        }
        .data-table th {
            background-color: #3498db;
            color: white;
            font-weight: 600;
        }
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .data-table tr:hover {
            background-color: #f0f4f8;
        }
        .example-calculation {
            background-color: #e8f5e9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            border-left: 5px solid #4CAF50;
        }
        .variable-definition {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 25px 0;
        }
        .variable-card {
            flex: 1;
            min-width: 250px;
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.08);
            border-top: 5px solid #2196F3;
            transition: transform 0.2s ease;
        }
        .variable-card:hover {
            transform: translateY(-3px);
        }
        .step-by-step {
            counter-reset: step;
            margin: 30px 0;
        }
        .step {
            counter-increment: step;
            margin-bottom: 25px;
            position: relative;
            padding: 20px 20px 20px 70px;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.05);
        }
        .step:before {
            content: counter(step);
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background-color: #3498db;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            padding-bottom: 15px;
        }
        h1:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background-color: #3498db;
            border-radius: 2px;
        }
        h2 {
            font-size: 1.8rem;
            margin-top: 20px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #f0f4f8;
        }
        h3 {
            font-size: 1.4rem;
            margin-top: 25px;
        }
        .highlight {
            background-color: #fffde7;
            padding: 3px 6px;
            border-radius: 4px;
            font-weight: 500;
        }
        .alert {
            background-color: #fff3e0;
            color: #e65100;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 5px solid #ff9800;
        }
        .note {
            background-color: #e1f5fe;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 5px solid #03a9f4;
        }
        .formula-visual {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .formula-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.08);
            min-width: 150px;
        }
        .arrow {
            font-size: 24px;
            color: #7f8c8d;
            align-self: center;
            padding: 0 10px;
        }
        .delivery-bar {
            height: 40px;
            margin: 5px 0;
            position: relative;
            border-radius: 4px;
            overflow: hidden;
        }
        .delivery-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: pointer;
            border-bottom: 1px dotted #3498db;
            color: #3498db;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        .icon {
            font-size: 36px;
            margin-bottom: 10px;
        }
        .comparison-container {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }
        .comparison-item {
            flex: 1;
            min-width: 250px;
            max-width: 400px;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        .comparison-icon {
            font-size: 60px;
            margin-bottom: 15px;
        }
        .progress-bar {
            background-color: #e0e0e0;
            border-radius: 10px;
            height: 20px;
            width: 100%;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            display: block;
            position: relative;
            overflow: hidden;
        }
        .color-box {
            width: 15px;
            height: 15px;
            display: inline-block;
            margin-right: 5px;
            border-radius: 3px;
        }
        .color-legend {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
        }
        .visual-element {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
        }
        .pallet-visual {
            width: 250px;
            height: 180px;
            position: relative;
            margin: 0 auto;
        }
        .pallet-base {
            width: 200px;
            height: 30px;
            background-color: #8B4513;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
        }
        .pallet-middle {
            width: 180px;
            height: 10px;
            background-color: #A0522D;
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
        }
        .pallet-top {
            width: 200px;
            height: 30px;
            background-color: #8B4513;
            position: absolute;
            bottom: 60px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
        }
        .pallet-boxes {
            width: 180px;
            height: 90px;
            background-color: #D2B48C;
            position: absolute;
            bottom: 90px;
            left: 50%;
            transform: translateX(-50%);
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 5px;
            padding: 5px;
        }
        .pallet-box {
            background-color: #CD853F;
            border: 1px solid #8B4513;
        }
        
        .rollcage-visual {
            width: 250px;
            height: 200px;
            position: relative;
            margin: 0 auto;
        }
        .rollcage-base {
            width: 180px;
            height: 10px;
            background-color: #696969;
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
        }
        .rollcage-wheel {
            width: 20px;
            height: 20px;
            background-color: #2F4F4F;
            border-radius: 50%;
            position: absolute;
            bottom: 0;
        }
        .rollcage-wheel-1 {
            left: 35px;
        }
        .rollcage-wheel-2 {
            left: 165px;
        }
        .rollcage-wheel-3 {
            left: 65px;
        }
        .rollcage-wheel-4 {
            left: 195px;
        }
        .rollcage-side {
            width: 10px;
            height: 160px;
            background-color: #708090;
            position: absolute;
            bottom: 30px;
        }
        .rollcage-side-1 {
            left: 45px;
        }
        .rollcage-side-2 {
            left: 195px;
        }
        .rollcage-side-3 {
            left: 45px;
            height: 10px;
            width: 150px;
            bottom: 180px;
        }
        .rollcage-side-4 {
            left: 45px;
            height: 10px;
            width: 150px;
            bottom: 120px;
        }
        .rollcage-side-5 {
            left: 45px;
            height: 10px;
            width: 150px;
            bottom: 70px;
        }
        .rollcage-boxes {
            width: 140px;
            height: 100px;
            position: absolute;
            bottom: 40px;
            left: 55px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 5px;
        }
        .rollcage-box {
            background-color: #B0C4DE;
            border: 1px solid #4682B4;
        }
        
        .flow-diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 30px 0;
        }
        .flow-step {
            width: 80%;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            position: relative;
        }
        .flow-step:after {
            content: '↓';
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            color: #7f8c8d;
        }
        .flow-step:last-child:after {
            content: '';
        }
        .calculation-result {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 5px;
            margin: 0 5px;
        }
        .pallet-result {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        .rollcage-result {
            background-color: #fff8e1;
            color: #f57f17;
        }
        .product-highlight {
            background-color: #ffe0b2;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 2px dashed #ff9800;
            text-align: center;
        }
        .product-image {
            width: 150px;
            height: 150px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            border-radius: 50%;
            margin-bottom: 15px;
        }
        .product-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #e65100;
        }
        .chip-bag {
            width: 100px;
            height: 140px;
            background: #d32f2f;
            border-radius: 10px;
            position: relative;
            margin: 0 auto;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .chip-bag:before {
            content: "LAY'S";
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-weight: bold;
            font-size: 18px;
        }
        .chip-bag:after {
            content: "BBQ";
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        .chip-logo {
            width: 40px;
            height: 40px;
            background: yellow;
            border-radius: 50%;
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
        }
        .day-breakdown {
            margin: 30px 0;
        }
        .day-card {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.08);
        }
        .day-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        .day-name {
            font-weight: bold;
            font-size: 1.2em;
        }
        .day-data {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .data-point {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            flex: 1;
            min-width: 150px;
            text-align: center;
        }
        .data-point-label {
            font-size: 0.9em;
            color: #777;
        }
        .data-point-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
        }
        .calculation-diagram {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 30px 0;
        }
        .formula-chart {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        .calculation-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .calculation-step {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            flex: 1;
            text-align: center;
        }
        .math-symbol {
            font-size: 24px;
            margin: 0 15px;
            color: #7f8c8d;
        }
        .input-data {
            background-color: #e3f2fd;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 10px;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }
        .result-box {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border: 2px solid #4caf50;
            font-weight: bold;
            text-align: center;
        }
        .pallets-code {
            background-color: #e3f2fd;
            border-left: 5px solid #6e82f3;
        }
        .rollcages-code {
            background-color: #fff3e0;
            border-left: 5px solid #6e82f3;
        }
        .dep-table {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
        }
        .dep-table th, .dep-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .dep-table th {
            background-color: #f2f2f2;
        }
        .dep-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .dep-table tr:hover {
            background-color: #f0f4f8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>Understanding Delivered RC & Pallets Calculations in Model</h1>
            <p style="text-align: center; font-size: 1.2rem; margin-bottom: 20px;">A visual guide to how products are delivered using pallets and roll cages</p>
            
            <div class="product-highlight">
                <div class="product-image">
                    <div class="chip-bag">
                        <div class="chip-logo"></div>
                    </div>
                </div>
                <div class="product-name">Lay's Zemiakove lupienky pr. BBQ rebierka 130 g</div>
                <p style="margin-top: 10px;">All calculations in this document are specifically for this product in the DRY department</p>
            </div>
        </div>

        <div class="card">
            <h2>Department-Specific Delivery Ratios</h2>
            
            <p>Each department has its own "pallets delivery ratio" which determines what percentage of deliveries are made using pallets versus roll cages:</p>
            
            <div style="overflow-x: auto;">
                <table class="dep-table">
                    <thead>
                        <tr>
                            <th>Store</th>
                            <th>Department</th>
                            <th>Pallets Delivery Ratio</th>
                            <th>Meaning</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>21005</td>
                            <td>Grocery (DRY, BWS, HEA)</td>
                            <td>0.846698837</td>
                            <td>84.7% pallets, 15.3% roll cages</td>
                        </tr>
                        <tr>
                            <td>21005</td>
                            <td>Fresh (DAI, FRZ, PPD)</td>
                            <td>0.2</td>
                            <td>20% pallets, 80% roll cages</td>
                        </tr>
                        <tr>
                            <td>21005</td>
                            <td>GM (HDL, NEW)</td>
                            <td>1.0</td>
                            <td>100% pallets, 0% roll cages</td>
                        </tr>
                        <tr>
                            <td>21005</td>
                            <td>Produce (PRO, SFM)</td>
                            <td>0.2</td>
                            <td>20% pallets, 80% roll cages</td>
                        </tr>
                        <tr>
                            <td>21005</td>
                            <td>Other (SFB, pre_meat_polt)</td>
                            <td>varies</td>
                            <td>Depends on specific department</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="note">
                <strong>Note:</strong> For our Lay's BBQ chips (in the DRY grocery department), the pallets delivery ratio is 0.846698837, which means approximately 84.7% of deliveries use pallets and 15.3% use roll cages.
            </div>
        </div>

        <div class="card">
            <h2>Understanding the Python Code</h2>
            
            <p>Let's understand the Python code that calculates how the Lay's BBQ chips are delivered using pallets and roll cages:</p>
            
            <div class="code-box pallets-code">
                <h3>New Delivery - Pallets Calculation</h3>
                <pre>Drivers["New Delivery - Pallets"] = ( Drivers.cases_delivered_on_sf / Drivers.pallet_capacity ) * Drivers["pallets delivery ratio"]</pre>
            </div>
            
            <div class="code-box rollcages-code">
                <h3>New Delivery - Rollcages Calculation</h3>
                <pre>Drivers["New Delivery - Rollcages"] = ( (Drivers.cases_delivered_on_sf / Drivers.pallet_capacity) * RC_Capacity_Ratio ) * (1 - Drivers["pallets delivery ratio"]).astype("float32")</pre>
                <p style="margin-top: 10px;">Where: RC_Capacity_Ratio = 1 + (1 - 0.62) = 1.38</p>
            </div>
        </div>

        <div class="card">
            <h2>Key Variables Explained</h2>
            
            <div class="variable-definition">
                <div class="variable-card">
                    <div class="icon">📦</div>
                    <h3>cases_delivered_on_sf</h3>
                    <p>Number of Lay's BBQ chip cases delivered to the store floor</p>
                    <div class="example-calculation" style="background-color: #f0f4f8; border-left-color: #2196F3;">
                        <strong>Example:</strong> 0.5 cases on Monday<br>
                        <strong>Range:</strong> 0.2 (Friday) to 0.6 (Wednesday, Thursday)
                    </div>
                </div>
                
                <div class="variable-card">
                    <div class="icon">📏</div>
                    <h3>pallet_capacity</h3>
                    <p>Number of Lay's BBQ chip cases that fit on one pallet</p>
                    <div class="example-calculation" style="background-color: #f0f4f8; border-left-color: #2196F3;">
                        <strong>Value:</strong> 24.0 cases per pallet
                    </div>
                </div>
                
                <div class="variable-card">
                    <div class="icon">⚖️</div>
                    <h3>RC_Capacity_Ratio</h3>
                    <p>Conversion factor between pallet capacity and roll cage capacity</p>
                    <div class="example-calculation" style="background-color: #f0f4f8; border-left-color: #2196F3;">
                        <strong>Formula:</strong> RC_Capacity_Ratio = 1 + (1 - 0.62) = 1.38<br>
                        <strong>Meaning:</strong> 1 pallet capacity = 1.38 roll cage capacities
                    </div>
                </div>
                
                <div class="variable-card">
                    <div class="icon">📊</div>
                    <h3>pallets delivery ratio</h3>
                    <p>Percentage of Lay's BBQ chip deliveries using pallets vs. roll cages</p>
                    <div class="example-calculation" style="background-color: #f0f4f8; border-left-color: #2196F3;">
                        <strong>DRY department value:</strong> 0.846698837 (84.7%)
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>"New Delivery - Pallets" Calculation Explained</h2>
            
            <div class="formula-box">
                New Delivery - Pallets = ( cases_delivered_on_sf / pallet_capacity ) * pallets_delivery_ratio
            </div>
            
            <div class="calculation-diagram">
                <h3>Formula Breakdown</h3>
                <div class="formula-chart">
                    <div class="calculation-row">
                        <div class="calculation-step">
                            <div class="input-data">cases_delivered_on_sf</div>
                            <p>Number of cases delivered</p>
                        </div>
                        <div class="math-symbol">÷</div>
                        <div class="calculation-step">
                            <div class="input-data">pallet_capacity</div>
                            <p>How many cases fit on a pallet</p>
                        </div>
                        <div class="math-symbol">=</div>
                        <div class="calculation-step">
                            <div class="input-data">Pallet Fraction</div>
                            <p>How many pallets needed to deliver cases</p>
                        </div>
                    </div>
                    
                    <div class="calculation-row">
                        <div class="calculation-step">
                            <div class="input-data">Pallet Fraction</div>
                        </div>
                        <div class="math-symbol">×</div>