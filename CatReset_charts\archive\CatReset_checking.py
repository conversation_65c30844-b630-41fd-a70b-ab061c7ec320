import pandas as pd
import numpy as np
pd.set_option("display.max_columns", None)

category = "PROVISIONS & DELI (RTE)"

a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\CategoryReset\CatRes_shared\CatReset\number_of_tpns\SRP_Tracker_CatReset_by_TPNB")
a = a[[x for x in a.columns if not x.__contains__("diff")]]
a['first_occurrence'] = a.apply(lambda row: next((col for col in a.columns[6:] if row[col] != 0), 'no_need'), axis=1)

def last_non_zero_column(row):
    columns = row.index[6:18]
    for col in reversed(columns):
        if row[col] != 0:
            return col
    return "no_need"

# Apply the function to each row
a['last_occurrence'] = a.apply(last_non_zero_column, axis=1)

firts_o = a.copy()
firts_o = firts_o[firts_o.first_occurrence != 'no_need']
firts_o.rename(columns={'repl_types':'product_type'}, inplace=True)
firts_o['first_occurrence_value'] = firts_o.apply(lambda row: row[row['first_occurrence']], axis=1)
columns_to_drop = ['p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']
firts_o = firts_o.drop(columns=columns_to_drop)
firts_o = firts_o.pivot_table(index=['country','tpnb','category','product_name','DIV_DESC', 'first_occurrence'], columns='product_type', values='first_occurrence_value', fill_value=0).reset_index()

# Columns to update
columns_to_update = ["srp", "nsrp", "mu", "split_pallet", "full_pallet"]



for index, row in firts_o.iterrows():
    max_value_column = max(columns_to_update, key=lambda col: row[col])
    for col in columns_to_update:
        firts_o.at[index, col] = 1 if col == max_value_column else 0



cascade_first = firts_o[(firts_o.country.isin(['CZ','SK', 'HU'])) & (firts_o.category == category)].groupby(['country', 'first_occurrence'])[["srp", "nsrp", "mu", "split_pallet", "full_pallet"]].sum().reset_index()




last_o = a.copy()
last_o = last_o[last_o.last_occurrence != 'no_need']
last_o.rename(columns={'repl_types':'product_type'}, inplace=True)
last_o['last_occurrence_value'] = last_o.apply(lambda row: row[row['last_occurrence']], axis=1)
columns_to_drop = ['p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']
last_o = last_o.drop(columns=columns_to_drop)
last_o = last_o.pivot_table(index=['country','tpnb','category','product_name','DIV_DESC', 'last_occurrence'], columns='product_type', values='last_occurrence_value', fill_value=0).reset_index()

# Columns to update
columns_to_update = ["srp", "nsrp", "mu", "split_pallet", "full_pallet"]



for index, row in last_o.iterrows():
    max_value_column = max(columns_to_update, key=lambda col: row[col])
    for col in columns_to_update:
        last_o.at[index, col] = 1 if col == max_value_column else 0



cascade_last = last_o[(last_o.country.isin(['CZ','SK', 'HU'])) & (last_o.category == category)].groupby(['country', 'last_occurrence'])[["srp", "nsrp", "mu", "split_pallet", "full_pallet"]].sum().reset_index()



a.drop(["last_occurrence", "first_occurrence"], axis=1, inplace=True)

a = a.melt(id_vars=['country', 'DIV_DESC', 'tpnb', 'product_name', 'category', 'repl_types',], var_name='period')\
.pivot_table(index=['country', 'DIV_DESC', 'tpnb', 'product_name', 'category', 'period'],columns='repl_types', values="value", fill_value=0).reset_index()
a['product_type'] =np.where((a.full_pallet == 0)&(a.srp == 0)&(a.nsrp == 0)&(a.split_pallet == 0)&(a.mu == 0), 0,  a.iloc[:, 6:].idxmax(axis=1))
b = a[a.product_type !=0]

which_bigger = b[(b.country.isin(['SK', 'CZ', 'HU'])) & (b.category == category)].groupby(['country','product_type',  'period'])['tpnb'].nunique().reset_index().pivot_table(index=['country',  'period'], columns="product_type", values="tpnb",fill_value=0).reset_index()
try:
    which_bigger['sum_tpn'] = which_bigger[['full_pallet', 'nsrp','srp', 'mu', 'split_pallet']].sum(axis=1)
except:
    pass




#ha egy boltban is srp akkor srp
x = a.groupby(['country', 'DIV_DESC','tpnb', 'product_name', 'category', 'period'])[['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']].sum().reset_index()
x['pack_type'] = np.where(x['srp'] > 0, 'srp', 'nsrp')
x['pack_type'] = np.where((x.full_pallet == 0)&(x.srp == 0)&(x.nsrp == 0)&(x.split_pallet == 0)&(x.mu == 0), 'no_need', x['pack_type'])
x['pack_type_value'] = 1
x['pack_type_value'] = np.where(x.pack_type == 'no_need', 0, x['pack_type_value'])
x = x[(x.category == category)&(x.country.isin(['SK', 'CZ', 'HU']))].pivot_table(index=['country', 'tpnb', 'period'], columns="pack_type", values='pack_type_value', fill_value=0)
# Define the columns to aggregate
columns_to_aggregate = ['nsrp', 'srp', 'full_pallet', 'mu', 'split_pallet']
# Filter the columns that exist in the DataFrame
valid_columns = [col for col in columns_to_aggregate if col in x.columns]
x=x.groupby(['country', 'period'])[valid_columns].sum().reset_index()


# Define the desired column order
desired_order = ['p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']

def apply_categorical_order(df, desired_order, column_name):
    df[column_name] = pd.Categorical(df[column_name], categories=desired_order, ordered=True)
    return df.sort_values(by=['country', column_name])

which_bigger = apply_categorical_order(which_bigger, desired_order, 'period')
x = apply_categorical_order(x, desired_order, 'period')
cascade_first = apply_categorical_order(cascade_first, desired_order, 'first_occurrence')
cascade_last = apply_categorical_order(cascade_last, desired_order, 'last_occurrence')





filename = fr'c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\CategoryReset\Categories\CatReset_checks_p1_p12_{category}.xlsx'
with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
    which_bigger.to_excel(writer, sheet_name='product_is_srp_where_the_most', index=False)
    x.to_excel(writer, sheet_name='if_one_store_has_srp_it_is_srp', index=False)
    cascade_first.to_excel(writer, sheet_name='Listing_products', index=False)
    cascade_last.to_excel(writer, sheet_name='Delisting_products', index=False)



