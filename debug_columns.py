#!/usr/bin/env python3
"""
Simple script to count columns in final_columns list
"""

def count_final_columns():
    print("🔍 COUNTING FINAL COLUMNS")
    print("=" * 30)
    
    # Manually count the final_columns from the polars_drivers.py file
    final_columns = [
        "country", "store", "day", "dep", "pmg", "t_touch", "cases_to_replenish",
        "Sec_NSRP_cases", "Sec_SRP_cases", "light", "heavy", "backstock pallet ratio",
        "Replenished Rollcages", "Replenished Pallets", "tpnb", "sold_units", "sold_cases",
        "sales", "stock", "weekly_stock", "cases_delivered", "cases_delivered_on_sf",
        "cases_delivered_rsu", "Add_Walking Cages", "pallet_capacity", "Add_Walking Backstock Cages",
        "Add_Walking Pallets", "Backstock unit", "Backstock Cases", "Backstock Pallets",
        "Backstock Rollcages", "Backstock Shelf Trolley", "Backstock_tpn_nr", "backroom_pallets",
        "Bottle_Tag", "Broken Items", "broken_case_flag", "Bulk Pallets", "Capping Shelf Cases",
        "Clip Strip Cases", "Clip Strip Items", "Electro_Tag", "Empty Pallets", "Empty Rollcages",
        "Empty Shelf Trolley", "Foil_Cases", "Full Pallet", "Full Pallet Cases",
        "Full + Half Pallet Cases", "icreamNSRP", "Gillette_Tag", "H_Post-sort Cases",
        "H_CASE_H_NSRP_items", "H_CASE_L_NSRP_items", "single_pick_items", "H_Hook Fill Cases",
        "L_Hook Fill Cases", "H_NSRP_for_opening_type", "H_Pre-sorted Cases", "H_SRP",
        "H_NSRP", "H_SRP_for_opening_type", "Hard_Tag", "Hook Fill Items", "L_Post-sort Cases",
        "L_NSRP", "L_NSRP_for_opening_type", "L_NSRP_Items", "L_Pre-sorted Cases", "L_SRP",
        "L_SRP_for_opening_type", "MU Pallet", "New Delivery - Pallets", "New Delivery - Rollcages",
        "New Delivery - Shelf Trolley", "Ownbrand_perforated_box_cases", "Ownbrand_shrink_cases",
        "Ownbrand_tray_cases", "Ownbrand_tray_with_hood_cases", "Ownbrand_tray_with_shrink_cases",
        "Pre-sorted Rollcages", "Pre-sorted Shelf Trolley", "Post-sort Cases", "Post-sort Pallets",
        "Post-sort Rollcages", "Racking Pallets", "Safer_Tag", "Salami_Tag", "Soft_Tag",
        "CrocoTag", "BliszterfülTag", "Total RC's and Pallets", "High_pallet_cases_on_Dry30_and_DRY24",
        "High_pallets_on_Dry30_and_DRY24", "High_half_pallet_cases_on_Dry30_and_DRY24",
        "High_half_pallets_on_Dry30_and_DRY24", "Tag_total_nr", "shelfCapacity", "nr_of_tpn",
        "unit", "case_capacity", "Two Touch Cases", "Two Touch unit", "total_cases_to_disassemble",
        "cases_foil_to_disassemble", "stock_cases_weekly", "Add_Walking PBL Cages",
        "Add_Walking PBS Cages", "Add_Walking PBL Pallets", "Add_Walking PBS Pallets",
        "PBL_CAGE", "PBL_PALLET", "PBS_CAGE", "PBS_PALLET", "pallet_capacity_avg"
    ]
    
    print(f"📊 Total columns in final_columns: {len(final_columns)}")
    print(f"📊 Expected (pandas): 116")
    print(f"📊 Difference: {116 - len(final_columns)}")
    
    # Check for some key columns that might be missing
    key_columns_to_check = [
        "product_name", "format", "tpn", "secondary_srp", "secondary_nsrp",
        "srp", "nsrp", "mu", "split_pallet", "full_pallet", "icream_nsrp",
        "stock_unit_weekly", "weekly_stock"
    ]
    
    print(f"\n🔍 Checking for potentially missing key columns:")
    for col in key_columns_to_check:
        if col in final_columns:
            print(f"   ✅ {col} - PRESENT")
        else:
            print(f"   ❌ {col} - MISSING (potential candidate!)")

if __name__ == "__main__":
    count_final_columns()
