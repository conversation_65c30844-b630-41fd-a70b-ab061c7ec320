import pyodbc
import pandas as pd
import polars as pl
import time

start = time.time()
conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()


a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\online_price_change_whatif_full\losses_RawData_online_price_change_whatif_full")

# Assuming your existing result is in a dataframe called 'existing_results'
# Get unique TPNBs from your existing results
unique_tpnbs = a['tpnb'].dropna().unique().tolist()


start = "'f2025w12'"
end = "'f2025w22'"

countries = "('HU','CZ','SK')"

pmg = "('BWS','DAI','DRY','FRZ','HDL','HEA','PPD','PRO','SFB','SFM', 'SFP')"


# Convert to string format for SQL IN clause
tpnb_list = "(" + ",".join([str(int(tpnb)) for tpnb in unique_tpnbs]) + ")"

sms_query = f"""
SELECT 
    stores.cntr_code AS country,
    CAST(stores.dmst_store_code as INT) AS store,
    cal.dmtm_fw_code as week,
    CAST(tpns.slad_tpnb as INT) AS tpnb,
    CAST(tpns.slad_tpn as INT) AS tpn,
    tpns.slad_long_des as product_name,
    hier.pmg AS pmg,
    99 AS code,
    SUM(a.slsms_unit) AS amount 
FROM dw.sl_sms a
JOIN dm.dim_stores stores ON stores.dmst_store_id = a.slsms_dmst_id 
    AND stores.cntr_id = a.slsms_cntr_id 
JOIN dm.dim_artgld_details tpns ON tpns.slad_dmat_id = a.slsms_dmat_id 
    AND tpns.cntr_id = a.slsms_cntr_id 
LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
JOIN tesco_analysts.hierarchy_spm hier ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0") 
    AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
    AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
    AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
    AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
    AND stores.cntr_code IN {countries}
    AND SUBSTRING(hier.pmg, 1, 3) IN {pmg}
    AND a.slsms_unit > 0
    AND CAST(tpns.slad_tpnb as INT) IN {tpnb_list}
GROUP BY stores.cntr_code,
    stores.dmst_store_code,
    cal.dmtm_fw_code,
    tpns.slad_tpnb,
    tpns.slad_tpn,
    hier.pmg,
    tpns.slad_long_des
"""

sms_results = pd.read_sql(sms_query, conn)