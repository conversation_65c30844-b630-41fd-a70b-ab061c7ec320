{"cells": [{"cell_type": "code", "execution_count": null, "id": "a1d1dada-24b6-423b-a285-4dd11661c661", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e743dfd9-97e9-4cda-bdcd-a3b386bfa2d4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "84e1e089-393e-47bd-898d-04599b0dc593", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "07ca6ecb-1103-4b72-9f3c-560c3f649550", "metadata": {}, "outputs": [], "source": ["mods_sk_f = get_latest_xlsx_file(latest_folder_sk_cz, 'SK')\n", "# mods_sk = read_xlsx_sheets(mods_sk_f)\n", "# mods_sk['Store Number'] = mods_sk['Store Number'].map(lambda x: str(2) + str(x)).astype(\"int\")\n", "# mods_cz_f = get_latest_xlsx_file(latest_folder_sk_cz, 'CZ')\n", "# mods_cz = read_xlsx_sheets(mods_cz_f)"]}, {"cell_type": "code", "execution_count": 6, "id": "a3184022-9eb3-4cb6-ad98-be34286b397c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\\\\\\\huprgvmfs05\\\\CE_SRD_IDR_SHARED\\\\REPORT\\\\DB_Report_SRD_6462376\\\\DB Report CZ  & SK\\\\CZ&SK\\\\2024\\\\05.03.2024'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["latest_folder_sk_cz"]}, {"cell_type": "code", "execution_count": 12, "id": "21593275-6073-4b96-ac1d-d0d6e20c9c93", "metadata": {}, "outputs": [], "source": ["def get_latest_xlsx_file(directory, country):\n", "    # List all files in the given directory, excluding hidden files and files starting with ~$\n", "    all_files = [os.path.join(directory, f) for f in os.listdir(directory) if not f.startswith('~$') and not f.startswith('.') and os.path.isfile(os.path.join(directory, f))]\n", "\n", "    if country == 'HU':\n", "        # Filter files to include only .xlsx files\n", "        xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx')]\n", "    if country == 'CZ':\n", "        xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx') and f.__contains__('CZ.xlsx')]\n", "    if country == 'SK':\n", "        xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx') and f.__contains__('SK.xlsx')]\n", "    \n", "    if not xlsx_files:\n", "        return None  # No .xlsx files found\n", "    \n", "    # Get the most recently modified .xlsx file\n", "    latest_xlsx_file = max(xlsx_files, key=os.path.getmtime)\n", "    \n", "    return latest_xlsx_file\n", "    \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "55cebbb9-03a8-4abe-8d09-8bd2bf0f1fbf", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\\\\\\\huprgvmfs05\\\\CE_SRD_IDR_SHARED\\\\REPORT\\\\DB_Report_SRD_6462376\\\\DB Report CZ  & SK\\\\CZ&SK\\\\2024\\\\05.03.2024\\\\CZ.xlsx'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["get_latest_xlsx_file(latest_folder_sk_cz, 'CZ')"]}, {"cell_type": "code", "execution_count": null, "id": "958376ba-3bbf-4031-84f8-96fa04bb7259", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ef8c7c50-fd03-4946-8724-241362f61e77", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "83cc2ab7-b1bd-4c9a-bc5d-c74dfcd988f7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "\n", "\n", "df = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\p.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "e18a7eea-ad02-42d6-9877-0d861717bf7b", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "76b42b7f-11e2-4f60-b7d8-abeab80bf545", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "\n", "import pandas as pd\n", "\n", "# Create a DataFrame with store and date columns\n", "data = {\n", "    'store': ['41001', '41002', '41003'],\n", "    'date': ['2023-02-02', '2023-02-02', '2023-02-02'],\n", "    'dummy_data_1': [10, 20, 30],\n", "    'dummy_data_2': [15, 25, 35],\n", "    'dummy_data_3': [20, 30, 40],\n", "    'dummy_data_4': [25, 35, 45],\n", "    'dummy_data_5': [30, 40, 50]\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "\n", "# Create a pivot table\n", "pivot_table = pd.pivot_table(df, index=['store', 'date'])\n", "\n", "# Create a writer object with pandas\n", "with pd.ExcelWriter(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\apps\\15mins_report\\pivot_table_with_slicers.xlsx\", engine='xlsxwriter') as writer:\n", "    # Write DataFrame to sheet 1\n", "    df.to_excel(writer, sheet_name='Data', index=False)\n", "    \n", "    # Write pivot table to sheet 2\n", "    pivot_table.to_excel(writer, sheet_name='PivotTable')\n", "    \n", "    # Create slicers for store and date\n", "    workbook = writer.book\n", "    worksheet = writer.sheets['PivotTable']\n", "    \n", "    # Create a slicer for the 'store' column\n", "    slicer_store = workbook.add_slicer('PivotTable!A1', pivot_table.index, 'store', 'Store')\n", "    \n", "    # Create a slicer for the 'date' column\n", "    slicer_date = workbook.add_slicer('PivotTable!B1', pivot_table.index, 'date', 'Date')\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f8dad442-1687-450a-a730-e338b20e25a6", "metadata": {}, "outputs": [], "source": ["for col in range(1, 3):\n", "    print(col)"]}, {"cell_type": "code", "execution_count": null, "id": "4a27118f-8bfc-4206-a825-e436081ef6a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "817e54fc-f164-4132-a39e-4a8bbcf6e1c2", "metadata": {}, "outputs": [], "source": ["d = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\Calculations\\2024_02\\2023_model_drivers\")"]}, {"cell_type": "code", "execution_count": null, "id": "0439c564-c57c-4a4b-81a3-7c8273a81007", "metadata": {}, "outputs": [], "source": ["d[(d.country == 'HU')&(d.tpnb.isin([*********, *********, *********]))].groupby(['country', 'tpnb', ], observed=True)['cases_to_replenish'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "7cecebb2-2d32-47e3-b651-49bae476f6e3", "metadata": {}, "outputs": [], "source": ["c = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\model_outputs\\Q1V3_GMbackstock_whFoodbank4stores_WHrepairCLG\\cases_to_replenish_Q1V3_GMbackstock_whFoodbank4stores_WHrepairCLG_tpn\")"]}, {"cell_type": "code", "execution_count": null, "id": "********-d326-4d46-8a4c-fad8d9288479", "metadata": {}, "outputs": [], "source": ["c[(c.country == 'HU')&(c.tpnb.isin([*********, *********, *********]))].groupby(['country', 'tpnb', 'product_name'], observed=True)['cases_to_replenish'].sum().reset_index().to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\Calculations\\2024_02\\HU_baby_check.xlsx\",index=False)"]}, {"cell_type": "markdown", "id": "d85f6848-a616-416a-8550-b6cae1d0f504", "metadata": {}, "source": ["# CatReset checkings:"]}, {"cell_type": "code", "execution_count": 1, "id": "ce5c00e2-ee9f-4b40-ad21-a710ad2c4a34", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "category = \"PROVISIONS & DELI (RTE)\"\n", "\n", "a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\CategoryReset\\CatRes_shared\\CatReset\\number_of_tpns\\SRP_Tracker_CatReset_by_TPNB\")\n", "a = a[[x for x in a.columns if not x.__contains__(\"diff\")]]\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a8d8b5e8-3baa-45a8-9c3f-1128bda3be79", "metadata": {}, "outputs": [], "source": ["a[(a.category == category)&(a.country == 'SK')].to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\CategoryReset\\Categories\\RTE\\RTE_SK_products_how_many_stores.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": 11, "id": "da78a704-d3ae-494c-be15-bd3c6f77b4b1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "category = \"PROVISIONS & DELI (RTE)\"\n", "\n", "a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\CategoryReset\\CatRes_shared\\CatReset\\number_of_tpns\\SRP_Tracker_CatReset_by_TPNB\")\n", "a = a[[x for x in a.columns if not x.__contains__(\"diff\")]]\n", "\n", "original = a.melt(id_vars=['country', 'DIV_DESC', 'tpnb', 'product_name', 'category', 'repl_types',], var_name='period')\\\n", ".pivot_table(index=['country', 'DIV_DESC', 'tpnb', 'product_name', 'category', 'period'],columns='repl_types', values=\"value\", fill_value=0).reset_index()\n", "\n", "original = original[original['category'] == category].groupby(['country', 'category', 'period'], as_index=False)[['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']].sum()\n", "\n", "\n", "# Calculate percentages\n", "for col in ['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']:\n", "    percentage_col = col + '_%'\n", "    original[percentage_col] = (original[col] / original[['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']].sum()) * 100"]}, {"cell_type": "code", "execution_count": 14, "id": "728ed4a9-71f3-4fd4-8fe1-5bec5debfc80", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>repl_types</th>\n", "      <th>country</th>\n", "      <th>category</th>\n", "      <th>period</th>\n", "      <th>full_pallet</th>\n", "      <th>mu</th>\n", "      <th>nsrp</th>\n", "      <th>split_pallet</th>\n", "      <th>srp</th>\n", "      <th>full_pallet_%</th>\n", "      <th>mu_%</th>\n", "      <th>nsrp_%</th>\n", "      <th>split_pallet_%</th>\n", "      <th>srp_%</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CE</td>\n", "      <td>PROVISIONS &amp; DELI (RTE)</td>\n", "      <td>p1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>58399.0</td>\n", "      <td>0.0</td>\n", "      <td>114372.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>33.801390</td>\n", "      <td>0.0</td>\n", "      <td>66.198610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CE</td>\n", "      <td>PROVISIONS &amp; DELI (RTE)</td>\n", "      <td>p10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>39776.0</td>\n", "      <td>0.0</td>\n", "      <td>84359.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>32.042534</td>\n", "      <td>0.0</td>\n", "      <td>67.957466</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CE</td>\n", "      <td>PROVISIONS &amp; DELI (RTE)</td>\n", "      <td>p11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>39773.0</td>\n", "      <td>0.0</td>\n", "      <td>84375.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>32.036763</td>\n", "      <td>0.0</td>\n", "      <td>67.963237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CE</td>\n", "      <td>PROVISIONS &amp; DELI (RTE)</td>\n", "      <td>p12</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>39152.0</td>\n", "      <td>0.0</td>\n", "      <td>84695.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>31.613200</td>\n", "      <td>0.0</td>\n", "      <td>68.386800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CE</td>\n", "      <td>PROVISIONS &amp; DELI (RTE)</td>\n", "      <td>p2</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>47886.0</td>\n", "      <td>0.0</td>\n", "      <td>93245.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>33.930178</td>\n", "      <td>0.0</td>\n", "      <td>66.069822</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["repl_types country                 category period  full_pallet   mu     nsrp  \\\n", "0               CE  PROVISIONS & DELI (RTE)     p1          0.0  0.0  58399.0   \n", "1               CE  PROVISIONS & DELI (RTE)    p10          0.0  0.0  39776.0   \n", "2               CE  PROVISIONS & DELI (RTE)    p11          0.0  0.0  39773.0   \n", "3               CE  PROVISIONS & DELI (RTE)    p12          0.0  0.0  39152.0   \n", "4               CE  PROVISIONS & DELI (RTE)     p2          0.0  0.0  47886.0   \n", "\n", "repl_types  split_pallet       srp  full_pallet_%  mu_%     nsrp_%  \\\n", "0                    0.0  114372.0            0.0   0.0  33.801390   \n", "1                    0.0   84359.0            0.0   0.0  32.042534   \n", "2                    0.0   84375.0            0.0   0.0  32.036763   \n", "3                    0.0   84695.0            0.0   0.0  31.613200   \n", "4                    0.0   93245.0            0.0   0.0  33.930178   \n", "\n", "repl_types  split_pallet_%      srp_%  \n", "0                      0.0  66.198610  \n", "1                      0.0  67.957466  \n", "2                      0.0  67.963237  \n", "3                      0.0  68.386800  \n", "4                      0.0  66.069822  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["for col in ['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']:\n", "    percentage_col = col + '_%'\n", "    original[percentage_col] = (original[col] / original[['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']].sum(axis=1)) * 100\n", "original.head()\n", "    "]}, {"cell_type": "code", "execution_count": 19, "id": "34ad9220-e9f9-4b78-8632-4f5d8f188597", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "category = \"PROVISIONS & DELI (RTE)\"\n", "\n", "a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\CategoryReset\\CatRes_shared\\CatReset\\number_of_tpns\\SRP_Tracker_CatReset_by_TPNB\")\n", "a = a[[x for x in a.columns if not x.__contains__(\"diff\")]]\n", "\n", "original = a.melt(id_vars=['country', 'DIV_DESC', 'tpnb', 'product_name', 'category', 'repl_types',], var_name='period')\\\n", ".pivot_table(index=['country', 'DIV_DESC', 'tpnb', 'product_name', 'category', 'period'],columns='repl_types', values=\"value\", fill_value=0).reset_index()\n", "\n", "original = original[original['category'] == category].groupby(['country', 'category', 'period'], as_index=False)[['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']].sum()\n", "\n", "for col in ['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']:\n", "    percentage_col = col + '_%'\n", "    original[percentage_col] = (original[col] / original[['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']].sum(axis=1))\n", "\n", "a['first_occurrence'] = a.apply(lambda row: next((col for col in a.columns[6:] if row[col] != 0), 'no_need'), axis=1)\n", "\n", "def last_non_zero_column(row):\n", "    columns = row.index[6:18]\n", "    for col in reversed(columns):\n", "        if row[col] != 0:\n", "            return col\n", "    return \"no_need\"\n", "\n", "# Apply the function to each row\n", "a['last_occurrence'] = a.apply(last_non_zero_column, axis=1)\n", "\n", "firts_o = a.copy()\n", "firts_o = firts_o[firts_o.first_occurrence != 'no_need']\n", "firts_o.rename(columns={'repl_types':'product_type'}, inplace=True)\n", "firts_o['first_occurrence_value'] = firts_o.apply(lambda row: row[row['first_occurrence']], axis=1)\n", "columns_to_drop = ['p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']\n", "firts_o = firts_o.drop(columns=columns_to_drop)\n", "firts_o = firts_o.pivot_table(index=['country','tpnb','category','product_name','DIV_DESC', 'first_occurrence'], columns='product_type', values='first_occurrence_value', fill_value=0).reset_index()\n", "\n", "# Columns to update\n", "columns_to_update = [\"srp\", \"nsrp\", \"mu\", \"split_pallet\", \"full_pallet\"]\n", "\n", "\n", "\n", "for index, row in firts_o.iterrows():\n", "    max_value_column = max(columns_to_update, key=lambda col: row[col])\n", "    for col in columns_to_update:\n", "        firts_o.at[index, col] = 1 if col == max_value_column else 0\n", "\n", "\n", "\n", "cascade_first = firts_o[(firts_o.country.isin(['CZ','SK', 'HU', 'CE'])) & (firts_o.category == category)].groupby(['country', 'first_occurrence'])[[\"srp\", \"nsrp\", \"mu\", \"split_pallet\", \"full_pallet\"]].sum().reset_index()\n", "\n", "\n", "\n", "\n", "last_o = a.copy()\n", "last_o = last_o[last_o.last_occurrence != 'no_need']\n", "last_o.rename(columns={'repl_types':'product_type'}, inplace=True)\n", "last_o['last_occurrence_value'] = last_o.apply(lambda row: row[row['last_occurrence']], axis=1)\n", "columns_to_drop = ['p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']\n", "last_o = last_o.drop(columns=columns_to_drop)\n", "last_o = last_o.pivot_table(index=['country','tpnb','category','product_name','DIV_DESC', 'last_occurrence'], columns='product_type', values='last_occurrence_value', fill_value=0).reset_index()\n", "\n", "# Columns to update\n", "columns_to_update = [\"srp\", \"nsrp\", \"mu\", \"split_pallet\", \"full_pallet\"]\n", "\n", "\n", "\n", "for index, row in last_o.iterrows():\n", "    max_value_column = max(columns_to_update, key=lambda col: row[col])\n", "    for col in columns_to_update:\n", "        last_o.at[index, col] = 1 if col == max_value_column else 0\n", "\n", "\n", "\n", "cascade_last = last_o[(last_o.country.isin(['CZ','SK', 'HU', 'CE'])) & (last_o.category == category)].groupby(['country', 'last_occurrence'])[[\"srp\", \"nsrp\", \"mu\", \"split_pallet\", \"full_pallet\"]].sum().reset_index()\n", "\n", "\n", "\n", "a.drop([\"last_occurrence\", \"first_occurrence\"], axis=1, inplace=True)\n", "\n", "a = a.melt(id_vars=['country', 'DIV_DESC', 'tpnb', 'product_name', 'category', 'repl_types',], var_name='period')\\\n", ".pivot_table(index=['country', 'DIV_DESC', 'tpnb', 'product_name', 'category', 'period'],columns='repl_types', values=\"value\", fill_value=0).reset_index()\n", "a['product_type'] =np.where((a.full_pallet == 0)&(a.srp == 0)&(a.nsrp == 0)&(a.split_pallet == 0)&(a.mu == 0), 0,  a.iloc[:, 6:].idxmax(axis=1))\n", "b = a[a.product_type !=0]\n", "\n", "which_bigger = b[(b.country.isin(['CZ','SK', 'HU', 'CE'])) & (b.category == category)].groupby(['country','product_type',  'period'])['tpnb'].nunique().reset_index().pivot_table(index=['country',  'period'], columns=\"product_type\", values=\"tpnb\",fill_value=0).reset_index()\n", "try:\n", "    which_bigger['sum_tpn'] = which_bigger[['full_pallet', 'nsrp','srp', 'mu', 'split_pallet']].sum(axis=1)\n", "except:\n", "    pass\n", "\n", "\n", "\n", "\n", "#ha egy <PERSON>ban is srp akkor srp\n", "x = a.groupby(['country', 'DIV_DESC','tpnb', 'product_name', 'category', 'period'])[['full_pallet', 'mu', 'nsrp', 'split_pallet', 'srp']].sum().reset_index()\n", "x['pack_type'] = np.where(x['srp'] > 0, 'srp', 'nsrp')\n", "x['pack_type'] = np.where((x.full_pallet == 0)&(x.srp == 0)&(x.nsrp == 0)&(x.split_pallet == 0)&(x.mu == 0), 'no_need', x['pack_type'])\n", "x['pack_type_value'] = 1\n", "x['pack_type_value'] = np.where(x.pack_type == 'no_need', 0, x['pack_type_value'])\n", "x = x[(x.category == category)&(x.country.isin(['CZ','SK', 'HU', 'CE']))].pivot_table(index=['country', 'tpnb', 'product_name', 'period'], columns=\"pack_type\", values='pack_type_value', fill_value=0)\n", "# Define the columns to aggregate\n", "columns_to_aggregate = ['nsrp', 'srp', 'full_pallet', 'mu', 'split_pallet']\n", "# Filter the columns that exist in the DataFrame\n", "valid_columns = [col for col in columns_to_aggregate if col in x.columns]\n", "x=x.groupby(['country', 'period'])[valid_columns].sum().reset_index()\n", "\n", "\n", "# Define the desired column order\n", "desired_order = ['p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']\n", "\n", "def apply_categorical_order(df, desired_order, column_name):\n", "    df[column_name] = pd.Categorical(df[column_name], categories=desired_order, ordered=True)\n", "    return df.sort_values(by=['country', column_name])\n", "\n", "which_bigger = apply_categorical_order(which_bigger, desired_order, 'period')\n", "x = apply_categorical_order(x, desired_order, 'period')\n", "cascade_first = apply_categorical_order(cascade_first, desired_order, 'first_occurrence')\n", "cascade_last = apply_categorical_order(cascade_last, desired_order, 'last_occurrence')\n", "original = apply_categorical_order(original, desired_order, 'period')\n", "\n", "\n", "\n", "\n", "\n", "filename = fr'c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\CategoryReset\\Categories\\CatReset_checks_p1_p12_{category}.xlsx'\n", "with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:\n", "    original.to_excel(writer, sheet_name='original_as_chart_is_done', index=False)\n", "    which_bigger.to_excel(writer, sheet_name='product_is_srp_where_the_most', index=False)\n", "    x.to_excel(writer, sheet_name='if_one_store_has_srp_it_is_srp', index=False)\n", "    cascade_first.to_excel(writer, sheet_name='Listing_products', index=False)\n", "    cascade_last.to_excel(writer, sheet_name='Delisting_products', index=False)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 32, "id": "e4df1195-a7a1-427b-b597-ea387918e41b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>repl_types</th>\n", "      <th>country</th>\n", "      <th>DIV_DESC</th>\n", "      <th>tpnb</th>\n", "      <th>product_name</th>\n", "      <th>category</th>\n", "      <th>period</th>\n", "      <th>full_pallet</th>\n", "      <th>mu</th>\n", "      <th>nsrp</th>\n", "      <th>split_pallet</th>\n", "      <th>srp</th>\n", "      <th>product_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1372227</th>\n", "      <td>SK</td>\n", "      <td>Fresh_Froz_Food</td>\n", "      <td>220338905</td>\n", "      <td>TS Kuraci hamburger 121 g</td>\n", "      <td>PROVISIONS &amp; DELI (RTE)</td>\n", "      <td>p12</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>118.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>nsrp</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["repl_types country         DIV_DESC       tpnb               product_name  \\\n", "1372227         SK  Fresh_Froz_Food  220338905  TS Kuraci hamburger 121 g   \n", "\n", "repl_types                 category period  full_pallet   mu   nsrp  \\\n", "1372227     PROVISIONS & DELI (RTE)    p12          0.0  0.0  118.0   \n", "\n", "repl_types  split_pallet  srp product_type  \n", "1372227              0.0  0.0         nsrp  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["a[(a.country == 'SK')&(a.tpnb == 220338905)&(a.period == 'p12')].head()"]}, {"cell_type": "code", "execution_count": 25, "id": "35167115-2f1f-4901-8e5e-90feda5e6186", "metadata": {}, "outputs": [], "source": ["\n", "x.reset_index().query(\"country == 'SK' & period == 'p12' & nsrp == 1\").to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\CategoryReset\\Categories\\RTE\\SK_RTE_p12_nsrp_products.xlsx\", index=False)\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "e9fb82ed-bdb6-467a-90a7-2b1ac99f9a79", "metadata": {}, "outputs": [], "source": ["baby_cascade_first.to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\Calculations\\2024_02\\baby_with_new_prod.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6e45991b-8588-4e62-9371-8ca9b975e3cb", "metadata": {}, "outputs": [], "source": ["import os\n", "from datetime import datetime\n", "\n", "folder_path = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\WH\\SK_HU_CZ_bottle_return\"  # Update this with the actual path to your folder\n", "\n", "def count_days(file_name):\n", "    date_range = file_name.split(\"__\")[-1].split(\" \")[0].split(\"_\")\n", "    start_date = datetime.strptime(date_range[0], \"%Y-%m-%d\")\n", "    end_date = datetime.strptime(date_range[1], \"%Y-%m-%d\")\n", "    return (end_date - start_date).days\n", "\n", "\n", "files = [f for f in os.listdir(folder_path) if f.startswith(\"kpi-SK\")]\n", "total_days = 0\n", "for file in files:\n", "    total_days += count_days(file)\n", "# print(\"Total days across all files:\", total_days)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a068574b-4372-4c21-9118-ee9be8522c58", "metadata": {}, "outputs": [], "source": ["date_range = file.split(\"__\")[-1].split(\" \")[0].split(\"_\")"]}, {"cell_type": "code", "execution_count": null, "id": "2fb86e62-d520-4e08-948d-086e4fd274fd", "metadata": {}, "outputs": [], "source": ["datetime.strptime(date_range[0], \"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "542dff17-7278-4d0f-be25-a57fccaea39c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "59ee78dc-5f97-4cd7-b9c3-b88919b5c07d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "36442768-ae1f-47df-aa7b-b744421baa97", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f7801f97-d5e4-4db8-8f73-9d698f18051e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ade6a911-b110-4fde-908f-dd1506b2cc8a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ca0d714e-b28c-4e4b-bbde-64dcbb43b16d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fab8eacf-09af-443a-892a-71b077d8f6f5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "81edd084-0407-4457-84e3-a8747d651dba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f508d46b-2b75-4f6c-acfa-25530eaa20d6", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "import polars.selectors as cs\n", "from great_tables import GT, md\n", "\n", "\n", "# Utilities -----\n", "\n", "\n", "def calc_n(df: pl.DataFrame, colname: str):\n", "    \"\"\"Count the number of final digits observed across games.\"\"\"\n", "\n", "    return df.select(final_digit=pl.col(colname).mod(10)).group_by(\"final_digit\").agg(n=pl.len())\n", "\n", "\n", "def team_final_digits(game: pl.DataFrame, team_code: str) -> pl.DataFrame:\n", "    \"\"\"Calculate a team's proportion of digits across games (both home and away).\"\"\"\n", "\n", "    home_n = calc_n(game.filter(pl.col(\"home_team\") == team_code), \"home_score\")\n", "    away_n = calc_n(game.filter(pl.col(\"away_team\") == team_code), \"away_score\")\n", "\n", "    joined = (\n", "        home_n.join(away_n, \"final_digit\")\n", "        .select(\"final_digit\", n=pl.col(\"n\") + pl.col(\"n_right\"))\n", "        .with_columns(prop=pl.col(\"n\") / pl.col(\"n\").sum())\n", "    )\n", "\n", "    return joined\n", "\n", "\n", "# Analysis -----\n", "\n", "games = pl.read_csv(\"./games.csv\").filter(\n", "    pl.col(\"game_id\") != \"2023_22_SF_KC\",\n", "    pl.col(\"season\") >= 2015,\n", ")\n", "\n", "# Individual probabilities of final digits per team\n", "home = team_final_digits(games, \"KC\")\n", "away = team_final_digits(games, \"SF\")\n", "\n", "# Cross and multiply p(digit | team=KC)p(digit | team=SF) to get\n", "# the joint probability p(digit_KC, digit_SF | KC, SF)\n", "joint = (\n", "    home.join(away, on=\"final_digit\", how=\"cross\")\n", "    .with_columns(joint=pl.col(\"prop\") * pl.col(\"prop_right\"))\n", "    .sort(\"final_digit\", \"final_digit_right\")\n", "    .pivot(values=\"joint\", columns=\"final_digit_right\", index=\"final_digit\")\n", "    .with_columns((cs.all().exclude(\"final_digit\") * 100).round(1))\n", ")\n", "\n", "# Display -----\n", "\n", "(\n", "    GT(joint, rowname_col=\"final_digit\")\n", "    .data_color(domain=[0, 4], palette=[\"red\", \"grey\", \"blue\"])\n", "    .tab_header(\n", "        \"Super Bowl Squares | Final Score Probabilities\",\n", "        \"Based on all NFL regular season and playoff games (2015-2023)\",\n", "    )\n", "    .tab_stubhead(\"\")\n", "    .tab_spanner(\"San Francisco 49ers\", cs.all())\n", "    .tab_stubhead(\"KC Chiefs\")\n", "    .tab_source_note(\n", "        md(\n", "            '<span style=\"float: right;\">Source data: [<PERSON>, nflverse](https://github.com/nflverse/nfldata)</span>'\n", "        )\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "98f0b1eb-0a5d-401d-a22e-f28be530af98", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import plotly.express as px\n", "\n", "# load dataset\n", "df = pd.read_csv(\"https://raw.githubusercontent.com/plotly/datasets/master/volcano.csv\")\n", "\n", "# Create figure\n", "fig = px.imshow(df, color_continuous_scale=\"Viridis\")\n", "\n", "# Update plot sizing\n", "fig.update_layout(\n", "    width=800,\n", "    height=900,\n", "    autosize=False,\n", "    margin=dict(t=0, b=0, l=0, r=0),\n", "    template=\"plotly_white\",\n", ")\n", "\n", "# Add dropdown\n", "fig.update_layout(\n", "    updatemenus=[\n", "        dict(\n", "            buttons=list([\n", "                dict(\n", "                    args=[\"type\", \"surface\"],\n", "                    label=\"3D Surface\",\n", "                    method=\"restyle\"\n", "                ),\n", "                dict(\n", "                    args=[\"type\", \"heatmap\"],\n", "                    label=\"Heatmap\",\n", "                    method=\"restyle\"\n", "                )\n", "            ]),\n", "            direction=\"down\",\n", "            pad={\"r\": 10, \"t\": 10},\n", "            showactive=True,\n", "            x=0.1,\n", "            xanchor=\"left\",\n", "            y=1.1,\n", "            yanchor=\"top\"\n", "        ),\n", "    ]\n", ")\n", "\n", "# Add annotation\n", "fig.update_layout(\n", "    annotations=[\n", "        dict(text=\"Trace type:\", showarrow=False,\n", "        x=0, y=1.085, yref=\"paper\", align=\"left\")\n", "    ]\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "1328edd9-ef79-4c30-b305-c61300e91faa", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "import pandas as pd\n", "\n", "df_y=pd.DataFrame({'x':[2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020],\n", "                   'y':[  73,  562, 1153,  700, 2104, 1816, 1691, 1082,  914,  482]})\n", "\n", "df_m=pd.DataFrame({'x':['2011-06', '2011-07', '2011-08', '2011-09', '2011-10', '2011-11',\n", "                        '2011-12', '2012-01', '2012-02', '2012-03', '2012-04', '2012-05',\n", "                        '2012-06', '2012-07', '2012-08', '2012-09', '2012-10', '2012-11',\n", "                        '2012-12', '2013-01', '2013-02', '2013-03', '2013-04', '2013-05',\n", "                        '2013-06', '2013-07', '2013-08', '2013-09', '2013-10', '2013-11',\n", "                        '2013-12', '2014-01', '2014-02', '2014-03', '2014-04', '2014-05',\n", "                        '2014-06', '2014-07', '2014-08', '2014-09', '2014-10', '2014-11',\n", "                        '2014-12', '2015-01', '2015-02', '2015-03', '2015-04', '2015-05',\n", "                        '2015-06', '2015-07', '2015-08', '2015-09', '2015-10', '2015-11',\n", "                        '2015-12', '2016-01', '2016-02', '2016-03', '2016-04', '2016-05',\n", "                        '2016-06', '2016-07', '2016-08', '2016-09', '2016-10', '2016-11',\n", "                        '2016-12', '2017-01', '2017-02', '2017-03', '2017-04', '2017-05',\n", "                        '2017-06', '2017-07', '2017-08', '2017-09', '2017-10', '2017-11',\n", "                        '2017-12', '2018-01', '2018-02', '2018-03', '2018-04', '2018-05',\n", "                        '2018-06', '2018-07', '2018-08', '2018-09', '2018-10', '2018-11',\n", "                        '2018-12', '2019-01', '2019-02', '2019-03', '2019-04', '2019-05',\n", "                        '2019-06', '2019-08', '2019-09', '2019-10', '2019-11', '2019-12',\n", "                        '2020-01', '2020-02', '2020-03', '2020-04', '2020-05', '2020-06'],\n", "                    'y':[  1,   1,   2,   8,   4,  20,  37,  79,  16,  13,   8,  12,   2,   5,\n", "                         68, 139,  57,  64,  99, 182,  63,  60,  74, 128,  59, 109, 126,  86,\n", "                         77, 112,  77,  78,  44,  32,  22,  33,  46,  61,  66, 109,  81,  78,\n", "                         50, 140, 151, 297, 173, 225,  69, 119, 213, 177, 134, 217, 189, 255,\n", "                        149, 114, 127, 154, 116, 110, 150, 184, 179, 117, 161,  48, 115, 147,\n", "                        153, 199, 174, 195, 154, 162, 114, 140,  90, 156,  81, 107,  62,  64,\n", "                         49, 128, 127,  60,  89, 115,  44,  58,  86,  65, 102,  93,  82,  78,\n", "                        158,  65,  50,  77,  55,  71,  70, 105, 124,  57]})\n", "\n", "# IMPROVEMENT 1\n", "# INSERT ANOTHER DAT<PERSON><PERSON>ME FOR DAYS HERE WITH THE SAME STRUCTURE AS ABOVE\n", "\n", "# IMPROVEMENT 1\n", "# INCLUDE THE DATAFRAME AS VALUE AND THE NAME df_d as key\n", "# in the dict below:\n", "\n", "dfc = {'year':df_y, 'month':df_m}\n", "\n", "# set index\n", "for df in dfc.keys():\n", "    dfc[df].set_index('x', inplace=True)\n", "\n", "\n", "# plotly start \n", "fig = go.Figure()\n", "# menu setup    \n", "updatemenu= []\n", "\n", "# buttons for menu 1, names\n", "buttons=[]\n", "\n", "# plotly start \n", "fig = go.Figure()\n", "# one trace for each column per dataframe: AI and RANDOM\n", "for df in dfc.keys():\n", "    fig.add_trace(go.<PERSON>(x=dfc[df].index,\n", "                             y=dfc[df]['y'],\n", "                             visible=True,\n", "                             #marker=dict(size=12, line=dict(width=2)),\n", "                             #marker_symbol = 'diamond',\n", "                             name=df\n", "                  )\n", "             )\n", "\n", "\n", "# some line settings for fun\n", "lines = [dict(color='royalblue', width=2, dash='dot'), dict(color='firebrick', width=1, dash='dash')]\n", "markers = [dict(size=12, line=dict(width=2)), dict(size=12, line=dict(width=2))]\n", "\n", "# create traces for each color: \n", "# build argVals for buttons and create buttons\n", "for i, df in enumerate(dfc.keys()):\n", "    args_y = []\n", "    args_x = []\n", "    for col in dfc[df]:\n", "        args_y.append(dfc[df][col].values)\n", "        args_x.append(dfc[df].index)\n", "    argVals = [ {'y':args_y, 'x':args_x,\n", "                 'marker':markers[i], 'line': lines[i]}]\n", "\n", "    buttons.append(dict(method='update',\n", "                        label=df,\n", "                        visible=True,\n", "                        args=argVals))\n", "\n", "updatemenu=[]\n", "your_menu=dict()\n", "updatemenu.append(your_menu)\n", "updatemenu[0]['buttons']=buttons\n", "updatemenu[0]['direction']='down'\n", "updatemenu[0]['showactive']=True\n", "\n", "\n", "fig.update_layout(showlegend=False, updatemenus=updatemenu)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f44d8e12-822b-43df-b0d9-4c6d684bad9a", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "\n", "df_y = pd.DataFrame({'x': [2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020],\n", "                     'y': [73, 562, 1153, 700, 2104, 1816, 1691, 1082, 914, 482]})\n", "\n", "df_m = pd.DataFrame({'x': ['2011-06', '2011-07', '2011-08', '2011-09', '2011-10', '2011-11',\n", "                           '2011-12', '2012-01', '2012-02', '2012-03', '2012-04', '2012-05',\n", "                           '2012-06', '2012-07', '2012-08', '2012-09', '2012-10', '2012-11',\n", "                           '2012-12', '2013-01', '2013-02', '2013-03', '2013-04', '2013-05',\n", "                           '2013-06', '2013-07', '2013-08', '2013-09', '2013-10', '2013-11',\n", "                           '2013-12', '2014-01', '2014-02', '2014-03', '2014-04', '2014-05',\n", "                           '2014-06', '2014-07', '2014-08', '2014-09', '2014-10', '2014-11',\n", "                           '2014-12', '2015-01', '2015-02', '2015-03', '2015-04', '2015-05',\n", "                           '2015-06', '2015-07', '2015-08', '2015-09', '2015-10', '2015-11',\n", "                           '2015-12', '2016-01', '2016-02', '2016-03', '2016-04', '2016-05',\n", "                           '2016-06', '2016-07', '2016-08', '2016-09', '2016-10', '2016-11',\n", "                           '2016-12', '2017-01', '2017-02', '2017-03', '2017-04', '2017-05',\n", "                           '2017-06', '2017-07', '2017-08', '2017-09', '2017-10', '2017-11',\n", "                           '2017-12', '2018-01', '2018-02', '2018-03', '2018-04', '2018-05',\n", "                           '2018-06', '2018-07', '2018-08', '2018-09', '2018-10', '2018-11',\n", "                           '2018-12', '2019-01', '2019-02', '2019-03', '2019-04', '2019-05',\n", "                           '2019-06', '2019-08', '2019-09', '2019-10', '2019-11', '2019-12',\n", "                           '2020-01', '2020-02', '2020-03', '2020-04', '2020-05', '2020-06'],\n", "                    'y': [1, 1, 2, 8, 4, 20, 37, 79, 16, 13, 8, 12, 2, 5,\n", "                          68, 139, 57, 64, 99, 182, 63, 60, 74, 128, 59, 109, 126, 86,\n", "                          77, 112, 77, 78, 44, 32, 22, 33, 46, 61, 66, 109, 81, 78,\n", "                          50, 140, 151, 297, 173, 225, 69, 119, 213, 177, 134, 217, 189, 255,\n", "                          149, 114, 127, 154, 116, 110, 150, 184, 179, 117, 161, 48, 115, 147,\n", "                          153, 199, 174, 195, 154, 162, 114, 140, 90, 156, 81, 107, 62, 64,\n", "                          49, 128, 127, 60, 89, 115, 44, 58, 86, 65, 102, 93, 82, 78,\n", "                          158, 65, 50, 77, 55, 71, 70, 105, 124, 57]})\n", "\n", "df_d = pd.DataFrame({'x': ['2011-01-01', '2011-01-02', '2011-01-03', '2011-01-04', '2011-01-05'],\n", "                     'y': [10, 20, 30, 40, 50]})\n", "\n", "dfc = {'year': df_y, 'month': df_m, 'day': df_d}\n", "\n", "# set index\n", "for df in dfc.keys():\n", "    dfc[df].set_index('x', inplace=True)\n", "\n", "fig = go.Figure()\n", "\n", "# Create traces for each dataframe using Plotly Express\n", "for df in dfc.keys():\n", "    fig.add_trace(go.<PERSON>(x=dfc[df].index,\n", "                             y=dfc[df]['y'],\n", "                             visible=True,\n", "                             name=df))\n", "\n", "# Set layout\n", "fig.update_layout(showlegend=False)\n", "\n", "# Update layout with buttons for each dataframe using Plotly Express\n", "buttons = []\n", "for i, df in enumerate(dfc.keys()):\n", "    visible = [False] * len(dfc)\n", "    visible[i] = True\n", "    buttons.append(dict(label=df,\n", "                        method=\"update\",\n", "                        args=[{\"visible\": visible}],\n", "                        ))\n", "\n", "fig.update_layout(\n", "    updatemenus=[\n", "        dict(\n", "            buttons=buttons,\n", "            direction=\"down\",\n", "            showactive=True,\n", "            x=0.1,\n", "            xanchor=\"left\",\n", "            y=1.1,\n", "            yanchor=\"top\"\n", "        ),\n", "    ]\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e79c650b-4289-4a7b-a4c2-db4a67c788b5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "\n", "df_y = pd.DataFrame({'x': [2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020],\n", "                     'y': [73, 562, 1153, 700, 2104, 1816, 1691, 1082, 914, 482]})\n", "\n", "df_m = pd.DataFrame({'x': ['2011-06', '2011-07', '2011-08', '2011-09', '2011-10', '2011-11',\n", "                           '2011-12', '2012-01', '2012-02', '2012-03', '2012-04', '2012-05',\n", "                           '2012-06', '2012-07', '2012-08', '2012-09', '2012-10', '2012-11',\n", "                           '2012-12', '2013-01', '2013-02', '2013-03', '2013-04', '2013-05',\n", "                           '2013-06', '2013-07', '2013-08', '2013-09', '2013-10', '2013-11',\n", "                           '2013-12', '2014-01', '2014-02', '2014-03', '2014-04', '2014-05',\n", "                           '2014-06', '2014-07', '2014-08', '2014-09', '2014-10', '2014-11',\n", "                           '2014-12', '2015-01', '2015-02', '2015-03', '2015-04', '2015-05',\n", "                           '2015-06', '2015-07', '2015-08', '2015-09', '2015-10', '2015-11',\n", "                           '2015-12', '2016-01', '2016-02', '2016-03', '2016-04', '2016-05',\n", "                           '2016-06', '2016-07', '2016-08', '2016-09', '2016-10', '2016-11',\n", "                           '2016-12', '2017-01', '2017-02', '2017-03', '2017-04', '2017-05',\n", "                           '2017-06', '2017-07', '2017-08', '2017-09', '2017-10', '2017-11',\n", "                           '2017-12', '2018-01', '2018-02', '2018-03', '2018-04', '2018-05',\n", "                           '2018-06', '2018-07', '2018-08', '2018-09', '2018-10', '2018-11',\n", "                           '2018-12', '2019-01', '2019-02', '2019-03', '2019-04', '2019-05',\n", "                           '2019-06', '2019-08', '2019-09', '2019-10', '2019-11', '2019-12',\n", "                           '2020-01', '2020-02', '2020-03', '2020-04', '2020-05', '2020-06'],\n", "                    'y': [1, 1, 2, 8, 4, 20, 37, 79, 16, 13, 8, 12, 2, 5,\n", "                          68, 139, 57, 64, 99, 182, 63, 60, 74, 128, 59, 109, 126, 86,\n", "                          77, 112, 77, 78, 44, 32, 22, 33, 46, 61, 66, 109, 81, 78,\n", "                          50, 140, 151, 297, 173, 225, 69, 119, 213, 177, 134, 217, 189, 255,\n", "                          149, 114, 127, 154, 116, 110, 150, 184, 179, 117, 161, 48, 115, 147,\n", "                          153, 199, 174, 195, 154, 162, 114, 140, 90, 156, 81, 107, 62, 64,\n", "                          49, 128, 127, 60, 89, 115, 44, 58, 86, 65, 102, 93, 82, 78,\n", "                          158, 65, 50, 77, 55, 71, 70, 105, 124, 57]})\n", "\n", "df_d = pd.DataFrame({'x': ['2011-01-01', '2011-01-02', '2011-01-03', '2011-01-04', '2011-01-05'],\n", "                     'y': [10, 20, 30, 40, 50]})\n", "\n", "dfc = {'year': df_y, 'month': df_m, 'day': df_d}\n", "\n", "# set index\n", "for df in dfc.keys():\n", "    dfc[df].set_index('x', inplace=True)\n", "\n", "fig = go.Figure()\n", "\n", "# Create traces for each dataframe using go.<PERSON>atter\n", "for df in dfc.keys():\n", "    fig.add_trace(go.<PERSON>(x=dfc[df].index,\n", "                             y=dfc[df]['y'], # Set visibility for 'year' initially\n", "                             mode='lines',\n", "                             name=df))\n", "\n", "    # Set layout\n", "    fig.update_layout(showlegend=False)\n", "    \n", "    # # Update layout with buttons for each dataframe\n", "    # buttons = []\n", "    # for df in dfc.keys():\n", "    #     visible = [False] * len(dfc)\n", "    #     visible[list(dfc.keys()).index(df)] = True\n", "    #     buttons.append(dict(label=df,\n", "    #                         method=\"update\",\n", "    #                         args=[{\"visible\": visible}],\n", "    #                         ))\n", "    \n", "    # fig.update_layout(\n", "    #     updatemenus=[\n", "    #         dict(\n", "    #             buttons=buttons,\n", "    #             direction=\"down\",\n", "    #             showactive=True,\n", "    #             x=0.1,\n", "    #             xanchor=\"left\",\n", "    #             y=1.1,\n", "    #             yanchor=\"top\"\n", "    #         ),\n", "    #     ]\n", "    # )\n", "    \n", "    fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "30e4c961-ed91-442c-985d-3ec4acad8b24", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "\n", "df_y = pd.DataFrame({'x': [2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020],\n", "                     'y': [73, 562, 1153, 700, 2104, 1816, 1691, 1082, 914, 482]})\n", "\n", "df_m = pd.DataFrame({'x': ['2011-06', '2011-07', '2011-08', '2011-09', '2011-10', '2011-11',\n", "                           '2011-12', '2012-01', '2012-02', '2012-03', '2012-04', '2012-05',\n", "                           '2012-06', '2012-07', '2012-08', '2012-09', '2012-10', '2012-11',\n", "                           '2012-12', '2013-01', '2013-02', '2013-03', '2013-04', '2013-05',\n", "                           '2013-06', '2013-07', '2013-08', '2013-09', '2013-10', '2013-11',\n", "                           '2013-12', '2014-01', '2014-02', '2014-03', '2014-04', '2014-05',\n", "                           '2014-06', '2014-07', '2014-08', '2014-09', '2014-10', '2014-11',\n", "                           '2014-12', '2015-01', '2015-02', '2015-03', '2015-04', '2015-05',\n", "                           '2015-06', '2015-07', '2015-08', '2015-09', '2015-10', '2015-11',\n", "                           '2015-12', '2016-01', '2016-02', '2016-03', '2016-04', '2016-05',\n", "                           '2016-06', '2016-07', '2016-08', '2016-09', '2016-10', '2016-11',\n", "                           '2016-12', '2017-01', '2017-02', '2017-03', '2017-04', '2017-05',\n", "                           '2017-06', '2017-07', '2017-08', '2017-09', '2017-10', '2017-11',\n", "                           '2017-12', '2018-01', '2018-02', '2018-03', '2018-04', '2018-05',\n", "                           '2018-06', '2018-07', '2018-08', '2018-09', '2018-10', '2018-11',\n", "                           '2018-12', '2019-01', '2019-02', '2019-03', '2019-04', '2019-05',\n", "                           '2019-06', '2019-08', '2019-09', '2019-10', '2019-11', '2019-12',\n", "                           '2020-01', '2020-02', '2020-03', '2020-04', '2020-05', '2020-06'],\n", "                    'y': [1, 1, 2, 8, 4, 20, 37, 79, 16, 13, 8, 12, 2, 5,\n", "                          68, 139, 57, 64, 99, 182, 63, 60, 74, 128, 59, 109, 126, 86,\n", "                          77, 112, 77, 78, 44, 32, 22, 33, 46, 61, 66, 109, 81, 78,\n", "                          50, 140, 151, 297, 173, 225, 69, 119, 213, 177, 134, 217, 189, 255,\n", "                          149, 114, 127, 154, 116, 110, 150, 184, 179, 117, 161, 48, 115, 147,\n", "                          153, 199, 174, 195, 154, 162, 114, 140, 90, 156, 81, 107, 62, 64,\n", "                          49, 128, 127, 60, 89, 115, 44, 58, 86, 65, 102, 93, 82, 78,\n", "                          158, 65, 50, 77, 55, 71, 70, 105, 124, 57]})\n", "\n", "df_d = pd.DataFrame({'x': ['2011-01-01', '2011-01-02', '2011-01-03', '2011-01-04', '2011-01-05'],\n", "                     'y': [10, 20, 30, 40, 50]})\n", "\n", "dfc = {'year': df_y, 'month': df_m, 'day': df_d}\n", "\n", "# set index\n", "for df in dfc.keys():\n", "    dfc[df].set_index('x', inplace=True)\n", "\n", "fig = go.Figure()\n", "\n", "# Create traces for each dataframe using go.<PERSON>atter\n", "for df in dfc.keys():\n", "    fig.add_trace(go.<PERSON>(x=dfc[df].index,\n", "                             y=dfc[df]['y'],\n", "                             mode='lines',\n", "                             name=df,\n", "                             visible=True if df == 'year' else False))\n", "\n", "# Set layout\n", "fig.update_layout(showlegend=False)\n", "\n", "# Update layout with buttons for each dataframe\n", "buttons = []\n", "for df in dfc.keys():\n", "    visible = [False] * len(dfc)\n", "    visible[list(dfc.keys()).index(df)] = True\n", "    buttons.append(dict(label=df,\n", "                        method=\"update\",\n", "                        args=[{\"visible\": visible}],\n", "                        ))\n", "\n", "fig.update_layout(\n", "    updatemenus=[\n", "        dict(\n", "            buttons=buttons,\n", "            direction=\"down\",\n", "            showactive=True,\n", "            x=0.1,\n", "            xanchor=\"left\",\n", "            y=1.1,\n", "            yanchor=\"top\"\n", "        ),\n", "    ]\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "db188b78-5b80-47b5-bd7a-e2205c215218", "metadata": {}, "outputs": [], "source": ["import requests\n", "import io\n", "import pandas as pd\n", "import plotly.express as px\n", "\n", "# fmt: off\n", "# case / death data\n", "dfall = pd.read_csv(io.StringIO(\n", "    requests.get(\"https://raw.githubusercontent.com/owid/covid-19-data/master/public/data/owid-covid-data.csv\").text))\n", "dfall[\"date\"] = pd.to_datetime(dfall[\"date\"])\n", "countries = ['Australia', 'Austria', 'France', 'Germany', 'Japan', 'New Zealand', 'Singapore', 'United Kingdom', 'United States']\n", "# fmt: on\n", "\n", "# mimic described dict of dataframes\n", "dfs = {c: dfall.loc[dfall[\"location\"].eq(c), [\"date\", \"new_cases\"]] for c in countries}"]}, {"cell_type": "code", "execution_count": null, "id": "7f55d051-ceb2-419f-b7ba-eb2d44ab81a1", "metadata": {}, "outputs": [], "source": ["px.line(dfs[list(dfs.keys())[0]], x=\"date\", y=\"new_cases\").update_layout(\n", "    updatemenus=[\n", "        {\n", "            \"buttons\": [\n", "                {\n", "                    \"label\": c,\n", "                    \"method\": \"restyle\",\n", "                    \"args\": [\n", "                        {\"x\": [dfs[c][\"date\"]], \"y\": [dfs[c][\"new_cases\"]]},\n", "                    ],\n", "                }\n", "                for c in dfs.keys()\n", "            ]\n", "        }\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6b6c0bc8-714a-44e0-833e-23742e9561e1", "metadata": {}, "outputs": [], "source": ["# mimic describe dict of dataframes\n", "dfs = {\n", "    c: dfall.loc[\n", "        dfall[\"location\"].eq(c), [\"date\", \"new_cases\", \"new_deaths\", \"new_vaccinations\"]\n", "    ]\n", "    .set_index(\"date\")\n", "    .stack()\n", "    .reset_index()\n", "    .rename(columns={\"level_1\": \"measure\", 0: \"value\"})\n", "    for c in countries\n", "}\n", "\n", "fig = (\n", "    px.line(\n", "        pd.concat([dfs[c].assign(country=c) for c in dfs.keys()]),\n", "        x=\"date\",\n", "        y=\"value\",\n", "        facet_row=\"measure\",\n", "        color=\"country\",\n", "    )\n", "    .update_layout({f\"yaxis{n}\": {\"matches\": None} for n in range(2, 6)})\n", "    .update_traces(visible=False)\n", ")\n", "\n", "fig.update_layout(\n", "    showlegend=False,\n", "    updatemenus=[\n", "        {\n", "            \"buttons\": [\n", "                {\n", "                    \"label\": c,\n", "                    \"method\": \"update\",\n", "                    \"args\": [{\"visible\": [c in t.hovertemplate for t in fig.data]}],\n", "                }\n", "                for c in dfs.keys()\n", "            ]\n", "        }\n", "    ],\n", "    annotations=[\n", "        {**a, **{\"text\": a[\"text\"].replace(\"measure=\", \"\")}}\n", "        for a in fig.to_dict()[\"layout\"][\"annotations\"]\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cf976644-c1a6-4dbb-ac95-2aef43e6f136", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pathlib import Path\n", "import pyarrow.parquet as pq\n", "import plotly.express as px\n", "import warnings\n", "import plotly.graph_objects as go\n", "import numpy as np\n", "from plotly.subplots import make_subplots\n", "import time\n", "import plotly.io as pio\n", "import io\n", "from PIL import Image\n", "\n", "\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option('display.max_columns', None)\n", "\n", "time_start = time.time()\n", "\n", "def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                            separator=None, auto_open=False):\n", "    with open(html_fname, 'w') as f:\n", "        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))\n", "        for fig in plotly_figs[1:]:\n", "            if separator:\n", "                f.write(separator)\n", "            f.write(fig.to_html(full_html=False, include_plotlyjs=False))\n", "\n", "    if auto_open:\n", "        import pathlib, webbrowser\n", "        uri = pathlib.Path(html_fname).absolute().as_uri()\n", "        webbrowser.open(uri)\n", "        \n", "p11 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\18-01-2024\\CE_JDA_SRD_for_model\"\n", "\n", "\n", "category_reset_df = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\CategoryReset\\cateres_products\"\n", "\n", "period = [ 11]\n", "\n", "\n", "repl_types = [\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]\n", "\n", "store_list = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl\\Stores_Inputs_2023_Q1_Vol3.xlsx\")\n", "store_list.columns = [x.lower() for x in store_list.columns]\n", "\n", "store_list = store_list['store'].unique().tolist()\n", "\n", "df_ce = pd.DataFrame()\n", "\n", "for x, y in zip([ p11], period ):\n", "    \n", "    df = pd.read_parquet(x)\n", "    \n", "    df = df[df.tpnb > 0]\n", "    \n", "    df = df[df.store.isin(store_list)]\n", "\n", "    condition = [\n", "        df[\"store\"].astype(str).str.match(\"^1\"),\n", "        df[\"store\"].astype(str).str.match(\"^2\"),\n", "        df[\"store\"].astype(str).str.match(\"^4\"),\n", "    ]\n", "    results = [\"CZ\", \"SK\", \"HU\"]\n", "    df[\"country\"] = np.select(condition, results, 0)\n", "        \n", "\n", "        \n", "        \n", "    df = df[['country', 'store', 'tpnb','product_name', \"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]]\n", "        \n", "        \n", "        \n", "\n", "    \n", "    # for r in repl_types:\n", "    #     df[r] = np.where(df[r] == 1, df.sold_units, 0)\n", "    \n", "    df['period'] = y\n", "    \n", "    \n", "    \n", "    df = df.groupby(['country', 'tpnb', 'product_name', 'period'], observed=True)[[\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]].sum().reset_index()\n", "    \n", "    \n", "    df_ce = pd.concat([df_ce, df])\n", "    \n", "    print(f\"Done with processing {y}!\")\n", "    \n", "\n", "\n", "\n", "\n", "cat_df = pd.read_parquet(category_reset_df)[['country', 'tpnb', 'category', 'DIV_DESC']].drop_duplicates()\n", "\n", "\n", "\n", "\n", "df_ce = df_ce.merge(cat_df, on=['country', 'tpnb'], how='left')\n", "\n", "df_ce_total = df_ce.groupby([ 'DIV_DESC','category','tpnb', 'product_name', 'period'], observed=True)[[\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]].sum().reset_index()\n", "df_ce_total['country'] = 'CE'\n", "\n", "df_ce = pd.concat([df_ce, df_ce_total])\n", "\n", "df_ce = df_ce[df_ce.category.notnull()]\n", "\n", "\n", "\n", "        \n", "need_cols = [x for x in df_ce.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]  \n", "\n", "# =============================================================================\n", "# Charts\n", "# =============================================================================\n", "df_catres_sum = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'tpnb', 'product_name', 'category', 'repl_types'] ,columns = \"period\", values = 'value', observed=True).reset_index()\n", "\n", "\n", "\n", "# non_food_food = df_ce[df_ce['DIV_DESC'].isin(['NonFood Grocery', 'Food Grocery'])]\n", "# non_food_food['division'] = np.where(non_food_food['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)', 'Grocery (Food)')\n", "\n", "df_catres_sum_div = df_ce.copy()\n", "\n", "# df_catres_sum_div = pd.concat([df_catres_sum_div, non_food_food])\n", "# df_catres_sum_div['division'] = np.where(df_catres_sum_div['division'] == 'Grocery', 'Grocery (total)', df_catres_sum_div['division'])\n", "\n", "df_catres_sum_div = df_catres_sum_div.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'DIV_DESC','tpnb', 'product_name', 'repl_types'] ,columns = \"period\", values = 'value', observed=True).reset_index()\n", "\n", "\n", "\n", "\n", "cols_need = [x for x in df_catres_sum.columns if x not in [1] + df_catres_sum.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]\n", "\n", "cols_need_div = [x for x in df_catres_sum_div.columns if x not in [1] + df_catres_sum_div.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]\n", "\n", "df_catres_sum_category = df_catres_sum.melt(id_vars=cols_need, var_name='period' ).groupby(['country', 'category', 'repl_types','period'], observed=True)['value'].sum().reset_index()\n", "df_catres_sum_category['value_%'] = df_catres_sum_category['value'] / df_catres_sum_category.groupby(['country', 'category', 'period'], observed=True)['value'].transform('sum')\n", "\n", "df_catres_sum_division = df_catres_sum_div.melt(id_vars=cols_need_div, var_name='period' ).groupby(['country', 'DIV_DESC', 'repl_types','period'], observed=True)['value'].sum().reset_index()\n", "df_catres_sum_division['value_%'] = df_catres_sum_division['value'] / df_catres_sum_division.groupby(['country', 'DIV_DESC', 'period'],observed=True)['value'].transform('sum')\n", "df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)',df_catres_sum_division['DIV_DESC'] )\n", "df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'Food Grocery', 'Grocery (Food)',df_catres_sum_division['DIV_DESC'] )\n", "\n", "# df_total = df_catres_sum_division[df_catres_sum_division.country == 'CE'].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()\n", "df_total = df_catres_sum_division.groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()\n", "\n", "df_total['value_%'] = df_total['value'] / df_total.groupby(['country', 'period'],observed=True)['value'].transform('sum')\n", "\n", "df_catres_sum_category['category'] = df_catres_sum_category['category'].apply(lambda x: x.replace(\";\",\" \"))\n", "\n", "\n", "def rename_period_columns(df, prefix='p'):\n", "    df['period'] = df['period'].astype(str)  \n", "    df['period'] = prefix + df['period']  \n", "\n", "for x in [df_total, df_catres_sum_category, df_catres_sum_division ]:\n", "    rename_period_columns(x, 'p')\n", "\n", "\n", "# sorted(df_catres_sum_category['category'].unique().tolist())\n", "cont = []\n", "ind=0\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "b3d2b81d-42d8-4a7f-9877-435f1e23804d", "metadata": {}, "outputs": [], "source": ["df_catres_sum_category[df_catres_sum_category.category==\"BABY\"]"]}, {"cell_type": "code", "execution_count": null, "id": "906174b5-a3cc-43d1-8bbe-4b51bafac39e", "metadata": {}, "outputs": [], "source": ["df_catres_sum_category['year'] = 2023\n", "df_catres_sum_category_b = df_catres_sum_category.copy()\n", "df_catres_sum_category_b['year'] = 2024\n"]}, {"cell_type": "code", "execution_count": null, "id": "016f4f28-aa82-41b6-a819-33b294246c2e", "metadata": {}, "outputs": [], "source": ["dfc = {'2023': df_catres_sum_category, '2024': df_catres_sum_category_b}\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "ddd197cb-1f08-4b94-9c5c-fee2f656d372", "metadata": {}, "outputs": [], "source": ["dfc"]}, {"cell_type": "code", "execution_count": null, "id": "092d9906-ba87-4860-a630-c1a177c8c651", "metadata": {}, "outputs": [], "source": ["dfc\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "d14d5a47-0aea-4862-b2be-b4c6a3b5b7b4", "metadata": {}, "outputs": [], "source": ["df_catres_sum_category_b."]}, {"cell_type": "code", "execution_count": null, "id": "30c9563f-3b00-4d2b-a84e-0aaf1de63f9c", "metadata": {"scrolled": true}, "outputs": [], "source": ["for x in df_catres_sum_category['category'].unique().tolist():\n", "\n", "    fig = px.bar(df_catres_sum_category[df_catres_sum_category.category.isin([x])], x=\"period\", y=\"value_%\",\n", "                 color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=[\"#5497c7\", \"green\", \"#c74848\", \"goldenrod\", \"#5f48c7\"],\n", "                       opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,\n", "                  height=400,\n", "                 width = 1800,\n", "                 category_orders={\"country\": [\"CZ\", \"HU\", \"SK\", \"CE\"]},\n", "                 title=f'{x}',facet_col_wrap=5, orientation='v')\n", "\n", "    \n", "    fig.update_traces(textfont_size=12, textangle=0, textposition=\"auto\", cliponaxis=False)\n", "    fig.update_yaxes(matches=None)\n", "    fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=18))\n", "    fig.update_yaxes(title_font_color='white')\n", "    for annotation in fig['layout']['annotations']: \n", "        annotation['textangle']= 0\n", "    # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))\n", "    fig.update_layout(title_text=f\"<b>{x}</b>\")\n", "    # fig.update_layout(bargap=0.5)\n", "    fig.update_layout(legend=dict(\n", "                orientation=\"h\",\n", "                yanchor=\"bottom\",\n", "                y=1.1,\n", "                xanchor=\"right\",\n", "                x=1.1))\n", "\n", "    \n", "    # fig.update_layout(\n", "    # updatemenus=[\n", "    #     dict(\n", "    #         active=0,\n", "    #         buttons=list([\n", "    #             dict(label=\"2023\",\n", "    #                  method=\"update\",\n", "    #                  args=[{\"data_frame\": df_catres_sum_category[df_catres_sum_category.category.isin([x])],\n", "    #                         }]),\n", "    #             dict(label=\"2024\",\n", "    #                  method=\"update\",\n", "    #                  args=[{\"data_frame\": df_catres_sum_category_b[df_catres_sum_category_b.category.isin([x])],\n", "    #                         }]),\n", "    #         ]),\n", "    #     )\n", "    # ])\n", "\n", "\n", "    # buttons = [\n", "    #   dict(label='2023', \n", "    #        method='update', \n", "    #        args=[{'x':[df_catres_sum_category[df_catres_sum_category.category.isin([x])]['period']], 'y': [df_catres_sum_category[df_catres_sum_category.category.isin([x])]['value_%']]}\n", "                \n", "    #        ]),\n", "    #   dict(label='2024', \n", "    #        method='update', \n", "    #        args=[{'x':[df_catres_sum_category_b[df_catres_sum_category_b.category.isin([x])]['period']], 'y': [df_catres_sum_category_b[df_catres_sum_category_b.category.isin([x])]['value_%']],\n", "    #              'color':[df_catres_sum_category_b[df_catres_sum_category_b.category.isin([x])]['repl_types']],  'facet_col': [df_catres_sum_category_b[df_catres_sum_category_b.category.isin([x])]['country']], 'barmode':['stack']}\n", "    #       ]),\n", "    #    ]\n", "\n", "    # fig.update_layout(updatemenus=[dict(active=0, buttons=buttons)],\n", "                  # )\n", "\n", "    fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "b0e42d13-55c1-4fd2-98a6-7a2120a1cd33", "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "import pandas as pd\n", "import plotly.graph_objects as go\n", "df1 = pd.DataFrame({'Mes': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Out', 'Nov', 'Dez'],\n", "'Vendas': [10000, 20000, 15000, 17000, 16000, 19000, 21000, 22000, 23000, 25000, 26000, 27000]\n", "})\n", "\n", "df2 = pd.DataFrame({\n", "'Mes': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', 'Nov', '<PERSON><PERSON>'],\n", "'Vendas': [12000, 22000, 17000, 19000, 18000, 21000, 23000, 24000, 25000, 27000, 28000, 29000]\n", "})\n", "\n", "fig = px.line(df1, x='Me<PERSON>', y='Vendas', title='Vendas por Mes')\n", "\n", "buttons = [\n", "      dict(label='Vendas - DF1', \n", "           method='restyle', \n", "           args=[{'x': [df1['Mes']], 'y': [df1['Vendas']]},\n", "                 {'title':' Vendas por Mes - DF1'}\n", "                \n", "           ]),\n", "      dict(label='Vendas - DF2', \n", "           method='update', \n", "           args=[{'x':[df2['Me<PERSON>']], 'y': [df2['Vendas']], },\n", "                 {'title':'Vendas por Mes - DF1'}\n", "          ]),\n", "       ]\n", "\n", "fig.update_layout(updatemenus=[dict(active=0, buttons=buttons)],\n", "                  yaxis_range=[9800, 29800])\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "581bd87f-2564-4dea-a8ac-fc0c0f88f12c", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "80572078-6e3d-4380-8179-384e57ca6175", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import plotly.express as px\n", "import datetime\n", "\n", "# mimic OP's datasample\n", "\n", "NPERIODS = 200\n", "\n", "np.random.seed(123)\n", "df = pd.DataFrame(np.random.randint(-10, 12, size=(NPERIODS, 4)),\n", "                  columns=list('ABCD'))\n", "datelist = pd.date_range(datetime.datetime(2020, 1, 1).strftime('%Y-%m-%d'),\n", "                         periods=NPERIODS).tolist()\n", "df['dates'] = datelist \n", "df = df.set_index(['dates'])\n", "df.index = pd.to_datetime(df.index)\n", "df.iloc[0] = 0\n", "df = df.cumsum()\n", "\n", "# Plotly Express\n", "fig = px.line(df, x=df.index, y=df.columns[0], title='My Plotly Express Plot')\n", "\n", "fig.update_layout(\n", "    updatemenus=[\n", "        dict(\n", "            buttons=[\n", "                dict(label=col,\n", "                     method=\"update\",\n", "                     args=[{\"y\": [df[col]]},\n", "                           {\"xaxis\": {\"title\": \"\"},\n", "                            \"yaxis\": {\"title\": \"\"}}])\n", "                for col in df.columns\n", "            ],\n", "            direction=\"down\",\n", "            pad={\"r\": 10, \"t\": -20},\n", "            showactive=True,\n", "            x=0.0001,\n", "            xanchor=\"left\",\n", "            y=1.1,\n", "            yanchor=\"top\"\n", "        ),\n", "    ]\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "56215e34-b9a9-40b4-9a8c-417b7990e467", "metadata": {}, "outputs": [], "source": ["# Imports\n", "import plotly.graph_objs as go\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# data\n", "d = {'x': ['a','b','c','a','b','c','a','b','c'], 'y': [1,2,3,10,20,30,100,200,300], 'group': [1,1,1,2,2,2,3,3,3]}\n", "df = pd.DataFrame(data=d)\n", "\n", "# split df by groups and organize them in a dict\n", "groups = df['group'].unique().tolist()\n", "dfs={}\n", "for g in groups:\n", "    dfs[str(g)]=df[df['group']==g]\n", "\n", "# get column names from first dataframe in the dict\n", "#colNames = list(dfs[list(dfs.keys())[0]].columns)\n", "#colNames=colNames[:2]\n", "\n", "\n", "# one trace for each column per dataframe\n", "fig=go.Figure()\n", "\n", "# set up the first trace\n", "fig.add_trace(go.Bar(x=dfs['1']['x'],\n", "                             y=dfs['1']['y'],\n", "                             visible=True)\n", "             )\n", "# set up the second trace\n", "fig.add_trace(go.Bar(x=dfs['1']['x'],\n", "                             y=dfs['1']['y'],)\n", "             )\n", "\n", "#f=fig.to_dict()\n", "\n", "# plotly start\n", "# buttons for menu 1, names\n", "updatemenu=[]\n", "buttons=[]\n", "\n", "# button with one option for each dataframe\n", "for df in dfs.keys():\n", "    #print(b, df)\n", "    buttons.append(dict(method='restyle',\n", "                        label=df,\n", "                        visible=True,\n", "                        args=[{'y':[dfs[str(df)]['y'].values],\n", "                               'type':'bar'}, [0]],\n", "                        )\n", "                  )\n", "\n", "# another button with one option for each dataframe\n", "buttons2=[]\n", "for df in dfs.keys():\n", "    buttons2.append(dict(method='restyle',\n", "                        label=df,\n", "                        visible=True,\n", "                        args=[{'y':[dfs[str(df)]['y'].values],\n", "                               'type':'bar'}, [1]],\n", "                        )\n", "                  )\n", "\n", "# some adjustments to the updatemenus\n", "updatemenu=[]\n", "your_menu=dict()\n", "updatemenu.append(your_menu)\n", "your_menu2=dict()\n", "updatemenu.append(your_menu2)\n", "#updatemenu[1]\n", "updatemenu[0]['buttons']=buttons\n", "updatemenu[0]['direction']='down'\n", "updatemenu[0]['showactive']=True\n", "updatemenu[1]['buttons']=buttons2\n", "updatemenu[1]['y']=0.5\n", "\n", "# add dropdown menus to the figure\n", "fig.update_layout(showlegend=False, updatemenus=updatemenu)\n", "\n", "# add notations to the dropdown menus\n", "fig.update_layout(\n", "    annotations=[\n", "        go.layout.Annotation(text=\"<b>group/<br>trace:</b>\",\n", "                             x=-0.15, xref=\"paper\",\n", "                             y=1.15, yref=\"paper\",\n", "                             align=\"left\", showarrow=False),\n", "        go.layout.Annotation(text=\"<b>group/<br>trace:</b>\",\n", "                             x=-0.15, xref=\"paper\", y=0.6,\n", "                             yref=\"paper\", showarrow=False),\n", "                  ]\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4ab7c03f-69eb-4178-af24-9bfa7e5b262a", "metadata": {}, "outputs": [], "source": ["from scipy import stats\n", "\n", "def corr_annotation(x, y):\n", "    pearsonr = stats.pearsonr(x, y)\n", "    return 'r = {:.2f} (p = {:.3f})'.format(pearsonr[0], pearsonr[1])\n", "\n", "# Prep random data\n", "import pandas as pd\n", "import numpy as np\n", "\n", "np.random.seed(12)\n", "\n", "data = pd.DataFrame(dict(\n", "    A=np.random.randint(11, size=10),\n", "    B=np.random.randint(11, size=10),\n", "    C=np.random.randint(11, size=10),\n", "    D=np.random.randint(11, size=10) \n", "))\n", "\n", "# Create base figure\n", "import plotly.express as px\n", "\n", "fig = px.scatter(data, x='A', y='B')\n", "\n", "fig.add_annotation(dict(text=corr_annotation(data['A'], data['B']),\n", "                        showarrow=False, \n", "                        yref='paper', xref='paper',\n", "                        x=0.99, y=0.95))\n", "\n", "# Create buttons\n", "import itertools\n", "\n", "buttons = []\n", "\n", "for x, y in itertools.combinations(data.columns, 2):\n", "    buttons.append(dict(method='update',\n", "                        label='{} x {}'.format(x, y),\n", "                        args=[{'x': [data[x]],\n", "                               'y': [data[y]]},\n", "                              {'xaxis': {'title': x},\n", "                               'yaxis': {'title': y},\n", "                               'annotations': [dict(text=corr_annotation(data[x], data[y]),\n", "                                                    showarrow=False, \n", "                                                    yref='paper', xref='paper',\n", "                                                    x=0.99, y=0.95)]}]\n", "                        )\n", "                   )\n", "\n", "# Update and show figure\n", "fig.update_layout(updatemenus=[dict(buttons=buttons, direction='down', x=0.1, y=1.15)])\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ac7e9020-0d2e-4fad-8e51-7e94f4a45ba6", "metadata": {}, "outputs": [], "source": ["\n", "df_catres_sum_category['year'] = '2023'\n", "b = df_catres_sum_category.copy()\n", "b['year'] = '2024'\n", "b['value_%'] = b['value_%']*0.8\n", "df_catres_sum_category = pd.concat([df_catres_sum_category, b])"]}, {"cell_type": "code", "execution_count": null, "id": "7f5726e7-a747-49aa-a1c6-2f72f98db9af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e61979c5-2405-4fd4-a63e-996d712e9821", "metadata": {}, "outputs": [], "source": ["df_catres_sum_category.year.unique()"]}, {"cell_type": "code", "execution_count": null, "id": "8ee422a7-7abe-4a78-b5fe-61c7c6e9cc03", "metadata": {}, "outputs": [], "source": ["cont = []\n", "ind=0\n", "\n", "\n", "years = df_catres_sum_category['year'].unique().tolist()\n", "for x in [df_catres_sum_category['category'].unique().tolist()[0]]:\n", "    for y in df_catres_sum_category['year'].unique().tolist():\n", "        \n", "\n", "        \n", "    \n", "\n", "        fig = px.histogram(df_catres_sum_category[df_catres_sum_category.category.isin([x])], x=\"period\", y=\"value_%\",\n", "                     color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=[\"#5497c7\", \"green\", \"#c74848\", \"goldenrod\", \"#5f48c7\"],\n", "                           opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,\n", "                      height=400,\n", "                     width = 1800,\n", "                     category_orders={\"country\": [\"CZ\", \"HU\", \"SK\", \"CE\"]},\n", "                     title=f'{x}',facet_col_wrap=5, orientation='v')\n", "        fig.update_traces(textfont_size=12, textangle=0, textposition=\"auto\", cliponaxis=False)\n", "        fig.update_yaxes(matches=None)\n", "        fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=18))\n", "        fig.update_yaxes(title_font_color='white')\n", "        for annotation in fig['layout']['annotations']: \n", "            annotation['textangle']= 0\n", "        # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))\n", "        fig.update_layout(title_text=f\"<b>{x}</b>\")\n", "        # fig.update_layout(bargap=0.5)\n", "        fig.update_layout(legend=dict(\n", "                    orientation=\"h\",\n", "                    yanchor=\"bottom\",\n", "                    y=1.1,\n", "                    xanchor=\"right\",\n", "                    x=1.1))\n", "    \n", "    \n", "#     dropdown_buttons = [\n", "#     dict(label='All', method='update', args=[{'visible': [True for _ in countries]}]),\n", "#     *[dict(label=country, method='update', args=[{'visible': [year==c for c in years]}]) for year in years]\n", "# ]\n", "    buttons = [*[dict(method='update',\n", "                    label='{}'.format(y),\n", "                    args=[{'visible': [year==c for c in years]}]) for year in years]]\n", "                            \n", "                       \n", "    \n", "    # Update and show figure\n", "    fig.update_layout(updatemenus=[dict(buttons=buttons, direction='down', x=0.1, y=1.15)])\n", "\n", "    fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "78a29dfc-fbda-4861-bf6c-ebfd0fa75b5f", "metadata": {}, "outputs": [], "source": ["buttons"]}, {"cell_type": "code", "execution_count": null, "id": "eef49d00-9512-478a-825e-538c289da4ae", "metadata": {}, "outputs": [], "source": ["df_catres_sum_category['category'].unique().tolist()[0]"]}, {"cell_type": "code", "execution_count": null, "id": "9debefbe-4b63-4c5a-9356-b9ae3f116123", "metadata": {"scrolled": true}, "outputs": [], "source": ["import plotly.express as px\n", "import plotly.graph_objects as go\n", "df = px.data.tips()\n", "\n", "# We have a list for every day\n", "# In your case will be gropuby('RuleName')\n", "# here for every element d\n", "# d[0] is the name(key) and d[1] is the dataframe\n", "dfs = list(df.groupby(\"day\"))\n", "\n", "first_title = dfs[0][0]\n", "traces = []\n", "buttons = []\n", "for i,d in enumerate(dfs):\n", "    visible = [False] * len(dfs)\n", "    visible[i] = True\n", "    name = d[0]\n", "    traces.append(\n", "        px.treemap(d[1],\n", "                   path=['day', 'time', 'sex'],\n", "                   values='total_bill').update_traces(visible=True if i==0 else False).data[0]\n", "    )\n", "    buttons.append(dict(label=name,\n", "                        method=\"update\",\n", "                        args=[{\"visible\":visible},\n", "                              {\"title\":f\"{name}\"}]))\n", "\n", "updatemenus = [{'active':0, \"buttons\":buttons}]\n", "\n", "fig = go.Figure(data=traces,\n", "                 layout=dict(updatemenus=updatemenus))\n", "fig.update_layout(title=first_title, title_x=0.5)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "e74df95c-91b6-4df8-be42-31081bbfd94e", "metadata": {}, "outputs": [], "source": ["\n", "import pandas as pd\n", "from pathlib import Path\n", "import pyarrow.parquet as pq\n", "import plotly.express as px\n", "import warnings\n", "import plotly.graph_objects as go\n", "import numpy as np\n", "from plotly.subplots import make_subplots\n", "import time\n", "import plotly.io as pio\n", "import io\n", "from PIL import Image\n", "\n", "\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option('display.max_columns', None)\n", "\n", "time_start = time.time()\n", "\n", "def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                            separator=None, auto_open=False):\n", "    with open(html_fname, 'w') as f:\n", "        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))\n", "        for fig in plotly_figs[1:]:\n", "            if separator:\n", "                f.write(separator)\n", "            f.write(fig.to_html(full_html=False, include_plotlyjs=False))\n", "\n", "    if auto_open:\n", "        import pathlib, webbrowser\n", "        uri = pathlib.Path(html_fname).absolute().as_uri()\n", "        webbrowser.open(uri)\n", "\n", "\n", "\n", "\n", "\n", "p1 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\files_for_dataset\\opsdev_verz_0119_.parquet\"\n", "\n", "\n", "\n", "\n", "p2 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\18-04-2023\\CE_JDA_SRD_for_model\"\n", "\n", "p3 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\16-05-2023\\CE_JDA_SRD_for_model\"\n", "\n", "p4 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\12-06-2023\\CE_JDA_SRD_for_model\"\n", "\n", "p5 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\13-07-2023\\CE_JDA_SRD_for_model\"\n", "\n", "p6 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\14-08-2023\\CE_JDA_SRD_for_model\"\n", "\n", "p7 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\13-09-2023\\CE_JDA_SRD_for_model\"\n", "\n", "p8 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\09-10-2023\\CE_JDA_SRD_for_model\"\n", "\n", "p9 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\13-11-2023\\CE_JDA_SRD_for_model\"\n", "\n", "p10 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\12-12-2023\\CE_JDA_SRD_for_model\"\n", "\n", "category_reset_df = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\CategoryReset\\cateres_products\"\n", "\n", "period = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]\n", "\n", "\n", "repl_types = [\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]\n", "\n", "store_list = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl\\Stores_Inputs_2023_Q1_Vol3.xlsx\")\n", "store_list.columns = [x.lower() for x in store_list.columns]\n", "\n", "store_list = store_list['store'].unique().tolist()\n", "\n", "df_ce = pd.DataFrame()\n", "\n", "for x, y in zip([p1, p2, p3, p4, p5, p6, p7, p8, p9, p10], period ):\n", "    \n", "    df = pd.read_parquet(x)\n", "    \n", "    df = df[df.tpnb > 0]\n", "    \n", "    df = df[df.store.isin(store_list)]\n", "\n", "    condition = [\n", "        df[\"store\"].astype(str).str.match(\"^1\"),\n", "        df[\"store\"].astype(str).str.match(\"^2\"),\n", "        df[\"store\"].astype(str).str.match(\"^4\"),\n", "    ]\n", "    results = [\"CZ\", \"SK\", \"HU\"]\n", "    df[\"country\"] = np.select(condition, results, 0)\n", "        \n", "\n", "        \n", "        \n", "    df = df[['country', 'store', 'tpnb','product_name', \"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]]\n", "        \n", "        \n", "        \n", "\n", "    \n", "    # for r in repl_types:\n", "    #     df[r] = np.where(df[r] == 1, df.sold_units, 0)\n", "    \n", "    df['period'] = y\n", "    \n", "    \n", "    \n", "    df = df.groupby(['country', 'tpnb', 'product_name', 'period'], observed=True)[[\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]].sum().reset_index()\n", "    \n", "    \n", "    df_ce = pd.concat([df_ce, df])\n", "    \n", "    print(f\"Done with processing {y}!\")\n", "    \n", "\n", "\n", "\n", "\n", "cat_df = pd.read_parquet(category_reset_df)[['country', 'tpnb', 'category', 'DIV_DESC']].drop_duplicates()\n", "\n", "\n", "\n", "\n", "df_ce = df_ce.merge(cat_df, on=['country', 'tpnb'], how='left')\n", "\n", "df_ce_total = df_ce.groupby([ 'DIV_DESC','category','tpnb', 'product_name', 'period'], observed=True)[[\"srp\", \"nsrp\", \"full_pallet\", \"mu\", \"split_pallet\"]].sum().reset_index()\n", "df_ce_total['country'] = 'CE'\n", "\n", "df_ce = pd.concat([df_ce, df_ce_total])\n", "\n", "df_ce = df_ce[df_ce.category.notnull()]\n", "\n", "\n", "\n", "        \n", "need_cols = [x for x in df_ce.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]  \n", "\n", "# =============================================================================\n", "# Charts\n", "# =============================================================================\n", "df_catres_sum = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'tpnb', 'product_name', 'category', 'repl_types'] ,columns = \"period\", values = 'value', observed=True).reset_index()\n", "\n", "\n", "\n", "# non_food_food = df_ce[df_ce['DIV_DESC'].isin(['NonFood Grocery', 'Food Grocery'])]\n", "# non_food_food['division'] = np.where(non_food_food['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)', 'Grocery (Food)')\n", "\n", "df_catres_sum_div = df_ce.copy()\n", "\n", "# df_catres_sum_div = pd.concat([df_catres_sum_div, non_food_food])\n", "# df_catres_sum_div['division'] = np.where(df_catres_sum_div['division'] == 'Grocery', 'Grocery (total)', df_catres_sum_div['division'])\n", "\n", "df_catres_sum_div = df_catres_sum_div.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'DIV_DESC','tpnb', 'product_name', 'repl_types'] ,columns = \"period\", values = 'value', observed=True).reset_index()\n", "\n", "\n", "\n", "\n", "cols_need = [x for x in df_catres_sum.columns if x not in [1] + df_catres_sum.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]\n", "\n", "cols_need_div = [x for x in df_catres_sum_div.columns if x not in [1] + df_catres_sum_div.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]\n", "\n", "df_catres_sum_category = df_catres_sum.melt(id_vars=cols_need, var_name='period' ).groupby(['country', 'category', 'repl_types','period'], observed=True)['value'].sum().reset_index()\n", "df_catres_sum_category['value_%'] = df_catres_sum_category['value'] / df_catres_sum_category.groupby(['country', 'category', 'period'], observed=True)['value'].transform('sum')\n", "\n", "df_catres_sum_division = df_catres_sum_div.melt(id_vars=cols_need_div, var_name='period' ).groupby(['country', 'DIV_DESC', 'repl_types','period'], observed=True)['value'].sum().reset_index()\n", "df_catres_sum_division['value_%'] = df_catres_sum_division['value'] / df_catres_sum_division.groupby(['country', 'DIV_DESC', 'period'],observed=True)['value'].transform('sum')\n", "df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)',df_catres_sum_division['DIV_DESC'] )\n", "df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'Food Grocery', 'Grocery (Food)',df_catres_sum_division['DIV_DESC'] )\n", "\n", "df_total = df_catres_sum_division[df_catres_sum_division.country == 'CE'].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()\n", "df_total['value_%'] = df_total['value'] / df_total.groupby(['country', 'period'],observed=True)['value'].transform('sum')\n", "\n", "df_catres_sum_category['category'] = df_catres_sum_category['category'].apply(lambda x: x.replace(\";\",\" \"))\n", "\n", "\n", "def rename_period_columns(df, prefix='p'):\n", "    df['period'] = df['period'].astype(str)  \n", "    df['period'] = prefix + df['period']  \n", "\n", "for x in [df_total, df_catres_sum_category, df_catres_sum_division ]:\n", "    rename_period_columns(x, 'p')\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a5a7cee4-e7b5-4d0a-9bdb-c52385c9272e", "metadata": {}, "outputs": [], "source": ["df_catres_sum_category['year'] = 'last_year'"]}, {"cell_type": "code", "execution_count": null, "id": "4e2f3a8c-e8eb-4f61-9b88-74201e25a7e8", "metadata": {}, "outputs": [], "source": [" \n", "dfs = list(df_catres_sum_category.groupby(['year']))\n", "\n", "\n", "for i,d in enumerate(dfs):\n", "    print(d[0][0])"]}, {"cell_type": "code", "execution_count": null, "id": "2c61f026-48c5-438d-bf85-08d28b3273ca", "metadata": {"scrolled": true}, "outputs": [], "source": ["import plotly.express as px\n", "import plotly.graph_objects as go\n", "\n", "\n", "cont = []\n", "ind=0\n", "for x in df_catres_sum_category['category'].unique().tolist():\n", "    dfs = list(df_catres_sum_category.groupby(['year']))\n", "    \n", "    first_title = dfs[0][0]\n", "    traces = []\n", "    buttons = []\n", "    for i,d in enumerate(dfs):\n", "        \n", "        visible = [False] * len(dfs)\n", "        visible[i] = True\n", "        name = d[0][0]\n", "        traces.append(\n", "            # px.treemap(d[1],\n", "            #            path=['day', 'time', 'sex'],\n", "            #            values='total_bill').update_traces(visible=True if i==0 else False).data[0]\n", "\n", "            px.histogram(df_catres_sum_category[df_catres_sum_category.category.isin([x])], x=\"period\", y=\"value_%\",\n", "                 color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=[\"#5497c7\", \"green\", \"#c74848\", \"goldenrod\", \"#5f48c7\"],\n", "                       opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,\n", "                  height=400,\n", "                 width = 1800,\n", "                 category_orders={\"country\": [\"CZ\", \"HU\", \"SK\", \"CE\"]},\n", "                 title=f'{x}',facet_col_wrap=5, orientation='v').data[0]\n", "            \n", "        )\n", "        buttons.append(dict(label=name,\n", "                            method=\"update\",\n", "                            args=[{\"visible\":visible},\n", "                                  {\"title\":f\"{name}\"}]))\n", "    \n", "    updatemenus = [{'active':0, \"buttons\":buttons}]\n", "    \n", "    fig = go.Figure(data=traces,\n", "                     layout=dict(updatemenus=updatemenus))\n", "    fig.update_layout(title=first_title, title_x=0.5)\n", "    fig.update_traces(textfont_size=12, textangle=0, textposition=\"auto\", cliponaxis=False)\n", "    fig.update_yaxes(matches=None)\n", "    fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=18))\n", "    fig.update_yaxes(title_font_color='white')\n", "    for annotation in fig['layout']['annotations']: \n", "        annotation['textangle']= 0\n", "    # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))\n", "    fig.update_layout(title_text=f\"<b>{x}</b>\")\n", "    # fig.update_layout(bargap=0.5)\n", "    fig.update_layout(legend=dict(\n", "                orientation=\"h\",\n", "                yanchor=\"bottom\",\n", "                y=1.1,\n", "                xanchor=\"right\",\n", "                x=1.1))\n", "    fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "5cd3683c-700c-4aa0-a33f-18c3abfe3303", "metadata": {}, "outputs": [], "source": ["dfs[0][0]"]}, {"cell_type": "code", "execution_count": null, "id": "902697e3-5c89-48ba-a571-13d0bc043cf0", "metadata": {"scrolled": true}, "outputs": [], "source": ["# sorted(df_catres_sum_category['category'].unique().tolist())\n", "cont = []\n", "ind=0\n", "for x in df_catres_sum_category['category'].unique().tolist():\n", "\n", "\n", "        \n", "    \n", "\n", "    fig = px.histogram(df_catres_sum_category[df_catres_sum_category.category.isin([x])], x=\"period\", y=\"value_%\",\n", "                 color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=[\"#5497c7\", \"green\", \"#c74848\", \"goldenrod\", \"#5f48c7\"],\n", "                       opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,\n", "                  height=400,\n", "                 width = 1800,\n", "                 category_orders={\"country\": [\"CZ\", \"HU\", \"SK\", \"CE\"]},\n", "                 title=f'{x}',facet_col_wrap=5, orientation='v')\n", "    fig.update_traces(textfont_size=12, textangle=0, textposition=\"auto\", cliponaxis=False)\n", "    fig.update_yaxes(matches=None)\n", "    fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=18))\n", "    fig.update_yaxes(title_font_color='white')\n", "    for annotation in fig['layout']['annotations']: \n", "        annotation['textangle']= 0\n", "    # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))\n", "    fig.update_layout(title_text=f\"<b>{x}</b>\")\n", "    # fig.update_layout(bargap=0.5)\n", "    fig.update_layout(legend=dict(\n", "                orientation=\"h\",\n", "                yanchor=\"bottom\",\n", "                y=1.1,\n", "                xanchor=\"right\",\n", "                x=1.1))\n", "    # Create dropdown buttons\n", "    dropdown_buttons = [\n", "        dict(label='All', method='update', args=[{'visible': [True for _ in countries]}]),\n", "        *[dict(label=country, method='update', args=[{'visible': [country==c for c in countries]}]) for country in countries]\n", "    ]\n", "    # Add dropdown menu to layout\n", "    fig.update_layout(\n", "        updatemenus=[\n", "            go.layout.Updatemenu(buttons=dropdown_buttons)\n", "        ],\n", "        title='Sales',\n", "        xaxis_title='Division',\n", "        yaxis_title='Sales'\n", "    )\n", "    \n", "    fig.show()\n", "    # cont.append(fig)\n", "    #fig.show()\n", "    \n", "\n", "        \n", "        \n", "\n", "# for x in sorted(df_catres_sum_division['DIV_DESC'].unique().tolist()):\n", "    \n", "\n", "        \n", "#     fig = px.histogram(df_catres_sum_division[df_catres_sum_division.DIV_DESC.isin([x])], x=\"period\", y=\"value_%\",\n", "#                   color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=[\"#5497c7\", \"green\", \"#c74848\", \"goldenrod\", \"#5f48c7\"],\n", "#                         opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,\n", "#                   height=400,\n", "#                   width = 1800,\n", "#                   category_orders={\"country\": [\"CZ\", \"HU\", \"SK\", \"CE\"]},\n", "#                   title=f'{x}',facet_col_wrap=5, orientation='v')\n", "#     fig.update_traces(textfont_size=12, textangle=0, textposition=\"auto\", cliponaxis=False)\n", "#     fig.update_yaxes(matches=None)\n", "#     fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=18))\n", "#     fig.update_yaxes(title_font_color='black')\n", "#     for annotation in fig['layout']['annotations']: \n", "#         annotation['textangle']= 0\n", "#     # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))\n", "#     fig.update_layout(title_text=f\"<b>{x}</b>\", template = 'plotly_dark') #, bargap=0.5)\n", "#     fig.update_layout(legend=dict(\n", "#                 orientation=\"h\",\n", "#                 yanchor=\"bottom\",\n", "#                 y=1.1,\n", "#                 xanchor=\"right\",\n", "#                 x=1.1))\n", "#     cont.append(fig)\n", "    \n", "\n", "\n", "# #Total CE   \n", "# fig = px.histogram(df_total, x=\"period\", y=\"value_%\",\n", "#               color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=[\"#5497c7\", \"green\", \"#c74848\", \"goldenrod\", \"#5f48c7\"],\n", "#                     opacity=0.8, facet_row_spacing =0.1,# color_discrete_sequence=px.colors.qualitative.Alphabet,\n", "#               height=400,\n", "#               width = 1800,\n", "#               category_orders={\"country\": [\"CE\"]},\n", "#               title='CE Total',facet_col_wrap=5, orientation='v')\n", "# fig.update_traces(textfont_size=12, textangle=0, textposition=\"auto\", cliponaxis=False)\n", "# fig.update_yaxes(matches=None)\n", "# fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=18))\n", "# fig.update_yaxes(title_font_color='black')\n", "# for annotation in fig['layout']['annotations']: \n", "#     annotation['textangle']= 0\n", "# # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))\n", "# fig.update_layout(title_text=\"<b>CE Total</b>\", template = 'plotly_dark', bargap=0.5) #, bargap=0.5)\n", "# fig.update_layout(legend=dict(\n", "#             orientation=\"h\",\n", "#             yanchor=\"bottom\",\n", "#             y=1.1,\n", "#             xanchor=\"right\",\n", "#             x=1.1))\n", "# cont.append(fig)\n", "    \n", "    \n", "# html_fname = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\CategoryReset\\Category_Reset_chart_by_TPN_numbers_CE.html\"\n", "# pdf_fname = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\CategoryReset\\Category_Reset_chart_by_TPN_numbers_CE.pdf\"\n", "# plotly_figs = cont\n", "# combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "#                                 separator=None, auto_open=False)\n", "\n", "\n", "\n", "\n", "# #PDF converter\n", "# figures = plotly_figs\n", "# image_list = [pio.to_image(fig, format='png', scale=1.5) for fig in figures] #width=1440, height=900, scale=1.5\n", "# for index, image in enumerate(image_list):\n", "#     with io.BytesIO() as tmp:\n", "#         tmp.write(image)  # write the image bytes to the io.BytesIO() temporary object\n", "#         image = Image.open(tmp).convert('RGB')  # convert and overwrite 'image' to prevent creating a new variable\n", "#         image_list[index] = image  # overwrite byte image data in list, replace with PIL converted image data\n", "\n", "# # pop first item from image_list, use that to access .save(). Then refer back to image_list to append the rest\n", "# image_list.pop(0).save(pdf_fname, 'PDF',\n", "#                        save_all=True, append_images=image_list, resolution=100.0)  # TODO improve resolution\n", "\n", "# # pio.write_image(fig, pdf_fname, format=\"pdf\", engine=\"kaleido\")\n", "\n", "\n", "# print(\"\\n###############\")\n", "# print(\"Charts are ready!\")\n", "# print(\"###############\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "88ffc87f-4c3c-4893-a91b-614c67541de2", "metadata": {}, "outputs": [], "source": ["from io import StringIO\n", "import pandas as pd\n", "import plotly.express as px\n", "\n", "segment_data = StringIO(\"\"\"Segment|PrimaryBodyType|Month|Year|NumSold|MonthNum(Index)|Compact|\n", "A|SUV|January|2021|254391|0|Compact| \n", "B|SUV|January|2019|249913|0|Midsize| \n", "C|SUV|January|2021|248762|0|Midsize| \n", "D|SUV|January|2020|239102|0|Compact| \n", "E|SUV|January|2020|233614|0|Compact|\n", "\"\"\")\n", "\n", "segments = pd.read_csv(segment_data, sep=\"|\")\n", "segments[\"Year\"] = segments[\"Year\"].astype(str)\n", "\n", "segbar = px.bar(segments, x=segments.Month, y=segments.NumSold, color=segments.NumSold,\n", "color_continuous_scale='inferno', custom_data=[segments.Segment, segments.PrimaryBodyType, segments.Month, segments.Year], barmode='group')\n", "segbar.update_traces(hovertemplate='<b>Segment:</b> %{customdata[0]} %{customdata[1]}<br><b>Units Sold:</b> %{y:,.0f}<br>Date: %{customdata[2]} %{customdata[3]}')\n", "\n", "\n", "segbar.update_layout(\n", "    updatemenus=[\n", "        dict(\n", "            type=\"dropdown\",\n", "            direction=\"down\",\n", "            bgcolor='Dark Blue',\n", "            buttons=list(\n", "                [\n", "                dict(\n", "                    label=\"(All)\",\n", "                    method=\"update\",\n", "                    args=[{\"y\": [segments.NumSold]},\n", "                    {\"x\": [segments.Month]}],\n", "         ),\n", "            dict(\n", "                label=\"2021\",\n", "                method=\"update\",\n", "                args=[{\"y\": [segments.loc[segments['Year'] == \"2021\", \"NumSold\"]]},\n", "                      {\"x\": [segments.loc[segments['Year'] == \"2021\", \"Month\"]]}]\n", "            )\n", "            ]),\n", "        ), dict(\n", "            type=\"dropdown\",\n", "            direction=\"down\"\n", "    )\n", "], template='plotly_dark')\n", "segbar.show()"]}, {"cell_type": "code", "execution_count": null, "id": "9169d14b-3059-43cd-8740-22d23f38c1d4", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "\n", "def gen_bar(df, title, sub):\n", "    \"\"\"\n", "    Displays an interactive plotly graph using the given column and dataframe.\n", "    \n", "    df: dataframe containing relevant data\n", "    col: data to be displayed along x-axis\n", "    title: title (and subtitle) for given visualization\n", "    \"\"\"\n", "    # Top 10 men and women\n", "    #comb = df.sort_values(col, ascending=False)[:20]\n", "    comb = df[:20]\n", "    \n", "    # Define colors\n", "    color_discrete_map = {\"M\": \"#10baee\", \"F\": \"#ff007e\"}\n", "    \n", "    # Define plot\n", "    fig=go.Figure()\n", "    for k,r in enumerate(df.columns[4:]):\n", "        #dfp = comb[comb['Name'] == r]\n", "        dfp = comb.sort_values(r, ascending=False)\n", "        g = dfp['Gender'].values\n", "        print(g)\n", "        colors = [color_discrete_map[x] for x in g]\n", "        fig.add_traces(\n", "            go.Bar(x=dfp[r], \n", "                   y=dfp['Name'],\n", "                   customdata=dfp[r].values,\n", "                   name='', \n", "                   marker_color=colors,#color_discrete_map[g], \n", "                   orientation='h',\n", "                   hovertemplate=\"<b>%{y}</b><br>%{customdata}: %{x}\",\n", "                   visible=True if k == 0 else False\n", "                   ))\n", "        \n", "    \n", "# Define buttons for dropdown\n", "    col_opts = list(df.columns[4:])\n", "    buttons_opts = []\n", "    for i, opt in enumerate(col_opts):\n", "        args = [False] * len(col_opts)\n", "        args[i] = True\n", "        buttons_opts.append(\n", "            dict(\n", "                method='restyle',\n", "                label=opt,\n", "                args=[{\n", "                    'visible': args, #this is the key line!\n", "                    'title': opt,\n", "                    'showlegend': <PERSON><PERSON><PERSON>\n", "                }]\n", "            )\n", "        )\n", "        \n", "    # Styling\n", "    title = f\"{title}<br><sup>{sub}\"\n", "    fig.update_layout(\n", "        updatemenus = [go.layout.Updatemenu(\n", "            active=0,#col_opts.index(col),\n", "            buttons=buttons_opts,\n", "            x=1.12,\n", "            xanchor='right',\n", "            y=1.1,\n", "            yanchor='top'\n", "            )],\n", "        yaxis={\n", "            'autorange': \"reversed\",\n", "            'showline': True,\n", "            'linecolor': 'black',\n", "            'title': None\n", "        },\n", "        title=dict(text=title, font=dict(size=30)),\n", "        showlegend=False,\n", "        width=1000,\n", "        height=600,\n", "        plot_bgcolor='#f0f0f0',\n", "        paper_bgcolor='#f0f0f0',\n", "        xaxis_title=None,\n", "        margin=dict(l=85, r=85, t=95, b=45)\n", "    )\n", "    # print(fig.data)\n", "    fig.show()#config=config\n", "    \n", "title = \"Top 20 Climbers in IFSC\"\n", "sub = f\"Based on total number of given value\"\n", "gen_bar(all_boulder_podiums, title, sub)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}