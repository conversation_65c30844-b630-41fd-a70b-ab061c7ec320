import pyautogui as pa
import subprocess as sp
import time
import os
import pandas as pd
import numpy as np
import pyodbc
from datetime import datetime


path = r"C:\Users\<USER>\OneDrive - Tesco\#MODELS\###OTHER\putty.exe"
hu = "pr4g001.hu.tesco-europe.com"
sk = "pr2g001.sk.tesco-europe.com"
cz = "pr1g001.cz.tesco-europe.com"
login_name = "phrubos"
login_pass = "Harslakokert2023"
report_name = "FI_packaging_detail_p.sql"

start_date = "03-JUL-23"
end_date = "23-JUL-23"
stores = "%"
movement_code = "160"

laptop_screen = False


start_date_to_calc = datetime.strptime(start_date, "%d-%b-%y")
end_date_to_calc = datetime.strptime(end_date, "%d-%b-%y")

delta = end_date_to_calc - start_date_to_calc
num_weeks = (delta.days +1) / 7
if num_weeks == 0:
    num_weeks = 1

screen = pa.size()



def laptopScreen_fix(screen, image_name):
    global laptop_screen
    
    if laptop_screen:
        
        if screen == (1920, 1080):
            image_name_laptop = image_name[:-4]
            image_name_laptop = image_name_laptop + "_laptop.PNG"
        if screen != (1920, 1080):
            image_name_laptop = image_name
        
    else:
        if screen != (1920, 1080):
            image_name_laptop = image_name[:-4]
            image_name_laptop = image_name_laptop + "_laptop.PNG"
        if screen == (1920, 1080):
            image_name_laptop = image_name

    return image_name_laptop


image_enter = "images/text_input_field.PNG"
image_no = "images/No_button.PNG"
image_login = "images/login.PNG"
image_pass = "images/password.PNG"
image_end = "images/script_finished.PNG"

for c in (cz, hu, sk):

    sp.Popen(path)

    while True:
        try:
            x, y = pa.locateCenterOnScreen(laptopScreen_fix(screen, image_enter))
            pa.click(x, y)
            break
        except:
            time.sleep(1)
            print(f"{c[8:10]} Loading...")
            pass

    pa.typewrite(c)
    pa.press("enter")

    try:
        time.sleep(1)
        x, y = pa.locateCenterOnScreen(laptopScreen_fix(screen, image_no))
        pa.click(x, y)
    except:
        pass

    time.sleep(2)
    x, y = pa.locateCenterOnScreen(laptopScreen_fix(screen, image_login))
    pa.click(x, y)
    pa.typewrite(login_name)
    pa.press("enter")

    time.sleep(2)
    x, y = pa.locateCenterOnScreen(laptopScreen_fix(screen, image_pass))
    pa.click(x, y)
    pa.typewrite(login_pass)
    pa.press("enter")

    time.sleep(1)
    pa.press("enter")

    time.sleep(1)
    pa.press("enter")

    time.sleep(2)
    pa.press("5")
    pa.press("enter")

    time.sleep(2)
    pa.typewrite(report_name)
    pa.press("enter")

    for x in (start_date, end_date, stores, movement_code):
        time.sleep(1)
        pa.typewrite(x)
        pa.press("enter")

    time.sleep(1)
    while True:
        try:
            x, y = pa.locateCenterOnScreen(laptopScreen_fix(screen, image_end))
            pa.click(x, y)
            pa.press("enter")
            print(f"country:{c[8:10]} done!!!\n")
            break
        except:
            time.sleep(1)
            print("Downloading...")
            pass

    time.sleep(2)

    if c == cz:
        pa.typewrite("8")
        pa.press("enter")
        time.sleep(1)
        pa.typewrite("y")
        pa.press("enter")

    if c == hu:
        pa.typewrite("8")
        pa.press("enter")
        time.sleep(1)
        pa.typewrite("y")
        pa.press("enter")

    if c == sk:
        pa.typewrite("8")
        pa.press("enter")
        time.sleep(1)
        pa.typewrite("y")
        pa.press("enter")


hu_folder = r"\\czprgvmfs04\GOLD_Reports_adhoc\HU\OP\phrubos"
cz_folder = r"\\czprgvmfs04\GOLD_Reports_adhoc\CZ\OP\phrubos"
sk_folder = r"\\czprgvmfs04\GOLD_Reports_adhoc\SK\OP\phrubos"

for folder in (hu_folder, cz_folder, sk_folder):

    # Dictionary for extension mappings
    rename_dict = {"lst": "txt"}
    for filename in os.listdir(folder):

        # Get the extension and remove . from it
        base_file, ext = os.path.splitext(filename)
        ext = ext.replace(".", "")

        # If you find the extension to rename
        if ext in rename_dict:
            # Create the new file name
            new_ext = rename_dict[ext]
            new_file = base_file + "." + new_ext

            # Create the full old and new path
            old_path = os.path.join(folder, filename)
            new_path = os.path.join(folder, new_file)

            # Rename the file
            os.rename(old_path, new_path)
print("Done with changing .lst to .txt extensions\n")


ce_df = pd.DataFrame()
for x in ("CZ", "HU", "SK"):
    try:
        df = pd.read_csv(
            fr"\\czprgvmfs04\GOLD_Reports_adhoc\{x}\OP\phrubos\FI_packaging_detail_p{start_date}{end_date}%160.txt",
            sep="|",
            low_memory=False,
            skiprows=15,
        )
        df.columns = df.columns.str.replace(" ", "")
        df = df[["Site", "date", "Tpn", "in"]].apply(lambda x: x.str.replace(" ", ""))
        df[["Site", "Tpn", "in"]] = (
            df[["Site", "Tpn", "in"]]
            .apply(pd.to_numeric, errors="coerce")
            .replace(np.nan, 0)
        )
        df[["Site", "in"]] = df[["Site", "in"]].astype("int")
        df["date"] = (
            df["date"]
            .apply(pd.to_datetime, errors="coerce", format="%d-%b-%y")
            .dt.strftime("%A")
        )
        df.rename(columns={"date": "day"}, inplace=True)
        df["Tpn"] = df["Tpn"].astype("int64")
        df.columns = [i.lower() for i in df.columns]
        df.rename(columns={"site": "store"}, inplace=True)
        df = df.groupby(["store", "day", "tpn"])["in"].sum().reset_index()
        ce_df = pd.concat([ce_df, df])
    except:
        pass

condition = [
    ce_df["store"].astype(str).str.match("^1"),
    ce_df["store"].astype(str).str.match("^2"),
    ce_df["store"].astype(str).str.match("^4"),
]
results = ["CZ", "SK", "HU"]
ce_df["country"] = np.select(condition, results, 0)
print("Done with getting the CE file together\n")

conn = pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()

print("Starting to fetch TPN's name from Hadoop...\n")

t = tuple(ce_df["tpn"].unique())

tpns = f"{t}"

sql = """ SELECT cntr_code AS country, slad_tpnb AS tpnb, slad_tpn AS tpn, hier.pmg AS pmg, slad_long_des as product_name
        FROM DM.dim_artgld_details mstr
        JOIN tesco_analysts.hierarchy_spm hier
        ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0")
        AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0")
        AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0")
        AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0")
        AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
        WHERE slad_tpn in {tpns}
        AND cntr_code <> "PL"
        AND dmat_sgr_des_en <> "Do not use"
        GROUP BY cntr_code, hier.pmg, slad_tpnb,slad_tpn,  slad_long_des
        
        """.format(
    tpns=tpns
)


art_gold = pd.read_sql(sql, conn)


print("Hadoop part done\n")

art_gold["tpn"] = art_gold["tpn"].astype("int64")
ce_df_final = ce_df.merge(art_gold, on=["country", "tpn"], how="left")
ce_df_final = ce_df_final[ce_df_final.store != 0]
# ce_df_final["in"] = ce_df_final["in"] / num_weeks
missing_product_tpn = list(set(ce_df_final[ce_df_final.product_name.isnull()]["tpn"]))

ce_df_final = ce_df_final.groupby(['country', 'store', 'tpn', 'tpnb', 'pmg', 'product_name'])['in'].sum().reset_index()


# CSD data

print("CSD Data Fetching...!!!!\n")

start_date_csd = int(start_date_to_calc.strftime("%Y%m%d"))
end_date_csd = int(end_date_to_calc.strftime("%Y%m%d"))

csd_part = pd.DataFrame()
for x, y in zip(['hu','sk','cz'], ['HU','SK', 'CZ']):
    
    sql = """ 
            SELECT b.cntr_code AS country,
            a.site AS store,
            cal.dtdw_day_desc_en as day,
            a.ret AS rc_code,
            sum(a.qty) *-1 AS in_csd, 
            b.slad_tpnb AS tpnb,
            b.slad_long_des AS product_name
            FROM pos{x}.t001csd a
            LEFT JOIN DM.dim_artgld_details b
            ON a.ean = lpad(b.slem_ean,14,0)
            LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
            WHERE a.part_col BETWEEN {start_date_csd} AND {end_date_csd}
            AND b.cntr_code = '{y}'
            AND a.ret = 6
            GROUP BY b.cntr_code, a.site, cal.dtdw_day_desc_en, a.ret, a.qty, b.slad_tpnb, b.slad_long_des
            """.format(
        x=x, y=y, start_date_csd=start_date_csd, end_date_csd=end_date_csd
    )


    # art_gold = pd.read_sql(sql, conn)
    
    csd_part = pd.concat([csd_part, pd.read_sql(sql, conn)])


csd_part_ = csd_part.copy()
# csd_part_["in_csd"] = csd_part["in_csd"] / num_weeks
csd_part_ = csd_part_.groupby(['store','tpnb'])['in_csd'].sum().reset_index()
ce_df_final = ce_df_final.merge(csd_part_[['store','tpnb','in_csd']], on=['store', 'tpnb'], how='left')
ce_df_final['in_csd'] = ce_df_final['in_csd'].fillna(0)


# ce_df_final["in_csd"] = csd_part["in_csd"] / num_weeks

ce_df_final[['in_csd','in']] /= num_weeks

ce_df_final['in_machine'] = ce_df_final['in'] - ce_df_final['in_csd']

ce_df_final['in_machine'] = np.where(ce_df_final['in_machine'] < 0, 0, ce_df_final['in_machine'])




print("Bottle Return Data Fetching Done!!!!\n")
