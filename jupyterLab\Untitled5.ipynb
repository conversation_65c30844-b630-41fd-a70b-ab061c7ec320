{"cells": [{"cell_type": "code", "execution_count": 39, "id": "03162dcc-8bea-4fd7-908f-40f109b1f6dc", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import polars as pl\n", "\n", "\n", "\n", "a = pl.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\Calculations\\2024_04\\2024_Q2_PD_answers bakery.xlsx\",engine=\"calamine\").to_pandas()"]}, {"cell_type": "code", "execution_count": 40, "id": "92b8b02b-3324-477f-b980-ad3fd9474ebe", "metadata": {}, "outputs": [], "source": ["a = a[a.Owner == 'Peti']"]}, {"cell_type": "code", "execution_count": 41, "id": "bf125659-326b-48e1-ab38-9141215b489d", "metadata": {}, "outputs": [], "source": ["a.rename(columns={'Answer':'1_Answer', 'Store comment':'2_Store comment', 'Anna comment':'3_<PERSON> comment'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 42, "id": "a174949c-617a-4ba3-b7bb-c5ca5508914c", "metadata": {}, "outputs": [], "source": ["a['4_asis'] = 0\n", "a['5_diff'] = 0\n", "a['6_to_model'] = 0"]}, {"cell_type": "code", "execution_count": 34, "id": "9b196fb5-22a9-4c9e-bee6-862330858675", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Shop</th>\n", "      <th>ID</th>\n", "      <th>Category</th>\n", "      <th>Owner</th>\n", "      <th>Question</th>\n", "      <th>Description</th>\n", "      <th>1_Answer</th>\n", "      <th>2_Store comment</th>\n", "      <th>3_<PERSON> comment</th>\n", "      <th>4_asis</th>\n", "      <th>5_diff</th>\n", "      <th>6_to_model</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>167</th>\n", "      <td>41002</td>\n", "      <td>bak_don</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Doughnut furniture</td>\n", "      <td>Is there Doughnut shopfloor furniture (a kind ...</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168</th>\n", "      <td>41002</td>\n", "      <td>bak_vol</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>ISB - Number of Volumetric divider</td>\n", "      <td>ISB - Number of Volumetric divider</td>\n", "      <td>0.0</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169</th>\n", "      <td>41002</td>\n", "      <td>bak_rac</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>ISB - Number of Rack oven (for ISB products)</td>\n", "      <td>ISB - Number of Rack oven (for ISB products)</td>\n", "      <td>0.0</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>41002</td>\n", "      <td>bak_mon</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Number of MONO ovens (for BO products)</td>\n", "      <td>Number of MONO ovens (for BO products)</td>\n", "      <td>0.0</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>171</th>\n", "      <td>41002</td>\n", "      <td>bak_tray</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Total tray capacity of the MONO ovens in the s...</td>\n", "      <td>Total tray capacity of the MONO ovens in the s...</td>\n", "      <td>0.0</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Shop        ID Category Owner  \\\n", "167  41002   bak_don   <PERSON>   \n", "168  41002   bak_vol   <PERSON><PERSON>  Peti   \n", "169  41002   bak_rac   <PERSON><PERSON>   \n", "170  41002   bak_mon   Bakery  Peti   \n", "171  41002  bak_tray   <PERSON><PERSON>i   \n", "\n", "                                              Question  \\\n", "167                                 Doughnut furniture   \n", "168                 ISB - Number of Volumetric divider   \n", "169       ISB - Number of Rack oven (for ISB products)   \n", "170             Number of MONO ovens (for BO products)   \n", "171  Total tray capacity of the MONO ovens in the s...   \n", "\n", "                                           Description  1_Answer  \\\n", "167  Is there Doughnut shopfloor furniture (a kind ...       NaN   \n", "168                 ISB - Number of Volumetric divider       0.0   \n", "169       ISB - Number of Rack oven (for ISB products)       0.0   \n", "170             Number of MONO ovens (for BO products)       0.0   \n", "171  Total tray capacity of the MONO ovens in the s...       0.0   \n", "\n", "    2_Store comment 3_<PERSON> comment  4_asis  5_diff  6_to_model  \n", "167               -              -       0       0           0  \n", "168               -              -       0       0           0  \n", "169               -              -       0       0           0  \n", "170               -              -       0       0           0  \n", "171               -              -       0       0           0  "]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["a.head()"]}, {"cell_type": "code", "execution_count": 43, "id": "998a4633-12aa-4956-bad4-001ad8a54a70", "metadata": {"scrolled": true}, "outputs": [], "source": ["a = a.pivot(index=[\"Shop\", 'Category'], columns=['ID', 'Question'], values=['1_Answer',\t'2_Store comment',\t'3_<PERSON> comment',\t'4_asis',\t'5_diff',\t'6_to_model'] )"]}, {"cell_type": "code", "execution_count": 36, "id": "ac6335f4-7cb4-4017-bccb-476f9ee11afa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 44, "id": "8a73316e-3e1c-4da7-b0ca-01913eb17a34", "metadata": {}, "outputs": [], "source": ["# Flatten MultiIndex columns to single level\n", "a.columns = [f\"{col[1]}_{col[0]}\" for col in a.columns if col not in ['Shop','Category']]\n", "\n", "# Reset index to move multi-index into columns\n", "a.reset_index(inplace=True)\n", "\n", "a = a.sort_index(axis=1)\n", "\n", "a.to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\Calculations\\2024_04\\bakery_profiles.xlsx\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "abfe8abf-ac43-4a79-b971-690a3adbe030", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 25, "id": "0ee2c36d-8716-4693-bea9-94deef589404", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Shop</th>\n", "      <th>Category</th>\n", "      <th>bak_don_Answer</th>\n", "      <th>bak_vol_Answer</th>\n", "      <th>bak_rac_Answer</th>\n", "      <th>bak_mon_Answer</th>\n", "      <th>bak_tray_Answer</th>\n", "      <th>bak_deck_Answer</th>\n", "      <th>bak_tab_Answer</th>\n", "      <th>bak_chill_Answer</th>\n", "      <th>...</th>\n", "      <th>bak_cool_2_<PERSON> comment</th>\n", "      <th>bak_gr2prd_<PERSON> comment</th>\n", "      <th>bak_no_of_debag_<PERSON> comment</th>\n", "      <th>bak_debag_cart_<PERSON> comment</th>\n", "      <th>bak_debag_tray_<PERSON> comment</th>\n", "      <th>bak_no_of_<PERSON><PERSON><PERSON>_<PERSON> comment</th>\n", "      <th>bak_<PERSON><PERSON><PERSON>_cart_Anna comment</th>\n", "      <th>bak_ram<PERSON><PERSON>_tray_<PERSON> comment</th>\n", "      <th>bak_coffin_<PERSON> comment</th>\n", "      <th>bak_new_eq_<PERSON> comment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>11002</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>40.0</td>\n", "      <td>40.0</td>\n", "      <td>...</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>11003</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>11004</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>87.0</td>\n", "      <td>96.0</td>\n", "      <td>...</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>11005</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>160.0</td>\n", "      <td>200.0</td>\n", "      <td>...</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 107 columns</p>\n", "</div>"], "text/plain": ["    Shop Category bak_don_Answer bak_vol_Answer bak_rac_Answer bak_mon_Answer  \\\n", "0  11001   Bakery            NaN            NaN            NaN            NaN   \n", "1  11002   Bakery            NaN            1.0            3.0            0.0   \n", "2  11003   Bakery            NaN            NaN            NaN            NaN   \n", "3  11004   Bakery            NaN            1.0            3.0            0.0   \n", "4  11005   Bakery            NaN            2.0            3.0            0.0   \n", "\n", "  bak_tray_Answer bak_deck_Answer bak_tab_Answer bak_chill_Answer  ...  \\\n", "0             NaN             NaN            NaN              NaN  ...   \n", "1             0.0             0.0           40.0             40.0  ...   \n", "2             NaN             NaN            NaN              NaN  ...   \n", "3             0.0             0.0           87.0             96.0  ...   \n", "4             0.0             0.0          160.0            200.0  ...   \n", "\n", "  bak_cool_2_<PERSON> comment bak_gr2prd_<PERSON> comment  \\\n", "0                       -                       -   \n", "1                       -                       -   \n", "2                       -                       -   \n", "3                       -                       -   \n", "4                       -                       -   \n", "\n", "  bak_no_of_debag_<PERSON> comment bak_debag_cart_<PERSON> comment  \\\n", "0                            -                           -   \n", "1                            -                           -   \n", "2                            -                           -   \n", "3                            -                           -   \n", "4                            -                           -   \n", "\n", "  bak_debag_tray_<PERSON> comment bak_no_of_ram<PERSON><PERSON>_<PERSON> comment  \\\n", "0                           -                               -   \n", "1                           -                               -   \n", "2                           -                               -   \n", "3                           -                               -   \n", "4                           -                               -   \n", "\n", "  bak_ram<PERSON><PERSON>_cart_<PERSON> comment bak_ramal<PERSON>_tray_Anna comment  \\\n", "0                              -                              -   \n", "1                              -                              -   \n", "2                              -                              -   \n", "3                              -                              -   \n", "4                              -                              -   \n", "\n", "  bak_coffin_<PERSON> comment bak_new_eq_<PERSON> comment  \n", "0                       -                       -  \n", "1                       -                       -  \n", "2                       -                       -  \n", "3                       -                       -  \n", "4                       -                       -  \n", "\n", "[5 rows x 107 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# Flatten MultiIndex columns to single level\n", "a.columns = [f\"{col[1]}_{col[0]}\" for col in a.columns if col not in ['Shop','Category']]\n", "\n", "# Reset index to move multi-index into columns\n", "a.reset_index(inplace=True)\n", "a.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "cbe80cde-4c49-4877-8065-d80e6dea051e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Example DataFrame (replace this with your DataFrame)\n", "data = {\n", "    'Shop': ['A', 'A', 'B', 'B'],\n", "    'Category': ['X', 'X', 'Y', 'Y'],\n", "    'ID': ['a', 'a', 'b', 'b'],\n", "    'Question': ['Q1', 'Q2', 'Q1', 'Q2'],\n", "    'Answer': [20, 25, 30, 35],\n", "    'Store comment': ['Still progress', 'Good work', 'Needs improvement', 'Almost there'],\n", "    'Anna comment': ['Focus on it', 'Keep it up', 'Try harder', 'You can do it']\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "\n", "# Pivot the DataFrame\n", "pivot_df = df.pivot_table(index=[\"Shop\", \"Category\", \"ID\", \"Question\"],\n", "                          columns=None,  # No additional grouping\n", "                          values=[\"Answer\", \"Store comment\", \"Anna comment\"],\n", "                          aggfunc=lambda x: x)  # Use lambda to keep all values (no aggregation)\n", "\n", "# Flatten MultiIndex columns to single level\n", "pivot_df.columns = [f\"{col[1]}_{col[0]}\" for col in pivot_df.columns]\n", "\n", "# Reset index to move multi-index into columns\n", "pivot_df.reset_index(inplace=True)\n", "\n", "# # Reorder columns to place related values next to each other\n", "# desired_order = ['Shop', 'Category', 'ID', 'Question', \n", "#                  'Answer', 'Store comment', 'Anna comment']\n", "# pivot_df = pivot_df[desired_order]\n", "\n", "# # Display the resulting DataFrame\n", "# print(pivot_df)\n"]}, {"cell_type": "code", "execution_count": 15, "id": "73a7acb7-da08-4eed-a257-bfdee7edc22e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Shop</th>\n", "      <th>Category</th>\n", "      <th>ID</th>\n", "      <th>Question</th>\n", "      <th>n_A</th>\n", "      <th>n_A</th>\n", "      <th>t_S</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A</td>\n", "      <td>X</td>\n", "      <td>a</td>\n", "      <td>Q1</td>\n", "      <td>Focus on it</td>\n", "      <td>20</td>\n", "      <td>Still progress</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A</td>\n", "      <td>X</td>\n", "      <td>a</td>\n", "      <td>Q2</td>\n", "      <td>Keep it up</td>\n", "      <td>25</td>\n", "      <td>Good work</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>B</td>\n", "      <td>Y</td>\n", "      <td>b</td>\n", "      <td>Q1</td>\n", "      <td>Try harder</td>\n", "      <td>30</td>\n", "      <td>Needs improvement</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>B</td>\n", "      <td>Y</td>\n", "      <td>b</td>\n", "      <td>Q2</td>\n", "      <td>You can do it</td>\n", "      <td>35</td>\n", "      <td>Almost there</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Shop Category ID Question            n_A  n_A                t_S\n", "0    A        X  a       Q1    Focus on it   20     Still progress\n", "1    A        X  a       Q2     Keep it up   25          Good work\n", "2    B        Y  b       Q1     Try harder   30  Needs improvement\n", "3    B        Y  b       Q2  You can do it   35       Almost there"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["pivot_df.head()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}