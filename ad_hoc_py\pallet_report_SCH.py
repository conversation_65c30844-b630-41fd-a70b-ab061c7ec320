import pandas as pd
import pyodbc

query = """

select * from tesco_analysts.tbl_altmu_dpn_final
where country_code='HU'
and store_nr = 1520
and part_col between 20230709 and 20230728
limit 10000


"""

with pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
) as conn:

    df_total_case = pd.read_sql_query(query, conn)
    
    
    
    
q = """

select * from sch_analysts.tbl_ce_rangetool_grade_alaptabla_wocluster
limit 100

"""

with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:

    df_total_case = pd.read_sql_query(q, conn)