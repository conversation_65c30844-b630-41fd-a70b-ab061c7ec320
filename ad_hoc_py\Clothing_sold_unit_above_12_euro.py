import pyodbc
import pandas as pd
import polars as pl

import time

start = time.time()


with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:
    
    
    
    
    ruha = """
    
SELECT
    mstr.cntr_code AS country,
    --d.pmg AS pmg,
    --mstr.slad_tpnb as tpnb,
    SUM(sunit.slsms_unit) AS total_sold_units,
    SUM(CASE 
        WHEN mstr.cntr_code = 'HU' AND stock.slstks_price >= 4800 THEN sunit.slsms_unit
        WHEN mstr.cntr_code = 'SK' AND stock.slstks_price >= 12 THEN sunit.slsms_unit  
        WHEN mstr.cntr_code = 'CZ' AND stock.slstks_price >= 300 THEN sunit.slsms_unit
        ELSE 0
    END) AS units_sold_above_12_local_currency,
    AVG(stock.slstks_price) as avg_item_price
FROM dw.sl_sms sunit 
JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id 
    AND mstr.cntr_id = sunit.slsms_cntr_id
JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id 
    AND stores.dmst_store_id = sunit.slsms_dmst_id
JOIN tesco_analysts.hierarchy_spm d ON mstr.dmat_div_code = LPAD(d.div_code,4,"0") 
    AND mstr.dmat_dep_code = LPAD(d.dep_code,4,"0") 
    AND mstr.dmat_sec_code = LPAD(d.sec_code,4,"0") 
    AND mstr.dmat_grp_code = LPAD(d.grp_code,4,"0") 
    AND mstr.dmat_sgr_code = LPAD(d.sgr_code,4,"0") 
LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
JOIN dw.sl_stocks stock ON stock.slstks_dmst_id = sunit.slsms_dmst_id 
    AND stock.slstks_cntr_id = sunit.slsms_cntr_id
    AND stock.slstks_dmat_id = sunit.slsms_dmat_id
    AND stock.part_col = sunit.part_col

WHERE stores.cntr_code IN ('SK', 'CZ', 'HU')
    AND sunit.part_col BETWEEN '20240527' AND '20240901'
    AND SUBSTRING(d.pmg, 1, 3) = 'CLG'
    AND sunit.slsms_unit > 0

GROUP BY mstr.cntr_code
    
    
    
    """
    
    
    df = pl.read_database(ruha, conn)






