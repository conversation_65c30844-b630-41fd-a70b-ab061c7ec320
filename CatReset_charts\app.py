import dash
from dash import html, dcc
import dash_bootstrap_components as dbc
from dash.dependencies import Input, Output, State
import plotly.io as pio

#Import charts functions from Charts.py.  Make sure the file is named correctly and in the same directory
from Charts import tpnb_charts, shelfcap_charts, sales_charts


# Set default template for all plotly figures
pio.templates.default = "plotly_dark"

# Initialize the Dash app with dark theme
app = dash.Dash(
    __name__,
    external_stylesheets=[
        dbc.themes.DARKLY,
        "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap",
    ],
    meta_tags=[
        {"name": "viewport", "content": "width=device-width, initial-scale=1.0"}
    ],
)

# Custom CSS for dark theme and modern styling
app.index_string = '''<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>Category Reset Dashboard</title>
        {%favicon%}
        {%css%}
        <style>
            body {
                background-color: #121212;
                color: #e0e0e0;
                font-family: 'Inter', sans-serif;
            }
            .card {
                background-color: #1e1e1e;
                border: 1px solid #2d2d2d;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .nav-link {
                color: #e0e0e0 !important;
            }
            .nav-link.active {
                background-color: #2d2d2d !important;
                border-radius: 4px;
            }
            .loading-spinner {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>'''

# Navigation bar
navbar = dbc.NavbarSimple(
    children=[
        dbc.NavItem(dbc.NavLink("TPNB Analysis", href="#", id="tpnb-link", active=True)),
        dbc.NavItem(dbc.NavLink("Shelf Capacity", href="#", id="shelfcap-link")),
        dbc.NavItem(dbc.NavLink("Sales Analysis", href="#", id="sales-link")),
    ],
    brand="Category Reset Dashboard",
    brand_href="#",
    color="dark",
    dark=True,
    className="mb-4",
)

# Main layout
app.layout = html.Div([
    navbar,
    dbc.Container([
        dbc.Row([
            dbc.Col([
                html.Div(id="page-content"),
                dcc.Loading(
                    id="loading",
                    type="circle",
                    children=[],
                    className="loading-spinner"
                )
            ])
        ])
    ], fluid=True)
])

# Callbacks for navigation
@app.callback(
    [Output("page-content", "children"),
     Output("tpnb-link", "active"),
     Output("shelfcap-link", "active"),
     Output("sales-link", "active")],
    [Input("tpnb-link", "n_clicks"),
     Input("shelfcap-link", "n_clicks"),
     Input("sales-link", "n_clicks")]
)
def navigate(tpnb_clicks, shelfcap_clicks, sales_clicks):
    ctx = dash.callback_context
    if not ctx.triggered:
        # Default view
        return render_tpnb_page(), True, False, False
    button_id = ctx.triggered[0]["prop_id"].split(".")[0]
    if button_id == "tpnb-link":
        return render_tpnb_page(), True, False, False
    elif button_id == "shelfcap-link":
        return render_shelfcap_page(), False, True, False
    elif button_id == "sales-link":
        return render_sales_page(), False, False, True
    return render_tpnb_page(), True, False, False


def render_tpnb_page():
    return dbc.Card(
        dbc.CardBody([
            html.H2("TPNB Analysis", className="mb-4"),
            dbc.Row([
                dbc.Col([
                    tpnb_charts() #Call the chart generating function.
                ])
            ])
        ]),
        className="mb-4"
    )


def render_shelfcap_page():
    return dbc.Card(
        dbc.CardBody([
            html.H2("Shelf Capacity Analysis", className="mb-4"),
            dbc.Row([
                dbc.Col([
                    shelfcap_charts() #Call the chart generating function.
                ])
            ])
        ]),
        className="mb-4"
    )


def render_sales_page():
    return dbc.Card(
        dbc.CardBody([
            html.H2("Sales Analysis", className="mb-4"),
            dbc.Row([
                dbc.Col([
                    sales_charts() #Call the chart generating function.
                ])
            ])
        ]),
        className="mb-4"
    )


if __name__ == '__main__':
    app.run_server(debug=True, port=8050)
