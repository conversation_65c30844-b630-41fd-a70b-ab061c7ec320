#!/usr/bin/env python3
"""
Test script to verify the table works correctly after repair
"""

import paramiko
import sys
import time
sys.path.append('.')
import delivered

def test_table_queries():
    """Test various queries on the repaired table"""
    print('🧪 Table Query Testing')
    print('=' * 40)
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        ssh_client.connect(
            hostname=delivered.hostname, 
            username=delivered.username, 
            password=delivered.password, 
            timeout=30
        )
        print('✅ Connected to server')
        
        # Test queries in order of complexity
        test_queries = [
            {
                'name': 'Table Existence Check',
                'query': 'SHOW TABLES LIKE "*cases_delivered*";',
                'timeout': 30,
                'expected': 'tbl_cases_delivered_productivity'
            },
            {
                'name': 'Schema Check',
                'query': 'DESCRIBE sch_analysts.tbl_cases_delivered_productivity;',
                'timeout': 60,
                'expected': 'fw'
            },
            {
                'name': 'Row Count',
                'query': 'SELECT COUNT(*) FROM sch_analysts.tbl_cases_delivered_productivity;',
                'timeout': 180,
                'expected': None
            },
            {
                'name': 'Sample Data',
                'query': 'SELECT fw, country, store, pmg FROM sch_analysts.tbl_cases_delivered_productivity LIMIT 5;',
                'timeout': 120,
                'expected': None
            },
            {
                'name': 'Date Range Check',
                'query': 'SELECT MIN(fw) as min_date, MAX(fw) as max_date FROM sch_analysts.tbl_cases_delivered_productivity;',
                'timeout': 180,
                'expected': None
            }
        ]
        
        results = []
        
        for i, test in enumerate(test_queries, 1):
            print(f'\n📋 Test {i}: {test["name"]}')
            print(f'   Query: {test["query"]}')
            
            try:
                start_time = time.time()
                
                cmd = f"echo '{test['query']}' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
                
                _, stdout, stderr = ssh_client.exec_command(cmd, timeout=test['timeout'])
                output = stdout.read().decode('utf-8')
                error = stderr.read().decode('utf-8')
                
                execution_time = time.time() - start_time
                
                # Check for errors
                if 'FileNotFoundException' in output or 'FileNotFoundException' in error:
                    print(f'   ❌ FAILED: FileNotFoundException (corrupted files)')
                    results.append({'test': test['name'], 'status': 'FAILED', 'error': 'FileNotFoundException'})
                elif 'Error' in output or 'Exception' in output:
                    print(f'   ❌ FAILED: {output[:100]}...')
                    results.append({'test': test['name'], 'status': 'FAILED', 'error': 'SQL Error'})
                elif test['expected'] and test['expected'] in output:
                    print(f'   ✅ PASSED ({execution_time:.1f}s)')
                    results.append({'test': test['name'], 'status': 'PASSED', 'time': execution_time})
                elif test['expected'] is None:
                    # For queries without specific expected results, check if we got meaningful output
                    if len(output.strip()) > 50 and 'jdbc:hive2' in output:
                        print(f'   ✅ PASSED ({execution_time:.1f}s)')
                        print(f'   📊 Result preview: {output.strip()[:100]}...')
                        results.append({'test': test['name'], 'status': 'PASSED', 'time': execution_time})
                    else:
                        print(f'   ⚠️  UNCLEAR: Got response but unclear if valid')
                        print(f'   📄 Output: {output.strip()[:200]}...')
                        results.append({'test': test['name'], 'status': 'UNCLEAR', 'output': output[:200]})
                else:
                    print(f'   ⚠️  UNCLEAR: Expected "{test["expected"]}" not found')
                    results.append({'test': test['name'], 'status': 'UNCLEAR', 'expected': test['expected']})
                    
            except Exception as e:
                print(f'   ❌ FAILED: Exception - {str(e)}')
                results.append({'test': test['name'], 'status': 'FAILED', 'error': str(e)})
        
        # Summary
        print('\n' + '=' * 40)
        print('📊 Test Results Summary:')
        print('=' * 40)
        
        passed = sum(1 for r in results if r['status'] == 'PASSED')
        failed = sum(1 for r in results if r['status'] == 'FAILED')
        unclear = sum(1 for r in results if r['status'] == 'UNCLEAR')
        
        print(f'✅ Passed: {passed}')
        print(f'❌ Failed: {failed}')
        print(f'⚠️  Unclear: {unclear}')
        print(f'📊 Total: {len(results)}')
        
        if failed == 0:
            print('\n🎉 All tests passed! Table is working correctly.')
        elif failed > 0:
            print('\n⚠️  Some tests failed. Table may need repair.')
            print('💡 Try running repair_table.py')
        
        ssh_client.close()
        print('\n🔌 Connection closed')
        
        return results
        
    except Exception as e:
        print(f'❌ Connection error: {e}')
        return []

if __name__ == "__main__":
    test_table_queries()
