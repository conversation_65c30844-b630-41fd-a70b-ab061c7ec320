import pandas as pd
import numpy as np
from pathlib import Path
import pyodbc
import pyarrow as pa
import pyarrow.parquet as pq
from re import search  # searching substring in fullstring
from zipfile import ZipFile
from os import listdir
import os
from os.path import isfile, join
import dask.dataframe as dd
import dask.array as da
import dask


######### for SQL queries ##########
start_date = "'f2021w36'"
end_date = "'f2022w35'"
nr_weeks = int(end_date[7:9]) - int(start_date[7:9]) + 1
pmg_list = (
    "('BWS01', 'BWS02', 'BWS03')"  #'BWS', 'DAI','DRY','FRZ','HDL','HEA','PPD','PRO'
)
country = "('HU','CZ','SK')"  #'HU','CZ','SK'


with pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
) as conn:
    item_sold = """
    SELECT  cast(stores.dmst_store_code as INT) AS store,  hier.pmg AS pmg, cast(mstr.slad_tpnb as INT) AS tpnb, mstr.own_brand as ownbrand,
    mstr.slad_long_des AS product_name,
    SUM(sunit.slsms_unit) AS sold_units, SUM(sunit.slsms_salex) AS sales_excl_vat
    FROM dw.sl_sms sunit 
    JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id 
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
    JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id
    AND mstr.cntr_id = sunit.slsms_cntr_id 
    JOIN tesco_analysts.hierarchy_spm_odbc hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
    WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
    AND stores.cntr_code IN {countries}
    AND sunit.slsms_unit > 0 
    AND sunit.slsms_salex > 0 
    AND hier.pmg IN {pmg}
    AND stores.convenience IN ('Convenience', 'HM')
    GROUP BY  stores.dmst_store_code, hier.pmg, mstr.slad_tpnb, mstr.own_brand, mstr.slad_long_des
    ORDER BY  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb
        """.format(
        start=start_date, end=end_date, countries=country, pmg=pmg_list
    )

    BWS = pd.read_sql(item_sold, conn)


BWS.pivot_table(
    index=["pmg", "tpnb", "ownbrand", "product_name"],
    columns="store",
    values=["sold_units"],
).fillna(0).reset_index().to_excel(
    r"c:\Users\<USER>\OneDrive - Tesco\Documents\Turn up the workin' !!!!!!!\#MODELS\###GA MODELS\#REPLENISHMENT\ReplModel_2023\others\22_oct-nov\sold_items_BWS01-03.xlsx"
)
