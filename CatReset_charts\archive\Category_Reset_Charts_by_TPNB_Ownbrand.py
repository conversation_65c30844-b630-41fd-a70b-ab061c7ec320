
import pandas as pd
from pathlib import Path
import pyarrow.parquet as pq
import plotly.express as px
import warnings
import plotly.graph_objects as go
import numpy as np
from plotly.subplots import make_subplots
import time
import plotly.io as pio
import io
from PIL import Image
import pyodbc




time_start = time.time()

warnings.filterwarnings("ignore")
pd.set_option('display.max_columns', None)



conn = pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()



def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', 
                            separator=None, auto_open=False):
    with open(html_fname, 'w') as f:
        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))
        for fig in plotly_figs[1:]:
            if separator:
                f.write(separator)
            f.write(fig.to_html(full_html=False, include_plotlyjs=False))

    if auto_open:
        import pathlib, webbrowser
        uri = pathlib.Path(html_fname).absolute().as_uri()
        webbrowser.open(uri)





p1 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\opsdev_verz_0119_.parquet"




p2 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\18-04-2023\CE_JDA_SRD_for_model"

p3 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\16-05-2023\CE_JDA_SRD_for_model"

p4 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\12-06-2023\CE_JDA_SRD_for_model"

p5 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\13-07-2023\CE_JDA_SRD_for_model"

p6 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\14-08-2023\CE_JDA_SRD_for_model"

p7 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\13-09-2023\CE_JDA_SRD_for_model"

p8 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\09-10-2023\CE_JDA_SRD_for_model"

p9 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\13-11-2023\CE_JDA_SRD_for_model"

p10 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\12-12-2023\CE_JDA_SRD_for_model"

p11 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\JDA_SRD_Tables\18-01-2024\CE_JDA_SRD_for_model"

p12 = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\JDA_SRD_Tables\05-02-2024\CE_JDA_SRD_for_model"

category_reset_df = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\CategoryReset\cateres_products"

# ownbrand = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\ownbrand_opening_type_28-06-2023.xlsx"

period = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]


repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]

store_list = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\Repl\Stores_Inputs_2023_Q1_Vol3.xlsx")
store_list.columns = [x.lower() for x in store_list.columns]

# ownbrand_df = pd.read_excel(ownbrand)
# ownbrand_df['ownbrand_flag'] = 1

store_list = store_list['store'].unique().tolist()

df_ce = pd.DataFrame()

for x, y in zip([p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11, p12], period ):
    
    df = pd.read_parquet(x)
    
    df = df[df.tpnb > 0]
    
    df = df[df.store.isin(store_list)]

    condition = [
        df["store"].astype(str).str.match("^1"),
        df["store"].astype(str).str.match("^2"),
        df["store"].astype(str).str.match("^4"),
    ]
    results = ["CZ", "SK", "HU"]
    df["country"] = np.select(condition, results, 0)
        

        
        
    df = df[['country', 'store', 'tpnb','product_name', "srp", "nsrp", "full_pallet", "mu", "split_pallet"]]
        
        
        

    
    # for r in repl_types:
    #     df[r] = np.where(df[r] == 1, df.sold_units, 0)
    
    df['period'] = y
    
    
    
    df = df.groupby(['country', 'tpnb', 'product_name', 'period'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
    
    
    df_ce = pd.concat([df_ce, df])
    
    print(f"Done with processing {y}!")
    




cat_df = pd.read_parquet(category_reset_df)[['country', 'tpnb', 'category', 'DIV_DESC']].drop_duplicates()



products=  df_ce.groupby("country")["tpnb"].apply(lambda s: s.tolist()).to_dict()


ownbrand_df = pd.DataFrame()


for k, v in products.items():
    
    s = list()
    for x in v:

        s.append(str(x))

    tpnb = tuple(s)

    sql ="""
    
    select cntr_code AS country, slad_tpnb AS tpnb, own_brand as ownbrand_flag, slad_long_des as product_name
    from DM.dim_artgld_details
    where own_brand = 'Y'
    and cntr_code = '{k}'
    and slad_tpnb in {tpnb}
    GROUP BY cntr_code, slad_tpnb, own_brand, slad_long_des
    
    """.format(k=k, tpnb=tpnb)


    art_gold = pd.read_sql(sql, conn)
    ownbrand_df = pd.concat([ownbrand_df, art_gold])
    
ownbrand_df[ 'tpnb'] = ownbrand_df[ 'tpnb'].astype('int')
ownbrand_df = ownbrand_df.merge(cat_df[['country', 'tpnb', 'category']], on=['country', 'tpnb'], how='left').fillna("No_info")

ownbrand_df.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\CategoryReset\ownbrand_tpns_number_of_tpn_version.xlsx", index=False)


df_ce = df_ce.merge(cat_df, on=['country', 'tpnb'], how='left')

df_ce = df_ce.merge(ownbrand_df[['country', 'tpnb', 'ownbrand_flag']], on=['country', 'tpnb'], how='left')

df_ce = df_ce[df_ce.ownbrand_flag == 'Y']


df_ce_total = df_ce.groupby([ 'DIV_DESC','category','tpnb', 'product_name', 'period', 'ownbrand_flag'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
df_ce_total['country'] = 'CE'

df_ce = pd.concat([df_ce, df_ce_total])

df_ce = df_ce[df_ce.category.notnull()]



        
need_cols = [x for x in df_ce.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]  

# =============================================================================
# Charts
# =============================================================================
df_catres_sum = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'tpnb', 'product_name', 'category', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()



# non_food_food = df_ce[df_ce['DIV_DESC'].isin(['NonFood Grocery', 'Food Grocery'])]
# non_food_food['division'] = np.where(non_food_food['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)', 'Grocery (Food)')

df_catres_sum_div = df_ce.copy()

# df_catres_sum_div = pd.concat([df_catres_sum_div, non_food_food])
# df_catres_sum_div['division'] = np.where(df_catres_sum_div['division'] == 'Grocery', 'Grocery (total)', df_catres_sum_div['division'])

df_catres_sum_div = df_catres_sum_div.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'DIV_DESC','tpnb', 'product_name', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()




cols_need = [x for x in df_catres_sum.columns if x not in [1] + df_catres_sum.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]

cols_need_div = [x for x in df_catres_sum_div.columns if x not in [1] + df_catres_sum_div.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]

df_catres_sum_category = df_catres_sum.melt(id_vars=cols_need, var_name='period' ).groupby(['country', 'category', 'repl_types','period'], observed=True)['value'].sum().reset_index()
df_catres_sum_category['value_%'] = df_catres_sum_category['value'] / df_catres_sum_category.groupby(['country', 'category', 'period'], observed=True)['value'].transform('sum')

df_catres_sum_division = df_catres_sum_div.melt(id_vars=cols_need_div, var_name='period' ).groupby(['country', 'DIV_DESC', 'repl_types','period'], observed=True)['value'].sum().reset_index()
df_catres_sum_division['value_%'] = df_catres_sum_division['value'] / df_catres_sum_division.groupby(['country', 'DIV_DESC', 'period'],observed=True)['value'].transform('sum')
df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)',df_catres_sum_division['DIV_DESC'] )
df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'Food Grocery', 'Grocery (Food)',df_catres_sum_division['DIV_DESC'] )

# df_total = df_catres_sum_division[df_catres_sum_division.country == 'CE'].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
df_total = df_catres_sum_division.groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()

df_total['value_%'] = df_total['value'] / df_total.groupby(['country', 'period'],observed=True)['value'].transform('sum')

df_catres_sum_category['category'] = df_catres_sum_category['category'].apply(lambda x: x.replace(";"," "))


def rename_period_columns(df, prefix='p'):
    df['period'] = df['period'].astype(str)  
    df['period'] = prefix + df['period']  

for x in [df_total, df_catres_sum_category, df_catres_sum_division ]:
    rename_period_columns(x, 'p')

# sorted(df_catres_sum_category['category'].unique().tolist())
cont = []
ind=0
for x in df_catres_sum_category['category'].unique().tolist():

        
    

    fig = px.bar(df_catres_sum_category[df_catres_sum_category.category.isin([x])], x="period", y="value_%",
                 color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                       opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                  height=400,
                 width = 1800,
                 category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                 title=f'{x}',facet_col_wrap=5, orientation='v')
    fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
    fig.update_yaxes(matches=None)
    fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
    fig.update_yaxes(title_font_color='white')
    for annotation in fig['layout']['annotations']: 
        annotation['textangle']= 0
    # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
    fig.update_layout(title_text=f"<b>{x}</b> (Ownbrand)")
    # fig.update_layout(bargap=0.5)
    fig.update_layout(legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.1,
                xanchor="right",
                x=1.0))
    cont.append(fig)
    #fig.show()
        

        
        

for x in sorted(df_catres_sum_division['DIV_DESC'].unique().tolist()):
    

        
    fig = px.bar(df_catres_sum_division[df_catres_sum_division.DIV_DESC.isin([x])], x="period", y="value_%",
                  color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                        opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
                  height=400,
                  width = 1800,
                  category_orders={"country": ["CZ", "HU", "SK", "CE"]},
                  title=f'{x}',facet_col_wrap=5, orientation='v')
    fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
    fig.update_yaxes(matches=None)
    fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
    fig.update_yaxes(title_font_color='black')
    for annotation in fig['layout']['annotations']: 
        annotation['textangle']= 0
    # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
    fig.update_layout(title_text=f"<b>{x}</b> (Ownbrand)", template = 'plotly_dark') #, bargap=0.5)
    fig.update_layout(legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.1,
                xanchor="right",
                x=1.0))
    cont.append(fig)
    


#Total CE   
# fig = px.histogram(df_total, x="period", y="value_%",
#               color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
#                     opacity=0.8, facet_row_spacing =0.1,# color_discrete_sequence=px.colors.qualitative.Alphabet,
#               height=400,
#               width = 1800,
#               category_orders={"country": ["CE"]},
#               title='CE Total',facet_col_wrap=5, orientation='v')
# fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
# fig.update_yaxes(matches=None)
# fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
# fig.update_yaxes(title_font_color='black')
# for annotation in fig['layout']['annotations']: 
#     annotation['textangle']= 0
# # fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
# fig.update_layout(title_text="<b>CE Total</b> (Ownbrand)", template = 'plotly_dark', bargap=0.5) #, bargap=0.5)
# fig.update_layout(legend=dict(
#             orientation="h",
#             yanchor="bottom",
#             y=1.1,
#             xanchor="right",
#             x=1.1))
# cont.append(fig)


fig = px.bar(df_total, x="period", y="value_%",
              color='repl_types', barmode='stack', facet_col='country',text_auto='.0%', color_discrete_sequence=["#5497c7", "green", "#c74848", "goldenrod", "#5f48c7"],
                    opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,
              height=400,
              width = 1800,
              category_orders={"country": ["CZ", "HU", "SK", "CE"]},
              title='CE Total',facet_col_wrap=5, orientation='v')
fig.update_traces(textfont_size=12, textangle=0, textposition="auto", cliponaxis=False)
fig.update_yaxes(matches=None)
fig.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1], font_size=18))
fig.update_yaxes(title_font_color='black')
for annotation in fig['layout']['annotations']: 
    annotation['textangle']= 0
# fig.for_each_xaxis(lambda x: x.update(showticklabels=True))
fig.update_layout(title_text="<b>CE Total</b> (Ownbrand)", template = 'plotly_dark') #, bargap=0.5)
fig.update_layout(legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.1,
            xanchor="right",
            x=1.0))
cont.append(fig)
    
    
html_fname = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\CategoryReset\Category_Reset_chart_by_TPN_numbers_CE_Ownbrand.html"
pdf_fname = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\CategoryReset\Category_Reset_chart_by_TPN_numbers_CE_Ownbrand.pdf"
plotly_figs = cont
combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', 
                                separator=None, auto_open=False)




#PDF converter
figures = plotly_figs
image_list = [pio.to_image(fig, format='png', scale=1.5) for fig in figures] #width=1440, height=900, scale=1.5
for index, image in enumerate(image_list):
    with io.BytesIO() as tmp:
        tmp.write(image)  # write the image bytes to the io.BytesIO() temporary object
        image = Image.open(tmp).convert('RGB')  # convert and overwrite 'image' to prevent creating a new variable
        image_list[index] = image  # overwrite byte image data in list, replace with PIL converted image data

# pop first item from image_list, use that to access .save(). Then refer back to image_list to append the rest
image_list.pop(0).save(pdf_fname, 'PDF',
                       save_all=True, append_images=image_list, resolution=100.0)  # TODO improve resolution

# pio.write_image(fig, pdf_fname, format="pdf", engine="kaleido")


print("\n###############")
print("Charts are ready!")
print("###############\n")

# =============================================================================
# Background Table .xlsx
# =============================================================================

# Category Reset Sheet
df_catres_sum = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'DIV_DESC', 'tpnb', 'product_name', 'category', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()

df_catres_sum.columns = [f'p{col}' if isinstance(col, int) else col for col in df_catres_sum.columns]

df_catres_sum_div.columns = [f'p{col}' if isinstance(col, int) else col for col in df_catres_sum_div.columns]


df_catres_sum.fillna(0, inplace=True)

ind = 1
for x in df_catres_sum.filter(regex=r'^p(?!1)[2-9]|1[0-2]').columns.tolist():

    df_catres_sum[f'{x}_diff'] = df_catres_sum[x] - df_catres_sum[f'p{ind}']  
    ind+=1
    
diff_cols = [x for x in df_catres_sum.columns if x.endswith("diff")]



# df_catres_sum = df_catres_sum.groupby(['country', 'category', 'repl_types'])[['p1'] + diff_cols].sum().reset_index().query("category != 0")

# df_catres_sum['p1_%'] = df_catres_sum['p1']/df_catres_sum.groupby(['country',"category"])['p1'].transform('sum')

# for x in [x for x in df_catres_sum.columns if x.endswith('diff')]:
    
#     df_catres_sum[f'{x}_%'] = df_catres_sum[f'{x}']/df_catres_sum.groupby(['country',"category"])['p1'].transform('sum')

# # Total Divisions Sheet
# df_catres_sum_total = df_catres_sum_div.copy()

# ind = 1
# for x in df_catres_sum_total.filter(regex=r'^p(?!1)[2-9]|1[0-2]').columns.tolist():

#     df_catres_sum_total[f'{x}_diff'] = df_catres_sum_total[x] - df_catres_sum_total[f'p{ind}']  
#     ind+=1
    
# diff_cols = [x for x in df_catres_sum_total.columns if x.endswith("diff")]

# df_catres_sum_total = df_catres_sum_total.groupby(['country', 'DIV_DESC', 'repl_types'])[['p1'] + diff_cols].sum().reset_index()

# df_catres_sum_total['p1_%'] = df_catres_sum_total['p1']/df_catres_sum_total.groupby(['country',"DIV_DESC"])['p1'].transform('sum')

# for x in [x for x in df_catres_sum_total.columns if x.endswith('diff')]:
    
#     df_catres_sum_total[f'{x}_%'] = df_catres_sum_total[f'{x}']/df_catres_sum_total.groupby(['country',"DIV_DESC"])['p1'].transform('sum')

print("\n###############")
print("Background Table done part : Category & Total Divisions!")
print("###############\n")


# Store Divisions Sheet

# stores = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\Repl\Stores_Inputs_2023_Q1_Vol1.xlsx", usecols=["Country", "Format", "Store"])["Store"].unique().tolist()




# df_ce = pd.DataFrame()

# for x, y in zip([p1, p2, p3, p4], period ):
    
#     df = pd.read_parquet(x)
    
#     df = df[df.tpnb > 0]
    
#     df = df[df.store.isin(store_list)]

#     condition = [
#         df["store"].astype(str).str.match("^1"),
#         df["store"].astype(str).str.match("^2"),
#         df["store"].astype(str).str.match("^4"),
#     ]
#     results = ["CZ", "SK", "HU"]
#     df["country"] = np.select(condition, results, 0)
        

        
        
#     df = df[['country', 'store', 'tpnb','product_name', "srp", "nsrp", "full_pallet", "mu", "split_pallet"]]
        
        
        

    
#     # for r in repl_types:
#     #     df[r] = np.where(df[r] == 1, df.sold_units, 0)
    
#     df['period'] = y
    
        
    
#     df_ce = pd.concat([df_ce, df])
    
#     print(f"Done with processing for background table on store level {y}!")


# df_ce = df_ce.merge(cat_df, on=['country', 'tpnb'], how='left')
# df_ce = df_ce[df_ce.category.notnull()]

# need_cols = [x for x in df_ce.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]  


# df_store_level = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country','store', 'DIV_DESC', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()


# ind = 1
# for x in df_store_level.filter(regex=r'^p(?!1)[2-9]|1[0-2]').columns.tolist():

#     df_store_level[f'{x}_diff'] = df_store_level[x] - df_store_level[f'p{ind}']  
#     ind+=1
    
# diff_cols = [x for x in df_store_level.columns if x.endswith("diff")]

# df_store_level = df_store_level.groupby(['country','store', 'DIV_DESC', 'repl_types'], observed=True)[['p1'] + diff_cols].sum().reset_index()

# df_store_level['p1_%'] = df_store_level['p1']/df_store_level.groupby(['country','store',"DIV_DESC"])['p1'].transform('sum')

# for x in [x for x in df_store_level.columns if x.endswith('diff')]:
    
#     df_store_level[f'{x}_%'] = df_store_level[f'{x}']/df_store_level.groupby(['country','store',"DIV_DESC"])['p1'].transform('sum')






with pd.ExcelWriter(
    r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\CategoryReset\SRP_Tracker_CatReset_by_TPNB_OWNBRAND.xlsx", engine="xlsxwriter"
) as writer:
    df_catres_sum.to_excel(writer, sheet_name="Category_Reset (Groups)", index=False)
    workbook = writer.book
    worksheet = writer.sheets["Category_Reset (Groups)"]
    worksheet.set_tab_color("green")
    worksheet.set_zoom(90)
    
    # df_catres_sum_total.to_excel(writer, sheet_name="Total_Divisions", index=False)
    # worksheet2 = writer.sheets["Total_Divisions"]
    # worksheet2.set_tab_color("blue")
    # worksheet2.set_zoom(90)
    
    # df_store_level.to_excel(writer, sheet_name="Store_Divisions", index=False)
    # worksheet3 = writer.sheets["Store_Divisions"]
    # worksheet3.set_tab_color("yellow")
    # worksheet3.set_zoom(90)
    
    
    # vcenter = workbook.add_format(
    #     {
    #         "align": "center_across",
    #         "valign": "vcenter",
    #     }
    # )
    
    percent_columns = workbook.add_format({
        "num_format": '#,##0',
        "bg_color": "#35566e",
        #"right": 1,
        'font_color': 'white',
        "align": "center_across",
        "valign": "vcenter",
        'bold': True

        })
    
    number_columns = workbook.add_format({
        "num_format": '#,##0',
        "bg_color": "#4c608c",
        #"right": 1,
        "align": "center_across",
        "valign": "vcenter",
        'bold': True,
        'font_color': 'white',

        })
    
    border = workbook.add_format({
        'bottom': 1,

    
        })    
    
    max_column_size_cat = len(df_catres_sum.columns) - 1
    # max_column_size_total = len(df_catres_sum_total.columns) - 1
    # max_column_size_store = len(df_store_level.columns) - 1
    
    # worksheet.set_column(0, max_column_size_cat, 18, vcenter)
    # worksheet2.set_column(0, max_column_size_total, 18, vcenter)
    
    cols_diff_part = list(df_catres_sum.columns[df_catres_sum.columns.get_loc("p2_diff") : len(df_catres_sum.columns)])
    cols_number_part = list(df_catres_sum.columns[df_catres_sum.columns.get_loc("p1") : len(df_catres_sum.columns[:df_catres_sum.columns.get_loc("p2_diff")])])   
    
    

    
    for i, col in enumerate(df_catres_sum.columns):
        if col in cols_diff_part:
            worksheet.set_column(i, i, None, percent_columns)
        if col in cols_number_part:
            worksheet.set_column(i, i, None, number_columns)
            
            
    # for i, col in enumerate(df_catres_sum_total.columns):
    #     if col in cols_percent_part:
    #         worksheet2.set_column(i, i, None, percent_columns)      
    #     if col in cols_number_part:
    #         worksheet2.set_column(i, i, None, number_columns)
            
    # for i, col in enumerate(df_store_level.columns):
    #     if col in cols_percent_part:
    #         worksheet3.set_column(i, i, None, percent_columns)      
    #     if col in cols_number_part:
    #         worksheet3.set_column(i, i, None, number_columns)
            

    
    dfs = {"Category_Reset (Groups)": df_catres_sum,
           # "Total_Divisions": df_catres_sum_total,
           # 'Store_Divisions': df_store_level
           }

    for sheetname, df in dfs.items():
        df.to_excel(writer, sheet_name=sheetname, index=False)  # send df to writer
        worksheet = writer.sheets[sheetname]  # pull worksheet object
        for idx, col in enumerate(df):  # loop through all columns
            series = df[col]
            max_len = (
                max(
                    (
                        series.astype(str).map(len).max(),  # len of largest item
                        len(str(series.name)),  # len of column name/header
                    )
                )
                + 1.5
            )  # adding a little extra space
            worksheet.set_column(idx, idx, max_len)  # set column width
    
    
print("\n###############")
print("Background Table done part : Store and formatting all table!")
print("###############\n")

time_stop = time.time()
print(
    "SRP_Tracker_Charts_BackgroundTable - Executed Time: (sec): {:,.2f} ".format(
        time_stop - time_start
    )
)

    
# df_catres_sum.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\CategoryReset\SRP_Tracker_CatReset.xlsx", index=False)
