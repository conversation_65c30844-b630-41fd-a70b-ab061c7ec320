{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e26e1f01-ae97-4308-a8dd-909d7e8ffd84", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.9.7 (default, Sep 16 2021, 16:59:28) [MSC v.1916 64 bit (AMD64)]\n", "pandas version 2.0.3\n"]}], "source": ["import pandas as pd\n", "import sys\n", "print(sys.version)\n", "print('pandas version', pd.__version__)"]}, {"cell_type": "markdown", "id": "e0438202-8adc-4326-a515-e159dfed78b6", "metadata": {}, "source": ["# I/O (Reading and Writing) "]}, {"cell_type": "code", "execution_count": 2, "id": "965659a2-a07a-4590-a59f-91ebb9042882", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "id": "3e476595-cc2c-4749-b683-d9d9ffd27338", "metadata": {}, "outputs": [], "source": ["df.to_parquet()"]}, {"cell_type": "code", "execution_count": null, "id": "efb52379-6065-42b3-9281-2630eca359cc", "metadata": {}, "outputs": [], "source": ["!pip  uninstall matplotlib"]}, {"cell_type": "code", "execution_count": 2, "id": "3924c2b5-482d-4d11-8c9d-b56ded9db287", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "plt.style.use(\"ggplot\")\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": 80, "id": "cc4f596c-d78e-4051-aa28-234eb8d2c2fb", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\JDA_SRD_Tables\\14-08-2023\\CE_JDA_SRD.csv.gzip\", compression=\"gzip\")"]}, {"cell_type": "code", "execution_count": 7, "id": "23297c7d-704a-4cbb-b10d-ca93b31a2c12", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Country', 'Store_Number', 'Store_Name', 'Store_Format', 'Displaygroup',\n", "       'Displaygroup_description', 'POG_ID', 'POG_Name', 'segmentnumber',\n", "       'Fixture_name', 'Fixture_depth', 'Fixture_width', 'Pallet_info',\n", "       'Product_id', 'Product_Name', 'Position_HFacing', 'Position_VFacing',\n", "       'Position_DFacing', 'merchstyle_ID', 'merchstyle_string', 'brand',\n", "       'ownbrand', 'Height', 'Width', 'Depth', 'TrayHeight', 'TrayWidth',\n", "       'Tray<PERSON><PERSON><PERSON>', 'Tray<PERSON><PERSON><PERSON><PERSON><PERSON>', 'TrayN<PERSON>ber<PERSON><PERSON>', 'TrayNumberD<PERSON>',\n", "       'TrayTotalNumber', 'CaseHeight', 'CaseWidth', 'CaseDepth',\n", "       'CaseNumberHigh', 'CaseNumberWide', 'CaseNumberDeep', 'CaseTotalNumber',\n", "       'Division', 'Department', 'Section', 'Group', 'Subgroup', 'DIV_DESC',\n", "       'DEP_DESC', 'SEC_DESC', 'GRP_DESC', 'SGR_DESC'],\n", "      dtype='object')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 82, "id": "fbdb5fe1-1bd1-4567-b0a1-ab6ec0bc1dce", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: ylabel='Country_MerchStyle'>"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["ax"]}, {"cell_type": "code", "execution_count": 100, "id": "ea2ed2a2-eac6-4786-bc09-f3716c1e14b9", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Unit", "marker": {"color": "#636efa", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Unit", "offsetgroup": "Unit", "orientation": "v", "showlegend": true, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Unit"], "xaxis": "x", "y": [14195], "yaxis": "y"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Unit", "marker": {"color": "#636efa", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Unit", "offsetgroup": "Unit", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Unit"], "xaxis": "x2", "y": [14134], "yaxis": "y2"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Unit", "marker": {"color": "#636efa", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Unit", "offsetgroup": "Unit", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Unit"], "xaxis": "x3", "y": [13741], "yaxis": "y3"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Tray", "marker": {"color": "#EF553B", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Tray", "offsetgroup": "Tray", "orientation": "v", "showlegend": true, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Tray"], "xaxis": "x", "y": [9198], "yaxis": "y"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Tray", "marker": {"color": "#EF553B", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Tray", "offsetgroup": "Tray", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Tray"], "xaxis": "x2", "y": [7426], "yaxis": "y2"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Tray", "marker": {"color": "#EF553B", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Tray", "offsetgroup": "Tray", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Tray"], "xaxis": "x3", "y": [9278], "yaxis": "y3"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Alternate", "marker": {"color": "#00cc96", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Alternate", "offsetgroup": "Alternate", "orientation": "v", "showlegend": true, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Alternate"], "xaxis": "x", "y": [1594], "yaxis": "y"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Alternate", "marker": {"color": "#00cc96", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Alternate", "offsetgroup": "Alternate", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Alternate"], "xaxis": "x2", "y": [1559], "yaxis": "y2"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Alternate", "marker": {"color": "#00cc96", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Alternate", "offsetgroup": "Alternate", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Alternate"], "xaxis": "x3", "y": [1746], "yaxis": "y3"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Loose", "marker": {"color": "#ab63fa", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Loose", "offsetgroup": "Loose", "orientation": "v", "showlegend": true, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Loose"], "xaxis": "x", "y": [1], "yaxis": "y"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Loose", "marker": {"color": "#ab63fa", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Loose", "offsetgroup": "Loose", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Loose"], "xaxis": "x2", "y": [474], "yaxis": "y2"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Loose", "marker": {"color": "#ab63fa", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Loose", "offsetgroup": "Loose", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Loose"], "xaxis": "x3", "y": [1], "yaxis": "y3"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Case", "marker": {"color": "#FFA15A", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Case", "offsetgroup": "Case", "orientation": "v", "showlegend": true, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Case"], "xaxis": "x", "y": [180], "yaxis": "y"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Case", "marker": {"color": "#FFA15A", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Case", "offsetgroup": "Case", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Case"], "xaxis": "x2", "y": [60], "yaxis": "y2"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Case", "marker": {"color": "#FFA15A", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Case", "offsetgroup": "Case", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Case"], "xaxis": "x3", "y": [70], "yaxis": "y3"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Display", "marker": {"color": "#19d3f3", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Display", "offsetgroup": "Display", "orientation": "v", "showlegend": true, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Display"], "xaxis": "x2", "y": [2], "yaxis": "y2"}, {"alignmentgroup": "True", "bingroup": "x", "cliponaxis": false, "histfunc": "sum", "hovertemplate": "merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>", "legendgroup": "Display", "marker": {"color": "#19d3f3", "opacity": 0.8, "pattern": {"shape": ""}}, "name": "Display", "offsetgroup": "Display", "orientation": "v", "showlegend": false, "textangle": 0, "textfont": {"size": 12}, "textposition": "auto", "texttemplate": "%{value:0}", "type": "histogram", "x": ["Display"], "xaxis": "x3", "y": [32], "yaxis": "y3"}], "layout": {"annotations": [{"font": {"size": 18}, "showarrow": false, "text": "CZ", "textangle": 0, "x": 0.15999999999999998, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 18}, "showarrow": false, "text": "HU", "textangle": 0, "x": 0.49999999999999994, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 18}, "showarrow": false, "text": "SK", "textangle": 0, "x": 0.8399999999999999, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}], "barmode": "stack", "height": 400, "legend": {"orientation": "h", "title": {"text": "merchstyle_string"}, "tracegroupgap": 0, "x": 1.1, "xanchor": "right", "y": 1.1, "yanchor": "bottom"}, "margin": {"t": 60}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "<b>Displayed_MerchStyle</b>"}, "width": 1500, "xaxis": {"anchor": "y", "autorange": true, "categoryarray": ["Unit", "Tray", "Alternate", "Loose", "Case", "Display"], "categoryorder": "array", "domain": [0, 0.31999999999999995], "range": [-0.5, 5.5], "title": {"text": "merchstyle_string"}, "type": "category"}, "xaxis2": {"anchor": "y2", "autorange": true, "categoryarray": ["Unit", "Tray", "Alternate", "Loose", "Case", "Display"], "categoryorder": "array", "domain": [0.33999999999999997, 0.6599999999999999], "matches": "x", "range": [-0.5, 5.5], "title": {"text": "merchstyle_string"}, "type": "category"}, "xaxis3": {"anchor": "y3", "autorange": true, "categoryarray": ["Unit", "Tray", "Alternate", "Loose", "Case", "Display"], "categoryorder": "array", "domain": [0.6799999999999999, 0.9999999999999999], "matches": "x", "range": [-0.5, 5.5], "title": {"text": "merchstyle_string"}, "type": "category"}, "yaxis": {"anchor": "x", "autorange": true, "domain": [0, 1], "range": [0, 14942.105263157895], "title": {"font": {"color": "white"}, "text": "sum of different_product"}, "type": "linear"}, "yaxis2": {"anchor": "x2", "autorange": true, "domain": [0, 1], "range": [0, 14877.894736842105], "showticklabels": false, "title": {"font": {"color": "white"}}, "type": "linear"}, "yaxis3": {"anchor": "x3", "autorange": true, "domain": [0, 1], "range": [0, 14464.21052631579], "showticklabels": false, "title": {"font": {"color": "white"}}, "type": "linear"}}}, "image/png": "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", "text/html": ["<div>                            <div id=\"3c1136af-50b7-46de-b62f-7586cbe683b2\" class=\"plotly-graph-div\" style=\"height:400px; width:1500px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"3c1136af-50b7-46de-b62f-7586cbe683b2\")) {                    Plotly.newPlot(                        \"3c1136af-50b7-46de-b62f-7586cbe683b2\",                        [{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Unit\",\"marker\":{\"color\":\"#636efa\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Unit\",\"offsetgroup\":\"Unit\",\"orientation\":\"v\",\"showlegend\":true,\"texttemplate\":\"%{value:0}\",\"x\":[\"Unit\"],\"xaxis\":\"x\",\"y\":[14195],\"yaxis\":\"y\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Unit\",\"marker\":{\"color\":\"#636efa\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Unit\",\"offsetgroup\":\"Unit\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Unit\"],\"xaxis\":\"x2\",\"y\":[14134],\"yaxis\":\"y2\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Unit\",\"marker\":{\"color\":\"#636efa\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Unit\",\"offsetgroup\":\"Unit\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Unit\"],\"xaxis\":\"x3\",\"y\":[13741],\"yaxis\":\"y3\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Tray\",\"marker\":{\"color\":\"#EF553B\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Tray\",\"offsetgroup\":\"Tray\",\"orientation\":\"v\",\"showlegend\":true,\"texttemplate\":\"%{value:0}\",\"x\":[\"Tray\"],\"xaxis\":\"x\",\"y\":[9198],\"yaxis\":\"y\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Tray\",\"marker\":{\"color\":\"#EF553B\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Tray\",\"offsetgroup\":\"Tray\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Tray\"],\"xaxis\":\"x2\",\"y\":[7426],\"yaxis\":\"y2\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Tray\",\"marker\":{\"color\":\"#EF553B\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Tray\",\"offsetgroup\":\"Tray\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Tray\"],\"xaxis\":\"x3\",\"y\":[9278],\"yaxis\":\"y3\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Alternate\",\"marker\":{\"color\":\"#00cc96\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Alternate\",\"offsetgroup\":\"Alternate\",\"orientation\":\"v\",\"showlegend\":true,\"texttemplate\":\"%{value:0}\",\"x\":[\"Alternate\"],\"xaxis\":\"x\",\"y\":[1594],\"yaxis\":\"y\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Alternate\",\"marker\":{\"color\":\"#00cc96\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Alternate\",\"offsetgroup\":\"Alternate\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Alternate\"],\"xaxis\":\"x2\",\"y\":[1559],\"yaxis\":\"y2\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Alternate\",\"marker\":{\"color\":\"#00cc96\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Alternate\",\"offsetgroup\":\"Alternate\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Alternate\"],\"xaxis\":\"x3\",\"y\":[1746],\"yaxis\":\"y3\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Loose\",\"marker\":{\"color\":\"#ab63fa\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Loose\",\"offsetgroup\":\"Loose\",\"orientation\":\"v\",\"showlegend\":true,\"texttemplate\":\"%{value:0}\",\"x\":[\"Loose\"],\"xaxis\":\"x\",\"y\":[1],\"yaxis\":\"y\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Loose\",\"marker\":{\"color\":\"#ab63fa\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Loose\",\"offsetgroup\":\"Loose\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Loose\"],\"xaxis\":\"x2\",\"y\":[474],\"yaxis\":\"y2\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Loose\",\"marker\":{\"color\":\"#ab63fa\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Loose\",\"offsetgroup\":\"Loose\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Loose\"],\"xaxis\":\"x3\",\"y\":[1],\"yaxis\":\"y3\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=CZ<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Case\",\"marker\":{\"color\":\"#FFA15A\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Case\",\"offsetgroup\":\"Case\",\"orientation\":\"v\",\"showlegend\":true,\"texttemplate\":\"%{value:0}\",\"x\":[\"Case\"],\"xaxis\":\"x\",\"y\":[180],\"yaxis\":\"y\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Case\",\"marker\":{\"color\":\"#FFA15A\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Case\",\"offsetgroup\":\"Case\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Case\"],\"xaxis\":\"x2\",\"y\":[60],\"yaxis\":\"y2\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Case\",\"marker\":{\"color\":\"#FFA15A\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Case\",\"offsetgroup\":\"Case\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Case\"],\"xaxis\":\"x3\",\"y\":[70],\"yaxis\":\"y3\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=HU<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Display\",\"marker\":{\"color\":\"#19d3f3\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Display\",\"offsetgroup\":\"Display\",\"orientation\":\"v\",\"showlegend\":true,\"texttemplate\":\"%{value:0}\",\"x\":[\"Display\"],\"xaxis\":\"x2\",\"y\":[2],\"yaxis\":\"y2\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"merchstyle_string=%{x}<br>Country=SK<br>sum of different_product=%{y}<extra></extra>\",\"legendgroup\":\"Display\",\"marker\":{\"color\":\"#19d3f3\",\"opacity\":0.8,\"pattern\":{\"shape\":\"\"}},\"name\":\"Display\",\"offsetgroup\":\"Display\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:0}\",\"x\":[\"Display\"],\"xaxis\":\"x3\",\"y\":[32],\"yaxis\":\"y3\",\"type\":\"histogram\",\"textfont\":{\"size\":12},\"cliponaxis\":false,\"textangle\":0,\"textposition\":\"auto\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.31999999999999995],\"title\":{\"text\":\"merchstyle_string\"},\"categoryorder\":\"array\",\"categoryarray\":[\"Unit\",\"Tray\",\"Alternate\",\"Loose\",\"Case\",\"Display\"]},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"sum of different_product\",\"font\":{\"color\":\"white\"}}},\"xaxis2\":{\"anchor\":\"y2\",\"domain\":[0.33999999999999997,0.6599999999999999],\"matches\":\"x\",\"title\":{\"text\":\"merchstyle_string\"},\"categoryorder\":\"array\",\"categoryarray\":[\"Unit\",\"Tray\",\"Alternate\",\"Loose\",\"Case\",\"Display\"]},\"yaxis2\":{\"anchor\":\"x2\",\"domain\":[0.0,1.0],\"showticklabels\":false,\"title\":{\"font\":{\"color\":\"white\"}}},\"xaxis3\":{\"anchor\":\"y3\",\"domain\":[0.6799999999999999,0.9999999999999999],\"matches\":\"x\",\"title\":{\"text\":\"merchstyle_string\"},\"categoryorder\":\"array\",\"categoryarray\":[\"Unit\",\"Tray\",\"Alternate\",\"Loose\",\"Case\",\"Display\"]},\"yaxis3\":{\"anchor\":\"x3\",\"domain\":[0.0,1.0],\"showticklabels\":false,\"title\":{\"font\":{\"color\":\"white\"}}},\"annotations\":[{\"font\":{\"size\":18},\"showarrow\":false,\"text\":\"CZ\",\"x\":0.15999999999999998,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\",\"textangle\":0},{\"font\":{\"size\":18},\"showarrow\":false,\"text\":\"HU\",\"x\":0.49999999999999994,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\",\"textangle\":0},{\"font\":{\"size\":18},\"showarrow\":false,\"text\":\"SK\",\"x\":0.8399999999999999,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\",\"textangle\":0}],\"legend\":{\"title\":{\"text\":\"merchstyle_string\"},\"tracegroupgap\":0,\"orientation\":\"h\",\"yanchor\":\"bottom\",\"y\":1.1,\"xanchor\":\"right\",\"x\":1.1},\"margin\":{\"t\":60},\"barmode\":\"stack\",\"height\":400,\"width\":1500,\"title\":{\"text\":\"<b>Displayed_MerchStyle</b>\"}},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('3c1136af-50b7-46de-b62f-7586cbe683b2');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.express as px\n", "\n", "fig = px.histogram(a.sort_values(\"different_product\", ascending=False), x=\"merchstyle_string\", y=\"different_product\",\n", "             color='merchstyle_string', barmode='stack', facet_col='Country',text_auto='0', \n", "                   opacity=0.8, facet_row_spacing =0.08,# color_discrete_sequence=px.colors.qualitative.Alphabet,\n", "              height=400,\n", "             width = 1500,\n", "             category_orders={\"country\": [\"CZ\", \"HU\", \"SK\"]},\n", "             facet_col_wrap=5, orientation='v')\n", "fig.update_traces(textfont_size=12, textangle=0, textposition=\"auto\", cliponaxis=False)\n", "fig.update_yaxes(matches=None)\n", "fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=18))\n", "fig.update_yaxes(title_font_color='white')\n", "for annotation in fig['layout']['annotations']: \n", "    annotation['textangle']= 0\n", "# fig.for_each_xaxis(lambda x: x.update(showticklabels=True))\n", "fig.update_layout(title_text=\"<b>Displayed_MerchStyle</b>\")\n", "# fig.update_layout(bargap=0.5)\n", "fig.update_layout(legend=dict(\n", "            orientation=\"h\",\n", "            yanchor=\"bottom\",\n", "            y=1.1,\n", "            xanchor=\"right\",\n", "            x=1.1))\n", "# cont.append(fig)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 84, "id": "02863d26-92c0-42bb-97f4-9c53422ff51a", "metadata": {}, "outputs": [], "source": ["a = df.groupby([\"Country\",\"merchstyle_string\"]).agg(different_product=(\"Product_id\", \"nunique\")) \\\n", ".reset_index()\\\n", ".assign(Country_MerchStyle=lambda x: x['Country']+ \"_\"+x['merchstyle_string']) \\\n", ".sort_values(by=['Country', 'different_product'],ascending=True) \\\n", "#.plot(kind=\"barh\", x=\"Country_MerchStyle\", y=\"different_product\", figsize=(10,15))\n", "\n", "\n", "\n", "#y ='different_product' , x =\"merchstyle_string\","]}, {"cell_type": "code", "execution_count": 85, "id": "77946751-7646-425e-930c-31e7ceb23307", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>merchstyle_string</th>\n", "      <th>different_product</th>\n", "      <th>Country_MerchStyle</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CZ</td>\n", "      <td>Loose</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>_<PERSON>ose</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CZ</td>\n", "      <td>Case</td>\n", "      <td>180</td>\n", "      <td>CZ_Case</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CZ</td>\n", "      <td>Alternate</td>\n", "      <td>1594</td>\n", "      <td>CZ_Alternate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CZ</td>\n", "      <td>Tray</td>\n", "      <td>9198</td>\n", "      <td><PERSON><PERSON>_Tray</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CZ</td>\n", "      <td>Unit</td>\n", "      <td>14195</td>\n", "      <td>CZ_Unit</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Country merchstyle_string  different_product Country_MerchStyle\n", "2      CZ             Loose                  1           CZ_Loose\n", "1      CZ              Case                180            CZ_Case\n", "0      CZ         Alternate               1594       CZ_Alternate\n", "3      CZ              Tray               9198            CZ_Tray\n", "4      CZ              Unit              14195            CZ_Unit"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["a.head()"]}, {"cell_type": "code", "execution_count": 77, "id": "3d7adaf5-3b1b-4388-98ff-f8ae08c08557", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Sample DataFrame\n", "data = {'Category': ['A', 'B', 'C', 'D'],\n", "        'Value': [10, 25, 15, 30]}\n", "\n", "df = pd.DataFrame(data)\n", "\n", "# Create a horizontal bar plot\n", "ax = df.plot(kind='barh', x='Category', y='Value')\n", "\n", "# Annotate the bars with their values using a loop\n", "for i, v in enumerate(df['Value']):\n", "    ax.text(v, i, str(v), va='center')\n", "\n", "plt.xlabel('Value')\n", "plt.ylabel('Category')\n", "plt.title('Horizontal Bar Plot with Values')\n", "\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "b5197ca4-87f4-4f92-b7a7-307a17886cd3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "33ccfd32-6afd-43bf-99a6-a0f1c701bd5c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c720a43b-0f92-48e1-a694-a363919d1fa4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "371c2daa-538f-4424-a75a-11feea910149", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "\n", "\n", "# Load the dataset\n", "df = pd.read_excel(r'c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_09\\region.xlsx')\n", "\n", "df['65+'] = pd.to_numeric(df['65+'], errors=\"coerce\")\n", "# Calculate median PI for each country for the age group \"65+\"\n", "median_pis = df.groupby('region')['65+'].median().sort_values(ascending=False)\n", "# Get the top 10 countries\n", "top_countries = median_pis.head(10).index\n", "# Filter the dataframe to include only the top 10 countries\n", "df_top = df[df['region'].isin(top_countries)]"]}, {"cell_type": "code", "execution_count": 2, "id": "ec371032-2747-4f8f-a71f-dc79d0d91307", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Index</th>\n", "      <th>Variant</th>\n", "      <th>region</th>\n", "      <th>Notes</th>\n", "      <th>Location code</th>\n", "      <th>ISO3 Alpha-code</th>\n", "      <th>ISO2 Alpha-code</th>\n", "      <th>SDMX code**</th>\n", "      <th>Type</th>\n", "      <th>Parent code</th>\n", "      <th>...</th>\n", "      <th>15-59</th>\n", "      <th>15-64</th>\n", "      <th>20-64</th>\n", "      <th>20-69</th>\n", "      <th>25-64</th>\n", "      <th>25-69</th>\n", "      <th>60+</th>\n", "      <th>65+</th>\n", "      <th>70+</th>\n", "      <th>80+</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7509</th>\n", "      <td>7510</td>\n", "      <td>Median PI</td>\n", "      <td>China, Hong Kong SAR</td>\n", "      <td>6</td>\n", "      <td>344</td>\n", "      <td>HKG</td>\n", "      <td>HK</td>\n", "      <td>344.0</td>\n", "      <td>Country/Area</td>\n", "      <td>906</td>\n", "      <td>...</td>\n", "      <td>59.227</td>\n", "      <td>67.487</td>\n", "      <td>63.93</td>\n", "      <td>70.6</td>\n", "      <td>59.652</td>\n", "      <td>66.323</td>\n", "      <td>28.733</td>\n", "      <td>20.473</td>\n", "      <td>13.802</td>\n", "      <td>5.411</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7510</th>\n", "      <td>7511</td>\n", "      <td>Median PI</td>\n", "      <td>China, Hong Kong SAR</td>\n", "      <td>6</td>\n", "      <td>344</td>\n", "      <td>HKG</td>\n", "      <td>HK</td>\n", "      <td>344.0</td>\n", "      <td>Country/Area</td>\n", "      <td>906</td>\n", "      <td>...</td>\n", "      <td>58.374</td>\n", "      <td>66.758</td>\n", "      <td>63.132</td>\n", "      <td>70.163</td>\n", "      <td>59.082</td>\n", "      <td>66.114</td>\n", "      <td>29.758</td>\n", "      <td>21.374</td>\n", "      <td>14.342</td>\n", "      <td>5.434</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7511</th>\n", "      <td>7512</td>\n", "      <td>Median PI</td>\n", "      <td>China, Hong Kong SAR</td>\n", "      <td>6</td>\n", "      <td>344</td>\n", "      <td>HKG</td>\n", "      <td>HK</td>\n", "      <td>344.0</td>\n", "      <td>Country/Area</td>\n", "      <td>906</td>\n", "      <td>...</td>\n", "      <td>57.548</td>\n", "      <td>66</td>\n", "      <td>62.272</td>\n", "      <td>69.64</td>\n", "      <td>58.365</td>\n", "      <td>65.733</td>\n", "      <td>30.793</td>\n", "      <td>22.341</td>\n", "      <td>14.973</td>\n", "      <td>5.517</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7512</th>\n", "      <td>7513</td>\n", "      <td>Median PI</td>\n", "      <td>China, Hong Kong SAR</td>\n", "      <td>6</td>\n", "      <td>344</td>\n", "      <td>HKG</td>\n", "      <td>HK</td>\n", "      <td>344.0</td>\n", "      <td>Country/Area</td>\n", "      <td>906</td>\n", "      <td>...</td>\n", "      <td>56.857</td>\n", "      <td>65.268</td>\n", "      <td>61.411</td>\n", "      <td>69.064</td>\n", "      <td>57.603</td>\n", "      <td>65.257</td>\n", "      <td>31.731</td>\n", "      <td>23.320</td>\n", "      <td>15.667</td>\n", "      <td>5.626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7513</th>\n", "      <td>7514</td>\n", "      <td>Median PI</td>\n", "      <td>China, Hong Kong SAR</td>\n", "      <td>6</td>\n", "      <td>344</td>\n", "      <td>HKG</td>\n", "      <td>HK</td>\n", "      <td>344.0</td>\n", "      <td>Country/Area</td>\n", "      <td>906</td>\n", "      <td>...</td>\n", "      <td>56.342</td>\n", "      <td>64.612</td>\n", "      <td>60.599</td>\n", "      <td>68.449</td>\n", "      <td>56.844</td>\n", "      <td>64.695</td>\n", "      <td>32.545</td>\n", "      <td>24.275</td>\n", "      <td>16.425</td>\n", "      <td>5.801</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["      Index    Variant                region Notes  Location code  \\\n", "7509   7510  Median PI  China, Hong Kong SAR     6            344   \n", "7510   7511  Median PI  China, Hong Kong SAR     6            344   \n", "7511   7512  Median PI  China, Hong Kong SAR     6            344   \n", "7512   7513  Median PI  China, Hong Kong SAR     6            344   \n", "7513   7514  Median PI  China, Hong Kong SAR     6            344   \n", "\n", "     ISO3 Alpha-code ISO2 Alpha-code  SDMX code**          Type  Parent code  \\\n", "7509             HKG              HK        344.0  Country/Area          906   \n", "7510             HKG              HK        344.0  Country/Area          906   \n", "7511             HKG              HK        344.0  Country/Area          906   \n", "7512             HKG              HK        344.0  Country/Area          906   \n", "7513             HKG              HK        344.0  Country/Area          906   \n", "\n", "      ...   15-59   15-64   20-64   20-69   25-64   25-69     60+     65+  \\\n", "7509  ...  59.227  67.487   63.93    70.6  59.652  66.323  28.733  20.473   \n", "7510  ...  58.374  66.758  63.132  70.163  59.082  66.114  29.758  21.374   \n", "7511  ...  57.548      66  62.272   69.64  58.365  65.733  30.793  22.341   \n", "7512  ...  56.857  65.268  61.411  69.064  57.603  65.257  31.731  23.320   \n", "7513  ...  56.342  64.612  60.599  68.449  56.844  64.695  32.545  24.275   \n", "\n", "         70+    80+  \n", "7509  13.802  5.411  \n", "7510  14.342  5.434  \n", "7511  14.973  5.517  \n", "7512  15.667  5.626  \n", "7513  16.425  5.801  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df_top.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b2c5d509-a1d8-4c02-bba9-4244bc499557", "metadata": {}, "outputs": [], "source": ["top_countries"]}, {"cell_type": "code", "execution_count": 26, "id": "d1d76fc2-08ef-43bf-8f5f-4e24df827eb0", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"name": "Republic of Korea", "type": "scatter", "visible": false, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [17.491, 18.379, 19.311, 20.349, 21.43, 22.426, 23.326, 24.157, 24.979, 25.809, 26.631, 27.525, 28.468, 29.397, 30.327, 31.217, 32.048, 32.848, 33.553, 34.153, 34.723, 35.283, 35.887, 36.579, 37.283, 37.96, 38.558, 39.023, 39.395, 39.73, 40.047, 40.355, 40.678, 41.041, 41.464, 41.967, 42.509, 43.05, 43.59, 44.12, 44.621, 45.075, 45.481, 45.877, 46.206, 46.377, 46.449, 46.493, 46.491, 46.475, 46.532, 46.626, 46.688, 46.761, 46.895, 47.068, 47.209, 47.312, 47.423, 47.51, 47.504, 47.397, 47.214, 46.975, 46.711, 46.442, 46.18, 45.935, 45.712, 45.517, 45.345, 45.193, 45.066, 44.954, 44.85, 44.752, 44.655, 44.554, 44.444]}, {"name": "<PERSON>", "type": "scatter", "visible": false, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [10.527, 11.01, 11.68, 12.473, 13.323, 14.245, 15.193, 16.166, 17.147, 18.125, 19.142, 20.251, 21.402, 22.451, 23.476, 24.624, 25.74, 26.647, 27.463, 28.273, 29.021, 29.75, 30.443, 30.997, 31.639, 32.436, 33.208, 33.962, 34.655, 35.316, 36.046, 36.899, 37.848, 38.855, 39.892, 40.865, 41.828, 42.786, 43.713, 44.64, 45.457, 46.092, 46.516, 46.713, 46.706, 46.547, 46.279, 45.941, 45.619, 45.278, 44.971, 44.701, 44.517, 44.44, 44.418, 44.451, 44.459, 44.429, 44.4, 44.401, 44.5, 44.503, 44.334, 44.225, 44.1, 43.994, 43.943, 43.886, 43.881, 43.908, 43.918, 43.918, 43.909, 43.929, 43.969, 43.953, 43.921, 43.878, 43.848]}, {"name": "China, Hong Kong SAR", "type": "scatter", "visible": false, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [20.473, 21.374, 22.341, 23.32, 24.275, 25.232, 26.237, 27.218, 28.103, 28.868, 29.546, 30.221, 30.915, 31.611, 32.29, 32.95, 33.62, 34.284, 34.9, 35.46, 35.997, 36.533, 37.094, 37.69, 38.291, 38.885, 39.483, 40.051, 40.574, 41.043, 41.449, 41.849, 42.275, 42.677, 43.018, 43.312, 43.587, 43.861, 44.134, 44.394, 44.595, 44.687, 44.676, 44.625, 44.546, 44.417, 44.264, 44.1, 43.942, 43.805, 43.694, 43.625, 43.588, 43.59, 43.65, 43.699, 43.678, 43.634, 43.613, 43.774, 44.063, 44.27, 44.412, 44.435, 44.299, 44.094, 43.892, 43.692, 43.494, 43.3, 43.109, 42.921, 42.736, 42.554, 42.372, 42.186, 42.002, 41.818, 41.642]}, {"name": "Japan", "type": "scatter", "visible": true, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [29.925, 30.068, 30.231, 30.383, 30.531, 30.69, 30.878, 31.11, 31.377, 31.52, 31.718, 32.088, 32.439, 32.809, 33.222, 33.69, 34.215, 34.748, 35.219, 35.616, 35.956, 36.253, 36.511, 36.727, 36.901, 37.048, 37.199, 37.355, 37.503, 37.625, 37.736, 37.85, 37.944, 38.008, 38.066, 38.12, 38.164, 38.223, 38.286, 38.34, 38.4, 38.461, 38.516, 38.576, 38.633, 38.67, 38.687, 38.687, 38.671, 38.66, 38.671, 38.698, 38.731, 38.767, 38.802, 38.838, 38.886, 38.938, 38.992, 39.043, 39.051, 39.017, 38.957, 38.891, 38.836, 38.783, 38.737, 38.7, 38.668, 38.643, 38.627, 38.62, 38.617, 38.619, 38.626, 38.638, 38.654, 38.676, 38.702]}, {"name": "Malta", "type": "scatter", "visible": false, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [19.128, 19.577, 20.001, 20.44, 20.837, 21.194, 21.515, 21.798, 22.049, 22.257, 22.466, 22.666, 22.858, 23.077, 23.34, 23.622, 23.928, 24.278, 24.684, 25.114, 25.563, 26.042, 26.56, 27.112, 27.674, 28.323, 29.022, 29.72, 30.459, 31.229, 32.053, 32.967, 33.957, 34.967, 35.977, 36.987, 37.953, 38.804, 39.525, 40.153, 40.674, 41.062, 41.302, 41.426, 41.443, 41.359, 41.25, 41.121, 40.972, 40.815, 40.641, 40.484, 40.347, 40.184, 40.031, 39.9, 39.752, 39.602, 39.474, 39.371, 39.258, 39.131, 38.981, 38.816, 38.681, 38.58, 38.509, 38.455, 38.419, 38.397, 38.384, 38.378, 38.375, 38.37, 38.363, 38.35, 38.33, 38.3, 38.263]}, {"name": "Andorra", "type": "scatter", "visible": false, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [14.967, 15.547, 16.198, 16.923, 17.691, 18.509, 19.408, 20.301, 21.212, 22.15, 23.059, 24.018, 24.945, 25.794, 26.653, 27.609, 28.592, 29.517, 30.426, 31.264, 32.077, 32.852, 33.544, 34.162, 34.766, 35.377, 35.886, 36.28, 36.59, 36.878, 37.138, 37.405, 37.689, 37.948, 38.169, 38.368, 38.55, 38.658, 38.717, 38.735, 38.782, 38.853, 38.914, 38.967, 38.96, 38.95, 38.942, 38.93, 38.929, 38.939, 38.951, 39.038, 39.177, 39.301, 39.42, 39.486, 39.481, 39.435, 39.399, 39.376, 39.325, 39.263, 39.111, 38.817, 38.395, 38.085, 37.977, 37.876, 37.781, 37.68, 37.581, 37.496, 37.414, 37.331, 37.259, 37.194, 37.134, 37.082, 37.036]}, {"name": "Spain", "type": "scatter", "visible": false, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [20.265, 20.736, 21.231, 21.754, 22.287, 22.827, 23.402, 24.028, 24.674, 25.31, 25.962, 26.62, 27.274, 27.933, 28.601, 29.284, 29.973, 30.676, 31.397, 32.122, 32.832, 33.51, 34.137, 34.701, 35.203, 35.636, 35.998, 36.299, 36.553, 36.76, 36.93, 37.076, 37.202, 37.307, 37.393, 37.472, 37.535, 37.56, 37.554, 37.534, 37.511, 37.481, 37.453, 37.444, 37.44, 37.429, 37.434, 37.47, 37.527, 37.609, 37.717, 37.867, 38.024, 38.154, 38.285, 38.409, 38.501, 38.592, 38.703, 38.813, 38.897, 38.933, 38.926, 38.878, 38.839, 38.828, 38.815, 38.8, 38.783, 38.766, 38.751, 38.735, 38.724, 38.714, 38.708, 38.707, 38.709, 38.711, 38.716]}, {"name": "Italy", "type": "scatter", "visible": false, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [24.054, 24.461, 24.897, 25.376, 25.888, 26.431, 27.01, 27.661, 28.347, 29.02, 29.682, 30.33, 30.977, 31.605, 32.216, 32.822, 33.41, 33.992, 34.545, 35.03, 35.447, 35.808, 36.11, 36.355, 36.562, 36.748, 36.908, 37.035, 37.14, 37.215, 37.266, 37.324, 37.38, 37.423, 37.455, 37.479, 37.481, 37.451, 37.407, 37.363, 37.328, 37.304, 37.289, 37.287, 37.289, 37.282, 37.286, 37.318, 37.368, 37.436, 37.529, 37.643, 37.773, 37.906, 38.032, 38.151, 38.253, 38.336, 38.407, 38.462, 38.5, 38.51, 38.48, 38.428, 38.381, 38.34, 38.302, 38.269, 38.239, 38.213, 38.194, 38.178, 38.162, 38.153, 38.15, 38.152, 38.162, 38.175, 38.191]}, {"name": "China, Taiwan Province of China", "type": "scatter", "visible": false, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [16.708, 17.452, 18.196, 18.943, 19.695, 20.451, 21.207, 21.957, 22.695, 23.416, 24.115, 24.788, 25.43, 26.037, 26.605, 27.145, 27.673, 28.213, 28.787, 29.413, 30.093, 30.81, 31.544, 32.272, 32.975, 33.641, 34.26, 34.828, 35.336, 35.779, 36.162, 36.501, 36.812, 37.116, 37.427, 37.753, 38.09, 38.432, 38.772, 39.103, 39.416, 39.697, 39.932, 40.108, 40.213, 40.245, 40.211, 40.123, 39.989, 39.821, 39.627, 39.416, 39.195, 38.971, 38.772, 38.649, 38.573, 38.507, 38.461, 38.411, 38.337, 38.228, 38.095, 37.956, 37.824, 37.705, 37.602, 37.517, 37.447, 37.39, 37.347, 37.318, 37.298, 37.289, 37.287, 37.291, 37.3, 37.309, 37.32]}, {"name": "Puerto Rico", "type": "scatter", "visible": false, "x": [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100], "y": [22.93, 23.365, 23.778, 24.188, 24.581, 24.959, 25.338, 25.733, 26.141, 26.544, 26.927, 27.287, 27.647, 28.022, 28.388, 28.721, 29.025, 29.324, 29.635, 29.957, 30.289, 30.625, 30.975, 31.335, 31.675, 31.97, 32.216, 32.421, 32.604, 32.784, 32.98, 33.217, 33.524, 33.894, 34.287, 34.693, 35.123, 35.585, 36.081, 36.608, 37.165, 37.757, 38.383, 39.024, 39.657, 40.261, 40.829, 41.372, 41.9, 42.405, 42.881, 43.325, 43.772, 44.185, 44.548, 44.895, 45.204, 45.493, 45.757, 45.983, 46.155, 46.187, 46.228, 46.398, 46.595, 46.776, 46.934, 47.083, 47.228, 47.374, 47.521, 47.674, 47.828, 47.985, 48.153, 48.331, 48.517, 48.708, 48.899]}], "layout": {"autosize": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Population Index over the Years for Age Group 65+"}, "updatemenus": [{"active": 4, "buttons": [{"args": [{"visible": [true, true, true, true, true, true, true, true, true, true]}], "label": "All", "method": "update"}, {"args": [{"visible": [true, false, false, false, false, false, false, false, false, false]}], "label": "Republic of Korea", "method": "update"}, {"args": [{"visible": [false, true, false, false, false, false, false, false, false, false]}], "label": "<PERSON>", "method": "update"}, {"args": [{"visible": [false, false, true, false, false, false, false, false, false, false]}], "label": "China, Hong Kong SAR", "method": "update"}, {"args": [{"visible": [false, false, false, true, false, false, false, false, false, false]}], "label": "Japan", "method": "update"}, {"args": [{"visible": [false, false, false, false, true, false, false, false, false, false]}], "label": "Malta", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, true, false, false, false, false]}], "label": "Andorra", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, false, true, false, false, false]}], "label": "Spain", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, false, false, true, false, false]}], "label": "Italy", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, false, false, false, true, false]}], "label": "China, Taiwan Province of China", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, false, false, false, false, true]}], "label": "Puerto Rico", "method": "update"}]}], "xaxis": {"autorange": true, "range": [2022, 2100], "title": {"text": "Year"}, "type": "linear"}, "yaxis": {"autorange": true, "range": [29.418, 39.558], "title": {"text": "Population Index"}, "type": "linear"}}}, "image/png": "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*************************************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*****************************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", "text/html": ["<div>                            <div id=\"116e8b17-4378-4ab0-a9ed-03eeb46d576a\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"116e8b17-4378-4ab0-a9ed-03eeb46d576a\")) {                    Plotly.newPlot(                        \"116e8b17-4378-4ab0-a9ed-03eeb46d576a\",                        [{\"name\":\"Republic of Korea\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[17.491,18.379,19.311,20.349,21.43,22.426,23.326,24.157,24.979,25.809,26.631,27.525,28.468,29.397,30.327,31.217,32.048,32.848,33.553,34.153,34.723,35.283,35.887,36.579,37.283,37.96,38.558,39.023,39.395,39.73,40.047,40.355,40.678,41.041,41.464,41.967,42.509,43.05,43.59,44.12,44.621,45.075,45.481,45.877,46.206,46.377,46.449,46.493,46.491,46.475,46.532,46.626,46.688,46.761,46.895,47.068,47.209,47.312,47.423,47.51,47.504,47.397,47.214,46.975,46.711,46.442,46.18,45.935,45.712,45.517,45.345,45.193,45.066,44.954,44.85,44.752,44.655,44.554,44.444],\"type\":\"scatter\"},{\"name\":\"Saint Barth\\u00e9lemy\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[10.527,11.01,11.68,12.473,13.323,14.245,15.193,16.166,17.147,18.125,19.142,20.251,21.402,22.451,23.476,24.624,25.74,26.647,27.463,28.273,29.021,29.75,30.443,30.997,31.639,32.436,33.208,33.962,34.655,35.316,36.046,36.899,37.848,38.855,39.892,40.865,41.828,42.786,43.713,44.64,45.457,46.092,46.516,46.713,46.706,46.547,46.279,45.941,45.619,45.278,44.971,44.701,44.517,44.44,44.418,44.451,44.459,44.429,44.4,44.401,44.5,44.503,44.334,44.225,44.1,43.994,43.943,43.886,43.881,43.908,43.918,43.918,43.909,43.929,43.969,43.953,43.921,43.878,43.848],\"type\":\"scatter\"},{\"name\":\"China, Hong Kong SAR\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[20.473,21.374,22.341,23.32,24.275,25.232,26.237,27.218,28.103,28.868,29.546,30.221,30.915,31.611,32.29,32.95,33.62,34.284,34.9,35.46,35.997,36.533,37.094,37.69,38.291,38.885,39.483,40.051,40.574,41.043,41.449,41.849,42.275,42.677,43.018,43.312,43.587,43.861,44.134,44.394,44.595,44.687,44.676,44.625,44.546,44.417,44.264,44.1,43.942,43.805,43.694,43.625,43.588,43.59,43.65,43.699,43.678,43.634,43.613,43.774,44.063,44.27,44.412,44.435,44.299,44.094,43.892,43.692,43.494,43.3,43.109,42.921,42.736,42.554,42.372,42.186,42.002,41.818,41.642],\"type\":\"scatter\"},{\"name\":\"Japan\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[29.925,30.068,30.231,30.383,30.531,30.69,30.878,31.11,31.377,31.52,31.718,32.088,32.439,32.809,33.222,33.69,34.215,34.748,35.219,35.616,35.956,36.253,36.511,36.727,36.901,37.048,37.199,37.355,37.503,37.625,37.736,37.85,37.944,38.008,38.066,38.12,38.164,38.223,38.286,38.34,38.4,38.461,38.516,38.576,38.633,38.67,38.687,38.687,38.671,38.66,38.671,38.698,38.731,38.767,38.802,38.838,38.886,38.938,38.992,39.043,39.051,39.017,38.957,38.891,38.836,38.783,38.737,38.7,38.668,38.643,38.627,38.62,38.617,38.619,38.626,38.638,38.654,38.676,38.702],\"type\":\"scatter\"},{\"name\":\"Malta\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[19.128,19.577,20.001,20.44,20.837,21.194,21.515,21.798,22.049,22.257,22.466,22.666,22.858,23.077,23.34,23.622,23.928,24.278,24.684,25.114,25.563,26.042,26.56,27.112,27.674,28.323,29.022,29.72,30.459,31.229,32.053,32.967,33.957,34.967,35.977,36.987,37.953,38.804,39.525,40.153,40.674,41.062,41.302,41.426,41.443,41.359,41.25,41.121,40.972,40.815,40.641,40.484,40.347,40.184,40.031,39.9,39.752,39.602,39.474,39.371,39.258,39.131,38.981,38.816,38.681,38.58,38.509,38.455,38.419,38.397,38.384,38.378,38.375,38.37,38.363,38.35,38.33,38.3,38.263],\"type\":\"scatter\"},{\"name\":\"Andorra\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[14.967,15.547,16.198,16.923,17.691,18.509,19.408,20.301,21.212,22.15,23.059,24.018,24.945,25.794,26.653,27.609,28.592,29.517,30.426,31.264,32.077,32.852,33.544,34.162,34.766,35.377,35.886,36.28,36.59,36.878,37.138,37.405,37.689,37.948,38.169,38.368,38.55,38.658,38.717,38.735,38.782,38.853,38.914,38.967,38.96,38.95,38.942,38.93,38.929,38.939,38.951,39.038,39.177,39.301,39.42,39.486,39.481,39.435,39.399,39.376,39.325,39.263,39.111,38.817,38.395,38.085,37.977,37.876,37.781,37.68,37.581,37.496,37.414,37.331,37.259,37.194,37.134,37.082,37.036],\"type\":\"scatter\"},{\"name\":\"Spain\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[20.265,20.736,21.231,21.754,22.287,22.827,23.402,24.028,24.674,25.31,25.962,26.62,27.274,27.933,28.601,29.284,29.973,30.676,31.397,32.122,32.832,33.51,34.137,34.701,35.203,35.636,35.998,36.299,36.553,36.76,36.93,37.076,37.202,37.307,37.393,37.472,37.535,37.56,37.554,37.534,37.511,37.481,37.453,37.444,37.44,37.429,37.434,37.47,37.527,37.609,37.717,37.867,38.024,38.154,38.285,38.409,38.501,38.592,38.703,38.813,38.897,38.933,38.926,38.878,38.839,38.828,38.815,38.8,38.783,38.766,38.751,38.735,38.724,38.714,38.708,38.707,38.709,38.711,38.716],\"type\":\"scatter\"},{\"name\":\"Italy\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[24.054,24.461,24.897,25.376,25.888,26.431,27.01,27.661,28.347,29.02,29.682,30.33,30.977,31.605,32.216,32.822,33.41,33.992,34.545,35.03,35.447,35.808,36.11,36.355,36.562,36.748,36.908,37.035,37.14,37.215,37.266,37.324,37.38,37.423,37.455,37.479,37.481,37.451,37.407,37.363,37.328,37.304,37.289,37.287,37.289,37.282,37.286,37.318,37.368,37.436,37.529,37.643,37.773,37.906,38.032,38.151,38.253,38.336,38.407,38.462,38.5,38.51,38.48,38.428,38.381,38.34,38.302,38.269,38.239,38.213,38.194,38.178,38.162,38.153,38.15,38.152,38.162,38.175,38.191],\"type\":\"scatter\"},{\"name\":\"China, Taiwan Province of China\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[16.708,17.452,18.196,18.943,19.695,20.451,21.207,21.957,22.695,23.416,24.115,24.788,25.43,26.037,26.605,27.145,27.673,28.213,28.787,29.413,30.093,30.81,31.544,32.272,32.975,33.641,34.26,34.828,35.336,35.779,36.162,36.501,36.812,37.116,37.427,37.753,38.09,38.432,38.772,39.103,39.416,39.697,39.932,40.108,40.213,40.245,40.211,40.123,39.989,39.821,39.627,39.416,39.195,38.971,38.772,38.649,38.573,38.507,38.461,38.411,38.337,38.228,38.095,37.956,37.824,37.705,37.602,37.517,37.447,37.39,37.347,37.318,37.298,37.289,37.287,37.291,37.3,37.309,37.32],\"type\":\"scatter\"},{\"name\":\"Puerto Rico\",\"x\":[2022.0,2023.0,2024.0,2025.0,2026.0,2027.0,2028.0,2029.0,2030.0,2031.0,2032.0,2033.0,2034.0,2035.0,2036.0,2037.0,2038.0,2039.0,2040.0,2041.0,2042.0,2043.0,2044.0,2045.0,2046.0,2047.0,2048.0,2049.0,2050.0,2051.0,2052.0,2053.0,2054.0,2055.0,2056.0,2057.0,2058.0,2059.0,2060.0,2061.0,2062.0,2063.0,2064.0,2065.0,2066.0,2067.0,2068.0,2069.0,2070.0,2071.0,2072.0,2073.0,2074.0,2075.0,2076.0,2077.0,2078.0,2079.0,2080.0,2081.0,2082.0,2083.0,2084.0,2085.0,2086.0,2087.0,2088.0,2089.0,2090.0,2091.0,2092.0,2093.0,2094.0,2095.0,2096.0,2097.0,2098.0,2099.0,2100.0],\"y\":[22.93,23.365,23.778,24.188,24.581,24.959,25.338,25.733,26.141,26.544,26.927,27.287,27.647,28.022,28.388,28.721,29.025,29.324,29.635,29.957,30.289,30.625,30.975,31.335,31.675,31.97,32.216,32.421,32.604,32.784,32.98,33.217,33.524,33.894,34.287,34.693,35.123,35.585,36.081,36.608,37.165,37.757,38.383,39.024,39.657,40.261,40.829,41.372,41.9,42.405,42.881,43.325,43.772,44.185,44.548,44.895,45.204,45.493,45.757,45.983,46.155,46.187,46.228,46.398,46.595,46.776,46.934,47.083,47.228,47.374,47.521,47.674,47.828,47.985,48.153,48.331,48.517,48.708,48.899],\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"updatemenus\":[{\"buttons\":[{\"args\":[{\"visible\":[true,true,true,true,true,true,true,true,true,true]}],\"label\":\"All\",\"method\":\"update\"},{\"args\":[{\"visible\":[true,false,false,false,false,false,false,false,false,false]}],\"label\":\"Republic of Korea\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,true,false,false,false,false,false,false,false,false]}],\"label\":\"Saint Barth\\u00e9lemy\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,false,true,false,false,false,false,false,false,false]}],\"label\":\"China, Hong Kong SAR\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,false,false,true,false,false,false,false,false,false]}],\"label\":\"Japan\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,false,false,false,true,false,false,false,false,false]}],\"label\":\"Malta\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,false,false,false,false,true,false,false,false,false]}],\"label\":\"Andorra\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,false,false,false,false,false,true,false,false,false]}],\"label\":\"Spain\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,false,false,false,false,false,false,true,false,false]}],\"label\":\"Italy\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,false,false,false,false,false,false,false,true,false]}],\"label\":\"China, Taiwan Province of China\",\"method\":\"update\"},{\"args\":[{\"visible\":[false,false,false,false,false,false,false,false,false,true]}],\"label\":\"Puerto Rico\",\"method\":\"update\"}]}],\"title\":{\"text\":\"Population Index over the Years for Age Group 65+\"},\"xaxis\":{\"title\":{\"text\":\"Year\"}},\"yaxis\":{\"title\":{\"text\":\"Population Index\"}}},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('116e8b17-4378-4ab0-a9ed-03eeb46d576a');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "plotly_figs = []\n", "# Create a line chart with dropdown\n", "fig = go.Figure()\n", "\n", "for country in top_countries:\n", "    fig.add_trace(\n", "        go.<PERSON><PERSON>(x=df_top[df_top['region'] == country]['Year'],\n", "                   y=df_top[df_top['region'] == country]['65+'],\n", "                   name=country)\n", "    )\n", "# Create dropdown buttons\n", "dropdown_buttons = [\n", "    dict(label='All', method='update', args=[{'visible': [True for _ in top_countries]}]),\n", "    *[dict(label=country, method='update', args=[{'visible': [country==c for c in top_countries]}]) for country in top_countries]\n", "]\n", "# Add dropdown menu to layout\n", "fig.update_layout(\n", "    updatemenus=[\n", "        go.layout.Updatemenu(buttons=dropdown_buttons)\n", "    ],\n", "    title='Population Index over the Years for Age Group 65+',\n", "    xaxis_title='Year',\n", "    yaxis_title='Population Index'\n", ")\n", "fig.show()\n", "plotly_figs.append(fig)\n"]}, {"cell_type": "code", "execution_count": 24, "id": "4aff7a4a-92db-40ac-9250-9daebaa7fff7", "metadata": {}, "outputs": [], "source": ["def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                            separator=None, auto_open=False):\n", "    with open(html_fname, 'w') as f:\n", "        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))\n", "        for fig in plotly_figs[1:]:\n", "            if separator:\n", "                f.write(separator)\n", "            f.write(fig.to_html(full_html=False, include_plotlyjs=False))\n", "\n", "    if auto_open:\n", "        import pathlib, webbrowser\n", "        uri = pathlib.Path(html_fname).absolute().as_uri()\n", "        webbrowser.open(uri)"]}, {"cell_type": "code", "execution_count": 25, "id": "a729f036-a53c-4b33-8a11-7c77c6568298", "metadata": {}, "outputs": [], "source": ["html_fname = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_09\\test_dropdown.html\"\n"]}, {"cell_type": "code", "execution_count": 27, "id": "305ad34c-1914-43d2-8f06-ba2c541972a9", "metadata": {}, "outputs": [], "source": ["combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                                separator=None, auto_open=False)"]}, {"cell_type": "code", "execution_count": null, "id": "8143d8d1-fa5c-4d6a-b679-e0a53c9c87e0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c9b1184a-a43c-4da5-bfc2-4e93973dbeb2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "a0343bee-c795-456a-bf50-9975a1e116b9", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "a = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\OPB_Dep_JDA_0612.xlsx\").query(\"Store.isin([11122, 21047, 41049])\")\n", "b = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\Measurements\\inputs\\CE_CHM.xlsx\").replace(np.nan, 0).query(\"ROLE != 'External'\")"]}, {"cell_type": "code", "execution_count": 9, "id": "49dd4405-103b-4629-acbb-f0b6f0b877f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["['<PERSON><PERSON>',\n", " 'Deputy',\n", " 'Counters',\n", " 'GA',\n", " 'Cleaning',\n", " 'Agency',\n", " 'OSA',\n", " 'CSD specialists ',\n", " 'Tesco colleague',\n", " 'Store Manager',\n", " 'FF Replenishment',\n", " 'CCLB',\n", " 'Dotcom',\n", " 'CS',\n", " 'Ambient',\n", " 'GM',\n", " 'Duty MNG / SM Cover',\n", " 'Hired - Cleaning',\n", " 'OSA ',\n", " 'MBC',\n", " 'Student Hired- Ambient',\n", " 'Produce',\n", " 'Student Hired - FF',\n", " 'Student Hired-Ambient',\n", " 'Student Hired - Ambient',\n", " 'Student Hired - F&F',\n", " 'Store MNG',\n", " 'Student',\n", " 'Student Hired - MBC',\n", " 'Duty MNG',\n", " 'Student Hired- MBC',\n", " 'F&F',\n", " 'Student Hired- FF',\n", " 'Sudent Hired- Ambient',\n", " 'Tesco Colleague 01',\n", " 'FF',\n", " 'Tesco Colleague 02',\n", " 'Tesco Colleague 05',\n", " 'Tesco Colleague 04',\n", " 'Tesco Colleague 03',\n", " 'Compliance',\n", " 0,\n", " 'Replenishment',\n", " 'Deputy Manager',\n", " 'Customer Service',\n", " 'External']"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["b.ROLE.unique().tolist()"]}, {"cell_type": "code", "execution_count": 6, "id": "ff8242ec-977d-42e5-aef3-8d6c2e57c8be", "metadata": {}, "outputs": [{"data": {"text/plain": ["store  Level_3                                                             Level_4                                   \n", "41049  0                                                                   0                                             0.083333\n", "       AD-HOC REQUESTS (Broken drinks, cases, etc.- Please type location)  0                                             0.083333\n", "       Can't find the person                                               0                                             0.250000\n", "       Discussion with colleagues (2 people)                               0                                             0.333333\n", "       Other warehouse activities                                          Arrange back area, RSU - Packaging, crates    0.166667\n", "       Pre-sort                                                            0                                             1.666667\n", "       Receiving deliveries                                                DC                                            0.416667\n", "       Replenishment                                                       BWS                                           0.916667\n", "                                                                           Dry                                           1.583333\n", "                                                                           F&F                                           1.250000\n", "                                                                           Food Grocery                                  2.000000\n", "                                                                           GM                                            0.333333\n", "                                                                           Health and B.                                 0.583333\n", "                                                                           News & Magazines                              0.916667\n", "       Sending back News & Magazines                                       0                                             0.166667\n", "       Stock movement                                                      Move products back to warehouse               0.166667\n", "                                                                           Move products to shopfloor                    0.166667\n", "                                                                           Pre-sort                                      0.166667\n", "       Tidy up after replenishment, rip apart RSU                          0                                             0.083333\n", "       Walking (can't identify specific activity)                          0                                             0.250000\n", "Name: Duration (hours), dtype: float64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["b[b.ROLE == 'External'].groupby(['store', 'Level_3','Level_4'])['Duration (hours)'].sum()"]}, {"cell_type": "code", "execution_count": 7, "id": "a7b7564e-1b89-4d54-858f-328351400fa9", "metadata": {}, "outputs": [], "source": ["\n", "import numpy as np\n", "import pandas as pd\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.set_option(\"display.max_rows\", None)\n", "a = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\OPB_Dep_JDA_0612.xlsx\").query(\"Store.isin([11122, 21047, 41049])\")\n", "b = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\Measurements\\inputs\\CE_CHM.xlsx\").replace(np.nan, 0)\n", "\n", "\n", "\n", "\n", "\n", "b = b[~((b.Level_3 == \"Replenishment\")&(b.Level_4.isin(['Bakery', 'Meat&Poultry Counters',\n", "                                                        'Produce and bakery bags',\n", "                                                        'Shopping bags','Deli&Cheese Counters','F&F'])))].query(\"Level_2.isin(['Warehouse', 'Store', 'Shopfloor','Cleaning '])\")\n", "b = b[~b.Level_3.isin([\"Other with PDCU, can't identify\", \"Inventory\", \"Other (notes)\", \"Tablet Use for Bakery\", 'Check scales', 'Bakery production ', 'Other (notes)', 'Prepare new delivery of F&F items',\n", "                      'Counter Cleaning', 'Price change process', 'Product prep (slice, cut) - Counter ','Price verification', 'Counter Opening/ Closing routine', 'Serve customer - Counters',\n", "                      'F&F - clothing', 'Gap scan', 'Print/Handle POS', 'Promo Change', 'Planogram Change',\n", "       'Loss prevention (make notes)'])]"]}, {"cell_type": "code", "execution_count": 133, "id": "b966693c-7de1-43cc-840f-cb327f8e3bca", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>store</th>\n", "      <th>ROLE</th>\n", "      <th>Level_1</th>\n", "      <th>Level_2</th>\n", "      <th>Level_3</th>\n", "      <th>Level_4</th>\n", "      <th>Level_5</th>\n", "      <th>Level_6</th>\n", "      <th>DURATION</th>\n", "      <th>Duration (hours)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>55118</th>\n", "      <td>41049</td>\n", "      <td>Compliance</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Shopfloor</td>\n", "      <td>Tidy up after replenishment, rip apart RSU</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55511</th>\n", "      <td>41049</td>\n", "      <td>Replenishment</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Shopfloor</td>\n", "      <td>Tidy up after replenishment, rip apart RSU</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55601</th>\n", "      <td>41049</td>\n", "      <td>Replenishment</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Shopfloor</td>\n", "      <td>Tidy up after replenishment, rip apart RSU</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55604</th>\n", "      <td>41049</td>\n", "      <td>Compliance</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Shopfloor</td>\n", "      <td>Tidy up after replenishment, rip apart RSU</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55627</th>\n", "      <td>41049</td>\n", "      <td>Replenishment</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Shopfloor</td>\n", "      <td>Tidy up after replenishment, rip apart RSU</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       store           ROLE                Level_1    Level_2  \\\n", "55118  41049     Compliance  Bornemissza Hajnalka   Shopfloor   \n", "55511  41049  Replenishment       Kaszás Ferencné   Shopfloor   \n", "55601  41049  Replenishment         Kis Alexandra   Shopfloor   \n", "55604  41049     Compliance  Bornemissza Hajnalka   Shopfloor   \n", "55627  41049  Replenishment         Kis Alexandra   Shopfloor   \n", "\n", "                                          Level_3 Level_4 Level_5 Level_6  \\\n", "55118  Tidy up after replenishment, rip apart RSU     NaN     NaN     NaN   \n", "55511  Tidy up after replenishment, rip apart RSU     NaN     NaN     NaN   \n", "55601  Tidy up after replenishment, rip apart RSU     NaN     NaN     NaN   \n", "55604  Tidy up after replenishment, rip apart RSU     NaN     NaN     NaN   \n", "55627  Tidy up after replenishment, rip apart RSU     NaN     NaN     NaN   \n", "\n", "       DURATION  Duration (hours)  \n", "55118         5          0.083333  \n", "55511         5          0.083333  \n", "55601         5          0.083333  \n", "55604         5          0.083333  \n", "55627         5          0.083333  "]}, "execution_count": 133, "metadata": {}, "output_type": "execute_result"}], "source": ["b[(b.store == 41049) & (b.Level_3 == 'Tidy up after replenishment, rip apart RSU')].head()"]}, {"cell_type": "code", "execution_count": 10, "id": "2f66239c-08f6-45ea-82c7-557e1b6d1805", "metadata": {}, "outputs": [], "source": ["b.groupby(['store','Level_2','Level_3','Level_4'])['Duration (hours)'].sum().reset_index().to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\Measurements\\outputs\\measurement_repl.xlsx\")"]}, {"cell_type": "code", "execution_count": 120, "id": "48e41a44-d857-4b51-97c1-29c1c0329f60", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Country', 'Store', 'Format', 'Dep', 'Division', 'Fix Hours',\n", "       'Variable Hours', 'Total Weekly Hours', 'Yearly GBP',\n", "       'Variable Currency', 'Additional Hours', 'Arrange and clean back area',\n", "       'Backroom DotCom', '<PERSON><PERSON> return', 'Box Opening', 'Cleaning',\n", "       'Clip Strip', 'Customer Service', 'EPW', 'Facing-Rumble',\n", "       'Food Donation', 'Fresh waste shipping', 'Handle Price',\n", "       'Managing Colleagues', 'Morning routines', 'Online Price Changes',\n", "       'PRO_WGLL - change layout', 'Packaging RSU movements',\n", "       'Packaging bottles and crates', 'Post-sort', 'Pre-sort',\n", "       'Preparation for Replenishment', 'Product Returns', 'Product tagging',\n", "       'RSU', 'RTC & CCLB', 'RTV return', 'Range Check', 'Remitenda',\n", "       'Replenishment', 'Safe and legal', 'Stock Movement',\n", "       'Stock Movement WH', 'Torn packaging', 'Transfers', 'WGLL', 'WIBI',\n", "       'Waste'],\n", "      dtype='object')"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["a['replenishment_to_compare'] = a[['Clip Strip','Preparation for Replenishment', 'Box Opening', 'Torn packaging', 'WGLL']]"]}, {"cell_type": "code", "execution_count": null, "id": "57e47c9e-451b-4df4-a9c2-67c4a2288011", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 46, "id": "934a65ab-9014-4cd3-8d3f-3ef16afb7660", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Level_2</th>\n", "      <th>Level_3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>Admin &amp; Management</td>\n", "      <td>Cash Office</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6997</th>\n", "      <td>Admin &amp; Management</td>\n", "      <td>Other (notes)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>509</th>\n", "      <td>Admin &amp; Management</td>\n", "      <td>Store manager activities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>Admin &amp; Management</td>\n", "      <td>Management activities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>Admin &amp; Management</td>\n", "      <td>PC work</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1018</th>\n", "      <td>Admin &amp; Management</td>\n", "      <td>F&amp;F management</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61464</th>\n", "      <td>Admin &amp; Management</td>\n", "      <td>Mid year cash inventory</td>\n", "    </tr>\n", "    <tr>\n", "      <th>904</th>\n", "      <td>Cleaning</td>\n", "      <td>Outside store ( Car park, walking zones)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9107</th>\n", "      <td>Cleaning</td>\n", "      <td>Administration, paperwork, sign ups</td>\n", "    </tr>\n", "    <tr>\n", "      <th>550</th>\n", "      <td>Cleaning</td>\n", "      <td>Counters</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10589</th>\n", "      <td>Cleaning</td>\n", "      <td>AD-HOC REQUESTS (Broken drinks, cases, etc.- P...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>Cleaning</td>\n", "      <td>Back area</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>Cleaning</td>\n", "      <td>Trash movement</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68079</th>\n", "      <td>Cleaning</td>\n", "      <td>Cleaning baskets</td>\n", "    </tr>\n", "    <tr>\n", "      <th>868</th>\n", "      <td>Cleaning</td>\n", "      <td>Mall</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>Cleaning</td>\n", "      <td>Shopfloor</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23762</th>\n", "      <td>Cleaning</td>\n", "      <td>Other  (notes)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24086</th>\n", "      <td>Cleaning</td>\n", "      <td>Dotcom</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58310</th>\n", "      <td>Cleaning</td>\n", "      <td>mall</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1098</th>\n", "      <td>Cleaning</td>\n", "      <td>F&amp;F</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539</th>\n", "      <td>Help customer</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55062</th>\n", "      <td>Help customer</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59713</th>\n", "      <td>Maintanance activities</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15338</th>\n", "      <td>On demand</td>\n", "      <td>Other (notes)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6892</th>\n", "      <td>On demand</td>\n", "      <td>Admin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1816</th>\n", "      <td>Other</td>\n", "      <td>Meeting (more than 2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>272</th>\n", "      <td>Other</td>\n", "      <td>Discussion with colleagues (2 people)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>247</th>\n", "      <td>Other</td>\n", "      <td>Walking (can't identify specific activity)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243</th>\n", "      <td>Other</td>\n", "      <td>Walk to personal area / back</td>\n", "    </tr>\n", "    <tr>\n", "      <th>603</th>\n", "      <td>Other</td>\n", "      <td>Idle time</td>\n", "    </tr>\n", "    <tr>\n", "      <th>970</th>\n", "      <td>Other</td>\n", "      <td>Ineffective</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>Other</td>\n", "      <td>Can't find the person</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>Other</td>\n", "      <td>Break</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11271</th>\n", "      <td>Other</td>\n", "      <td>Take training</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9688</th>\n", "      <td>Other</td>\n", "      <td>Other, please take notes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14166</th>\n", "      <td>Service</td>\n", "      <td>TILLS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11364</th>\n", "      <td>Service</td>\n", "      <td>TILLS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>Service</td>\n", "      <td>ASCO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1006</th>\n", "      <td>Service</td>\n", "      <td>Ask help of manager / Managerial help</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23256</th>\n", "      <td>Service</td>\n", "      <td>Other (take notes)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>172</th>\n", "      <td>Service</td>\n", "      <td>TILLS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8194</th>\n", "      <td>Service</td>\n", "      <td>ASCO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>455</th>\n", "      <td>Service</td>\n", "      <td>Other Service activites</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>Service</td>\n", "      <td>Money handling (counting, change, add money, e...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>246</th>\n", "      <td>Service</td>\n", "      <td>Customer Service activities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3030</th>\n", "      <td>Shopfloor</td>\n", "      <td>Print/Handle POS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13511</th>\n", "      <td>Shopfloor</td>\n", "      <td>Other (notes)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6802</th>\n", "      <td>Shopfloor</td>\n", "      <td>Loss prevention (make notes)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3510</th>\n", "      <td>Shopfloor</td>\n", "      <td>Planogram Change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3338</th>\n", "      <td>Shopfloor</td>\n", "      <td>Promo Change</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9563</th>\n", "      <td>Shopfloor</td>\n", "      <td>Take back products to SF</td>\n", "    </tr>\n", "    <tr>\n", "      <th>657</th>\n", "      <td>Shopfloor</td>\n", "      <td>Product tagging</td>\n", "    </tr>\n", "    <tr>\n", "      <th>854</th>\n", "      <td>Shopfloor</td>\n", "      <td>Gap scan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>Shopfloor</td>\n", "      <td>Handle CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>Shopfloor</td>\n", "      <td>Safe and legal, traceability, fill documents, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>Shopfloor</td>\n", "      <td>Price verification</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>Shopfloor</td>\n", "      <td>Product prep (slice, cut) - Counter</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>Shopfloor</td>\n", "      <td>Counter Opening/ Closing routine</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>Shopfloor</td>\n", "      <td>Facing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>Shopfloor</td>\n", "      <td>Counter Cleaning</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>Shopfloor</td>\n", "      <td>Price change process</td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>Shopfloor</td>\n", "      <td>CCLB</td>\n", "    </tr>\n", "    <tr>\n", "      <th>406</th>\n", "      <td>Shopfloor</td>\n", "      <td>F&amp;F - clothing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Shopfloor</td>\n", "      <td>Replenishment</td>\n", "    </tr>\n", "    <tr>\n", "      <th>592</th>\n", "      <td>Shopfloor</td>\n", "      <td>WIBI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Shopfloor</td>\n", "      <td>Tidy up after replenishment, rip apart RSU</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>Shopfloor</td>\n", "      <td>Serve customer - Counters</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Store</td>\n", "      <td>Tablet Use for Bakery</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>Store</td>\n", "      <td>RTC - yellow label, IRE, Online</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>Store</td>\n", "      <td>Packaging, bottles and crates/RSU</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Store</td>\n", "      <td>Stock movement</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>Store</td>\n", "      <td>Handle waste and donation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11095</th>\n", "      <td>Store</td>\n", "      <td>Check scales</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>Store</td>\n", "      <td>Inventory</td>\n", "    </tr>\n", "    <tr>\n", "      <th>900</th>\n", "      <td>Store</td>\n", "      <td>Bottle return activites</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7242</th>\n", "      <td>Store</td>\n", "      <td>Other (notes)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>173</th>\n", "      <td>Store</td>\n", "      <td>Sending back News &amp; Magazines</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1319</th>\n", "      <td>Store</td>\n", "      <td>Pre-sort</td>\n", "    </tr>\n", "    <tr>\n", "      <th>728</th>\n", "      <td>Store</td>\n", "      <td>Post-sort</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>Store</td>\n", "      <td>Other with PDCU, can't identify</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7477</th>\n", "      <td>Warehouse</td>\n", "      <td>Prepare new delivery of F&amp;F items</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13549</th>\n", "      <td>Warehouse</td>\n", "      <td>Charging the equipments</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>Warehouse</td>\n", "      <td>Receiving deliveries</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6946</th>\n", "      <td>Warehouse</td>\n", "      <td>Waste Shipping</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6822</th>\n", "      <td>Warehouse</td>\n", "      <td>Other (notes)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2141</th>\n", "      <td>Warehouse</td>\n", "      <td>Other warehouse activities</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27615</th>\n", "      <td>Warehouse</td>\n", "      <td>Receiving News &amp; Magazines</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1464</th>\n", "      <td>Warehouse</td>\n", "      <td>Transfers and returns</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Warehouse</td>\n", "      <td>Bakery production</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      Level_2  \\\n", "234       Admin & Management    \n", "6997      Admin & Management    \n", "509       Admin & Management    \n", "36        Admin & Management    \n", "77        Admin & Management    \n", "1018      Admin & Management    \n", "61464     Admin & Management    \n", "904                 Cleaning    \n", "9107                Cleaning    \n", "550                 Cleaning    \n", "10589               Cleaning    \n", "183                 Cleaning    \n", "56                  Cleaning    \n", "68079               Cleaning    \n", "868                 Cleaning    \n", "41                  Cleaning    \n", "23762               Cleaning    \n", "24086               Cleaning    \n", "58310               Cleaning    \n", "1098                Cleaning    \n", "539            Help customer    \n", "55062          Help customer    \n", "59713  Maintanance activities   \n", "15338               On demand   \n", "6892                On demand   \n", "1816                   Other    \n", "272                    Other    \n", "247                    Other    \n", "243                    Other    \n", "603                    Other    \n", "970                    Other    \n", "48                     Other    \n", "158                    Other    \n", "11271                  Other    \n", "9688                   Other    \n", "14166                 Service   \n", "11364                 Service   \n", "188                  Service    \n", "1006                 Service    \n", "23256                Service    \n", "172                  Service    \n", "8194                 Service    \n", "455                  Service    \n", "250                  Service    \n", "246                  Service    \n", "3030                Shopfloor   \n", "13511               Shopfloor   \n", "6802                Shopfloor   \n", "3510                Shopfloor   \n", "3338                Shopfloor   \n", "9563                Shopfloor   \n", "657                 Shopfloor   \n", "854                 Shopfloor   \n", "106                 Shopfloor   \n", "107                 Shopfloor   \n", "46                  Shopfloor   \n", "44                  Shopfloor   \n", "165                 Shopfloor   \n", "70                  Shopfloor   \n", "37                  Shopfloor   \n", "43                  Shopfloor   \n", "207                 Shopfloor   \n", "406                 Shopfloor   \n", "7                   Shopfloor   \n", "592                 Shopfloor   \n", "2                   Shopfloor   \n", "176                 Shopfloor   \n", "5                       Store   \n", "49                      Store   \n", "58                      Store   \n", "1                       Store   \n", "60                      Store   \n", "11095                   Store   \n", "78                      Store   \n", "900                     Store   \n", "7242                    Store   \n", "173                     Store   \n", "1319                    Store   \n", "728                     Store   \n", "61                      Store   \n", "7477                Warehouse   \n", "13549               Warehouse   \n", "108                 Warehouse   \n", "6946                Warehouse   \n", "6822                Warehouse   \n", "2141                Warehouse   \n", "27615               Warehouse   \n", "1464                Warehouse   \n", "0                   Warehouse   \n", "\n", "                                                 Level_3  \n", "234                                         Cash Office   \n", "6997                                       Other (notes)  \n", "509                             Store manager activities  \n", "36                                Management activities   \n", "77                                              PC work   \n", "1018                                      F&F management  \n", "61464                            Mid year cash inventory  \n", "904            Outside store ( Car park, walking zones)   \n", "9107                 Administration, paperwork, sign ups  \n", "550                                             Counters  \n", "10589  AD-HOC REQUESTS (Broken drinks, cases, etc.- P...  \n", "183                                            Back area  \n", "56                                       Trash movement   \n", "68079                                   Cleaning baskets  \n", "868                                                 Mall  \n", "41                                             Shopfloor  \n", "23762                                     Other  (notes)  \n", "24086                                             Dotcom  \n", "58310                                               mall  \n", "1098                                                 F&F  \n", "539                                                    0  \n", "55062                                                NaN  \n", "59713                                                NaN  \n", "15338                                      Other (notes)  \n", "6892                                               Admin  \n", "1816                              Meeting (more than 2)   \n", "272                Discussion with colleagues (2 people)  \n", "247           Walking (can't identify specific activity)  \n", "243                         Walk to personal area / back  \n", "603                                            Idle time  \n", "970                                          Ineffective  \n", "48                                Can't find the person   \n", "158                                                Break  \n", "11271                                     Take training   \n", "9688                            Other, please take notes  \n", "14166                                             TILLS   \n", "11364                                              TILLS  \n", "188                                                ASCO   \n", "1006              Ask help of manager / Managerial help   \n", "23256                                Other (take notes)   \n", "172                                               TILLS   \n", "8194                                                ASCO  \n", "455                              Other Service activites  \n", "250    Money handling (counting, change, add money, e...  \n", "246                          Customer Service activities  \n", "3030                                    Print/Handle POS  \n", "13511                                      Other (notes)  \n", "6802                        Loss prevention (make notes)  \n", "3510                                    Planogram Change  \n", "3338                                        Promo Change  \n", "9563                            Take back products to SF  \n", "657                                      Product tagging  \n", "854                                             Gap scan  \n", "106                                          Handle CAYG  \n", "107    Safe and legal, traceability, fill documents, ...  \n", "46                                    Price verification  \n", "44                  Product prep (slice, cut) - Counter   \n", "165                     Counter Opening/ Closing routine  \n", "70                                                Facing  \n", "37                                      Counter Cleaning  \n", "43                                  Price change process  \n", "207                                                 CCLB  \n", "406                                       F&F - clothing  \n", "7                                          Replenishment  \n", "592                                                 WIBI  \n", "2             Tidy up after replenishment, rip apart RSU  \n", "176                            Serve customer - Counters  \n", "5                                  Tablet Use for Bakery  \n", "49                       RTC - yellow label, IRE, Online  \n", "58                    Packaging, bottles and crates/RSU   \n", "1                                         Stock movement  \n", "60                            Handle waste and donation   \n", "11095                                       Check scales  \n", "78                                             Inventory  \n", "900                              Bottle return activites  \n", "7242                                       Other (notes)  \n", "173                        Sending back News & Magazines  \n", "1319                                            Pre-sort  \n", "728                                            Post-sort  \n", "61                       Other with PDCU, can't identify  \n", "7477                   Prepare new delivery of F&F items  \n", "13549                            Charging the equipments  \n", "108                                 Receiving deliveries  \n", "6946                                     Waste Shipping   \n", "6822                                       Other (notes)  \n", "2141                         Other warehouse activities   \n", "27615                        Receiving News & Magazines   \n", "1464                               Transfers and returns  \n", "0                                     Bakery production   "]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.set_option(\"display.max_rows\", None)\n", "b[['Level_2','Level_3']].drop_duplicates().sort_values(by=['Level_2'])"]}, {"cell_type": "code", "execution_count": 47, "id": "ea32933c-e22d-4fa2-9864-493c190fc28b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>store</th>\n", "      <th>ROLE</th>\n", "      <th>Level_1</th>\n", "      <th>Level_2</th>\n", "      <th>Level_3</th>\n", "      <th>Level_4</th>\n", "      <th>Level_5</th>\n", "      <th>Level_6</th>\n", "      <th>DURATION</th>\n", "      <th>Duration (hours)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Warehouse</td>\n", "      <td>Bakery production</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Store</td>\n", "      <td>Stock movement</td>\n", "      <td>Move bakery product from chiller to bakery</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Shopfloor</td>\n", "      <td>Tidy up after replenishment, rip apart RSU</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Store</td>\n", "      <td>Stock movement</td>\n", "      <td>Move bakery product from chiller to bakery</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Store</td>\n", "      <td>Stock movement</td>\n", "      <td>Move bakery product from chiller to bakery</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   store    ROLE        Level_1    Level_2  \\\n", "0  11122  Bakery  Jan <PERSON>  Warehouse   \n", "1  11122  Bakery  Jan <PERSON>      Store   \n", "2  11122  <PERSON><PERSON>  Shopfloor   \n", "3  11122  Bakery  Jan <PERSON>      Store   \n", "4  11122  <PERSON>y  Jan <PERSON>   \n", "\n", "                                      Level_3  \\\n", "0                          Bakery production    \n", "1                              Stock movement   \n", "2  Tidy up after replenishment, rip apart RSU   \n", "3                              Stock movement   \n", "4                              Stock movement   \n", "\n", "                                      Level_4 Level_5 Level_6  DURATION  \\\n", "0                                           0       0       0         5   \n", "1  Move bakery product from chiller to bakery       0       0         5   \n", "2                                           0       0       0         5   \n", "3  Move bakery product from chiller to bakery       0       0         5   \n", "4  Move bakery product from chiller to bakery       0       0         5   \n", "\n", "   Duration (hours)  \n", "0          0.083333  \n", "1          0.083333  \n", "2          0.083333  \n", "3          0.083333  \n", "4          0.083333  "]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["b.head()"]}, {"cell_type": "code", "execution_count": 51, "id": "f5a39e7d-e93e-403f-94bf-1427f3e6ab35", "metadata": {}, "outputs": [{"data": {"text/plain": ["store\n", "11122    31.516667\n", "21047    46.583333\n", "41049    16.316667\n", "Name: Duration (hours), dtype: float64"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["b[(b.Level_3 == \"Tidy up after replenishment, rip apart RSU\")].groupby(\"store\")['Duration (hours)'].sum()"]}, {"cell_type": "code", "execution_count": 54, "id": "69f2afe0-1672-45a8-accb-8c318bc956c5", "metadata": {}, "outputs": [], "source": ["b_ex = b[b.Level_4.isin(['Produce', 'News & Magazines ', 'GM', 'Food Grocery ', 'Dairy - Prepacked Deli, Frozen', 'Dry', 'BWS ', 'Prepacked meat', 'Health and B. '])]"]}, {"cell_type": "code", "execution_count": 106, "id": "e5e615a3-e2d3-47b3-9c68-ae740ab0dc0d", "metadata": {"tags": []}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 61, "id": "46879f7f-de45-4789-b703-29d1cb110d63", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([11122, 21047, 41049], dtype=int64)"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["b[b.Level_4 == 'Prepacked meat']['store'].unique()"]}, {"cell_type": "code", "execution_count": 86, "id": "5d6182c0-efa4-4313-b4a1-bea40f69dd7a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>store</th>\n", "      <th>ROLE</th>\n", "      <th>Level_1</th>\n", "      <th>Level_2</th>\n", "      <th>Level_3</th>\n", "      <th>Level_4</th>\n", "      <th>Level_5</th>\n", "      <th>Level_6</th>\n", "      <th>DURATION</th>\n", "      <th>Duration (hours)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Warehouse</td>\n", "      <td>Bakery production</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Warehouse</td>\n", "      <td>Bakery production</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Warehouse</td>\n", "      <td>Bakery production</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Warehouse</td>\n", "      <td>Bakery production</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>11122</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Warehouse</td>\n", "      <td>Bakery production</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.083333</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    store    ROLE        Level_1    Level_2             Level_3 Level_4  \\\n", "0   11122  Bakery  Jan <PERSON>cher  Warehouse  Bakery production        0   \n", "6   11122  Bakery  Jan <PERSON>  Warehouse  Bakery production        0   \n", "8   11122  Bakery  Jan <PERSON>cher  Warehouse  Bakery production        0   \n", "14  11122  Bakery  Jan <PERSON>cher  Warehouse  Bakery production        0   \n", "15  11122  Bakery  Jan <PERSON>cher  Warehouse  Bakery production        0   \n", "\n", "   Level_5 Level_6  DURATION  Duration (hours)  \n", "0        0       0         5          0.083333  \n", "6        0       0         5          0.083333  \n", "8        0       0         5          0.083333  \n", "14       0       0         5          0.083333  \n", "15       0       0         5          0.083333  "]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["b[b.Level_3 == 'Bakery production '] .head()"]}, {"cell_type": "code", "execution_count": 103, "id": "514b4d93-afb9-4ff2-8198-be9188642fbb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 104, "id": "f5528008-9eff-4ffa-afa3-214b57f1eb2b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Tidy up after replenishment, rip apart RSU', 'Facing',\n", "       'Replenishment', 'Handle CAYG',\n", "       'Safe and legal, traceability, fill documents, check fridge',\n", "       'CCLB', 'WIBI', 'Product tagging', 'Take back products to SF'],\n", "      dtype=object)"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["b[b.Level_2 == \"Shopfloor\"].Level_3.unique()"]}, {"cell_type": "code", "execution_count": 91, "id": "6f1b76e4-4792-4102-aab2-1399cc52122a", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'SeriesGroupBy' object has no attribute 'groupby'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_1684\\2167829035.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mb_ex\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mgroupby\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m\"store\"\u001b[0m\u001b[1;33m,\u001b[0m  \u001b[1;34m\"Level_3\"\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'Duration (hours)'\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mgroupby\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"store\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'Duration (hours)'\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msum\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\AppData\\Roaming\\Python\\Python39\\site-packages\\pandas\\core\\groupby\\groupby.py\u001b[0m in \u001b[0;36m__getattr__\u001b[1;34m(self, attr)\u001b[0m\n\u001b[0;32m    950\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mattr\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    951\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 952\u001b[1;33m         raise AttributeError(\n\u001b[0m\u001b[0;32m    953\u001b[0m             \u001b[1;34mf\"'{type(self).__name__}' object has no attribute '{attr}'\"\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    954\u001b[0m         )\n", "\u001b[1;31mAttributeError\u001b[0m: 'SeriesGroupBy' object has no attribute 'groupby'"]}], "source": ["b_ex.groupby([\"store\",  \"Level_3\"])['Duration (hours)']"]}, {"cell_type": "code", "execution_count": null, "id": "26ac5bc5-adca-4e97-9f3a-76dc0a803e7d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 29, "id": "d01ac8d5-63aa-4e1e-b267-1f45d2b9fbed", "metadata": {}, "outputs": [], "source": ["a = a[[x for x in list(a.columns) if x not in ['Fix Hours',\\\n", "       'Variable Hours', 'Total Weekly Hours', 'Yearly GBP',\n", "       'Variable Currency']  ]]"]}, {"cell_type": "code", "execution_count": 36, "id": "60c6076b-ae5f-4128-b7a0-7d3f43a981aa", "metadata": {}, "outputs": [], "source": ["a = a.melt(id_vars=list(a.columns[:5]), var_name = \"act_groups\", value_name= \"hours\").head()"]}, {"cell_type": "code", "execution_count": null, "id": "7551055f-a0eb-448f-af29-cd85a326ca18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 60, "id": "1c4317f5-4d53-4bcc-b285-643136bebc8c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Country</th>\n", "      <th>Store</th>\n", "      <th>Format</th>\n", "      <th>Dep</th>\n", "      <th>Division</th>\n", "      <th>Fix Hours</th>\n", "      <th>Variable Hours</th>\n", "      <th>Total Weekly Hours</th>\n", "      <th>Yearly GBP</th>\n", "      <th>Variable Currency</th>\n", "      <th>Additional Hours</th>\n", "      <th>Arrange and clean back area</th>\n", "      <th>Backroom DotCom</th>\n", "      <th><PERSON><PERSON> return</th>\n", "      <th>Box Opening</th>\n", "      <th>Cleaning</th>\n", "      <th>Clip Strip</th>\n", "      <th>Customer Service</th>\n", "      <th>EPW</th>\n", "      <th>Facing-Rumble</th>\n", "      <th>Food Donation</th>\n", "      <th>Fresh waste shipping</th>\n", "      <th><PERSON><PERSON> Price</th>\n", "      <th>Managing Colleagues</th>\n", "      <th>Morning routines</th>\n", "      <th>Online Price Changes</th>\n", "      <th>PRO_WGLL - change layout</th>\n", "      <th>Packaging RSU movements</th>\n", "      <th>Packaging bottles and crates</th>\n", "      <th>Post-sort</th>\n", "      <th>Pre-sort</th>\n", "      <th>Preparation for Replenishment</th>\n", "      <th>Product Returns</th>\n", "      <th>Product tagging</th>\n", "      <th>RSU</th>\n", "      <th>RTC &amp; CCLB</th>\n", "      <th>RTV return</th>\n", "      <th>Range Check</th>\n", "      <th>Remitenda</th>\n", "      <th>Replenishment</th>\n", "      <th>Safe and legal</th>\n", "      <th>Stock Movement</th>\n", "      <th>Stock Movement WH</th>\n", "      <th>Torn packaging</th>\n", "      <th>Transfers</th>\n", "      <th>WGLL</th>\n", "      <th>WIBI</th>\n", "      <th>Waste</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>780</th>\n", "      <td>CZ</td>\n", "      <td>11122</td>\n", "      <td>Compact</td>\n", "      <td>DAI</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>17.355743</td>\n", "      <td>46.650124</td>\n", "      <td>64.005867</td>\n", "      <td>22466.059362</td>\n", "      <td>16096.878009</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.161643</td>\n", "      <td>2.398877</td>\n", "      <td>0.000000</td>\n", "      <td>1.742948</td>\n", "      <td>0.026780</td>\n", "      <td>0.000000</td>\n", "      <td>0.055164</td>\n", "      <td>0.0</td>\n", "      <td>2.901567</td>\n", "      <td>2.027864</td>\n", "      <td>0.0</td>\n", "      <td>1.381307</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.865303</td>\n", "      <td>0.628801</td>\n", "      <td>0.351594</td>\n", "      <td>0.327598</td>\n", "      <td>0.158947</td>\n", "      <td>2.290184</td>\n", "      <td>10.123078</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>23.944213</td>\n", "      <td>0.0</td>\n", "      <td>8.287661</td>\n", "      <td>0.0</td>\n", "      <td>0.686544</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>1.645794</td>\n", "    </tr>\n", "    <tr>\n", "      <th>781</th>\n", "      <td>CZ</td>\n", "      <td>11122</td>\n", "      <td>Compact</td>\n", "      <td>FRZ</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>5.141633</td>\n", "      <td>8.703958</td>\n", "      <td>13.845591</td>\n", "      <td>4859.802527</td>\n", "      <td>12724.890019</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.567790</td>\n", "      <td>0.695704</td>\n", "      <td>0.000000</td>\n", "      <td>0.211907</td>\n", "      <td>0.095223</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>1.194089</td>\n", "      <td>1.182921</td>\n", "      <td>0.0</td>\n", "      <td>1.221827</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.240277</td>\n", "      <td>0.118862</td>\n", "      <td>0.141873</td>\n", "      <td>0.311220</td>\n", "      <td>0.000000</td>\n", "      <td>0.202860</td>\n", "      <td>0.733200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>4.985058</td>\n", "      <td>0.0</td>\n", "      <td>0.609097</td>\n", "      <td>0.0</td>\n", "      <td>0.056260</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>1.277423</td>\n", "    </tr>\n", "    <tr>\n", "      <th>782</th>\n", "      <td>CZ</td>\n", "      <td>11122</td>\n", "      <td>Compact</td>\n", "      <td>PPD</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>9.761269</td>\n", "      <td>18.237006</td>\n", "      <td>27.998275</td>\n", "      <td>9827.394483</td>\n", "      <td>17388.637065</td>\n", "      <td>4.723043</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.674332</td>\n", "      <td>1.583109</td>\n", "      <td>0.000000</td>\n", "      <td>0.316204</td>\n", "      <td>0.106952</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>1.394988</td>\n", "      <td>1.182921</td>\n", "      <td>0.0</td>\n", "      <td>1.222817</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.458652</td>\n", "      <td>0.090758</td>\n", "      <td>0.205140</td>\n", "      <td>0.305272</td>\n", "      <td>0.000000</td>\n", "      <td>0.610447</td>\n", "      <td>3.211773</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>8.614298</td>\n", "      <td>0.0</td>\n", "      <td>1.621673</td>\n", "      <td>0.0</td>\n", "      <td>0.174667</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.501230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>783</th>\n", "      <td>CZ</td>\n", "      <td>11122</td>\n", "      <td>Compact</td>\n", "      <td>HDL</td>\n", "      <td>General Merchandise</td>\n", "      <td>14.221833</td>\n", "      <td>42.174615</td>\n", "      <td>56.396448</td>\n", "      <td>19795.153199</td>\n", "      <td>10976.820540</td>\n", "      <td>1.733333</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.157167</td>\n", "      <td>0.124016</td>\n", "      <td>0.215089</td>\n", "      <td>0.885098</td>\n", "      <td>0.231491</td>\n", "      <td>1.067733</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>7.170605</td>\n", "      <td>1.689887</td>\n", "      <td>0.0</td>\n", "      <td>1.369420</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.974137</td>\n", "      <td>1.941243</td>\n", "      <td>0.541419</td>\n", "      <td>0.562263</td>\n", "      <td>0.486805</td>\n", "      <td>0.806447</td>\n", "      <td>0.938296</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>19.072527</td>\n", "      <td>0.0</td>\n", "      <td>4.397299</td>\n", "      <td>0.0</td>\n", "      <td>0.217282</td>\n", "      <td>0.0</td>\n", "      <td>7.40041</td>\n", "      <td>0.0</td>\n", "      <td>1.414479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>784</th>\n", "      <td>CZ</td>\n", "      <td>11122</td>\n", "      <td>Compact</td>\n", "      <td>NEW</td>\n", "      <td>General Merchandise</td>\n", "      <td>0.311781</td>\n", "      <td>2.098562</td>\n", "      <td>2.410343</td>\n", "      <td>846.030355</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.001159</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.002290</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.020020</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.478932</td>\n", "      <td>0.660373</td>\n", "      <td>0.0</td>\n", "      <td>0.247568</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Country  Store   Format  Dep             Division  Fix Hours  \\\n", "780      CZ  11122  Compact  DAI      Prepacked Fresh  17.355743   \n", "781      CZ  11122  Compact  FRZ      Prepacked Fresh   5.141633   \n", "782      CZ  11122  Compact  PPD      Prepacked Fresh   9.761269   \n", "783      CZ  11122  Compact  HDL  General Merchandise  14.221833   \n", "784      CZ  11122  Compact  NEW  General Merchandise   0.311781   \n", "\n", "     Variable Hours  Total Weekly Hours    Yearly GBP  Variable Currency  \\\n", "780       46.650124           64.005867  22466.059362       16096.878009   \n", "781        8.703958           13.845591   4859.802527       12724.890019   \n", "782       18.237006           27.998275   9827.394483       17388.637065   \n", "783       42.174615           56.396448  19795.153199       10976.820540   \n", "784        2.098562            2.410343    846.030355           0.000000   \n", "\n", "     Additional Hours  Arrange and clean back area  Backroom DotCom  \\\n", "780          0.000000                          0.0              0.0   \n", "781          0.000000                          0.0              0.0   \n", "782          4.723043                          0.0              0.0   \n", "783          1.733333                          0.0              0.0   \n", "784          0.000000                          0.0              0.0   \n", "\n", "     Bottle return  Box Opening  Cleaning  Clip Strip  Customer Service  \\\n", "780            0.0     4.161643  2.398877    0.000000          1.742948   \n", "781            0.0     0.567790  0.695704    0.000000          0.211907   \n", "782            0.0     1.674332  1.583109    0.000000          0.316204   \n", "783            0.0     3.157167  0.124016    0.215089          0.885098   \n", "784            0.0     0.000000  0.001159    0.000000          0.000000   \n", "\n", "          EPW  Facing-Rumble  Food Donation  Fresh waste shipping  \\\n", "780  0.026780       0.000000       0.055164                   0.0   \n", "781  0.095223       0.000000       0.000000                   0.0   \n", "782  0.106952       0.000000       0.000000                   0.0   \n", "783  0.231491       1.067733       0.000000                   0.0   \n", "784  0.000000       0.000000       0.000000                   0.0   \n", "\n", "     <PERSON><PERSON> Price  Managing Colleagues  Morning routines  \\\n", "780      2.901567             2.027864               0.0   \n", "781      1.194089             1.182921               0.0   \n", "782      1.394988             1.182921               0.0   \n", "783      7.170605             1.689887               0.0   \n", "784      0.000000             0.000000               0.0   \n", "\n", "     Online Price Changes  PRO_WGLL - change layout  Packaging RSU movements  \\\n", "780              1.381307                       0.0                      0.0   \n", "781              1.221827                       0.0                      0.0   \n", "782              1.222817                       0.0                      0.0   \n", "783              1.369420                       0.0                      0.0   \n", "784              0.000000                       0.0                      0.0   \n", "\n", "     Packaging bottles and crates  Post-sort  Pre-sort  \\\n", "780                           0.0   0.865303  0.628801   \n", "781                           0.0   0.240277  0.118862   \n", "782                           0.0   0.458652  0.090758   \n", "783                           0.0   0.974137  1.941243   \n", "784                           0.0   0.002290  0.000000   \n", "\n", "     Preparation for Replenishment  Product Returns  Product tagging  \\\n", "780                       0.351594         0.327598         0.158947   \n", "781                       0.141873         0.311220         0.000000   \n", "782                       0.205140         0.305272         0.000000   \n", "783                       0.541419         0.562263         0.486805   \n", "784                       0.000000         0.020020         0.000000   \n", "\n", "          RSU  RTC & CCLB  RTV return  Range Check  Remitenda  Replenishment  \\\n", "780  2.290184   10.123078         0.0          0.0   0.000000      23.944213   \n", "781  0.202860    0.733200         0.0          0.0   0.000000       4.985058   \n", "782  0.610447    3.211773         0.0          0.0   0.000000       8.614298   \n", "783  0.806447    0.938296         0.0          0.0   0.000000      19.072527   \n", "784  0.000000    0.000000         0.0          0.0   1.478932       0.660373   \n", "\n", "     Safe and legal  Stock Movement  Stock Movement WH  Torn packaging  \\\n", "780             0.0        8.287661                0.0        0.686544   \n", "781             0.0        0.609097                0.0        0.056260   \n", "782             0.0        1.621673                0.0        0.174667   \n", "783             0.0        4.397299                0.0        0.217282   \n", "784             0.0        0.247568                0.0        0.000000   \n", "\n", "     Transfers     WGLL  WIBI     Waste  \n", "780        0.0  0.00000   0.0  1.645794  \n", "781        0.0  0.00000   0.0  1.277423  \n", "782        0.0  0.00000   0.0  0.501230  \n", "783        0.0  7.40041   0.0  1.414479  \n", "784        0.0  0.00000   0.0  0.000000  "]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["a.groupby(['Store'])[['Facing-Rumble', '']]"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}