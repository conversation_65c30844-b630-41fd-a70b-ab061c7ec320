{"cells": [{"cell_type": "code", "execution_count": 1, "id": "433b2f81-89d5-4eb4-b7b7-33d98871ecf0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "pd.options.display.float_format = \"{:,.0f}\".format\n", "\n", "a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\model_outputs\\Q1_v3_fluct_cust_pbs\\INSIGHT_Q1_v3_fluct_cust_pbs.parquet.gz\")"]}, {"cell_type": "code", "execution_count": 2, "id": "bc817d3b-70e8-4602-a565-e3fb391188d3", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Country', 'Store', 'Format', 'Division', 'Activity_Group',\n", "       'Suboperation', 'Driver_1', 'Driver_2', 'Driver_3', 'Driver_4',\n", "       'Driver_1_value', 'Driver_2_value', 'Driver_3_value', 'Driver_4_value',\n", "       'hours', 'Yearly GBP', 'Model'],\n", "      dtype='object')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["a.columns"]}, {"cell_type": "code", "execution_count": 9, "id": "dd747a13-af60-4e77-8231-c2229e098b0d", "metadata": {}, "outputs": [{"data": {"text/plain": ["Activity_Group\n", "Pre-sort   680,601\n", "Name: Yearly GBP, dtype: float64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["a[a.Activity_Group == 'Pre-sort'].groupby(['Activity_Group'], observed=True)['Yearly GBP'].sum()"]}, {"cell_type": "code", "execution_count": 2, "id": "ab10079b-e72a-4309-8eaf-58f39af8de2f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Country  Activity_Group\n", "CZ       Pre-sort         238,134\n", "HU       Pre-sort         221,626\n", "SK       Pre-sort         220,841\n", "Name: Yearly GBP, dtype: float64"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["a[a.Activity_Group == 'Pre-sort'].groupby(['Country', 'Activity_Group'], observed=True)['Yearly GBP'].sum()\n", "# .reset_index().to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\model_outputs\\Q1_v3_fluct_cust_pbs\\pre-sort_cost_base.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "088baeb9-037b-4d10-8319-79cc3c441ce4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "id": "884092b4-055e-4e5a-8cd2-4cc27f8762fe", "metadata": {}, "outputs": [{"data": {"text/plain": ["Country  Activity_Group  Suboperation                                         \n", "CZ       Pre-sort        Get and assemble one empty rollcage with cage shelves    20,263\n", "                         Get shelf trolley                                         5,602\n", "                         Make additional adjustment during Pre Sorting             2,605\n", "                         Push back full cage                                      10,419\n", "                         Remove paper sheet                                        9,596\n", "                         Transfer heavy case to cage/shelf trolley                43,030\n", "                         Transfer heavy crates to cage/shelf trolley              36,037\n", "                         Transfer light case to cage/shelf trolley               103,886\n", "                         Transfer light crates to cage/shelf trolley               6,696\n", "HU       Pre-sort        Get and assemble one empty rollcage with cage shelves    20,065\n", "                         Get shelf trolley                                         3,341\n", "                         Make additional adjustment during Pre Sorting             2,335\n", "                         Push back full cage                                       9,340\n", "                         Remove paper sheet                                        8,603\n", "                         Transfer heavy case to cage/shelf trolley                47,504\n", "                         Transfer heavy crates to cage/shelf trolley              33,177\n", "                         Transfer light case to cage/shelf trolley                94,067\n", "                         Transfer light crates to cage/shelf trolley               3,195\n", "SK       Pre-sort        Get and assemble one empty rollcage with cage shelves    15,384\n", "                         Get shelf trolley                                         6,130\n", "                         Make additional adjustment during Pre Sorting             2,521\n", "                         Push back full cage                                      10,083\n", "                         Remove paper sheet                                        9,287\n", "                         Transfer heavy case to cage/shelf trolley                50,867\n", "                         Transfer heavy crates to cage/shelf trolley              31,058\n", "                         Transfer light case to cage/shelf trolley                91,330\n", "                         Transfer light crates to cage/shelf trolley               4,181\n", "Name: Yearly GBP, dtype: float64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["a[a.Activity_Group == 'Pre-sort'].groupby(['Country','Activity_Group', 'Suboperation'], observed=True)['Yearly GBP'].sum()"]}, {"cell_type": "code", "execution_count": 15, "id": "844aa01f-14c2-49fd-9118-b31e27a3ba68", "metadata": {}, "outputs": [{"data": {"text/plain": ["Country  Activity_Group  Suboperation                                        \n", "CZ       Stock Movement  Additional rollcage movements during Replenishment     185,224\n", "                         Additional walking with the cases taken from pallets   733,137\n", "HU       Stock Movement  Additional rollcage movements during Replenishment     346,650\n", "                         Additional walking with the cases taken from pallets   554,898\n", "SK       Stock Movement  Additional rollcage movements during Replenishment     106,140\n", "                         Additional walking with the cases taken from pallets   798,768\n", "Name: Yearly GBP, dtype: float64"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["a[(a.Division=='Grocery')&(a.Suboperation.isin( ['Additional walking with the cases taken from pallets','Additional rollcage movements during Replenishment']))].groupby(['Country','Activity_Group', 'Suboperation'], observed=True)['Yearly GBP'].sum()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}