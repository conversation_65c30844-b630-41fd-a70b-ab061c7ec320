{"cells": [{"cell_type": "code", "execution_count": 197, "id": "b68e20c8-cbfd-4c57-b390-8ee768a7b325", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_4788\\70172230.py:100: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise in a future error of pandas. Value 'BAK' has dtype incompatible with float64, please explicitly cast to a compatible dtype first.\n", "  mods_ce.loc[mods_ce['drg'].str.startswith(drg_letter), 'dep'] = dep\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import shutil\n", "import openpyxl\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "\n", "\n", "def get_latest_folder(directory):\n", "    # List all directories in the given directory\n", "    all_directories = [os.path.join(directory, d) for d in os.listdir(directory) if os.path.isdir(os.path.join(directory, d))]\n", "    \n", "    # Get the most recently modified directory\n", "    latest_folder = max(all_directories, key=os.path.getmtime)\n", "    \n", "    return latest_folder\n", "\n", "def get_latest_xlsx_file(directory, country):\n", "    # List all files in the given directory, excluding hidden files and files starting with ~$\n", "    all_files = [os.path.join(directory, f) for f in os.listdir(directory) if not f.startswith('~$') and not f.startswith('.') and os.path.isfile(os.path.join(directory, f))]\n", "\n", "    if country == 'HU':\n", "        # Filter files to include only .xlsx files\n", "        xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx')]\n", "    if country == 'CZ':\n", "        xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx') and f.__contains__('CZ.')]\n", "    if country == 'SK':\n", "        xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx') and f.__contains__('SK.')]\n", "    \n", "    if not xlsx_files:\n", "        return None  # No .xlsx files found\n", "    \n", "    # Get the most recently modified .xlsx file\n", "    latest_xlsx_file = max(xlsx_files, key=os.path.getmtime)\n", "    \n", "    return latest_xlsx_file\n", "\n", "\n", "\n", "\n", "def read_xlsx_sheets(filepath):\n", "    # Read all sheets of the Excel file into a dictionary of DataFrames\n", "    xls = pd.ExcelFile(filepath)\n", "    sheets = {}\n", "    for sheet_name in xls.sheet_names:\n", "        sheets[sheet_name] = pd.read_excel(xls, sheet_name)\n", "    \n", "    # Concatenate all DataFrames into a single DataFrame\n", "    combined_df = pd.concat(sheets.values(), ignore_index=True)\n", "\n", "    return combined_df\n", "\n", "def copy_excel_file(source, destination):\n", "    shutil.copy(source, destination)\n", "\n", "# HU part\n", "hu_path = r\"\\\\huprgvmfs05\\CE_SRD_IDR_SHARED\\REPORT\\DB_Report_SRD_6462376\\DB Report Hungary\"\n", "latest_folder_hu = get_latest_folder(hu_path)\n", "mods_hu_f = get_latest_xlsx_file(latest_folder_hu, 'HU')\n", "mods_hu = read_xlsx_sheets(mods_hu_f)\n", "mods_hu = mods_hu.query(\"Status == 'Live'\")\n", "mods_hu = mods_hu[['Store Number', 'Department (Desc1)', 'Merchandising Group','Merch. Group Local Description', 'Merchandising Group Description', 'No of mods']].drop_duplicates()\n", "mods_hu['Store Number'] = mods_hu['Store Number'].map(lambda x: str(4) + str(x)).astype(\"int\")\n", "\n", "# SK CZ part\n", "sk_cz_path = r\"\\\\huprgvmfs05\\CE_SRD_IDR_SHARED\\REPORT\\DB_Report_SRD_6462376\\DB Report CZ  & SK\\CZ&SK\"\n", "latest_folder_sk_cz = get_latest_folder(sk_cz_path)\n", "latest_folder_sk_cz = get_latest_folder(latest_folder_sk_cz)\n", "mods_sk_f = get_latest_xlsx_file(latest_folder_sk_cz, 'SK')\n", "mods_sk = read_xlsx_sheets(mods_sk_f)\n", "mods_sk['Store Number'] = mods_sk['Store Number'].map(lambda x: str(2) + str(x)).astype(\"int\")\n", "mods_cz_f = get_latest_xlsx_file(latest_folder_sk_cz, 'CZ')\n", "mods_cz = read_xlsx_sheets(mods_cz_f)\n", "mods_cz['Store Number'] = mods_cz['Store Number'].map(lambda x: str(1) + str(x)).astype(\"int\")\n", "mods_cz_sk = pd.concat([mods_sk, mods_cz])\n", "mods_cz_sk = mods_cz_sk[mods_cz_sk[\"Planogram Status\"] == 'Live']\n", "mods_cz_sk = mods_cz_sk[['Store Number', 'Merchandising Group', 'Merchandising Group Description', 'POG No Of Mods']].drop_duplicates()\n", "mods_cz_sk.rename(columns={'POG No Of Mods':'No of mods'}, inplace=True)\n", "\n", "mods_ce = pd.concat([mods_hu, mods_cz_sk])\n", "mods_ce.rename(columns={'Merchandising Group':'drg', 'Store Number':'store'}, inplace=True)\n", "\n", "\n", "\n", "# # DRG mapping part\n", "# source_path = r\"\\\\czprgvmfs03\\CERange\\CE RANGE\\Mastersheet CE Range\\Mastersheet stuff\\DRG MAPPING.xlsx\"\n", "# destination_path = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\Moduls\"\n", "\n", "# copy_excel_file(source_path, destination_path)\n", "\n", "key_to_drg_f = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\Repl\\Stores_Inputs_2024_Q1_wo_prepacked_hours_fluct_cust_repaired_N.xlsx\" \n", "key_to_drg = pd.read_excel(key_to_drg_f, \"key_table_moduls\")\n", "\n", "# Create a dictionary mapping drg_letter to department\n", "drg_dep_mapping = key_to_drg.set_index('drg_letter')['dep'].to_dict()\n", "\n", "for drg_letter, dep in drg_dep_mapping.items():\n", "    mods_ce.loc[mods_ce['drg'].str.startswith(drg_letter), 'dep'] = dep\n", "\n", "# OFD or PROMO or CAYG\n", "mods_ce['dep'] = np.where((mods_ce['Merchandising Group Description'].str.contains('OFD'))\n", "  | (mods_ce['Merchandising Group Description'].str.contains('PROMO'))\n", "    |(mods_ce['Merchandising Group Description'].str.contains('CAYG')), 'PROMO_OFD_CAYG', mods_ce['dep'])\n", "\n", "# free form & healthy food to PRO\n", "mods_ce['dep'] = np.where((mods_ce['Merchandising Group Description'].str.contains('FREE_FROM'))\n", "  | (mods_ce['Merchandising Group Description'].str.contains('HEALTHY_FOOD')), 'PRO', mods_ce['dep'])\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 206, "id": "daba7956-4ac9-49d5-8d30-dec7dbf70b7d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>store</th>\n", "      <th>Department (Desc1)</th>\n", "      <th>drg</th>\n", "      <th>Merch. Group Local Description</th>\n", "      <th>Merchandising Group Description</th>\n", "      <th>No of mods</th>\n", "      <th>dep</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>12550</th>\n", "      <td>41520</td>\n", "      <td>ENTERTAINMENT</td>\n", "      <td>P1H</td>\n", "      <td>SZORAKOZTATO_OFD</td>\n", "      <td>ENTERTAINMENT_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12775</th>\n", "      <td>41520</td>\n", "      <td>EXTRA GRF OFD</td>\n", "      <td>V1T</td>\n", "      <td>EXTRA_GRNF_OFD</td>\n", "      <td>EXTRA_GRNF_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12582</th>\n", "      <td>41520</td>\n", "      <td>FLOWER OFD</td>\n", "      <td>V1Y</td>\n", "      <td>FLOWER_OFD</td>\n", "      <td>FLOWER_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12643</th>\n", "      <td>41520</td>\n", "      <td>FROZEN_FOOD</td>\n", "      <td>V5D</td>\n", "      <td>MIRELIT_OFD_UNILEVER</td>\n", "      <td>FROZEN_OFD_UNILEVER</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12645</th>\n", "      <td>41520</td>\n", "      <td>FROZEN_FOOD</td>\n", "      <td>Z3U</td>\n", "      <td>MIRELIT_JEGKOCKA</td>\n", "      <td>FROZEN_OFD_ICECUBE</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12762</th>\n", "      <td>41520</td>\n", "      <td>FROZEN_FOOD</td>\n", "      <td>Z4W</td>\n", "      <td>MIRELIT_JEGKREM</td>\n", "      <td>FROZEN_OFD_HAAGEN-DAZS</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12761</th>\n", "      <td>41520</td>\n", "      <td>FROZEN_FOOD</td>\n", "      <td>Z4W</td>\n", "      <td>MIRELIT_JEGKREM</td>\n", "      <td>FROZEN_OFD_BEN&amp;JERRYS</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12651</th>\n", "      <td>41520</td>\n", "      <td>FROZEN_IN_OUT</td>\n", "      <td>Z3P</td>\n", "      <td>MIRELIT_IN_OUT</td>\n", "      <td>FROZEN_IN-OUT</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12715</th>\n", "      <td>41520</td>\n", "      <td>Food Grocery</td>\n", "      <td>Z1O</td>\n", "      <td>AJANDEKKOSAR</td>\n", "      <td>Gift basket</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12649</th>\n", "      <td>41520</td>\n", "      <td>GRF_WINE_TOWER</td>\n", "      <td>W4G</td>\n", "      <td>GRF BORTORONY</td>\n", "      <td>GRF_WINE_TOWER</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12661</th>\n", "      <td>41520</td>\n", "      <td>HOME</td>\n", "      <td>Q4B</td>\n", "      <td>OTTHON_OFD</td>\n", "      <td>HOME_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12455</th>\n", "      <td>41520</td>\n", "      <td>HOME_GARDENING</td>\n", "      <td>W2G</td>\n", "      <td>NOHEL_HU_OFD</td>\n", "      <td>NOHEL_HU_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12454</th>\n", "      <td>41520</td>\n", "      <td>HOME_GARDENING</td>\n", "      <td>R7F</td>\n", "      <td>GARAFARM_HM_CHM_OFD</td>\n", "      <td>GARAFARM_HM_CHM_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12456</th>\n", "      <td>41520</td>\n", "      <td>HOME_GARDENING</td>\n", "      <td>R6L</td>\n", "      <td>BROOM_OFD</td>\n", "      <td>BROOM_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12453</th>\n", "      <td>41520</td>\n", "      <td>HOME_GARDENING</td>\n", "      <td>R6G</td>\n", "      <td>SOLO_FIRE_&amp;_BBQ_OFD</td>\n", "      <td>SOLO_FIRE_&amp;_BBQ_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12463</th>\n", "      <td>41520</td>\n", "      <td>HOUSEHOLD</td>\n", "      <td>K1H</td>\n", "      <td>VEGYI_PAPIR_OFD</td>\n", "      <td>HOUSEHOLD_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12639</th>\n", "      <td>41520</td>\n", "      <td>HOUSEHOLD</td>\n", "      <td>U1B</td>\n", "      <td>CAYG_HAZTARTAS</td>\n", "      <td>CAYG_HOUSEHOLD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12640</th>\n", "      <td>41520</td>\n", "      <td>PERSONAL CARE</td>\n", "      <td>U1B</td>\n", "      <td>CAYG_KOZMETIKA</td>\n", "      <td>CAYG_PERSONAL_CARE</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12667</th>\n", "      <td>41520</td>\n", "      <td>PET</td>\n", "      <td>M1O</td>\n", "      <td>ALLATELEDEL_OFD</td>\n", "      <td>PET_FOOD_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12726</th>\n", "      <td>41520</td>\n", "      <td>PET_PRODUCTS</td>\n", "      <td>U1B</td>\n", "      <td>CAYG_ALLAT</td>\n", "      <td>CAYG_PET_ACC_HL</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12481</th>\n", "      <td>41520</td>\n", "      <td>PRICE ANCHOR</td>\n", "      <td>U2B</td>\n", "      <td>ARGARANCIA</td>\n", "      <td>PRICE_ANCHOR_OFD_HM</td>\n", "      <td>18.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12538</th>\n", "      <td>41520</td>\n", "      <td>PRODUCE</td>\n", "      <td>W5H</td>\n", "      <td>HUTETLEN_ZOLDSEG_DOT_COM</td>\n", "      <td>AMBIENT_PRODUCE_DOTCOM</td>\n", "      <td>6.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12539</th>\n", "      <td>41520</td>\n", "      <td>PRODUCE</td>\n", "      <td>W5G</td>\n", "      <td>HUTOTT_ZOLDSEG_DOT_COM</td>\n", "      <td>CHILLED_PRODUCE_DOTCOM</td>\n", "      <td>6.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12797</th>\n", "      <td>41520</td>\n", "      <td>PROVISIONS</td>\n", "      <td>U1B</td>\n", "      <td>CAYG_CSEMEGE</td>\n", "      <td>CAYG_PROVISIONS</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12801</th>\n", "      <td>41520</td>\n", "      <td>PROVISIONS</td>\n", "      <td>V4V</td>\n", "      <td>GRILL_TERMEKEK</td>\n", "      <td>BBQ_PROMO</td>\n", "      <td>2.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12508</th>\n", "      <td>41520</td>\n", "      <td>SOFTDRINK</td>\n", "      <td>G1B</td>\n", "      <td>ALKOHOLMENTES_ITALOK_OFD</td>\n", "      <td>DRINKS_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12516</th>\n", "      <td>41520</td>\n", "      <td>SOFT_DRINKS</td>\n", "      <td>Z3I</td>\n", "      <td>SZALLITOI_VIZ_HUTO</td>\n", "      <td>SUPPLIER_CHILLED_WATER</td>\n", "      <td>2.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12518</th>\n", "      <td>41520</td>\n", "      <td>SOFT_DRINKS</td>\n", "      <td>U1B</td>\n", "      <td>CAYG_UDITO</td>\n", "      <td>CAYG_DRINKS</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12596</th>\n", "      <td>41520</td>\n", "      <td>SPORT</td>\n", "      <td>S1E</td>\n", "      <td>SPORT_OFD</td>\n", "      <td>SPORT_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12552</th>\n", "      <td>41520</td>\n", "      <td>STATIONARY</td>\n", "      <td>P3F</td>\n", "      <td>PAPIR_IROSZER_OFD</td>\n", "      <td>STATIONERY_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12566</th>\n", "      <td>41520</td>\n", "      <td>STATIONERY</td>\n", "      <td>U1S</td>\n", "      <td>GM_NEM_TERVEZETT_HELY_IROSZER</td>\n", "      <td>GM_FREE_SPACE_STATIONERY</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12731</th>\n", "      <td>41520</td>\n", "      <td>Spirits</td>\n", "      <td>Z3F</td>\n", "      <td>SZALLITOI MODUL (ROVIDITAL)</td>\n", "      <td>Spirit and Liqueurs</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12545</th>\n", "      <td>41520</td>\n", "      <td>TOYS</td>\n", "      <td>S3Y</td>\n", "      <td>JATEK_OFD</td>\n", "      <td>TOYS_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12641</th>\n", "      <td>41520</td>\n", "      <td>Tesco Magazine</td>\n", "      <td>P2D</td>\n", "      <td>TESCO_MAGAZINE_OFD</td>\n", "      <td>TESCO_MAGAZINE_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12594</th>\n", "      <td>41520</td>\n", "      <td>NaN</td>\n", "      <td>W6A</td>\n", "      <td>KASSZA_OFD</td>\n", "      <td>CHECKOUT_OFD</td>\n", "      <td>12.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12600</th>\n", "      <td>41520</td>\n", "      <td>NaN</td>\n", "      <td>U5C</td>\n", "      <td>EZ_+_AZ</td>\n", "      <td>PICKUP_LINES</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12671</th>\n", "      <td>41520</td>\n", "      <td>NaN</td>\n", "      <td>U1N</td>\n", "      <td>NaN</td>\n", "      <td>DOTCOM_GM</td>\n", "      <td>10.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       store Department (Desc1)  drg Merch. Group Local Description  \\\n", "12550  41520      ENTERTAINMENT  P1H               SZORAKOZTATO_OFD   \n", "12775  41520      EXTRA GRF OFD  V1T                 EXTRA_GRNF_OFD   \n", "12582  41520         FLOWER OFD  V1Y                     FLOWER_OFD   \n", "12643  41520        FROZEN_FOOD  V5D           MIRELIT_OFD_UNILEVER   \n", "12645  41520        FROZEN_FOOD  Z3U               MIRELIT_JEGKOCKA   \n", "12762  41520        FROZEN_FOOD  Z4W                MIRELIT_JEGKREM   \n", "12761  41520        FROZEN_FOOD  Z4W                MIRELIT_JEGKREM   \n", "12651  41520      FROZEN_IN_OUT  Z3P                 MIRELIT_IN_OUT   \n", "12715  41520       Food Grocery  Z1O                   AJANDEKKOSAR   \n", "12649  41520     GRF_WINE_TOWER  W4G                  GRF BORTORONY   \n", "12661  41520               HOME  Q4B                     OTTHON_OFD   \n", "12455  41520     HOME_GARDENING  W2G                   NOHEL_HU_OFD   \n", "12454  41520     HOME_GARDENING  R7F            GARAFARM_HM_CHM_OFD   \n", "12456  41520     HOME_GARDENING  R6L                      BROOM_OFD   \n", "12453  41520     HOME_GARDENING  R6G            SOLO_FIRE_&_BBQ_OFD   \n", "12463  41520          HOUSEHOLD  K1H                VEGYI_PAPIR_OFD   \n", "12639  41520          HOUSEHOLD  U1B                 CAYG_HAZTARTAS   \n", "12640  41520      PERSONAL CARE  U1B                 CAYG_KOZMETIKA   \n", "12667  41520                PET  M1O                ALLATELEDEL_OFD   \n", "12726  41520       PET_PRODUCTS  U1B                     CAYG_ALLAT   \n", "12481  41520       PRICE ANCHOR  U2B                     ARGARANCIA   \n", "12538  41520            PRODUCE  W5H       HUTETLEN_ZOLDSEG_DOT_COM   \n", "12539  41520            PRODUCE  W5G         HUTOTT_ZOLDSEG_DOT_COM   \n", "12797  41520         PROVISIONS  U1B                   CAYG_CSEMEGE   \n", "12801  41520         PROVISIONS  V4V                 GRILL_TERMEKEK   \n", "12508  41520          SOFTDRINK  G1B       ALKOHOLMENTES_ITALOK_OFD   \n", "12516  41520        SOFT_DRINKS  Z3I             SZALLITOI_VIZ_HUTO   \n", "12518  41520        SOFT_DRINKS  U1B                     CAYG_UDITO   \n", "12596  41520              SPORT  S1E                      SPORT_OFD   \n", "12552  41520         STATIONARY  P3F              PAPIR_IROSZER_OFD   \n", "12566  41520         STATIONERY  U1S  GM_NEM_TERVEZETT_HELY_IROSZER   \n", "12731  41520            Spirits  Z3F    SZALLITOI MODUL (ROVIDITAL)   \n", "12545  41520               TOYS  S3Y                      JATEK_OFD   \n", "12641  41520     Tesco Magazine  P2D             TESCO_MAGAZINE_OFD   \n", "12594  41520                NaN  W6A                     KASSZA_OFD   \n", "12600  41520                NaN  U5C                        EZ_+_AZ   \n", "12671  41520                NaN  U1N                            NaN   \n", "\n", "      Merchandising Group Description  No of mods             dep  \n", "12550               ENTERTAINMENT_OFD         1.0  PROMO_OFD_CAYG  \n", "12775                  EXTRA_GRNF_OFD         1.0  PROMO_OFD_CAYG  \n", "12582                      FLOWER_OFD         1.0  PROMO_OFD_CAYG  \n", "12643             FROZEN_OFD_UNILEVER         1.0  PROMO_OFD_CAYG  \n", "12645              FROZEN_OFD_ICECUBE         1.0  PROMO_OFD_CAYG  \n", "12762          FROZEN_OFD_HAAGEN-DAZS         1.0  PROMO_OFD_CAYG  \n", "12761           FROZEN_OFD_BEN&JERRYS         1.0  PROMO_OFD_CAYG  \n", "12651                   FROZEN_IN-OUT         1.0  PROMO_OFD_CAYG  \n", "12715                     Gift basket         1.0  PROMO_OFD_CAYG  \n", "12649                  GRF_WINE_TOWER         1.0  PROMO_OFD_CAYG  \n", "12661                        HOME_OFD         1.0  PROMO_OFD_CAYG  \n", "12455                    NOHEL_HU_OFD         1.0  PROMO_OFD_CAYG  \n", "12454             GARAFARM_HM_CHM_OFD         1.0  PROMO_OFD_CAYG  \n", "12456                       BROOM_OFD         1.0  PROMO_OFD_CAYG  \n", "12453             SOLO_FIRE_&_BBQ_OFD         1.0  PROMO_OFD_CAYG  \n", "12463                   HOUSEHOLD_OFD         1.0  PROMO_OFD_CAYG  \n", "12639                  CAYG_HOUSEHOLD         1.0  PROMO_OFD_CAYG  \n", "12640              CAYG_PERSONAL_CARE         1.0  PROMO_OFD_CAYG  \n", "12667                    PET_FOOD_OFD         1.0  PROMO_OFD_CAYG  \n", "12726                 CAYG_PET_ACC_HL         1.0  PROMO_OFD_CAYG  \n", "12481             PRICE_ANCHOR_OFD_HM        18.0  PROMO_OFD_CAYG  \n", "12538          AMBIENT_PRODUCE_DOTCOM         6.0  PROMO_OFD_CAYG  \n", "12539          CHILLED_PRODUCE_DOTCOM         6.0  PROMO_OFD_CAYG  \n", "12797                 CAYG_PROVISIONS         1.0  PROMO_OFD_CAYG  \n", "12801                       BBQ_PROMO         2.0  PROMO_OFD_CAYG  \n", "12508                      DRINKS_OFD         1.0  PROMO_OFD_CAYG  \n", "12516          SUPPLIER_CHILLED_WATER         2.0  PROMO_OFD_CAYG  \n", "12518                     CAYG_DRINKS         1.0  PROMO_OFD_CAYG  \n", "12596                       SPORT_OFD         1.0  PROMO_OFD_CAYG  \n", "12552                  STATIONERY_OFD         1.0  PROMO_OFD_CAYG  \n", "12566        GM_FREE_SPACE_STATIONERY         1.0  PROMO_OFD_CAYG  \n", "12731             Spirit and Liqueurs         1.0  PROMO_OFD_CAYG  \n", "12545                        TOYS_OFD         1.0  PROMO_OFD_CAYG  \n", "12641              TESCO_MAGAZINE_OFD         1.0  PROMO_OFD_CAYG  \n", "12594                    CHECKOUT_OFD        12.0  PROMO_OFD_CAYG  \n", "12600                    PICKUP_LINES         1.0  PROMO_OFD_CAYG  \n", "12671                       DOTCOM_GM        10.0  PROMO_OFD_CAYG  "]}, "execution_count": 206, "metadata": {}, "output_type": "execute_result"}], "source": ["mods_ce[(\n", "mods_ce['dep'].str.contains('PROMO')) & (mods_ce.store == 41520)].sort_values(by=\"Department (Desc1)\").iloc[30:,:]"]}, {"cell_type": "code", "execution_count": 192, "id": "f0669f6b-cf91-4201-980f-7aa189323a98", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>store</th>\n", "      <th>Department (Desc1)</th>\n", "      <th>drg</th>\n", "      <th>Merch. Group Local Description</th>\n", "      <th>Merchandising Group Description</th>\n", "      <th>No of mods</th>\n", "      <th>dep</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1257</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3U</td>\n", "      <td>NaN</td>\n", "      <td>OREO_UNIT</td>\n", "      <td>1.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1271</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3K</td>\n", "      <td>NaN</td>\n", "      <td>GIFTS</td>\n", "      <td>1.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1315</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F1C</td>\n", "      <td>NaN</td>\n", "      <td>AMBIENT_OFD</td>\n", "      <td>1.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1317</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K1I</td>\n", "      <td>NaN</td>\n", "      <td>SWIFFER_OFD</td>\n", "      <td>1.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1323</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G1B</td>\n", "      <td>NaN</td>\n", "      <td>DRINKS_OFD</td>\n", "      <td>1.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1330</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3J</td>\n", "      <td>NaN</td>\n", "      <td>JO<PERSON>O_SHOP_IN_SHOP</td>\n", "      <td>3.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1331</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F4B</td>\n", "      <td>NaN</td>\n", "      <td>SUGAR</td>\n", "      <td>3.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1336</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F2N</td>\n", "      <td>NaN</td>\n", "      <td>COOKING_SAUCES</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1349</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>M1K</td>\n", "      <td>NaN</td>\n", "      <td>PET_ACC_HL</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1351</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3E</td>\n", "      <td>NaN</td>\n", "      <td>CHOCOLATE_BOXES</td>\n", "      <td>4.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1352</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K3E</td>\n", "      <td>NaN</td>\n", "      <td>FACIAL_TISSUES</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1353</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K3G</td>\n", "      <td>NaN</td>\n", "      <td>FOILS_&amp;_WRAPS</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1354</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K3D</td>\n", "      <td>NaN</td>\n", "      <td>KITCHEN_TOWELS_&amp;_NAPKINS</td>\n", "      <td>8.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1355</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K3A</td>\n", "      <td>NaN</td>\n", "      <td>TOILET_TISSUES</td>\n", "      <td>12.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1356</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G2D</td>\n", "      <td>NaN</td>\n", "      <td>CARBONATED_DRINKS</td>\n", "      <td>8.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1357</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>M1D</td>\n", "      <td>NaN</td>\n", "      <td>SMALL_PET_FOOD</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1358</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G4D</td>\n", "      <td>NaN</td>\n", "      <td>NUTS_SEEDS</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1359</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F1F</td>\n", "      <td>NaN</td>\n", "      <td>CANNED_VEGETABLES</td>\n", "      <td>7.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1360</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F2H</td>\n", "      <td>NaN</td>\n", "      <td>CANNED_FRUIT</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1361</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K2K</td>\n", "      <td>NaN</td>\n", "      <td>TOILET_CARE</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1363</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3I</td>\n", "      <td>NaN</td>\n", "      <td>KIDS_SWEETS</td>\n", "      <td>1.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1364</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3V</td>\n", "      <td>NaN</td>\n", "      <td>BITES_MOD</td>\n", "      <td>1.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1368</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>M1C</td>\n", "      <td>NaN</td>\n", "      <td>CAT_FOOD</td>\n", "      <td>8.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1370</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>M1A</td>\n", "      <td>NaN</td>\n", "      <td>DOG_FOOD</td>\n", "      <td>12.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1371</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G2F</td>\n", "      <td>NaN</td>\n", "      <td>CHILLED_DRINKS</td>\n", "      <td>3.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1375</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G2C</td>\n", "      <td>NaN</td>\n", "      <td>COLA_DRINKS</td>\n", "      <td>13.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1376</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K2L</td>\n", "      <td>NaN</td>\n", "      <td>AIR_FRESHENERS</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1378</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3Z</td>\n", "      <td>NaN</td>\n", "      <td>CRISPS_SNACKS</td>\n", "      <td>16.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1379</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K2A</td>\n", "      <td>NaN</td>\n", "      <td>CLEANING</td>\n", "      <td>6.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1380</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K1E</td>\n", "      <td>NaN</td>\n", "      <td>DISHWASHING</td>\n", "      <td>5.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1381</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F4K</td>\n", "      <td>NaN</td>\n", "      <td>FREE_FROM</td>\n", "      <td>12.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1382</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F4E</td>\n", "      <td>NaN</td>\n", "      <td>HEALTHY_FOOD</td>\n", "      <td>6.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1384</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3A</td>\n", "      <td>NaN</td>\n", "      <td>BISCUITS</td>\n", "      <td>14.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1385</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3D</td>\n", "      <td>NaN</td>\n", "      <td>CHOCOLATE_TABLETS</td>\n", "      <td>3.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1386</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3F</td>\n", "      <td>NaN</td>\n", "      <td>COUNTLINES</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1387</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>G3G</td>\n", "      <td>NaN</td>\n", "      <td>PACKED_SWEETS_&amp;_MINTS</td>\n", "      <td>6.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1389</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>K3I</td>\n", "      <td>NaN</td>\n", "      <td>KITCHEN_ACCESSORIES_&amp;_INSECTS_KILLERS</td>\n", "      <td>2.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1395</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F3A</td>\n", "      <td>NaN</td>\n", "      <td>EATING_AIDS</td>\n", "      <td>5.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1396</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F1B</td>\n", "      <td>NaN</td>\n", "      <td>SOUPS</td>\n", "      <td>3.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1397</th>\n", "      <td>21001</td>\n", "      <td>NaN</td>\n", "      <td>F1E</td>\n", "      <td>NaN</td>\n", "      <td>INSTANT_FOOD</td>\n", "      <td>4.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      store Department (Desc1)  drg Merch. Group Local Description  \\\n", "1257  21001                NaN  G3U                            NaN   \n", "1271  21001                NaN  G3K                            NaN   \n", "1315  21001                NaN  F1C                            NaN   \n", "1317  21001                NaN  K1I                            NaN   \n", "1323  21001                NaN  G1B                            NaN   \n", "1330  21001                NaN  G3J                            NaN   \n", "1331  21001                NaN  F4B                            NaN   \n", "1336  21001                NaN  F2N                            NaN   \n", "1349  21001                NaN  M1K                            NaN   \n", "1351  21001                NaN  G3E                            NaN   \n", "1352  21001                NaN  K3E                            NaN   \n", "1353  21001                NaN  K3G                            NaN   \n", "1354  21001                NaN  K3D                            NaN   \n", "1355  21001                NaN  K3A                            NaN   \n", "1356  21001                NaN  G2D                            NaN   \n", "1357  21001                NaN  M1D                            NaN   \n", "1358  21001                NaN  G4D                            NaN   \n", "1359  21001                NaN  F1F                            NaN   \n", "1360  21001                NaN  F2H                            NaN   \n", "1361  21001                NaN  K2K                            NaN   \n", "1363  21001                NaN  G3I                            NaN   \n", "1364  21001                NaN  G3V                            NaN   \n", "1368  21001                NaN  M1C                            NaN   \n", "1370  21001                NaN  M1A                            NaN   \n", "1371  21001                NaN  G2F                            NaN   \n", "1375  21001                NaN  G2C                            NaN   \n", "1376  21001                NaN  K2L                            NaN   \n", "1378  21001                NaN  G3Z                            NaN   \n", "1379  21001                NaN  K2A                            NaN   \n", "1380  21001                NaN  K1E                            NaN   \n", "1381  21001                NaN  F4K                            NaN   \n", "1382  21001                NaN  F4E                            NaN   \n", "1384  21001                NaN  G3A                            NaN   \n", "1385  21001                NaN  G3D                            NaN   \n", "1386  21001                NaN  G3F                            NaN   \n", "1387  21001                NaN  G3G                            NaN   \n", "1389  21001                NaN  K3I                            NaN   \n", "1395  21001                NaN  F3A                            NaN   \n", "1396  21001                NaN  F1B                            NaN   \n", "1397  21001                NaN  F1E                            NaN   \n", "\n", "            Merchandising Group Description  No of mods  dep  \n", "1257                              OREO_UNIT         1.0  DRY  \n", "1271                                  GIFTS         1.0  DRY  \n", "1315                            AMBIENT_OFD         1.0  DRY  \n", "1317                            SWIFFER_OFD         1.0  DRY  \n", "1323                             DRINKS_OFD         1.0  DRY  \n", "1330                      JOJO_SHOP_IN_SHOP         3.0  DRY  \n", "1331                                  SUGAR         3.0  DRY  \n", "1336                         COOKING_SAUCES         2.0  DRY  \n", "1349                             PET_ACC_HL         2.0  DRY  \n", "1351                        CHOCOLATE_BOXES         4.0  DRY  \n", "1352                         FACIAL_TISSUES         2.0  DRY  \n", "1353                          FOILS_&_WRAPS         2.0  DRY  \n", "1354               KITCHEN_TOWELS_&_NAPKINS         8.0  DRY  \n", "1355                         TOILET_TISSUES        12.0  DRY  \n", "1356                      CARBONATED_DRINKS         8.0  DRY  \n", "1357                         SMALL_PET_FOOD         2.0  DRY  \n", "1358                             NUTS_SEEDS         2.0  DRY  \n", "1359                      CANNED_VEGETABLES         7.0  DRY  \n", "1360                           CANNED_FRUIT         2.0  DRY  \n", "1361                            TOILET_CARE         2.0  DRY  \n", "1363                            KIDS_SWEETS         1.0  DRY  \n", "1364                              BITES_MOD         1.0  DRY  \n", "1368                               CAT_FOOD         8.0  DRY  \n", "1370                               DOG_FOOD        12.0  DRY  \n", "1371                         CHILLED_DRINKS         3.0  DRY  \n", "1375                            COLA_DRINKS        13.0  DRY  \n", "1376                         AIR_FRESHENERS         2.0  DRY  \n", "1378                          CRISPS_SNACKS        16.0  DRY  \n", "1379                               CLEANING         6.0  DRY  \n", "1380                            DISHWASHING         5.0  DRY  \n", "1381                              FREE_FROM        12.0  DRY  \n", "1382                           HEALTHY_FOOD         6.0  DRY  \n", "1384                               BISCUITS        14.0  DRY  \n", "1385                      CHOCOLATE_TABLETS         3.0  DRY  \n", "1386                             COUNTLINES         2.0  DRY  \n", "1387                  PACKED_SWEETS_&_MINTS         6.0  DRY  \n", "1389  KITCHEN_ACCESSORIES_&_INSECTS_KILLERS         2.0  DRY  \n", "1395                            EATING_AIDS         5.0  DRY  \n", "1396                                  SOUPS         3.0  DRY  \n", "1397                           INSTANT_FOOD         4.0  DRY  "]}, "execution_count": 192, "metadata": {}, "output_type": "execute_result"}], "source": ["mods_ce[(mods_ce.store == 21001) & (mods_ce.dep == 'DRY')].iloc[:40,:]"]}, {"cell_type": "code", "execution_count": 180, "id": "3330113e-e6cb-4fee-8d65-5d3459d5bb3e", "metadata": {}, "outputs": [{"data": {"text/plain": ["dep\n", "BAK                31.0\n", "BWS                56.0\n", "DAI                51.0\n", "DRY               389.0\n", "FRZ                29.0\n", "HDL               244.0\n", "HEA                94.0\n", "PPD                20.0\n", "PRO                 7.0\n", "PROMO_OFD_CAYG    150.0\n", "SFM                 8.0\n", "Name: No of mods, dtype: float64"]}, "execution_count": 180, "metadata": {}, "output_type": "execute_result"}], "source": ["mods_ce[(mods_ce.store == 41520)].groupby(\"dep\")['No of mods'].sum()"]}, {"cell_type": "code", "execution_count": 198, "id": "4aec4c3d-288a-46c9-b8ee-7bbb2d3be944", "metadata": {}, "outputs": [{"data": {"text/plain": ["dep\n", "BAK                30.0\n", "BWS                55.0\n", "DAI                50.0\n", "DRY               361.0\n", "FRZ                26.0\n", "HDL               232.0\n", "HEA                94.0\n", "PPD                20.0\n", "PRO                33.0\n", "PROMO_OFD_CAYG    170.0\n", "SFM                 8.0\n", "Name: No of mods, dtype: float64"]}, "execution_count": 198, "metadata": {}, "output_type": "execute_result"}], "source": ["mods_ce[(mods_ce.store == 41520)].groupby(\"dep\")['No of mods'].sum()"]}, {"cell_type": "code", "execution_count": 172, "id": "270a8ece-a3bc-4bf2-a463-10b2b9301d6e", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 172, "metadata": {}, "output_type": "execute_result"}], "source": ["mods_ce['dep'].isnull().sum()"]}, {"cell_type": "code", "execution_count": 158, "id": "6d440224-e55a-4510-81d3-62fa2119f390", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'A': 'BAK', 'B': 'SFM', 'C': 'PRO', 'D': 'DAI', 'E': 'PPD'}"]}, "execution_count": 158, "metadata": {}, "output_type": "execute_result"}], "source": ["drg_dep_mapping"]}, {"cell_type": "code", "execution_count": 168, "id": "8b0a03af-487e-4421-9f59-95082728a3c6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stores</th>\n", "      <th>Department (Desc1)</th>\n", "      <th>drg</th>\n", "      <th>Merch. Group Local Description</th>\n", "      <th>Merchandising Group Description</th>\n", "      <th>No of mods</th>\n", "      <th>dep</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>41510</td>\n", "      <td>AMBIENT_FOOD</td>\n", "      <td>F3A</td>\n", "      <td>ONTETEK</td>\n", "      <td>EATING_AIDS</td>\n", "      <td>4.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>41510</td>\n", "      <td>AMBIENT_FOOD</td>\n", "      <td>U1B</td>\n", "      <td>CAYG_ALAPVETO</td>\n", "      <td>CAYG_AMBIENT_FOOD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD_CAYG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>41510</td>\n", "      <td>AMBIENT_FOOD</td>\n", "      <td>F1O</td>\n", "      <td>HALKONZERV_ES_HUSKONZERV</td>\n", "      <td>CANNED_FISH_MEAT_&amp;_PATES</td>\n", "      <td>3.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>41510</td>\n", "      <td>AMBIENT_FOOD</td>\n", "      <td>F1F</td>\n", "      <td>ZOLDSEG_KONZERV</td>\n", "      <td>CANNED_VEGETABLES</td>\n", "      <td>5.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>41510</td>\n", "      <td>AMBIENT_FOOD</td>\n", "      <td>G4H</td>\n", "      <td>MUZLI &amp; MUZLI SZELETEK</td>\n", "      <td>BARS_&amp;_MUSLI</td>\n", "      <td>4.0</td>\n", "      <td>DRY</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   stores Department (Desc1)  drg Merch. Group Local Description  \\\n", "0   41510       AMBIENT_FOOD  F3A                        ONTETEK   \n", "1   41510       AMBIENT_FOOD  U1B                  CAYG_ALAPVETO   \n", "2   41510       AMBIENT_FOOD  F1O       HALKONZERV_ES_HUSKONZERV   \n", "3   41510       AMBIENT_FOOD  F1F                ZOLDSEG_KONZERV   \n", "4   41510       AMBIENT_FOOD  G4H         MUZLI & MUZLI SZELETEK   \n", "\n", "  Merchandising Group Description  No of mods             dep  \n", "0                     EATING_AIDS         4.0             DRY  \n", "1               CAYG_AMBIENT_FOOD         1.0  PROMO_OFD_CAYG  \n", "2        CANNED_FISH_MEAT_&_PATES         3.0             DRY  \n", "3               CANNED_VEGETABLES         5.0             DRY  \n", "4                    BARS_&_MUSLI         4.0             DRY  "]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["mods_ce.head()"]}, {"cell_type": "code", "execution_count": 104, "id": "31bbc81f-b9a4-4b05-ba2f-16458b837b9e", "metadata": {}, "outputs": [], "source": ["a = mods_ce.merge(drg, on=\"drg\", how='left')"]}, {"cell_type": "code", "execution_count": 146, "id": "b832f6ed-17a2-45fe-93ff-f32dfe3ee455", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stores</th>\n", "      <th>Department (Desc1)</th>\n", "      <th>drg</th>\n", "      <th>Merch. Group Local Description</th>\n", "      <th>Merchandising Group Description</th>\n", "      <th>No of mods</th>\n", "      <th>prod_dep</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>63988</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z3T</td>\n", "      <td>NaN</td>\n", "      <td>FF_PROMO_END_DAIRY</td>\n", "      <td>2.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63989</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z3U</td>\n", "      <td>NaN</td>\n", "      <td>FF_PROMO_END_PROVISION</td>\n", "      <td>2.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63990</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>V2P</td>\n", "      <td>NaN</td>\n", "      <td>ONTOP_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63992</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z1L</td>\n", "      <td>NaN</td>\n", "      <td>GRF_PROMO_END_SOFTDRINKS</td>\n", "      <td>3.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63993</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z2W</td>\n", "      <td>NaN</td>\n", "      <td>GRF_PROMO_END_AMBIENT</td>\n", "      <td>2.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63994</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z1Q</td>\n", "      <td>NaN</td>\n", "      <td>GRF_PROMO_END_IMPULSE</td>\n", "      <td>3.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63995</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z1T</td>\n", "      <td>NaN</td>\n", "      <td>GRNF_PROMO_END_HOUSEHOLD</td>\n", "      <td>3.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63996</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z2P</td>\n", "      <td>NaN</td>\n", "      <td>GRNF_PROMO_END_PET</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63998</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z3O</td>\n", "      <td>NaN</td>\n", "      <td>FF_PROMO_END_FROZEN</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63999</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z1M</td>\n", "      <td>NaN</td>\n", "      <td>GRF_PROMO_END_BWS</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64002</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>A1J</td>\n", "      <td>NaN</td>\n", "      <td>PRESERVED_&amp;_SPREADS_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64003</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z3S</td>\n", "      <td>NaN</td>\n", "      <td>FF_PROMO_END_EVENT</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64012</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>V2D</td>\n", "      <td>NaN</td>\n", "      <td>AMBIENT_PROMO_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64013</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>V2E</td>\n", "      <td>NaN</td>\n", "      <td>BWS_PROMO_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64014</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>V2F</td>\n", "      <td>NaN</td>\n", "      <td>IMPULS_PROMO_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64015</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>V2G</td>\n", "      <td>NaN</td>\n", "      <td>SOFTDRINKS_PROMO_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64016</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>V2J</td>\n", "      <td>NaN</td>\n", "      <td>HOUSEHOLD_PROMO_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64017</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>V2L</td>\n", "      <td>NaN</td>\n", "      <td>PET_PROMO_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64019</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>G3P</td>\n", "      <td>NaN</td>\n", "      <td>CONFECTIONARY_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64021</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z1E</td>\n", "      <td>NaN</td>\n", "      <td>GRNF_PROMO_HOUSEHOLD_FLEXI</td>\n", "      <td>4.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64022</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Q1S</td>\n", "      <td>NaN</td>\n", "      <td>TESCO_MOBILE_OFD</td>\n", "      <td>1.0</td>\n", "      <td>HDL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64023</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>I1N</td>\n", "      <td>NaN</td>\n", "      <td>BEER_WINE_SPIRITS_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64024</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>D1W</td>\n", "      <td>NaN</td>\n", "      <td>DAIRY_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64073</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>A1I</td>\n", "      <td>NaN</td>\n", "      <td>BAKERY_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64074</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>C2Z</td>\n", "      <td>NaN</td>\n", "      <td>PRODUCE_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64084</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>G1B</td>\n", "      <td>NaN</td>\n", "      <td>DRINKS_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64094</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>D1X</td>\n", "      <td>NaN</td>\n", "      <td>DAIRY_PROMO_OFD</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64125</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>F1C</td>\n", "      <td>NaN</td>\n", "      <td>AMBIENT_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64126</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>K1H</td>\n", "      <td>NaN</td>\n", "      <td>HOUSEHOLD_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64127</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>E1C</td>\n", "      <td>NaN</td>\n", "      <td>MEAT_PROVISIONS_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73503</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>U3Z</td>\n", "      <td>NaN</td>\n", "      <td>GM_PROMO_END</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73510</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z2K</td>\n", "      <td>NaN</td>\n", "      <td>GRNF_PROMO_END_BABY</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73513</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>R4C</td>\n", "      <td>NaN</td>\n", "      <td>CAR_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73515</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Q4B</td>\n", "      <td>NaN</td>\n", "      <td>HOME_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73516</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z1Z</td>\n", "      <td>NaN</td>\n", "      <td>GRNF_PROMO_BABY_FLEXI</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73517</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z1V</td>\n", "      <td>NaN</td>\n", "      <td>GRNF_PROMO_END_H&amp;B</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73518</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>V2H</td>\n", "      <td>NaN</td>\n", "      <td>BABY_PROMO_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73519</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>V2I</td>\n", "      <td>NaN</td>\n", "      <td>H&amp;B_PROMO_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73520</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>Z1Y</td>\n", "      <td>NaN</td>\n", "      <td>GRNF_PROMO_H&amp;B_FLEXI</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73521</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>L1J</td>\n", "      <td>NaN</td>\n", "      <td>H&amp;B_FLEXI_OFD</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73522</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>R4G</td>\n", "      <td>NaN</td>\n", "      <td>DIY_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73592</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>R6G</td>\n", "      <td>NaN</td>\n", "      <td>SOLO_FIRE_&amp;_BBQ_OFD</td>\n", "      <td>1.0</td>\n", "      <td>HDL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73593</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>R6L</td>\n", "      <td>NaN</td>\n", "      <td>BROOM_OFD</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73598</th>\n", "      <td>11032</td>\n", "      <td>NaN</td>\n", "      <td>O1I</td>\n", "      <td>NaN</td>\n", "      <td>BABY_OFD</td>\n", "      <td>1.0</td>\n", "      <td>PROMO_OFD</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       stores Department (Desc1)  drg Merch. Group Local Description  \\\n", "63988   11032                NaN  Z3T                            NaN   \n", "63989   11032                NaN  Z3U                            NaN   \n", "63990   11032                NaN  V2P                            NaN   \n", "63992   11032                NaN  Z1L                            NaN   \n", "63993   11032                NaN  Z2W                            NaN   \n", "63994   11032                NaN  Z1Q                            NaN   \n", "63995   11032                NaN  Z1T                            NaN   \n", "63996   11032                NaN  Z2P                            NaN   \n", "63998   11032                NaN  Z3O                            NaN   \n", "63999   11032                NaN  Z1M                            NaN   \n", "64002   11032                NaN  A1J                            NaN   \n", "64003   11032                NaN  Z3S                            NaN   \n", "64012   11032                NaN  V2D                            NaN   \n", "64013   11032                NaN  V2E                            NaN   \n", "64014   11032                NaN  V2F                            NaN   \n", "64015   11032                NaN  V2G                            NaN   \n", "64016   11032                NaN  V2J                            NaN   \n", "64017   11032                NaN  V2L                            NaN   \n", "64019   11032                NaN  G3P                            NaN   \n", "64021   11032                NaN  Z1E                            NaN   \n", "64022   11032                NaN  Q1S                            NaN   \n", "64023   11032                NaN  I1N                            NaN   \n", "64024   11032                NaN  D1W                            NaN   \n", "64073   11032                NaN  A1I                            NaN   \n", "64074   11032                NaN  C2Z                            NaN   \n", "64084   11032                NaN  G1B                            NaN   \n", "64094   11032                NaN  D1X                            NaN   \n", "64125   11032                NaN  F1C                            NaN   \n", "64126   11032                NaN  K1H                            NaN   \n", "64127   11032                NaN  E1C                            NaN   \n", "73503   11032                NaN  U3Z                            NaN   \n", "73510   11032                NaN  Z2K                            NaN   \n", "73513   11032                NaN  R4C                            NaN   \n", "73515   11032                NaN  Q4B                            NaN   \n", "73516   11032                NaN  Z1Z                            NaN   \n", "73517   11032                NaN  Z1V                            NaN   \n", "73518   11032                NaN  V2H                            NaN   \n", "73519   11032                NaN  V2I                            NaN   \n", "73520   11032                NaN  Z1Y                            NaN   \n", "73521   11032                NaN  L1J                            NaN   \n", "73522   11032                NaN  R4G                            NaN   \n", "73592   11032                NaN  R6G                            NaN   \n", "73593   11032                NaN  R6L                            NaN   \n", "73598   11032                NaN  O1I                            NaN   \n", "\n", "      Merchandising Group Description  No of mods   prod_dep  \n", "63988              FF_PROMO_END_DAIRY         2.0  PROMO_OFD  \n", "63989          FF_PROMO_END_PROVISION         2.0  PROMO_OFD  \n", "63990                       ONTOP_OFD         1.0  PROMO_OFD  \n", "63992        GRF_PROMO_END_SOFTDRINKS         3.0  PROMO_OFD  \n", "63993           GRF_PROMO_END_AMBIENT         2.0  PROMO_OFD  \n", "63994           GRF_PROMO_END_IMPULSE         3.0  PROMO_OFD  \n", "63995        GRNF_PROMO_END_HOUSEHOLD         3.0  PROMO_OFD  \n", "63996              GRNF_PROMO_END_PET         1.0  PROMO_OFD  \n", "63998             FF_PROMO_END_FROZEN         1.0  PROMO_OFD  \n", "63999               GRF_PROMO_END_BWS         1.0  PROMO_OFD  \n", "64002         PRESERVED_&_SPREADS_OFD         1.0  PROMO_OFD  \n", "64003              FF_PROMO_END_EVENT         1.0  PROMO_OFD  \n", "64012               AMBIENT_PROMO_OFD         1.0  PROMO_OFD  \n", "64013                   BWS_PROMO_OFD         1.0  PROMO_OFD  \n", "64014                IMPULS_PROMO_OFD         1.0  PROMO_OFD  \n", "64015            SOFTDRINKS_PROMO_OFD         1.0  PROMO_OFD  \n", "64016             HOUSEHOLD_PROMO_OFD         1.0  PROMO_OFD  \n", "64017                   PET_PROMO_OFD         1.0  PROMO_OFD  \n", "64019               CONFECTIONARY_OFD         1.0  PROMO_OFD  \n", "64021      GRNF_PROMO_HOUSEHOLD_FLEXI         4.0  PROMO_OFD  \n", "64022                TESCO_MOBILE_OFD         1.0        HDL  \n", "64023           BEER_WINE_SPIRITS_OFD         1.0  PROMO_OFD  \n", "64024                       DAIRY_OFD         1.0  PROMO_OFD  \n", "64073                      BAKERY_OFD         1.0  PROMO_OFD  \n", "64074                     PRODUCE_OFD         1.0  PROMO_OFD  \n", "64084                      DRINKS_OFD         1.0  PROMO_OFD  \n", "64094                 DAIRY_PROMO_OFD         1.0        NaN  \n", "64125                     AMBIENT_OFD         1.0  PROMO_OFD  \n", "64126                   HOUSEHOLD_OFD         1.0  PROMO_OFD  \n", "64127             MEAT_PROVISIONS_OFD         1.0  PROMO_OFD  \n", "73503                    GM_PROMO_END         1.0  PROMO_OFD  \n", "73510             GRNF_PROMO_END_BABY         1.0  PROMO_OFD  \n", "73513                         CAR_OFD         1.0  PROMO_OFD  \n", "73515                        HOME_OFD         1.0  PROMO_OFD  \n", "73516           GRNF_PROMO_BABY_FLEXI         1.0        NaN  \n", "73517              GRNF_PROMO_END_H&B         1.0  PROMO_OFD  \n", "73518                  BABY_PROMO_OFD         1.0  PROMO_OFD  \n", "73519                   H&B_PROMO_OFD         1.0  PROMO_OFD  \n", "73520            GRNF_PROMO_H&B_FLEXI         2.0        NaN  \n", "73521                   H&B_FLEXI_OFD         1.0        NaN  \n", "73522                         DIY_OFD         1.0  PROMO_OFD  \n", "73592             SOLO_FIRE_&_BBQ_OFD         1.0        HDL  \n", "73593                       BROOM_OFD         1.0        NaN  \n", "73598                        BABY_OFD         1.0  PROMO_OFD  "]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 34, "id": "df2cc180-ab7b-44e8-8805-9ded5f8d541d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Store Number</th>\n", "      <th>Name</th>\n", "      <th>Format</th>\n", "      <th>Cluster name</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Date Pending</th>\n", "      <th>Date Live</th>\n", "      <th>DBKey.1</th>\n", "      <th>Status</th>\n", "      <th>Date pending</th>\n", "      <th>Date Live.1</th>\n", "      <th>Department (Desc1)</th>\n", "      <th>Merchandising Group</th>\n", "      <th>Merch. Group Local Description</th>\n", "      <th>Merchandising Group Description</th>\n", "      <th>Cluster Range Class</th>\n", "      <th>Name.1</th>\n", "      <th>No of mods</th>\n", "      <th>Number of Sections</th>\n", "      <th>Number of Products allocated</th>\n", "      <th>Width</th>\n", "      <th>Height</th>\n", "      <th>Depth</th>\n", "      <th>Equipment Type</th>\n", "      <th>Name.2</th>\n", "      <th>Dummy planogram</th>\n", "      <th>Actual Sales (Curr)</th>\n", "      <th>Actual Sales (units)</th>\n", "      <th>Historic Sales (Curr)</th>\n", "      <th>Historic Sales (units)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>46213</th>\n", "      <td>4081</td>\n", "      <td>DEBREC PIAC EXP 4081 1 SM</td>\n", "      <td>SM</td>\n", "      <td>All Express</td>\n", "      <td>66974</td>\n", "      <td>24/11/2023</td>\n", "      <td>27/11/2023</td>\n", "      <td>440366</td>\n", "      <td>Live</td>\n", "      <td>2023-06-03 00:00:00</td>\n", "      <td>20/03/2023</td>\n", "      <td>STATIONERY</td>\n", "      <td>P9I</td>\n", "      <td>BEVASARLOTASKA_OFD_1K</td>\n", "      <td>SHOPPING_BAGS_OFD_1K</td>\n", "      <td>C</td>\n", "      <td>SHOPPING_BAGS_OFD_1K All Express C 1 STD WK_04</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>50.0</td>\n", "      <td>180.0</td>\n", "      <td>29.0</td>\n", "      <td>STD</td>\n", "      <td>Mod 1.8x0.47x1</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>118767.32</td>\n", "      <td>508.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11522</th>\n", "      <td>1980</td>\n", "      <td>HM PECS 3 1980 1 1</td>\n", "      <td>1.0</td>\n", "      <td>All Hypers</td>\n", "      <td>67063</td>\n", "      <td>18/12/2023</td>\n", "      <td>2024-08-01 00:00:00</td>\n", "      <td>452133</td>\n", "      <td>Live</td>\n", "      <td>2023-04-12 00:00:00</td>\n", "      <td>25/12/2023</td>\n", "      <td>HOME_GARDENING</td>\n", "      <td>R6L</td>\n", "      <td>BROOM_OFD</td>\n", "      <td>BROOM_OFD</td>\n", "      <td>C</td>\n", "      <td>BROOM_OFD All Hypers C 1 OFD</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>60.0</td>\n", "      <td>200.0</td>\n", "      <td>40.0</td>\n", "      <td>OFD</td>\n", "      <td>OFD</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5971.91</td>\n", "      <td>1.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10967</th>\n", "      <td>1051</td>\n", "      <td>HM TISZAFU 1051 1 8</td>\n", "      <td>7.0</td>\n", "      <td>All Hypers</td>\n", "      <td>67059</td>\n", "      <td>18/12/2023</td>\n", "      <td>2024-08-01 00:00:00</td>\n", "      <td>452105</td>\n", "      <td>Live</td>\n", "      <td>30/10/2023</td>\n", "      <td>13/11/2023</td>\n", "      <td>HOUSEHOLD</td>\n", "      <td>K1J</td>\n", "      <td>ECO_MOSOSZER</td>\n", "      <td>ECO_DETERGENTS</td>\n", "      <td>A</td>\n", "      <td>ECO_DETERGENTS All Hypers A 1 ACC</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>35</td>\n", "      <td>133.0</td>\n", "      <td>200.0</td>\n", "      <td>57.0</td>\n", "      <td>ACC</td>\n", "      <td>Mod 2.0x0.57x1.33</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>27174.21</td>\n", "      <td>14.84</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Store Number                       Name Format Cluster name  DBKey  \\\n", "46213          4081  DEBREC PIAC EXP 4081 1 SM     SM  All Express  66974   \n", "11522          1980         HM PECS 3 1980 1 1    1.0   All Hypers  67063   \n", "10967          1051        HM TISZAFU 1051 1 8    7.0   All Hypers  67059   \n", "\n", "      Date Pending            Date Live  DBKey.1 Status         Date pending  \\\n", "46213   24/11/2023           27/11/2023   440366   Live  2023-06-03 00:00:00   \n", "11522   18/12/2023  2024-08-01 00:00:00   452133   Live  2023-04-12 00:00:00   \n", "10967   18/12/2023  2024-08-01 00:00:00   452105   Live           30/10/2023   \n", "\n", "      Date Live.1 Department (Desc1) Merchandising Group  \\\n", "46213  20/03/2023         STATIONERY                 P9I   \n", "11522  25/12/2023     HOME_GARDENING                 R6L   \n", "10967  13/11/2023          HOUSEHOLD                 K1J   \n", "\n", "      Merch. Group Local Description Merchandising Group Description  \\\n", "46213          BEVASARLOTASKA_OFD_1K            SHOPPING_BAGS_OFD_1K   \n", "11522                      BROOM_OFD                       BROOM_OFD   \n", "10967                   ECO_MOSOSZER                  ECO_DETERGENTS   \n", "\n", "      Cluster Range Class                                          Name.1  \\\n", "46213                   C  SHOPPING_BAGS_OFD_1K All Express C 1 STD WK_04   \n", "11522                   C                   BROOM_OFD All Hypers C 1 OFD    \n", "10967                   A              ECO_DETERGENTS All Hypers A 1 ACC    \n", "\n", "       No of mods  Number of Sections  Number of Products allocated  Width  \\\n", "46213           1                   1                             7   50.0   \n", "11522           1                   1                             2   60.0   \n", "10967           1                   1                            35  133.0   \n", "\n", "       Height  Depth Equipment Type             Name.2  Dummy planogram  \\\n", "46213   180.0   29.0            STD     Mod 1.8x0.47x1                0   \n", "11522   200.0   40.0            OFD                OFD                0   \n", "10967   200.0   57.0            ACC  Mod 2.0x0.57x1.33                0   \n", "\n", "       Actual Sales (Curr)  Actual Sales (units)  Historic Sales (Curr)  \\\n", "46213                  NaN                   NaN              118767.32   \n", "11522                  NaN                   NaN                5971.91   \n", "10967                  NaN                   NaN               27174.21   \n", "\n", "       Historic Sales (units)  \n", "46213                  508.13  \n", "11522                    1.33  \n", "10967                   14.84  "]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.set_option(\"display.max_columns\", None)\n", "combined_data.columns"]}, {"cell_type": "code", "execution_count": null, "id": "7c68e29f-3c68-4950-9808-8100b2360c0f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "201209d8-59e2-4086-9c04-de68c33e8a41", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.options.display.float_format = \"{:,.0f}\".format\n", "\n", "\n", "a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\Repl_Dataset_23Q1\").query(\"dep.isin(['DRY', 'BWS', 'HEA'])\")\n", "\n", "b = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\Repl_Dataset_2024\").query(\"dep.isin(['DRY', 'BWS', 'HEA'])\")"]}, {"cell_type": "code", "execution_count": null, "id": "394fe819-01de-4849-a02e-a739bdc2fbc1", "metadata": {}, "outputs": [], "source": ["a.groupby('country')['tpnb'].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "f88c7740-e2d1-4faf-8f4c-cc37d28f21f4", "metadata": {}, "outputs": [], "source": ["b.groupby('country')['tpnb'].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "782e3ab2-532d-44e3-8b02-3befaeb7a3d2", "metadata": {}, "outputs": [], "source": ["a = pd.read_csv(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\JDA_SRD_Tables\\10-01-2024\\CE_JDA_SRD.csv.gzip\", compression=\"gzip\")"]}, {"cell_type": "code", "execution_count": null, "id": "80a4470b-d773-4f5f-bf30-00e462183f36", "metadata": {}, "outputs": [], "source": ["a.columns"]}, {"cell_type": "code", "execution_count": null, "id": "b8acd7e2-6908-45ec-968f-679d2f19aebc", "metadata": {}, "outputs": [], "source": ["a[a.Product_id == 220233748].head()"]}, {"cell_type": "code", "execution_count": null, "id": "208f6ecf-bff0-4f0d-8f4d-26c113e4250b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1e7d7c2d-2b12-4d99-81fc-ce76bffbff37", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "pd.set_option(\"display.max_columns\", None)\n", "pd.options.display.float_format = \"{:,.0f}\".format\n", "\n", "a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\before_volume_insight\").query(\"Dep != 'SFB'\")\n", "\n", "b = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\model_outputs\\Q1_verz1__\\INSIGHT_Q1_verz1__.parquet.gz\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "e02c9c04-5d27-431a-aed5-de7120552201", "metadata": {}, "outputs": [], "source": ["a[(a.Division == 'Grocery')&(a.Store == 11001)]['hours'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "18854ddd-06ab-49f1-875f-c49f96d91400", "metadata": {}, "outputs": [], "source": ["b[(b.Division == 'Grocery')&(b.Store== 11001)]['hours'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "4f481cd5-05cd-463d-8d9b-db3200289d8c", "metadata": {}, "outputs": [], "source": ["c = a.merge(b[['Country', 'Store', 'Division', 'Activity_Group', 'Suboperation', 'Driver_1', 'Driver_2', 'Driver_3', 'Driver_4','Driver_1_value', 'Driver_2_value', 'Driver_3_value', 'Driver_4_value', 'hours','Yearly GBP']], on=['Country', 'Store', 'Division', 'Activity_Group', 'Suboperation', 'Driver_1', 'Driver_2', 'Driver_3', 'Driver_4'], how='left', suffixes=('_old', '_new'))\n", "\n", "c['hours_diff'] = c['hours_new']-c['hours_old']"]}, {"cell_type": "code", "execution_count": null, "id": "92afbc5b-bc76-4236-a1fe-d0a130169271", "metadata": {}, "outputs": [], "source": ["c[(c.Store == 11001)].groupby('Activity_Group', observed=True, as_index=False)['hours_diff'].sum().sort_values(by=\"hours_diff\", ascending=False)['hours_diff'].sum()"]}, {"cell_type": "code", "execution_count": null, "id": "863d2b78-1939-4b11-8776-d498a7176746", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}