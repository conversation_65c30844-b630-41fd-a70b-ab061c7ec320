# -*- coding: utf-8 -*-
"""
Created on Tue Feb 11 12:41:40 2025

@author: phrubos
"""

import pandas as pd
import numpy as np







def analyze_case_capacity_pandas_fast(df):
    # Reduced bins by one to match labels exactly
    bins = [-np.inf, 0, 1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50]
    labels = ['0', '1-5', '5-10', '10-15', '15-20', '20-25', '25-30',
             '30-35', '35-40', '40-45', '45-50', '50+']
    
    # Create categories using pd.cut
    df['case_cap_groups'] = pd.cut(
        df['case_capacity'],
        bins=bins,
        labels=labels,
        ordered=True
    )
    
    # Handle values above 50 separately
    df.loc[df['case_capacity'] > 50, 'case_cap_groups'] = '50+'
    
    # Compute groupby and ratios
    result = (df
        .groupby(['country', 'dep', 'case_cap_groups'], observed=True)
        .size()
        .reset_index(name='count')
    )
    
    result['total'] = result.groupby(['country', 'dep'], observed=True)['count'].transform('sum')
    result['ratio'] = (result['count'] / result['total']).round(2)
    result['case_cap_groups'] = result['case_cap_groups'].astype("string")
    
    # Pivot the result
    pivoted_result = (result
        .pivot_table(
            index=['country', 'dep'],
            columns='case_cap_groups',
            values='ratio',
            fill_value=0
        )
        .round(2)
        .reset_index()
    )
    
    # Ensure all categories are present and in correct order
    desired_columns = ['country', 'dep'] + labels
    missing_cols = [col for col in desired_columns if col not in pivoted_result.columns]
    for col in missing_cols:
        pivoted_result[col] = 0
    
    # Reorder columns
    pivoted_result = pivoted_result[desired_columns]
    
    return pivoted_result

# Usage:
result = analyze_case_capacity_pandas_fast(a)