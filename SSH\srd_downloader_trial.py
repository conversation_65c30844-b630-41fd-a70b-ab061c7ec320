import paramiko
import pandas as pd
import io
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HDFSDataExtractor:
    def __init__(self, hostname: str, username: str, password: str):
        self.hostname = hostname
        self.username = username
        self.password = password
        self.client = None
        
    def connect(self):
        """Connect to server"""
        try:
            self.client = paramiko.SSHClient()
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.client.connect(hostname=self.hostname, username=self.username, password=self.password)
            logger.info(f"Connected to {self.hostname}")
            return True
        except Exception as e:
            logger.error(f"Connection failed: {str(e)}")
            return False
    
    def run_command(self, command: str, timeout: int = 300):
        """Run command and return output"""
        try:
            stdin, stdout, stderr = self.client.exec_command(command, timeout=timeout)
            exit_status = stdout.channel.recv_exit_status()
            output = stdout.read().decode('utf-8', errors='ignore').strip()
            error = stderr.read().decode('utf-8', errors='ignore').strip()
            return output, error, exit_status
        except Exception as e:
            logger.error(f"Command execution failed: {str(e)}")
            return None, str(e), 1
    
    def find_table_location(self, table_name: str):
        """Find where the table data is stored in HDFS"""
        logger.info(f"Looking for table location: {table_name}")
        
        # Common Hive warehouse locations
        possible_paths = [
            f"/warehouse/tablespace/managed/hive/{table_name.replace('.', '.db/')}",
            f"/apps/hive/warehouse/{table_name.replace('.', '.db/')}",
            f"/user/hive/warehouse/{table_name.replace('.', '.db/')}",
            f"/tmp/hive-scratch/{table_name.replace('.', '.db/')}",
            f"/hdp/apps/*/hive/warehouse/{table_name.replace('.', '.db/')}",
        ]
        
        # Try to find the table using hdfs commands
        for path in possible_paths:
            logger.info(f"Checking path: {path}")
            output, error, status = self.run_command(f"hdfs dfs -ls {path}", timeout=60)
            if status == 0 and output:
                logger.info(f"Found table at: {path}")
                logger.info(f"Contents: {output}")
                return path
            else:
                logger.debug(f"Path not found: {path}")
        
        # Try alternative discovery methods
        logger.info("Trying alternative discovery methods...")
        
        # Method 1: Search for table in common locations
        search_cmd = f"hdfs dfs -find / -name '*{table_name.split('.')[-1]}*' 2>/dev/null | head -10"
        output, error, status = self.run_command(search_cmd, timeout=120)
        if status == 0 and output:
            logger.info(f"Search results: {output}")
            lines = output.strip().split('\n')
            for line in lines:
                if line.strip() and 'warehouse' in line.lower():
                    return line.strip()
        
        # Method 2: Try Kyuubi connection
        logger.info("Trying Kyuubi Spark Thrift server...")
        kyuubi_cmd = f"/opt/spark_thrift_kyuubi/bin/beeline -u '****************************' -e 'DESCRIBE FORMATTED {table_name};'"
        output, error, status = self.run_command(kyuubi_cmd, timeout=60)
        if status == 0 and 'Location:' in output:
            for line in output.split('\n'):
                if 'Location:' in line:
                    location = line.split('Location:')[-1].strip()
                    if location.startswith('hdfs://'):
                        return location.replace('hdfs://nameservice1', '')  # Remove nameservice prefix
        
        logger.error("Could not find table location")
        return None
    
    def extract_hdfs_data(self, hdfs_path: str, sample_only: bool = False):
        """Extract data directly from HDFS files"""
        logger.info(f"Extracting data from HDFS path: {hdfs_path}")
        
        # First, list files in the directory
        output, error, status = self.run_command(f"hdfs dfs -ls {hdfs_path}", timeout=60)
        if status != 0:
            logger.error(f"Cannot access HDFS path: {error}")
            return None
        
        # Find data files (typically .txt, .csv, or part-* files)
        data_files = []
        for line in output.split('\n'):
            if line.strip() and not line.startswith('Found'):
                parts = line.split()
                if len(parts) >= 8:
                    file_path = parts[-1]
                    file_name = file_path.split('/')[-1]
                    # Look for data files
                    if (file_name.startswith('part-') or 
                        file_name.endswith('.txt') or 
                        file_name.endswith('.csv') or
                        file_name.startswith('000000_')):
                        data_files.append(file_path)
        
        if not data_files:
            logger.error("No data files found in HDFS path")
            return None
        
        logger.info(f"Found {len(data_files)} data files")
        
        # Extract data from files
        all_data = []
        files_to_process = data_files[:1] if sample_only else data_files[:10]  # Limit for testing
        
        for i, file_path in enumerate(files_to_process):
            logger.info(f"Processing file {i+1}/{len(files_to_process)}: {file_path}")
            
            # Copy file to local temp
            temp_file = f"/tmp/hdfs_extract_{i}.txt"
            copy_cmd = f"hdfs dfs -get {file_path} {temp_file}"
            
            output, error, status = self.run_command(copy_cmd, timeout=300)
            if status != 0:
                logger.warning(f"Failed to copy file {file_path}: {error}")
                continue
            
            # Read the file
            file_data = self.read_remote_file(temp_file)
            if file_data is not None and len(file_data) > 0:
                all_data.append(file_data)
                logger.info(f"File {i+1} loaded: {len(file_data)} rows")
            
            # Clean up
            self.run_command(f"rm -f {temp_file}")
            
            if sample_only and len(all_data) > 0:
                break  # Just need one file for sample
        
        # Combine data
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            logger.info(f"Combined data: {len(combined_df)} rows, {len(combined_df.columns)} columns")
            return combined_df
        
        return None
    
    def try_alternative_methods(self, table_name: str):
        """Try alternative methods to access the data"""
        logger.info("Trying alternative access methods...")
        
        # Method 1: Try direct Python with existing HDFS tools
        python_script = f'''
import subprocess
import sys

try:
    # Try to use pyhive or other Hadoop libraries
    result = subprocess.run(['hdfs', 'dfs', '-cat', '/warehouse/tablespace/managed/hive/{table_name.replace(".", ".db/")}/*'], 
                          capture_output=True, text=True, timeout=300)
    if result.returncode == 0:
        print("HDFS_DATA_START")
        print(result.stdout[:1000])  # First 1000 chars
        print("HDFS_DATA_END")
    else:
        print("HDFS_ERROR:", result.stderr)
except Exception as e:
    print("PYTHON_ERROR:", str(e))
'''
        
        temp_script = "/tmp/hdfs_test.py"
        # Write script to file
        script_cmd = f"cat > {temp_script} << 'EOF'\n{python_script}\nEOF"
        self.run_command(script_cmd)
        
        # Run script
        output, error, status = self.run_command(f"python3 {temp_script}", timeout=300)
        
        if "HDFS_DATA_START" in output:
            data_section = output.split("HDFS_DATA_START")[1].split("HDFS_DATA_END")[0]
            if data_section.strip():
                logger.info("Alternative method found data!")
                # Parse the data
                lines = data_section.strip().split('\n')
                if len(lines) > 1:
                    # Try to create DataFrame
                    for delimiter in ['\t', ',', '|', '\001']:  # Include Hive default delimiter
                        try:
                            df = pd.read_csv(io.StringIO('\n'.join(lines)), delimiter=delimiter, header=None)
                            if len(df.columns) > 1:
                                logger.info(f"Parsed with delimiter '{repr(delimiter)}': {len(df)} rows, {len(df.columns)} columns")
                                return df
                        except:
                            continue
        
        # Clean up
        self.run_command(f"rm -f {temp_script}")
        
        return None
    
    def read_remote_file(self, file_path: str):
        """Read file from remote server"""
        try:
            sftp = self.client.open_sftp()
            
            try:
                sftp.stat(file_path)
            except FileNotFoundError:
                sftp.close()
                return None
            
            with sftp.open(file_path, 'rb') as file:
                content = file.read()
            
            sftp.close()
            
            # Decode content
            try:
                text_content = content.decode('utf-8')
            except UnicodeDecodeError:
                text_content = content.decode('latin-1', errors='ignore')
            
            if not text_content.strip():
                return pd.DataFrame()
            
            lines = text_content.strip().split('\n')
            lines = [line for line in lines if line.strip()]
            
            if len(lines) == 0:
                return pd.DataFrame()
            
            # Try different delimiters including Hive's default
            delimiters = ['\001', '\t', ',', '|']  # \001 is Hive's default delimiter
            
            for delimiter in delimiters:
                try:
                    df = pd.read_csv(io.StringIO('\n'.join(lines)), delimiter=delimiter, header=None, low_memory=False)
                    if len(df.columns) > 1 and len(df) > 0:
                        logger.info(f"Successfully parsed with delimiter '{repr(delimiter)}': {len(df)} rows, {len(df.columns)} columns")
                        # Add generic column names
                        df.columns = [f'col_{i}' for i in range(len(df.columns))]
                        return df
                except Exception as e:
                    continue
            
            # If all fail, return as single column
            logger.warning("Could not parse as delimited data, returning as single column")
            return pd.DataFrame({'raw_data': lines})
            
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {str(e)}")
            return None
    
    def disconnect(self):
        """Close connection"""
        if self.client:
            self.client.close()
            logger.info("Disconnected")

def main():
    # Configuration
    HOSTNAME = "skpbj0003.global.tesco.org"
    USERNAME = "phrubos"  # UPDATE THIS
    PASSWORD = "BigCityLife2005"  # UPDATE THIS
    TABLE_NAME = "sch_analysts.tbl_srd_planogram_analysis"
    
    try:
        # Connect
        extractor = HDFSDataExtractor(HOSTNAME, USERNAME, PASSWORD)
        if not extractor.connect():
            return None
        
        logger.info("Attempting data extraction through multiple methods...")
        
        # Method 1: Find table location and extract via HDFS
        table_path = extractor.find_table_location(TABLE_NAME)
        
        if table_path:
            logger.info("Testing with sample data from HDFS...")
            sample_df = extractor.extract_hdfs_data(table_path, sample_only=True)
            
            if sample_df is not None and len(sample_df) > 0:
                logger.info(f"Sample successful! Shape: {sample_df.shape}")
                print("Sample data:")
                print(sample_df.head())
                
                proceed = input("\nSample extraction successful! Extract more data? (y/n): ")
                if proceed.lower() == 'y':
                    logger.info("Extracting full dataset...")
                    df = extractor.extract_hdfs_data(table_path, sample_only=False)
                    
                    if df is not None:
                        # Save to file
                        filename = f"planogram_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                        df.to_csv(filename, index=False)
                        logger.info(f"Data saved to {filename}")
                        extractor.disconnect()
                        return df
            else:
                logger.info("HDFS method failed, trying alternative...")
        
        # Method 2: Try alternative approaches
        alt_df = extractor.try_alternative_methods(TABLE_NAME)
        if alt_df is not None:
            logger.info(f"Alternative method successful! Shape: {alt_df.shape}")
            print("Sample data:")
            print(alt_df.head())
            
            filename = f"planogram_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            alt_df.to_csv(filename, index=False)
            logger.info(f"Data saved to {filename}")
            extractor.disconnect()
            return alt_df
        
        logger.error("All extraction methods failed. You may need to:")
        logger.error("1. Check if the table name is correct")
        logger.error("2. Verify you have permissions to access the table")
        logger.error("3. Contact your Hadoop administrator")
        
        extractor.disconnect()
        return None
        
    except Exception as e:
        logger.error(f"Execution failed: {str(e)}")
        return None

if __name__ == "__main__":
    df = main()