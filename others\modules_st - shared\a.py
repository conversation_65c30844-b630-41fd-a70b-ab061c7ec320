import streamlit as st
import pandas as pd
from pathlib import Path
from io import BytesIO
import os
import polars as pl
import math
import shutil
import numpy as np
from datetime import date, datetime, timedelta

# data input
@st.cache_data
def load_initial_data():
    directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd())
    excel_inputs_f = "Stores_Inputs_2024_Q1_wo_prepacked_hours_fluct_cust_repaired_N.xlsx"
    
    key_to_drg = pd.read_excel(directory / excel_inputs_f, "key_table_moduls")

    stores = list(
        pl.read_excel(directory / excel_inputs_f, engine="calamine")["Store"]
            .unique()
    )
    
    return key_to_drg, stores

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_files_by_date(base_path, target_date):
    all_folders = [os.path.join(base_path, d) for d in os.listdir(base_path) 
                  if os.path.isdir(os.path.join(base_path, d))]
    
    folder_dates = [(folder, datetime.fromtimestamp(os.path.getmtime(folder))) 
                   for folder in all_folders]
    folder_dates.sort(key=lambda x: abs((x[1].date() - target_date).days))
    
    return folder_dates[0][0] if folder_dates else None

def get_latest_xlsx_file(directory, country, target_date=None):
    """
    Get the latest Excel file for the specified country, considering target date if provided
    """
    all_files = [os.path.join(directory, f) for f in os.listdir(directory) 
                 if not f.startswith('~$') and not f.startswith('.') 
                 and os.path.isfile(os.path.join(directory, f))]
    
    if not all_files:
        return None

    if target_date:
        # Sort files by how close they are to the target date
        files_with_dates = [(f, datetime.fromtimestamp(os.path.getmtime(f)).date()) 
                           for f in all_files]
        files_with_dates.sort(key=lambda x: abs((x[1] - target_date).days))
        
        if country == 'HU':
            xlsx_files = [f for f, _ in files_with_dates if f.lower().endswith('.xlsx')]
            return xlsx_files[0] if xlsx_files else None
        elif country == 'CZ':
            return files_with_dates[0][0]
        elif country == 'SK':
            return files_with_dates[1][0] if len(files_with_dates) > 1 else None
    else:
        if country == 'HU':
            xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx')]
            xlsx_files = max(xlsx_files, key=os.path.getmtime)
        elif country == 'CZ':
            xlsx_files = all_files[0]
        elif country == 'SK':
            xlsx_files = all_files[1]
        else:
            return None

        return xlsx_files

def Modul_numbers(key_to_drg, stores, selected_date):
    print("\nCE Modul numbers downloading...\n")
    
    def read_xlsx_sheets(filepath):
        xls = pd.ExcelFile(filepath)
        sheets = {}
        for sheet_name in xls.sheet_names:
            sheets[sheet_name] = pd.read_excel(xls, sheet_name)
        combined_df = pd.concat(sheets.values(), ignore_index=True)
        return combined_df
    
    def copy_excel_file(source, destination):
        shutil.copy(source, destination)
    
    # HU part
    hu_path = r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report Hungary"
    latest_folder_hu = get_files_by_date(hu_path, selected_date)
    mods_hu_f = get_latest_xlsx_file(latest_folder_hu, 'HU', selected_date)
    print(f"\nHU file: {mods_hu_f}\n")
    mods_hu = read_xlsx_sheets(mods_hu_f)
    mods_hu = mods_hu.query("Status == 'Live'")

    mods_hu = mods_hu.groupby(['Store Number', 'Department (Desc1)', 'Display Group Code',
                              'Display Group Local Description', 'Display Group Description'],
                             as_index=False).agg({'Number of mods':'sum',
                                                'Number of Products allocated':'sum'})

    mods_hu['Store Number'] = mods_hu['Store Number'].map(lambda x: str(4) + str(x)).astype("int")
    print(f"HU file contains: {mods_hu['Store Number'].nunique()} stores")
    dep_df = mods_hu[['Display Group Code', 'Department (Desc1)']].drop_duplicates()
    print("\nHU Modul numbers done!!\n")
    
    # SK CZ part
    sk_cz_path =  r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report CZ  & SK\CZ&SK"
    latest_folder_sk_cz = get_files_by_date(sk_cz_path, selected_date)  # Updated function name
    latest_folder_sk_cz = get_files_by_date(latest_folder_sk_cz, selected_date)
    mods_sk_f = get_latest_xlsx_file(latest_folder_sk_cz, 'SK', selected_date)
    print(f"\nSK file: {mods_sk_f}\n")
    mods_sk = read_xlsx_sheets(mods_sk_f)
    mods_sk['Store Number'] = mods_sk['Store Number'].map(lambda x: str(2) + str(x)).astype("int")
    
    print(f"SK file contains: {mods_sk['Store Number'].nunique()} stores\n")

    mods_cz_f = get_latest_xlsx_file(latest_folder_sk_cz, 'CZ', selected_date)
    print(f"\nCZ file: {mods_cz_f}\n")
    mods_cz = read_xlsx_sheets(mods_cz_f)
    mods_cz['Store Number'] = pd.to_numeric(mods_cz['Store Number'], errors='coerce')
    mods_cz = mods_cz[mods_cz['Store Number'].notna()]
    mods_cz['Store Number'] = mods_cz['Store Number'].astype(int)
    mods_cz['Store Number'] = mods_cz['Store Number'].map(lambda x: str(1) + str(x)).astype("int")
    print(f"CZ file contains: {mods_cz['Store Number'].nunique()} stores\n")
    
    mods_cz_sk = pd.concat([mods_sk, mods_cz])
    mods_cz_sk = mods_cz_sk[mods_cz_sk["Planogram Status"] == 'Live']
    mods_cz_sk = mods_cz_sk.groupby(['Store Number', 'Display Group Code', 'Display Group Description'],
                                   as_index=False).agg({'POG No Of Mods':'sum',
                                                      'Number of Products allocated':'sum'})
    mods_cz_sk.rename(columns={'POG No Of Mods':'Number of mods'}, inplace=True)
    mods_cz_sk = mods_cz_sk.merge(dep_df, on="Display Group Code", how='left')
    print("\nCZ&SK Modul numbers done!!\n")
    
    mods_ce = pd.concat([mods_hu, mods_cz_sk])
    mods_ce.rename(columns={'Display Group Code':'drg', 'Store Number':'store'}, inplace=True)
    
    print(f"CE total file contains: {mods_ce['store'].nunique()} stores\n")
    
    # Create a dictionary mapping drg_letter to department
    drg_dep_mapping = key_to_drg.set_index('drg_letter')['dep'].to_dict()
    drg_dep_mapping = {k: v for k, v in drg_dep_mapping.items() 
                      if not (isinstance(k, float) and math.isnan(k))}
    
    for drg_letter, dep in drg_dep_mapping.items():
        mods_ce.loc[mods_ce['drg'].str.startswith(drg_letter), 'dep'] = dep
    
    # free form & healthy food to PRO
    # mods_ce['dep'] = np.where((mods_ce['Display Group Description']=='FREE_FROM') |
    #                          (mods_ce['Display Group Description']=='HEALTHY_FOOD'),
    #                          'PRO', mods_ce['dep'])
    
    mods_ce['country'] = ['CZ' if str(x).startswith('1') else 'SK' if str(x).startswith('2')
                         else 'HU' for x in mods_ce['store']]
    
    mods_ce = mods_ce[mods_ce.store.isin(stores)]
    
    print(f"CE (with store list) file contains: {mods_ce['store'].nunique()} stores\n")

    mods_ce['dep'] = np.where(mods_ce['drg'] == 'P1A', 'NEW', mods_ce['dep'])
    
    print("\nCE Modul numbers done!!\n")
    
    return mods_ce

def to_excel(df):
    output = BytesIO()
    writer = pd.ExcelWriter(output, engine='xlsxwriter')
    df.to_excel(writer, index=False, sheet_name='Sheet1')
    writer.close()
    processed_data = output.getvalue()
    return processed_data

def main():
    st.set_page_config(
        page_title="Ce_Module_Numbers",
        page_icon=':bar_chart:'
    )

    # Load initial data with caching
    key_to_drg, stores = load_initial_data()

    st.title("Modul Numbers Downloader")

    # Add date selector
    selected_date = st.date_input(
        "Select Date",
        value=date.today(),
        min_value=date(2022, 1, 1),
        max_value=date.today()
    )

    # Update file paths based on selected date
    hu_path = r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report Hungary"
    sk_cz_path = r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report CZ  & SK\CZ&SK"

    latest_folder_hu = get_files_by_date(hu_path, selected_date)
    latest_folder_sk_cz = get_files_by_date(sk_cz_path, selected_date)
    if latest_folder_sk_cz:
        latest_folder_sk_cz = get_files_by_date(latest_folder_sk_cz, selected_date)

    # Get files based on selected date
    mods_hu_f = get_latest_xlsx_file(latest_folder_hu, 'HU', selected_date) if latest_folder_hu else None
    mods_sk_f = get_latest_xlsx_file(latest_folder_sk_cz, 'SK', selected_date) if latest_folder_sk_cz else None
    mods_cz_f = get_latest_xlsx_file(latest_folder_sk_cz, 'CZ', selected_date) if latest_folder_sk_cz else None

    with st.expander('Input Files', expanded=False):
        if mods_hu_f:
            st.write(f":flag-hu: : {mods_hu_f}")
            st.write(f"Last modified: {datetime.fromtimestamp(os.path.getmtime(mods_hu_f)).strftime('%Y-%m-%d %H:%M:%S')}")
        
        if mods_sk_f:
            st.write(f":flag-sk: : {mods_sk_f}")
            st.write(f"Last modified: {datetime.fromtimestamp(os.path.getmtime(mods_sk_f)).strftime('%Y-%m-%d %H:%M:%S')}")
        
        if mods_cz_f:
            st.write(f":flag-cz: : {mods_cz_f}")
            st.write(f"Last modified: {datetime.fromtimestamp(os.path.getmtime(mods_cz_f)).strftime('%Y-%m-%d %H:%M:%S')}")

    # Button to run the script
    if st.button("Run Script"):
        with st.spinner("Running Script..."):
            df = Modul_numbers(key_to_drg, stores, selected_date)  # Added selected_date parameter
            df = df.drop_duplicates()
        st.success("Script execution complete!")

        df_xlsx = to_excel(df.drop_duplicates())
        st.download_button(
            label="Download data as Excel",
            data=df_xlsx,
            file_name=f'CE_modules_{selected_date}.xlsx',
            mime='application/vnd.ms-excel'
        )

if __name__ == "__main__":
    main()