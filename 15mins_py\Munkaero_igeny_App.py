import sys
import os
from PySide6.QtWidgets import (
    QA<PERSON>lication, QMainWindow, QPushButton, QMessageBox,
    QVBoxLayout, QWidget, QHBoxLayout, QDateEdit,
    QStyleFactory, QLabel, QSpacerItem, QSizePolicy,
    QComboBox, QCheckBox, QLineEdit, QFrame, QGridLayout
)
from PySide6.QtCore import QObject, Signal, QRunnable, Slot, QThreadPool, QDate, Qt, QPoint, QTimer
from PySide6.QtGui import QIcon, QColor, QPalette, QKeyEvent
from PySide6 import QtGui

import algos.quarterHour_fetch_data as al
from pathlib import Path
from datetime import datetime, timedelta

class WorkerSignals(QObject):
    finished = Signal(str)

class Worker(QRunnable):
    def __init__(self, start, end, selected_stores, customerModifier, save_Separately):
        super().__init__()
        self.signals = WorkerSignals()
        self.start = start
        self.end = end
        self.selected_stores = selected_stores
        self.customerModifier = customerModifier
        self.save_Separately = save_Separately

    def run(self):



        data = al.quarterHour_report(self.start, self.end, self.selected_stores, self.customerModifier)
        directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd())

        if all(value[0] == 100 for value in self.customerModifier.values()):

            if len(self.selected_stores) == 1:
                filename_f = directory / f"Munkaerő_igény_{self.start}_-_{self.end}_{self.selected_stores}.xlsx"
                filename = al.get_unique_filename(directory, filename_f)
            if len(self.selected_stores) > 1:
                filename_f = directory / f"Munkaerő_igény_{self.start}_-_{self.end}_All_Stores.xlsx"
                filename = al.get_unique_filename(directory, filename_f)
            if not self.save_Separately:
                al.formatting_df(data, filename)
                al.adjust_column_widths(filename)



            if self.save_Separately:
                folder_name_f = f"{self.start}_{self.end}_AllStores"
                folder_name = al.get_unique_filename(directory, folder_name_f)
                al.create_a_folder(directory, folder_name)

                # Group the data by the "store" column
                grouped = data.groupby("Áruház")

                # Save each group into separate Excel files
                for name, group in grouped:
                    filename = f"{folder_name}/{self.start}_-_{self.end}_{name}.xlsx"
                    al.formatting_df(group, filename)
                    al.adjust_column_widths(filename)
                    # group.to_excel(filename, index=False)

                al.all_stores_separated_conditional_formatting(directory, folder_name )

        if not all(value[0] == 100 for value in self.customerModifier.values()):
            if len(self.selected_stores) == 1:
                filename_f = directory / f"Munkaerő_igény_{self.start}_-_{self.end}_{self.selected_stores}_Vásárló%.xlsx"
                filename = al.get_unique_filename(directory, filename_f)
            if len(self.selected_stores) > 1:
                filename_f = directory / f"Munkaerő_igény_{self.start}_-_{self.end}_All_Stores_Vásárló%.xlsx"
                filename = al.get_unique_filename(directory, filename_f)

            if not self.save_Separately:
                al.formatting_df(data, filename)
                al.adjust_column_widths(filename)

            if self.save_Separately:
                folder_name_f = f"{self.start}_{self.end}_AllStores_Vásárló%"
                folder_name = al.get_unique_filename(directory, folder_name_f)
                al.create_a_folder(directory, folder_name)

                # Group the data by the "store" column
                grouped = data.groupby("Áruház")

                # Save each group into separate Excel files
                for name, group in grouped:
                    filename = f"{folder_name}/{self.start}_-_{self.end}_Vásárló%_{name}.xlsx"
                    al.formatting_df(group, filename)
                    al.adjust_column_widths(filename)
                    # group.to_excel(filename, index=False)

                al.all_stores_separated_conditional_formatting(directory, folder_name)



        self.signals.finished.emit(str(filename))

class CollapsibleFrame(QFrame):
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.StyledPanel | QFrame.Plain)

        self.toggle_button = QPushButton(title)
        self.toggle_button.setCheckable(True)
        self.toggle_button.setChecked(False)
        self.toggle_button.clicked.connect(self.toggle_collapsed)


        self.content_layout = QVBoxLayout()
        self.content_layout.setContentsMargins(10, 10, 10, 10)
        self.setLayout(self.content_layout)

        self.header_layout = QHBoxLayout()
        self.header_layout.addWidget(self.toggle_button)
        self.header_layout.addStretch()

        self.content_layout.addLayout(self.header_layout)

        # Placeholder widget for content
        self.content_widget = QWidget()
        self.content_widget.setVisible(False)  # Initially hidden
        self.content_layout.addWidget(self.content_widget)

    def toggle_collapsed(self):
        is_checked = self.toggle_button.isChecked()
        self.content_widget.setVisible(is_checked)

class LimitedComboBox(QComboBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.max_chars = 5  # Set the maximum number of characters allowed
        self.setEditable(True)
        self.lineEdit().setMaxLength(self.max_chars)

    def keyPressEvent(self, event: QKeyEvent) -> None:
        if event.key() == Qt.Key_Return:
            text = self.currentText()
            if len(text) > self.max_chars:
                self.setEditText(text[:self.max_chars])
            self.lineEdit().deselect()
            return
        super().keyPressEvent(event)

class PercentLineEdit(QLineEdit):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMaxLength(3)  # Set maximum length to 3 characters
        self.setAlignment(Qt.AlignCenter)  # Align text to the right
        self.setText("100")  # Set default value

    def mousePressEvent(self, event):
        self.selectAll()  # Select all text when clicked
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        self.setWindowTitle("Munkaerő Igény Riport")
        self.setGeometry(100, 100, 400, 150)
        QApplication.setStyle(QStyleFactory.create("Fusion"))

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        self.start_label = QLabel("Kezdő Dátum:")
        self.layout.addWidget(self.start_label)

        self.start_input = QDateEdit(self)
        self.start_input.setDisplayFormat("yyyy-MM-dd")
        self.start_input.setCalendarPopup(True)
        self.start_input.setDate(QDate.currentDate().addYears(-1))
        self.start_input.setMaximumDate(QDate.currentDate().addDays(-1))
        self.layout.addWidget(self.start_input)

        self.end_label = QLabel("Záró Dátum:")
        self.layout.addWidget(self.end_label)

        self.end_input = QDateEdit(self)
        self.end_input.setDisplayFormat("yyyy-MM-dd")
        self.end_input.setCalendarPopup(True)
        self.end_input.setDate(QDate.currentDate().addYears(-1))
        self.end_input.setMaximumDate(QDate.currentDate().addDays(-1))
        self.layout.addWidget(self.end_input)

        self.store_layout = QVBoxLayout()

        self.store_label = QLabel("Áruház Választó:")
        self.store_layout.addWidget(self.store_label)

        self.store_options_layout = QHBoxLayout()

        self.combo_box = LimitedComboBox()
        self.combo_box.setEnabled(False)
        self.combo_box.setEditable(True)
        self.combo_box.addItems([str(store) for store in al.stores])
        self.combo_box.currentIndexChanged.connect(self.update_selected_stores)
        self.store_options_layout.addWidget(self.combo_box)

        self.check_box_save_separately = QCheckBox("Mentés Áruházanként")
        self.check_box = QCheckBox("Minden Bolt")
        self.check_box.stateChanged.connect(self.toggle_combo_box)
        self.store_options_layout.addWidget(self.check_box)
        self.check_box.setCheckState(Qt.Checked)

        self.check_box_save_separately.stateChanged.connect(self.save_into_separate_Excels)
        self.store_options_layout.addWidget(self.check_box_save_separately)

        self.store_layout.addLayout(self.store_options_layout)
        self.layout.addLayout(self.store_layout)

        # Create a collapsible frame for the customer modifier
        self.customer_modifier_frame = CollapsibleFrame("Vásárlószám Módosító (%)")

        # Layout for customer modifier frame content
        self.customer_modifier_content_layout = QVBoxLayout(self.customer_modifier_frame.content_widget)
        self.customer_modifier_content_layout.setContentsMargins(10, 10, 10, 10)

        # Create input fields for each day of the week
        days_of_week = ["Hétfő", "Kedd", "Szerda", "Csütörtök", "Péntek", "Szombat", "Vasárnap"]
        self.day_inputs = {}
        grid_layout = QGridLayout()

        row = 0
        col = 0
        for i, day in enumerate(days_of_week):
            day_label = QLabel(f"{day}:")
            day_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

            day_input = PercentLineEdit(self)
            day_input.setFixedWidth(30)

            grid_layout.addWidget(day_label, row, col)
            grid_layout.addWidget(day_input, row, col + 1)

            row += 1
            if row > 2:
                row = 0
                col += 2

            # Store the input field in a dictionary for later access
            self.day_inputs[day] = day_input

        self.customer_modifier_content_layout.addLayout(grid_layout)

        # Add the customer modifier frame to the main layout
        self.layout.addWidget(self.customer_modifier_frame)

        self.button_layout = QHBoxLayout()
        self.button_layout.addStretch()

        self.button = QPushButton("Futtatás!!", self)
        self.button.clicked.connect(self.run_task)
        self.button.setFixedSize(150, 50)
        self.button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: #ffffff;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        self.button_layout.addWidget(self.button)
        self.layout.addLayout(self.button_layout)

        footer_label = QLabel("© 2024 Munkaerő Igény App.")
        footer_label.setObjectName("footerLabel")
        footer_layout = QVBoxLayout()
        spacer_item = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        footer_layout.addItem(spacer_item)
        footer_layout.addWidget(footer_label)
        self.layout.addLayout(footer_layout)

        self.threadpool = QThreadPool()
        self.is_report_shown = False

        self.grab_offset = QPoint()

        self.download_label_text = "Letöltés"
        self.download_timer = QTimer(self)
        self.download_timer.timeout.connect(self.update_download_label)

        self.selected_stores = al.stores
        self.customerModifier =  {
                                        "Hétfő": [100],
                                        "Kedd": [100],
                                        "Szerda": [100],
                                        "Csütörtök": [100],
                                        "Péntek": [100],
                                        "Szombat": [100],
                                        "Vasárnap": [100]
                                        }

        # # Add the loop to connect textChanged signal to update_customer_modifier method
        for day, input_field in self.day_inputs.items():
            input_field.textChanged.connect(lambda text, day=day: self.update_customer_modifier(day, text))

        # for day, input_field in self.day_inputs.items():
        #     input_field.textChanged.connect(lambda text, day=day: self.update_customer_modifier(day, text))

        self.save_Separately = False
        for x in [self.check_box, self.check_box_save_separately]:
            x.setStyleSheet("""
                QCheckBox {
                    spacing: 2px;
                }
                QCheckBox::indicator {
                    width: 20px;
                    height: 20px;
                }
            """)

    def update_customer_modifier(self, day, value):
        try:
            new_value = int(value)
            self.customerModifier[day] = [new_value]
        except ValueError:
            # Handle the case where the user enters non-integer values or empty string
            pass



    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.grab_offset = event.globalPos() - self.frameGeometry().topLeft()

    def mouseMoveEvent(self, event):
        if event.buttons() & Qt.LeftButton:
            self.move(event.globalPos() - self.grab_offset)

    def mouseReleaseEvent(self, event):
        pass

    def update_download_label(self):
        if len(self.download_label_text) >= 14:
            self.download_label_text = "Letöltés"
        else:
            self.download_label_text += "."
        self.button.setText(self.download_label_text)

    def run_task(self):
        if self.is_report_shown:
            self.is_report_shown = False

        start_date = int(self.start_input.date().toString("yyyyMMdd"))
        end_date = int(self.end_input.date().toString("yyyyMMdd"))

        if end_date < start_date:
            QMessageBox.warning(self, "Hiba", "A záró dátumnak nagyobbnak, vagy egyenlőnek kell lenni, mint a kezdő dátum.")
            return

        self.button.setEnabled(False)
        self.button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: #ffffff;
                border-radius: 10px;
            }
        """)

        self.download_label_text = "Letöltés"
        self.download_timer.start(500)

        worker = Worker(start_date, end_date, self.selected_stores, self.customerModifier, self.save_Separately)
        worker.signals.finished.connect(self.task_finished)
        self.threadpool.start(worker)

    @Slot(str)
    def task_finished(self, filename):
        self.download_timer.stop()
        self.button.setText("Futtatás!!")
        self.button.setEnabled(True)
        self.button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: #ffffff;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        if not self.is_report_shown:
            self.show_report_popup(self.save_Separately, self.customerModifier)
            self.is_report_shown = True

            if not self.save_Separately:
                if filename:
                    os.startfile(filename)

    def show_report_popup(self, save_Separately, customerModifier):
        if save_Separately and all(value[0] == 100 for value in customerModifier.values()):
            start = int(self.start_input.date().toString("yyyyMMdd"))
            end = int(self.end_input.date().toString("yyyyMMdd"))
            QMessageBox.information(self, "Futtatás vége", f" Riport(ok) mentve a '{start}_{end}_AllStores' mappába.")

        if save_Separately and not all(value[0] == 100 for value in customerModifier.values()):
            start = int(self.start_input.date().toString("yyyyMMdd"))
            end = int(self.end_input.date().toString("yyyyMMdd"))
            QMessageBox.information(self, "Futtatás vége", f" Riport(ok) mentve a '{start}_{end}_AllStores_Vásárló%' mappába.")

        if not save_Separately:
            QMessageBox.information(self, "Futtatás vége", "A riport elkészült.")

    def toggle_combo_box(self, state):
        if state == 2:
            self.combo_box.setEnabled(False)
            self.selected_stores = al.stores.copy()
            self.check_box_save_separately.setEnabled(True)
        else:
            self.combo_box.setEnabled(True)
            self.check_box_save_separately.setEnabled(False)
            self.check_box_save_separately.setChecked(False)
            self.selected_stores = [self.combo_box.currentText()]

    def update_selected_stores(self, index):
        if self.check_box.isChecked():
            self.selected_stores = al.stores.copy()
        else:
            self.selected_stores = [self.combo_box.itemText(index)]

    def save_into_separate_Excels(self):
        if self.check_box_save_separately.isChecked():
            self.save_Separately = True
        if not self.check_box_save_separately.isChecked():
            self.save_Separately = False

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QtGui.QIcon('cash-desk.ico'))
    window = MainWindow()
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor("#f2f2f2"))
    window.setPalette(palette)
    window.show()
    sys.exit(app.exec())
