import pyodbc
import pandas as pd
import os
import polars as pl
import numpy as np



pd.set_option("display.max_columns", None)


conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()



# # Specify the directory path
# folder_path = r'C:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\PBS_PBL'

# # List all .xlsb files in the directory
# xlsb_files = [file for file in os.listdir(folder_path) if file.endswith('.xlsb')]

# # Initialize an empty list to store DataFrames
# dfs = []

# # Iterate over each xlsb file
# for file in xlsb_files:
#     # Read the xlsb file into a DataFrame
#     # df = pd.read_excel(os.path.join(folder_path, file), engine='pyxlsb')
    
#     df = pl.read_excel(os.path.join(folder_path, file), engine="calamine").to_pandas()
    
#     # Append the DataFrame to the list
#     dfs.append(df)

# # Concatenate all DataFrames in the list into a single DataFrame
# ce_pbs_pbl = pd.concat(dfs, ignore_index=True)

def determine_country(store):
    if str(store).startswith('2'):
        return 'SK'
    elif str(store).startswith('1'):
        return 'CZ'
    elif str(store).startswith('4'):
        return 'HU'
    else:
        return None



store_list = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\Repl\Repl_Stores_Inputs_2025_Q1_v5.xlsx")
store_list.columns = [ x.lower() for x in store_list.columns]
store_list = store_list[["store", "format"]]

ce_pbs_pbl = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\PBS_PBL\dcr_reports\pbs_pbl_dataset.parquet")
ce_pbs_pbl['country'] = ce_pbs_pbl['Store'].apply(determine_country)
ce_pbs_pbl.rename(columns={"Tpnb": "tpnb", "Tpn":"tpn", "Store": "store", "Container Id":"rollcage/pallet ID", "Deliv Type":"Delivering type"}, inplace=True)

ce_pbs_pbl = ce_pbs_pbl.merge(store_list, on="store", how="left")

ce_pbs_pbl = ce_pbs_pbl[ce_pbs_pbl.format.notnull()]
ce_pbs_pbl = ce_pbs_pbl[(ce_pbs_pbl.Ldate >= '2024-05-27') & (ce_pbs_pbl.Ldate <= '2024-09-01')]



act_dataset = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\plano_1104\Dataset_based_on_plano_1104"
act_dataset_df = pd.read_parquet(act_dataset)

ce_pbs_pbl = ce_pbs_pbl.merge(act_dataset_df[['country', 'tpn', 'pmg', 'Category name']].drop_duplicates(), on=['country', 'tpn'], how='left')
    


missing_df = ce_pbs_pbl[(ce_pbs_pbl.tpnb.notnull())&(ce_pbs_pbl.pmg.isnull())]
# missing_df['tpn'] = missing_df['tpn'].astype("int32")



products = missing_df[['country', 'tpnb']].drop_duplicates().groupby(["country"])['tpnb'].apply(lambda s: s.tolist()).to_dict()



# Function to count unique values for all keys in a dictionary
def count_unique_values_for_all_keys(dictionary):
    unique_counts = {}
    # Iterate over each key-value pair in the dictionary
    for key, values in dictionary.items():
        # Initialize a set to store unique values for the current key
        unique_values = set()
        # Iterate over each value in the list
        for value in values:
            # Add the value to the set
            unique_values.add(value)
        # Count the number of unique values for the current key
        unique_counts[key] = len(unique_values)
    return unique_counts

# Example usage
unique_counts = count_unique_values_for_all_keys(products)
print("Number of unique values for each key:")
for key, count in unique_counts.items():
    print(f"'{key}': {count}")



df_pmg = pd.DataFrame()

for k, v in products.items():
                    
    s = list()
    
    for x in v:
                
        s.append(str(int(x)))
        tpnbs = ",".join(s)
    
    sql = """ SELECT 
                    tpns.cntr_code AS country,
                    cast(tpns.slad_tpnb AS INT) AS tpnb,
                    cast(tpns.slad_tpn AS INT) AS tpn,
                    /*tpns.dmat_div_des_en AS division,
                    cast(tpns.dmat_div_code as INT) as DIV_ID,
                    tpns.dmat_dep_des_en AS department,
                    cast(tpns.dmat_dep_code as INT) as DEP_ID,
                    tpns.dmat_sec_des_en AS section,
                    cast(tpns.dmat_sec_code as INT) as SEC_ID,
                    tpns.dmat_grp_des_en AS group,
                    cast(tpns.dmat_grp_code as INT) as GRP_ID,
                    tpns.dmat_sgr_des_en AS subgroup,
                    cast(tpns.dmat_sgr_code as INT) as SGR_ID,*/

                    hier.pmg as pmg
            FROM
                    DM.dim_artgld_details tpns
                    
            LEFT JOIN tesco_analysts.hierarchy_spm hier
            ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0")
            AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0")
            AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0")
            AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0")
            AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
            WHERE 
                    slad_tpnb in ({tpnbs}) 
            AND     
                    cntr_code = '{k}' 
            AND     
                    dmat_sgr_des_en <> "Do not use"
            GROUP BY 
                    cntr_code,
                    slad_tpnb,
                    slad_tpn,
                    /*dmat_div_des_en,
                    dmat_div_code,
                    dmat_dep_des_en,
                    dmat_dep_code,
                    dmat_sec_des_en,
                    dmat_sec_code,
                    dmat_grp_des_en,
                    dmat_grp_code,
                    dmat_sgr_des_en,
                    dmat_sgr_code,*/
                    hier.pmg
                    
                    """.format(
        tpnbs=tpnbs, k=k
    )


    # art_gold = pl.read_database(sql, conn)
    df_pmg = pd.concat([df_pmg, pd.read_sql(sql, conn)])
    print(f"\nDone: {k}")
    
    
missing_df = missing_df.loc[:, missing_df.columns != 'pmg'].merge(df_pmg[['country','tpnb', 'pmg']].drop_duplicates(), on=['country','tpnb'], how='left')
ce_pbs_pbl = pd.concat([ce_pbs_pbl[ce_pbs_pbl.pmg.notnull()], missing_df])

# =============================================================================
# Settings the Categories
# =============================================================================
ce_pbs_pbl['Category name'] = np.where(ce_pbs_pbl['Category name'].isnull(), "no_data", ce_pbs_pbl['Category name'])

cond = [(ce_pbs_pbl['Category name'] == 'HOUSEHOLD - Cleaning') | (ce_pbs_pbl['Category name'] == 'HOUSEHOLD -Paper;Laundry'),
        (ce_pbs_pbl['Category name'] == 'HEALTH & BEAUTY - Hair care &shaving &season&travel&kids') |
        (ce_pbs_pbl['Category name'] == 'HEALTH & BEAUTY - Oral Care;Face&Body Care;Femme Care') |
        (ce_pbs_pbl['Category name'] == 'HEALTH & BEAUTY - Bath;Deo&Fragances;Pharmacy') ]

result = ["HOUSEHOLD", "HEALTH & BEAUTY" ]



ce_pbs_pbl['Category name'] = np.select(cond, result, ce_pbs_pbl['Category name'])

# Create a dictionary to store pmg values and their corresponding most frequent Category name
pmg_categories = {}

# Iterate over unique pmg values
for pmg_value in ce_pbs_pbl['pmg'].unique():
    # Filter rows where pmg is equal to current pmg_value and Category name is not "no_data"
    filtered_rows = ce_pbs_pbl[(ce_pbs_pbl['pmg'] == pmg_value) & (ce_pbs_pbl['Category name'] != 'no_data') & (ce_pbs_pbl['pmg'].str[:3] != 'HDL')]
    
    # Check if there are any non-"no_data" rows matching the conditions
    if not filtered_rows.empty:
        # Find the most frequent non-"no_data" Category name for the current pmg_value
        most_frequent_category = filtered_rows['Category name'].mode().iloc[0]
        
        # Store the most frequent non-"no_data" Category name for the current pmg_value in the dictionary
        pmg_categories[pmg_value] = most_frequent_category
            
# Update the 'no_data' rows in the DataFrame based on the mapping
ce_pbs_pbl['Category name'] = ce_pbs_pbl.apply(lambda row: pmg_categories.get(row['pmg'], row['Category name']) if row['Category name'] == 'no_data' else row['Category name'], axis=1)
ce_pbs_pbl['Category name'] = np.where(ce_pbs_pbl.pmg.str[:3] == 'FRZ', 'FROZEN', ce_pbs_pbl['Category name'] )
ce_pbs_pbl['Category name'] = np.where((ce_pbs_pbl['Category name'] == 'no_data')&(ce_pbs_pbl.pmg.str[:3] == 'HDL'), 'GM', ce_pbs_pbl['Category name'] )

ce_pbs_pbl['Category name'] = np.where((ce_pbs_pbl.pmg.notnull())&(ce_pbs_pbl['Category name']=="no_data"),ce_pbs_pbl['pmg'].str[:3], ce_pbs_pbl['Category name'] )




print("\nHow many different products have no PMG:")
print(ce_pbs_pbl[(ce_pbs_pbl.pmg.isnull())].groupby('country')['tpnb'].nunique())

# =============================================================================
# Calculate where to need touching RC/Pallet
# =============================================================================
ce_pbs_pbl = ce_pbs_pbl[(ce_pbs_pbl.pmg.notnull())]

ce_pbs_pbl['store_rc_pallet_id_category_nr'] = ce_pbs_pbl.groupby(['store', 'rollcage/pallet ID'])['Category name'].transform("nunique")

unique_cat = ce_pbs_pbl.groupby(['store', 'rollcage/pallet ID'])['Category name'].agg(
    store_rc_pal_id_categories=(lambda x: list(set(x)))).reset_index()


ce_pbs_pbl = ce_pbs_pbl.merge(unique_cat, on=['store', 'rollcage/pallet ID'], how="left")



ce_pbs_pbl['need_to_touch'] = np.where(ce_pbs_pbl['store_rc_pallet_id_category_nr'] >= 4, 1, 0)


#When you need PBS/PBL ratio from the pure dataset
pure_perc_from_base_dataset = ce_pbs_pbl.groupby(["country", "Delivering type",])["rollcage/pallet ID"].nunique()



# #on product level How many to touch to RC/Pallet
# a = ce_pbs_pbl.groupby(["country", 'store','pmg','tpnb','Delivering type'],as_index=False, observed=True).agg({'need_to_touch':'sum', 'rollcage/pallet ID':'nunique'})
# a['tpn_perc_on_how_many_touch'] = a['need_to_touch'] / a['rollcage/pallet ID']

# #what if every PBL needed to touch
# ce_pbs_pbl['need_to_touch'] = np.where(ce_pbs_pbl['Delivering type'].str.contains('PBL') |
#                                             ce_pbs_pbl['Delivering type'].str.contains('MIX'),1, ce_pbs_pbl['need_to_touch'])



# =============================================================================
# Pre-Sort % by PMG
# =============================================================================
pmg_pre_sort_df = ce_pbs_pbl.groupby(['country','store', 'format', 'pmg', 'Delivering type', 'rollcage/pallet ID']).agg(nr_rc_pal =('rollcage/pallet ID', 'nunique')).reset_index()
pmg_pre_sort_df = pmg_pre_sort_df.merge(ce_pbs_pbl[['store', 'rollcage/pallet ID', 'need_to_touch']].drop_duplicates(), on=['store', 'rollcage/pallet ID'], how='left')


# #Settings if only PBS or PBL or both
pmg_pre_sort_df['need_to_touch'] = np.where((pmg_pre_sort_df['Delivering type'].str.contains('PBS')) 
                                            ,0, pmg_pre_sort_df['need_to_touch'])


pmg_pre_sort_df['need_to_touch'] = np.where((pmg_pre_sort_df['country'].str.contains('SK')) 
                                            ,0, pmg_pre_sort_df['need_to_touch'])



pmg_pre_sort_df_wo_del_type = pmg_pre_sort_df.groupby(['country', 'store', 'pmg',])[['nr_rc_pal', 'need_to_touch']].sum().reset_index()
pmg_pre_sort_df_wo_del_type['pre_sort_perc_by_pmg'] = pmg_pre_sort_df_wo_del_type['need_to_touch'] / pmg_pre_sort_df_wo_del_type['nr_rc_pal']


# =============================================================================
# Pallets Delivery Ratio
# =============================================================================

pal_del_ratio = ce_pbs_pbl.groupby(['country','store', 'pmg', 'Delivering type',]).agg(nr_rc_pal =('rollcage/pallet ID', 'nunique')).reset_index()

pal_del_ratio['type'] = np.where(pal_del_ratio['Delivering type'].str.contains('PALLET'), 'pallet', 'rollcage')
pal_del_ratio_df = pal_del_ratio.groupby(['country', 'store', 'pmg', 'type'])['nr_rc_pal'].sum().reset_index()

pal_del_ratio_df = pal_del_ratio_df.pivot_table(index=['country', 'store', 'pmg'], columns=['type'], values=['nr_rc_pal'], aggfunc='sum', fill_value=0).reset_index()


pal_del_ratio_df.columns = [' '.join(col).strip() for col in pal_del_ratio_df.columns.values]
pal_del_ratio_df.rename(columns=lambda x: x.replace('nr_rc_pal ', ''), inplace=True)


for col in ['pallet', 'rollcage']:
    percentage_col = col + '_%'
    pal_del_ratio_df[percentage_col] = (pal_del_ratio_df[col] / pal_del_ratio_df[['pallet', 'rollcage',]].sum(axis=1))
    



# =============================================================================
# Delivery type %
# =============================================================================

pmg_del_type_ratio = pmg_pre_sort_df.groupby(['country', 'store', 'pmg', 'Delivering type'])['nr_rc_pal'].sum().reset_index()\
    .pivot_table(index=['country', 'store', 'pmg'],
                 columns='Delivering type', values='nr_rc_pal', aggfunc="sum", fill_value=0).reset_index()
    
delivery_type_list_pallet = [ 'MIX_PALLET',  'PBL_PALLET', 'PBS_PALLET', ]
delivery_type_list_rc = ['MIX_CAGE',  'PBL_CAGE',  'PBS_CAGE',  ]

full = ['MIX_CAGE',  'PBL_CAGE',  'PBS_CAGE', 'MIX_PALLET',  'PBL_PALLET', 'PBS_PALLET', ]

def create_pallet_rc_percent(df, list_to_iterate: list):   
    for col in list_to_iterate:
        percentage_col = col + '_%'
        df[percentage_col] = (df[col] / df[list_to_iterate].sum(axis=1))
    return df
        
pmg_del_type_ratio = create_pallet_rc_percent(pmg_del_type_ratio, delivery_type_list_pallet)
pmg_del_type_ratio = create_pallet_rc_percent(pmg_del_type_ratio, delivery_type_list_rc)

# pmg_del_type_ratio = create_pallet_rc_percent(pmg_del_type_ratio, full)

pmg_del_type_ratio.replace(np.nan, 0, inplace=True) 

# =============================================================================
# Pre-Sort & Pallets Delivery Ratio & Delivery type % DF
# =============================================================================

df = pmg_pre_sort_df_wo_del_type.merge(pal_del_ratio_df, on=['country','store', 'pmg'], how='left')\
    .merge(pmg_del_type_ratio, on=['country','store', 'pmg'], how='left')
    
df['dep'] = df.pmg.str[:3]
df = df[df.dep.isin(['BWS','HEA','DRY'])]
df.drop('dep', axis=1, inplace=True)


# df = df[df.country == "CZ"]
   
df.to_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\PBS_PBL_presort\presort_pal_del_with4prodGroups_w14_27", compression="gzip")

