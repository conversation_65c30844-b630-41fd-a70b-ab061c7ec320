{"cells": [{"cell_type": "code", "execution_count": 2, "id": "8d117a1c-c3d7-48fe-86ec-d89c840a73f6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "pd.set_option(\"display.max_columns\", None)\n", "\n", "a = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\model_outputs\\Q1_v3_fluct_cust\\INSIGHT_Q1_v3_fluct_cust.parquet.gz\")"]}, {"cell_type": "code", "execution_count": 9, "id": "2703e6d9-490a-4a9c-97e6-712ae3a45ce4", "metadata": {}, "outputs": [{"data": {"text/plain": ["Country  Division         \n", "CZ       Fresh                 65.778513\n", "         GM                   221.077408\n", "         Grocery              175.770255\n", "         Prepacked Meat         4.093930\n", "         Prepacked Poultry      1.640588\n", "         Produce              142.422839\n", "HU       Fresh                 54.156343\n", "         GM                   328.431836\n", "         Grocery              218.440432\n", "         Prepacked Meat         3.544973\n", "         Prepacked Poultry      1.379232\n", "         Produce              166.880955\n", "SK       Fresh                 43.926965\n", "         GM                   197.754403\n", "         Grocery              264.485110\n", "         Prepacked Meat         2.679193\n", "         Prepacked Poultry      1.477215\n", "         Produce              118.090804\n", "Name: hours, dtype: float64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["a[a.Activity_Group== 'Pre-sort'].groupby([\"Country\", 'Division'])['hours'].sum()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}