<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CE Backstock Logic Visualization - For an Example Product</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f8fa;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 10px;
        }
        .step-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 25px;
        }
        .step-header {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.2em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .step-number {
            background-color: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 16px;
        }
        .formula {
            background-color: #f0f7fc;
            padding: 10px;
            border-left: 3px solid #3498db;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .example {
            background-color: #e8f4fc;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .example-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2980b9;
        }
        .result-container {
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        .result-box {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 10px;
            min-width: 220px;
            flex: 1;
        }
        .shelf-box {
            border-top: 5px solid #2ecc71;
        }
        .backstock-box {
            border-top: 5px solid #e74c3c;
        }
        .value-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px auto;
            font-weight: bold;
        }
        .shelf-circle {
            background-color: #e8f8f5;
            color: #27ae60;
            border: 2px solid #2ecc71;
        }
        .backstock-circle {
            background-color: #fdedec;
            color: #c0392b;
            border: 2px solid #e74c3c;
        }
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 0.9em;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .code-block {
            background-color: #f8f8f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            font-family: monospace;
            margin: 15px 0;
            overflow-x: auto;
            white-space: pre;
            line-height: 1.5;
            tab-size: 4;
            -moz-tab-size: 4;
        }
        .step-explanation {
            margin-bottom: 10px;
        }
        .arrow-down {
            text-align: center;
            margin: 15px 0;
            color: #7f8c8d;
            font-size: 20px;
        }
        .variable-definition {
            margin-bottom: 5px;
        }
        .variables-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        .variable-box {
            flex: 1;
            min-width: 200px;
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            border-left: 4px solid #3498db;
        }
        .variable-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .variable-description {
            font-size: 0.9em;
        }
        .formula-definition {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background-color: #f0f7fc;
            border-left: 3px solid #3498db;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>CE Backstock Logic Visualization - For an Example Product</h1>
    
    <div class="step-container">
        <div class="step-header">
            <div class="step-number">i</div>
            Key Variables
        </div>
        
        <div class="variables-container">
            <div class="variable-box">
                <div class="variable-name">stock</div>
                <div class="variable-description">Total number of units in inventory</div>
            </div>
            <div class="variable-box">
                <div class="variable-name">case_capacity</div>
                <div class="variable-description">Number of units that fit in a full case</div>
            </div>
            <div class="variable-box">
                <div class="variable-name">shelfCapacity</div>
                <div class="variable-description">Maximum number of units that can fit on shelf</div>
            </div>
        </div>
        
        <div class="example">
            <div class="example-title">Example Values:</div>
            <ul>
                <li>stock = 23 units</li>
                <li>case_capacity = 6 units per case</li>
                <li>shelfCapacity = 20 units</li>
            </ul>
        </div>
    </div>
    
    <div class="step-container">
        <div class="step-header">
            <div class="step-number">1</div>
            Break down stock into loose singles and case units
        </div>
        
        <div class="step-explanation">
            <p><strong>looseSingles:</strong> Individual units that don't make a full case</p>
            <div class="formula">looseSingles = stock % case_capacity (if stock > case_capacity)</div>
            
            <p><strong>inCaseSingles:</strong> Units that are in full cases</p>
            <div class="formula">inCaseSingles = stock - looseSingles</div>
        </div>
        
        <div class="code-block"># Python code
Drivers['looseSingles'] = np.where(Drivers['stock'] > Drivers['case_capacity'],
                                Drivers['stock'] % Drivers['case_capacity'],
                                0)
Drivers['inCaseSingles'] = Drivers['stock'] - Drivers['looseSingles']</div>
        
        <div class="example">
            <div class="example-title">Example Calculation:</div>
            <p>With stock = 23 and case_capacity = 6:</p>
            <ul>
                <li>looseSingles = 23 % 6 = 5 (remainder after filling cases)</li>
                <li>inCaseSingles = 23 - 5 = 18 (units in full cases, which is 3 complete cases)</li>
            </ul>
        </div>
    </div>
    
    <div class="arrow-down">↓</div>
    
    <div class="step-container">
        <div class="step-header">
            <div class="step-number">2</div>
            Calculate available space on shelf
        </div>
        
        <div class="step-explanation">
            <p><strong>spaceOnShelfSingles:</strong> How many more individual units can fit on the shelf</p>
            <div class="formula">Calculated based on shelf capacity and current stock situation
If negative (shelf already full), set to 0</div>
        </div>
        
        <div class="code-block"># Python code
Drivers['spaceOnShelfSingles'] = np.where(
    (Drivers['shelfCapacity'] > Drivers['looseSingles']) & (Drivers['stock'] > Drivers['shelfCapacity']),
    Drivers['shelfCapacity'] - Drivers['looseSingles'],
    Drivers['shelfCapacity'] - Drivers['stock'] - Drivers['looseSingles']
)

# Ensure not negative
Drivers['spaceOnShelfSingles'] = np.where(
    Drivers['spaceOnShelfSingles'] < 0, 
    0, 
    Drivers['spaceOnShelfSingles']
)</div>
        
        <div class="example">
            <div class="example-title">Example Calculation:</div>
            <p>With shelfCapacity = 20, stock = 23, and looseSingles = 5:</p>
            <ul>
                <li>Since shelf capacity (20) > loose singles (5) AND stock (23) > shelf capacity (20):
                    <br>spaceOnShelfSingles = 20 - 5 = 15 (space for 15 more units after placing loose singles)</li>
            </ul>
        </div>
    </div>
    
    <div class="arrow-down">↓</div>
    
    <div class="step-container">
        <div class="step-header">
            <div class="step-number">3</div>
            Calculate how many full cases can fit on shelf
        </div>
        
        <div class="step-explanation">
            <p><strong>caseUnitSpaceOnShelf:</strong> Shelf space that can hold complete cases</p>
            <div class="formula">caseUnitSpaceOnShelf = spaceOnShelfSingles - (spaceOnShelfSingles % case_capacity)</div>
            
            <p><strong>fullCaseSingleShelf:</strong> Units from full cases to put on shelf</p>
            <div class="formula">Based on available case space and inventory in cases</div>
        </div>
        
        <div class="code-block"># Python code
Drivers['caseUnitSpaceOnShelf'] = Drivers['spaceOnShelfSingles'] - (Drivers['spaceOnShelfSingles'] % Drivers['case_capacity'])

Drivers['fullCaseSingleShelf'] = np.where(
    (Drivers['caseUnitSpaceOnShelf'] >= Drivers['inCaseSingles']) & (Drivers['stock'] > Drivers['caseUnitSpaceOnShelf']),
    Drivers['caseUnitSpaceOnShelf'],
    Drivers['stock'] - Drivers['looseSingles']
)</div>
        
        <div class="example">
            <div class="example-title">Example Calculation:</div>
            <p>With spaceOnShelfSingles = 15, case_capacity = 6, inCaseSingles = 18:</p>
            <ul>
                <li>caseUnitSpaceOnShelf = 15 - (15 % 6) = 15 - 3 = 12
                    <br>(We can fit exactly 2 full cases, which is 12 units)</li>
                <li>Since we have more inCaseSingles (18) than caseUnitSpaceOnShelf (12):
                    <br>fullCaseSingleShelf = 12 (we'll put 12 units from cases on the shelf)</li>
            </ul>
        </div>
    </div>
    
    <div class="arrow-down">↓</div>
    
    <div class="step-container">
        <div class="step-header">
            <div class="step-number">4</div>
            Calculate total units on shelf and in backstock
        </div>
        
        <div class="step-explanation">
            <p><strong>totalSinglesShelf:</strong> Total units to be placed on shelf</p>
            <div class="formula">totalSinglesShelf = fullCaseSingleShelf + looseSingles (limited by shelfCapacity)</div>
            
            <p><strong>totalSinglesBackstock:</strong> Units that must go to backstock</p>
            <div class="formula">totalSinglesBackstock = stock - totalSinglesShelf</div>
            
            <p><strong>Backstock Cases:</strong> Number of cases in backstock</p>
            <div class="formula">Backstock Cases = totalSinglesBackstock / case_capacity</div>
        </div>
        
        <div class="code-block"># Python code
Drivers['totalSinglesShelf'] = np.where(
    (Drivers['fullCaseSingleShelf'] + Drivers['looseSingles']) > Drivers['shelfCapacity'],
    Drivers['shelfCapacity'],
    Drivers['fullCaseSingleShelf'] + Drivers['looseSingles']
)

Drivers['totalSinglesBackstock'] = np.where(
    Drivers['stock'] > Drivers['totalSinglesShelf'],
    Drivers['stock'] - Drivers['totalSinglesShelf'],
    0
)

Drivers['totalCasesShelf'] = Drivers['totalSinglesShelf'] / Drivers['case_capacity']
Drivers['Backstock Cases'] = (Drivers['totalSinglesBackstock'] / Drivers['case_capacity'])
Drivers['Backstock unit'] = Drivers['totalSinglesBackstock']</div>
        
        <div class="example">
            <div class="example-title">Example Calculation:</div>
            <p>With fullCaseSingleShelf = 12, looseSingles = 5, stock = 23, shelfCapacity = 20:</p>
            <ul>
                <li>totalSinglesShelf = 12 + 5 = 17 (total units on shelf)</li>
                <li>totalSinglesBackstock = 23 - 17 = 6 (units that need to go to backstock)</li>
                <li>totalCasesShelf = 17 / 6 = 2.83 (number of cases on shelf)</li>
                <li>Backstock Cases = 6 / 6 = 1 (one complete case in backstock)</li>
                <li>Backstock unit = 6 (total units in backstock)</li>
            </ul>
        </div>
    </div>

    <h2>Final Result</h2>
    
    <div class="result-container">
        <div class="result-box shelf-box">
            <h3 style="text-align: center; color: #27ae60;">On Shelf</h3>
            <div class="value-circle shelf-circle">17<br><span style="font-size: 0.8em;">units</span></div>
            <p style="text-align: center;"><strong>Details:</strong></p>
            <ul>
                <li>5 loose singles</li>
                <li>12 units from 2 full cases</li>
                <li>Total cases: 2.83</li>
            </ul>
        </div>
        
        <div class="result-box backstock-box">
            <h3 style="text-align: center; color: #c0392b;">In Backstock</h3>
            <div class="value-circle backstock-circle">6<br><span style="font-size: 0.8em;">units</span></div>
            <p style="text-align: center;"><strong>Details:</strong></p>
            <ul>
                <li>6 units total in backstock</li>
                <li>Equivalent to 1 full case</li>
            </ul>
        </div>
    </div>

    <div class="step-container" style="margin-top: 30px;">
        <div class="step-header">
            <div class="step-number">!</div>
            Key Takeaways
        </div>
        
        <ol>
            <li>First, identify loose units (not in complete cases)</li>
            <li>Try to put all loose units on the shelf first</li>
            <li>Calculate remaining shelf space in terms of complete cases</li>
            <li>Fill that space with as many complete cases as possible</li>
            <li>Any excess inventory goes to backstock</li>
            <li>Convert backstock units to case counts for warehouse handling</li>
        </ol>
    </div>
</body>
</html>