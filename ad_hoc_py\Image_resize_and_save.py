# -*- coding: utf-8 -*-
"""
Created on Wed Jan 25 10:14:58 2023

@author: phrubos
"""
from PIL import Image
import pandas as pd


directory = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\apps\Repl_Whatifs_App\icons"

image_home = "/home_focus.png"
image_hadoop_system = "/database_focus.png"
image_model_settings = "/check-square_focus.png"
image_folder = "/folder-plus_focus.png"
plus = "/check.png"

image_settings = "/settings.png"

image_charts = "/pie-chart_focus.png"

toggle_on = "/toggle-left.png"
toggle_off = "/toggle-right_disabled.png"


image_left = "/arrow-left-circle.png"
image_right = "/arrow-right-circle.png"


def resize_menu_image(directory, Image_name):
    image = Image.open(directory + Image_name)
    new_image = image.resize((50, 50))
    return new_image


inc = 1
for x in [image_left, image_right]:

    a = resize_menu_image(directory, x)
    a.save(directory + x)


image_select_act_output = "\data-recovery-mini.png"

a = resize_menu_image(directory, image_select_act_output)
a.save(directory + image_select_act_output)
