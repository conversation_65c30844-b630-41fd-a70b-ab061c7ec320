{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Getting Started with <PERSON><PERSON> on Notebooks\n", "\n", "!!! important \"Supported Python versions\"\n", "\n", "    Taipy requires **Python 3.8** or newer.\n", "\n", "Welcome to the **Getting Started** guide for <PERSON><PERSON>. This tour shows you how to create an entire application using \n", "the two components of Taipy:\n", "\n", "- **Graphical User Interface builder** (Taipy GUI): allows any Python developer to create a complex and interactive GUI.\n", "\n", "- **Scenario Management** (Taipy Core): implements a modern backend for any data-driven application based on your business case.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_00/imd_end_interface.png width=700>\n", "</div>\n", "\n", "\n", "You can use Taipy GUI without Taipy Core and vice-versa. However, as you will see, they are incredibly efficient \n", "when combined.\n", "\n", "Each step of the **\"Getting Started\"** will focus on basic concepts of *Taipy*. Note that every step is dependent on \n", "the code of the previous one. After completing the last step, you will have the skills to develop your own Tai<PERSON> \n", "application. \n", "\n", "## Before we begin\n", "\n", "Three packages have to be installed:\n", "\n", " 1. **Taipy** package, it requires Python 3.8 or newer;\n", "\n", " 2. **scikit-learn**: A Machine-Learning package that will be used in the Getting Started user code;\n", "\n", " 3. **statsmodels**: Another package for statistics also used in the user code.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 0, "source": ["# !pip install taipy\n", "# !pip install scikit-learn\n", "# !pip install statsmodels"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using Notebooks\n", "Some functions will be used in the Getting Started for Notebooks that are primarly used for Notebooks (`gui.stop()`, `gui.run()`, `gui.on_change`, `set_content()`)\n", "To have more explanation on these different functions, you can find the documentation related [here](https://docs.taipy.io/manuals/gui/notebooks/)\n", "**Warning**: Do not forget to stop your server when you are finished. You can do so by restarting your kernel.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code for this step [here](https://docs.taipy.io/getting_started/src/step_00.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 0: First web page\n", "\n", "To create your first Taipy web page, you only need one line of code. Create a `Gui` object with a String and run it. \n", "A client link will be displayed in the console. Enter it in a web browser to open your first Taipy web client!\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 1, "source": ["from taipy.gui import <PERSON><PERSON>, <PERSON><PERSON>\n", "\n", "# A dark mode is available in Taipy\n", "# However, we will use the light mode for the Getting Started\n", "\n", "# We can use G<PERSON>(\"# Getting Started with <PERSON><PERSON>\").run() directly\n", "# However, we need a <PERSON><PERSON> and Gui object to modify the content of the page\n", "# in the Notebook\n", "\n", "main_page = Markdown(\"# Getting Started with <PERSON><PERSON>\")\n", "gui = Gui(main_page)\n", "gui.run(dark_mode=False)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "If you want to run multiple servers at the same time, you can change the server port number (5000 by default) in the `.run()` method. For example, `Gui(...).run(port=xxxx)`.\n", "\n", "\n", "Note that you can style the text. <PERSON><PERSON> uses the Markdown syntax to style your text and more. Therefore, `#` creates \n", "a title, `##` makes a subtitle. Put your text in `*` for *italics* or in `**` to have it in **bold**.\n", "\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_00/result.png width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_01.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 1: Visual elements\n", "\n", "Many visual elements can be added to the basic code viewed in Step 0. This Step shows how to use visual elements \n", "like charts, sliders and tables and implement them in the GUI.\n", "\n", "## Importing the Dataset\n", "\n", "Suppose that you have a [*dataset.csv*](https://docs.taipy.io/getting_started/step_01/dataset.csv) file, using the *Pandas* library, you can retrieve this dataset \n", "with the following code:\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 2, "source": ["import pandas as pd\n", "\n", "def get_data(path_to_csv: str):\n", "    # pandas.read_csv() returns a pd.DataFrame\n", "    dataset = pd.read_csv(path_to_csv)\n", "    dataset[\"Date\"] = pd.to_datetime(dataset[\"Date\"])\n", "    return dataset\n", "\n", "# Read the dataframe\n", "path_to_csv = \"dataset.csv\"\n", "dataset = get_data(path_to_csv)\n", "\n", "...\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "_dataset_ is a *pd.DataFrame*, a basic *Pandas main* object representing, in this case, a realistic time series. \n", "It represents the historical number of articles sold for a given store on a 15-minute basis (we have the historical \n", "sales data for the year 2021). Being a real dataset, there will sometimes be missing information for specific days. \n", "The columns are:\n", "\n", "- Index: a unique identifier for each data point.\n", "\n", "- Date: the date of the data point. Each date are separated by 15 minutes.\n", "\n", "- Value: the number of articles sold per 15-minute timeframe.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_01/table.png width=700>\n", "</div>\n", "\n", "After creating your first web client with just one line of code and reading our dataset data with the code above, \n", "let's add some  visual elements to our initial page.\n", "\n", "## Visual elements\n", "\n", "Taipy GUI can be considered as an **augmented** Markdown; it adds the concept of \n", "**[Visual elements](https://docs.taipy.io/en/latest/manuals/gui/viselements/)** on top of all the Markdown syntax. A visual \n", "element is a Taipy graphical object displayed on the client. It can be a \n", "[slider](https://docs.taipy.io/en/latest/manuals/gui/viselements/slider/), a \n", "[chart](https://docs.taipy.io/en/latest/manuals/gui/viselements/chart/), a \n", "[table](https://docs.taipy.io/en/latest/manuals/gui/viselements/table/), an \n", "[input](https://docs.taipy.io/en/latest/manuals/gui/viselements/input/), a \n", "[menu](https://docs.taipy.io/en/latest/manuals/gui/viselements/menu/), etc. Check the list \n", "[here](https://docs.taipy.io/en/latest/manuals/gui/controls/).\n", "\n", "Every visual element follows a similar syntax:\n", "\n", "`<|{variable}|visual_element_name|param_1=param_1|param_2=param_2| ... |>`.\n", "\n", "For example, a [slider](https://docs.taipy.io/en/latest/manuals/gui/viselements/slider/) is written this way :\n", "\n", "`<|{variable}|slider|min=min_value|max=max_value|>`.\n", "\n", "For each visual element you wish to add to your web page, you must include the syntax above inside your markdown \n", "string (representing your page). For example, at the beginning of the page, let's display:\n", "\n", "- a Python variable *n_week*;\n", "\n", "- a slider that will \"visually\" modify the value of __n_week__.\n", "\n", "Here is the overall syntax:\n", "\n", "```\n", "*<|{n_week}|>*\n", "<|{n_week}|slider|min=1|max=52|>\n", "```\n", "\n", "We will then create a chart and a table:\n", "\n", "```\n", "<|{dataset}|chart|type=bar|x=Date|y=Value|height=100%|>\n", "<|{dataset}|table|height=400px|width=95%|>\n", "```\n", "\n", "Here is the combined code:\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 3, "source": ["...\n", "\n", "from taipy import Gui\n", "\n", "dataset = get_data(path_to_csv)\n", "\n", "# Initial value\n", "n_week = 10\n", "\n", "# Definition of the page\n", "page = \"\"\"\n", "# Getting started with <PERSON><PERSON>\n", "\n", "Week number: *<|{n_week}|>*\n", "\n", "Interact with this slider to change the week number:\n", "<|{n_week}|slider|min=1|max=52|>\n", "\n", "## Dataset:\n", "\n", "Display the last three months of data:\n", "<|{dataset[9000:]}|chart|type=bar|x=Date|y=Value|height=100%|>\n", "\n", "<|{dataset}|table|height=400px|width=95%|>\n", "\"\"\"\n", "\n", "# Create a Gui object with our page content\n", "gui.stop()\n", "main_page.set_content(page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_01/result.gif width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_02.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 2: Interactive GUI\n", "\n", "Now, the page has several visual elements:\n", "\n", "- A slider that is connected to the Python variable *n_week* ;\n", "\n", "- A chart and a table controls that represent the DataFrame content.\n", "\n", "Taipy GUI manages everything. To go further into Taipy GUI, let's consider the concept of **state**.\n", "\n", "## Multi-client - state\n", "\n", "Try to open a few clients with the same URL. You will see that every client is independent from each other; you can change *n_week* on a client, and *n_week* will not change in other clients. This is due to the concept of **state**.\n", "\n", "The state holds the value of all the variables that are used in the user interface, for one specific connection.\n", "\n", "For example, at the beginning, `state.n_week = 10`. When *n_week* is modified by the slider (through a given graphical client), this is, in fact, *state.n_week* that is modified, not *n_week* (the global Python variable). Therefore, if you open 2 different clients, *n_week* will have 2 state values (*state.n_week*), one for each client.\n", "\n", "In the code below, this concept will be used to connect a variable (*n_week*) to other variables:\n", "\n", "- We will create a chart that will only display one week of data corresponding to the selected week of the slider.\n", "\n", "- A connection has to be made between the slider's value  (*state.n_week*) and the chart data (*state.dataset_week*).\n", "\n", "## How to connect two variables - the *[on_change](https://docs.taipy.io/en/latest/manuals/gui/callbacks/)* function\n", "\n", "In *Taipy*, the `on_change()` function is a \"special\" function. **Taipy** will check if you created a function with this name and will use it. Whenever the state of a variable is modified, the *callback* function is called with three parameters:\n", "\n", "- state (the state object containing all the variables)\n", "\n", "- The name of the modified variable\n", "\n", "- Its value.\n", "\n", "Here, `on_change()` will be called whenever the slider's value (*state.n_week*) changes. Each time this happens, *state.dataset_week* will be updated according to the new value of the selected week. Then, <PERSON><PERSON> will propagate this change automatically to the associated chart.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 4, "source": ["# Select the week based on the slider value\n", "dataset_week = dataset[dataset[\"Date\"].dt.isocalendar().week == n_week]\n", "\n", "page = \"\"\"\n", "# Getting started with <PERSON><PERSON>\n", "\n", "Select week: *<|{n_week}|>*\n", "\n", "<|{n_week}|slider|min=1|max=52|>\n", "\n", "<|{dataset_week}|chart|type=bar|x=Date|y=Value|height=100%|width=100%|>\n", "\"\"\"\n", "\n", "# on_change is the function that is called when any variable is changed\n", "def on_change(state, var_name: str, var_value):\n", "    if var_name == \"n_week\":\n", "        # Update the dataset when the slider is moved\n", "        state.dataset_week = dataset[dataset[\"Date\"].dt.isocalendar().week == var_value]\n", "\n", "gui.stop()\n", "gui.on_change = on_change\n", "main_page.set_content(page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_02/result.gif width=700>\n", "</div>\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_03.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 3: Introducing <PERSON><PERSON>\n", "\n", "From Step 2, you now know the basics of Taipy GUI. Let's go for a moment over the Scenario Management aspect of Tai<PERSON>.\n", "\n", "Even if Taipy GUI can be used without Taipy Core (and vice-versa), there are a lot of reasons for using Taipy Core:\n", "\n", "- Taipy Core efficiently manages the execution of your functions/pipelines.\n", "\n", "- Taipy Core manages data sources and monitors KPIs.\n", "\n", "- Taipy Core provides an easy management of multiple pipelines and end-user scenarios which comes in handy in the \n", "  context of Machine Learning or Mathematical optimization.\n", "\n", "To apprehend the Scenario Management aspect of <PERSON><PERSON>, you need to understand four essential concepts.\n", "\n", "\n", "## Four fundamental [concepts](https://docs.taipy.io/en/latest/manuals/core/concepts/) in Taipy Core:\n", "\n", "- [**Data Nodes**](https://docs.taipy.io/en/latest/manuals/core/concepts/data-node/): are the translation of variables in \n", "  Taipy. Data Nodes don't contain the data itself but know how to retrieve it. They can refer to any kind of data: \n", "  any *Python* object (*string*, *int*, *list*, *dict*, *model*, *dataframe*, etc), a Pickle file, a CSV file, an \n", "  SQL database, etc. They know how to read and write data. You can even write your own custom Data Node if needed to \n", "  access a particular data format.\n", "\n", "- [**Tasks**](https://docs.taipy.io/en/latest/manuals/core/concepts/task/): are the translation of functions in Taipy.\n", "\n", "- [**Pipelines**](https://docs.taipy.io/en/latest/manuals/core/concepts/pipeline/): are a list of tasks executed with \n", "  intelligent scheduling created automatically by <PERSON><PERSON>. They usually represent a sequence of Tasks/functions \n", "  corresponding to different algorithms like a simple baseline Algorithm or a more sophisticated Machine-Learning \n", "  pipeline.\n", "\n", "- [**<PERSON><PERSON><PERSON><PERSON>**](https://docs.taipy.io/en/latest/manuals/core/concepts/scenario/): End-Users very often require modifying \n", "  various parameters to reflect different business situations. Taipy Scenarios will provide the framework to \n", "  \"play\"/\"execute\" pipelines under different conditions/variations (i.e. data/parameters modified by the end-user)\n", "\n", "\n", "Let's create a Machine Learning (ML) example to clarify these concepts.\n", "\n", "In a ML context, it is common to have numerous training and testing pipelines for different algorithms. For \n", "simplification, we will only configure a single baseline pipeline that will predict on a given **day** the values \n", "for the following days. In Taipy, you will describe (i.e. configure) your pipeline with three tasks:\n", "\n", "- Retrieval of the initial dataset,\n", "\n", "- Data Cleaning,\n", "\n", "- Predictions (for *number of predictions*) from **day** onwards. In our example, predictions represents the number \n", "  of items sold in a given store on a 15-min basis.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_03/baseline_pipeline.svg width=500>\n", "</div>\n", "\n", "This graph is created by configuring Data Nodes (variables) and tasks (functions). This configuration doesn't \n", "execute anything; it is just a configuration that enables <PERSON><PERSON> to map the Tasks and Data Nodes as a Directed \n", "Acyclic Graph (DAG).\n", "\n", "## Data Nodes configuration\n", "\n", "Data Nodes can point to:\n", "\n", "- any kind of *Python variables* by default: *int*, *string*, *dict*, *list*, *np.array*, *pd.DataFrame*, *models*, etc. \n", "\n", "- a CSV file, Pickle file or SQL database.\n", "\n", "During the configuration of the Data Nodes, the developer specifies the type or format of each Data Node. A *Python* \n", "variable is stored by default by a Pickle file.\n", "\n", "Some parameters for Data Node configuration:\n", "\n", "- **Storage type**: This is where the storage type is selected: CSV file, SQL database, Pickle file, etc. Here, the initial dataset is a CSV file so *storage_type=\"csv\"* for this Data Node. <PERSON><PERSON> knows how to \n", "  access it, thanks to the path. By default, the storage type is *pickle*.\n", "\n", "- **[Scope](https://docs.taipy.io/en/latest/manuals/core/concepts/scope/)**: You can find below three types of Scope in the \n", "  code: the Pipeline, the Scenario (by default) and the Global scope.\n", "\n", "    - *Global scope*: all Data Nodes are shared between every pipelines, scenarios and cycles. For example, the \n", "      initial dataset is shared between every pipelines and scenarios.\n", "\n", "    - *Scenario scope*: they are shared between all the pipelines of the scenario.\n", "\n", "    - *Pipeline scope*: Data Nodes don't have access to other Data Nodes from other pipelines. A 'predictions' Data \n", "      Node is created for each pipeline in the current example. So, adding pipelines/algorithms will store \n", "      predictions in different \"predictions\" Data Nodes.\n", "\n", "- **Cacheable**: This is a parameter used to increase the efficiency of the program. If the Data Node has already \n", "  been created and if its input/upstream data nodes haven’t changed since the last run (of the pipeline), then it is \n", "  not necessary to rerun the task that creates it.\n", "\n", "\n", "### Input Data Nodes configuration\n", "These are the input Data Nodes. They represent the variables in Taipy when a pipeline is executed. Still, first, we \n", "have to configure them to create the DAG.\n", "\n", "- *initial_dataset* is simply the initial CSV file. <PERSON><PERSON> needs some parameters to read this data: *path* and \n", "  *header*. The `scope` is global; each scenario or pipeline has the same initial dataset.\n", "\n", "- *day* is the beginning of the predictions. The default value is the 26th of July. It means the training data will \n", "  end before the 26th of July, and predictions will begin on this day.\n", "\n", "- *n_predictions* is the number of predictions you want to make while predicting. The default value is 40. A \n", "  prediction represents the number of items sold in a given store per 15-minute time slot.\n", "\n", "- *max_capacity* is the maximum value that can take a prediction; it is the ceiling of the projections. The default \n", "  value is 200. It means that, in our example, the maximum number of items sold per 15 minutes is 200.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 5, "source": ["import datetime as dt\n", "import pandas as pd\n", "\n", "from taipy import Config, Scope\n", "\n", "## Input Data Nodes\n", "initial_dataset_cfg = Config.configure_data_node(id=\"initial_dataset\",\n", "                                                 storage_type=\"csv\",\n", "                                                 path=path_to_csv,\n", "                                                 scope=Scope.GLOBAL)\n", "\n", "# We assume the current day is the 26th of July 2021.\n", "# This day can be changed to simulate multiple executions of scenarios on different days\n", "day_cfg = Config.configure_data_node(id=\"day\", default_data=dt.datetime(2021, 7, 26))\n", "\n", "n_predictions_cfg = Config.configure_data_node(id=\"n_predictions\", default_data=40)\n", "\n", "max_capacity_cfg = Config.configure_data_node(id=\"max_capacity\", default_data=200)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Remaining Data Nodes\n", "\n", "- *cleaned_dataset* is the dataset after cleaning (after the `clean_data()` function). _cacheable_ is set to True \n", "  with a `scope.GLOBAL`. It means if the initial dataset didn't change, <PERSON><PERSON> will not re-execute the `clean_data()` \n", "  task. In other words, after the creation of this data node through `clean_data()`, <PERSON><PERSON> knows that it is not \n", "  necessary to create it again.\n", "\n", "- *predictions* are the predictions of the model. In this pipeline, it will be the output of the `predict_baseline()` \n", "  function. Each pipeline will create its own *prediction* Data Node hence `scope=Scope.PIPELINE`.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 6, "source": ["## Remaining Data Nodes\n", "cleaned_dataset_cfg = Config.configure_data_node(id=\"cleaned_dataset\",\n", "                                             cacheable=True,\n", "                                             validity_period=dt.<PERSON><PERSON><PERSON>(days=1),\n", "                                             scope=Scope.GLOBAL) \n", "\n", "predictions_cfg = Config.configure_data_node(id=\"predictions\", scope=Scope.PIPELINE)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "## Functions\n", "\n", "Here’s the code of each of the two *Python* functions: `clean_data()` and `predict_baseline()`. Their goal is \n", "respectively to clean the data and to predict the data.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 7, "source": ["def clean_data(initial_dataset: pd.DataFrame):\n", "    print(\"     Cleaning data\")\n", "    # Convert the date column to datetime\n", "    initial_dataset[\"Date\"] = pd.to_datetime(initial_dataset[\"Date\"])\n", "    cleaned_dataset = initial_dataset.copy()\n", "    return cleaned_dataset\n", "\n", "\n", "def predict_baseline(cleaned_dataset: pd.DataFrame, n_predictions: int, day: dt.datetime, max_capacity: int):\n", "    print(\"     Predicting baseline\")\n", "    # Select the train data\n", "    train_dataset = cleaned_dataset[cleaned_dataset[\"Date\"] < day]\n", "    \n", "    predictions = train_dataset[\"Value\"][-n_predictions:].reset_index(drop=True)\n", "    predictions = predictions.apply(lambda x: min(x, max_capacity))\n", "    return predictions\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Tasks\n", "\n", "Tasks are the translation of functions in Taipy. These tasks combined with Data Nodes create your graph (DAG). \n", "Creating a task is simple; you need:\n", "\n", "- An id\n", "\n", "- A function\n", "\n", "- Inputs\n", "\n", "- Outputs\n", "\n", "### clean_data_task\n", "\n", "The first task that you want to create is your `clean_data()` task. It will take your initial dataset (input Data \n", "Node), clean it (calling the `clean_data()` function) and generate the cleaned dataset Data Node.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_03/clean_data.svg width=300>\n", "</div>\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 8, "source": ["clean_data_task_cfg = Config.configure_task(id=\"clean_data\",\n", "                                            function=clean_data,\n", "                                            input=initial_dataset_cfg,\n", "                                            output=cleaned_dataset_cfg)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### predict_baseline_task\n", "\n", "This task will take the cleaned dataset and predict it according to your parameters i.e. the three input Data Nodes: \n", "*Day*, *Number of predictions* and *Max Capacity*.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_03/predict_baseline.svg width=300>\n", "</div>\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 9, "source": ["predict_baseline_task_cfg = Config.configure_task(id=\"predict_baseline\",\n", "                                                  function=predict_baseline,\n", "                                                  input=[cleaned_dataset_cfg, n_predictions_cfg, day_cfg, max_capacity_cfg],\n", "                                                  output=predictions_cfg)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_04.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 4: Pipeline Management\n", "\n", "In Step 3, you have described your graph; let's implement it with <PERSON><PERSON>! \n", "\n", "## Pipeline configuration\n", "\n", "To configure your first pipeline, you need to list all the tasks you want to be done by the pipeline. This pipeline executes the cleaning (*clean_data_task*) and the predicting (*predict_baseline_task*). Note that the **task_configs** is a list, so you don't have to worry about the order of the tasks. <PERSON><PERSON> does that for you and optimizes its execution.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 10, "source": ["# Create the first pipeline configuration\n", "baseline_pipeline_cfg = Config.configure_pipeline(id=\"baseline\",\n", "                                                  task_configs=[clean_data_task_cfg, predict_baseline_task_cfg])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Pipeline creation and execution\n", "\n", "Then, create your pipeline from its configuration, submit it, and print the \"predictions\" Data Node results.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 11, "source": ["import taipy as tp\n", "\n", "# Create the pipeline\n", "baseline_pipeline = tp.create_pipeline(baseline_pipeline_cfg)\n", "# Submit the pipeline (Execution)\n", "tp.submit(baseline_pipeline)\n", "    \n", "# Read output data from the pipeline\n", "baseline_predictions = baseline_pipeline.predictions.read()\n", "print(\"Predictions of baseline algorithm\\n\", baseline_predictions)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "> Note that when creating the pipeline (`tp.create_pipeline()`), all associated Taipy objects of the pipeline (Data nodes, Tasks, etc) get automatically created (unless already present).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_05.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 5: GUI and Pipeline\n", "\n", "In Step 4, we created a first pipeline using only Taipy Core. Let's update the GUI to reflect the results of the \n", "pipeline.\n", "\n", "A \"Predict\" [button](https://docs.taipy.io/en/latest/manuals/gui/viselements/button/) is added to the page to create the \n", "pipeline and run it. When you press a button, <PERSON><PERSON> calls the function passed to the *on_action* property.\n", "\n", "`<|Text displayed on button|button|on_action=fct_name_called_when_pressed|>`\n", "   \n", "A [chart](https://docs.taipy.io/en/latest/manuals/gui/viselements/chart/) control can be found at the end of the markdown to \n", "visualize the predictions. The chart plots two traces: the historical values and the predicted values.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 12, "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "# Initialize the \"predictions\" dataset\n", "predictions_dataset = pd.DataFrame({\"Date\":[dt.datetime(2021, 6, 1)], \"Historical values\":[np.NaN], \"Predicted values\":[np.NaN]})\n", "\n", "# Add a button and a chart for our predictions\n", "pipeline_page = page + \"\"\"\n", "Press <|predict|button|on_action=predict|> to predict with default parameters (30 predictions) and June 1st as day.\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "`create_and_submit_pipeline()` creates and executes the pipeline after being called by `predict()`. \n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 13, "source": ["def predict(state):\n", "    print(\"'Predict' button clicked\")\n", "    pipeline = create_and_submit_pipeline()\n", "    update_predictions_dataset(state, pipeline)\n", "\n", "\n", "def create_and_submit_pipeline():\n", "    print(\"Execution of pipeline...\")\n", "    # Create the pipeline from the pipeline config\n", "    pipeline = tp.create_pipeline(baseline_pipeline_cfg)\n", "    # Submit the pipeline (Execution)\n", "    tp.submit(pipeline)\n", "    return pipeline\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "After the execution of the pipeline (`tp.submit()`), the data stored in *predictions* and *cleaned_data* Data \n", "Nodes become accessible. The `read()` method accesses the data in Data Nodes.\n", "\n", "The `create_predictions_dataset()` function below creates a final dataframe (that concatenates the predictions and \n", "the historical data together) containing three columns:\n", "\n", "- Date,\n", "\n", "- Historical values,\n", "\n", "- Predicted values.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 14, "source": ["def create_predictions_dataset(pipeline):\n", "    print(\"Creating predictions dataset...\")\n", "    # Read data from the pipeline\n", "    predictions = pipeline.predictions.read()\n", "    day = pipeline.day.read()\n", "    n_predictions = pipeline.n_predictions.read()\n", "    cleaned_data = pipeline.cleaned_dataset.read()\n", "    \n", "    # Set arbitrarily the time window for the chart as 5 times the number of predictions\n", "    window = 5 * n_predictions\n", "\n", "    # Create the historical dataset that will be displayed\n", "    new_length = len(cleaned_data[cleaned_data[\"Date\"] < day]) + n_predictions\n", "    temp_df = cleaned_data[:new_length]\n", "    temp_df = temp_df[-window:].reset_index(drop=True)\n", "    \n", "    # Create the series that will be used in the concat\n", "    historical_values = pd.Series(temp_df[\"Value\"], name=\"Historical values\")\n", "    predicted_values = pd.Series([np.NaN]*len(temp_df), name=\"Predicted values\")\n", "    predicted_values[-len(predictions):] = predictions\n", "    \n", "    # Create the predictions dataset\n", "    # Columns : [Date, Historical values, Predicted values]\n", "    return pd.concat([temp_df[\"Date\"], historical_values, predicted_values], axis=1)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "It is now really simple to get  the predictions dataset and display it in the \"Prediction chart\" created above.\n", "\n", "\n", "When you press the \"Predict\" button, this function below is called. It will update the predictions' dataset, and \n", "this change will propagate to the chart.\n", "\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 15, "source": ["def update_predictions_dataset(state, pipeline):\n", "    print(\"Updating predictions dataset...\")\n", "    state.predictions_dataset = create_predictions_dataset(pipeline)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This is what the structure of the code looks like for the GUI:\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_05/organisation.svg width=500>\n", "</div>\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 16, "source": ["gui.stop()\n", "main_page.set_content(pipeline_page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_05/result.png width=700>\n", "</div>\n", "\n", "> **Important Remark**: A better option would have been to have the `create_predictions_dataset()` modeled as a last **Task** inside the pipeline graph.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_06.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 6: Creation of Scenarios\n", "\n", "Now that you have seen how to create and run a single pipeline, let's configure a scenario. Remember, scenarios are \n", "required whenever the end-user wants to run variations of the pipelines and perform what-if analysis to simulate \n", "different business situations . Each scenario would represent a different solution to your problem. Here, \n", "*max_capacity*, *day* and *number of predictions* can influence the scenario.\n", "\n", "In this example, we will run two pipelines: our initial  pipeline (*baseline*) together with a new one (referred as \n", "\"*ml*\") that will implement a  different prediction function/model.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 17, "source": ["# For the sake of clarity, we have used an AutoRegressive model rather than a pure ML model such as:\n", "# Random Forest, Linear Regression, LSTM, etc   \n", "from statsmodels.tsa.ar_model import AutoReg\n", "\n", "# This is the function that will be used by the task\n", "def predict_ml(cleaned_dataset: pd.DataFrame, n_predictions: int, day: dt.datetime, max_capacity: int):\n", "    print(\"     Predicting with ML\")\n", "    # Select the train data\n", "    train_dataset = cleaned_dataset[cleaned_dataset[\"Date\"] < day]\n", "    \n", "    # Fit the AutoRegressive model\n", "    model = AutoReg(train_dataset[\"Value\"], lags=7).fit()\n", "    \n", "    # Get the n_predictions forecasts\n", "    predictions = model.forecast(n_predictions).reset_index(drop=True)\n", "    predictions = predictions.apply(lambda x: min(x, max_capacity))\n", "    return predictions\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "A **predict_ml** Task config will need to be created and associated with the newly created `predict_ml()` function.\n", "The **predict_ml** Task configuration is created using the same format as before with a function, inputs, and outputs.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_06/predict_ml.svg width=300>\n", "</div>\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 18, "source": ["# Create the task configuration of the predict_ml function.\n", "## We use the same input and ouput as the previous predict_baseline task but we change the funtion\n", "predict_ml_task_cfg = Config.configure_task(id=\"predict_ml\",\n", "                                            function=predict_ml,\n", "                                            input=[cleaned_dataset_cfg, n_predictions_cfg, day_cfg, max_capacity_cfg],\n", "                                            output=predictions_cfg)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "With this new task, the Machine Learning pipeline can finally be configured.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 19, "source": ["# Create the new ml pipeline that will clean and predict with the ml model\n", "ml_pipeline_cfg = Config.configure_pipeline(id=\"ml\", task_configs=[clean_data_task_cfg, predict_ml_task_cfg])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "To configure a scenario, you need to use `tp.configure_scenario` and the list of the related pipelines. You can \n", "easily add more pipelines/algorithms if you wished to.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 20, "source": ["# Configure our scenario which is our business problem.\n", "scenario_cfg = Config.configure_scenario(id=\"scenario\", pipeline_configs=[baseline_pipeline_cfg, ml_pipeline_cfg])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "The configuration is now complete. Now, you can create your scenario and execute it. When creating it, <PERSON><PERSON> will \n", "create your pipelines (and its associated Tasks), and when you submit the scenario, it will run them based on \n", "<PERSON><PERSON>’s built-in intelligent scheduling. <PERSON><PERSON> knows in which sequence the Tasks need to be performed.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 21, "source": ["# Create the scenario\n", "scenario = tp.create_scenario(scenario_cfg)\n", "# Execute it\n", "tp.submit(scenario)\n", "# Get the resulting scenario\n", "## Print the predictions of the two pipelines (baseline and ml)\n", "print(\"\\nBaseline predictions\\n\", scenario.baseline.predictions.read())\n", "print(\"\\nMachine Learning predictions\\n\", scenario.ml.predictions.read())   \n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_07.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 7: GUI and Scenario\n", "\n", "In Step 6, using Taipy Core, we implemented a scenario configuration and created our first scenario (based on that \n", "config) . In this step, we will implement a graphical interface that makes use of scenarios. \n", "\n", "- First, a scenario will be created and executed at the beginning.\n", "\n", "- Then, a Taipy GUI *selector* will be used to select one of the two pipelines associated with the scenario: the \n", "  *baseline* or the *ml* pipeline.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_07/selector.gif width=250>\n", "</div>\n", "\n", "A [selector](https://docs.taipy.io/en/latest/manuals/gui/viselements/selector/) only needs two properties: a value that gets \n", "dynamically updated through the selector and the list of values possible (aka \"lov\"). Here is the syntax for a selector:\n", "\n", "`<|{selected_value}|selector|lov={lov_selector}|>`.\n", "\n", "An \"Update chart\" button will update the chart according to the selected pipeline.\n", "\n", "These variables below are the parameters of the pipeline selector. The selected pipeline will be the first among \n", "\"baseline\" and \"ml\" when starting the client.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 22, "source": ["# Set the list of pipelines names\n", "# It will be used in a selector of pipelines\n", "pipeline_selector = [\"baseline\", \"ml\"]\n", "selected_pipeline = pipeline_selector[0]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This pipeline selector is added in the Markdown file just before the chart as well as the \"Update chart\" button.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 23, "source": ["scenario_page = page + \"\"\"\n", "Select the pipeline\n", "<|{selected_pipeline}|selector|lov={pipeline_selector}|> <|Update chart|button|on_action=update_chart|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "The code around the GUI has evolved. `create_scenario()` is creating a scenario and submitting it with the \n", "`submit_scenario()` function. `update_chart()` is updating the chart based upon the selected scenario and pipeline.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_07/organisation.svg width=500>\n", "</div>\n", "\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 24, "source": ["def create_scenario():\n", "    print(\"Creating scenario...\")\n", "    scenario = tp.create_scenario(scenario_cfg)\n", "    scenario = submit_scenario(scenario)\n", "    return scenario\n", "\n", "def submit_scenario(scenario):\n", "    print(\"Submitting scenario...\")\n", "    tp.submit(scenario)\n", "    return scenario\n", "\n", "def update_chart(state):\n", "    print(\"'Update chart' button clicked\")\n", "    # Select the right pipeline\n", "    pipeline = scenario.pipelines[state.selected_pipeline]\n", "\n", "    # Update the chart based on this pipeline\n", "    # It is the same function as created before in step_5\n", "    update_predictions_dataset(state, pipeline)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Before running the GUI, these two lines of code will erase the previous scenarios, pipelines, data nodes that you \n", "created in the previous steps to avoid any problem of compatibility.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 25, "source": ["# Delete all entities\n", "Config.configure_global_app(clean_entities_enabled=True)\n", "tp.clean_all_entities()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 26, "source": ["# Creation of our first scenario\n", "scenario = create_scenario()\n", "gui.stop()\n", "main_page.set_content(scenario_page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_07/result.gif width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_08.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 8: Modify Data Nodes content\n", "\n", "Now that the GUI has been created to handle one scenario, it would be interesting to change the \"initial\" variables \n", "to see their impact on the predictions. These variables are: the *number of predictions*, the *max capacity* and the \n", "*day*. How can we interact with them in real-time?\n", "\n", "It can easily be done using the `write()` function of Data Nodes.\n", "\n", "First, to link these variables to a visual element, they have to be initialized. \n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 27, "source": ["# Initial variables\n", "## Initial variables for the scenario   \n", "day = dt.datetime(2021, 7, 26)\n", "n_predictions = 40\n", "max_capacity = 200\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Second, we will add to the Markdown (before the chart), a visual element binding each of these variables. We will be \n", "using them to \"modify\" the scenario. See the documentation for these newly introduced visual elements here: \n", "[date](https://docs.taipy.io/en/latest/manuals/gui/viselements/date/) and \n", "[number](https://docs.taipy.io/en/latest/manuals/gui/viselements/number/). A \"Save button\" is also created to run the \n", "\"submit_scenario()\" function when pressed.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 28, "source": ["page_scenario_manager = page + \"\"\"\n", "# Change your scenario\n", "\n", "**Prediction date**\\n\\n <|{day}|date|not with_time|>\n", "\n", "**Max capacity**\\n\\n <|{max_capacity}|number|>\n", "\n", "**Number of predictions**\\n\\n<|{n_predictions}|number|>\n", "\n", "<|Save changes|button|on_action={submit_scenario}|>\n", "\n", "Select the pipeline\n", "<|{selected_pipeline}|selector|lov={pipeline_selector}|> <|Update chart|button|on_action={update_chart}|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "`create_scenario()` function is almost the same as before except for the need to track the *scenario_id* of the \n", "newly created scenario (using the Global variable *selected_scenario*).\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 29, "source": ["def create_scenario():\n", "    global selected_scenario\n", "\n", "    print(\"Creating scenario...\")\n", "    scenario = tp.create_scenario(scenario_cfg)\n", "  \n", "    selected_scenario = scenario.id\n", "  \n", "    tp.submit(scenario)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "The `submit_scenario()` function introduces two essential Taipy functions:\n", "\n", "- `tp.get(scenario_id)`: Taipy function used to get the scenario from its id.\n", "\n", "- `write(new_value)`: a Data Node function that changes the value stored in the Data Node. For example, \n", "  *scenario.max_capacity* is a Data Node whose value can be changed to 100 like this\n", "  `scenario.max_capacity.write(100)`.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 30, "source": ["def submit_scenario(state):\n", "    print(\"Submitting scenario...\")\n", "    # Get the selected scenario: in this current step a single scenario is created then modified here.\n", "    scenario = tp.get(selected_scenario)\n", "    \n", "    # Conversion to the right format\n", "    state_day = dt.datetime(state.day.year, state.day.month, state.day.day)\n", "\n", "    # Change the default parameters by writing in the datanodes\n", "    scenario.day.write(state_day)\n", "    scenario.n_predictions.write(int(state.n_predictions))\n", "    scenario.max_capacity.write(int(state.max_capacity))\n", "\n", "    # Execute the pipelines/code\n", "    tp.submit(scenario)\n", "    \n", "    # Update the chart when we change the scenario\n", "    update_chart(state)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "`update_chart()` uses a previous function (`update_predictions_dataset()`) to update the *predictions_dataset* \n", "with the correct pipeline.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 31, "source": ["def update_chart(state):\n", "    # Select the right scenario and pipeline\n", "    scenario = tp.get(selected_scenario)\n", "    pipeline = scenario.pipelines[state.selected_pipeline]\n", "    # Update the chart based on this pipeline\n", "    update_predictions_dataset(state, pipeline)\n", "\n", "\n", "global selected_scenario\n", "# Creation of a single scenario\n", "create_scenario()\n", "gui.stop()\n", "main_page.set_content(page_scenario_manager)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_08/result.gif width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_09.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 9: <PERSON><PERSON>\n", "\n", "Now that you know how to create a scenario, submit it and change it, you will create in this step a Taipy program \n", "able to manage multiple scenarios (and pipelines).\n", "\n", "## Dynamic selectors\n", "\n", "Let's manage multiple scenarios through a dynamic scenario selector. This selector will be updated whenever a new \n", "scenario is created. It will store the \"id\" of the scenarios and their names. For clarity, only their names do get \n", "displayed (in the selector).\n", "\n", "This code initializes the scenario selector with previously created scenarios. If there are no scenarios yet, the \n", "scenario selector will be empty.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 32, "source": ["# Get all the scenarios already created\n", "all_scenarios = tp.get_scenarios()\n", "\n", "# Delete the scenarios that don't have a name attribute\n", "# All the scenarios of the previous steps do not have an associated name so they will be deleted,\n", "# this will not be the case for those created by this step\n", "[tp.delete(scenario.id) for scenario in all_scenarios if scenario.name is None]\n", "\n", "# Initial variable for the scenario selector\n", "# The list of possible values (lov) for the scenario selector is a list of tuples (scenario_id, scenario_name),\n", "# but the selected_scenario is just used to retrieve the scenario id and what gets displayed is the name of the scenario.\n", "scenario_selector = [(scenario.id, scenario.name) for scenario in tp.get_scenarios()]\n", "selected_scenario = None\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Beside adding to the Markdown the new scenario selector, we also add a new \"Create new scenario\" button. This button \n", "calls the `create_scenario()` function. So, now each time we modify the parameters (*day*, *max_capacity*, \n", "*n_prediction*) we will create a new scenario upon clicking on this \"Create new scenario\" button.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 33, "source": ["scenario_manager_page = page + \"\"\"\n", "# Create your scenario\n", "\n", "**Prediction date**\\n\\n <|{day}|date|not with_time|>\n", "\n", "**Max capacity**\\n\\n <|{max_capacity}|number|>\n", "\n", "**Number of predictions**\\n\\n<|{n_predictions}|number|>\n", "\n", "<|Create new scenario|button|on_action=create_scenario|>\n", "\n", "## <PERSON><PERSON><PERSON> \n", "<|{selected_scenario}|selector|lov={scenario_selector}|dropdown|>\n", "\n", "## Display the pipeline\n", "<|{selected_pipeline}|selector|lov={pipeline_selector}|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Here is the main code for managing scenarios. As you can see, the architecture doesn't change from the previous code.\n", "Two functions have been altered: `_create_scenario()` and `submit_scenario()`. \n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 34, "source": ["def create_name_for_scenario(state)->str:\n", "    name = f\"<PERSON><PERSON><PERSON> ({state.day.strftime('%A, %d %b')}; {state.max_capacity}; {state.n_predictions})\"\n", "    \n", "    # Change the name if it is the same as some scenarios\n", "    if name in [s[1] for s in state.scenario_selector]:\n", "        name += f\" ({len(state.scenario_selector)})\"\n", "    return name\n", "\n", "\n", "def update_chart(state):\n", "    # Now, the selected_scenario comes from the state, it is interactive\n", "    scenario = tp.get(state.selected_scenario[0])\n", "    pipeline = scenario.pipelines[state.selected_pipeline]\n", "    update_predictions_dataset(state, pipeline)\n", "    \n", "\n", "# Change the create_scenario function in order to change the default parameters\n", "# and allow the creation of multiple scenarios\n", "def create_scenario(state):\n", "    print(\"Execution of scenario...\")\n", "    # Extra information for the scenario\n", "    creation_date = state.day\n", "    name = create_name_for_scenario(state)\n", "    # Create a scenario\n", "    scenario = tp.create_scenario(scenario_cfg,creation_date=creation_date, name=name)\n", "    \n", "    state.selected_scenario = (scenario.id, name)\n", "    # Submit the scenario that is currently selected\n", "    submit_scenario(state)\n", "\n", "\n", "def submit_scenario(state):\n", "    print(\"Submitting scenario...\")\n", "    # Get the currently selected scenario\n", "    scenario = tp.get(state.selected_scenario[0])\n", "    \n", "    # Conversion to the right format (change?)\n", "    day = dt.datetime(state.day.year, state.day.month, state.day.day) \n", "\n", "    # Change the default parameters by writing in the Data Nodes\n", "    scenario.day.write(day)\n", "    scenario.n_predictions.write(int(state.n_predictions))\n", "    scenario.max_capacity.write(int(state.max_capacity))\n", "    scenario.creation_date = state.day\n", "        \n", "\n", "    # Execute the scenario\n", "    tp.submit(scenario)\n", "    \n", "    # Update the scenario selector and the scenario that is currently selected\n", "    update_scenario_selector(state, scenario) # change list to scenario\n", "    \n", "    # Update the chart directly\n", "    update_chart(state) \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "The function below will update the scenario selector whenever the user creates a new scenario. It is called in the \n", "`submit_scenario` function.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 35, "source": ["def update_scenario_selector(state, scenario):\n", "    print(\"Updating scenario selector...\")\n", "    # Update the scenario selector\n", "    state.scenario_selector += [(scenario.id, scenario.name)]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This graph summarizes the code for the GUI.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_09/organisation.svg width=500>\n", "</div>\n", "\n", "\n", "## Automating the graph update - *on_change* function\n", "\n", "The `on_change` function can automatically change the graph when another pipeline or scenario is selected.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 36, "source": ["def on_change(state, var_name: str, var_value):\n", "    if var_name == \"n_week\":\n", "        # Update the dataset when the slider is moved\n", "        state.dataset_week = dataset[dataset[\"Date\"].dt.isocalendar().week == var_value]\n", "        \n", "    elif var_name == \"selected_pipeline\" or var_name == \"selected_scenario\":\n", "        # Update the chart when the scenario or the pipeline is changed\n", "        # Check if we can read the Data Node to update the chart\n", "        if tp.get(state.selected_scenario[0]).predictions.read() is not None:\n", "            update_chart(state)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Run the Gui.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 37, "source": ["gui.stop()\n", "gui.on_change = on_change\n", "main_page.set_content(scenario_manager_page)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_09/result.gif width=700>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_10.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 10: Embellish your App\n", "\n", "With just a few steps, you have created a full forecasting application which predicts across multiple days with different parameters. However, the page's layout is not yet optimal and it could be greatly improved. This will be done during this step. To get a more aesthetically pleasing page, three new useful controls will be used. These are:\n", "\n", "- [menu](https://docs.taipy.io/en/latest/manuals/gui/viselements/menu/): creates a menu on the left to navigate through the pages.\n", "\n", "`<|menu|label=Menu|lov={lov_pages}|on_action=on_menu|>`. For example, this code creates a menu with two pages:\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 38, "source": ["from taipy import Gui\n", "\n", "def on_menu():\n", "    print('Menu function called')\n", "\n", "gui.stop()\n", "main_page.set_content(\"<|menu|label=Menu|lov={['Data Visualization', 'Scenario Manager']}|on_action=on_menu|>\")\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/menu.png width=50>\n", "</div>\n", "\n", "\n", "\n", "- [part](https://docs.taipy.io/en/latest/manuals/gui/viselements/part/): creates a group of text/visual elements. A useful property of `part` is _render_. If set to False, it will not display the part. This allows the developer to dynamically display a group of visual elements or not.\n", "\n", "```\n", "<|part|render={bool_variable}|\n", "Text\n", "Or visual elements...\n", "|>\n", "```\n", "\n", "- [layout](https://docs.taipy.io/en/latest/manuals/gui/viselements/layout/): creates invisible columns where you can put your texts and visual elements. The _columns_ property indicates the width and number of columns. Here, we create three columns of the same width.\n", "\n", "```\n", "<|layout|columns=1 1 1|\n", "But<PERSON> in first column <|Press|button|>\n", "\n", "Second column\n", "\n", "Third column\n", "|>\n", "```\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/layout.png width=500>\n", "</div>\n", "\n", "\n", "One strategy to switch from one page to another is:\n", "\n", "1. To create a specific Markdown string for each page;\n", "\n", "2. Use the Menu control to switch from one page to another by controlling the page variable.\n", "\n", "This is how you can easily create multiple pages; there are many other ways to do so.\n", " \n", "First, let’s start by creating the 2 pages.\n", "\n", "The first page contains the original chart and slider defined in Step 2. Let’s use the same Markdown as the one defined in Step 2. It is named _page_ (and is also present in Step 9). \n", "\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 39, "source": ["# Our first page is the original page\n", "# (with the slider and the chart that displays a week of the historical data)\n", "page_data_visualization = page\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/data_visualization.png width=700>\n", "</div>\n", "\n", "\n", "Then let’s create our second page which contains the page corresponding to the creation of scenarios seen in Step 9.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 40, "source": ["# Second page: create scenarios and display results\n", "page_scenario_manager = \"\"\"\n", "# Create your scenario\n", "\n", "<|layout|columns=1 1 1 1|\n", "<|\n", "**Prediction date**\\n\\n <|{day}|date|not with_time|>\n", "|>\n", "\n", "<|\n", "**Max capacity**\\n\\n <|{max_capacity}|number|>\n", "|>\n", "\n", "<|\n", "**Number of predictions**\\n\\n<|{n_predictions}|number|>\n", "|>\n", "\n", "<|\n", "<br/>\\n <|Create new scenario|button|on_action=create_scenario|>\n", "|>\n", "|>\n", "\n", "<|part|render={len(scenario_selector) > 0}|\n", "<|layout|columns=1 1|\n", "<|\n", "## Scenario \\n <|{selected_scenario}|selector|lov={scenario_selector}|dropdown|>\n", "|>\n", "\n", "<|\n", "## Display the pipeline \\n <|{selected_pipeline}|selector|lov={pipeline_selector}|dropdown|>\n", "|>\n", "|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/scenario_manager.gif width=700>\n", "</div>\n", "\n", "\n", "The menu combines these two pages. When a page is selected in the menu control, `on_menu()` is called and updates the \n", "page.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 41, "source": ["# Create a menu with our pages\n", "multi_pages = \"\"\"\n", "<|menu|label=Menu|lov={[\"Data Visualization\", \"Scenario Manager\"]}|on_action=on_menu|>\n", "\n", "<|part|render={page==\"Data Visualization\"}|\"\"\" + page_data_visualization + \"\"\"|>\n", "<|part|render={page==\"Scenario Manager\"}|\"\"\" + page_scenario_manager + \"\"\"|>\n", "\"\"\"\n", "\n", "\n", "# The initial page is the \"Data Visualization\" page\n", "page = \"Data Visualization\"\n", "def on_menu(state, var_name: str, fct: str, var_value: list):\n", "    # Change the value of the state.page variable in order to render the correct page\n", "    state.page = var_value[\"args\"][0]\n", "\n", "\n", "gui.stop()\n", "main_page.set_content(multi_pages)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_10/multi_pages.png width=700>\n", "</div>\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_11.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 11: Introducing Cycles\n", "\n", "So far, we have talked about how having different scenarios helps us to oversee our assumptions about the future. \n", "For example, in business, it is critical to weigh different options in order to come up with an optimal solution. \n", "However, this decision making process isn’t just a one-time task, but rather a recurrent operation that happens over \n", "a time period. This is why we want to introduce [Cycles](https://docs.taipy.io/en/latest/manuals/core/concepts/cycle/).\n", "\n", "A cycle can be thought of as a place to store different and recurrent scenarios, within a time frame. In Taipy Core, \n", "each cycle will have a unique primary scenario, which represents the reference scenario for a time period.\n", "\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_11/cycle.svg width=300>\n", "</div>\n", "\n", "Typically, in a Machine Learning problem, a lot of scenarios are created daily to predict the next day. Among all \n", "those scenarios, there is only one primary scenario. In the step's example, scenarios are attached to a DAILY cycle. \n", "Using Cycles is useful because some specific <PERSON><PERSON>'s functions exist to navigate through these Cycles. <PERSON><PERSON> can get \n", "all the scenarios created in a day by providing the Cycle. You can also get every primary scenario ever made to \n", "quickly see their progress over time.\n", "\n", "Moreover, nothing is more straightforward than creating a Cycle. The frequency parameter in a scenario configuration \n", "will create the desired type of Cycle. In the code below, the scenario has a daily cycle. It will be attached to the \n", "correct period (day) when it is created.\n", "\n", "As you can see, a Cycle can be made very easily once you have the desired frequency. In this snippet of code, since \n", "we have specified `frequency=Frequency.DAILY`, the corresponding scenario will be automatically attached to the \n", "correct period (*day*) once it is created. \n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 42, "source": ["from taipy import Config, Frequency\n", "\n", "# Create scenarios each week and compare them\n", "scenario_daily_cfg = Config.configure_scenario(id=\"scenario\",\n", "                                           pipeline_configs=[baseline_pipeline_cfg, ml_pipeline_cfg],\n", "                                           frequency=Frequency.DAILY)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "To avoid any compatibility issue between scenarios with or without cycle, this code will erase the previous \n", "scenarios, pipelines, datanodes that you have maybe created in the previous steps.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 43, "source": ["# Delete all entities\n", "Config.configure_global_app(clean_entities_enabled=True)\n", "tp.clean_all_entities()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "To clarify this concept of primary scenario, the scenario selector will show a `*` before its name if the scenario \n", "is primary. This is why we update the following functions.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 44, "source": ["# Change the inital scenario selector to see which scenarios are primary\n", "scenario_selector = [(scenario.id, (\"*\" if scenario.is_primary else \"\") + scenario.name) for scenario in tp.get_scenarios()]\n", "\n", "# Redefine update_scenario_selector to add \"*\" in the display name when the scnario is primary\n", "def update_scenario_selector(state, scenario):\n", "    print(\"Updating scenario selector...\")\n", "    # Create the scenario name for the scenario selector\n", "    # This name changes dependind whether the scenario is primary or not\n", "    scenario_name = (\"*\" if scenario.is_primary else \"\") + scenario.name\n", "    print(scenario_name)\n", "    # Update the scenario selector\n", "    state.scenario_selector += [(scenario.id, scenario_name)]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "In `create_scenario()`, *scenario_daily_cfg* is now the configuration used to create the scenario. By creating it, \n", "you also create the dependent Cycle. For example, setting `creation_date` to 04/02/2021 makes a cycle related to \n", "this day. All scenarios that are created on this day belong to this Cycle with just one primary scenario. Changing \n", "`creation_date` again will create another cycle for a different day and so on.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 45, "source": ["# Change the create_scenario function to create a scenario with the selected frequency\n", "def create_scenario(state):\n", "    print(\"Execution of scenario...\")\n", "    # Extra information for scenario\n", "    creation_date = state.day\n", "    name = create_name_for_scenario(state)\n", "\n", "    # Create a scenario with the week cycle\n", "    scenario = tp.create_scenario(scenario_daily_cfg, creation_date=creation_date, name=name)\n", "\n", "    state.selected_scenario = (scenario.id, name)\n", "\n", "    # Change the scenario that is currently selected\n", "    submit_scenario(state)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Two buttons are added to the GUI (\"Make primary\" and \"Delete scenario\"). They call the `make_primary()` and \n", "`delete_scenario()` functions below.\n", "\n", "`make_primary()` changes the current primary scenario of the cycle thanks to `tp.set_primary(scenario)`. It is the \n", "Taipy function used to make a scenario primary.\n", "\n", "> Note that the previous primary scenario will not longer be primary. There is always just one primary scenario in a cycle. \n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 46, "source": ["selected_scenario_is_primary = None\n", "\n", "def make_primary(state):\n", "    print(\"Making the current scenario primary...\")\n", "    scenario = tp.get(state.selected_scenario[0])\n", "    # Take the current scenario primary\n", "    tp.set_primary(scenario)\n", "    \n", "    # Update the scenario selector accordingly\n", "    state.scenario_selector = [(scenario.id, (\"*\" if scenario.is_primary else \"\") + scenario.name)\n", "                               for scenario in tp.get_scenarios()]\n", "    state.selected_scenario_is_primary = True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This function is triggered by the \"Delete scenario\" button.\n", "\n", "> Note that a primary scenario cannot be deleted.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 47, "source": ["from taipy.gui import notify\n", "\n", "def remove_scenario_from_selector(state, scenario: list):\n", "    # Take all the scenarios in the selector that doesn't have the scenario.id\n", "    state.scenario_selector = [(s[0], s[1]) for s in state.scenario_selector if s[0] != scenario.id]\n", "    state.selected_scenario = state.scenario_selector[-1]\n", "\n", "def delete_scenario(state):\n", "    scenario = tp.get(state.selected_scenario[0])\n", "    \n", "    if scenario.is_primary:\n", "        # Notify the user that primary scenarios can not be deleted\n", "        notify(state, \"info\", \"Cannot delete the primary scenario\")\n", "    else:\n", "        # Delete the scenario and the related objects (datanodes, tasks, jobs,...)\n", "        tp.delete(scenario.id)\n", "        \n", "        # Update the scenario selector accordingly\n", "        remove_scenario_from_selector(state,scenario)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "As previously said, just two visual elements (\"Make primary\" and \"Delete scenario\" buttons) have been added to the \n", "page. This code is almost identical to the previous *page_scenario_manager*.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 48, "source": ["# Add a \"Delete scenario\" and a \"Make primary\" buttons\n", "page_scenario_manager = \"\"\"\n", "# Create your scenario:\n", "\n", "<|layout|columns=1 1 1 1|\n", "<|\n", "**Prediction date**\\n\\n <|{day}|date|not with_time|>\n", "|>\n", "\n", "<|\n", "**Max capacity**\\n\\n <|{max_capacity}|number|>\n", "|>\n", "\n", "<|\n", "**Number of predictions**\\n\\n<|{n_predictions}|number|>\n", "|>\n", "\n", "<|\n", "<br/>\n", "<br/>\n", "<|Create new scenario|button|on_action=create_scenario|>\n", "|>\n", "|>\n", "\n", "\n", "<|part|render={len(scenario_selector) > 0}|\n", "<|layout|columns=1 1|\n", "\n", "<|layout|columns=1 1|\n", "<|\n", "## Scenario \\n <|{selected_scenario}|selector|lov={scenario_selector}|dropdown|>\n", "|>\n", "\n", "<br/>\n", "<br/>\n", "<br/>\n", "<br/>\n", "<|Delete scenario|button|on_action=delete_scenario|active={len(scenario_selector)>0}|>\n", "<|Make primary|button|on_action=make_primary|active={not(selected_scenario_is_primary) and len(scenario_selector)>0}|>\n", "|>\n", "\n", "\n", "<|\n", "## Display the pipeline \\n <|{selected_pipeline}|selector|lov={pipeline_selector}|dropdown|>\n", "|>\n", "|>\n", "\n", "<|{predictions_dataset}|chart|x=Date|y[1]=Historical values|type[1]=bar|y[2]=Predicted values|type[2]=scatter|height=80%|width=100%|>\n", "|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 49, "source": ["# Redefine the multi_pages\n", "multi_pages = \"\"\"\n", "<|menu|label=Menu|lov={[\"Data Visualization\", \"Scenario Manager\"]}|on_action=on_menu|>\n", "\n", "<|part|render={page==\"Data Visualization\"}|\"\"\" + page_data_visualization + \"\"\"|>\n", "<|part|render={page==\"Scenario Manager\"}|\"\"\" + page_scenario_manager + \"\"\"|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "When the selected scenario is changed, <PERSON><PERSON> calls the `on_change` and will update `state.\n", "selected_scenario_is_primary` (set to `True` if the selected scenario is primary).\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 50, "source": ["def on_change(state, var_name: str, var_value):\n", "    if var_name == \"n_week\":\n", "        # Update the dataset when the slider is moved\n", "        state.dataset_week = dataset[dataset[\"Date\"].dt.isocalendar().week == var_value]\n", "        \n", "    elif var_name == \"selected_pipeline\" or var_name == \"selected_scenario\":\n", "        # Update selected_scenario_is_primary indicating if the current scenario is primary or not\n", "        state.selected_scenario_is_primary = tp.get(state.selected_scenario[0]).is_primary\n", "\n", "        # Check if we can read the Data Node to update the chart\n", "        if tp.get(state.selected_scenario[0]).predictions.read() is not None:\n", "            update_chart(state)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 51, "source": ["gui.stop()\n", "gui.on_change = on_change\n", "main_page.set_content(multi_pages)\n", "gui.run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_11/result.gif style=\"margin:auto;display:block;border:>\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> You can download the code of this step [here](https://docs.taipy.io/getting_started/src/step_12.py) or all the steps [here](https://github.com/Avaiga/taipy-getting-started/tree/develop/src).\n", "\n", "\n", "\n", "# Step 12: <PERSON><PERSON><PERSON>\n", "\n", "Cycles are helpful to keep track of KPI over time. The goal of this step is to compare the primary scenario of every \n", "cycle and its pipelines over time.\n", "\n", "To achieve this:\n", "\n", "- A new dataframe has to be initialized. It will store the metrics for the *baseline* and *ml* pipeline. \n", "\n", "- Then, a part will use a boolean to show or not the comparison.\n", "\n", "- Finally, a selector will change the displayed metrics of the graph.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 52, "source": ["# Initial dataset for comparison\n", "comparison_scenario = pd.DataFrame({\"Scenario Name\":[],\n", "                                    \"RMSE baseline\":[], \"MAE baseline\":[],\n", "                                    \"RMSE ML\":[], \"MAE ML\":[]})\n", "\n", "# Indicates if the comparison is done\n", "comparison_scenario_done = False\n", "\n", "# Selector for metrics\n", "metric_selector = [\"RMSE\", \"MAE\"]\n", "selected_metric = metric_selector[0]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "First, a function has to be created to compare the primary scenario of all the cycles. \n", "`tp.get_primary_scenarios()` is the useful function to use for this effect. `compare()` goes through all of these \n", "scenarios and pipelines and add the metrics in lists. In the end, *state.comparison_scenario* is updated and \n", "*comparison_scenario_done* set to `True`.\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 53, "source": ["from sklearn.metrics import mean_absolute_error, mean_squared_error\n", "\n", "\n", "def compute_metrics(historical_data, predicted_data):\n", "    rmse = mean_squared_error(historical_data, predicted_data)\n", "    mae = mean_absolute_error(historical_data, predicted_data)\n", "    return rmse, mae\n", "\n", "\n", "def compare(state):\n", "    print(\"Comparing...\")\n", "    # Initial lists for comparison\n", "    scenario_names = []\n", "    rmses_baseline = []\n", "    maes_baseline = []\n", "    rmses_ml = []\n", "    maes_ml = []\n", "    \n", "    # Go through all the primary scenarios\n", "    all_scenarios = tp.get_primary_scenarios()\n", "    all_scenarios_ordered = sorted(all_scenarios, key=lambda x: x.creation_date.timestamp())\n", "    \n", "    for scenario in all_scenarios_ordered:\n", "        print(f\"<PERSON><PERSON><PERSON> {scenario.name}\")\n", "        # Go through all the pipelines\n", "        for pipeline in scenario.pipelines.values():\n", "            print(f\"     Pipeline {pipeline.config_id}\")\n", "            # Get the predictions dataset with the historical data\n", "            only_prediction_dataset = create_predictions_dataset(pipeline)[-pipeline.n_predictions.read():]\n", "            \n", "            # Series to compute the metrics (true values and predicted values)\n", "            historical_values = only_prediction_dataset[\"Historical values\"]\n", "            predicted_values = only_prediction_dataset[\"Predicted values\"]\n", "            \n", "            # Compute the metrics for this pipeline and primary scenario\n", "            rmse, mae = compute_metrics(historical_values, predicted_values)\n", "            \n", "            # Add values to the appropriate lists\n", "            if \"baseline\" in pipeline.config_id:\n", "                rmses_baseline.append(rmse)\n", "                maes_baseline.append(mae)\n", "            elif \"ml\" in pipeline.config_id:\n", "                rmses_ml.append(rmse)\n", "                maes_ml.append(mae)\n", "\n", "        scenario_names.append(scenario.creation_date.strftime(\"%A %d %b\"))\n", "        \n", "    # Update comparison_scenario\n", "    state.comparison_scenario = pd.DataFrame({\"Scenario Name\":scenario_names,\n", "                                              \"RMSE baseline\":rmses_baseline,\n", "                                              \"MAE baseline\":maes_baseline,\n", "                                              \"RMSE ML\":rmses_ml,\n", "                                              \"MAE ML\":maes_ml})\n", "    \n", "    # When comparison_scenario_done will be set to True,\n", "    # the part with the graphs will be finally rendered\n", "    state.comparison_scenario_done = True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Let's create a page related to this comparison. As said before, this page will contain a graph to compare scenarios \n", "and pipelines; and a selector to choose the metric on which to compare. When pressed the button at the bottom of the \n", "page calls the `compare()` function. When finished, the _render_ property of the *part* will render the rest of the \n", "page. Also, a new <PERSON><PERSON>'s block is present in the Markdown: \n", "[expandable](https://docs.taipy.io/en/latest/manuals/gui/viselements/expandable/).\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 54, "source": ["# Performance page\n", "page_performance = \"\"\"\n", "<br/>\n", "\n", "<|part|render={comparison_scenario_done}|\n", "\n", "<|Table|expanded=False|expandable|\n", "<|{comparison_scenario}|table|width=100%|>\n", "|>\n", "\n", "<|{selected_metric}|selector|lov={metric_selector}|dropdown|>\n", "\n", "<|part|render={selected_metric==\"RMSE\"}|\n", "<|{comparison_scenario}|chart|type=bar|x=Scenario Name|y[1]=RMSE baseline|y[2]=RMSE ML|height=100%|width=100%|>\n", "|>\n", "\n", "<|part|render={selected_metric==\"MAE\"}|\n", "<|{comparison_scenario}|chart|type=bar|x=Scenario Name|y[1]=MAE baseline|y[2]=MAE ML|height=100%|width=100%|>\n", "|>\n", "\n", "|>\n", "\n", "\n", "<center>\n", "<|Compare primarys|button|on_action=compare|>\n", "</center>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=https://docs.taipy.io/getting_started/step_12/page_performance.gif width=700>\n", "</div>\n", "\n"]}, {"cell_type": "code", "metadata": {}, "outputs": [], "execution_count": 55, "source": ["# Add the page_performance section to the menu   \n", "multi_pages = \"\"\"\n", "<|menu|label=Menu|lov={[\"Data Visualization\", \"Scenario Manager\", \"Performance\"]}|on_action=on_menu|>\n", "\n", "<|part|render={page==\"Data Visualization\"}|\"\"\" + page_data_visualization + \"\"\"|>\n", "<|part|render={page==\"Scenario Manager\"}|\"\"\" + page_scenario_manager + \"\"\"|>\n", "<|part|render={page==\"Performance\"}|\"\"\" + page_performance + \"\"\"|>\n", "\n", "\"\"\"\n", "\n", "gui.stop()\n", "main_page.set_content(multi_pages)\n", "gui.run()\n"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}