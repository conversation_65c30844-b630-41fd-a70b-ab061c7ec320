import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import StaleElementReferenceException, TimeoutException
import time
import glob
import os
from datetime import datetime

def click_element_with_retry(driver, by, value, max_attempts=30, timeout=10):
    for attempt in range(max_attempts):
        try:
            element = WebDriverWait(driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            driver.execute_script("arguments[0].click();", element)
            return  # Success, exit the function
        except (StaleElementReferenceException, TimeoutException) as e:
            if attempt == max_attempts - 1:
                print(f"Failed to click element after {max_attempts} attempts.")
                raise e
            print(f"Attempt {attempt + 1} failed. Retrying...")
            time.sleep(1)  # Wait a bit before retrying

def download_wait(directory, timeout, nfiles=None):
    """
    Wait for downloads to finish with a specified timeout.
    """
    seconds = 0
    dl_wait = True
    while dl_wait and seconds < timeout:
        time.sleep(1)
        dl_wait = False
        files = os.listdir(directory)
        if nfiles and len(files) != nfiles:
            dl_wait = True

        for fname in files:
            if fname.endswith('.crdownload'):
                dl_wait = True

        seconds += 1
    return seconds

def OB_packaging_types_download(place_to_save):
    # print("\n###################")
    # print("Downloading from OwnBrand page is started...")
    # print("###################\n")
    
    # service = Service()
    # options = webdriver.ChromeOptions()
    # options.add_argument("--start-maximized")  # Maximize the browser window
    # driver = webdriver.Chrome(service=service, options=options)
    
    # try:
    #     # Navigate to the webpage
    #     driver.get('https://1p.tesco-europe.com/OBCEFE/')
        
    #     # Wait for the page to load and click the first button
    #     click_element_with_retry(driver, By.XPATH, "//button[@class='btn btn-primary btn-lg btn-block']")
        
    #     # Refresh the page and wait
    #     driver.refresh()
    #     time.sleep(2)
        
    #     # Click the menu button
    #     click_element_with_retry(driver, By.XPATH, "//a[@onclick=\"appl.openMenu(this,'obfilter');\"]")
        
    #     # Click the download button
    #     click_element_with_retry(driver, By.XPATH, "//button[@id=\"obfilter_downloadSelCrit\"]")
        
    #     # Wait for the download to complete
    #     download_wait(r'c:\Users\<USER>\Downloads', 30, nfiles=None)
        
    # except Exception as e:
    #     print(f"An error occurred: {e}")
    # finally:
    #     driver.quit()
    
    # print("\n###################")
    # print("Downloading from OwnBrand page is done!")
    # print("###################\n")
    
    # # Transform and process the file
    # print("\n###################")
    # print("Transform and process the file is started...")
    # print("###################\n")
    
    date_now = datetime.now().strftime("%d-%m-%Y")
    
    list_of_files = glob.glob(r'c:\Users\<USER>\Downloads\*.xlsx') 
    latest_file = max(list_of_files, key=os.path.getmtime)
    
    a = pd.read_excel(latest_file, skiprows=1)
    
    a = a[(a.SRP.notnull()) & (~a.SRP.isin(['No']))]
    
    a = a.melt(id_vars="SRP", value_vars=['TPNB CZ', 'TPNB HU', 'TPNB SK'], var_name='country', value_name='tpnb').drop_duplicates().assign(country=lambda x: x.country.str[-2:])
    
    a.rename(columns={'SRP':'opening_type'}, inplace=True)
    
    a = a[a.tpnb.notnull()]
    
    a.to_excel(place_to_save + f"\ownbrand_opening_type_{date_now}.xlsx", index=False)
    
    print("\n###################")
    print("The file is Ready and Saved!")
    print("###################\n")

