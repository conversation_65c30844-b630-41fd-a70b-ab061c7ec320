import pyodbc
import pandas as pd
import numpy as np



start = 20230101
end = 20231231


pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)    
    
with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:

    countries_adress = ["hu", "cz", "sk"]
    countries_to_select = ['"HU"', '"CZ"', '"SK"']

    print("POS csd Downloading has started")
    
    df = pd.DataFrame()

    for c1, c2 in zip(countries_adress, countries_to_select):

        sql = """
        
            SELECT
            a.part_col,
            b.cntr_code AS COUNTRY,
            a.site, a.pon, a.tin, a.ret, a.ean,
            
            b.dmat_div_code AS DIV_CODE, b.dmat_dep_code AS DEP_CODE,
            b.dmat_sec_code AS SEC_CODE, b.dmat_grp_code AS GRP_CODE,
            b.dmat_sgr_code AS SGR_CODE,
            b.slad_long_des AS product_name,
            a.amo/100 AS VALUE, a.qty AS QUANTITY
            
            FROM pos{c1}.t001csd a
            LEFT JOIN DM.dim_artgld_details b
            ON a.ean = lpad(b.slem_ean,14,0)

            WHERE a.part_col BETWEEN {start} AND {end}
            AND b.cntr_code = {c2}
            --AND a.ret = 1


        
        """.format(
            c1=c1, c2=c2, start=start, end=end
        )

        
        ret = pd.read_sql(sql, conn)
        df = pd.concat([df, ret])
        print(f"DONE with {c2}")
            


df_ce = df.copy()
# Initialize the 'limit' column with default value 0
df_ce['limit'] = 0

df_ce['id'] = df_ce['part_col'].astype(str) + df_ce['site'].astype(int).astype(str) + df_ce['pon'].astype(int).astype(str) + df_ce['tin'].astype(int).astype(str) + df_ce['ret'].astype(int).astype(str)

df_ce["QUANTITY"] = df_ce["QUANTITY"]*-1

# Create masks for each condition
mask_SK_4 = (df_ce['COUNTRY'] == 'SK') & (df_ce['ret'] == 4)
mask_HU_4 = (df_ce['COUNTRY'] == 'HU') & (df_ce['ret'] == 4)
mask_CZ_4 = (df_ce['COUNTRY'] == 'CZ') & (df_ce['ret'] == 4)
mask_PL_4 = (df_ce['COUNTRY'] == 'PL') & (df_ce['ret'] == 4)
mask_SK_2 = (df_ce['COUNTRY'] == 'SK') & (df_ce['ret'] == 2)
mask_HU_2 = (df_ce['COUNTRY'] == 'HU') & (df_ce['ret'] == 2)
mask_CZ_2 = (df_ce['COUNTRY'] == 'CZ') & (df_ce['ret'] == 2)
mask_PL_2 = (df_ce['COUNTRY'] == 'PL') & (df_ce['ret'] == 2)

# Assign values based on the masks
df_ce.loc[mask_SK_4, 'limit'] = -1
df_ce.loc[mask_HU_4, 'limit'] = -300
df_ce.loc[mask_CZ_4, 'limit'] = -30
df_ce.loc[mask_PL_4, 'limit'] = -5
df_ce.loc[mask_SK_2, 'limit'] = -100
df_ce.loc[mask_HU_2, 'limit'] = -10000
df_ce.loc[mask_CZ_2, 'limit'] = -1000
df_ce.loc[mask_PL_2, 'limit'] = -80

df_ce['above_under'] = np.where(df_ce['VALUE']<df_ce['limit'], "above", "under")

df_ce['a_u'] = np.where(df_ce['ret'].isin([4, 2, 5]), df_ce['above_under'], '-')



df_ce = df_ce.groupby(["COUNTRY", "site", "ret", "a_u"]).agg({"id":"nunique", "QUANTITY":"sum"}).reset_index().pivot_table(index=['COUNTRY', "site", "ret"], columns="a_u", values=["id", "QUANTITY"], fill_value=0, aggfunc="sum").reset_index()



