
import paramiko
import pandas as pd
import numpy as np
import time
import sys
import os
from pathlib import Path
sys.path.append(os.path.dirname(Path.cwd()))
import Replenishment_Model_Functions_24 as rmf
import pyodbc
import re
import getpass

from io import StringIO

@rmf.timeit
def ssh_to_dataframe(stores):
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh_client.connect(hostname='hdp0430.global.tesco.org', username='phrubos', password='BleachersTinymoves')
    
    
    ce = pd.DataFrame()
    # Set the size of each sublist
    chunk_size = 20
    
    # Use a list comprehension to create sublists
    sublists = tuple(tuple(stores[i:i + chunk_size]) for i in range(0, len(stores), chunk_size))
    
    for stores in sublists:
    
    
    
    
    
    
        sql_query= """
                            select 
                            cast(stores.dmst_store_code as INT) as store,
                            cal.dtdw_day_desc_en as day,
                            hier.pmg as pmg,
                            cast(mstr.slad_tpnb as INT) as tpnb,
                            sum(sunit.sltrg_tr_unit)/14 as sold_units_dotcom
                            from dw.sl_trg sunit 
                            join dm.dim_stores stores on stores.cntr_id = sunit.sltrg_cntr_id and stores.dmst_store_id = sunit.sltrg_dmst_id 
                            left join dm.dim_time_d cal on cal.dmtm_d_code = sunit.part_col
                            join dm.dim_artgld_details mstr on mstr.slad_dmat_id = sunit.sltrg_dmat_id
                            and mstr.cntr_id = sunit.sltrg_cntr_id 
                            join tesco_analysts.hierarchy_spm hier on mstr.dmat_div_code = lpad(hier.div_code,4,"0") 
                            and mstr.dmat_dep_code = lpad(hier.dep_code,4,"0") 
                            and mstr.dmat_sec_code = lpad(hier.sec_code,4,"0") 
                            and mstr.dmat_grp_code = lpad(hier.grp_code,4,"0") 
                            and mstr.dmat_sgr_code = lpad(hier.sgr_code,4,"0") 
                            where cal.dmtm_fw_code between 'f2023w14' and 'f2023w27'
                            and stores.dmst_store_code in {stores}
                            and sunit.sltrg_tr_unit > 0 
                            and substring(hier.pmg, 1, 3) in ('BWS', 'DAI','DRY','FRZ','HDL','HEA','PPD','PRO','SFB','SFM')
                            group by  stores.dmst_store_code, cal.dtdw_day_desc_en,  hier.pmg,  mstr.slad_tpnb
                            order by  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb, cal.dtdw_day_desc_en;
        
            """.format(stores = stores)
        
        
        
        
        # Beeline command with the SQL query
        beeline_command = f'/opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file ' \
                           f'-u "******************************************************;' \
                           f'transportMode=https?kyuubi.engine.share.level.subdomain=cep_user_dc2sweekly;' \
                           f'spark.hadoop.fs.s3a.assumed.role.arn=arn:aws:iam:::role/role-cep-resource-sch;' \
                           f'spark.yarn.queue=kyuubi" -e "{sql_query}"'
                           
                           
        print(beeline_command)
        
        
        std_in, std_out, std_err = ssh_client.exec_command(beeline_command)
        
        
        raw_data = str(std_out.read())
        data = "\n".join(raw_data.split("\\n")) 
        
        
        df = pd.read_table(StringIO(data), sep='|', skiprows=[0, 2], skipfooter=2, engine='python')
        
        df = df.loc[:, ~df.columns.str.contains('Unnamed')]
        
        df.columns = [col.strip() for col in df.columns]
        
        for x in df.columns:
        
            df[x] = df[x].str.strip()
            
        df = df[pd.to_numeric(df['store'], errors='coerce').notna()]
        df['sold_units_dotcom'] = df['sold_units_dotcom'].astype("float")
        df[['store', 'tpnb']] = df[['store', 'tpnb']].astype("int")
        
        ce = pd.concat([df, ce])


    ssh_client.close()
    
    return df


directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd()).parent.parent
excel_inputs_f = "inputs/Repl/Stores_Inputs_2024_Q1_new_wo_counters_h.xlsx"
stores = list(
    pd.read_excel(directory / excel_inputs_f, usecols=["Country", "Format", "Store"])["Store"]
    .unique()
)





df = ssh_to_dataframe(stores)




    


