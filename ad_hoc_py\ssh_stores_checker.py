import pandas as pd

def check_stores_in_csv(df, csv_content):
    # Get all stores from DataFrame that start with '4'
    df_stores = set(str(store) for store in df['store'] if str(store).startswith('4'))
    
    # Parse CSV content and get all store numbers
    csv_stores = set()
    for line in csv_content.strip().split('\n'):
        # Split by | and get the store numbers part (last element)
        store_numbers = line.split('|')[-1]
        # Split by comma and add to set
        csv_stores.update(store_numbers.split(','))
    
    # Remove empty strings if any
    csv_stores.discard('')
    
    # Find stores that are in DataFrame but not in CSV
    missing_stores = df_stores - csv_stores
    
    # Find stores that are in CSV but not in DataFrame
    extra_stores = csv_stores - df_stores
    
    return {
        'all_stores_present': len(missing_stores) == 0,
        'stores_in_df': len(df_stores),
        'stores_in_csv': len(csv_stores),
        'missing_stores': sorted(missing_stores),
        'extra_stores': sorted(extra_stores)
    }

# Example usage:
csv_content = """HU|1|41001,41002,41003,41004,41005,41006,41007,41008,41009,41010,41011,41012,41013,41014,41015,41016,41017,41018,41019,41020,41021,41022,41024,41025,41026,41027,41028,41029,41030
HU|2|41031,41033,41034,41036,41037,41038,41039,41040,41041,41042,41043,41044,41045,41046,41047,41049,41051,41052,41053,41058,41059,41060,41390,41400,41410,41420,41430,41440
HU|3|41450,41460,41470,41480,41490,41500,41510,41520,41530,41540,41550,41560,41570,41580,41590,41600,41610,41620,41630,41640,41650
HU|4|41740,41750,41760,41770,41780,41790,41800,41810,41820,41830,41840,41850,41860,41870,41880,41890,41900,41910,41920,41930,41940,41950,41960,41970,41980,41990
HU|5|43001,43002,43003,43004,44001,44003,44004,44005,44006,44012,44013,44014,44015,44016,44017,44018,44019,44020,44021,44022,44023,44024,44025,44026,44027,44028,44029,44030,44031
HU|6|44076,44077,44078,44079,44080,44081,44082,44083,44087,44089,44090,45002,45003,45004,45005,45006,45007,45009,45010,45011,45012,45015,45016,45017,45018,41660,41670,41680,41690,41700,41710,41720,41730
HU|7|44032,44033,44034,44035,44036,44037,44040,44042,44043,44044,44045,44046,44049,44050,44051,44052,44053,44054,44055,44056,44057,44058,44060,44061,44062,44063,44064,44067,44069,44070,44071,44092
HU|8|


"""

# To use this function:
    
l = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\Repl\Repl_Stores_Inputs_2025_Q1.xlsx")
l.columns = [x.lower() for x in l.columns]    
result = check_stores_in_csv(l, csv_content)
print(f"All stores present: {result['all_stores_present']}")
print(f"Number of stores in DataFrame starting with '1': {result['stores_in_df']}")
print(f"Number of stores in CSV: {result['stores_in_csv']}")
if result['missing_stores']:
    print("\nStores in DataFrame but missing from CSV:")
    print(result['missing_stores'])
if result['extra_stores']:
    print("\nStores in CSV but not in DataFrame:")
    print(result['extra_stores'])