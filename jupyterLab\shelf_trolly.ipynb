{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f4ed79ce-e204-4747-812e-a19e6470898d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "daf9c4d2-f768-47ad-831e-fd794ecf867d", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\OPB_Dep_23_Q1_vol2_new_wh_alu_pet_data.xlsx\", \"Drivers\")"]}, {"cell_type": "code", "execution_count": 16, "id": "440f4bf7-d09b-4c2c-a95c-3b63914c59ab", "metadata": {}, "outputs": [], "source": ["condition = [df['Store'].astype(str).str.match(\"^1\"), df['Store'].astype(str).str.match(\"^2\"), df['Store'].astype(str).str.match(\"^4\")]\n", "results = ['CZ','SK', \"HU\"]\n", "df['country'] = np.select(condition, results, 0)"]}, {"cell_type": "code", "execution_count": 32, "id": "82f596a2-6b41-493d-b5b0-8d75fef3165c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>Format</th>\n", "      <th>Division</th>\n", "      <th>Salami_Tag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CZ</td>\n", "      <td>1K</td>\n", "      <td>Fresh</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CZ</td>\n", "      <td>1K</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CZ</td>\n", "      <td>1K</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CZ</td>\n", "      <td>1K</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CZ</td>\n", "      <td>1K</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>CZ</td>\n", "      <td>Compact</td>\n", "      <td>Fresh</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>CZ</td>\n", "      <td>Compact</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>CZ</td>\n", "      <td>Compact</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>CZ</td>\n", "      <td>Compact</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>CZ</td>\n", "      <td>Compact</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>CZ</td>\n", "      <td>Express</td>\n", "      <td>Fresh</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>CZ</td>\n", "      <td>Express</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>CZ</td>\n", "      <td>Express</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>CZ</td>\n", "      <td>Express</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>CZ</td>\n", "      <td>Express</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>CZ</td>\n", "      <td>Hypermarket</td>\n", "      <td>Fresh</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>CZ</td>\n", "      <td>Hypermarket</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>CZ</td>\n", "      <td>Hypermarket</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>CZ</td>\n", "      <td>Hypermarket</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>CZ</td>\n", "      <td>Hypermarket</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>HU</td>\n", "      <td>1K</td>\n", "      <td>Fresh</td>\n", "      <td>744.648815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>HU</td>\n", "      <td>1K</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>HU</td>\n", "      <td>1K</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>HU</td>\n", "      <td>1K</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>HU</td>\n", "      <td>1K</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>HU</td>\n", "      <td>Compact</td>\n", "      <td>Fresh</td>\n", "      <td>2680.487343</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>HU</td>\n", "      <td>Compact</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>HU</td>\n", "      <td>Compact</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>HU</td>\n", "      <td>Compact</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>HU</td>\n", "      <td>Compact</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>HU</td>\n", "      <td>Express</td>\n", "      <td>Fresh</td>\n", "      <td>229.633576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>HU</td>\n", "      <td>Express</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>HU</td>\n", "      <td>Express</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>HU</td>\n", "      <td>Express</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>HU</td>\n", "      <td>Express</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>HU</td>\n", "      <td>Hypermarket</td>\n", "      <td>Fresh</td>\n", "      <td>8340.686135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>HU</td>\n", "      <td>Hypermarket</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>HU</td>\n", "      <td>Hypermarket</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>HU</td>\n", "      <td>Hypermarket</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>HU</td>\n", "      <td>Hypermarket</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>SK</td>\n", "      <td>1K</td>\n", "      <td>Fresh</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>SK</td>\n", "      <td>1K</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>SK</td>\n", "      <td>1K</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>SK</td>\n", "      <td>1K</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>SK</td>\n", "      <td>1K</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>SK</td>\n", "      <td>Compact</td>\n", "      <td>Fresh</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>SK</td>\n", "      <td>Compact</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>SK</td>\n", "      <td>Compact</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>SK</td>\n", "      <td>Compact</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>SK</td>\n", "      <td>Compact</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>SK</td>\n", "      <td>Express</td>\n", "      <td>Fresh</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>SK</td>\n", "      <td>Express</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>SK</td>\n", "      <td>Express</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>SK</td>\n", "      <td>Express</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>SK</td>\n", "      <td>Express</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>SK</td>\n", "      <td>Hypermarket</td>\n", "      <td>Fresh</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>SK</td>\n", "      <td>Hypermarket</td>\n", "      <td>GM</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>SK</td>\n", "      <td>Hypermarket</td>\n", "      <td>Grocery</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>SK</td>\n", "      <td>Hypermarket</td>\n", "      <td>Produce</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>SK</td>\n", "      <td>Hypermarket</td>\n", "      <td>Warehouse</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   country       Format   Division   Salami_Tag\n", "0       CZ           1K      Fresh     0.000000\n", "1       CZ           1K         GM     0.000000\n", "2       CZ           1K    Grocery     0.000000\n", "3       CZ           1K    Produce     0.000000\n", "4       CZ           1K  Warehouse     0.000000\n", "5       CZ      Compact      Fresh     0.000000\n", "6       CZ      Compact         GM     0.000000\n", "7       CZ      Compact    Grocery     0.000000\n", "8       CZ      Compact    Produce     0.000000\n", "9       CZ      Compact  Warehouse     0.000000\n", "10      CZ      Express      Fresh     0.000000\n", "11      CZ      Express         GM     0.000000\n", "12      CZ      Express    Grocery     0.000000\n", "13      CZ      Express    Produce     0.000000\n", "14      CZ      Express  Warehouse     0.000000\n", "15      CZ  Hypermarket      Fresh     0.000000\n", "16      CZ  Hypermarket         GM     0.000000\n", "17      CZ  Hypermarket    Grocery     0.000000\n", "18      CZ  Hypermarket    Produce     0.000000\n", "19      CZ  Hypermarket  Warehouse     0.000000\n", "20      HU           1K      Fresh   744.648815\n", "21      HU           1K         GM     0.000000\n", "22      HU           1K    Grocery     0.000000\n", "23      HU           1K    Produce     0.000000\n", "24      HU           1K  Warehouse     0.000000\n", "25      HU      Compact      Fresh  2680.487343\n", "26      HU      Compact         GM     0.000000\n", "27      HU      Compact    Grocery     0.000000\n", "28      HU      Compact    Produce     0.000000\n", "29      HU      Compact  Warehouse     0.000000\n", "30      HU      Express      Fresh   229.633576\n", "31      HU      Express         GM     0.000000\n", "32      HU      Express    Grocery     0.000000\n", "33      HU      Express    Produce     0.000000\n", "34      HU      Express  Warehouse     0.000000\n", "35      HU  Hypermarket      Fresh  8340.686135\n", "36      HU  Hypermarket         GM     0.000000\n", "37      HU  Hypermarket    Grocery     0.000000\n", "38      HU  Hypermarket    Produce     0.000000\n", "39      HU  Hypermarket  Warehouse     0.000000\n", "40      SK           1K      Fresh     0.000000\n", "41      SK           1K         GM     0.000000\n", "42      SK           1K    Grocery     0.000000\n", "43      SK           1K    Produce     0.000000\n", "44      SK           1K  Warehouse     0.000000\n", "45      SK      Compact      Fresh     0.000000\n", "46      SK      Compact         GM     0.000000\n", "47      SK      Compact    Grocery     0.000000\n", "48      SK      Compact    Produce     0.000000\n", "49      SK      Compact  Warehouse     0.000000\n", "50      SK      Express      Fresh     0.000000\n", "51      SK      Express         GM     0.000000\n", "52      SK      Express    Grocery     0.000000\n", "53      SK      Express    Produce     0.000000\n", "54      SK      Express  Warehouse     0.000000\n", "55      SK  Hypermarket      Fresh     0.000000\n", "56      SK  Hypermarket         GM     0.000000\n", "57      SK  Hypermarket    Grocery     0.000000\n", "58      SK  Hypermarket    Produce     0.000000\n", "59      SK  Hypermarket  Warehouse     0.000000"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby(['country', 'Format', 'Division'])['Salami_Tag'].sum().reset_index()"]}, {"cell_type": "code", "execution_count": 28, "id": "42947753-5425-4b88-a46f-bdf20f711772", "metadata": {}, "outputs": [], "source": ["df.groupby(['country', 'Format', 'Division']).mean().reset_index().query(\"Format.isin(['1K', 'Express'])\").melt(id_vars=['country', 'Format', 'Division'] ).pivot(index=['variable'], values='value', columns=['country', 'Format', 'Division']).to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_jan\\shelftrolley_1pager.xlsx\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}