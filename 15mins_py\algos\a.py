import sys
from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Slot, Signal
from b import MainWindow as ExistingMainWindow  # Importing your existing GUI class

class PySideMainWindow(ExistingMainWindow):
    closed = Signal()  # Define a custom signal

    def closeEvent(self, event):
        super().closeEvent(event)
        self.closed.emit()  # Emit the signal when the window is closed

class DashboardWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Dashboard")
        self.setGeometry(100, 100, 300, 100)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        self.layout = QVBoxLayout(self.central_widget)

        self.launch_button = QPushButton("Launch PySide GUI")
        self.layout.addWidget(self.launch_button)

        self.launch_button.clicked.connect(self.launch_pyside_gui)

        self.pyside_gui = None

    @Slot()
    def launch_pyside_gui(self):
        if not self.pyside_gui or not self.pyside_gui.isVisible():
            self.pyside_gui = PySideMainWindow()  # Creating an instance of your existing PySide GUI
            self.pyside_gui.closed.connect(self.enable_launch_button)  # Connect signal to slot
            self.pyside_gui.show()
            self.launch_button.setEnabled(False)  # Disable the launch button when the PySide GUI is open

    @Slot()
    def enable_launch_button(self):
        self.launch_button.setEnabled(True)  # Enable the launch button when the PySide GUI is closed

if __name__ == "__main__":
    app = QApplication(sys.argv)
    dashboard = DashboardWindow()
    dashboard.show()
    sys.exit(app.exec())
