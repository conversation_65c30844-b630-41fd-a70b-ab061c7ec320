{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tutorial for Taipy GUI using Notebooks\n", "\n", "*Supported Python versions* Taipy requires **Python 3.8** or newer.\n", "\n", "Welcome to the **Tutorial** for using Taipy frontend. This guideguide will demonstrate how to utilize Taipy to build an interactive web application.\n", "\n", "<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_07/result.png\" width=\"700\">\n", "</div>\n", "\n", "Taipy aims to simplify web application development:\n", "\n", "- Accelerates application building.\n", "\n", "- Streamlines management of variables and events.\n", "\n", "- Offers intuitive visualization using Markdown syntax.\n", "\n", "In each part of the **Tutorial** we'll emphasize the basic principles of *Taipy*. It's\n", "important to note that each step builds on the code from the previous one. By the end of the\n", "final step, you'll be equipped with the ability to create your own Taipy application.\n", "\n", "## Before we begin\n", "\n", "**Taipy** package requires Python 3.8 or newer;\n", "\n", "The first step is to install Taipy for your Python environment.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pip install taipy\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 1: First Web page\n", "\n", "You only need one line of code to create your first Taipy web page. Just create a `Gui` object with a string and run it.\n", "In the console, you'll find a client link. All you need to do is copy and paste it into your web browser to open your first Taipy page!\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2024-01-18 16:46:38][<PERSON><PERSON>][INFO] Running in 'single_client' mode in notebook environment\n"]}, {"ename": "ConnectionError", "evalue": "Port 5000 is already opened on 127.0.0.1. You have another server application running on the same port.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mConnectionError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_28060\\2630806196.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 4\u001b[1;33m \u001b[0mGui\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mpage\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;34m\"# Getting started with *Taipy*\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrun\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;31m# use_reloader=True\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\taipy\\gui\\gui.py\u001b[0m in \u001b[0;36mrun\u001b[1;34m(self, run_server, run_in_thread, async_mode, **kwargs)\u001b[0m\n\u001b[0;32m   2172\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget_flask_app\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   2173\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 2174\u001b[1;33m         return self._server.run(\n\u001b[0m\u001b[0;32m   2175\u001b[0m             \u001b[0mhost\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mapp_config\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m\"host\"\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   2176\u001b[0m             \u001b[0mport\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mapp_config\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m\"port\"\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\taipy\\gui\\server.py\u001b[0m in \u001b[0;36mrun\u001b[1;34m(self, host, port, debug, use_reloader, flask_log, run_in_thread, allow_unsafe_werkzeug, notebook_proxy)\u001b[0m\n\u001b[0;32m    262\u001b[0m             \u001b[1;31m# Start proxy if not already started\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    263\u001b[0m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_proxy\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mNotebookProxy\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mgui\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_gui\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mlistening_port\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mport\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 264\u001b[1;33m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_proxy\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrun\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    265\u001b[0m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_port\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_get_random_port\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    266\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0m_is_in_notebook\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mor\u001b[0m \u001b[0mrun_in_thread\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\taipy\\gui\\utils\\proxy.py\u001b[0m in \u001b[0;36mrun\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     98\u001b[0m         \u001b[0mport\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_listening_port\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     99\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0m_is_port_open\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mhost\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mport\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 100\u001b[1;33m             raise ConnectionError(\n\u001b[0m\u001b[0;32m    101\u001b[0m                 \u001b[1;34mf\"Port {port} is already opened on {host}. You have another server application running on the same port.\"\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    102\u001b[0m             )\n", "\u001b[1;31mConnectionError\u001b[0m: Port 5000 is already opened on 127.0.0.1. You have another server application running on the same port."]}, {"name": "stderr", "output_type": "stream", "text": ["UserWarning: libuv only supports millisecond timer resolution; all times less will be set to 1 ms\n"]}], "source": ["from taipy import Gui\n", "\n", "\n", "Gui(page=\"# Getting started with *Taipy*\").run() # use_reloader=True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By default, the page won't refresh on its own after you make a code modification.\n", "\n", "If you want to alter this behavior, you can set the *use_reloader* parameter of the `run()` method to True. This will cause the application to automatically reload when you make changes to a file in your application and save it. It's typically used in development mode.\n", "\n", "If you wish to run multiple servers concurrently, you can modify the server port number (5000 by default) in the `run()` method. For example, `Gui(...).run(port=xxxx)`. Other parameters to the `run()` method can be found [here](https://docs.taipy.io/en/release/3.0/manuals/gui/configuration.md#configuring-the-gui-instance).\n", "\n", "Keep in mind that you have the option to format your text. <PERSON><PERSON> uses different ways to create pages: [Markdown](https://docs.taipy.io/en/release/3.0/manuals/gui/pages/index.md#using-markdown), [HTML](https://docs.taipy.io/en/release/3.0/manuals/gui/pages/index.md#using-html) or [Python code](https://docs.taipy.io/en/release/3.0/manuals/gui/page_builder.md). Here is the Markdown syntax to style your text  and more. Therefore, `#` creates a title, `##` makes a subtitle. Put your text in `*` for *italics* or in `**` to have it in **bold**.\n", "\n", "<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_01/result.png\" width=\"700\">\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 2: Visual elements\n", "\n", "You can incorporate various visual elements into the basic code demonstrated in Step 1. In this step, we will illustrate how to utilize visual elements such as charts, sliders, tables, and more within the graphical interface.\n", "\n", "## Visual elements\n", "\n", "When using the Mardown syntax, <PERSON><PERSON> augments it with the concept of **[visual elements](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/index.md)**. A visual element is a Taipy graphical object displayed on the client. It can be a  [slider](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/slider.md), a  [chart](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/chart.md), a  [table](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/table.md), an  [input](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/input.md), a  [menu](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/menu.md), etc. Check the list  [here](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/controls.md).\n", "\n", "Every visual element follows a similar syntax:\n", "\n", "`<|{variable}|visual_element_name|param_1=param_1|param_2=param_2| ... |>`.\n", "\n", "For example, a [slider](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/slider.md) is written this way :\n", "\n", "`<|{variable}|slider|min=min_value|max=max_value|>`.\n", "\n", "To include each visual element you want in your web page, you should incorporate the syntax mentioned above within your markdown string, which represents your page. For example, at the beginning of the page, if you want to display:\n", "\n", "- a Python variable *text*\n", "\n", "- an input that will \"visually\" modify the value of __text__.\n", "\n", "Here is the overall syntax:\n", "\n", "```\n", "<|{text}|>\n", "<|{text}|input|>\n", "```\n", "\n", "Here is the combined code:\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2024-01-18 16:47:04][<PERSON><PERSON>][INFO] Running in 'single_client' mode in notebook environment\n"]}, {"ename": "ConnectionError", "evalue": "Port 5000 is already opened on 127.0.0.1. You have another server application running on the same port.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mConnectionError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_28060\\3399578162.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     11\u001b[0m \"\"\"\n\u001b[0;32m     12\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 13\u001b[1;33m \u001b[0mGui\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mpage\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrun\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\taipy\\gui\\gui.py\u001b[0m in \u001b[0;36mrun\u001b[1;34m(self, run_server, run_in_thread, async_mode, **kwargs)\u001b[0m\n\u001b[0;32m   2172\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget_flask_app\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   2173\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 2174\u001b[1;33m         return self._server.run(\n\u001b[0m\u001b[0;32m   2175\u001b[0m             \u001b[0mhost\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mapp_config\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m\"host\"\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   2176\u001b[0m             \u001b[0mport\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mapp_config\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m\"port\"\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\taipy\\gui\\server.py\u001b[0m in \u001b[0;36mrun\u001b[1;34m(self, host, port, debug, use_reloader, flask_log, run_in_thread, allow_unsafe_werkzeug, notebook_proxy)\u001b[0m\n\u001b[0;32m    262\u001b[0m             \u001b[1;31m# Start proxy if not already started\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    263\u001b[0m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_proxy\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mNotebookProxy\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mgui\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_gui\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mlistening_port\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mport\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 264\u001b[1;33m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_proxy\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrun\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    265\u001b[0m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_port\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_get_random_port\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    266\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0m_is_in_notebook\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mor\u001b[0m \u001b[0mrun_in_thread\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\taipy\\gui\\utils\\proxy.py\u001b[0m in \u001b[0;36mrun\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     98\u001b[0m         \u001b[0mport\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_listening_port\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     99\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0m_is_port_open\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mhost\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mport\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 100\u001b[1;33m             raise ConnectionError(\n\u001b[0m\u001b[0;32m    101\u001b[0m                 \u001b[1;34mf\"Port {port} is already opened on {host}. You have another server application running on the same port.\"\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    102\u001b[0m             )\n", "\u001b[1;31mConnectionError\u001b[0m: Port 5000 is already opened on 127.0.0.1. You have another server application running on the same port."]}], "source": ["from taipy.gui import Gui\n", "\n", "text = \"Original text\"\n", "\n", "page = \"\"\"\n", "# Getting started with Taipy GUI\n", "\n", "My text: <|{text}|>\n", "\n", "<|{text}|input|>\n", "\"\"\"\n", "\n", "<PERSON><PERSON>(page).run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_02/result.png\" width=\"700\">\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 3: Interaction\n", "\n", "Now, the page has several visual elements:\n", "\n", "- A text that is connected to the Python variable *text*;\n", "\n", "- An input that changes the value *text* automatically.\n", "\n", "Taipy GUI manages everything in the background.\n", "\n", "To go further with Taipy GUI, let's introduce the concept of **state**. Thanks to this state concept, <PERSON><PERSON> natively provides multi-user GUI apps.\n", "\n", "## Multi-user - state\n", "\n", "Try to open a few clients with the same URL. You will see that every client is independent of each other; you can change *text* on a client, and *text* will not change in other clients. This is due to the concept of **state**.\n", "\n", "The state holds the value of all the variables used in the user interface for one specific connection.\n", "\n", "For example, in the beginning, `state.text = 'Original text'`. When *text* is modified by the input (through a given graphical client), this is, in fact, *state.text* that is modified, not *text* (the global Python variable). Therefore, if you open two different clients, *text* will have two state values (*state.text*), one for each client.\n", "\n", "In the code below, this concept will be used to:\n", "\n", "- Notify the user when the button is pressed;\n", "\n", "- Reset the input when the text equals \"Reset\".\n", "\n", "## How to connect two variables - the [`on_change`](https://docs.taipy.io/en/release/3.0/manuals/gui/callbacks/) callback\n", "\n", "In *Taipy*, the `on_change()` function is a \"special\" function. **Taipy** will check if you created and will use a function with this name. Whenever the state of a variable is modified, the *callback* function is called with three parameters:\n", "\n", "- state (the state object containing all the variables);\n", "\n", "- The name of the modified variable;\n", "\n", "- The new value for this variable.\n", "\n", "Here, *on_change()* will be called whenever the text's value (*state.text*) changes. If a variable is changed in this function, <PERSON><PERSON> will propagate this change automatically to the associated visual elements.\n", "\n", "Other callbacks specific to visual elements exist. They are named `on_change` or `on_action`. For example, a button has an _on_action_ property. When the button is pressed, <PERSON><PERSON> will call the callback function referenced in the `on_action` property.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from taipy.gui import <PERSON><PERSON>, notify\n", "\n", "text = \"Original text\"\n", "\n", "# Definition of the page\n", "page = \"\"\"\n", "# Getting started with Taipy GUI\n", "\n", "My text: <|{text}|>\n", "\n", "<|{text}|input|>\n", "\n", "<|Run local|button|on_action=on_button_action|>\n", "\"\"\"\n", "\n", "def on_button_action(state):\n", "    notify(state, \"info\", f\"The text is: {state.text}\")\n", "    state.text = \"Button Pressed\"\n", "\n", "def on_change(state, var_name, var_value):\n", "    if var_name == \"text\" and var_value == \"Reset\":\n", "        state.text = \"\"\n", "        return\n", "\n", "\n", "<PERSON><PERSON>(page).run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_03/result.png\" width=\"700\">\n", "</div>\n", "\n", "[`notify()`](https://docs.taipy.io/en/release/3.0/manuals/reference/taipy.gui.notify) is a Taipy GUI function that creates a [notification](https://docs.taipy.io/en/release/3.0/manuals/gui/notifications) with some information. The user can pass multiple parameters, including the *state*, the *notification_type*, and the *message*.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 4: Charts\n", " \n", "Charts are an essential part of Taipy (and of any Web application!). A chart is just another visual element with many properties to customize it.\n", "\n", "Here is one of the simplest code to create a chart:\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list_to_display = [100/x for x in range(1, 100)]\n", "Gui(\"<|{list_to_display}|chart|>\").run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Different formats can be passed to a chart element: a list, a Numpy array, or a Pandas Dataframe.\n", "\n", "## Different useful properties\n", "\n", "Taipy charts are based on Plotly charts. More than any other visual element, charts have a lot of properties.\n", "\n", "Here are a few of the essential properties. You can also look at the [documentation](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/chart/) for more information.\n", " - x and y are used to define the axis of the chart. Note that even if data inside columns are dynamic, the name of columns to display in a chart are not.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = { \"x_col\": [0, 1, 2], \"y_col1\": [4, 1, 2] }\n", "Gui(\"<|{data}|chart|x=x_col|y=y_col1|>\").run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": [" - x and y can be indexed to add more traces to the chart:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = {\"x_col\": [0, 1, 2], \"y_col_1\": [4, 2, 1], \"y_col_2\":[3, 1, 2]}\n", "Gui(\"<|{data}|chart|x=x_col|y[1]=y_col_1|y[2]=y_col_2|>\").run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": [" - Taipy provides a lot of different options to customize graphs. _color_ is one of them:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = {\"x_col\": [0, 1, 2], \"y_col_1\": [4, 2, 1], \"y_col_2\": [3, 1, 2]}\n", "Gui(\"<|{data}|chart|x=x_col|y[1]=y_col_1|y[2]=y_col_2|color[1]=green|>\").run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Different types of charts\n", "\n", "Different types are available: maps, bar charts, pie charts, line charts, and 3D charts, ... To know how to use them quickly, types are listed [here](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/chart/). If compatible, two types like _scatter_, _line_, and _bar_ can also be used together on the same chart.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = {\"x_col\": [0, 1, 2], \"y_col_1\": [4, 1, 2], \"y_col_2\": [3, 1, 2]}\n", "Gui(\"<|{data}|chart|x=x_col|y[1]=y_col_1|y[2]=y_col_2|type[1]=bar|>\").run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code\n", "\n", "A chart is added to our code to visualize the score given by our NLP algorithm to different lines.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["page = \"\"\"\n", "... put the previous Markdown page here\n", "\n", "<|{dataframe}|table|>\n", "\n", "<|{dataframe}|chart|type=bar|x=Text|y[1]=Score Pos|y[2]=Score Neu|y[3]=Score Neg|y[4]=Overall|color[1]=green|color[2]=grey|color[3]=red|type[4]=line|>\n", "\"\"\"\n", "\n", "\n", "dataframe = pd.DataFrame({\"Text\":[\"Test\", \"Other\", \"Love\"],\n", "                          \"Score Pos\":[1, 1, 4],\n", "                          \"Score Neu\":[2, 3, 1],\n", "                          \"Score Neg\":[1, 2, 0],\n", "                          \"Overall\":[0, -1, 4]})\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quick tip to write visual elements\n", "\n", "To make coding easier, each visual element has a property called *properties* that you can directly set to a Python dictionary of properties. To recreate the graph shown above, you can to the following:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["property_chart = {\"type\": \"bar\",\n", "                  \"x\": \"Text\",\n", "                  \"y[1]\": \"Score Pos\",\n", "                  \"y[2]\": \"Score Neu\",\n", "                  \"y[3]\": \"Score Neg\",\n", "                  \"y[4]\": \"Overall\",\n", "                  \"color[1]\": \"green\",\n", "                  \"color[2]\": \"grey\",\n", "                  \"color[3]\": \"red\",\n", "                  \"type[4]\": \"line\"\n", "                 }\n", "\n", "page = \"\"\"\n", "...\n", "<|{dataframe}|chart|properties={property_chart}|>\n", "...\n", "\"\"\"\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_04/result.png\" width=\"700\">\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 5: Python expression in properties\n", "\n", "As shown before, parameters and variables in <PERSON><PERSON> are dynamic. The same applies for every type of object, even data frames. Therefore, you can perform operations on data frames, and Taipy GUI will show real-time results on the GUI. These changes occur through the `=` assignment like `state.xxx = yyy` (`state.text = \"Example\"`).\n", "\n", "Any expression containing `xxx` in the Markdown will propagate the changes and reload related elements. It can be  simple charts or tables, but it can also be an expression like this:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "## Positive\n", "<|{np.mean(dataframe[\"Score Pos\"])}|text|>\n", "\n", "## Neutral\n", "<|{np.mean(dataframe[\"Score Neu\"])}|text|>\n", "\n", "## Negative\n", "<|{np.mean(dataframe[\"Score Neg\"])}|text|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This kind of expression creates direct connections between visual elements without coding anything.\n", "\n", "## A use case for NLP - Part 1\n", "\n", "The code for NLP is provided here, although it's not directly related to Tai<PERSON>. It will come into play in Part 2 when we wrap a GUI around this NLP engine..\n", "\n", "Before executing this step, you should have `pip install torch` and `pip install transformers`. The model will be downloaded and utilized in this code snippet. Note that Torch is currently only accessible for Python versions between 3.8 and 3.10.\n", "\n", "If you encounter difficulties installing these packages, you can simply provide a dictionary of random numbers as the output for the `analyze_text(text)` function.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer\n", "from transformers import AutoModelForSequenceClassification\n", "from scipy.special import softmax\n", "\n", "\n", "MODEL = f\"cardiffnlp/twitter-roberta-base-sentiment\"\n", "tokenizer = AutoTokenizer.from_pretrained(MODEL)\n", "model = AutoModelForSequenceClassification.from_pretrained(MODEL)\n", "\n", "\n", "def analyze_text(text):\n", "    # Run for Roberta Model\n", "    encoded_text = tokenizer(text, return_tensors=\"pt\")\n", "    output = model(**encoded_text)\n", "    scores = output[0][0].detach().numpy()\n", "    scores = softmax(scores)\n", "    \n", "    return {\"Text\": text,\n", "            \"Score Pos\": scores[2],\n", "            \"Score Neu\": scores[1],\n", "            \"Score Neg\": scores[0],\n", "            \"Overall\": scores[2]-scores[0]}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## A use case for NLP - Part 2\n", "\n", "The code below uses this concept to create metrics on the data frame generated.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd \n", "from taipy.gui import <PERSON><PERSON>, notify\n", "\n", "text = \"Original text\"\n", "\n", "dataframe = pd.DataFrame({\"Text\": [\"\"],\n", "                          \"Score Pos\": [0.33],\n", "                          \"Score Neu\": [0.33],\n", "                          \"Score Neg\": [0.33],\n", "                          \"Overall\": [0]})\n", "\n", "\n", "def local_callback(state):\n", "    notify(state, \"Info\", f\"The text is: {state.text}\", True)\n", "    temp = state.dataframe.copy()\n", "    scores = analyze_text(state.text)\n", "    temp.loc[len(temp)] = scores\n", "    state.dataframe = temp\n", "    state.text = \"\"\n", "\n", "\n", "page = \"\"\"\n", "<|toggle|theme|>\n", "\n", "# Getting started with Taipy GUI\n", "\n", "My text: <|{text}|>\n", "\n", "Enter a word:\n", "<|{text}|input|>\n", "<|Analyze|button|on_action=local_callback|>\n", "\n", "## Positive\n", "<|{np.mean(dataframe[\"Score Pos\"])}|text|format=%.2f|>\n", "\n", "## Neutral\n", "<|{np.mean(dataframe[\"Score Neu\"])}|text|format=%.2f|>\n", "\n", "## Negative\n", "<|{np.mean(dataframe[\"Score Neg\"])}|text|format=%.2f|>\n", "\n", "<|{dataframe}|table|>\n", "\n", "<|{dataframe}|chart|type=bar|x=Text|y[1]=Score Pos|y[2]=Score Neu|y[3]=Score Neg|y[4]=Overall|color[1]=green|color[2]=grey|color[3]=red|type[4]=line|>\n", "\"\"\"\n", "\n", "<PERSON><PERSON>(page).run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_05/result.png\" width=\"700\">\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 6: Page layout\n", "\n", "You have successfully built a comprehensive forecasting application capable of making predictions for multiple days with various parameters in just a few steps. Nevertheless, there is room for substantial improvement in the page's layout. We'll introduce three new helpful controls to enhance the page's visual appeal. These controls are:\n", "\n", "- [part](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/part/): creates a group of text/visual elements. A useful property of `part` is *render*. If set to False, it will not display the part. This allows the developer to hide a group of visual elements dynamically.\n", "\n", "```\n", "<|part|render={bool_variable}|\n", "Text\n", "Or visual elements...\n", "|>\n", "```\n", "\n", "- [layout](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/layout/): creates invisible columns where you can put your texts and visual elements. The *columns* property indicates the width and number of columns. Here, we create three columns of the same width.\n", "\n", "```\n", "<|layout|columns=1 1 1|\n", "But<PERSON> in first column <|Press|button|>\n", "\n", "Second column\n", "\n", "Third column\n", "|>\n", "```\n", "\n", "<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_06/layout.png\" width=\"700\">\n", "</div>\n", "\n", "- [expandable](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/expandable/): creates a block that can expand or shrink.\n", "\n", "<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_06/expandable.png\" width=\"700\">\n", "</div>\n", "\n", "## Back to the code\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "page = \"\"\"\n", "<|toggle|theme|>\n", "\n", "# Getting started with Taipy GUI\n", "\n", "<|layout|columns=1 1|\n", "<|\n", "My text: <|{text}|>\n", "\n", "Enter a word:\n", "<|{text}|input|>\n", "<|Analyze|button|on_action=local_callback|>\n", "|>\n", "\n", "\n", "<|Table|expandable|\n", "<|{dataframe}|table|width=100%|>\n", "|>\n", "|>\n", "\n", "<|layout|columns=1 1 1|\n", "## Positive <|{np.mean(dataframe[\"Score Pos\"])}|text|format=%.2f|raw|>\n", "\n", "## Neutral <|{np.mean(dataframe[\"Score Neu\"])}|text|format=%.2f|raw|>\n", "\n", "## Negative <|{np.mean(dataframe[\"Score Neg\"])}|text|format=%.2f|raw|>\n", "|>\n", "\n", "<|{dataframe}|chart|type=bar|x=Text|y[1]=Score Pos|y[2]=Score Neu|y[3]=Score Neg|y[4]=Overall|color[1]=green|color[2]=grey|color[3]=red|type[4]=line|>\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_06/result.png\" width=\"700\">\n", "</div>\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 7: Multi-pages, navbars, and menus\n", "\n", "Taipy significantly simplifies the process of building a multi-page application. To create a multi-page application, you need to define a dictionary of pages. In this example, we will create three pages: a *root* page and two additional pages (page 1 & page 2). We will incorporate visual elements, such as a menu or navbar, on the root page to facilitate navigation between page 1 and page 2.\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from taipy import Gui\n", "\n", "# Add a navbar to switch from one page to the other\n", "root_md = \"\"\"\n", "<|navbar|>\n", "# Multi-page application\n", "\"\"\"\n", "page1_md = \"## This is page 1\"\n", "page2_md = \"## This is page 2\"\n", "\n", "pages = {\n", "    \"/\": root_md,\n", "    \"page1\": page1_md,\n", "    \"page2\": page2_md\n", "}\n", "Gui(pages=pages).run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Navigating between pages\n", "\n", "- [menu](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/menu/): creates a menu on the left to navigate through the pages.\n", "\n", "`<|menu|label=Menu|lov={lov_pages}|on_action=on_menu|>`. For example, this code creates a menu with two options:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from taipy.gui import <PERSON><PERSON>, navigate\n", "\n", "\n", "root_md=\"<|menu|label=Menu|lov={[('Page-1', 'Page 1'), ('Page-2', 'Page 2')]}|on_action=on_menu|>\"\n", "page1_md=\"## This is page 1\"\n", "page2_md=\"## This is page 2\"\n", "\n", "\n", "def on_menu(state, var_name, function_name, info):\n", "    page = info[\"args\"][0]\n", "    navigate(state, to=page)\n", "\n", "\n", "pages = {\n", "    \"/\": root_md,\n", "    \"Page-1\": page1_md,\n", "    \"Page-2\": page2_md\n", "}\n", "\n", "Gui(pages=pages).run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_07/menu.png\" width=\"500\">\n", "</div>\n", "\n", "- [navbar](https://docs.taipy.io/en/release/3.0/manuals/gui/viselements/navbar/): creates an element to navigate through the Taipy pages by default\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from taipy.gui import Gui\n", "\n", "\n", "root_md = \"<|navbar|>\"\n", "page1_md = \"## This is page 1\"\n", "page2_md = \"## This is page 2\"\n", "\n", "pages = {\n", "    \"/\": root_md,\n", "    \"Page-1\": page1_md,\n", "    \"Page-2\": page2_md\n", "}\n", "\n", "Gui(pages=pages).run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_07/navbar.png\" width=\"500\">\n", "</div>\n", "\n", "## Back to the code\n", "\n", "The Markdown created in our previous steps will be the first page (named _page_) of the application. \n", "\n", "<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_07/first_markdown.png\" width=\"700\">\n", "</div>\n", "\n", "Then, let’s create our second page, which contains a page to analyze an entire text.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Second page\n", "\n", "dataframe2 = dataframe.copy()\n", "path = \"\"\n", "treatment = 0\n", "\n", "page_file = \"\"\"\n", "<|{path}|file_selector|extensions=.txt|label=Upload .txt file|on_action=analyze_file|> <|{f\"Downloading {treatment}%...\"}|>\n", "\n", "\n", "<|Table|expandable|\n", "<|{dataframe2}|table|width=100%|>\n", "|>\n", "\n", "<|{dataframe2}|chart|type=bar|x=Text|y[1]=Score Pos|y[2]=Score Neu|y[3]=Score Neg|y[4]=Overall|color[1]=green|color[2]=grey|color[3]=red|type[4]=line|height=800px|>\n", "\"\"\"\n", "\n", "def analyze_file(state):\n", "    state.dataframe2 = dataframe2\n", "    state.treatment = 0\n", "    with open(state.path,\"r\", encoding=\"utf-8\") as f:\n", "        data = f.read()\n", "        # split lines and eliminates duplicates\n", "        file_list = list(dict.fromkeys(data.replace(\"\\n\", \" \").split(\".\")[:-1]))\n", "    \n", "    \n", "    for i in range(len(file_list)):\n", "        text = file_list[i]\n", "        state.treatment = int((i+1)*100/len(file_list))\n", "        temp = state.dataframe2.copy()\n", "        scores = analyze_text(text)\n", "        temp.loc[len(temp)] = scores\n", "        state.dataframe2 = temp\n", "        \n", "    state.path = None\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "This little code below assembles our previous page and this new page. The `navbar` in the root page is also visible on both pages allowing for easy switching between pagesx.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# One root page for common content\n", "# The two pages that were created\n", "pages = {\"/\":\"<|toggle|theme|>\\n<center>\\n<|navbar|>\\n</center>\",\n", "         \"line\":page,\n", "         \"text\":page_file}\n", "\n", "Gui(pages=pages).run()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div align=\"center\">\n", " <img src=\"https://docs.taipy.io/en/release/3.0/knowledge_base/tutorials/understanding_gui/step_07/result.png\" width=\"700\">\n", "</div>\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}