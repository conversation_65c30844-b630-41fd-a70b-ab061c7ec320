import pandas as pd
import numpy as np


pd.set_option("display.max_columns", None)


repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]
a = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\as_is_modelDataSet_updated_12-06")

b = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\Repl_Dataset_2023_JDAapr18")

for x in (a, b):
    for r in repl_types:
        x[r] = np.where(x[r] == 1, x.sold_units, 0)
        

df_    = pd.DataFrame()     
for x, y in zip((a, b), ('p4', 'p2')):
    
    x = x.groupby(['country','division', 'pmg', 'tpnb', 'product_name'], observed=True)["srp", "nsrp", "full_pallet", "mu", "split_pallet"].sum().reset_index()
    
    x['period'] = y
    
    df_ = pd.concat([df_, x])
    

need_cols = [x for x in df_.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]



df_sum = df_.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'division', 'pmg', 'tpnb', 'product_name','repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()

df_sum['diff'] =  df_sum['p4'] - df_sum['p2']

df_sum['total_sold_item'] = df_sum.groupby(['country', 'tpnb'], observed=True)['p2'].transform('sum')


df_sum.sort_values(by=[ 'total_sold_item',  'diff', 'product_name'], ascending=[False,  False, True], inplace=True)

df_sum = df_sum[~df_sum.division.isin(['Produce', 'GM'])]


df_sum['% total'] = df_sum['p2'] / df_sum.groupby(['country', 'tpnb'], observed=True)['p2'].transform('sum')

df_sum['% diff'] = df_sum['diff'] / df_sum.groupby(['country', 'tpnb'], observed=True)['p2'].transform('sum')



df_sum_pmg = df_sum.groupby(['country', 'division', 'pmg', 'repl_types'], observed=True).agg({'p2': 'sum', 'p4':'sum','diff':'sum', 'total_sold_item':'sum'}).reset_index()

df_sum_pmg['% total'] = df_sum_pmg['p2'] / df_sum_pmg.groupby(['country', 'pmg'], observed=True)['p2'].transform('sum')

df_sum_pmg['% diff'] = df_sum_pmg['diff'] / df_sum_pmg.groupby(['country', 'pmg'], observed=True)['p2'].transform('sum')



   