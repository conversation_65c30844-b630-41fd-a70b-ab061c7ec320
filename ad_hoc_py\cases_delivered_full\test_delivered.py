#!/usr/bin/env python3
"""
Test suite for delivered.py module

This test suite verifies that all the fixes applied to delivered.py work correctly.
It focuses on syntax validation and logical structure testing without requiring external dependencies.
"""

import unittest
import sys
import os
import tempfile
import json
from pathlib import Path
import re
import ast

# Test the syntax and structure of the delivered.py file without importing it
delivered_file_path = Path(__file__).parent / "delivered.py"


class TestDeliveredSyntax(unittest.TestCase):
    """Test cases for delivered.py syntax and structure"""

    def setUp(self):
        """Set up test fixtures"""
        self.delivered_content = delivered_file_path.read_text()

    def test_file_exists(self):
        """Test that the delivered.py file exists"""
        self.assertTrue(delivered_file_path.exists(), "delivered.py file should exist")

    def test_python_syntax_valid(self):
        """Test that the Python syntax is valid"""
        try:
            ast.parse(self.delivered_content)
            syntax_valid = True
        except SyntaxError as e:
            syntax_valid = False
            print(f"Syntax error: {e}")

        self.assertTrue(syntax_valid, "delivered.py should have valid Python syntax")

    def test_function_definitions(self):
        """Test that required functions are properly defined"""
        # Check for function definitions
        self.assertIn("def ssh_table_create(", self.delivered_content)
        self.assertIn("def run_cases_delivered_script(", self.delivered_content)

        # Check that run_cases_delivered_script is not indented inside ssh_table_create
        lines = self.delivered_content.split('\n')
        ssh_func_line = None
        run_func_line = None

        for i, line in enumerate(lines):
            if line.strip().startswith("def ssh_table_create("):
                ssh_func_line = i
            elif line.strip().startswith("def run_cases_delivered_script("):
                run_func_line = i

        self.assertIsNotNone(ssh_func_line, "ssh_table_create function should be defined")
        self.assertIsNotNone(run_func_line, "run_cases_delivered_script function should be defined")

        # Check indentation - run_cases_delivered_script should not be indented more than ssh_table_create
        if ssh_func_line and run_func_line:
            ssh_indent = len(lines[ssh_func_line]) - len(lines[ssh_func_line].lstrip())
            run_indent = len(lines[run_func_line]) - len(lines[run_func_line].lstrip())
            self.assertEqual(ssh_indent, run_indent,
                           "run_cases_delivered_script should not be nested inside ssh_table_create")

    def test_variable_definitions(self):
        """Test that required variables are defined"""
        # Check for variable definitions that were missing
        self.assertIn("saved_filename", self.delivered_content)
        self.assertIn("place_to_save", self.delivered_content)
        self.assertIn("directory", self.delivered_content)

        # Check that variables are defined before use
        saved_filename_def = self.delivered_content.find("saved_filename =")
        saved_filename_use = self.delivered_content.find("ssh_table_create(")

        self.assertGreater(saved_filename_def, -1, "saved_filename should be defined")
        self.assertLess(saved_filename_def, saved_filename_use,
                       "saved_filename should be defined before use")
    
    def test_error_handling_structure(self):
        """Test that error handling is properly structured"""
        # Check for try-except blocks
        self.assertIn("try:", self.delivered_content)
        self.assertIn("except", self.delivered_content)
        self.assertIn("finally:", self.delivered_content)

        # Check that bare except clauses are avoided
        bare_except_count = self.delivered_content.count("except:")
        self.assertEqual(bare_except_count, 0, "Should not use bare except clauses")

    def test_unused_variables_fixed(self):
        """Test that unused stdin variables are replaced with underscore"""
        # Check that stdin variables are replaced with underscore
        stdin_count = self.delivered_content.count("stdin,")
        underscore_count = self.delivered_content.count("_, stdout, stderr")

        # Should have more underscores than stdin (indicating fixes were applied)
        self.assertGreater(underscore_count, 0, "Should use underscore for unused variables")

    def test_main_execution_guard(self):
        """Test that main execution is properly guarded"""
        self.assertIn('if __name__ == "__main__":', self.delivered_content)

    def test_directory_creation_code(self):
        """Test that directory creation code is present"""
        self.assertIn("mkdir(parents=True, exist_ok=True)", self.delivered_content)
    
    def test_sql_date_pattern_replacement(self):
        """Test that SQL date patterns are correctly replaced"""
        test_sql = """
        SELECT * FROM test_table
        WHERE part_col BETWEEN 20240101 AND 20240131
        AND other_col BETWEEN 20240201 AND 20240228
        """

        # Test the regex patterns used in the function
        start_pattern = r"(?<=BETWEEN\s)(\d{8})"
        end_pattern = r"(?<=AND\s)(\d{8})"

        modified_content = re.sub(start_pattern, "20250630", test_sql)
        modified_content = re.sub(end_pattern, "20250630", modified_content)

        # Verify replacements
        self.assertIn("BETWEEN 20250630", modified_content)
        self.assertIn("AND 20250630", modified_content)
        self.assertNotIn("20240101", modified_content)
        self.assertNotIn("20240131", modified_content)

    def test_regex_patterns_in_code(self):
        """Test that the correct regex patterns are used in the code"""
        # Check that the updated regex patterns are present
        self.assertIn(r"(?<=BETWEEN\s)(\d{8})", self.delivered_content)
        self.assertIn(r"(?<=AND\s)(\d{8})", self.delivered_content)

        # Check that old incorrect patterns are not present
        self.assertNotIn(r"'f(\d{4}w\d{2})'", self.delivered_content)


class TestSQLCompatibility(unittest.TestCase):
    """Test SQL file compatibility with Python code"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.sql_file_path = Path(__file__).parent / "unit_cases_delivered_prod.sql"
    
    def test_sql_file_exists(self):
        """Test that the SQL file exists"""
        self.assertTrue(self.sql_file_path.exists(), 
                       f"SQL file not found at {self.sql_file_path}")
    
    def test_sql_date_format(self):
        """Test that SQL file uses the expected date format"""
        if self.sql_file_path.exists():
            sql_content = self.sql_file_path.read_text()
            
            # Check for the expected date pattern
            date_pattern = r"part_col BETWEEN \d{8} AND \d{8}"
            matches = re.findall(date_pattern, sql_content)
            
            self.assertGreater(len(matches), 0, 
                             "SQL file should contain date patterns in format 'part_col BETWEEN YYYYMMDD AND YYYYMMDD'")
    
    def test_sql_syntax_basic(self):
        """Test basic SQL syntax"""
        if self.sql_file_path.exists():
            sql_content = self.sql_file_path.read_text()
            
            # Basic syntax checks
            self.assertIn("CREATE TABLE", sql_content.upper())
            self.assertIn("SELECT", sql_content.upper())
            self.assertIn("FROM", sql_content.upper())


def run_tests():
    """Run all tests and return results"""
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestDeliveredSyntax))
    suite.addTests(loader.loadTestsFromTestCase(TestSQLCompatibility))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    return result.wasSuccessful()


if __name__ == "__main__":
    print("Running tests for delivered.py module...")
    print("=" * 60)
    
    success = run_tests()
    
    print("=" * 60)
    if success:
        print("✅ All tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
