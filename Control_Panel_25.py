import pandas as pd
import Replenishment_Model_25 as rm
import ClassesModel as cm
from pathlib import Path
import polars as pl
import warnings


warnings.filterwarnings("ignore")

pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)

# =============================================================================
# Select what to do
# =============================================================================

RUN_MODEL = True
RUN_SQL_opsdev_planogram_palletCAP_others = False

# =============================================================================
# Set data_paths
# =============================================================================

act_version_name = "next_year_v0" # Tagging_rule_HU_0225
new_version_name = "_" #wh_repaired_v2_box_opening, repaired_presort_in_1k_express

saved_filename = "2025w14-27_v01"  # it is important when you want to run SQL part too
directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd()).parent
repl_dataset_f = "inputs/Repl_Dataset_2025w14-27_v01" #v2_final : Repl_Dataset_25_nsrpChanged_newer 

# --- For creating Replenishment Dataset ---
dataset_combined = r"inputs\files_for_dataset\test_combined_df_with_srd_foil\combined_productivity_test_combined_df_with_srd_foil_hier"
items_sold_f = r"inputs\files_for_dataset\w22_w34_rGM\item_sold_w22_w34_rGM_hier"
items_sold_dotcom = r"inputs\files_for_dataset\w22_w34_rGM\item_sold_dotcom_w22_w34_rGM"
stock_f = r"inputs\files_for_dataset\w22_w34_rGM\stock_w22_w34_rGM"
ops_dev_f = r"inputs\files_for_dataset\w22_w34_rGM\opsdev_df_Dataset_25"
box_op_type_f = r"inputs\files_for_dataset\w22_w34_rGM\ownbrand_opening_type_28-10-2024.xlsx"
cases_f = r"inputs\files_for_dataset\w22_w34_rGM\cases_w22_w34_rGM"
pallet_capacity_f = r"inputs\files_for_dataset\w22_w34_rGM\pallet_capacity_CE_Dataset_25"
broken_cases_f = r"inputs\files_for_dataset\w22_w34_rGM\broken_cases_list_2023w14_27.csv.gz"
single_pick_f = r"inputs\files_for_dataset\w22_w34_rGM\Single_pick_list_2024.10.04.xlsx"
shelfCapacity = r"inputs\files_for_dataset\w22_w34_rGM\planogram_Dataset_25.csv.gz"
foil_f = r"inputs\files_for_dataset\w22_w34_rGM\Foil_df_Dataset_25"
presort_pal_del = r"inputs\files_for_dataset\PBS_PBL_presort\presort_pal_del_with4prodGroups_w14_27_benchmarks" #_with4prodGroups_w14_27

# --- For rest of the model calculating ---
excel_inputs_f = "inputs/Repl/Repl_Stores_Inputs_2025_Q1_v7_GBPratesUpdate (1)_Zamky&others.xlsx" #v2_final : Repl_Stores_Inputs_2025_Q1_v6_GM and others_3 remaining stores.xlsx
losses_f = "inputs/files_for_dataset/Dataset_25/losses__t_benchmark" #losses__t_benchmark   #
most_f = "inputs/MOST/Repl/MOST_Repl_25_Q2_tags_SK new rotation rules with promo postsort_final_Q3 refresh_.xlsb" #v2_final : MOST_Repl_25_Q1_wibi FINAL solution.xlsb

selected_tpn = "inputs/Repl/selected_tpns.xlsx"

act_model_outputs = f"outputs/model_outputs/{act_version_name}/OPB_DEP_{act_version_name}.xlsx"
act_model_insights = f"outputs/model_outputs/{act_version_name}/INSIGHT_{act_version_name}.parquet.gz"
act_model_cost_base = f"inputs/model_outputs/{act_version_name}/ReplCostBase_{act_version_name}"

# --- Warehouse Datasets
wh_prof_drivers = "inputs/WH/WH_profile_drivers_profiles25_Q2_Pozsonyi AGAIN_DRS distance correction_benchmark_Zamky&others.xlsx" #v2_final: 
wh_most = "inputs/MOST/WH/MOST_Repl_WH_2025_Q2_repair_mohu_elevator RSU (1)_Zamky&others.xlsb" #MOST_Repl_WH_2024_Q2_v1.xlsb
wh_cases_pmg = r"inputs\files_for_dataset\Dataset_25\wh_cases_pmgw14_w27_03_benchmark_stores" #_repairCLG
wh_pattisserie = r"inputs\files_for_dataset\Dataset_25\wh_pattisseriew14_w27_benchmark_stores"
wh_deliveries = r"inputs\files_for_dataset\Dataset_25\wh_deliveries_dataw14_w27_w_sold_unit_benchmark_stores"
night_truck_helper = "inputs/WH/night_truck_helper"

# =============================================================================
# Store Selector
# =============================================================================
stores = list(
    pl.read_excel(directory / excel_inputs_f, engine="calamine").filter(pl.col("Country").is_in([ 'CZ']))["Store"]
    .unique()
)

# stores = [ 41520] #, 11029,11122,14036,21005,21047,24133,41049,41970,43001



data_paths = cm.Data_Paths(
    directory,
    repl_dataset_f,
    items_sold_f,
    items_sold_dotcom,
    stock_f,
    cases_f,
    ops_dev_f,
    box_op_type_f,
    excel_inputs_f,
    pallet_capacity_f,
    losses_f,
    most_f,
    act_model_outputs,
    act_model_insights,
    selected_tpn,
    stores,
    wh_prof_drivers,
    wh_most,
    wh_cases_pmg,
    wh_pattisserie,
    wh_deliveries,
    broken_cases_f,
    act_model_cost_base,
    single_pick_f,
    foil_f,
    shelfCapacity,
    presort_pal_del,
    night_truck_helper,
    dataset_combined
)
#####################################

if RUN_MODEL == True:
    
# =============================================================================
#     Set model_true_false
# =============================================================================

    # For Repl_DataSet
    Run_Dataset_only = False
    DATASET_TPN_FUNC = False
    
    # Do you want to remove hours for HU News&Mags?
    news_HU = False
    # Do you want to remove hours because of shelfService on GM?
    shelfService_gm = False
    
    # Do you want to get how many cases needs to be replenished?
    cases_to_replenish_only = False

    # important to Dataset, run it only 1x, once dataset base is ready:
    # (it calculates randomly "broken_case")
    broken_case = False

    # to TPN level
    tpnb_store = False
    tpnb_country = False
    TPN_Cost_Base = False

    # Save
    OPB = False

    INSIGHT_DIFF = False
    INSIGHT_BUSINESS = False  # in the way we send it to business
    OPB_DIV_SAVE = False
    Cost_Base = False
    calc_sheet_insight = False

    # Charts
    Run_CE_Charts = False

    # only Warehouse part
    only_WH = False

    model_true_false = cm.ModelTrueFalse(
        DATASET_TPN_FUNC,
        None,
        None,
        tpnb_store,
        tpnb_country,
        OPB_DIV_SAVE,
        calc_sheet_insight,
        INSIGHT_DIFF,
        INSIGHT_BUSINESS,
        broken_case,
        Run_Dataset_only,
        Run_CE_Charts,
        Cost_Base,
        news_HU,
        shelfService_gm,
        cases_to_replenish_only,
        OPB
    )



    ####### ReplType Changer Part #############

    sheet_name = "country_tpnb"
    repl_type = "srp"
    opening_type = "none"
    volume_modifier = False
    case_cap_modifier = False
    shelf_capacity_modifier = False
    chunk_size = 200

    for_what_ifs = cm.What_Ifs_Inputs(
        sheet_name,
        repl_type,
        TPN_Cost_Base,
        opening_type,
        volume_modifier,
        case_cap_modifier,
        shelf_capacity_modifier,
        chunk_size,
        only_WH
    )

    #######################################

    summary, a = rm.Replenishment_Model_Running(
        model_true_false,
        act_version_name,
        new_version_name,
        data_paths,
        saved_filename,
        for_what_ifs,
    )
    try:
        calc_sheet = summary.repl_wh_hours
    except:
        pass

    if not (tpnb_country or tpnb_store):
        try:
            pd.set_option("display.max_rows", None)
            hrs_comp_print = summary.hrs_comparison[
                ["Country", "Division", "diff_hours", "diff_%", "diff_in_GBP"]
            ]
            hrs_comp_print.loc[:, "diff_in_GBP"] = hrs_comp_print["diff_in_GBP"].map(
                "£ {:,.0f}".format
            )
            print(
                "\nDifferences between the last and current version:\n\n",
                hrs_comp_print,
                "\n",
            )
            print(
                "\nDifferences between the Activity Groups:\n\n",
                summary.insight_diff_act_groups[
                    ["Model", "Country", "Activity_Group", "DIFF_IN_HOURS"]
                ]
                .pivot_table(
                    index=["Model", "Activity_Group"],
                    values="DIFF_IN_HOURS",
                    columns=["Country"],
                    aggfunc="sum",
                    fill_value=0
                )
                .reset_index()
                .to_string(index=False, justify="center"),
                "\n",
            )
            print(
                "\nTotal Weekly  Hours for CE:" " {:.1f}\n".format(summary.op_deptotal)
            )
            print(
                "\nTotal Weekly  Hours Insight:"
                " {:.1f}\n".format(summary.insight_diff_total)
            )
            print(
                "\nTotal Yearly GBP for CE:"
                " £ {:,.0f}\n".format(summary.op_deptotal_gbp)
            )
            print(
                f"\nTotal Executed Time: {summary.minCode} min {summary.secCode} sec\n"
            )
            # rmf.Plot_Activity_Groups_by_Divisions(summary.repl_wh_hours)
            

        except (NameError, AttributeError):
            pass


# =============================================================================
# SQL part
# =============================================================================

if RUN_SQL_opsdev_planogram_palletCAP_others == True:

    ######### for SQL queries ##########
    start_date = "'f2025w14'"
    end_date = "'f2025w27'"
    nr_weeks =  int(end_date[7:9]) - int(start_date[7:9]) + 1
    pmg_list = "('BWS','DAI','DRY','FRZ','HDL','HEA','PPD','PRO','SFB','SFM', 'SFP')"#"('BWS','DAI','DRY','FRZ','HDL','HEA','PPD','PRO','SFB','SFM', 'SFP')"  #'BWS', 'DAI','DRY','FRZ','HDL','HEA','PPD','PRO','SFB','SFM', 'SFP'
    country = "('HU','CZ','SK')"  #'HU','CZ','SK'
    stores = tuple(pl.read_excel(directory / excel_inputs_f, engine="calamine").filter(pl.col("Country").is_in(['SK', 'HU', 'CZ']))["Store"].unique())

    # stores =tuple([21001, 21005])
    ####################################

    ######### for planogram downloading ##########
    


    date_plan = ********

    ##############################################

    place_to_save = Path(directory / "inputs/files_for_dataset/")
    
    ###### Download & Create Dataset ######
    download_create_dataset = True     

    ###### Replenishment part separately ######
    opsdev_sql = False #foil, srp/nsrp%
    planogram_sql = False #shelfCapacity

    pallet_capacity = False
    losses_RTC_FoodBank_sql = False
    WGLL_gapScan = False

    Rc1_products = False
    
    ob_packaging = False
    
    modul_nr = False

    ###### Warehouse part ######
    WH_deliveries = False
    WH_cases_PMG_level = False

    sql_part = cm.SQL_Part(
        start_date,
        end_date,
        nr_weeks,
        pmg_list,
        country,
        opsdev_sql,
        planogram_sql,
        saved_filename,
        date_plan,
        place_to_save,
        pallet_capacity,
        losses_RTC_FoodBank_sql,
        WGLL_gapScan,
        WH_deliveries,
        stores,
        Rc1_products,
        WH_cases_PMG_level,
        ob_packaging,
        download_create_dataset,
        modul_nr
    )

    a = rm.RunSQL_queries(sql_part, data_paths)
##################################################
