import paramiko
import time
from datetime import datetime

def run_srd_script(hostname, password, timeout_minutes=90):
    """
    Simple SSH monitor for SRD SQL script execution
    """
    
    print("=" * 50)
    print("🏪 SRD Planogram Analysis Script Monitor")
    print("=" * 50)
    
    
    
    print(f"🔌 Connecting to {hostname}...")
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        # Connect
        ssh_client.connect(hostname=hostname, username="phrubos", password=password, timeout=30)
        print("✅ Connected successfully")
        
        # Quick file check
        stdin, stdout, stderr = ssh_client.exec_command("ls -la /home/<USER>/srd/srd.sql")
        if 'srd.sql' not in stdout.read().decode('utf-8'):
            print("❌ SQL file not found!")
            return False
        
        # Start execution
        print(f"🚀 Starting SRD script execution...")
        start_time = time.time()
        
        stdin, stdout, stderr = ssh_client.exec_command("sh /home/<USER>/srd/start_q 2>&1", get_pty=True)
        
        # Monitor progress
        last_update = time.time()
        while True:
            if stdout.channel.exit_status_ready():
                break
                
            if stdout.channel.recv_ready():
                chunk = stdout.channel.recv(1024).decode('utf-8', errors='ignore')
                if chunk:
                    last_update = time.time()
                    
                    # Show only important progress
                    for line in chunk.split('\n'):
                        line = line.strip()
                        if not line:
                            continue
                            
                        # Key progress indicators
                        if 'CREATE TABLE' in line.upper():
                            print("📋 Creating table...")
                        elif 'DROP TABLE' in line.upper():
                            print("🗑️  Dropping old table...")
                        elif 'SELECT DISTINCT' in line.upper():
                            print("🔍 Processing data...")
                        elif 'Job' in line and 'finished' in line:
                            print("✨ Stage completed")
                        elif 'Exception' in line or 'ERROR' in line.upper():
                            print(f"⚠️  Warning: {line[:80]}...")
            
            # Timeout check
            elapsed = time.time() - start_time
            idle_time = time.time() - last_update
            
            if elapsed > (timeout_minutes * 60):
                print(f"⏰ Timeout after {timeout_minutes} minutes")
                return False
                
            if idle_time > 60:  # Show progress every minute of silence
                print(f"⏳ Still running... ({elapsed/60:.1f} minutes elapsed)")
                last_update = time.time()
                
            time.sleep(2)
        
        # Check result
        exit_status = stdout.channel.recv_exit_status()
        total_time = time.time() - start_time
        
        print(f"⏱️  Execution time: {total_time/60:.1f} minutes")
        
        if exit_status == 0:
            print("✅ Script completed successfully!")
            
            # Quick verification
            print("🔍 Verifying table creation...")
            cmd = "echo 'SELECT COUNT(*) FROM sch_analysts.tbl_srd_planogram_analysis;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
            
            stdin, stdout, stderr = ssh_client.exec_command(cmd, timeout=60)
            output = stdout.read().decode('utf-8')
            
            # Extract row count (look for numbers in the output)
            import re
            numbers = re.findall(r'\b\d{6,}\b', output)  # Look for 6+ digit numbers
            if numbers:
                row_count = int(numbers[-1])  # Take the last large number found
                print(f"📊 Table created with {row_count:,} rows")
            else:
                print("✅ Table created successfully!")
                
            return True
        else:
            print(f"❌ Script failed with exit code: {exit_status}")
            return False
            
    except paramiko.AuthenticationException:
        print("❌ Authentication failed - check credentials")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False
    finally:
        ssh_client.close()
        print("🔌 Connection closed")
        

# Usage
if __name__ == "__main__":
    hostname = "skpbj0003.global.tesco.org"
    password = "BigCityLife2005"
    
    print("=" * 50)
    print("🏪 SRD Planogram Analysis Script Monitor")
    print("=" * 50)
    
    success = run_srd_script(hostname, password)
    
    print("=" * 50)
    if success:
        print("🎉 All done! Your SRD table is ready.")
    else:
        print("💥 Something went wrong. Check the server logs.")
    print("=" * 50)
    
    
    
    

# # Configure logging with UTF-8 encoding for Windows compatibility
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler(f'sql_execution_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8'),
#         logging.StreamHandler()
#     ]
# )

# def monitor_sql_execution(hostname, password, what_to_create, timeout_minutes=60):
#     """
#     Enhanced monitoring function for SQL execution
#     """
#     ssh_client = None
#     start_time = time.time()
    
#     try:
#         # Setup the connection with timeout
#         ssh_client = paramiko.SSHClient()
#         ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
#         logging.info(f"Attempting to connect to {hostname}...")
#         ssh_client.connect(
#             hostname=hostname, 
#             username="phrubos", 
#             password=password,
#             timeout=30  # Connection timeout
#         )
#         logging.info("CONNECTION: SSH Connection established successfully")
        
#         # Check if the SQL file exists
#         logging.info("CHECKING: SQL file existence...")
#         stdin, stdout, stderr = ssh_client.exec_command("ls -la /home/<USER>/srd/srd.sql")
#         file_check = stdout.read().decode('utf-8')
#         if 'srd.sql' in file_check:
#             logging.info(f"FILE FOUND: {file_check.strip()}")
#         else:
#             logging.error("FILE ERROR: SQL file not found!")
#             return False
            
#         # Check available disk space
#         logging.info("CHECKING: Disk space...")
#         stdin, stdout, stderr = ssh_client.exec_command("df -h /home/<USER>/srd")
#         disk_space = stdout.read().decode('utf-8')
#         logging.info(f"DISK SPACE: {disk_space}")
        
#         # Start the main script execution
#         logging.info(f"STARTING: Script execution: {what_to_create}")
#         logging.info("=" * 60)
        
#         # Execute with real-time monitoring and capture all output
#         command = f"sh /home/<USER>/srd/start_q 2>&1"  # Redirect stderr to stdout
#         stdin, stdout, stderr = ssh_client.exec_command(command, get_pty=True)
        
#         # Monitor execution in real-time
#         output_buffer = []
#         error_buffer = []
#         last_activity = time.time()
        
#         while True:
#             # Check if process is still running
#             if stdout.channel.exit_status_ready():
#                 break
                
#             # Read available output
#             if stdout.channel.recv_ready():
#                 chunk = stdout.channel.recv(1024).decode('utf-8', errors='ignore')
#                 if chunk:
#                     output_buffer.append(chunk)
#                     last_activity = time.time()
                    
#                     # Log progress indicators with more detail
#                     for line in chunk.split('\n'):
#                         if line.strip():
#                             # Check for table creation indicators
#                             if any(keyword in line.lower() for keyword in ['create table', 'created', 'table created']):
#                                 logging.info(f"TABLE CREATE: {line.strip()}")
#                             elif any(keyword in line.lower() for keyword in ['error', 'exception', 'failed', 'cannot']):
#                                 logging.error(f"ERROR: {line.strip()}")
#                             elif any(keyword in line.lower() for keyword in ['success', 'completed', 'done', 'finished']):
#                                 logging.info(f"SUCCESS: {line.strip()}")
#                             elif any(keyword in line.lower() for keyword in ['select', 'insert', 'drop', 'show']):
#                                 logging.info(f"SQL: {line.strip()}")
#                             elif 'row' in line.lower() and ('selected' in line.lower() or 'count' in line.lower()):
#                                 logging.info(f"RESULT: {line.strip()}")
#                             elif line.strip().startswith('25/06/05'):  # Spark logs
#                                 logging.debug(f"SPARK: {line.strip()}")
#                             else:
#                                 logging.debug(f"OUTPUT: {line.strip()}")
            
#             # Read available errors
#             if stdout.channel.recv_stderr_ready():
#                 error_chunk = stdout.channel.recv_stderr(1024).decode('utf-8', errors='ignore')
#                 if error_chunk:
#                     error_buffer.append(error_chunk)
#                     last_activity = time.time()
#                     logging.error(f"STDERR: {error_chunk.strip()}")
            
#             # Check for timeout
#             elapsed_time = time.time() - start_time
#             idle_time = time.time() - last_activity
            
#             if elapsed_time > (timeout_minutes * 60):
#                 logging.error(f"TIMEOUT: Script exceeded {timeout_minutes} minutes")
#                 stdout.channel.close()
#                 return False
                
#             if idle_time > 300:  # 5 minutes of no activity
#                 logging.warning(f"IDLE: No activity for {idle_time:.0f} seconds")
                
#             # Brief sleep to prevent excessive CPU usage
#             time.sleep(1)
        
#         # Get final exit status
#         exit_status = stdout.channel.recv_exit_status()
#         total_time = time.time() - start_time
        
#         # Process final output
#         final_output = ''.join(output_buffer)
#         final_errors = ''.join(error_buffer)
        
#         logging.info("=" * 60)
#         logging.info(f"TIMING: Total execution time: {total_time:.2f} seconds")
#         logging.info(f"EXIT STATUS: {exit_status}")
        
#         if exit_status == 0:
#             logging.info("RESULT: Script finished successfully!")
            
#             # Verify table creation
#             verify_table_creation(ssh_client)
#             return True
#         else:
#             logging.error(f"FAILED: Script failed with exit status: {exit_status}")
            
#             # Analyze errors
#             analyze_errors(final_errors, final_output)
#             return False
            
#     except paramiko.AuthenticationException:
#         logging.error("AUTH ERROR: Authentication failed - check username/password")
#         return False
#     except paramiko.SSHException as e:
#         logging.error(f"SSH ERROR: SSH connection error: {str(e)}")
#         return False
#     except Exception as e:
#         logging.error(f"GENERAL ERROR: Unexpected error: {str(e)}")
#         return False
#     finally:
#         if ssh_client:
#             ssh_client.close()
#             logging.info("CONNECTION: SSH connection closed")

# def verify_table_creation(ssh_client):
#     """
#     Verify if the table was created successfully
#     """
#     logging.info("VERIFICATION: Checking table creation...")
    
#     verification_commands = [
#         "echo 'SHOW TABLES IN sch_analysts LIKE \"*srd*\";' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '***********************************************************************************************************************************************************************************************************************************************************.session=false;spark.sql.parquet.int96RebaseModeInWrite=CORRECTED;spark.sql.parquet.datetimeRebaseModeInWrite=CORRECTED;kyuubi.engine.share.level.subdomain=phrubos'",
#         "echo 'SELECT COUNT(*) FROM sch_analysts.tbl_srd_planogram_analysis;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '***********************************************************************************************************************************************************************************************************************************************************.session=false;spark.sql.parquet.int96RebaseModeInWrite=CORRECTED;spark.sql.parquet.datetimeRebaseModeInWrite=CORRECTED;kyuubi.engine.share.level.subdomain=phrubos'"
#     ]
    
#     table_exists = False
#     for i, cmd in enumerate(verification_commands, 1):
#         try:
#             stdin, stdout, stderr = ssh_client.exec_command(cmd, timeout=120)
#             output = stdout.read().decode('utf-8')
#             errors = stderr.read().decode('utf-8')
            
#             logging.info(f"VERIFICATION {i}: {output[:200]}...")
            
#             if i == 1:  # Check if table name appears in output
#                 if 'tbl_srd_planogram_analysis' in output:
#                     table_exists = True
#                     logging.info("SUCCESS: Table found in database!")
#                 else:
#                     logging.error("FAILED: Table NOT found in database!")
            
#             if i == 2 and table_exists:  # Check row count
#                 if 'Exception' not in output and 'Error' not in output:
#                     logging.info("SUCCESS: Table is accessible and contains data!")
                
#             if errors and 'SLF4J' not in errors:  # Ignore SLF4J warnings
#                 logging.warning(f"VERIFICATION {i} warnings: {errors[:200]}...")
                
#         except Exception as e:
#             logging.error(f"VERIFICATION {i} failed: {str(e)}")
    
#     return table_exists

# def analyze_errors(error_output, full_output):
#     """
#     Analyze common error patterns and provide suggestions
#     """
#     logging.info("ANALYSIS: Analyzing errors...")
    
#     error_patterns = {
#         'Table or view not found': 'Table creation might have failed or is still in progress',
#         'AnalysisException': 'SQL syntax or schema issue detected',
#         'Connection refused': 'Database connection issue',
#         'Permission denied': 'Access rights problem',
#         'Out of memory': 'Insufficient memory for the operation',
#         'Timeout': 'Operation took too long to complete',
#         'S3': 'S3 storage access issue',
#         'Cannot resolve': 'Column or table reference issue'
#     }
    
#     combined_output = error_output + full_output
    
#     for pattern, description in error_patterns.items():
#         if pattern.lower() in combined_output.lower():
#             logging.error(f"IDENTIFIED: {pattern} - {description}")
    
#     # Extract specific error details
#     if 'Exception' in combined_output:
#         exception_lines = [line for line in combined_output.split('\n') if 'Exception' in line]
#         for line in exception_lines[:3]:  # Show first 3 exception lines
#             logging.error(f"EXCEPTION: {line.strip()}")

# def check_system_resources(ssh_client):
#     """
#     Check system resources before execution
#     """
#     logging.info("🔧 Checking system resources...")
    
#     resource_commands = {
#         'Memory': 'free -h',
#         'CPU': 'top -bn1 | grep "Cpu(s)"',
#         'Disk Space': 'df -h',
#         'Active Processes': 'ps aux | grep -E "(beeline|spark|hive)" | grep -v grep'
#     }
    
#     for resource, command in resource_commands.items():
#         try:
#             stdin, stdout, stderr = ssh_client.exec_command(command, timeout=10)
#             output = stdout.read().decode('utf-8')
#             logging.info(f"{resource}: {output.strip()}")
#         except Exception as e:
#             logging.warning(f"Could not check {resource}: {str(e)}")
            
            
            
# # Example usage
# if __name__ == "__main__":
#     # Your connection parameters
#     hostname = "skpbj0003.global.tesco.org"
#     password = "BigCityLife2005"
#     what_to_create = "SRD Planogram Analysis"
    
#     success = monitor_sql_execution(hostname, password, what_to_create, timeout_minutes=90)
    
#     if success:
#         print("✅ Script completed successfully!")
#     else:
#         print("❌ Script failed - check logs for details")