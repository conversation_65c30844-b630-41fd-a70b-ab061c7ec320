import pandas as pd
from pathlib import Path
import pyodbc
import glob
import polars as pl
import sys
import os
import time
import numpy as np



import warnings


warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)


saved_filename = "GM"  # it will be the dataset name

start = "'f2023w14'"
end = "'f2023w27'"

directory = (
    Path(__file__).parent if "__file__" in locals() else Path.cwd()
).parent.parent

place_to_save = Path(directory / f"inputs/files_for_dataset/{saved_filename}")
broken_case = False

as_is_dataset_f = "inputs/JDA_SRD_Tables/15-09-2023/as_is_modelDataSet_updated_15-09"
excel_inputs_f = "inputs/Repl/Stores_Inputs_2024_Q1_produce_tablMult.xlsx"
box_op_type_f = "inputs/files_for_dataset/ownbrand_opening_type_2022_mod.xlsx"
broken_cases_f = "inputs/files_for_dataset/broken_cases_list_22w14_27.csv.gz"
catres_path = r"others\CategoryReset\Category Reset CE_hier.xlsx"



stores = list(
    pd.read_excel(directory / excel_inputs_f, usecols=["Country", "Format", "Store"])[
        "Store"
    ].unique()
)

conn = pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()

print("\n###########")
print("Item Sold Downloading for new Products")
print("###########\n")




item_sold = """
        SELECT 
        cast(stores.dmst_store_code as INT) AS store,
        cal.dtdw_day_desc_en as day,  hier.pmg AS pmg,
        cast(mstr.slad_tpnb as INT) AS tpnb,
        mstr.own_brand as ownbrand,
        mstr.slad_long_des AS product_name,
        mstr.slad_unit AS unit_type,
        mstr.slad_case_size AS case_capacity,
        mstr.slad_net_weight AS weight,
        SUM(sunit.slsms_unit) AS sold_units,
        SUM(sunit.slsms_salex) AS sales_excl_vat,
        sunit.part_col as date
        FROM dw.sl_sms sunit 
        JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id 
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
        JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id
        AND mstr.cntr_id = sunit.slsms_cntr_id 
        JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
        AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
        AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
        AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
        AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
        WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
        AND SUBSTRING(hier.pmg, 1, 3) IN ('HDL')
        AND stores.dmst_store_code = 41520
        AND sunit.slsms_unit > 0 
        AND sunit.slsms_salex > 0 
        
        GROUP BY stores.dmst_store_code, sunit.part_col, cal.dtdw_day_desc_en,  hier.pmg, mstr.slad_tpnb, mstr.own_brand, mstr.slad_long_des, mstr.slad_unit, mstr.slad_case_size, mstr.slad_net_weight
        ORDER BY  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb, cal.dtdw_day_desc_en, sunit.part_col
            """.format(start = start, end=end)

sold_units = pd.read_sql(item_sold, conn)


print(f"\nItem Sold Done\n")

print("\n###########")
print("Item Sold_dotcom Downloading for new Products")
print("###########\n")

isold_dotcom = """
        SELECT 
        cast(stores.dmst_store_code as INT) AS store,
        cal.dtdw_day_desc_en as day,
        hier.pmg AS pmg,
        cast(mstr.slad_tpnb as INT) AS tpnb,
        SUM(sunit.sltrg_tr_unit) AS sold_units_dotcom,
        sunit.part_col as date
        FROM dw.sl_trg sunit 
        JOIN dm.dim_stores stores ON stores.cntr_id = sunit.sltrg_cntr_id AND stores.dmst_store_id = sunit.sltrg_dmst_id 
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
        JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.sltrg_dmat_id
        AND mstr.cntr_id = sunit.sltrg_cntr_id 
        JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
        AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
        AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
        AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
        AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
        WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
        AND SUBSTRING(hier.pmg, 1, 3) IN ('HDL')
        AND sunit.sltrg_tr_unit > 0 
        AND stores.dmst_store_code = 41520
        GROUP BY  stores.dmst_store_code, cal.dtdw_day_desc_en, hier.pmg,  mstr.slad_tpnb, sunit.part_col
        ORDER BY  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb, cal.dtdw_day_desc_en
        """.format(
    start=start, end=end
)

sold_units_dotcom = pd.read_sql(isold_dotcom, conn)



print("\n###########")
print("Stock Downloading for new Products")
print("###########\n")


stock_sql = """
        SELECT 
        CAST(stores.dmst_store_code AS INT) AS store,
        cal.dtdw_day_desc_en as day,
        hier.pmg AS pmg,
        CAST(mstr.slad_tpnb AS INT) AS tpnb,
        SUM(stock.slstks_stock_unit_sl) AS stock,
        AVG(stock.slstks_price) as item_price,
        stock.part_col as date
        FROM dw.sl_stocks stock
        JOIN dm.dim_stores stores ON stock.slstks_dmst_id = stores.dmst_store_id AND stock.slstks_cntr_id = stores.cntr_id
        JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = stock.slstks_dmat_id AND mstr.cntr_id = stock.slstks_cntr_id
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = stock.part_col
        JOIN tesco_analysts.hierarchy_spm hier
        ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0")
        AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0")
        AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0")
        AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0")
        AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
        WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
        AND stores.dmst_store_code = 41520
        AND SUBSTRING(hier.pmg, 1, 3) IN ('HDL')
        AND stock.slstks_stock_unit_sl > 0
        GROUP BY stores.dmst_store_code, hier.pmg, cal.dtdw_day_desc_en, mstr.slad_tpnb, stock.part_col
        ORDER BY stock.part_col, stores.dmst_store_code, hier.pmg, mstr.slad_tpnb
            """.format(start = start, end=end)

stock = pd.read_sql(stock_sql, conn)



print("\n###########")
print("Cases Delivered Downloading for new Products")
print("###########\n")


Cases_delivered = """
        select 
        CAST(CONCAT(cases.int_cntr_id, cases.store) AS INT) as store,
        cal.dtdw_day_desc_en as day,
        CAST(cases.product as INT) as tpnb,
        mstr.slad_long_des AS product_name,
        hier.pmg AS pmg,
        SUM(cases.qty) as unit,
        AVG(mstr.slad_case_size) as artgld_case_capacity,
        cases.part_col as date
        from stg_go.go_106_order_receiving cases
        LEFT JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = cases.product and mstr.cntr_id = cases.int_cntr_id
        Right JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = lpad(hier.div_code,4,"0")
        AND mstr.dmat_dep_code = lpad(hier.dep_code,4,"0")
        AND mstr.dmat_sec_code = lpad(hier.sec_code,4,"0")
        AND mstr.dmat_grp_code = lpad(hier.grp_code,4,"0")
        AND mstr.dmat_sgr_code = lpad(hier.sgr_code,4,"0")
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = cases.part_col
        WHERE SUBSTRING(hier.pmg, 1, 3) IN ('HDL')
        and CONCAT(cases.int_cntr_id, cases.store) = 41520
        and cal.dmtm_fw_code BETWEEN {start} AND {end}
        GROUP BY  CONCAT(cases.int_cntr_id, cases.store), cal.dtdw_day_desc_en, cases.product, mstr.slad_long_des, hier.pmg, cases.part_col
        ORDER BY  CONCAT(cases.int_cntr_id, cases.store), cal.dtdw_day_desc_en, hier.pmg
        """.format(
    start=start, end=end
)

cases = pd.read_sql(Cases_delivered, conn)

cases.drop("product_name", axis=1,inplace=True)

df = sold_units.merge(stock,on=['store','pmg','date','day','tpnb'], how='outer')
df = df.merge(sold_units_dotcom,on=['store','pmg','date','day','tpnb'], how='outer')
df = df.merge(cases,on=['store','pmg','date','day','tpnb'], how="outer")

df.fillna(0, inplace=True)

df['case_capacity'] = np.where((df['case_capacity'] == 0) & (df['artgld_case_capacity'] > 0), df['artgld_case_capacity'], df['case_capacity'])
df['cases_delivered'] = df['unit'] / df['case_capacity']

df["cases_delivered"] = (
    df["cases_delivered"]
    .replace(np.nan, 0)
    .replace([np.inf, -np.inf], 0))

# df = df.groupby(['date'],as_index=False)[['stock', "sold_units", "cases_delivered", "unit"]].sum()

# df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')


# import plotly.io as pio
# import plotly.express as px
# pio.renderers.default='browser'
# # Create a line chart using Plotly Express:
# fig = px.line(df, x='date', y=['sold_units', 'cases_delivered','unit'],
#               labels={'date': 'Date', 'value': 'Value'}, title='Sales and Stock Over Time')

# # Show the chart
# fig.show()


