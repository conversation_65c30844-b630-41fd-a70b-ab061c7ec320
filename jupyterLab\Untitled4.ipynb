{"cells": [{"cell_type": "code", "execution_count": null, "id": "77a45d60-6df0-4ce8-b4a8-1543d8e8e8be", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a61aaeef-9cb5-470a-8017-2390fc8b64eb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b122f624-431c-47b4-9fe9-0f7c9b63873d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "db14c304-fcd1-4686-ae6c-b11e487384eb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "699470a6-ddae-457a-91dd-e52012016855", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "4dbfefc9-1fb5-4eea-b836-8c575bd5eaa8", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0.20.3'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "import polars as pl\n", "\n", "pl.__version__"]}, {"cell_type": "code", "execution_count": null, "id": "d37de632-93c3-4699-810b-f886091ef40f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "c138a77e-c9c8-4611-adba-050116121223", "metadata": {"scrolled": true}, "outputs": [{"ename": "ValueError", "evalue": "ClassSelector parameter None value must be an instance of (function, tuple), not <function size at 0x0000028B4FE81970>.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_30280\\2791363706.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mpolars\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mpl\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[0mpl\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mDataFrame\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m{\u001b[0m\u001b[1;34m\"a\"\u001b[0m\u001b[1;33m:\u001b[0m \u001b[1;33m[\u001b[0m\u001b[1;36m1\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m2\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m\"b\"\u001b[0m\u001b[1;33m:\u001b[0m \u001b[1;33m[\u001b[0m\u001b[1;36m3\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m4\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mplot\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mline\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mx\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;34m\"a\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\polars\\dataframe\\frame.py\u001b[0m in \u001b[0;36mplot\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1165\u001b[0m         \u001b[1;33m>>\u001b[0m\u001b[1;33m>\u001b[0m \u001b[0mhvplot\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mhelp\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"scatter\"\u001b[0m\u001b[1;33m)\u001b[0m  \u001b[1;31m# doctest: +SKIP\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1166\u001b[0m         \"\"\"\n\u001b[1;32m-> 1167\u001b[1;33m         if not _HVPLOT_AVAILABLE or parse_version(hvplot.__version__) < parse_version(\n\u001b[0m\u001b[0;32m   1168\u001b[0m             \u001b[1;34m\"0.9.1\"\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1169\u001b[0m         ):\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\polars\\dependencies.py\u001b[0m in \u001b[0;36m__getattr__\u001b[1;34m(self, attr)\u001b[0m\n\u001b[0;32m     85\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_module_available\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     86\u001b[0m             \u001b[1;31m# import the module and return the requested attribute\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 87\u001b[1;33m             \u001b[0mmodule\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_import\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     88\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mmodule\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mattr\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     89\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\polars\\dependencies.py\u001b[0m in \u001b[0;36m_import\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     69\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_import\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mModuleType\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     70\u001b[0m         \u001b[1;31m# import the referenced module, replacing the proxy in this module's globals\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 71\u001b[1;33m         \u001b[0mmodule\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mimport_module\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     72\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_globals\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_module_name\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mmodule\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     73\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__dict__\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mupdate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mmodule\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__dict__\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\importlib\\__init__.py\u001b[0m in \u001b[0;36mimport_module\u001b[1;34m(name, package)\u001b[0m\n\u001b[0;32m    125\u001b[0m                 \u001b[1;32mbreak\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    126\u001b[0m             \u001b[0mlevel\u001b[0m \u001b[1;33m+=\u001b[0m \u001b[1;36m1\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 127\u001b[1;33m     \u001b[1;32mreturn\u001b[0m \u001b[0m_bootstrap\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_gcd_import\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mlevel\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mpackage\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mlevel\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    128\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    129\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\importlib\\_bootstrap.py\u001b[0m in \u001b[0;36m_gcd_import\u001b[1;34m(name, package, level)\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\importlib\\_bootstrap.py\u001b[0m in \u001b[0;36m_find_and_load\u001b[1;34m(name, import_)\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\importlib\\_bootstrap.py\u001b[0m in \u001b[0;36m_find_and_load_unlocked\u001b[1;34m(name, import_)\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\importlib\\_bootstrap.py\u001b[0m in \u001b[0;36m_load_unlocked\u001b[1;34m(spec)\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\importlib\\_bootstrap_external.py\u001b[0m in \u001b[0;36mexec_module\u001b[1;34m(self, module)\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\importlib\\_bootstrap.py\u001b[0m in \u001b[0;36m_call_with_frames_removed\u001b[1;34m(f, *args, **kwds)\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\hvplot\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     67\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mholoviews\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mStore\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mrender\u001b[0m  \u001b[1;31m# noqa\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     68\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 69\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mconverter\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mHoloViewsConverter\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     70\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0minteractive\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mInteractive\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     71\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mui\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mexplorer\u001b[0m  \u001b[1;31m# noqa\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\hvplot\\converter.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     21\u001b[0m     \u001b[0mVectorField\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mRectangles\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mSegments\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     22\u001b[0m )\n\u001b[1;32m---> 23\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mholoviews\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mplotting\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mbokeh\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mOverlayPlot\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mcolormap_generator\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     24\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mholoviews\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mplotting\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mutil\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mprocess_cmap\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     25\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mholoviews\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0moperation\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mhistogram\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mapply_when\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\holoviews\\plotting\\bokeh\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     38\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mgraphs\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mGraphPlot\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mNodePlot\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mTriMeshPlot\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mChordPlot\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     39\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mheatmap\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mHeatMapPlot\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mRadialHeatMapPlot\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 40\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mhex_tiles\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mHexTilesPlot\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     41\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mpath\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mPathPlot\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mPolygonPlot\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mContourPlot\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     42\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mplot\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mGridPlot\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mLayoutPlot\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mAdjointLayoutPlot\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\holoviews\\plotting\\bokeh\\hex_tiles.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     20\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     21\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 22\u001b[1;33m \u001b[1;32mclass\u001b[0m \u001b[0mhex_binning\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mOperation\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     23\u001b[0m     \"\"\"\n\u001b[0;32m     24\u001b[0m     \u001b[0mApplies\u001b[0m \u001b[0mhex\u001b[0m \u001b[0mbinning\u001b[0m \u001b[0mby\u001b[0m \u001b[0mcomputing\u001b[0m \u001b[0maggregates\u001b[0m \u001b[0mon\u001b[0m \u001b[0ma\u001b[0m \u001b[0mhexagonal\u001b[0m \u001b[0mgrid\u001b[0m\u001b[1;33m.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\holoviews\\plotting\\bokeh\\hex_tiles.py\u001b[0m in \u001b[0;36mhex_binning\u001b[1;34m()\u001b[0m\n\u001b[0;32m     28\u001b[0m     \"\"\"\n\u001b[0;32m     29\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 30\u001b[1;33m     aggregator = param.ClassSelector(\n\u001b[0m\u001b[0;32m     31\u001b[0m         default=np.size, class_=(types.FunctionType, tuple), doc=\"\"\"\n\u001b[0;32m     32\u001b[0m       \u001b[0mAggregation\u001b[0m \u001b[0mfunction\u001b[0m \u001b[1;32mor\u001b[0m \u001b[0mdimension\u001b[0m \u001b[0mtransform\u001b[0m \u001b[0mused\u001b[0m \u001b[0mto\u001b[0m \u001b[0mcompute\u001b[0m \u001b[0mbin\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\param\\__init__.py\u001b[0m in \u001b[0;36m__init__\u001b[1;34m(self, class_, default, instantiate, is_instance, **params)\u001b[0m\n\u001b[0;32m   1325\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mis_instance\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mis_instance\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1326\u001b[0m         \u001b[0msuper\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mClassSelector\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__init__\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mdefault\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mdefault\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0minstantiate\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0minstantiate\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m**\u001b[0m\u001b[0mparams\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1327\u001b[1;33m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_validate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mdefault\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1328\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1329\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_validate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mval\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\param\\__init__.py\u001b[0m in \u001b[0;36m_validate\u001b[1;34m(self, val)\u001b[0m\n\u001b[0;32m   1329\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_validate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mval\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1330\u001b[0m         \u001b[0msuper\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mClassSelector\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_validate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mval\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1331\u001b[1;33m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_validate_class_\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mval\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mclass_\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mis_instance\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1332\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1333\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_validate_class_\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mval\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mclass_\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mis_instance\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\param\\__init__.py\u001b[0m in \u001b[0;36m_validate_class_\u001b[1;34m(self, val, class_, is_instance)\u001b[0m\n\u001b[0;32m   1341\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mis_instance\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1342\u001b[0m             \u001b[1;32mif\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;33m(\u001b[0m\u001b[0misinstance\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mval\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mclass_\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1343\u001b[1;33m                 raise ValueError(\n\u001b[0m\u001b[0;32m   1344\u001b[0m                     \u001b[1;34m\"%s parameter %r value must be an instance of %s, not %r.\"\u001b[0m \u001b[1;33m%\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1345\u001b[0m                     (param_cls, self.name, class_name, val))\n", "\u001b[1;31mValueError\u001b[0m: ClassSelector parameter None value must be an instance of (function, tuple), not <function size at 0x0000028B4FE81970>."]}], "source": ["import polars as pl\n", "pl.DataFrame({\"a\": [1, 2], \"b\": [3, 4]}).plot.line(x=\"a\")"]}, {"cell_type": "code", "execution_count": 4, "id": "fda5885e-6587-4a2c-b41c-f130fa962e9b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (2, 3)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>d</th><th>a</th><th>b</th></tr><tr><td>str</td><td>i64</td><td>i64</td></tr></thead><tbody><tr><td>&quot;ping&quot;</td><td>1</td><td>3</td></tr><tr><td>&quot;pong&quot;</td><td>2</td><td>4</td></tr></tbody></table></div>"], "text/plain": ["shape: (2, 3)\n", "┌──────┬─────┬─────┐\n", "│ d    ┆ a   ┆ b   │\n", "│ ---  ┆ --- ┆ --- │\n", "│ str  ┆ i64 ┆ i64 │\n", "╞══════╪═════╪═════╡\n", "│ ping ┆ 1   ┆ 3   │\n", "│ pong ┆ 2   ┆ 4   │\n", "└──────┴─────┴─────┘"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import polars as pl\n", "pl.DataFrame({\"a\": [1, 2], \"b\": [3, 4], \"c\": [\"foo\", \"bar\"], \"d\": [\"ping\", \"pong\"]}).select([pl.col(\"d\"), pl.col(pl.NUMERIC_DTYPES)])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}