import pandas as pd
import numpy as np

# Assuming 'a' is your existing dataframe with a 'stores' column
benchmark_stores = [11019, 14201, 25016, 44089, 24062, 24047, 44087]
new_stores = [11085, 14222, 25034, 44092, 24174, 24173, 44093]
store_mapping = dict(zip(benchmark_stores, new_stores))

a = a[~a.store.isin(new_stores)]

# This is much faster than filtering the entire dataframe
# Only filter once to get benchmark rows
mask = a['store'].isin(benchmark_stores)
benchmark_rows = a[mask].copy()

# If there are matches, create the new rows efficiently
if not benchmark_rows.empty:
    # Create a new dataframe for the new stores
    new_rows = benchmark_rows.copy()
    
    # Replace store values using map (vectorized operation)
    new_rows['store'] = new_rows['store'].map(store_mapping)
    
    # Append to original dataframe (more efficient than repeated concatenation)
    a = pd.concat([a, new_rows], ignore_index=True)