import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import time

# Configure page - can be called multiple times now!
st.set_page_config(
    page_title="Modern Analytics Dashboard",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for modern styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }
    
    .stTabs [data-baseweb="tab-list"] {
        gap: 2rem;
    }
    
    .stTabs [data-baseweb="tab"] {
        padding: 0.5rem 1rem;
        border-radius: 8px;
    }
</style>
""", unsafe_allow_html=True)

# Generate sample data
@st.cache_data
def generate_sample_data():
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    
    # Sales data
    sales_data = pd.DataFrame({
        'date': dates,
        'revenue': np.random.normal(10000, 2000, len(dates)).cumsum(),
        'orders': np.random.poisson(50, len(dates)),
        'customers': np.random.poisson(30, len(dates)),
        'category': np.random.choice(['Electronics', 'Clothing', 'Books', 'Home'], len(dates))
    })
    
    # User analytics
    user_data = pd.DataFrame({
        'country': ['USA', 'UK', 'Germany', 'France', 'Canada', 'Australia', 'Japan', 'Brazil'],
        'users': np.random.randint(1000, 10000, 8),
        'conversion_rate': np.random.uniform(0.02, 0.08, 8),
        'avg_session_duration': np.random.uniform(120, 300, 8)
    })
    
    return sales_data, user_data

# Navigation setup using new top navigation
def create_navigation():
    # Create page objects for navigation
    dashboard = st.Page(dashboard_page, title="Dashboard", icon="🏠")
    analytics = st.Page(analytics_page, title="Analytics", icon="📊")
    settings = st.Page(settings_page, title="Settings", icon="🔧")
    about = st.Page(about_page, title="About", icon="ℹ️")
    
    # Using new top navigation feature
    nav = st.navigation([dashboard, analytics, settings, about], position="top")
    return nav

def dashboard_page():
    st.markdown('<h1 class="main-header">🚀 Modern Analytics Dashboard</h1>', unsafe_allow_html=True)
    
    # Theme detection feature
    theme = st.context.theme
    if theme:
        theme_indicator = "🌙 Dark Mode" if theme.type == "dark" else "☀️ Light Mode"
        st.markdown(f"**Current Theme:** {theme_indicator}")
    
    # Load data
    sales_data, user_data = generate_sample_data()
    
    # Top metrics with custom width
    col1, col2, col3, col4 = st.columns(4, gap="large")
    
    with col1:
        st.metric(
            "Total Revenue",
            f"${sales_data['revenue'].iloc[-1]:,.0f}",
            f"{((sales_data['revenue'].iloc[-1] - sales_data['revenue'].iloc[-30]) / sales_data['revenue'].iloc[-30] * 100):+.1f}%"
        )
    
    with col2:
        st.metric(
            "Total Orders",
            f"{sales_data['orders'].sum():,}",
            f"+{sales_data['orders'].iloc[-7:].sum()}"
        )
    
    with col3:
        st.metric(
            "Active Users",
            f"{user_data['users'].sum():,}",
            "+12.5%"
        )
    
    with col4:
        st.metric(
            "Conversion Rate",
            f"{user_data['conversion_rate'].mean():.2%}",
            "+0.8%"
        )
    
    st.divider()
    
    # Charts section with nested components
    chart_col1, chart_col2 = st.columns([2, 1], gap="large")
    
    with chart_col1:
        st.subheader("📈 Revenue Trend")
        
        # Nested expander within column
        with st.expander("Chart Options", expanded=True):
            chart_type = st.selectbox("Chart Type", ["Line", "Area", "Bar"])
            show_trend = st.checkbox("Show Trend Line", value=True)
        
        # Create revenue chart
        fig = px.line(sales_data.tail(90), x='date', y='revenue', 
                     title="Revenue Over Time (Last 90 Days)")
        
        if chart_type == "Area":
            fig = px.area(sales_data.tail(90), x='date', y='revenue')
        elif chart_type == "Bar":
            fig = px.bar(sales_data.tail(90), x='date', y='revenue')
        
        fig.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with chart_col2:
        st.subheader("🌍 User Distribution")
        
        # Nested popover within column
        with st.popover("🔍 View Details"):
            st.write("Geographic distribution of users")
            st.dataframe(user_data[['country', 'users']], hide_index=True)
        
        # Pie chart
        fig_pie = px.pie(user_data, values='users', names='country', 
                        title="Users by Country")
        fig_pie.update_layout(height=400)
        st.plotly_chart(fig_pie, use_container_width=True)
    
    # Interactive form with height configuration
    st.subheader("📝 Quick Actions")
    
    # Form with custom height
    with st.form("quick_actions_form", height=200):
        form_col1, form_col2 = st.columns(2)
        
        with form_col1:
            action_type = st.selectbox("Action Type", ["Send Report", "Update Settings", "Export Data"])
            recipient = st.text_input("Recipient Email")
        
        with form_col2:
            priority = st.select_slider("Priority", ["Low", "Medium", "High"])
            schedule = st.date_input("Schedule For", datetime.now().date())
        
        notes = st.text_area("Additional Notes", height=80)
        
        if st.form_submit_button("Execute Action", type="primary"):
            st.success(f"✅ {action_type} scheduled for {schedule}")
    
    # Real-time updates simulation
    st.subheader("🔄 Live Updates")
    
    # Columns with no gap
    live_col1, live_col2, live_col3 = st.columns(3, gap=None)
    
    with live_col1:
        st.markdown("**Server Status**")
        if st.button("🔄 Refresh"):
            st.success("🟢 Online")
    
    with live_col2:
        st.markdown("**Last Updated**")
        st.write(datetime.now().strftime("%H:%M:%S"))
    
    with live_col3:
        st.markdown("**Active Sessions**")
        st.write(f"{np.random.randint(150, 200)}")

def analytics_page():
    st.markdown('<h1 class="main-header">📊 Advanced Analytics</h1>', unsafe_allow_html=True)
    
    sales_data, user_data = generate_sample_data()
    
    # Nested components showcase
    with st.container():
        st.subheader("🎯 Performance Metrics")
        
        # Nested expander with nested columns
        with st.expander("📈 Detailed Analysis", expanded=True):
            analysis_col1, analysis_col2 = st.columns(2)
            
            with analysis_col1:
                st.write("**Revenue Analysis**")
                
                # Nested chat message container
                with st.chat_message("assistant"):
                    st.write("Revenue has increased by 15% this quarter")
                    
                    # Nested expander within chat message
                    with st.expander("View Details"):
                        st.line_chart(sales_data.set_index('date')['revenue'].tail(30))
            
            with analysis_col2:
                st.write("**User Engagement**")
                
                # Nested popover within expander
                with st.popover("💡 Insights"):
                    st.write("Key insights:")
                    st.write("• Peak hours: 2-4 PM")
                    st.write("• Best performing: Electronics")
                    st.write("• Conversion rate up 8%")
                
                st.bar_chart(user_data.set_index('country')['users'])
    
    # Advanced charts
    st.subheader("📊 Advanced Visualizations")
    
    tab1, tab2, tab3 = st.tabs(["🔥 Heatmap", "📈 Correlations", "🎯 Forecasting"])
    
    with tab1:
        # Create heatmap data
        heatmap_data = sales_data.pivot_table(
            values='revenue', 
            index=sales_data['date'].dt.month,
            columns=sales_data['date'].dt.day_name(),
            aggfunc='mean'
        )
        
        fig_heatmap = px.imshow(heatmap_data, 
                              title="Revenue Heatmap by Month and Day",
                              color_continuous_scale="Viridis")
        fig_heatmap.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )
        st.plotly_chart(fig_heatmap, use_container_width=True)
    
    with tab2:
        # Correlation matrix
        numeric_data = sales_data[['revenue', 'orders', 'customers']].corr()
        fig_corr = px.imshow(numeric_data, 
                           title="Correlation Matrix",
                           text_auto=True)
        fig_corr.update_layout(
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )
        st.plotly_chart(fig_corr, use_container_width=True)
    
    with tab3:
        # Simple forecasting visualization
        future_dates = pd.date_range(start=sales_data['date'].max() + timedelta(days=1), 
                                   periods=30, freq='D')
        forecast = sales_data['revenue'].iloc[-1] * (1 + np.random.normal(0.001, 0.01, 30)).cumprod()
        
        fig_forecast = go.Figure()
        fig_forecast.add_trace(go.Scatter(x=sales_data['date'].tail(30), 
                                        y=sales_data['revenue'].tail(30),
                                        mode='lines', name='Historical'))
        fig_forecast.add_trace(go.Scatter(x=future_dates, 
                                        y=forecast,
                                        mode='lines', name='Forecast',
                                        line=dict(dash='dash')))
        fig_forecast.update_layout(
            title="Revenue Forecast",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )
        st.plotly_chart(fig_forecast, use_container_width=True)

def settings_page():
    st.markdown('<h1 class="main-header">🔧 Settings & Configuration</h1>', unsafe_allow_html=True)
    
    # Theme detection and settings
    theme = st.context.theme
    if theme:
        st.success(f"✅ Current theme detected: {theme.type.title()} mode")
        st.write(f"**Theme Type:** {theme.type}")
    else:
        st.info("Theme information not available")
    st.divider()
    
    # Settings form with custom width
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("⚙️ Application Settings")
        
        # Use custom width for selectbox
        refresh_rate = st.selectbox(
            "Auto-refresh Rate",
            ["5 seconds", "30 seconds", "1 minute", "5 minutes"],
            index=2
        )
        
        enable_notifications = st.checkbox("Enable Notifications", value=True)
        enable_dark_mode = st.checkbox("Force Dark Mode", value=False)
        
        # Slider with custom width
        chart_height = st.slider("Default Chart Height", 300, 800, 400)
        
    with col2:
        st.subheader("🔐 User Preferences")
        
        default_view = st.radio(
            "Default Dashboard View",
            ["Overview", "Analytics", "Reports"],
            horizontal=True
        )
        
        date_format = st.selectbox(
            "Date Format",
            ["YYYY-MM-DD", "MM/DD/YYYY", "DD/MM/YYYY"]
        )
        
        currency = st.selectbox(
            "Currency",
            ["USD ($)", "EUR (€)", "GBP (£)", "JPY (¥)"]
        )
    
    st.divider()
    
    # Export/Import settings
    st.subheader("📤 Data Management")
    
    export_col1, export_col2, export_col3 = st.columns(3)
    
    with export_col1:
        if st.button("📊 Export Dashboard Data", type="primary"):
            st.success("✅ Data exported successfully!")
    
    with export_col2:
        if st.button("⚙️ Export Settings"):
            st.success("✅ Settings exported!")
    
    with export_col3:
        uploaded_file = st.file_uploader("📁 Import Settings", type=['json'])
        if uploaded_file:
            st.success("✅ Settings imported!")

def about_page():
    st.markdown('<h1 class="main-header">ℹ️ About This App</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    ## 🚀 Modern Streamlit Features Showcase
    
    This application demonstrates the latest features in Streamlit 1.46.0:
    """)
    
    # Feature showcase with nested components
    with st.expander("🧭 Top Navigation", expanded=True):
        st.write("• **st.navigation with position='top'** - Clean navigation across the top")
        st.write("• Improved user experience with horizontal menu")
        
        # Nested code block
        st.code("""
        pages = {
            "🏠 Dashboard": dashboard_page,
            "📊 Analytics": analytics_page,
            "🔧 Settings": settings_page
        }
        selected_page = st.navigation(pages, position="top")
        """, language="python")
    
    with st.expander("🔆 Theme Detection"):
        st.write("• **st.context.theme** - Runtime theme detection")
        st.write("• Adaptive UI based on light/dark mode")
        
        # Show current theme info
        theme = st.context.theme
        if theme:
            st.json({
                "type": theme.type
            })
        else:
            st.info("Theme information not available")
    
    with st.expander("🪺 Nested Components"):
        st.write("• **Unlimited nesting** - Columns, expanders, popovers, chat messages")
        st.write("• More flexible layouts and interactions")
        
        # Demonstration of nesting
        nest_col1, nest_col2 = st.columns(2)
        
        with nest_col1:
            with st.chat_message("assistant"):
                st.write("Chat message with nested content")
                with st.expander("Nested expander"):
                    st.write("This is nested inside a chat message!")
        
        with nest_col2:
            with st.popover("Nested popover"):
                st.write("Popover content")
                inner_col1, inner_col2 = st.columns(2)
                with inner_col1:
                    st.button("Button 1")
                with inner_col2:
                    st.button("Button 2")
    
    with st.expander("↔️ Width Configuration"):
        st.write("• **Custom width** for most Streamlit elements")
        st.write("• Better control over layout and spacing")
        
        # Demonstrate different widths
        st.write("Different column gap configurations:")
        
        st.write("**Large gap:**")
        gap_col1, gap_col2, gap_col3 = st.columns(3, gap="large")
        with gap_col1:
            st.info("Column 1")
        with gap_col2:
            st.info("Column 2")
        with gap_col3:
            st.info("Column 3")
        
        st.write("**No gap:**")
        nogap_col1, nogap_col2, nogap_col3 = st.columns(3, gap=None)
        with nogap_col1:
            st.info("Column 1")
        with nogap_col2:
            st.info("Column 2")
        with nogap_col3:
            st.info("Column 3")
    
    with st.expander("⬆️ Form Height Configuration"):
        st.write("• **st.form height parameter** - Custom form heights")
        st.write("• Better control over form layout")
        
        # Example form with custom height
        with st.form("demo_form", height=150):
            st.text_input("Name")
            st.text_input("Email")
            st.form_submit_button("Submit")
    
    st.divider()
    
    # App info
    info_col1, info_col2 = st.columns(2)
    
    with info_col1:
        st.subheader("🛠️ Technical Details")
        st.write("• **Streamlit Version:** 1.46.0+")
        st.write("• **Features:** Top navigation, theme detection, nested components")
        st.write("• **Styling:** Custom CSS with glassmorphism effects")
        st.write("• **Charts:** Plotly for interactive visualizations")
    
    with info_col2:
        st.subheader("📊 Sample Data")
        st.write("• **Sales Data:** 365 days of simulated revenue")
        st.write("• **User Analytics:** Geographic distribution")
        st.write("• **Real-time Updates:** Simulated live data")
        st.write("• **Caching:** Optimized data loading")

# Main app execution
def main():
    # Initialize session state
    if 'last_update' not in st.session_state:
        st.session_state.last_update = datetime.now()
    
    # Create and run navigation
    nav = create_navigation()
    nav.run()
    
    # Footer
    st.markdown("---")
    st.markdown(
        "🚀 **Modern Streamlit App** | Built with Streamlit 1.46.0 | "
        f"Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    )

if __name__ == "__main__":
    main()