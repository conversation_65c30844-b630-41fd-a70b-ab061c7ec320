{"cells": [{"cell_type": "code", "execution_count": null, "id": "2933973c-f2a7-485b-b543-d38b4c31a892", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "5d58490b-5b29-4e3c-8ab9-7e44b61666f4", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hoverinfo": "x+y", "name": "A", "textposition": "outside", "type": "bar", "x": ["A"], "y": [100]}, {"hoverinfo": "x+y", "name": "B", "textposition": "outside", "type": "bar", "x": ["B"], "y": [200]}, {"hoverinfo": "x+y", "name": "C", "textposition": "outside", "type": "bar", "x": ["C"], "y": [300]}, {"hoverinfo": "x+y", "name": "Subcategory of A", "textposition": "outside", "type": "bar", "visible": false, "x": ["A1"], "y": [30]}, {"hoverinfo": "x+y", "name": "Subcategory of A", "textposition": "outside", "type": "bar", "visible": false, "x": ["A2"], "y": [70]}, {"hoverinfo": "x+y", "name": "Subcategory of B", "textposition": "outside", "type": "bar", "visible": false, "x": ["B1"], "y": [150]}, {"hoverinfo": "x+y", "name": "Subcategory of B", "textposition": "outside", "type": "bar", "visible": false, "x": ["B2"], "y": [50]}, {"hoverinfo": "x+y", "name": "Subcategory of C", "textposition": "outside", "type": "bar", "visible": false, "x": ["C1"], "y": [100]}, {"hoverinfo": "x+y", "name": "Subcategory of C", "textposition": "outside", "type": "bar", "visible": false, "x": ["C2"], "y": [200]}], "layout": {"template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Main Categories"}, "updatemenus": [{"buttons": [{"args": [{"visible": [false, false, false, true, false, false, true, true]}, {"title": "Drilldown for A"}], "label": "A", "method": "update"}, {"args": [{"visible": [false, false, false, false, true, false, true, true]}, {"title": "Drilldown for B"}], "label": "B", "method": "update"}, {"args": [{"visible": [false, false, false, false, false, true, true, true]}, {"title": "Drilldown for C"}], "label": "C", "method": "update"}, {"args": [{"visible": [true, true, true, false, false, false, false, false, false]}, {"title": "Main Categories"}], "label": "Back", "method": "update"}], "direction": "down", "showactive": true}], "xaxis": {"title": {"text": "Category"}}, "yaxis": {"title": {"text": "Value"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objs as go\n", "import plotly.express as px\n", "import plotly.io as pio\n", "from plotly.subplots import make_subplots\n", "import pandas as pd\n", "\n", "# Sample data\n", "data = {\n", "    'Category': ['A', 'B', 'C'],\n", "    'Value': [100, 200, 300],\n", "    'Detail': [\n", "        [{'Subcategory': 'A1', 'Subvalue': 30}, {'Subcategory': 'A2', 'Subvalue': 70}],\n", "        [{'Subcategory': 'B1', 'Subvalue': 150}, {'Subcategory': 'B2', 'Subvalue': 50}],\n", "        [{'Subcategory': 'C1', 'Subvalue': 100}, {'Subcategory': 'C2', 'Subvalue': 200}]\n", "    ]\n", "}\n", "\n", "# Create main bar chart\n", "fig = go.Figure()\n", "\n", "# Add main category bars\n", "for category, value in zip(data['Category'], data['Value']):\n", "    fig.add_trace(go.Bar(\n", "        x=[category],\n", "        y=[value],\n", "        name=category,\n", "        hoverinfo='x+y',\n", "        textposition='outside'\n", "    ))\n", "\n", "# Define drill-down buttons\n", "buttons = []\n", "\n", "# Add buttons for each category's drill-down details\n", "for i, category in enumerate(data['Category']):\n", "    drilldown_data = data['Detail'][i]\n", "    subcategories = [item['Subcategory'] for item in drilldown_data]\n", "    subvalues = [item['Subvalue'] for item in drilldown_data]\n", "    \n", "    button = dict(\n", "        label=category,\n", "        method='update',\n", "        args=[\n", "            {\n", "                'visible': [False] * len(data['Category']) + [j == i for j in range(len(data['Category']))] + [True] * len(subcategories)\n", "            },\n", "            {\n", "                'title': f'Drill<PERSON> for {category}'\n", "            }\n", "        ]\n", "    )\n", "    \n", "    buttons.append(button)\n", "    \n", "    for subcategory, subvalue in zip(subcategories, subvalues):\n", "        fig.add_trace(go.Bar(\n", "            x=[subcategory],\n", "            y=[subvalue],\n", "            name=f'Subcategory of {category}',\n", "            hoverinfo='x+y',\n", "            textposition='outside',\n", "            visible=False\n", "        ))\n", "\n", "# Add button to go back to main view\n", "buttons.append(dict(\n", "    label='Back',\n", "    method='update',\n", "    args=[\n", "        {'visible': [True] * len(data['Category']) + [False] * (len(fig.data) - len(data['Category']))},\n", "        {'title': 'Main Categories'}\n", "    ]\n", "))\n", "\n", "# Update figure layout with buttons\n", "fig.update_layout(\n", "    updatemenus=[{\n", "        'buttons': buttons,\n", "        'direction': 'down',\n", "        'showactive': True\n", "    }],\n", "    title='Main Categories',\n", "    xaxis_title='Category',\n", "    yaxis_title='Value'\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "bf9f4ee3-775b-4d9c-a1fb-57364f6e74bd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "81f9dc4d-c4c0-4fa9-9671-ec004f051b4b", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"labels": ["Grocery", "Fresh Repl.", "Produce", "Managed", "<PERSON>d", "Main Bank Checkout", "<PERSON><PERSON>", "Deli & Cheese", "Meat&Poultry", "Softlines", "Self Scanning", "Gm", "Cleaning", "Price In...", "Dotcom", "Stock", "Customer...", "Warehouse", "Warehouse...", "Cash Office"], "parents": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "textinfo": "label", "texttemplate": "%{label}<br>£%{value:,.0f}", "type": "treemap", "values": [********.82, ********.14, ********.4, ********.67, ********.2, ********.41, ********.75, ********.55, ********.79, 9784080.6, 9500663.13, 8978752.75, 8787339.87, 8407379.26, 7960587.31, 6891912.03, 4951623.94, 7919572.66, 7317680.29, 6860211.2]}], "layout": {"margin": {"b": 25, "l": 25, "r": 25, "t": 50}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go\n", "\n", "labels = [\"Grocery\", \"Fresh Repl.\", \"Produce\", \"Managed\", \"Srd\", \"Main Bank Checkout\", \"Bakery\", \"Deli & Cheese\", \n", "          \"Meat&Poultry\", \"Softlines\", \"Self Scanning\", \"Gm\", \"Cleaning\", \"Price In...\", \"Dotcom\", \"Stock\", \"Customer...\", \n", "          \"Warehouse\", \"Warehouse...\", \"Cash Office\"]\n", "values = [********.82, ********.14, ********.40, ********.67, ********.20, ********.41, ********.75, ********.55, \n", "          ********.79, 9784080.60, 9500663.13, 8978752.75, 8787339.87, 8407379.26, 7960587.31, 6891912.03, \n", "          4951623.94, 7919572.66, 7317680.29, 6860211.20]\n", "\n", "fig = go.Figure(go.Treemap(\n", "    labels = labels,\n", "    values = values,\n", "    parents = [\"\"] * len(labels),\n", "    textinfo = \"label\",\n", "    texttemplate = \"%{label}<br>£%{value:,.0f}\"  # Format without decimals and add GBP symbol\n", "))\n", "\n", "fig.update_layout(margin = dict(t=50, l=25, r=25, b=25))\n"]}, {"cell_type": "code", "execution_count": 3, "id": "98050baf-f2ff-4d39-bac6-b5c4a661d4b2", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"labels": ["Grocery", "Fresh Repl.", "Produce", "Managed", "<PERSON>d", "Main Bank Checkout", "<PERSON><PERSON>", "Deli & Cheese", "Meat&Poultry", "Softlines", "Self Scanning", "Gm", "Cleaning", "Price In...", "Dotcom", "Stock", "Customer...", "Warehouse", "Warehouse...", "Cash Office"], "parents": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "textinfo": "label", "texttemplate": "%{label}<br>£%{value:,.0f}", "tiling": {"packing": "squarify"}, "type": "treemap", "values": [********.82, ********.14, ********.4, ********.67, ********.2, ********.41, ********.75, ********.55, ********.79, 9784080.6, 9500663.13, 8978752.75, 8787339.87, 8407379.26, 7960587.31, 6891912.03, 4951623.94, 7919572.66, 7317680.29, 6860211.2]}], "layout": {"height": 600, "margin": {"b": 10, "l": 10, "r": 10, "t": 10}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 800}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go\n", "\n", "labels = [\"Grocery\", \"Fresh Repl.\", \"Produce\", \"Managed\", \"Srd\", \"Main Bank Checkout\", \"Bakery\", \"Deli & Cheese\", \n", "          \"Meat&Poultry\", \"Softlines\", \"Self Scanning\", \"Gm\", \"Cleaning\", \"Price In...\", \"Dotcom\", \"Stock\", \"Customer...\", \n", "          \"Warehouse\", \"Warehouse...\", \"Cash Office\"]\n", "values = [********.82, ********.14, ********.40, ********.67, ********.20, ********.41, ********.75, ********.55, \n", "          ********.79, 9784080.60, 9500663.13, 8978752.75, 8787339.87, 8407379.26, 7960587.31, 6891912.03, \n", "          4951623.94, 7919572.66, 7317680.29, 6860211.20]\n", "\n", "fig = go.Figure(go.Treemap(\n", "    labels = labels,\n", "    values = values,\n", "    parents = [\"\"] * len(labels),\n", "    textinfo = \"label\",\n", "    texttemplate = \"%{label}<br>£%{value:,.0f}\",  # Format without decimals and add GBP symbol\n", "    tiling = dict(packing='squarify')  # Use squarify tiling for more compact layout\n", "))\n", "\n", "fig.update_layout(\n", "    margin = dict(t=10, l=10, r=10, b=10),  # Reduce margins to fit more compactly\n", "    height=600,  # Adjust the height to your preference\n", "    width=800  # Adjust the width to your preference\n", ")\n"]}, {"cell_type": "code", "execution_count": 21, "id": "06a511f0-a815-44f2-9634-09be0be5307a", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"labels": ["Grocery", "Fresh Repl.", "Produce", "Managed", "<PERSON>d", "Main Bank Checkout", "<PERSON><PERSON>", "Deli & Cheese", "Meat&Poultry", "Softlines", "Self Scanning", "Gm", "Cleaning", "Price In...", "Dotcom", "Stock", "Customer...", "Warehouse", "Warehouse...", "Cash Office"], "parents": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "textfont": {"size": 14.***************}, "textinfo": "label", "texttemplate": "%{label}<br>£%{value:,.0f}", "tiling": {"packing": "squarify"}, "type": "treemap", "values": [********.82, ********.14, ********.4, ********.67, ********.2, ********.41, ********.75, ********.55, ********.79, 9784080.6, 9500663.13, 8978752.75, 8787339.87, 8407379.26, 7960587.31, 6891912.03, 4951623.94, 7919572.66, 7317680.29, 6860211.2]}], "layout": {"height": 600, "margin": {"b": 10, "l": 10, "r": 10, "t": 10}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 800}}, "image/png": "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", "text/html": ["<div>                            <div id=\"337054ba-2f40-43bc-87d2-5b41252e0045\" class=\"plotly-graph-div\" style=\"height:600px; width:800px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"337054ba-2f40-43bc-87d2-5b41252e0045\")) {                    Plotly.newPlot(                        \"337054ba-2f40-43bc-87d2-5b41252e0045\",                        [{\"labels\":[\"Grocery\",\"Fresh Repl.\",\"Produce\",\"Managed\",\"Srd\",\"Main Bank Checkout\",\"Bakery\",\"Deli & Cheese\",\"Meat&Poultry\",\"Softlines\",\"Self Scanning\",\"Gm\",\"Cleaning\",\"Price In...\",\"Dotcom\",\"Stock\",\"Customer...\",\"Warehouse\",\"Warehouse...\",\"Cash Office\"],\"parents\":[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],\"textinfo\":\"label\",\"texttemplate\":\"%{label}<br>£%{value:,.0f}\",\"tiling\":{\"packing\":\"squarify\"},\"values\":[********.82,********.14,********.4,********.67,********.2,********.41,********.75,********.55,********.79,9784080.6,9500663.13,8978752.75,8787339.87,8407379.26,7960587.31,6891912.03,4951623.94,7919572.66,7317680.29,6860211.2],\"type\":\"treemap\",\"textfont\":{\"size\":14.***************}}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"margin\":{\"t\":10,\"l\":10,\"r\":10,\"b\":10},\"height\":600,\"width\":800},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('337054ba-2f40-43bc-87d2-5b41252e0045');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go\n", "\n", "labels = [\"Grocery\", \"Fresh Repl.\", \"Produce\", \"Managed\", \"Srd\", \"Main Bank Checkout\", \"Bakery\", \"Deli & Cheese\",\n", "          \"Meat&Poultry\", \"Softlines\", \"Self Scanning\", \"Gm\", \"Cleaning\", \"Price In...\", \"Dotcom\", \"Stock\", \"Customer...\",\n", "          \"Warehouse\", \"Warehouse...\", \"Cash Office\"]\n", "values = [********.82, ********.14, ********.40, ********.67, ********.20, ********.41, ********.75, ********.55,\n", "          ********.79, 9784080.60, 9500663.13, 8978752.75, 8787339.87, 8407379.26, 7960587.31, 6891912.03,\n", "          4951623.94, 7919572.66, 7317680.29, 6860211.20]\n", "\n", "# Create the initial treemap chart\n", "fig = go.Figure(go.Treemap(\n", "    labels=labels,\n", "    values=values,\n", "    parents=[\"\"] * len(labels),\n", "    textinfo=\"label\",\n", "    texttemplate=\"%{label}<br>£%{value:,.0f}\",  # Format without decimals and add GBP symbol\n", "    tiling=dict(packing='squarify'),  # Use squarify tiling for more compact layout\n", "))\n", "\n", "# Get the initial text font size, or set a default value if not defined\n", "initial_text_size = fig.data[0].textfont.size or 12\n", "\n", "# Increase text size by 20%\n", "new_text_size = initial_text_size * 1.2\n", "\n", "# Update the chart with new text size\n", "fig.update_traces(textfont=dict(size=new_text_size))\n", "\n", "fig.update_layout(\n", "    margin=dict(t=10, l=10, r=10, b=10),  # Reduce margins to fit more compactly\n", "    height=600,  # Adjust the height to your preference\n", "    width=800  # Adjust the width to your preference\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": 22, "id": "4b52a3ba-11e6-407c-bcf3-49cd0f2faccf", "metadata": {}, "outputs": [{"data": {"text/plain": ["Treemap({\n", "    'labels': [Grocery, Fresh Repl., Produce, Managed, Srd, Main Bank Checkout,\n", "               Bakery, Deli & Cheese, Meat&Poultry, Softlines, Self Scanning, Gm,\n", "               Cleaning, Price In..., Dotcom, Stock, Customer..., Warehouse,\n", "               Warehouse..., Cash Office],\n", "    'parents': [, , , , , , , , , , , , , , , , , , , ],\n", "    'textfont': {'size': 14.***************},\n", "    'textinfo': 'label',\n", "    'texttemplate': '%{label}<br>£%{value:,.0f}',\n", "    'tiling': {'packing': 'squarify'},\n", "    'values': [********.82, ********.14, ********.4, ********.67, ********.2,\n", "               ********.41, ********.75, ********.55, ********.79, 9784080.6,\n", "               9500663.13, 8978752.75, 8787339.87, 8407379.26, 7960587.31,\n", "               6891912.03, 4951623.94, 7919572.66, 7317680.29, 6860211.2]\n", "})"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["fig.data[0]"]}, {"cell_type": "code", "execution_count": 20, "id": "ed94db2d-13a5-4728-a563-3740ff76990b", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"labels": ["Grocery", "Fresh Repl.", "Produce", "Managed", "<PERSON>d", "Main Bank Checkout", "<PERSON><PERSON>", "Deli & Cheese", "Meat&Poultry", "Softlines", "Self Scanning", "Gm", "Cleaning", "Price In...", "Dotcom", "Stock", "Customer...", "Warehouse", "Warehouse...", "Cash Office"], "parents": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], "textfont": {"size": [30, 17.**************, 16.***************, 15.***************, 14.***************, 28.***************, 24.**************, 13.***************, 13.***************, 13.***************, 13.*************, 12.**************, 12.***************, 12.***************, 12.***************, 11.**************, 10, 11.***************, 11.***************, 11.**************]}, "textinfo": "label", "texttemplate": "%{label}<br>£%{value:,.0f}", "tiling": {"packing": "squarify"}, "type": "treemap", "values": [********.82, ********.14, ********.4, ********.67, ********.2, ********.41, ********.75, ********.55, ********.79, 9784080.6, 9500663.13, 8978752.75, 8787339.87, 8407379.26, 7960587.31, 6891912.03, 4951623.94, 7919572.66, 7317680.29, 6860211.2]}], "layout": {"height": 600, "margin": {"b": 10, "l": 10, "r": 10, "t": 10}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.****************, "#46039f"], [0.****************, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 800}}, "image/png": "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", "text/html": ["<div>                            <div id=\"1326cda4-ebc3-46de-974d-4049e4cac491\" class=\"plotly-graph-div\" style=\"height:600px; width:800px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"1326cda4-ebc3-46de-974d-4049e4cac491\")) {                    Plotly.newPlot(                        \"1326cda4-ebc3-46de-974d-4049e4cac491\",                        [{\"labels\":[\"Grocery\",\"Fresh Repl.\",\"Produce\",\"Managed\",\"Srd\",\"Main Bank Checkout\",\"Bakery\",\"Deli & Cheese\",\"Meat&Poultry\",\"Softlines\",\"Self Scanning\",\"Gm\",\"Cleaning\",\"Price In...\",\"Dotcom\",\"Stock\",\"Customer...\",\"Warehouse\",\"Warehouse...\",\"Cash Office\"],\"parents\":[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],\"textinfo\":\"label\",\"texttemplate\":\"%{label}<br>£%{value:,.0f}\",\"tiling\":{\"packing\":\"squarify\"},\"values\":[********.82,********.14,********.4,********.67,********.2,********.41,********.75,********.55,********.79,9784080.6,9500663.13,8978752.75,8787339.87,8407379.26,7960587.31,6891912.03,4951623.94,7919572.66,7317680.29,6860211.2],\"type\":\"treemap\",\"textfont\":{\"size\":[30.0,17.**************,16.***************,15.***************,14.***************,28.***************,24.**************,13.***************,13.***************,13.***************,13.*************,12.**************,12.***************,12.***************,12.***************,11.**************,10.0,11.***************,11.***************,11.**************]}}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.****************,\"#46039f\"],[0.****************,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"margin\":{\"t\":10,\"l\":10,\"r\":10,\"b\":10},\"height\":600,\"width\":800},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('1326cda4-ebc3-46de-974d-4049e4cac491');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go\n", "\n", "labels = [\"Grocery\", \"Fresh Repl.\", \"Produce\", \"Managed\", \"Srd\", \"Main Bank Checkout\", \"Bakery\", \"Deli & Cheese\",\n", "          \"Meat&Poultry\", \"Softlines\", \"Self Scanning\", \"Gm\", \"Cleaning\", \"Price In...\", \"Dotcom\", \"Stock\", \"Customer...\",\n", "          \"Warehouse\", \"Warehouse...\", \"Cash Office\"]\n", "values = [********.82, ********.14, ********.40, ********.67, ********.20, ********.41, ********.75, ********.55,\n", "          ********.79, 9784080.60, 9500663.13, 8978752.75, 8787339.87, 8407379.26, 7960587.31, 6891912.03,\n", "          4951623.94, 7919572.66, 7317680.29, 6860211.20]\n", "\n", "# Define the minimum and maximum text sizes\n", "min_text_size = 10\n", "max_text_size = 30\n", "\n", "# Find the maximum and minimum values\n", "max_value = max(values)\n", "min_value = min(values)\n", "\n", "# Calculate text sizes based on the values using linear interpolation\n", "text_sizes = [min_text_size + (max_text_size - min_text_size) * ((value - min_value) / (max_value - min_value)) for value in values]\n", "\n", "# Create the treemap chart with proportional text sizes\n", "fig = go.Figure(go.Treemap(\n", "    labels=labels,\n", "    values=values,\n", "    parents=[\"\"] * len(labels),\n", "    textinfo=\"label\",\n", "    texttemplate=\"%{label}<br>£%{value:,.0f}\",  # Format without decimals and add GBP symbol\n", "    tiling=dict(packing='squarify'),  # Use squarify tiling for more compact layout\n", "))\n", "\n", "# Update the text font size with proportional sizes\n", "fig.update_traces(textfont=dict(size=text_sizes))\n", "\n", "fig.update_layout(\n", "    margin=dict(t=10, l=10, r=10, b=10),  # Reduce margins to fit more compactly\n", "    height=600,  # Adjust the height to your preference\n", "    width=800  # Adjust the width to your preference\n", ")\n", "\n", "fig.show()\n"]}], "metadata": {"kernelspec": {"display_name": "taipy_env", "language": "python", "name": "taipy_env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}