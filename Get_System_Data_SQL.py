import pandas as pd
import numpy as np
from pathlib import Path
import pyodbc
import pyarrow as pa
import pyarrow.parquet as pq
from re import search  # searching substring in fullstring
from zipfile import ZipFile
from os import listdir
import os
from os.path import isfile, join
import Replenishment_Model_Functions_25 as rmf
import warnings
import shutil
import math
import polars as pl
import json
from SSH.SSH_volume_functions import ssh_table_create, ssh_downloader
import ad_hoc_py.packaging_type_table_transforming as ob
from datetime import datetime, timedelta
import re


warnings.filterwarnings("ignore")


# =============================================================================
# Json settings
# =============================================================================
try:
    # Try to use __file__ to get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    # Fallback to using the current working directory
    script_dir = os.getcwd()

# Construct the path to the config file one level up from the script directory or current working directory
config_path = os.path.join(script_dir,'config.json')

# Print the constructed path for debugging purposes
# print(f"Config path: {config_path}")

# Load the configuration from the JSON file

with open(config_path, 'r') as file:
    config = json.load(file)



ODBC_CONN = config['ODBC_connection']
######################################


def item_SQL(start, end, pmg, countries, nr_weeks, saved_name, place_to_save, stores):

    print("Sold Item Downloading has started")

    def isold():

        with pyodbc.connect(
            ODBC_CONN, autocommit=True, Trusted_Connection="yes"
        ) as conn:

            # chunksize=300000 # this is the number of lines
            collector = pd.DataFrame()

            index = 0
            s = list()
            for x in stores:

                s.append(str(x))
                index += 1

                if index % 100 == 0 or (len(stores) == index):

                    stores_string = ",".join(s)
                    print(f"{stores_string} to download!")


                    item_sold = """
                    SELECT 
                    cast(stores.dmst_store_code as INT) AS store,
                    cal.dtdw_day_desc_en as day,
                    hier.pmg AS pmg,
                    /*mstr.dmat_div_des_en AS DIV_DESC,
                    cast(mstr.dmat_div_code as INT) as DIV_ID,
                    mstr.dmat_dep_des_en AS DEP_DESC,
                    cast(mstr.dmat_dep_code as INT) as DEP_ID,
                    mstr.dmat_sec_des_en AS SEC_DESC,
                    cast(mstr.dmat_sec_code as INT) as SEC_ID,
                    mstr.dmat_grp_des_en AS GRP_DESC,
                    cast(mstr.dmat_grp_code as INT) as GRP_ID,
                    mstr.dmat_sgr_des_en AS SGR_DESC,
                    cast(mstr.dmat_sgr_code as INT) as SGR_ID,*/
                    cast(mstr.slad_tpnb as INT) AS tpnb,
                    /*mstr.slad_tpn AS tpn,
                    mstr.own_brand as ownbrand,
                    mstr.slad_long_des AS product_name,
                    mstr.slad_unit AS unit_type,
                    mstr.slad_case_size AS case_capacity,
                    mstr.slad_net_weight AS weight,*/
                    SUM(sunit.slsms_unit)/{nr_weeks} AS sold_units,
                    SUM(sunit.slsms_salex)/{nr_weeks} AS sales_excl_vat 
                    
                    FROM dw.sl_sms sunit 
                    
                    JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id 
                    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
                    JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id
                    AND mstr.cntr_id = sunit.slsms_cntr_id 
                    JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
                    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
                    
                    WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
                    AND stores.cntr_code IN {countries}
                    AND sunit.slsms_unit > 0 
                    AND sunit.slsms_salex > 0 
                    AND SUBSTRING(hier.pmg, 1, 3) IN {pmg}
                    AND stores.dmst_store_code in ({selected_stores})
                    AND stores.convenience IN ('Convenience', 'HM')
                    
                    GROUP BY  stores.dmst_store_code,
                    cal.dtdw_day_desc_en, 
                    hier.pmg,
                    /*mstr.dmat_div_des_en,
                    mstr.dmat_div_code,
                    mstr.dmat_dep_des_en,
                    mstr.dmat_dep_code,
                    mstr.dmat_sec_des_en,
                    mstr.dmat_sec_code,
                    mstr.dmat_grp_des_en,
                    mstr.dmat_grp_code,
                    mstr.dmat_sgr_des_en,
                    mstr.dmat_sgr_code,
                    mstr.slad_tpn,*/
                    mstr.slad_tpnb
                    /*mstr.own_brand,
                    mstr.slad_long_des,
                    mstr.slad_unit,
                    mstr.slad_case_size,
                    mstr.slad_net_weight*/
                    
                    ORDER BY  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb
                        """.format(
                        start=start,
                        end=end,
                        countries=countries,
                        pmg=pmg,
                        nr_weeks=nr_weeks,
                        selected_stores=stores_string,
                    )

                    collector = pd.concat([collector, pd.read_sql(item_sold, conn)])



                    s = list()
                    
            collector.to_parquet(
                place_to_save / f"isold_{saved_name}", compression="gzip")

            # pqwriter = None

            # table = pa.Table.from_pandas(collector)

            # # create a parquet write object giving it an output file
            # pqwriter = pq.ParquetWriter(
            #     place_to_save / f"item_{saved_name}",
            #     table.schema,
            #     compression="gzip",
            # )
            # pqwriter.write_table(table)

            # # close the parquet writer
            # if pqwriter:
            #     pqwriter.close()

    def isold_dotcom():
        with pyodbc.connect(
            ODBC_CONN, autocommit=True, Trusted_Connection="yes"
        ) as conn:

            # chunksize=300000 # this is the number of lines
            collector = pd.DataFrame()

            index = 0
            s = list()
            for x in stores:

                s.append(str(x))
                index += 1

                if index % 50 == 0 or (len(stores) == index):

                    stores_string = ",".join(s)
                    print(f"{stores_string} to download!")

                    isold_dotcom = """
                    
                    SELECT 
                    cast(stores.dmst_store_code as INT) AS store,
                    cal.dtdw_day_desc_en as day,
                    hier.pmg AS pmg,
                    cast(mstr.slad_tpnb as INT) AS tpnb,
                    SUM(sunit.sltrg_tr_unit)/{nr_weeks} AS sold_units_dotcom
                    
                    FROM dw.sl_trg sunit 
                    
                    JOIN dm.dim_stores stores ON stores.cntr_id = sunit.sltrg_cntr_id AND stores.dmst_store_id = sunit.sltrg_dmst_id 
                    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
                    JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.sltrg_dmat_id
                    AND mstr.cntr_id = sunit.sltrg_cntr_id 
                    JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
                    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
                    
                    WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
                    AND stores.cntr_code IN {countries}
                    AND sunit.sltrg_tr_unit > 0 
                    AND SUBSTRING(hier.pmg, 1, 3) IN {pmg}
                    AND stores.dmst_store_code in ({selected_stores})
                    /*AND stores.convenience IN ('Convenience', 'HM')*/
                    
                    GROUP BY  stores.dmst_store_code, cal.dtdw_day_desc_en,  hier.pmg,  mstr.slad_tpnb
                    ORDER BY  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb, cal.dtdw_day_desc_en
                    """.format(
                        start=start,
                        end=end,
                        countries=countries,
                        pmg=pmg,
                        nr_weeks=nr_weeks,
                        selected_stores=stores_string,
                    )

                    collector = pd.concat([collector, pd.read_sql(isold_dotcom, conn)])


                    s = list()
                    
                    
            collector.to_parquet(
                place_to_save / f"isold_dotcom_{saved_name}", compression="gzip")

            # conn.close()

            # pqwriter = None

            # table = pa.Table.from_pandas(collector)

            # # create a parquet write object giving it an output file
            # pqwriter = pq.ParquetWriter(
            #     place_to_save / f"isold_dotcom_{saved_name}",
            #     table.schema,
            #     compression="gzip",
            # )
            # pqwriter.write_table(table)

            # # close the parquet writer
            # if pqwriter:
            #     pqwriter.close()

    return (
        isold(),
        print("Part 1 - Item Sold wo dotcom done"),
        isold_dotcom(),
        print("Part 2 - Item Sold dotcom done"),
        print("Sold Item Downloading Done"),
    )


def stock_SQL(start, end, pmg, countries, nr_weeks, saved_name, place_to_save, stores):

    print("Stock Downloading has started")

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        # chunksize=300000 # this is the number of lines
        collector = pd.DataFrame()

        index = 0
        s = list()
        for x in stores:

            s.append(str(x))
            index += 1

            if index % 50 == 0 or (len(stores) == index):

                stores_string = ",".join(s)
                print(f"{stores_string} is done!")

                stock = """
                SELECT 
                CAST(stores.dmst_store_code AS INT) AS store,
                cal.dtdw_day_desc_en as day, hier.pmg AS pmg,
                CAST(mstr.slad_tpnb AS INT) AS tpnb,
                SUM(stock.slstks_stock_unit_sl)/{nr_weeks} AS stock,
                AVG(stock.slstks_price) as item_price
                
                FROM dw.sl_stocks stock
                
                JOIN dm.dim_stores stores ON stock.slstks_dmst_id = stores.dmst_store_id AND stock.slstks_cntr_id = stores.cntr_id
                JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = stock.slstks_dmat_id AND mstr.cntr_id = stock.slstks_cntr_id
                LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = stock.part_col
                JOIN tesco_analysts.hierarchy_spm hier
                ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0")
                AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0")
                AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0")
                AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0")
                AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
                
                WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
                AND stores.cntr_code IN {countries}
                AND SUBSTRING(hier.pmg, 1, 3) IN {pmg}
                AND stores.dmst_store_code in ({selected_stores})
                AND stock.slstks_stock_unit_sl > 0
                
                GROUP BY stores.dmst_store_code, hier.pmg, cal.dtdw_day_desc_en, mstr.slad_tpnb
                ORDER BY stores.dmst_store_code, hier.pmg, mstr.slad_tpnb
                    """.format(
                    start=start,
                    end=end,
                    countries=countries,
                    pmg=pmg,
                    nr_weeks=nr_weeks,
                    selected_stores=stores_string,
                )

                collector = pd.concat([collector, pd.read_sql(stock, conn)])

                s = list()
        conn.close()

        pqwriter = None

        table = pa.Table.from_pandas(collector)

        # create a parquet write object giving it an output file
        pqwriter = pq.ParquetWriter(
            place_to_save / f"stock_{saved_name}",
            table.schema,
            compression="gzip",
        )
        pqwriter.write_table(table)

        # close the parquet writer
        if pqwriter:
            pqwriter.close()

    print("Stock Downloading Done")


def cases_SQL(start, end, pmg, countries, nr_weeks, saved_name, place_to_save, stores):

    print("Cases Downloading has started")

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        chunksize = 300000  # this is the number of lines
        collector = pd.DataFrame()
        for x in ["('HU')", "('SK')", "('CZ')"]:

            Cases_delivered = """
           
           select 
           CAST(CONCAT(a.int_cntr_id,a.store) AS INT) as store,
           cal.dtdw_day_desc_en as day,
           CAST(a.product as INT) as tpnb,
           b.slad_long_des AS product_name,
           d.pmg AS pmg,
           SUM(a.qty)/{nr_weeks} as unit, 
           AVG(b.slad_case_size) as artgld_case_capacity
           
           from stg_go.go_106_order_receiving a
           
           LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product and b.cntr_id = a.int_cntr_id
           Right JOIN tesco_analysts.hierarchy_spm d ON b.dmat_div_code = lpad(d.div_code,4,"0")
           AND b.dmat_dep_code = lpad(d.dep_code,4,"0")
           AND b.dmat_sec_code = lpad(d.sec_code,4,"0")
           AND b.dmat_grp_code = lpad(d.grp_code,4,"0")
           AND b.dmat_sgr_code = lpad(d.sgr_code,4,"0")
           LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
           
           Where SUBSTRING(d.pmg, 1, 3) IN {pmg}
           and a.int_cntr_code in {countries}
           and CONCAT(a.int_cntr_id,a.store) in {stores}
           and cal.dmtm_fw_code BETWEEN {start} AND {end}
           
           GROUP BY  CONCAT(a.int_cntr_id,a.store), cal.dtdw_day_desc_en, a.product, b.slad_long_des,  d.pmg
           ORDER BY  CONCAT(a.int_cntr_id,a.store),cal.dtdw_day_desc_en,  d.pmg
           
                      
               
           """.format(
                start=start,
                end=end,
                pmg=pmg,
                nr_weeks=nr_weeks,
                stores=stores,
                countries=countries,
                x=x,
            )

            for i, df in enumerate(
                pd.read_sql(Cases_delivered, conn, chunksize=chunksize)
            ):
                collector = pd.concat([collector, df])

        pqwriter = None

        table = pa.Table.from_pandas(collector)
        # create a parquet write object giving it an output file
        pqwriter = pq.ParquetWriter(
            place_to_save / f"cases_{saved_name}",
            table.schema,
            compression="gzip",
        )
        pqwriter.write_table(table)

        # close the parquet writer
        if pqwriter:
            pqwriter.close()

        conn.close()

    print("Cases Downloading Done")


def opsdev_compiling(directory, place_to_save, saved_filename):
    
    print("""\n
          JDA/Planogram report starts to download.....
          \n""")
    
    SRD_tables = SRD_database()

    country_cond = [
        SRD_tables["Country"] == "HU",
        SRD_tables["Country"] == "SK",
        SRD_tables["Country"] == "CZ",
    ]

    store_code_create = [
        str(4) + SRD_tables.Store_Number.astype(str),
        str(2) + SRD_tables.Store_Number.astype(str),
        str(1) + SRD_tables.Store_Number.astype(str),
    ]


    SRD_tables["Store_Number"] = np.select(country_cond, store_code_create, 0)


    opsdev = SRD_tables[["Country", "Product_id"]].drop_duplicates()
    opsdev = opsdev[pd.to_numeric(opsdev["Product_id"], errors="coerce").notnull()].astype(
        {"Product_id": "int64"}
    )
    

    # path_srp = Path(r"\\huprgvmfs04.hu.tesco-europe.com\SRD-közös\Egyéb\Balázsnak")
    # # create a list with files in choosen folder

    # onlyfiles = [
    #     f
    #     for f in listdir(path_srp)
    #     if (isfile(join(path_srp, f)) and search("txt", f.lower()))
    # ]

    # # create an empty DataFrame and fill up with countries

    # opsdev = pd.DataFrame()

    # for index, filename in enumerate(onlyfiles):
    #     print(f"Collecting SRP Data: {filename}")

    #     df = pd.read_csv(join(path_srp, filename), sep="|", low_memory=False)
    #     if search("CZ", filename):

    #         df["Store_Number"] = str(1) + df.Store_Number.astype(str)
    #     elif search("SK", filename):

    #         df["Store_Number"] = str(2) + df.Store_Number.astype(str)
    #     else:

    #         df["Store_Number"] = str(4) + df.Store_Number.astype(str)
    #     opsdev = pd.concat([opsdev, df], ignore_index=True)
    #     no_need = ["1nan", "2nan", "4nan"]
    #     opsdev = opsdev.loc[~opsdev.Store_Number.isin(no_need)]
    #     # print(filename + ' imported. CE lines: ' + str(CE.shape[0]))

    # list of repl_type
    srp = ["Tray", "Case"]
    nsrp = ["Unit", "Display", "Alternate", "Loose", "Log Stack"]
    pallet_info = ["Half_Pallet", "Pallet", "Split_Pallet"]

    # create columns for repl_type

    opsdev["full_pallet"] = np.where(opsdev.Pallet_info == "Pallet", 1, 0)
    opsdev["mu"] = np.where(opsdev.Pallet_info == "Half_Pallet", 1, 0)
    opsdev["split_pallet"] = np.where(opsdev.Pallet_info == "Split_Pallet", 1, 0)
    opsdev["nsrp"] = np.where(
        (opsdev.merchstyle_string.isin(nsrp)) & (~opsdev.Pallet_info.isin(pallet_info)),
        1,
        0,
    )
    opsdev["srp"] = np.where(opsdev.merchstyle_string.isin(srp), 1, 0)
    opsdev["srp"] = np.where((opsdev.Pallet_info == "Split_Pallet"), 0, opsdev.srp)
    opsdev["srp"] = np.where(
        (opsdev.Pallet_info == "Pallet") | (opsdev.Pallet_info == "Half_Pallet"),
        0,
        opsdev.srp,
    )

    # # create ownbrand flag

    # opsdev['ownbrand_flag'] = np.where(opsdev.ownbrand.notnull(), 1, 0)

    # rename columns

    opsdev.rename(
        columns={
            "Store_Number": "store",
            "Product_id": "tpnb",
            "Product_Name": "product_name",
            "Displaygroup_description": "display_group",
            "Displaygroup": "drg",
        },
        inplace=True,
    )
    opsdev["checkout_stand_flag"] = np.where(
        opsdev["display_group"].str.lower().str.contains("checkout"), 1, 0
    )
    opsdev["clipstrip_flag"] = np.where(
        opsdev["display_group"].str.lower().str.contains("clipstrip"), 1, 0
    )
    opsdev["backroom_flag"] = np.where(opsdev["drg"].isin(["Z2D", "L1N"]), 1, 0)
    opsdev["store"] = opsdev["store"].astype(float).astype(int)
    opsdev["tpnb"] = (
        pd.to_numeric(opsdev.tpnb, errors="coerce").fillna(0).astype(float).astype(int)
    )
    checkout_stand = opsdev[opsdev.checkout_stand_flag == 1][
        ["store", "tpnb"]
    ].drop_duplicates()
    checkout_stand["checkout_stand_flag"] = int(1)
    clipstrip_flag = opsdev[opsdev.clipstrip_flag == 1][
        ["store", "tpnb"]
    ].drop_duplicates()
    clipstrip_flag["clipstrip_flag"] = int(1)
    backroom_flag = opsdev[opsdev.backroom_flag == 1][
        ["store", "tpnb"]
    ].drop_duplicates()
    backroom_flag["backroom_flag"] = int(1)

    opsdev = opsdev[
        [
            "store",
            "tpnb",
            "product_name",
            "srp",
            "nsrp",
            "mu",
            "full_pallet",
            "split_pallet",
        ]
    ]
    # opsdev.drop("checkout_stand_flag", axis = 1, inplace = True)
    opsdev = opsdev.merge(checkout_stand, on=["store", "tpnb"], how="left")
    opsdev["checkout_stand_flag"].replace(np.nan, 0, inplace=True)
    opsdev = opsdev.merge(clipstrip_flag, on=["store", "tpnb"], how="left")
    opsdev["clipstrip_flag"].replace(np.nan, 0, inplace=True)
    opsdev = opsdev.merge(backroom_flag, on=["store", "tpnb"], how="left")
    opsdev["backroom_flag"].replace(np.nan, 0, inplace=True)

    # get final opsdev looking

    opsdev = opsdev.drop_duplicates()

    # filter duplicates and form the dataframe again

    grouped_opsdev = (
        opsdev.groupby(["store", "tpnb", "product_name"])
        .agg(
            {
                "srp": "sum",
                "nsrp": "sum",
                "mu": "sum",
                "full_pallet": "sum",
                "split_pallet": "sum",
                "checkout_stand_flag": "sum",
                "clipstrip_flag": "sum",
                "backroom_flag": "sum",
            }
        )
        .reset_index()
    )
    grouped_opsdev["total"] = (
        grouped_opsdev.srp
        + grouped_opsdev.nsrp
        + grouped_opsdev.mu
        + grouped_opsdev.full_pallet
        + grouped_opsdev.split_pallet
    )
    grouped_opsdev["full_pallet"] = np.where(
        (grouped_opsdev.full_pallet == 1) & (grouped_opsdev.total > 1),
        1,
        grouped_opsdev.full_pallet,
    )
    grouped_opsdev["mu"] = np.where(
        (grouped_opsdev.total > 1) & (grouped_opsdev.full_pallet == 1),
        0,
        grouped_opsdev.mu,
    )
    grouped_opsdev["split_pallet"] = np.where(
        (grouped_opsdev.total > 1) & (grouped_opsdev.full_pallet == 1)
        | (grouped_opsdev.mu == 1),
        0,
        grouped_opsdev.split_pallet,
    )
    grouped_opsdev["nsrp"] = np.where(
        (grouped_opsdev.total > 1)
        & (
            (grouped_opsdev.full_pallet == 1)
            | (grouped_opsdev.mu == 1)
            | grouped_opsdev.split_pallet
            == 1
        )
        | (grouped_opsdev.srp == 1),
        0,
        grouped_opsdev.nsrp,
    )
    grouped_opsdev["srp"] = np.where(
        (grouped_opsdev.total > 1)
        & (
            (grouped_opsdev.full_pallet == 1)
            | (grouped_opsdev.mu == 1)
            | grouped_opsdev.split_pallet
            == 1
        ),
        0,
        grouped_opsdev.srp,
    )
    grouped_opsdev["total"] = (
        grouped_opsdev.srp
        + grouped_opsdev.nsrp
        + grouped_opsdev.mu
        + grouped_opsdev.full_pallet
        + grouped_opsdev.split_pallet
    )

    # check where is duplicates
    dupl_rows = grouped_opsdev[grouped_opsdev.total > 1].shape[0]
    print(f"We have: {dupl_rows} rows duplicated")

    # final dataframe
    grouped_opsdev.drop(columns={"total"}, inplace=True)
    opsdev = grouped_opsdev.copy()
    opsdev["checkout_stand_flag"] = np.where(opsdev["checkout_stand_flag"] > 0, 1, 0)
    opsdev["clipstrip_flag"] = np.where(opsdev["clipstrip_flag"] > 0, 1, 0)
    opsdev["backroom_flag"] = np.where(opsdev["backroom_flag"] > 0, 1, 0)

    import os

    # checking if the directory demo_folder2
    # exist or not.
    if not os.path.isdir(place_to_save):

        # if the demo_folder2 directory is
        # not present then create it.
        os.makedirs(place_to_save)

    # create a zip

    opsdev.to_parquet(
        place_to_save / f"opsdev_{saved_filename}",
        index=False,
        compression="gzip",
    )

    return opsdev


# def planogram_compiling(date_plan, saved_filename, place_to_save, more_weeks):

#     print("Planogram compiling has been started....")

#     ########################################### Collecting data PART I ############################################################
#     def check_closest_of_date(date_plan):

#         list_date = []
#         list_min_date = []
#         countries = ["HU", "SK", "CZ"]

#         for c in countries:
#             for a in listdir("//PVCKBSQLDB001{c}.global.tesco.org/BACKUP/".format(c=c)):
#                 if a[:8].isdigit():

#                     integer = int(a[:8])
#                     list_date.append(integer)

#             list_min_date.append(min(list_date, key=lambda x: abs(x - date_plan)))

#             return list_min_date[0]

#     date = check_closest_of_date(date_plan)
#     countries = ["HU", "SK", "CZ"]

#     for c in countries:
#         # Create a ZipFile Object and load sample.zip in it
#         with ZipFile(
#             "//PVCKBSQLDB001{c}.global.tesco.org/BACKUP/{date}.7z".format(
#                 date=date, c=c
#             ),
#             "r",
#         ) as zipObj:
#             # Get a list of all archived file names from the zip
#             listOfFileNames = zipObj.namelist()
#             # Iterate over the file names
#             for fileName in listOfFileNames:
#                 # Check filename endswith csv
#                 if search(
#                     "{date}/FutureDatedShelfCap_All_Stores_{date}".format(date=date),
#                     fileName,
#                 ) and fileName.endswith(".txt"):

#                     # Extract a single file from zip

#                     zipObj.extract(fileName, place_to_save / f"{saved_filename}")
#                     old_file = os.path.join(
#                         place_to_save / f"{saved_filename}" / f"{date}", os.path.basename(fileName)
#                     )
#                     new_file = os.path.join(
#                         place_to_save / f"{saved_filename}",
#                         f"FutureDatedShelfCap_All_Stores_{date}" + f"_{c}" + ".txt",
#                     )
#                     os.rename(old_file, new_file)

#                     print(new_file[-46:])

#     ########################################### Compiling data to one dataset PART II ############################################################
#     def planogram_total(df_outer, cntr_code, cntr_id):
#         df = df_outer.copy()
#         to_drop = ["DRG ID", "DRG name", "DRC ID", "DRC name", "Hyper DRG", "Supplier"]
#         for d in to_drop:
#             try:
#                 df = df.drop([d], axis=1)
#             except:
#                 pass

#         df = df.rename(
#             index=str,
#             columns={
#                 "Product TPN": "tpnb",
#                 "EAN code": "ean",
#                 "Product name": "pname",
#                 "Product status": "status",
#                 "Total Case Pack Size": "icase",
#             },
#         )
#         df = pd.melt(
#             df,
#             id_vars=["tpnb", "ean", "pname", "status", "icase"],
#             var_name="store",
#             value_name="capacity",
#         )
#         df = df.replace(np.nan, 0)
#         df["country"] = cntr_id
#         df["store"] = str(cntr_code) + df["store"].str[-4:]
#         df["tpnb"] = df["tpnb"].astype(str)
#         df["ean"] = df["ean"].astype(str)
#         df["icase"] = df["icase"].astype(int)
#         df["store"] = df["store"].astype(int)
#         df["capacity"] = df["capacity"].astype(int)
#         # df = df[['country','tpnb', 'icase']].drop_duplicates()
#         # df = df.loc[df.tpnb.str.isnumeric()]
#         return df

#     # create a list with files in choosen folder
#     onlyfiles = (
#         f
#         for f in listdir(place_to_save / f"{saved_filename}")
#         if (
#             isfile(join(place_to_save / f"{saved_filename}", f))
#             and search("txt", f.lower())
#             and search(f"{date}", f)
#         )
#     )

#     files_list = [file for file in onlyfiles]
#     planogram = pd.DataFrame()

#     i = 1
#     for index, filename in enumerate(files_list):
#         df = pd.read_csv(
#             join(place_to_save / f"{saved_filename}", filename),
#             sep="|",
#             encoding="unicode_escape",
#             low_memory=False,
#         )
#         if search("CZ", filename):
#             df = planogram_total(df, 1, "CZ")

#         elif search("SK", filename):
#             df = planogram_total(df, 2, "SK")

#         else:
#             df = planogram_total(df, 4, "HU")
#         print(filename + " planogram created")

#         planogram = pd.concat([planogram, df], ignore_index=True)
#         print(str(i) + " files appended")
#         i += 1
#     # plano_CE = plano_CE.append(plano_PL, ignore_index = True)
#     planogram = planogram[planogram["status"].str.contains("Live") == True]
#     planogram = (
#         (planogram[planogram.capacity > 0])
#         .sort_values(by=["store"])
#         .reset_index(drop=True)
#     )
#     planogram["pname"] = planogram["pname"]
#     planogram["status"] = planogram["status"]
#     planogram["country"] = planogram["country"]
    
    
#     if not more_weeks:

#         planogram.to_csv(
#             place_to_save / f"{saved_filename}" / f"planogram_{saved_filename}.csv.gz",
#             index=False,
#             compression="gzip",
#         )

#     for c in countries:
#         os.remove(
#             os.path.join(
#                 place_to_save / f"{saved_filename}",
#                 f"FutureDatedShelfCap_All_Stores_{date}" + f"_{c}" + ".txt",
#             )
#         )
        
        
        
        
#     try:
#         path_to_remove = place_to_save / f'{saved_filename}/{date}'.replace('/', '\\')
#         command = f'rmdir /S /Q "{path_to_remove}"'
#         result = os.system(command)
#         if result == 0:
#             print(f"Successfully removed directory: {path_to_remove}")
#         else:
#             print(f"Failed to remove directory. Error code: {result}")
#     except Exception as e:
#         print(f"An error occurred: {e}")
#     # try:
#     #     os.rmdir(place_to_save / f"{saved_filename}/{date}")
#     # except:
#     #     pass

#     return planogram



def planogram_compiling(date_plan, saved_filename, place_to_save, more_weeks):

    print("Planogram compiling has been started....")

    ########################################### Collecting data PART I ############################################################
    def check_closest_of_date_by_country(date_plan):
        """Find the closest date to date_plan for each country separately"""

        # Updated server mappings
        server_mapping = {
            "CZ": "//TPC96700BG1148.tgrc.tesco.org/Backup/",
            "SK": "//TPC96702bg1148.tgrc.tesco.org/BACKUP/",
            "HU": "//TPC96705BG1148.tgrc.tesco.org/Backup/"
        }

        country_dates = {}

        for country, server_path in server_mapping.items():
            try:
                files = listdir(server_path)
                country_date_list = []

                for a in files:
                    if a[:8].isdigit():
                        integer = int(a[:8])
                        country_date_list.append(integer)

                if country_date_list:
                    closest_date = min(country_date_list, key=lambda x: abs(x - date_plan))
                    country_dates[country] = closest_date
                    print(f"Closest date for {country}: {closest_date}")
                else:
                    print(f"No valid date files found for {country}")

            except Exception as e:
                print(f"Error accessing {server_path}: {e}")
                continue

        return country_dates

    country_dates = check_closest_of_date_by_country(date_plan)

    if not country_dates:
        print("No valid dates found for any country, cannot proceed with planogram compilation")
        return pd.DataFrame()

    # Updated server mappings
    server_mapping = {
        "CZ": "//TPC96700BG1148.tgrc.tesco.org/BACKUP/",
        "SK": "//TPC96702bg1148.tgrc.tesco.org/BACKUP/",
        "HU": "//TPC96705BG1148.tgrc.tesco.org/BACKUP/"
    }

    files_extracted = 0
    for country, server_path in server_mapping.items():
        if country not in country_dates:
            print(f"No date found for {country}, skipping")
            continue

        country_date = country_dates[country]
        zip_file_path = f"{server_path}{country_date}.7z"

        try:
            # Create a ZipFile Object and load sample.7z in it
            with ZipFile(zip_file_path, "r") as zipObj:
                # Get a list of all archived file names from the zip
                listOfFileNames = zipObj.namelist()

                # Iterate over the file names and find the closest date to date_plan
                matching_files = []
                file_dates = {}

                for fileName in listOfFileNames:
                    # Look for FutureDatedShelfCap_All_Stores files (not the SO or GM variants)
                    if ("FutureDatedShelfCap_All_Stores_" in fileName) and fileName.endswith(".txt") and not ("SO" in fileName or "GM" in fileName):
                        # Extract date from filename
                        import re
                        date_match = re.search(r'FutureDatedShelfCap_All_Stores_(\d{8})', fileName)
                        if date_match:
                            file_date = int(date_match.group(1))
                            file_dates[fileName] = file_date

                # Find the file with the closest date to date_plan for this country
                if file_dates:
                    closest_file = min(file_dates.keys(), key=lambda f: abs(file_dates[f] - date_plan))
                    matching_files = [closest_file]
                    print(f"Selected closest file for {country}: {closest_file} (date: {file_dates[closest_file]})")

                for fileName in matching_files:
                    # Extract a single file from zip
                    zipObj.extract(fileName, place_to_save / f"{saved_filename}")
                    old_file = os.path.join(
                        place_to_save / f"{saved_filename}" / f"{country_date}", os.path.basename(fileName)
                    )
                    # Extract the date from the original filename for the new filename
                    base_name = os.path.basename(fileName)
                    # Extract date from filename like "FutureDatedShelfCap_All_Stores_20250817_211730.txt"
                    date_match = re.search(r'FutureDatedShelfCap_All_Stores_(\d{8})', base_name)
                    file_date = date_match.group(1) if date_match else str(country_date)

                    new_file = os.path.join(
                        place_to_save / f"{saved_filename}",
                        f"FutureDatedShelfCap_All_Stores_{file_date}" + f"_{country}" + ".txt",
                    )
                    os.rename(old_file, new_file)
                    files_extracted += 1
                    print(f"Extracted: {new_file[-46:]}")

        except Exception as e:
            print(f"Error processing {zip_file_path}: {e}")
            continue

    ########################################### Compiling data to one dataset PART II ############################################################
    def planogram_total(df_outer, cntr_code, cntr_id):
        df = df_outer.copy()
        to_drop = ["DRG ID", "DRG name", "DRC ID", "DRC name", "Hyper DRG", "Supplier"]
        for d in to_drop:
            try:
                df = df.drop([d], axis=1)
            except:
                pass

        df = df.rename(
            index=str,
            columns={
                "Product TPN": "tpnb",
                "EAN code": "ean",
                "Product name": "pname",
                "Product status": "status",
                "Total Case Pack Size": "icase",
            },
        )
        df = pd.melt(
            df,
            id_vars=["tpnb", "ean", "pname", "status", "icase"],
            var_name="store",
            value_name="capacity",
        )
        df = df.replace(np.nan, 0)
        df["country"] = cntr_id
        df["store"] = str(cntr_code) + df["store"].str[-4:]
        df["tpnb"] = df["tpnb"].astype(str)
        df["ean"] = df["ean"].astype(str)
        df["icase"] = df["icase"].astype(int)
        df["store"] = df["store"].astype(int)
        df["capacity"] = df["capacity"].astype(int)
        # df = df[['country','tpnb', 'icase']].drop_duplicates()
        # df = df.loc[df.tpnb.str.isnumeric()]
        return df

    # create a list with files in choosen folder
    folder_path = place_to_save / f"{saved_filename}"

    try:
        all_files = listdir(folder_path)
    except Exception as e:
        print(f"Error listing files in {folder_path}: {e}")
        return pd.DataFrame()

    onlyfiles = (
        f
        for f in all_files
        if (
            isfile(join(folder_path, f))
            and search("txt", f.lower())
            and search("FutureDatedShelfCap_All_Stores_", f)
            and not search("GM", f)
            and not search("SO", f)
        )
    )

    files_list = [file for file in onlyfiles]

    if not files_list:
        print("No matching files found for processing")
        return pd.DataFrame()

    planogram = pd.DataFrame()

    i = 1
    for filename in files_list:
        try:
            df = pd.read_csv(
                join(folder_path, filename),
                sep="|",
                encoding="unicode_escape",
                low_memory=False,
            )

            if search("CZ", filename):
                df = planogram_total(df, 1, "CZ")
            elif search("SK", filename):
                df = planogram_total(df, 2, "SK")
            else:
                df = planogram_total(df, 4, "HU")

            print(filename + " planogram created")

            planogram = pd.concat([planogram, df], ignore_index=True)
            print(str(i) + " files appended")
            i += 1
        except Exception as e:
            print(f"Error processing file {filename}: {e}")
            continue
    # plano_CE = plano_CE.append(plano_PL, ignore_index = True)
    if planogram.empty:
        print("Planogram DataFrame is empty, returning empty DataFrame")
        return pd.DataFrame()

    if "status" not in planogram.columns:
        print("Warning: 'status' column not found in planogram DataFrame")
        return planogram  # Return without filtering if status column is missing

    planogram = planogram[planogram["status"].str.contains("Live") == True]
    planogram = (
        (planogram[planogram.capacity > 0])
        .sort_values(by=["store"])
        .reset_index(drop=True)
    )
    planogram["pname"] = planogram["pname"]
    planogram["status"] = planogram["status"]
    planogram["country"] = planogram["country"]
    
    
    if not more_weeks:

        planogram.to_parquet(
            place_to_save / f"{saved_filename}" / f"planogram_{saved_filename}",
            compression="gzip",
        )

    # Clean up extracted files
    try:
        # Find and remove the extracted files
        for f in listdir(place_to_save / f"{saved_filename}"):
            if (f.startswith("FutureDatedShelfCap_All_Stores_") and
                f.endswith(".txt") and
                ("_CZ.txt" in f or "_SK.txt" in f or "_HU.txt" in f)):
                file_to_remove = os.path.join(place_to_save / f"{saved_filename}", f)
                os.remove(file_to_remove)
                print(f"Cleaned up: {f}")
    except Exception as e:
        print(f"Error during cleanup: {e}")
    # Clean up extracted directories for each country
    unique_dates = set(country_dates.values())
    for country_date in unique_dates:
        try:
            path_to_remove = place_to_save / f'{saved_filename}/{country_date}'.replace('/', '\\')
            if os.path.exists(path_to_remove):
                command = f'rmdir /S /Q "{path_to_remove}"'
                result = os.system(command)
                if result == 0:
                    print(f"Successfully removed directory: {path_to_remove}")
                else:
                    print(f"Failed to remove directory. Error code: {result}")
        except Exception as e:
            print(f"An error occurred while removing {country_date}: {e}")

    return planogram


################################################################################################################################


def pallet_capacity(pmg, saved_name, place_to_save):

    countries_adress = ["hu", "cz", "sk"]
    countries_to_select = ['"HU"', '"CZ"', '"SK"']

    print("pallet_capacity Downloading has started")

    for c1, c2 in zip(countries_adress, countries_to_select):

        pallet_cap = """
        SELECT CAST(table1.tpnb AS INT) AS tpnb,
        table1.pmg AS pmg,
        table1.supplier_id AS supplier_id,
        table1.case_size AS case_size,
        table1.case_type AS case_type,
        table1.country AS country,
        table1.pallet_capacity AS pallet_capacity,
        MAX(year) AS year
        FROM
        (SELECT 
         tpns.slad_tpnb AS tpnb,
         hier.pmg AS pmg,
         pc.lnass_dmsup_id AS supplier_id, 
         pc.lnass_case_size AS case_size,
         pc.lnass_pur_unit AS case_type, 
         pc.lnass_no_cart_p AS pallet_capacity,
        tpns.cntr_code AS country,
        max(date_format(pc.lnass_upd_date,'yyyy')) AS year
        FROM syln{c1}.ln_art_sup_st pc
        LEFT JOIN dm.dim_artgld_details tpns ON pc.lnass_dmat_id = tpns.slad_dmat_id
        LEFT JOIN tesco_analysts.hierarchy_spm hier
        ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0")
        AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0")
        AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0")
        AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0")
        AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
        WHERE pc.lnass_no_cart_p > 1
        AND tpns.cntr_code = {c2}
        AND SUBSTRING(hier.pmg, 1, 3) IN {pmg}
        GROUP BY tpns.slad_tpnb, hier.pmg, pc.lnass_dmsup_id, pc.lnass_case_size, pc.lnass_pur_unit, pc.lnass_no_cart_p, tpns.cntr_code) table1
        WHERE table1.year IS NOT NULL
        AND table1.pmg IS NOT NULL
        GROUP BY table1.tpnb, table1.pmg, table1.supplier_id, table1.case_size, table1.case_type, table1.country, table1.pallet_capacity
        ORDER BY table1.country, table1.pmg, table1.tpnb, table1.pallet_capacity
        
        
        
    
        """.format(
            c1=c1, c2=c2, pmg=pmg
        )

        with pyodbc.connect(
            ODBC_CONN, autocommit=True, Trusted_Connection="yes"
        ) as conn:

            chunksize = 100000  # this is the number of lines

            pqwriter = None
            for i, df in enumerate(pd.read_sql(pallet_cap, conn, chunksize=chunksize)):
                table = pa.Table.from_pandas(df)
                # for the first chunk of records
                if i == 0:
                    # create a parquet write object giving it an output file
                    pqwriter = pq.ParquetWriter(
                        place_to_save / f"{saved_name}" / f"pallet_capacity_{saved_name}_{c1}" ,
                        table.schema,
                        compression="gzip",
                    )
                pqwriter.write_table(table)

            # close the parquet writer
            if pqwriter:
                pqwriter.close()

                print(f"Done with {c2}")

    onlyfiles = (
        f
        for f in listdir(place_to_save/ f"{saved_name}")
        if (isfile(join(place_to_save/ f"{saved_name}", f)) and search("pallet_capacity_", f.lower()))
    )

    # create an empty DataFrame and fill up with countries

    palet_cap = pd.DataFrame()

    for filename in onlyfiles:
        df = pd.read_parquet(join(place_to_save / f"{saved_name}", filename))
        palet_cap = pd.concat([palet_cap, df], ignore_index=True)

    palet_cap = (
        palet_cap.groupby(["country", "tpnb"])["pallet_capacity"]
        .mean()
        .reset_index()
        .to_parquet(
            place_to_save / f"{saved_name}" /  f"pallet_capacity_CE_{saved_name}",
            compression="gzip",
        )
    )

    ## If file exists, delete it ##

    for c1 in countries_adress:
        os.remove(place_to_save / f"{saved_name}" / f"pallet_capacity_{saved_name}_{c1}")

    conn.close()

    print("pallet_capacity Downloading Done")


############################################################################


def Rc1_products_pmg(nr_weeks, start, end, saved_name, place_to_save):

    countries_adress = ["hu", "cz", "sk"]
    countries_to_select = ['"HU"', '"CZ"', '"SK"']

    print("Rc1_products_pmg Downloading has started")

    for c1, c2 in zip(countries_adress, countries_to_select):

        rc1_products = """
        
            SELECT 
            b.cntr_code AS country,
            CAST(a.site as INT) as store, 
            cal.dtdw_day_desc_en as day,
            SUM(a.qty *-1)/{nr_weeks} AS  RC1_Products,
            /*CAST(b.slad_tpnb as INT) AS tpnb,
            b.slad_long_des AS product_name,*/ hier.pmg AS pmg
            FROM pos{c1}.t001csd a
            LEFT JOIN DM.dim_artgld_details b
            ON a.ean = lpad(b.slem_ean,14,0)
            RIGHT JOIN tesco_analysts.hierarchy_spm hier ON b.dmat_div_code = LPAD(hier.div_code,4,"0") 
            AND b.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
            AND b.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
            AND b.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
            AND b.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
            LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
            WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
            AND b.cntr_code = {c2}
            AND a.ret = 1
            GROUP BY b.cntr_code, a.site, cal.dtdw_day_desc_en,  /*b.slad_tpnb, b.slad_long_des,*/ hier.pmg
            Order by hier.pmg
        
        """.format(
            c1=c1, c2=c2, nr_weeks=nr_weeks, start=start, end=end
        )

        with pyodbc.connect(
            ODBC_CONN, autocommit=True, Trusted_Connection="yes"
        ) as conn:

            chunksize = 100000  # this is the number of lines

            pqwriter = None
            for i, df in enumerate(
                pd.read_sql(rc1_products, conn, chunksize=chunksize)
            ):
                table = pa.Table.from_pandas(df)
                # for the first chunk of records
                if i == 0:
                    # create a parquet write object giving it an output file
                    pqwriter = pq.ParquetWriter(
                        place_to_save / f"RC1_products_{saved_name}_{c1}",
                        table.schema,
                        compression="gzip",
                    )
                pqwriter.write_table(table)

            # close the parquet writer
            if pqwriter:
                pqwriter.close()

                print(f"Done with {c2}")

    onlyfiles = (
        f
        for f in listdir(place_to_save)
        if (isfile(join(place_to_save, f)) and search("RC1_products_", f))
    )

    # create an empty DataFrame and fill up with countries

    RC1_products = pd.DataFrame()

    for filename in onlyfiles:
        df = pd.read_parquet(join(place_to_save, filename))
        RC1_products = pd.concat([RC1_products, df], ignore_index=True)

    RC1_products["dep"] = RC1_products.pmg.str[:3]
    RC1_products.rename(columns={"rc1_products": "RC1 Products"}, inplace=True)
    RC1_products.to_parquet(
        place_to_save / "RC1_productsCE", compression="gzip"
    )

    ## If file exists, delete it ##

    for c1 in countries_adress:
        os.remove(place_to_save / f"RC1_products_{saved_name}_{c1}")

    conn.close()

    print("RC1_products Downloading Done")
    
def losses_sql(start, end, pmg, countries, nr_weeks,saved_name, place_to_save, stores):
    print("Losses_SQL has been started")

    conn = pyodbc.connect(ODBC_CONN, autocommit=True, Trusted_Connection="yes")
    cursor = conn.cursor()
    
    loss_query = f"""
        SELECT
        stores.cntr_code AS country,
        CAST(stores.dmst_store_code as INT) AS store,
        cal.dtdw_day_desc_en as day,
        --cal.dmtm_fw_code as week,
        CAST(tpns.slad_tpnb as INT) AS tpnb,
        CAST(tpns.slad_tpn as INT) AS tpn,
        tpns.slad_long_des as product_name,
        hier.pmg AS pmg,
        1 AS code,
        SUM(a.slrtc_rtcunit) / {nr_weeks} AS amount 
        FROM dw.sl_rtc a 
        JOIN dm.dim_stores stores ON stores.dmst_store_id = a.slrtc_dmst_id 
        AND stores.cntr_id = a.slrtc_cntr_id 
        JOIN dm.dim_artgld_details tpns ON tpns.slad_dmat_id = a.slrtc_dmat_id AND tpns.cntr_id = a.slrtc_cntr_id 
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
        JOIN tesco_analysts.hierarchy_spm hier ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0") 
        AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
        AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
        AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
        AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
        WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
        --AND tpns.dmat_sgr_des_en = 'gift_packs'
        AND stores.cntr_code IN {countries}
        AND a.slrtc_rc = 'RTC' 
        AND SUBSTRING(hier.pmg, 1, 3) IN {pmg}
        GROUP BY stores.cntr_code,
        stores.dmst_store_code,
        cal.dtdw_day_desc_en,
        tpns.slad_tpnb,
        tpns.slad_tpn,
        hier.pmg,
        tpns.slad_long_des
        --cal.dmtm_fw_code
        
        UNION ALL
        
        SELECT
        stores.cntr_code AS country,
        CAST(stores.dmst_store_code as INT) AS store,
        cal.dtdw_day_desc_en as day,
        --cal.dmtm_fw_code as week,
        CAST(tpns.slad_tpnb as INT) AS tpnb,
        CAST(tpns.slad_tpn as INT) AS tpn,
        tpns.slad_long_des as product_name,
        hier.pmg AS pmg,
        CAST(a.lssls_rcode as INT) AS code,
        SUM(a.lssls_quantity_adj) / {nr_weeks} AS amount 
        FROM dw.ls_stock_loss a
        JOIN dm.dim_stock_loss_rc b ON a.lssls_cntr_id = b.cntr_id
        AND a.lssls_rcode = b.lsrc_code 
        JOIN dm.dim_stores stores ON stores.dmst_store_id = a.lssls_dmst_id 
        AND stores.cntr_id = a.lssls_cntr_id 
        JOIN dm.dim_artgld_details tpns ON tpns.slad_dmat_id = a.lssls_dmat_id AND tpns.cntr_id = a.lssls_cntr_id 
        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
        JOIN tesco_analysts.hierarchy_spm hier ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0") 
        AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
        AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
        AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
        AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
        WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
        --AND tpns.dmat_sgr_des_en = 'gift_packs'
        AND stores.cntr_code IN {countries}
        AND a.lssls_rcode in ('3', '2', '4', '544','14','104', '181', '246')
        AND SUBSTRING(hier.pmg, 1, 3) IN  {pmg}
        GROUP BY stores.cntr_code,
        stores.dmst_store_code,
        cal.dtdw_day_desc_en,
        tpns.slad_tpnb,
        tpns.slad_tpn,
        hier.pmg, 
        a.lssls_rcode,
        tpns.slad_long_des
        --cal.dmtm_fw_code

    """.format(
            start=start, #
            end=end,
            countries=countries,
            pmg=pmg,
            nr_weeks=nr_weeks,
            stores=stores,
        )

    waste_rtc = pd.read_sql(loss_query, conn)
    conn.close()

    print("Data fetching is Done!")
    
    waste_rtc.to_parquet(
        place_to_save / saved_name / f"losses_RawData_{saved_name}", compression="gzip"
    )
    
    waste_rtc.rename(
        columns={
            col: col.replace(f"{saved_name}" + ".", "") for col in waste_rtc.columns
        },
        inplace=True,
    )
    waste_rtc.replace(np.nan, 0, inplace=True)

    waste_rtc["amount"] = waste_rtc.amount.abs()
    waste_rtc["dep"] = waste_rtc.pmg.astype(str).str[:3]
    waste_rtc = waste_rtc.sort_values(
        "amount", ascending=False
    )  # removal strange 3 records which amount is much higher than in the other stores
    waste_rtc.drop(waste_rtc.index[:3], inplace=True)
    waste_rtc["decimal"] = (
        (waste_rtc.amount.sub(waste_rtc.amount.astype(int))).mul(1000).astype(int)
    )  # Unit type
    waste_rtc["unit_type"] = np.where((waste_rtc["decimal"] > 0), "KG", "SNGL")
    waste_rtc.drop(["decimal"], axis=1, inplace=True)
    waste_rtc["code"] = waste_rtc.code.astype(int)
    waste_rtc["RTC Lines"] = np.where(waste_rtc.code == 1, 1, 0)
    waste_rtc["Waste Lines"] = np.where(
        (waste_rtc.code.isin([3, 4, 181])), 1, 0
    )
    waste_rtc["Food Donation Lines"] = np.where(
        (waste_rtc.code == 544) & (waste_rtc.unit_type != "KG"), 1, 0
    )  # just singles because I use it for scanning process
    waste_rtc["RTC Items"] = np.where((waste_rtc.code == 1), waste_rtc["amount"], 0)
    waste_rtc["Waste Items"] = np.where(
        (
            ((waste_rtc.code == 3) | (waste_rtc.code == 4))
            & (waste_rtc.unit_type != "KG")
            & (waste_rtc.dep == "PRO")
        ),
        waste_rtc["amount"],
        0,
    )
    waste_rtc["Waste Items"] = np.where(
        ((waste_rtc.code.isin([3, 4, 181])) & (waste_rtc.dep != "PRO")),
        waste_rtc["amount"] + waste_rtc["Waste Items"],
        waste_rtc["Waste Items"],
    )
    waste_rtc["Food Donation Items"] = np.where(
        (
            (waste_rtc.code.isin( [544, 246]))
            & (waste_rtc.unit_type != "KG")
            & (waste_rtc.dep == "PRO")
        ),
        waste_rtc["amount"],
        0,
    )
    waste_rtc["Food Donation Items"] = np.where(
        ((waste_rtc.code.isin([ 544, 246])) & (waste_rtc.dep != "PRO")),
        waste_rtc["amount"] + waste_rtc["Food Donation Items"],
        waste_rtc["Food Donation Items"],
    )
    waste_rtc["Waste Bulk (one bag)"] = np.where(
        (
            (waste_rtc.code.isin([3, 4, 181]))
            & (waste_rtc.unit_type == "KG")
            & (waste_rtc.dep == "PRO")
        ),
        waste_rtc["amount"] / 0.9,
        0,
    )  # 0.9 identifies how many part of the product you have to deal with
    waste_rtc["Food Donation Bulk (one bag)"] = np.where(
        (
            (waste_rtc.code.isin([ 544, 246]))
            & (waste_rtc.unit_type == "KG")
            & (waste_rtc.dep == "PRO")
        ),
        waste_rtc["amount"],
        0,
    )
    # condition = [
    #     waste_rtc["store"].astype(str).str.match("^1"),
    #     waste_rtc["store"].astype(str).str.match("^2"),
    #     waste_rtc["store"].astype(str).str.match("^4"),
    # ]
    # results = ["CZ", "SK", "HU"]
    # waste_rtc["country"] = np.select(condition, results, 0)

    """
      In here we have a change related with yelow labeled bags. In October 2020 I have calculated that 67% of code 544
      will not be send anymore, but it will be labeled and replenished like RTC items.
      The change is called: "Yellow Labeled Bags"
      I have added 2 Drivers: RTC (Produce Bags), RTC (Produce Items)
    
      Chosen PMGs:
      PRO01 Bananas 
      PRO02 Apples, Peaches, Tomatoes and Peppers 
      PRO03 Citrus 
      PRO04 Melones 
      PRO05 Exotic Fruit and Vegetable 
      PRO06 Organic PRO 
      PRO07 Berries and Grapes 
      PRO08 Potatoes and Onions 
      PRO09 Root Vegetable 
    """
    chosen_pmgs = [
        "PRO01",
        "PRO02",
        "PRO03",
        "PRO04",
        "PRO05",
        "PRO06",
        "PRO07",
        "PRO08",
        "PRO09",
    ]
    yell_bag_ratio = 1  # Initial percentega was 0.67 but we use looses for P8 2021 when the process was already implemented so we do not need to decrease anything
    yell_bag_capacity = 0.4
    yell_bag_capacity_sm = 0.9

    waste_rtc["RTC (Produce Bags)"] = np.where(
        (
            (waste_rtc["pmg"].isin(chosen_pmgs))
            & (waste_rtc.country == "HU")
            & (waste_rtc.unit_type == "KG")
        ),
        (waste_rtc["RTC Items"] * yell_bag_ratio) / yell_bag_capacity,
        0,
    )
    waste_rtc["RTC (Produce Bags)"] = np.where(
        (
            (waste_rtc["pmg"].isin(chosen_pmgs))
            & (waste_rtc.country == "HU")
            & (waste_rtc.unit_type == "KG")
            & (
                (waste_rtc.store.astype(str).str.match("^43"))
                | (waste_rtc.store.astype(str).str.match("^44"))
            )
        ),
        (waste_rtc["RTC Items"] * yell_bag_ratio) / yell_bag_capacity_sm,
        0,
    )
    waste_rtc["RTC Items"] = np.where(
        (
            (waste_rtc["pmg"].isin(chosen_pmgs))
            & (waste_rtc.country == "HU")
            & (waste_rtc.unit_type == "KG")
        ),
        waste_rtc["RTC Items"] * (1 - yell_bag_ratio),
        waste_rtc["RTC Items"],
    )  # decreasing RTC Items once we have yell_bag_ratio transfered to yellow labeled bags:

    # groupby
    waste_rtc = (
        waste_rtc.groupby(["country", "store", "day", "tpnb","product_name", "pmg", "dep"])[[
            "RTC Lines",
            "Waste Lines",
            "Food Donation Lines",
            "RTC Items",
            "Waste Items",
            "Food Donation Items",
            "Waste Bulk (one bag)",
            "Food Donation Bulk (one bag)",
            "RTC (Produce Bags)",
        ]]
        .sum()
        .reset_index()
    )

    waste_rtc["Food Donation (available)"] = np.where(
        waste_rtc["RTC (Produce Bags)"] > 0, 1, 0
    )
    waste_rtc["RTC Lines"] = np.where(waste_rtc["RTC Lines"] > 0, 1, 0)
    # waste_rtc['RTC Lines'] = 1 / waste_rtc.groupby(['store', 'tpnb'])['RTC Lines'].transform('sum')
    # waste_rtc['RTC Lines'] = waste_rtc['RTC Lines'].replace(np.nan, 0).replace([np.inf, -np.inf], 0)

    waste_rtc["Waste Lines"] = np.where(waste_rtc["Waste Lines"] > 0, 1, 0)
    # waste_rtc['Waste Lines'] = 1 / waste_rtc.groupby(['store', 'tpnb'])['Waste Lines'].transform('sum')
    # waste_rtc['Waste Lines'] = waste_rtc['Waste Lines'].replace(np.nan, 0).replace([np.inf, -np.inf], 0)

    waste_rtc["Food Donation Lines"] = np.where(
        waste_rtc["Food Donation Lines"] > 0, 1, 0
    )
    # waste_rtc['Food Donation Lines'] = 1 / waste_rtc.groupby(['store', 'tpnb'])['Food Donation Lines'].transform('sum')
    # waste_rtc['Food Donation Lines'] =  waste_rtc['Food Donation Lines'].replace(np.nan, 0).replace([np.inf, -np.inf], 0)

    waste_rtc.to_parquet(
        place_to_save / saved_name / f"losses_{saved_name}", compression="gzip"
    )

    print("Losses_SQL Done")

    

def wh_dataset_no_delivery(
    start_date,
    end_date,
    nr_weeks,
    place_to_save,
    saved_filename,
    directory,
    store_inputs,
    night_truck_helper
):

    print("WH_no_of_delivery has started")

    queries = """
    select gro.country as country, CAST(gro.dc as INT) as store,   gro.ord_type as order_type,
      time.dtdw_day_desc_en  as day, count(distinct(concat(gro.dc,gro.supp_om,gro.exp_del_date)))/{nr_weeks} as no_of_delivery_DTS
    from sch_analysts.tbl_ce_ssl_weekly_history gro
    JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = gro.tpnb
    AND mstr.cntr_code = gro.country
    JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
    JOIN dm.dim_time_d time ON LEFT(time.dmtm_value,10) = gro.exp_del_date
    WHERE gro.ord_type = "DTS"
    AND time.dmtm_fw_code BETWEEN {start} AND {end}
    GROUP by gro.country, gro.dc,  time.dtdw_day_desc_en, gro.ord_type 
    ORDER BY  gro.country, gro.dc , time.dtdw_day_desc_en
    
    ---
    select gm.country as country, CAST(gm.dc as INT) as store,  gm.ord_type as order_type ,
     time.dtdw_day_desc_en  as day, count(distinct(concat(gm.dc,gm.supp_om,gm.exp_del_date)))/{nr_weeks} as no_of_delivery_DTS
    from sch_analysts.tbl_ce_ssl_weekly_gm_history_new gm
    JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = gm.tpnb
    AND mstr.cntr_code = gm.country
    JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
    JOIN dm.dim_time_d time ON LEFT(time.dmtm_value,10) = gm.exp_del_date
    WHERE gm.ord_type = "DTS"
    AND time.dmtm_fw_code BETWEEN {start} AND {end}
    GROUP by gm.country, gm.dc, time.dtdw_day_desc_en, gm.ord_type 
    ORDER BY  gm.country, gm.dc ,  time.dtdw_day_desc_en
    
    """.format(
        start=start_date, end=end_date, nr_weeks=nr_weeks
    ).split(
        "---"
    )

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        df_dts = pd.concat([pd.read_sql_query(q, conn) for q in queries])

    Cases_delivered_total = """
    
    /*select
    CAST(CONCAT(a.int_cntr_id,a.store) AS INT) as store ,
    cal.dtdw_day_desc_en as day,
    COUNT(distinct(CONCAT(CONCAT(a.int_cntr_id,a.store),a.ref_no)))/{nr_weeks} as no_of_delivery_total
    from stg_go.go_106_order_receiving a
    LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product and b.cntr_id = a.int_cntr_id
    LEFT JOIN tesco_analysts.hierarchy_spm d ON b.dmat_div_code = lpad(d.div_code,4,"0")
    AND b.dmat_dep_code = lpad(d.dep_code,4,"0")
    AND b.dmat_sec_code = lpad(d.sec_code,4,"0")
    AND b.dmat_grp_code = lpad(d.grp_code,4,"0")
    AND b.dmat_sgr_code = lpad(d.sgr_code,4,"0")
    JOIN dm.dim_stores stores ON CAST(stores.dmst_store_code as INT) = CAST(CONCAT(a.int_cntr_id,a.store) AS INT)
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = TRANSLATE(SUBSTRING(a.act_del_datetime, 1,10),"-","")
    Where stores.convenience IN ('Convenience', 'HM')
    AND cal.dmtm_fw_code BETWEEN {start} AND {end}
    GROUP BY  stores.cntr_code, CONCAT(a.int_cntr_id,a.store) ,  cal.dtdw_day_desc_en
    ORDER BY  stores.cntr_code, CONCAT(a.int_cntr_id,a.store) ,  cal.dtdw_day_desc_en*/
    
    
    WITH delivered_cases AS (
    SELECT
        CAST(CONCAT(a.int_cntr_id,a.store) AS INT) as store,
        cal.dtdw_day_desc_en as day,
        COUNT(DISTINCT(CONCAT(CONCAT(a.int_cntr_id,a.store),a.ref_no)))/{nr_weeks} as no_of_delivery_total
    FROM stg_go.go_106_order_receiving a
    LEFT JOIN dm.dim_artgld_details b 
        ON b.slad_tpnb = a.product 
        AND b.cntr_id = a.int_cntr_id
    LEFT JOIN tesco_analysts.hierarchy_spm d 
        ON b.dmat_div_code = lpad(d.div_code,4,"0")
        AND b.dmat_dep_code = lpad(d.dep_code,4,"0")
        AND b.dmat_sec_code = lpad(d.sec_code,4,"0")
        AND b.dmat_grp_code = lpad(d.grp_code,4,"0")
        AND b.dmat_sgr_code = lpad(d.sgr_code,4,"0")
    JOIN dm.dim_stores stores 
        ON CAST(stores.dmst_store_code as INT) = CAST(CONCAT(a.int_cntr_id,a.store) AS INT)
    LEFT JOIN dm.dim_time_d cal 
        ON cal.dmtm_d_code = TRANSLATE(SUBSTRING(a.act_del_datetime, 1,10),"-","")
    WHERE stores.convenience IN ('Convenience', 'HM')
        AND cal.dmtm_fw_code BETWEEN {start} AND {end}
    GROUP BY  
        stores.cntr_code, 
        CONCAT(a.int_cntr_id,a.store),  
        cal.dtdw_day_desc_en
    ORDER BY  
        stores.cntr_code, 
        CONCAT(a.int_cntr_id,a.store),  
        cal.dtdw_day_desc_en
),
sold_units AS (
    SELECT
        CAST(stores.dmst_store_code AS INT) AS store, 
        cal.dtdw_day_desc_en as day,
        SUM(sunit.slsms_unit)/{nr_weeks} AS sold_units,
        SUM(sunit.slsms_salex)/{nr_weeks} AS sales_excl_vat
    FROM dw.sl_sms sunit 
    LEFT JOIN dm.dim_time_d cal 
        ON cal.dmtm_d_code = sunit.part_col
    JOIN dm.dim_stores stores 
        ON stores.cntr_id = sunit.slsms_cntr_id 
        AND stores.dmst_store_id = sunit.slsms_dmst_id
    WHERE stores.cntr_code IN ('SK', 'CZ', 'HU')
        AND cal.dmtm_fw_code BETWEEN {start} AND {end}
        AND sunit.slsms_unit > 0
    GROUP BY 
        stores.dmst_store_code,
        cal.dtdw_day_desc_en
)
SELECT 
    COALESCE(s.store, d.store) AS store,
    COALESCE(s.day, d.day) AS day,
    COALESCE(s.no_of_delivery_total, 0) AS no_of_delivery_total,
    COALESCE(d.sold_units, 0) AS sold_units,
    COALESCE(d.sales_excl_vat, 0) AS sales_excl_vat
FROM delivered_cases s
FULL OUTER JOIN sold_units d 
    ON s.store = d.store 
    AND s.day = d.day;
    
    
    
    """.format(
        start=start_date, end=end_date, nr_weeks=nr_weeks
    )

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        df_total = pd.read_sql_query(Cases_delivered_total, conn)
    ###############################################################################
    ##### CALCULATE DTS and DTW WH drivers
    ###############################################################################
    df_dts = (
        df_dts.groupby(["country", "store", "day",])["no_of_delivery_DTS"]
        .sum()
        .reset_index()
    )
    df_wh_delivery = df_total.merge(
        df_dts[["store", "day", "no_of_delivery_DTS"]], on=["store", "day"], how="left"
    )
    df_wh_delivery.fillna(0, inplace=True)
    df_wh_delivery["no_of_delivery_DTW"] = (
        df_wh_delivery["no_of_delivery_total"] - df_wh_delivery["no_of_delivery_DTS"]
    )
    df_wh_delivery["no_of_delivery_DTW"] = np.where(
        df_wh_delivery["no_of_delivery_DTW"] < 0,
        0,
        df_wh_delivery["no_of_delivery_DTW"],
    )
    store_list = pd.read_excel(directory / store_inputs, usecols=["Store", "Country"])
    store_list.columns = [f.lower() for f in store_list.columns]
    df_wh_delivery = df_wh_delivery.merge(store_list, on="store", how="inner")
    
    
    # #NightTruck settings
    # df_wh_delivery = df_wh_delivery.merge(night_truck_helper, on=["store"], how="left")
    
    # df_wh_delivery.loc[df_wh_delivery.flag.notnull(), ["pallets", "rollcages"]] = (
    # df_wh_delivery.loc[df_wh_delivery.flag.notnull(), ["pallets", "rollcages"]].values * 
    # df_wh_delivery.loc[df_wh_delivery.flag.notnull(), "percent_to_mult"].values.reshape(-1, 1)
    # )
    
    
    # a.loc[(a.pmg.str.contains("HDL")), ["stock",]] = (
    # a.loc[(a.pmg.str.contains("HDL")), ["stock", ]].values / 
    # a.loc[(a.pmg.str.contains("HDL")), "weeksGM"].values.reshape(-1, 1)
    # )
    
    
    # a.loc[(~a.pmg.str.contains("HDL")), ["stock"]] = (
    # a.loc[(~a.pmg.str.contains("HDL")), ["stock" ]].values / 
    # a.loc[(~a.pmg.str.contains("HDL")), "weeks data"].values.reshape(-1, 1)
    # )
    
    
    
    
    
    
    
    df_wh_delivery.to_parquet(
        place_to_save / f"wh_deliveries_data{saved_filename}",
        compression="gzip",
    )

    conn.close()

    print("WH_no_of_delivery has done")


def wh_dataset_no_of_cases(
    start_date,
    end_date,
    nr_weeks,
    place_to_save,
    saved_filename,
    directory,
    store_inputs,
):

    print("WH_no_of_cases has started")

    queries = """
    select gro.country as country,  SUBSTRING(hier.pmg, 1, 3) as dep,  gro.ord_type as order_type,
     /*SUM(IF(gro.pack_qty = 1 and SUBSTRING(hier.pmg, 1, 3) = "ISB" and gro.ord_type = "DTS", gro.ord_case/50 , gro.ord_case ))/{nr_weeks}*/ count(distinct(mstr.slad_tpnb)) as wh_cases_DTS
    from sch_analysts.tbl_ce_ssl_weekly_history gro
    JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = gro.tpnb
    AND mstr.cntr_code = gro.country
    JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
    JOIN dm.dim_time_d time ON LEFT(time.dmtm_value,10) = gro.exp_del_date
    WHERE gro.ord_type = "DTS"
    
    AND time.dmtm_fw_code BETWEEN {start} AND {end}
    
    GROUP by gro.country,  SUBSTRING(hier.pmg, 1, 3),  gro.ord_type 
    ORDER BY  gro.country,   SUBSTRING(hier.pmg, 1, 3)
    
    ---
    select gm.country as country,  SUBSTRING(hier.pmg, 1, 3) as dep, gm.ord_type as order_type ,
    /*SUM(gm.ord_case)/{nr_weeks}  as wh_cases_DTS,*/ count(distinct(mstr.slad_tpnb)) as wh_cases_DTS
    from sch_analysts.tbl_ce_ssl_weekly_gm_history_new gm
    JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = gm.tpnb
    AND mstr.cntr_code = gm.country
    JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
    JOIN dm.dim_time_d time ON LEFT(time.dmtm_value,10) = gm.exp_del_date
    WHERE gm.ord_type = "DTS"
    AND time.dmtm_fw_code BETWEEN {start} AND {end}
    
    GROUP by gm.country,  SUBSTRING(hier.pmg, 1, 3),  gm.ord_type 
    ORDER BY  gm.country,   SUBSTRING(hier.pmg, 1, 3)
    
    """.format(
        start=start_date, end=end_date, nr_weeks=nr_weeks
    ).split(
        "---"
    )

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        df_dts_case = pd.concat([pd.read_sql_query(q, conn) for q in queries])

    Cases_delivered_dep = """
    
    select  a.int_cntr_code as country, SUBSTRING(d.pmg, 1, 3) as dep, 
    /*SUM(if(SUBSTRING(d.pmg, 1, 3) = "ISB" and b.slad_case_size = 1, (a.qty /b.slad_case_size)/50, a.qty /b.slad_case_size  ))/{nr_weeks} as total_wh_cases*/ count(distinct(b.slad_tpnb)) as total_wh_cases
    from stg_go.go_106_order_receiving a
    LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product and b.cntr_id = a.int_cntr_id
    Right JOIN tesco_analysts.hierarchy_spm d ON b.dmat_div_code = lpad(d.div_code,4,"0")
    AND b.dmat_dep_code = lpad(d.dep_code,4,"0")
    AND b.dmat_sec_code = lpad(d.sec_code,4,"0")
    AND b.dmat_grp_code = lpad(d.grp_code,4,"0")
    AND b.dmat_sgr_code = lpad(d.sgr_code,4,"0")
    JOIN dm.dim_stores stores ON CAST(stores.dmst_store_code as INT) = CAST(CONCAT(a.int_cntr_id,a.store) AS INT)
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = TRANSLATE(SUBSTRING(a.act_del_datetime, 1,10),"-","")
    Where
    /*stores.convenience IN ('Convenience', 'HM')
    AND*/ cal.dmtm_fw_code BETWEEN {start} AND {end}
    GROUP BY  a.int_cntr_code,  SUBSTRING(d.pmg, 1, 3)
    ORDER BY  a.int_cntr_code,  SUBSTRING(d.pmg, 1, 3)
    """.format(
        start=start_date, end=end_date, nr_weeks=nr_weeks
    )

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        df_total_case = pd.read_sql_query(Cases_delivered_dep, conn)

    df_dts_case = (
        df_dts_case.groupby(["country",  "dep"])["wh_cases_DTS"]
        .sum()
        .reset_index()
    )
    df_wh_case = df_total_case.merge(
        df_dts_case[["country", "dep", "wh_cases_DTS"]],
        on=["country", "dep"],
        how="left",
    )
    df_wh_case.fillna(0, inplace=True)
    df_wh_case["wh_cases_DTW"] = (
        df_wh_case["total_wh_cases"] - df_wh_case["wh_cases_DTS"]
    )
    df_wh_case["wh_cases_DTW"] = np.where(
        df_wh_case["wh_cases_DTW"] < 0, 0, df_wh_case["wh_cases_DTW"]
    )
    # store_list = pd.read_excel(directory / store_inputs, usecols=["Store", "Country"])
    # store_list.columns = [f.lower() for f in store_list.columns]
    # df_wh_case = df_wh_case.merge(store_list, on="store", how="inner")

    df_wh_case.to_excel(place_to_save /  f"WH_cases_{saved_filename}.xlsx", index=False)

    conn.close()

    print("WH_no_of_cases has done")


def wh_dataset_no_of_cases_PMG(
    start_date,
    end_date,
    nr_weeks,
    place_to_save,
    saved_filename,
    directory,
    store_inputs,
):

    print("WH_no_of_cases_PMG has started")

    queries = """
    select gro.country as country, CAST(gro.dc as INT) as store, hier.pmg as pmg,  gro.ord_type as order_type,
      SUM(IF(gro.pack_qty = 1 and SUBSTRING(hier.pmg, 1, 3) = "ISB" and gro.ord_type = "DTS", gro.ord_case/50 , gro.ord_case ))/{nr_weeks} as wh_cases_DTS,  time.dtdw_day_desc_en  as day
    from sch_analysts.tbl_ce_ssl_weekly_history gro
    JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = gro.tpnb
    AND mstr.cntr_code = gro.country
    JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
    JOIN dm.dim_time_d time ON LEFT(time.dmtm_value,10) = gro.exp_del_date
    WHERE gro.ord_type = "DTS"
    
    AND time.dmtm_fw_code BETWEEN {start} AND {end}
    
    GROUP by gro.country, gro.dc, hier.pmg, time.dtdw_day_desc_en, gro.ord_type 
    ORDER BY  gro.country, gro.dc, time.dtdw_day_desc_en,   hier.pmg
    
    ---
    select gm.country as country, CAST(gm.dc as INT) as store, hier.pmg as pmg, gm.ord_type as order_type ,
    SUM(gm.ord_case)/{nr_weeks}  as wh_cases_DTS,  time.dtdw_day_desc_en  as day
    from sch_analysts.tbl_ce_ssl_weekly_gm_history_new gm
    JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = gm.tpnb
    AND mstr.cntr_code = gm.country
    JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
    JOIN dm.dim_time_d time ON LEFT(time.dmtm_value,10) = gm.exp_del_date
    WHERE gm.ord_type = "DTS"
    AND time.dmtm_fw_code BETWEEN {start} AND {end}
    
    GROUP by gm.country, gm.dc, hier.pmg, time.dtdw_day_desc_en, gm.ord_type 
    ORDER BY  gm.country, gm.dc,  time.dtdw_day_desc_en, hier.pmg
    
    """.format(
        start=start_date, end=end_date, nr_weeks=nr_weeks
    ).split(
        "---"
    )

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        df_dts_case = pd.concat([pd.read_sql_query(q, conn) for q in queries])

    Cases_delivered_dep = """
    
    select  CAST(CONCAT(a.int_cntr_id,a.store) AS INT) as store, cal.dtdw_day_desc_en as day, d.pmg as pmg, 
    SUM(if(SUBSTRING(d.pmg, 1, 3) = "ISB" and b.slad_case_size = 1, (a.qty /b.slad_case_size)/50, a.qty /b.slad_case_size  ))/{nr_weeks} as total_wh_cases
    from stg_go.go_106_order_receiving a
    LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product and b.cntr_id = a.int_cntr_id
    Right JOIN tesco_analysts.hierarchy_spm d ON b.dmat_div_code = lpad(d.div_code,4,"0")
    AND b.dmat_dep_code = lpad(d.dep_code,4,"0")
    AND b.dmat_sec_code = lpad(d.sec_code,4,"0")
    AND b.dmat_grp_code = lpad(d.grp_code,4,"0")
    AND b.dmat_sgr_code = lpad(d.sgr_code,4,"0")
    JOIN dm.dim_stores stores ON CAST(stores.dmst_store_code as INT) = CAST(CONCAT(a.int_cntr_id,a.store) AS INT)
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = TRANSLATE(SUBSTRING(a.act_del_datetime, 1,10),"-","")
    Where
    /*stores.convenience IN ('Convenience', 'HM')
    AND*/ cal.dmtm_fw_code BETWEEN {start} AND {end}
    GROUP BY  CONCAT(a.int_cntr_id,a.store), cal.dtdw_day_desc_en, d.pmg
    ORDER BY  CONCAT(a.int_cntr_id,a.store),cal.dtdw_day_desc_en,  d.pmg
    """.format(
        start=start_date, end=end_date, nr_weeks=nr_weeks
    )

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        df_total_case = pd.read_sql_query(Cases_delivered_dep, conn)

    df_dts_case = (
        df_dts_case.groupby(["country", "store", "pmg", "day"])["wh_cases_DTS"]
        .sum()
        .reset_index()
    )
    df_wh_case = df_total_case.merge(
        df_dts_case[["store", "day", "pmg", "wh_cases_DTS"]],
        on=["store", "day", "pmg"],
        how="left",
    )
    df_wh_case.fillna(0, inplace=True)
    df_wh_case["wh_cases_DTW"] = (
        df_wh_case["total_wh_cases"] - df_wh_case["wh_cases_DTS"]
    )
    df_wh_case["wh_cases_DTW"] = np.where(
        df_wh_case["wh_cases_DTW"] < 0, 0, df_wh_case["wh_cases_DTW"]
    )
    df_wh_case["total_wh_cases"] = (
        df_wh_case["wh_cases_DTW"] + df_wh_case["wh_cases_DTS"]
    )
    store_list = pd.read_excel(directory / store_inputs, usecols=["Store", "Country"])
    store_list.columns = [f.lower() for f in store_list.columns]
    df_wh_case = df_wh_case.merge(store_list, on="store", how="inner")

    df_wh_case.to_parquet(
        place_to_save / f"wh_cases_pmg{saved_filename}", compression="gzip"
    )

    conn.close()

    print("WH_no_of_cases_on_PMG has done")

    print("WH_no_of_cases_patisserie has started")

    queries = """
    select gro.country as country, CAST(gro.dc as INT) as store, mstr.dmat_sec_des as sec,  gro.ord_type as order_type,
      SUM(gro.ord_case )/{nr_weeks} as wh_cases_DTS,  time.dtdw_day_desc_en  as day
    from sch_analysts.tbl_ce_ssl_weekly_history gro
    JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = gro.tpnb
    AND mstr.cntr_code = gro.country
    JOIN dm.dim_time_d time ON LEFT(time.dmtm_value,10) = gro.exp_del_date
    WHERE gro.ord_type = "DTS"
    AND mstr.dmat_sec_code in (1103,1104,1105,1107)

    AND time.dmtm_fw_code BETWEEN {start} AND {end}

    GROUP by gro.country, gro.dc, mstr.dmat_sec_des, time.dtdw_day_desc_en, gro.ord_type 
    ORDER BY  gro.country, gro.dc, time.dtdw_day_desc_en,   mstr.dmat_sec_des


    """.format(
        start=start_date, end=end_date, nr_weeks=nr_weeks
    )

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        df_dts_case = pd.read_sql_query(queries, conn)

    Cases_delivered_dep = """

    select  CAST(CONCAT(a.int_cntr_id,a.store) AS INT) as store, cal.dtdw_day_desc_en as day, b.dmat_sec_des as sec, 
    SUM( a.qty /b.slad_case_size  )/{nr_weeks} as total_wh_cases
    from stg_go.go_106_order_receiving a
    LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product and b.cntr_id = a.int_cntr_id
    JOIN dm.dim_stores stores ON CAST(stores.dmst_store_code as INT) = CAST(CONCAT(a.int_cntr_id,a.store) AS INT)
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = TRANSLATE(SUBSTRING(a.act_del_datetime, 1,10),"-","")
    Where
    /*stores.convenience IN ('Convenience', 'HM')
    AND*/ cal.dmtm_fw_code BETWEEN {start} AND {end}
    AND b.dmat_sec_code in (1103,1104,1105,1107)
    GROUP BY  CONCAT(a.int_cntr_id,a.store), cal.dtdw_day_desc_en, b.dmat_sec_des
    ORDER BY  CONCAT(a.int_cntr_id,a.store),cal.dtdw_day_desc_en,  b.dmat_sec_des
    """.format(
        start=start_date, end=end_date, nr_weeks=nr_weeks
    )

    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

        df_total_case = pd.read_sql_query(Cases_delivered_dep, conn)

    df_dts_case = (
        df_dts_case.groupby(["country", "store", "sec", "day"])["wh_cases_DTS"]
        .sum()
        .reset_index()
    )
    df_wh_case = df_total_case.merge(
        df_dts_case[["store", "day", "sec", "wh_cases_DTS"]],
        on=["store", "day", "sec"],
        how="left",
    )
    df_wh_case.fillna(0, inplace=True)
    df_wh_case["wh_cases_DTW"] = (
        df_wh_case["total_wh_cases"] - df_wh_case["wh_cases_DTS"]
    )
    df_wh_case["wh_cases_DTW"] = np.where(
        df_wh_case["wh_cases_DTW"] < 0, 0, df_wh_case["wh_cases_DTW"]
    )
    df_wh_case["total_wh_cases"] = (
        df_wh_case["wh_cases_DTW"] + df_wh_case["wh_cases_DTS"]
    )
    store_list = pd.read_excel(directory / store_inputs, usecols=["Store", "Country"])
    store_list.columns = [f.lower() for f in store_list.columns]
    df_wh_case = df_wh_case.merge(store_list, on="store", how="inner")
    
    
    df_wh_case["department"] = df_wh_case["pmg"].str[:3]
    
    for x in ["total_wh_cases", "wh_cases_DTS", "wh_cases_DTW"]:
        df_wh_case[x] = np.where(((df_wh_case.country.isin(["SK", "HU"])) & (df_wh_case.department.isin(["BWS", "DRY"]))), df_wh_case[x] *0.3, df_wh_case[x])
    
    

    df_wh_case.to_parquet(
        place_to_save / f"wh_pattisserie{saved_filename}", compression="gzip"
    )

    conn.close()

    print("WH_no_of_cases_on_patisserie has done")


def WGLL_driver_gap_scan(start_date, end_date, nr_weeks, place_to_save, saved_filename):

    print("WGLL_gapScan report collecting....")

    ###########################################################################
    ##### part of date calculation
    ###########################################################################
    start = int(start_date[2:6] + start_date[7:9])
    end = int(end_date[2:6] + end_date[7:9])
    date_list = []
    for a in range((end - start) + 1):
        dates = str(start + 9 + a)
        date_list.append(dates)

    ###########################################################################
    ##### part of data calc
    ###########################################################################

    countries = ["HU", "SK", "CZ"]
    df_wggl_gapscan = pd.DataFrame()

    for country in countries:
        path_srp = Path(
            r"\\czprgvmfs03.cz.tesco-europe.com\Reports\CCLB_SHARED\{country}".format(
                country=country
            )
        )

        onlyfiles = (
            f
            for f in listdir(path_srp)
            if (isfile(join(path_srp, f)))
            and search("{country}_CCLB_gaps_summary_".format(country=country), f)
        )

        for filename in onlyfiles:

            for date in date_list:

                if search(date, filename):
                    df_wgll = pd.read_csv(path_srp / filename, sep=";")
                    datecol = [col for col in df_wgll.columns if "date" in col.lower()]
                    df_wgll[" ".join(datecol)] = df_wgll[" ".join(datecol)].apply(
                        pd.to_datetime
                    )
                    df_wgll["day"] = df_wgll[" ".join(datecol)].dt.day_name()
                    df_wgll = df_wgll[
                        df_wgll.DivName.str.lower().str.contains("hardlines")
                    ]
                    df_wgll["dep"] = "HDL"
                    df_wgll.rename(columns={"ShopNbr": "store"}, inplace=True)

                    df_wggl_gapscan = pd.concat([df_wggl_gapscan, df_wgll])

        print("{country} WGLL_gapScan Done".format(country=country))

    df_wggl_gapscan = (
        df_wggl_gapscan.groupby(["store", "day", "dep"])["Count"].sum().reset_index()
    )
    df_wggl_gapscan["no_of_avg_hdl_gapscan"] = df_wggl_gapscan["Count"] / nr_weeks * 0.3
    df_wggl_gapscan.drop(["Count"], axis=1, inplace=True)
    df_wggl_gapscan.to_excel(
        place_to_save / f"WGLL_gap_scan_{saved_filename}.xlsx", index=False
    )


def SRD_database_old():
    
    
    SK_IKB = r"PVCKBSQLDB001SK.global.tesco.org;"
    HU_IKB = r"PVCKBSQLDB001HU.global.tesco.org;"
    CZ_IKB = r"PVCKBSQLDB001CZ.global.tesco.org;"

    srdTable_CE = pd.DataFrame()

    for x, y in zip(["SK", "HU", "CZ"], [SK_IKB, HU_IKB, CZ_IKB]):

        with pyodbc.connect(
            "Driver={ODBC Driver 18 for SQL Server};"
            f"Server={y};"
            "Database=CKB;"
            "Trusted_Connection=yes;"
            "TrustServerCertificate=yes;"
            "Encrypt=yes;"
            
        ) as conn:
            

            
            

            srd_query = """
                        
                        

                        set nocount on;
                        IF OBJECT_ID('tempdb..#Flop_POG_1') IS NOT NULL DROP TABLE #Flop_POG_1;
                        IF OBJECT_ID('tempdb..#Pog_Table_0') IS NOT NULL DROP TABLE #Pog_Table_0;
                        IF OBJECT_ID('tempdb..#Pog_Table_1') IS NOT NULL DROP TABLE #Pog_Table_1;
                        IF OBJECT_ID('tempdb..#Darab_1') IS NOT NULL DROP TABLE #Darab_1;
                        IF OBJECT_ID('tempdb..#Pos_Table_1') IS NOT NULL DROP TABLE #Pos_Table_1;
                        
                        DECLARE @Country nvarchar(2) = '{x}';
                        
                        -- Floorplan base data
                        Select distinct 
                         	f.desc12 as 'Store_Number',
                         	f.desc11 as 'Store_Name',
                         	f.desc2 as 'Store_Format',
                         	fs.DBParentPlanogramKey
                        into #Flop_POG_1 
                        from ckb.dbo.ix_flr_floorplan as f WITH (nolock) join   
                        	 ckb.dbo.ix_flr_section as fs WITH (nolock) on (fs.dbparentfloorplankey=f.dbkey)
                        where f.dbstatus in (1)-- and f.desc12 in (1001)
                        OPTION (MAXDOP 1);
                        
                        -- POG base data
                        Select distinct 
                         	p.desc2 as 'Displaygroup',
                         	p.desc24 as 'Displaygroup_description', 
                         	p.Dbkey as 'POG_ID', 
                         	p.Name as 'POG_Name'
                        into #Pog_Table_0
                        from ckb.dbo.ix_spc_planogram as p WITH (nolock) 
                        where p.DBStatus not in (4) and 
                         	  p.dbkey in (Select distinct #Flop_POG_1.DBParentPlanogramKey from #Flop_POG_1)
                        OPTION (MAXDOP 1);
                        
                        -- Position records
                        Select distinct 
                         	pos.segment as 'segmentnumber',
                         	pos.HFacings as 'Position_HFacing',
                         	pos.VFacings as 'Position_VFacing', 
                         	pos.DFacings as 'Position_DFacing',
                         	pos.merchstyle as 'merchstyle_ID',
                         	pos.DBParentFixtureKey,
                         	pos.DBParentPlanogramKey,
                         	pos.dbparentproductkey
                        into #Pos_Table_1
                        from #Pog_Table_0 as Pog_Table_0 join 
                         	ckb.dbo.ix_spc_position pos WITH (nolock) on (Pog_Table_0.POG_ID=pos.dbparentplanogramkey)  
                        OPTION (MAXDOP 1);
                        
                        -- POG base data
                        Select distinct 
                         	Pog_Table_0.Displaygroup,
                         	Pog_Table_0.Displaygroup_description, 
                         	Pog_Table_0.POG_ID, 
                         	Pog_Table_0.POG_Name, 
                         	Pos_Table_1.segmentnumber,
                         	fx.name as 'Fixture_name',
                         	fx.depth as 'Fixture_depth', 
                         	fx.Width as 'Fixture_width',
                         	fx.x as 'Fixture_X_position', 
                         	pr.id as 'Product_id', 
                         	pr.name as 'Product_Name',
                         	Pos_Table_1.Position_HFacing,
                         	Pos_Table_1.Position_VFacing, 
                         	Pos_Table_1.Position_DFacing,
                         	Pos_Table_1.merchstyle_ID,
                         	Case when Pos_Table_1.merchstyle_ID=0 then 'Unit'
                         		 when Pos_Table_1.merchstyle_ID=1 then 'Tray'
                         		 when Pos_Table_1.merchstyle_ID=2 then 'Case'
                         		 when Pos_Table_1.merchstyle_ID=3 then 'Display'
                         		 when Pos_Table_1.merchstyle_ID=4 then 'Alternate'
                         		 when Pos_Table_1.merchstyle_ID=5 then 'Loose'
                         		 when Pos_Table_1.merchstyle_ID=6 then 'Log Stack' 
                         		 else 'NULL' 
                         	End as 'merchstyle_string',
                         	pr.desc18 as brand, 
                         	pr.desc16 as ownbrand,
                         	pr.Height, 
                         	pr.Width, 
                         	pr.Depth, 
                         	pr.TrayHeight, 
                         	pr.TrayWidth, 
                         	pr.TrayDepth, 
                         	pr.TrayNumberHigh, 
                         	pr.TrayNumberWide, 
                         	pr.TrayNumberDeep, 
                         	pr.TrayTotalNumber,  
                         	pr.CaseHeight, 
                         	pr.CaseWidth, 
                         	pr.CaseDepth, 
                         	pr.CaseNumberHigh, 
                         	pr.CaseNumberWide, 
                         	pr.CaseNumberDeep,
                         	pr.CaseTotalNumber,
                         	SUBSTRING( pr.desc8, 1, 4) as 'H1', 
                         	SUBSTRING( pr.desc8, 5, 4) as 'H2', 
                         	SUBSTRING( pr.desc8, 9, 4) as 'H3', 
                         	SUBSTRING( pr.desc8, 13, 4) as 'H4', 
                         	SUBSTRING( pr.desc8, 16, 4) as 'H5' 
                        into #Pog_Table_1
                        from #Pog_Table_0 as Pog_Table_0 join
                        	 #Pos_Table_1 as Pos_Table_1 on (Pog_Table_0.POG_ID=Pos_Table_1.dbparentplanogramkey) join
                        	 ckb.dbo.ix_spc_product as pr WITH (nolock) on (pr.dbkey=Pos_Table_1.dbparentproductkey) join 
                        	 ckb.dbo.ix_spc_fixture as fx WITH (nolock) on (Pog_Table_0.POG_ID=fx.DBParentPlanogramKey and Pos_Table_1.DBparentfixturekey=fx.DBKey)
                        OPTION (MAXDOP 1);
                        
                        --Position count based on fixture
                        select 
                         	Pog_Table_1.POG_ID, 
                         	Pog_Table_1.segmentnumber, 
                         	Pog_Table_1.Fixture_X_position, 
                         	COUNT(Pog_Table_1.POG_ID & Pog_Table_1.segmentnumber & convert(int, Pog_Table_1.Fixture_X_position)) as 'Darab'
                        INTO #Darab_1
                        from #Pog_Table_1 as Pog_Table_1
                        group by Pog_Table_1.POG_ID,
                         		 Pog_Table_1.segmentnumber, 
                         		 Pog_Table_1.Fixture_X_position
                        OPTION (MAXDOP 0);
                        
                        --Final report
                        select distinct
                         	@Country as 'Country',
                         	Flop_POG_1.Store_Number, 
                         	Flop_POG_1.Store_Name,
                         	Flop_POG_1.Store_Format,
                         	Pog_Table_1.Displaygroup,
                         	Pog_Table_1.Displaygroup_description, 
                         	Pog_Table_1.POG_ID, 
                         	Pog_Table_1.POG_Name,
                         	Pog_Table_1.segmentnumber,
                         	Pog_Table_1.Fixture_name,
                         	Pog_Table_1.Fixture_depth, 
                         	Pog_Table_1.Fixture_width, 
                         	iif(substring(Pog_Table_1.Fixture_name, 1, 3) = 'PAL' or substring (Pog_Table_1.Fixture_name, 1, 3) = 'Rak',
                        		IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =60) or (Pog_Table_1.Fixture_depth = 60 and Pog_Table_1.Fixture_width = 80),
                        		IIf(Darab_1.Darab >1,'Split_on_Half_Pallet', 'Half_Pallet'), 
                        		IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =120) or (Pog_Table_1.Fixture_depth= 120 and Pog_Table_1.Fixture_width = 80),
                        		IIf(Darab_1.Darab >1, 'Split_Pallet','Pallet'),'')),'') as 'Pallet_info',
                         	Pog_Table_1.Product_id, 
                         	Pog_Table_1.Product_Name,
                         	Pog_Table_1.Position_HFacing,
                         	Pog_Table_1.Position_VFacing, 
                         	Pog_Table_1.Position_DFacing,
                         	Pog_Table_1.merchstyle_ID,
                         	Pog_Table_1.merchstyle_string,
                         	Pog_Table_1.brand, 
                         	Pog_Table_1.ownbrand,
                         	Pog_Table_1.Height, 
                         	Pog_Table_1.Width, 
                         	Pog_Table_1.Depth, 
                         	Pog_Table_1.TrayHeight, 
                         	Pog_Table_1.TrayWidth, 
                         	Pog_Table_1.TrayDepth, 
                         	Pog_Table_1.TrayNumberHigh, 
                         	Pog_Table_1.TrayNumberWide, 
                         	Pog_Table_1.TrayNumberDeep, 
                         	Pog_Table_1.TrayTotalNumber,  
                         	Pog_Table_1.CaseHeight, 
                         	Pog_Table_1.CaseWidth, 
                         	Pog_Table_1.CaseDepth, 
                         	Pog_Table_1.CaseNumberHigh, 
                         	Pog_Table_1.CaseNumberWide, 
                         	Pog_Table_1.CaseNumberDeep,
                         	Pog_Table_1.CaseTotalNumber,
                         	Pog_Table_1.H1 as 'Division', 
                         	Pog_Table_1.H2 as 'Department',
                         	Pog_Table_1.H3 as 'Section',
                         	Pog_Table_1.H4 as 'Group',
                         	Pog_Table_1.H5 as 'Subgroup'
                        From #Flop_POG_1 as Flop_POG_1 join 
                        	 #Pog_Table_1 as Pog_Table_1 on (Flop_POG_1.DBParentPlanogramKey=Pog_Table_1.POG_ID) join
                        	 #Darab_1 as Darab_1 on (Pog_Table_1.POG_ID=Darab_1.POG_ID and Pog_Table_1.segmentnumber=Darab_1.segmentnumber and Pog_Table_1.Fixture_X_position=Darab_1.Fixture_X_position)
                        OPTION (MAXDOP 0);
              
                        
                        



                        
                        
    
                        """.format(
                x=x
            )

            srdTable_CE = pd.concat([srdTable_CE, pd.read_sql(srd_query, conn)])

            print(f"\n{x} SRD table is ready!\n")

    srdTable_CE = srdTable_CE.fillna(0)
    
    return srdTable_CE

    
    

# @rmf.timeit
# def SRD_database():
    
    
#     sql = ssh_table_create(what_to_create, sql_part.start_date, sql_part.end_date, sql_part.pmg_list  , sql_part.nr_weeks, sql_part.place_to_save, sql_part.saved_filename)        
    
#     sql= 0
#     if sql ==0:
                        
#         ssh_downloader(what_to_create, sql_part.place_to_save, sql_part.saved_filename, sql_part.stores)

    

#     srdTable_CE = srdTable_CE.fillna(0)
    
#     return srdTable_CE


@rmf.timeit    
def SRD_table_added_hierarchy(srdTable_CE):
    
    with pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    ) as conn:

    
        dict_list =  srdTable_CE.groupby("Country")["Product_id"].apply(lambda s: s.tolist()).to_dict()

        df2 = pd.DataFrame()
        for k, v in dict_list.items():
            
            s = list()
            for x in v:
    
                s.append(str(x))
    
            tpn = tuple(s)
    
    
            sql = """ SELECT cntr_code AS Country,
            slad_tpnb AS Product_id, 
            dmat_div_des_en AS DIV_DESC,
            dmat_dep_des_en AS DEP_DESC,
            dmat_sec_des_en AS SEC_DESC,
            dmat_grp_des_en AS GRP_DESC,
            dmat_sgr_des_en AS SGR_DESC
            FROM DM.dim_artgld_details mstr
            WHERE slad_tpnb in {tpn}
            AND cntr_code = '{k}'
            AND dmat_sgr_des_en <> "Do not use"
            GROUP BY cntr_code,
            slad_tpnb,
            dmat_div_des_en,
            dmat_dep_des_en,
            dmat_sec_des_en,
            dmat_grp_des_en,
            dmat_sgr_des_en
            
            """.format(tpn=tpn, k=k)
            
            art_gold = pd.read_sql(sql, conn)
            df2 = pd.concat([df2, art_gold])
            
            print(f"Hierarchy part is done with: {k}")
            
            
    
    srdTable_CE = srdTable_CE.merge(df2, on=['Country', 'Product_id'], how='left')
    
    return srdTable_CE


def SRD_to_opsdev_old(opsdev, directory, excel_inputs_f):
    
    
    

    foil_calc = pd.read_excel(directory / excel_inputs_f, sheet_name='foil_calc', usecols=['Country',
                                                                                            'level4',
                                                                                            'Foil Opening Reduction Opportunity',
                                                                                            'Proposed Opening %',
                                                                                            'Foil Replen Min Facing',
                                                                                            'SRP opening reduction opportunity',
                                                                                            'extra disassemble %'],
                              dtype={'level4': np.int64})
    
    # foil_calc.rename(columns={"Proposed Opening %":"foil"}, inplace=True)
    
    # foil_calc = foil_calc[foil_calc['SRP opening reduction opportunity'] == 1]
    
    
    
    opsdev_foil = opsdev.copy()
    
    for x in ['Division', 'Department', 'Section', 'Group_']:
        
        opsdev_foil[x] = np.where(opsdev_foil[x] == '', 0, opsdev_foil[x])    
    
    opsdev_foil['level4'] = [str(int(p1)) + str(int(p2)) + str(int(p3)) + str(int(p4)) for p1, p2, p3, p4 in zip(opsdev_foil['Division'],
                                                                                             opsdev_foil['Department'],
                                                                                             opsdev_foil['Section'],
                                                                                             opsdev_foil['Group_'])]
    opsdev_foil['level4'] = opsdev_foil['level4'].astype("int64")
    
    opsdev_foil = opsdev_foil.merge(foil_calc, on=['Country', 'level4'], how='left')
    
    
    cond=[(opsdev_foil['Foil Opening Reduction Opportunity'] == 1)
          & (opsdev_foil['merchstyle_string'] == 'Unit')
          & ((opsdev_foil['Pallet_info'].isna()) | (opsdev_foil['Pallet_info']=="")),
          
          (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)
          & (opsdev_foil['merchstyle_string'] == 'Tray')
          & ((opsdev_foil['Pallet_info'].isna()) | (opsdev_foil['Pallet_info']=="")),
          
          (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)
          &  (opsdev_foil['merchstyle_string'] == 'Case')
          & ((opsdev_foil['Pallet_info'].isna()) | (opsdev_foil['Pallet_info']==""))]
    
    result = [opsdev_foil['Position_HFacing'],
              opsdev_foil['Position_HFacing'] * opsdev_foil['TrayNumberWide'],
              opsdev_foil['Position_HFacing'] * opsdev_foil['CaseNumberWide']]
    
    
    owise= (opsdev_foil['Position_HFacing'] * opsdev_foil['merchstyle_ID'])
            
    
    opsdev_foil['for_foil_check'] = np.select(cond,result,owise)
    
    opsdev_foil['foil'] = np.where((opsdev_foil['Foil Replen Min Facing'] < opsdev_foil['for_foil_check'])
                                   & (opsdev_foil['Pallet_info'].isna()), 
                                   opsdev_foil["Proposed Opening %"], 0)
    
    
    
    # for x in [opsdev, opsdev_foil]:
    
    #     country_cond = [
    #         x["Country"] == "HU",
    #         x["Country"] == "SK",
    #         x["Country"] == "CZ",
    #     ]
    
    #     store_code_create = [
    #         str(4) + x.Store_Number.astype(str),
    #         str(2) + x.Store_Number.astype(str),
    #         str(1) + x.Store_Number.astype(str),
    #     ]
        
    #     x["Store_Number"] = np.select(country_cond, store_code_create, 0)
        
        
        
    
    opsdev_foil.rename(
        columns={
            "Country":  "country",
            "Store_Number": "store",
            "Product_id": "tpnb"}, inplace=True)
    
    opsdev_foil['store'] = opsdev_foil['store'].astype("int32")
    opsdev_foil = opsdev_foil[['Division',
                               'Department',
                               'Section',
                               'Group_',
                               'level4',
                               'country',
                               'store',
                               'tpnb',
                               # "Product_Name",
                               # "SRP opening reduction opportunity",
                               "Pallet_info",
                               'Position_HFacing',
                               'merchstyle_ID',
                               'merchstyle_string',
                               'TrayNumberWide',
                               'Foil Replen Min Facing',
                               'foil']].query("foil > 0").drop_duplicates()
    
    opsdev_foil = opsdev_foil[['country','store','level4', 'tpnb', 'foil']].drop_duplicates()
    foil_calc.rename(columns={'Country':'country'}, inplace=True)
    opsdev_foil = opsdev_foil.merge(foil_calc[['country', 'level4', 'SRP opening reduction opportunity','extra disassemble %']],
                                    on=['country', 'level4'], how='left')
    
      
        
    # opsdev
    
    
    
    
    # list of repl_type
    srp = ["Tray", "Case"]
    nsrp = ["Unit", "Display", "Alternate", "Loose", "Log Stack"]
    pallet_info = ["Half_Pallet", "Pallet", "Split_Pallet"]
    
    # create columns for repl_type
    
    
    opsdev["full_pallet"] = np.where(opsdev.Pallet_info == "Pallet", 1, 0)
    opsdev["mu"] = np.where(opsdev.Pallet_info == "Half_Pallet", 1, 0)
    opsdev["split_pallet"] = np.where(opsdev.Pallet_info == "Split_Pallet", 1, 0)
    opsdev["nsrp"] = np.where(
        (opsdev.merchstyle_string.isin(nsrp)) & (~opsdev.Pallet_info.isin(pallet_info)),
        1,
        0,
    )
    opsdev["srp"] = np.where(opsdev.merchstyle_string.isin(srp), 1, 0)
    opsdev["srp"] = np.where((opsdev.Pallet_info == "Split_Pallet"), 0, opsdev.srp)
    opsdev["srp"] = np.where(
        (opsdev.Pallet_info == "Pallet") | (opsdev.Pallet_info == "Half_Pallet"),
        0,
        opsdev.srp,
    )
    
    for x in ['Division','Department', 'Section', 'Group_', 'Subgroup' ]:
        opsdev[x] = opsdev[x].replace('','0')
    
    opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]] = opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]].astype("int64")
    
    opsdev['icream_nsrp'] = np.where((opsdev['Division'] == 1)
                                    & (opsdev['Department'] == 17)
                                    & (opsdev['Section'] == 1702)
                                    & (opsdev.merchstyle_string.isin(['Loose', 'Alternate']))
                                    , 1, 0)
    
    for x in [ "srp","nsrp","mu","full_pallet", "split_pallet"]:
        
        opsdev[x] = np.where(opsdev['icream_nsrp'] == 1, 0, opsdev[x] )
    
    
    # rename columns
    
    opsdev.rename(
        columns={
            "Country":  "country",
            "Store_Number": "store",
            "Product_id": "tpnb",
            # "Product_Name": "product_name",
            "Displaygroup_description": "display_group",
            "Displaygroup": "drg"
        },
        inplace=True,
    )
    opsdev["checkout_stand_flag"] = np.where(
        opsdev["display_group"].str.lower().str.contains("checkout"), 1, 0
    )
    opsdev["clipstrip_flag"] = np.where(
        opsdev["display_group"].str.lower().str.contains("clipstrip"), 1, 0
    )
    opsdev["backroom_flag"] = np.where(opsdev["drg"].isin(["Z2D", "L1N"]), 1, 0)
    opsdev["store"] = opsdev["store"].astype(float).astype(int)
    opsdev["tpnb"] = (
        pd.to_numeric(opsdev.tpnb, errors="coerce").fillna(0).astype(float).astype(int)
    )
    checkout_stand = opsdev[opsdev.checkout_stand_flag == 1][
        ["store", "tpnb"]
    ].drop_duplicates()
    checkout_stand["checkout_stand_flag"] = int(1)
    clipstrip_flag = opsdev[opsdev.clipstrip_flag == 1][["store", "tpnb"]].drop_duplicates()
    clipstrip_flag["clipstrip_flag"] = int(1)
    backroom_flag = opsdev[opsdev.backroom_flag == 1][["store", "tpnb"]].drop_duplicates()
    backroom_flag["backroom_flag"] = int(1)
    
    opsdev = opsdev[
        [
            "country",
            "store",
            # "level4",
            "tpnb",
            # "product_name",
            "srp",
            "nsrp",
            "mu",
            "full_pallet",
            "split_pallet",
            "icream_nsrp"
    
        ]
    ]
    opsdev = opsdev.merge(checkout_stand, on=["store", "tpnb"], how="left")
    opsdev["checkout_stand_flag"].replace(np.nan, 0, inplace=True)
    opsdev = opsdev.merge(clipstrip_flag, on=["store", "tpnb"], how="left")
    opsdev["clipstrip_flag"].replace(np.nan, 0, inplace=True)
    opsdev = opsdev.merge(backroom_flag, on=["store", "tpnb"], how="left")
    opsdev["backroom_flag"].replace(np.nan, 0, inplace=True)
    
    # get final opsdev looking
    
    
    opsdev = opsdev.drop_duplicates()
    
    # filter duplicates and form the dataframe again
    
    grouped_opsdev = (
        opsdev.groupby(["country",
                        "store",
                        # "level4", 
                        "tpnb",
                        # "product_name", 
                        ], observed = True)
        .agg(
            {
                "srp": "sum",
                "nsrp": "sum",
                "mu": "sum",
                "full_pallet": "sum",
                "split_pallet": "sum",
                "icream_nsrp": "sum",
                "checkout_stand_flag": "sum",
                "clipstrip_flag": "sum",
                "backroom_flag": "sum",
            }
        )
        .reset_index()
    )
    grouped_opsdev["total"] = (
        grouped_opsdev.srp
        + grouped_opsdev.nsrp
        + grouped_opsdev.mu
        + grouped_opsdev.full_pallet
        + grouped_opsdev.split_pallet
    )
    grouped_opsdev["full_pallet"] = np.where(
        (grouped_opsdev.full_pallet == 1) & (grouped_opsdev.total > 1),
        1,
        grouped_opsdev.full_pallet,
    )
    grouped_opsdev["mu"] = np.where(
        (grouped_opsdev.total > 1) & (grouped_opsdev.full_pallet == 1), 0, grouped_opsdev.mu
    )
    grouped_opsdev["split_pallet"] = np.where(
        (grouped_opsdev.total > 1) & (grouped_opsdev.full_pallet == 1)
        | (grouped_opsdev.mu == 1),
        0,
        grouped_opsdev.split_pallet,
    )
    grouped_opsdev["nsrp"] = np.where(
        (grouped_opsdev.total > 1)
        & (
            (grouped_opsdev.full_pallet == 1)
            | (grouped_opsdev.mu == 1)
            | grouped_opsdev.split_pallet
            == 1
        )
        | (grouped_opsdev.srp == 1)
        | (grouped_opsdev.icream_nsrp == 1),
        0,
        grouped_opsdev.nsrp,
    )
    grouped_opsdev["srp"] = np.where(
        (grouped_opsdev.total > 1)
        & (
            (grouped_opsdev.full_pallet == 1)
            | (grouped_opsdev.mu == 1)
            | grouped_opsdev.split_pallet
            == 1
        ),
        0,
        grouped_opsdev.srp,
    )
    
    grouped_opsdev["icream_nsrp"] = np.where(
        (grouped_opsdev.total > 1)
        & (
            (grouped_opsdev.full_pallet == 1)
            | (grouped_opsdev.mu == 1)
            | (grouped_opsdev.split_pallet == 1)
            | (grouped_opsdev.srp == 1)
        ),
        0,
        grouped_opsdev.icream_nsrp,
    )
    
    
    
    grouped_opsdev["total"] = (
        grouped_opsdev.srp
        + grouped_opsdev.nsrp
        + grouped_opsdev.mu
        + grouped_opsdev.full_pallet
        + grouped_opsdev.split_pallet
        + grouped_opsdev.icream_nsrp
    )
    
    
    # check where is duplicates
    dupl_rows = grouped_opsdev[grouped_opsdev.total > 1].shape[0]
    print(f"We have: {dupl_rows} rows duplicated")
    
    
    # final dataframe
    grouped_opsdev.drop(columns={"total"}, inplace=True)
    opsdev = grouped_opsdev.copy()
    opsdev["checkout_stand_flag"] = np.where(opsdev["checkout_stand_flag"] > 0, 1, 0)
    opsdev["clipstrip_flag"] = np.where(opsdev["clipstrip_flag"] > 0, 1, 0)
    opsdev["backroom_flag"] = np.where(opsdev["backroom_flag"] > 0, 1, 0)
    
    
    # foil_calc.rename(columns={"Country": "country"}, inplace=True)
    
    
    
    # opsdev = opsdev.merge(opsdev_foil, on=["store", "tpnb"], how="left")
    # opsdev["foil"] = opsdev["foil"].fillna(1)
    # opsdev = opsdev.drop_duplicates()

    return opsdev, opsdev_foil


@rmf.timeit
def category_reset_to_df(directory, catres_path):
    
    print("\nCategory Reset categories downloading and merging with dataset..\n")
    
    conn = pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    )



    catres = pd.read_excel(directory / catres_path)

    catres_ = catres[["country","DIV_ID","DEP_ID", "SEC_ID", "GRP_ID", "SGR_ID", "Category name"]].drop_duplicates()

    for x in ['DIV_ID', 'DEP_ID', 'SEC_ID', 'GRP_ID', 'SGR_ID']:
        catres_[x] = catres_[x].apply(str).str.pad(width=4, side='left', fillchar='0')
        
    catres_['level5'] = catres_[['DIV_ID', 'DEP_ID', 'SEC_ID', 'GRP_ID', 'SGR_ID']].apply(lambda row: ''.join(row.values.astype(str)), axis=1)


    products=  catres_.groupby("country")["level5"].apply(lambda s: s.tolist()).to_dict()


    df2 = pd.DataFrame()
    for k, v in products.items():
        
        s = list()
        for x in v:

            s.append(str(x))

        catres_tuple = tuple(s)

        sql = """ SELECT cntr_code AS country, slad_tpnb AS tpnb, slad_tpn AS tpn, hier.pmg AS pmg,
        dmat_div_des_en AS DIV_DESC,
        dmat_div_code as DIV_ID,
        dmat_dep_des_en AS DEP_DESC,
        dmat_dep_code as DEP_ID,
        dmat_sec_des_en AS SEC_DESC,
        dmat_sec_code as SEC_ID,
        dmat_grp_des_en AS GRP_DESC,
        dmat_grp_code as GRP_ID,
        dmat_sgr_des_en AS SGR_DESC,
        dmat_sgr_code as SGR_ID,
        slad_long_des as product_name
                FROM DM.dim_artgld_details mstr
                JOIN tesco_analysts.hierarchy_spm hier
                ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0")
                AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0")
                AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0")
                AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0")
                AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
        WHERE /*slad_tpnb in (tpnb)*/
        cntr_code = '{k}'
        AND CONCAT(dmat_div_code, dmat_dep_code, dmat_sec_code,dmat_grp_code, dmat_sgr_code ) in {catres}
        AND cntr_code <> "PL"
        AND dmat_sgr_des_en <> "Do not use"
        GROUP BY cntr_code, hier.pmg, slad_tpnb,slad_tpn,
        dmat_div_des_en,
        dmat_div_code,
        dmat_dep_des_en,
        dmat_dep_code,
        dmat_sec_des_en,
        dmat_sec_code,
        dmat_grp_des_en,
        dmat_grp_code,
        dmat_sgr_des_en,
        dmat_sgr_code,
        slad_long_des
        
        """.format(k=k, catres=catres_tuple)
        
        art_gold = pd.read_sql(sql, conn)
        df2 = pd.concat([df2, art_gold])
        
    df2['level5'] = df2['DIV_ID'] + df2['DEP_ID'] + df2['SEC_ID']+ df2['GRP_ID']+ df2['SGR_ID']

    df2 = df2.merge(catres_[['country','level5','Category name']].drop_duplicates(), on=['country','level5'], how='left')
    
    return df2


@rmf.timeit
def SRD_database_by_stores(stores: list):
    
    
    
    print("\n'SRD_database_by_stores' has been started ..\n")
    
    SK_IKB = r"PVCKBSQLDB001SK.global.tesco.org;"
    HU_IKB = r"PVCKBSQLDB001HU.global.tesco.org;"
    CZ_IKB = r"PVCKBSQLDB001CZ.global.tesco.org;"

    srdTable_CE = pd.DataFrame()
    
    countries_dict = {'SK' : SK_IKB,
                      'HU': HU_IKB,
                      'CZ': CZ_IKB}
    
    def store_countries(store_list):
        # Initialize an empty set to hold the country codes
        countries = set()
        
        # Iterate over each store number in the list
        for store in store_list:
            # Convert the store number to a string and check the first digit
            first_digit = str(store)[0]
            
            # Add the corresponding country code to the set
            if first_digit == '1':
                countries.add('CZ')
            elif first_digit == '2':
                countries.add('SK')
            elif first_digit == '4':
                countries.add('HU')
        
        # Convert the set to a list and return it
        return list(countries)
    
    def store_selector_by_countries(x, stores):
        # Define a dictionary to map country codes to the starting digit
        country_code_map = {
            'CZ': '1',
            'SK': '2',
            'HU': '4'
        }
        
        # Get the starting digit for the given country code
        start_digit = country_code_map.get(x)
        
        # If the country code is not in the map, return an empty list
        if start_digit is None:
            return []
        
        # Filter the stores list to include only those starting with the specified digit
        filtered_stores = [store for store in stores if str(store).startswith(start_digit)]
        
        return filtered_stores
    
    
    for x in store_countries(stores):
        
        y = [s for y,s in countries_dict.items() if y == x][0]
        
        with pyodbc.connect(
            "Driver={ODBC Driver 18 for SQL Server};"
            f"Server={y};"
            "Database=CKB;"
            "Trusted_Connection=yes;"
            "TrustServerCertificate=yes;"
            "Encrypt=yes;"
        ) as conn:
            

                
                
                
                
                def remove_first_character(store_list):
                    # Remove the first character from each element in the list
                    return list(set([int(str(store)[1:]) for store in store_list]))
                
                
                def list_to_tuple_format(store_list):
                    # Convert the list to a tuple
                    result_tuple = tuple(store_list)
                    
                    # Check the length of the tuple
                    if len(result_tuple) == 1:
                        # Format the single element without a trailing comma
                        return f"({result_tuple[0]})"
                    else:
                        # Format the tuple normally
                        return str(result_tuple)
                
                
                stores_for_sql = list_to_tuple_format(remove_first_character(store_selector_by_countries(x, stores)))
                
            
            
            
            
                srd_query = """
                           
                           
   
   					set nocount on;
   					IF OBJECT_ID('tempdb..#Flop_POG_1') IS NOT NULL DROP TABLE #Flop_POG_1;
   					IF OBJECT_ID('tempdb..#Pog_Table_0') IS NOT NULL DROP TABLE #Pog_Table_0;
   					IF OBJECT_ID('tempdb..#Pog_Table_1') IS NOT NULL DROP TABLE #Pog_Table_1;
   					IF OBJECT_ID('tempdb..#Darab_1') IS NOT NULL DROP TABLE #Darab_1;
   					IF OBJECT_ID('tempdb..#Pos_Table_1') IS NOT NULL DROP TABLE #Pos_Table_1;
   					IF OBJECT_ID('tempdb..#Flop2') IS NOT NULL DROP TABLE #Flop2;
   					IF OBJECT_ID('tempdb..#Final') IS NOT NULL DROP TABLE #Final;
   					IF OBJECT_ID('tempdb..#Final2') IS NOT NULL DROP TABLE #Final2;
   
   					DECLARE @Country nvarchar(2) = '{x}';
   
   					-- Floorplan base data
   					Select distinct 
   						f.dbkey as 'FlopID',
   						f.desc12 as 'Store_Number',
   						f.desc11 as 'Store_Name',
   						f.desc2 as 'Store_Format',
   						fs.DBParentPlanogramKey,
   						fs.dbkey as 'FS_DBkey',
   						fs.SegmentStart,
   						fs.SegmentEnd
   						INTO #Flop_POG_1 
   						FROM ckb.dbo.ix_flr_floorplan as f WITH (nolock) join   
   							 ckb.dbo.ix_flr_section as fs WITH (nolock) on (fs.dbparentfloorplankey=f.dbkey)
   						WHERE f.dbstatus in (1) --and f.desc12 in (1520) --and fs.DBParentPlanogramKey=447919
                           AND f.desc12 in {stores}
   						OPTION (MAXDOP 1);
   
   					-- POG base data
   					Select distinct 
   						p.desc2 as 'Displaygroup',
   						p.desc24 as 'Displaygroup_description', 
   						p.Dbkey as 'POG_ID', 
   						p.Name as 'POG_Name',
   						p.NumberOfProductsAllocated,
   						p.desc8 as 'NumberOfMods'
   						INTO #Pog_Table_0
   						FROM ckb.dbo.ix_spc_planogram as p WITH (nolock) 
   						WHERE p.DBStatus not in (4,3) and 
   							  p.dbkey in (Select distinct #Flop_POG_1.DBParentPlanogramKey FROM #Flop_POG_1)
   						OPTION (MAXDOP 1);
   
   					-- Position records
   					Select distinct 
   						pos.segment as 'segmentnumber',
   						pos.HFacings as 'Position_HFacing',
   						pos.VFacings as 'Position_VFacing', 
   						pos.DFacings as 'Position_DFacing',
   						pos.merchstyle as 'merchstyle_ID',
   						pos.capacity as 'Position_Capacity', 
   						pos.DBParentFixtureKey,
   						pos.DBParentPlanogramKey,
   						pos.dbparentproductkey
   						INTO #Pos_Table_1
   						FROM #Pog_Table_0 as Pog_Table_0 join 
   							ckb.dbo.ix_spc_position pos WITH (nolock) on (Pog_Table_0.POG_ID=pos.dbparentplanogramkey)  
   						OPTION (MAXDOP 1);
   
   					-- POG base data
   					Select distinct 
   						Pog_Table_0.Displaygroup,
   						Pog_Table_0.Displaygroup_description, 
   						Pog_Table_0.POG_ID, 
   						Pog_Table_0.POG_Name, 
   						Pog_Table_0.NumberOfProductsAllocated,
   						Pog_Table_0.NumberOfMods,
   						Pos_Table_1.segmentnumber,
   						fx.name as 'Fixture_name',
   						fx.depth as 'Fixture_depth', 
   						fx.Width as 'Fixture_width',
   						fx.x as 'Fixture_X_position', 
   						fx.y as 'Fixture_Y_position', 
   						pr.id as 'Product_id', 
   						pr.name as 'Product_Name',
   						Pos_Table_1.Position_HFacing,
   						Pos_Table_1.Position_VFacing, 
   						Pos_Table_1.Position_DFacing,
   						Pos_Table_1.merchstyle_ID,
   						Pos_Table_1.Position_Capacity,
   						Case when Pos_Table_1.merchstyle_ID=0 then 'Unit'
   							 when Pos_Table_1.merchstyle_ID=1 then 'Tray'
   							 when Pos_Table_1.merchstyle_ID=2 then 'Case'
   							 when Pos_Table_1.merchstyle_ID=3 then 'Display'
   							 when Pos_Table_1.merchstyle_ID=4 then 'Alternate'
   							 when Pos_Table_1.merchstyle_ID=5 then 'Loose'
   							 when Pos_Table_1.merchstyle_ID=6 then 'Log Stack' 
   							 else 'NULL' 
   						End as 'merchstyle_string',
   						pr.desc18 as brand, 
   						pr.desc16 as ownbrand,
   						pr.Height, 
   						pr.Width, 
   						pr.Depth, 
   						pr.TrayHeight, 
   						pr.TrayWidth, 
   						pr.TrayDepth, 
   						pr.TrayNumberHigh, 
   						pr.TrayNumberWide, 
   						pr.TrayNumberDeep, 
   						pr.TrayTotalNumber,  
   						pr.CaseHeight, 
   						pr.CaseWidth, 
   						pr.CaseDepth, 
   						pr.CaseNumberHigh, 
   						pr.CaseNumberWide, 
   						pr.CaseNumberDeep,
   						pr.CaseTotalNumber,
   						SUBSTRING( pr.desc8, 1, 4) as 'H1', 
   						SUBSTRING( pr.desc8, 5, 4) as 'H2', 
   						SUBSTRING( pr.desc8, 9, 4) as 'H3', 
   						SUBSTRING( pr.desc8, 13, 4) as 'H4', 
   						SUBSTRING( pr.desc8, 16, 4) as 'H5', 
   						per.capacity as 'Capacity_on_Planogram'
   						INTO #Pog_Table_1
   						FROM #Pog_Table_0 as Pog_Table_0 join
   							 #Pos_Table_1 as Pos_Table_1 on (Pog_Table_0.POG_ID=Pos_Table_1.dbparentplanogramkey) join
   							 ckb.dbo.ix_spc_product as pr WITH (nolock) on (pr.dbkey=Pos_Table_1.dbparentproductkey) join 
   							 ckb.dbo.ix_spc_fixture as fx WITH (nolock) on (Pog_Table_0.POG_ID=fx.DBParentPlanogramKey and Pos_Table_1.DBparentfixturekey=fx.DBKey) join
   							 ckb.dbo.ix_spc_performance as Per with(nolock) on (pr.dbkey= per.DBParentProductKey and Pog_Table_0.POG_ID=per.dbparentplanogramkey)
   
   						OPTION (MAXDOP 1);
   
   					--Position count based on fixture
   					select 
   						Pog_Table_1.POG_ID, 
   						Pog_Table_1.segmentnumber, 
   						Pog_Table_1.Fixture_X_position, 
   						Pog_Table_1.Fixture_Y_position, 
   						COUNT(Pog_Table_1.POG_ID & Pog_Table_1.segmentnumber & convert(int, Pog_Table_1.Fixture_X_position) & convert(int, Pog_Table_1.Fixture_Y_position)) as 'Darab'
   						INTO #Darab_1
   						FROM #Pog_Table_1 as Pog_Table_1
   						GROUP by Pog_Table_1.POG_ID,
   								 Pog_Table_1.segmentnumber, 
   								 Pog_Table_1.Fixture_X_position,
   								 Pog_Table_1.Fixture_Y_position
   						OPTION (MAXDOP 1);
   
   					select distinct 
   						tabla.FlopID, 
   						tabla.Store_Number,
   						tabla.POG_ID,
   						IIF(tabla.Z_Szegment = Tabla.NumberOfMods,1,
   							IIF(tabla.Z_Szegment > Tabla.NumberOfMods,
   							IIf((tabla.Z_Szegment /Tabla.NumberOfMods)=(round(tabla.Z_Szegment /Tabla.NumberOfMods,1)),tabla.Z_Szegment /Tabla.NumberOfMods,0),0)) as 'X_POG_NUM'
   						INTO #Flop2
   						FROM (select distinct
   								tabla.FlopID,
   								tabla.Store_Number,
   								tabla.POG_ID,
   								tabla.NumberOfMods,
   								sum(iif( tabla.SegmentStart=0 and tabla.SegmentEnd=0,tabla.NumberOfMods,
   									iif(tabla.SegmentStart=1 and tabla.SegmentEnd=tabla.NumberOfMods,tabla.NumberOfMods, 
   									iif(tabla.SegmentStart=tabla.SegmentEnd,1,tabla.SegmentEnd-
   									iif(tabla.SegmentStart>1,tabla.SegmentStart-1,0))))) as 'Z_Szegment' 
   								From (select distinct
   											Flop_POG_1.FlopID,
   											Flop_POG_1.fs_dbkey,
   											Flop_POG_1.Store_Number,
   											Pog_Table_1.POG_ID,
   											Pog_Table_1.NumberOfMods,
   											Flop_POG_1.SegmentStart,
   											Flop_POG_1.SegmentEnd
   											FROM #Flop_POG_1 as Flop_POG_1 join 
   												 #Pog_Table_1 as Pog_Table_1 on (Flop_POG_1.DBParentPlanogramKey=Pog_Table_1.POG_ID)
   										) as tabla
   								GROUP by tabla.FlopID,
   										 tabla.Store_Number,
   										 tabla.POG_ID,
   										 tabla.NumberOfMods
   								) as tabla
   						OPTION (MAXDOP 1);
   
   					--Final report
   					select distinct
   						@Country as 'Country',
   						Flop_POG_1.Store_Number, 
   						Flop_POG_1.Store_Name,
   						Flop_POG_1.Store_Format,
   						Pog_Table_1.Displaygroup,
   						Pog_Table_1.Displaygroup_description, 
   						Pog_Table_1.POG_ID, 
   						Pog_Table_1.POG_Name,
   						Pog_Table_1.segmentnumber,
   						Pog_Table_1.Fixture_name,
   						Pog_Table_1.Fixture_depth, 
   						Pog_Table_1.Fixture_width, 
   						IIF(substring(Pog_Table_1.Fixture_name, 1, 3) = 'PAL' or substring (Pog_Table_1.Fixture_name, 1, 3) = 'Rak',
   							IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =60) or (Pog_Table_1.Fixture_depth = 60 and Pog_Table_1.Fixture_width = 80),
   							IIf(Darab_1.Darab >1,'Split_on_Half_Pallet', 'Half_Pallet'), 
   							IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =120) or (Pog_Table_1.Fixture_depth= 120 and Pog_Table_1.Fixture_width = 80),
   							IIf(Darab_1.Darab >1, 'Split_Pallet','Pallet'),'')),'') as 'Pallet_info',
   						
   						IIF(substring(Pog_Table_1.Fixture_name, 1, 3) = 'PAL' or substring (Pog_Table_1.Fixture_name, 1, 3) = 'Rak',
   							IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =60) or (Pog_Table_1.Fixture_depth = 60 and Pog_Table_1.Fixture_width = 80),
   							IIf(Darab_1.Darab >1,'Split_on_Half_Pallet', 'Half_Pallet'), 
   							IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =120) or (Pog_Table_1.Fixture_depth= 120 and Pog_Table_1.Fixture_width = 80),
   							IIf(Darab_1.Darab >1, 'Split_Pallet','Pallet'),Pog_Table_1.merchstyle_string)),Pog_Table_1.merchstyle_string) as 'INFO',
   
   						Pog_Table_1.Product_id, 
   						Pog_Table_1.Product_Name,
   						Pog_Table_1.Position_HFacing,
   						Pog_Table_1.Position_VFacing, 
   						Pog_Table_1.Position_DFacing,
   						Pog_Table_1.merchstyle_ID,
   						Pog_Table_1.merchstyle_string,
   						Pog_Table_1.brand, 
   						Pog_Table_1.ownbrand,
   						Pog_Table_1.Height, 
   						Pog_Table_1.Width, 
   						Pog_Table_1.Depth, 
   						Pog_Table_1.TrayHeight, 
   						Pog_Table_1.TrayWidth, 
   						Pog_Table_1.TrayDepth, 
   						Pog_Table_1.TrayNumberHigh, 
   						Pog_Table_1.TrayNumberWide, 
   						Pog_Table_1.TrayNumberDeep, 
   						Pog_Table_1.TrayTotalNumber,  
   						Pog_Table_1.CaseHeight, 
   						Pog_Table_1.CaseWidth, 
   						Pog_Table_1.CaseDepth, 
   						Pog_Table_1.CaseNumberHigh, 
   						Pog_Table_1.CaseNumberWide, 
   						Pog_Table_1.CaseNumberDeep,
   						Pog_Table_1.CaseTotalNumber,
   						Pog_Table_1.H1 as 'Division', 
   						Pog_Table_1.H2 as 'Department',
   						Pog_Table_1.H3 as 'Section',
   						Pog_Table_1.H4 as 'Group_',
   						Pog_Table_1.H5 as 'Subgroup',
   						Pog_Table_1.NumberOfProductsAllocated,
   						Pog_Table_1.Position_Capacity,
   						fl.X_POG_NUM as 'Planogram_on_Store',
   						Pog_Table_1.Position_Capacity *fl.X_POG_NUM as 'Position_Capacity_X_Planogram_on_Store'
   						INTO #Final
   						FROM #Flop_POG_1 as Flop_POG_1 join 
   							 #Pog_Table_1 as Pog_Table_1 on (Flop_POG_1.DBParentPlanogramKey=Pog_Table_1.POG_ID) join
   							 #Darab_1 as Darab_1 on (Pog_Table_1.POG_ID=Darab_1.POG_ID and Pog_Table_1.segmentnumber=Darab_1.segmentnumber and Pog_Table_1.Fixture_X_position=Darab_1.Fixture_X_position and Pog_Table_1.Fixture_Y_position=Darab_1.Fixture_Y_position) left join
   							 #Flop2 as FL on(FL.Store_Number=Flop_POG_1.Store_Number and FL.POG_ID=Flop_POG_1.DBParentPlanogramKey)
   						OPTION (MAXDOP 1);
   
   
   					Select * from #Final;
   
   					----  Innentől csak az összesítések vannak és csoportosítások
   					--select
   					--	FI.Country,
   					--	FI.Division,
   					--	FI.Department,
   					--	FI.Section,
   					--	FI.Group_ as 'Group',
   					--	FI.Subgroup,
   					--	FI.Store_Number,
   					--	FI.Product_id,
   					--	FI.Product_Name,
   					--	FI.INFO,
   					--	SUM(FI.Planogram_on_Store) as 'CountOfLocation',
   					--	SUM(FI.Position_Capacity_X_Planogram_on_Store) AS 'Capacity_BasedOn_Merchstyle'
   
   					--	INTO #Final2
   					--	FROM #Final as FI
   					--	GROUP by FI.Country,
   					--		FI.Store_Number,
   					--		FI.Product_id,
   					--		FI.Product_Name,
   					--		FI.INFO,
   					--		FI.Division,
   					--		FI.Department,
   					--		FI.Section,
   					--		FI.Group_,
   					--		FI.Subgroup
   					--	OPTION (MAXDOP 1);
   
   					--select F2.*,F3.Capacity_on_Store 
   					--			FROM #Final2 as F2 
   					--				Left join (
   					--					select
   					--						FI.Store_Number,
   					--						FI.Product_id,
   					--						SUM(FI.Capacity_BasedOn_Merchstyle) AS 'Capacity_on_Store'
   					--						FROM #Final2 as FI
   					--						GROUP by FI.Store_Number,FI.Product_id) as 
   					--					F3 on (F2.Store_Number=F3.Store_Number and F2.Product_id=F3.Product_id)
   					--			OPTION (MAXDOP 1);                                                
                                                   
                           
                           
   
   
   
                           
                           
       
                           """.format(
                   x=x, stores = stores_for_sql
               )
   
        srdTable_CE = pd.concat([srdTable_CE, pd.read_sql(srd_query, conn)])
        print(f"\n{x} SRD with {x} is ready!\n")
        
    if len(stores) < 15:
        print(f"\n SRD with stores: {stores} table is ready!\n")
        
    print("\nCE SRD with stores is ready!\n")

    srdTable_CE = srdTable_CE.fillna(0)

    return srdTable_CE

            
            
        
    
    
    
    
@rmf.timeit
def SRD_database_TPNB(dict_list: dict ) -> pd.DataFrame():

    SK_IKB = r"PVCKBSQLDB001SK.global.tesco.org;"
    HU_IKB = r"PVCKBSQLDB001HU.global.tesco.org;"
    CZ_IKB = r"PVCKBSQLDB001CZ.global.tesco.org;"

    srdTable_CE = pd.DataFrame()
    
    countries_dict = {'SK' : SK_IKB,
                      'HU': HU_IKB,
                      'CZ': CZ_IKB}
    

    
    for k, v in dict_list.items():
        
        y = [s for y,s in countries_dict.items() if y == k][0]



        with pyodbc.connect(
            "Driver={ODBC Driver 18 for SQL Server};"
            f"Server={y};"
            "Database=CKB;"
            "Trusted_Connection=yes;"
            "TrustServerCertificate=yes;"
            "Encrypt=yes;"
        ) as conn:
            
            s = list()
            for x in v:

                s.append(str(x))

                tpnbs = ",".join(s)
            

            
            

                srd_query = """
                
                
                
					set nocount on;
					IF OBJECT_ID('tempdb..#Flop_POG_1') IS NOT NULL DROP TABLE #Flop_POG_1;
					IF OBJECT_ID('tempdb..#Pog_Table_0') IS NOT NULL DROP TABLE #Pog_Table_0;
					IF OBJECT_ID('tempdb..#Pog_Table_1') IS NOT NULL DROP TABLE #Pog_Table_1;
					IF OBJECT_ID('tempdb..#Darab_1') IS NOT NULL DROP TABLE #Darab_1;
					IF OBJECT_ID('tempdb..#Pos_Table_1') IS NOT NULL DROP TABLE #Pos_Table_1;
					IF OBJECT_ID('tempdb..#Flop2') IS NOT NULL DROP TABLE #Flop2;
					IF OBJECT_ID('tempdb..#Final') IS NOT NULL DROP TABLE #Final;
					IF OBJECT_ID('tempdb..#Final2') IS NOT NULL DROP TABLE #Final2;

					DECLARE @Country nvarchar(2) = '{k}';

					-- Floorplan base data
					Select distinct 
						f.dbkey as 'FlopID',
						f.desc12 as 'Store_Number',
						f.desc11 as 'Store_Name',
						f.desc2 as 'Store_Format',
						fs.DBParentPlanogramKey,
						fs.dbkey as 'FS_DBkey',
						fs.SegmentStart,
						fs.SegmentEnd
						INTO #Flop_POG_1 
						FROM ckb.dbo.ix_flr_floorplan as f WITH (nolock) join   
							 ckb.dbo.ix_flr_section as fs WITH (nolock) on (fs.dbparentfloorplankey=f.dbkey)
						WHERE f.dbstatus in (1) --and f.desc12 in (1520) --and fs.DBParentPlanogramKey=447919
						OPTION (MAXDOP 1);

					-- POG base data
					Select distinct 
						p.desc2 as 'Displaygroup',
						p.desc24 as 'Displaygroup_description', 
						p.Dbkey as 'POG_ID', 
						p.Name as 'POG_Name',
						p.NumberOfProductsAllocated,
						p.desc8 as 'NumberOfMods'
						INTO #Pog_Table_0
						FROM ckb.dbo.ix_spc_planogram as p WITH (nolock) 
						WHERE p.DBStatus not in (4,3) and 
							  p.dbkey in (Select distinct #Flop_POG_1.DBParentPlanogramKey FROM #Flop_POG_1)
						OPTION (MAXDOP 1);

					-- Position records
					Select distinct 
						pos.segment as 'segmentnumber',
						pos.HFacings as 'Position_HFacing',
						pos.VFacings as 'Position_VFacing', 
						pos.DFacings as 'Position_DFacing',
						pos.merchstyle as 'merchstyle_ID',
						pos.capacity as 'Position_Capacity', 
						pos.DBParentFixtureKey,
						pos.DBParentPlanogramKey,
						pos.dbparentproductkey
						INTO #Pos_Table_1
						FROM #Pog_Table_0 as Pog_Table_0 join 
							ckb.dbo.ix_spc_position pos WITH (nolock) on (Pog_Table_0.POG_ID=pos.dbparentplanogramkey)  
						OPTION (MAXDOP 1);

					-- POG base data
					Select distinct 
						Pog_Table_0.Displaygroup,
						Pog_Table_0.Displaygroup_description, 
						Pog_Table_0.POG_ID, 
						Pog_Table_0.POG_Name, 
						Pog_Table_0.NumberOfProductsAllocated,
						Pog_Table_0.NumberOfMods,
						Pos_Table_1.segmentnumber,
						fx.name as 'Fixture_name',
						fx.depth as 'Fixture_depth', 
						fx.Width as 'Fixture_width',
						fx.x as 'Fixture_X_position', 
						fx.y as 'Fixture_Y_position', 
						pr.id as 'Product_id', 
						pr.name as 'Product_Name',
						Pos_Table_1.Position_HFacing,
						Pos_Table_1.Position_VFacing, 
						Pos_Table_1.Position_DFacing,
						Pos_Table_1.merchstyle_ID,
						Pos_Table_1.Position_Capacity,
						Case when Pos_Table_1.merchstyle_ID=0 then 'Unit'
							 when Pos_Table_1.merchstyle_ID=1 then 'Tray'
							 when Pos_Table_1.merchstyle_ID=2 then 'Case'
							 when Pos_Table_1.merchstyle_ID=3 then 'Display'
							 when Pos_Table_1.merchstyle_ID=4 then 'Alternate'
							 when Pos_Table_1.merchstyle_ID=5 then 'Loose'
							 when Pos_Table_1.merchstyle_ID=6 then 'Log Stack' 
							 else 'NULL' 
						End as 'merchstyle_string',
						pr.desc18 as brand, 
						pr.desc16 as ownbrand,
						pr.Height, 
						pr.Width, 
						pr.Depth, 
						pr.TrayHeight, 
						pr.TrayWidth, 
						pr.TrayDepth, 
						pr.TrayNumberHigh, 
						pr.TrayNumberWide, 
						pr.TrayNumberDeep, 
						pr.TrayTotalNumber,  
						pr.CaseHeight, 
						pr.CaseWidth, 
						pr.CaseDepth, 
						pr.CaseNumberHigh, 
						pr.CaseNumberWide, 
						pr.CaseNumberDeep,
						pr.CaseTotalNumber,
						SUBSTRING( pr.desc8, 1, 4) as 'H1', 
						SUBSTRING( pr.desc8, 5, 4) as 'H2', 
						SUBSTRING( pr.desc8, 9, 4) as 'H3', 
						SUBSTRING( pr.desc8, 13, 4) as 'H4', 
						SUBSTRING( pr.desc8, 16, 4) as 'H5', 
						per.capacity as 'Capacity_on_Planogram'
						INTO #Pog_Table_1
						FROM #Pog_Table_0 as Pog_Table_0 join
							 #Pos_Table_1 as Pos_Table_1 on (Pog_Table_0.POG_ID=Pos_Table_1.dbparentplanogramkey) join
							 ckb.dbo.ix_spc_product as pr WITH (nolock) on (pr.dbkey=Pos_Table_1.dbparentproductkey) join 
							 ckb.dbo.ix_spc_fixture as fx WITH (nolock) on (Pog_Table_0.POG_ID=fx.DBParentPlanogramKey and Pos_Table_1.DBparentfixturekey=fx.DBKey) join
							 ckb.dbo.ix_spc_performance as Per with(nolock) on (pr.dbkey= per.DBParentProductKey and Pog_Table_0.POG_ID=per.dbparentplanogramkey)
                             where pr.id in ({tpnbs})
						OPTION (MAXDOP 1);

					--Position count based on fixture
					select 
						Pog_Table_1.POG_ID, 
						Pog_Table_1.segmentnumber, 
						Pog_Table_1.Fixture_X_position, 
						Pog_Table_1.Fixture_Y_position, 
						COUNT(Pog_Table_1.POG_ID & Pog_Table_1.segmentnumber & convert(int, Pog_Table_1.Fixture_X_position) & convert(int, Pog_Table_1.Fixture_Y_position)) as 'Darab'
						INTO #Darab_1
						FROM #Pog_Table_1 as Pog_Table_1
						GROUP by Pog_Table_1.POG_ID,
								 Pog_Table_1.segmentnumber, 
								 Pog_Table_1.Fixture_X_position,
								 Pog_Table_1.Fixture_Y_position
						OPTION (MAXDOP 1);

					select distinct 
						tabla.FlopID, 
						tabla.Store_Number,
						tabla.POG_ID,
						IIF(tabla.Z_Szegment = Tabla.NumberOfMods,1,
							IIF(tabla.Z_Szegment > Tabla.NumberOfMods,
							IIf((tabla.Z_Szegment /Tabla.NumberOfMods)=(round(tabla.Z_Szegment /Tabla.NumberOfMods,1)),tabla.Z_Szegment /Tabla.NumberOfMods,0),0)) as 'X_POG_NUM'
						INTO #Flop2
						FROM (select distinct
								tabla.FlopID,
								tabla.Store_Number,
								tabla.POG_ID,
								tabla.NumberOfMods,
								sum(iif( tabla.SegmentStart=0 and tabla.SegmentEnd=0,tabla.NumberOfMods,
									iif(tabla.SegmentStart=1 and tabla.SegmentEnd=tabla.NumberOfMods,tabla.NumberOfMods, 
									iif(tabla.SegmentStart=tabla.SegmentEnd,1,tabla.SegmentEnd-
									iif(tabla.SegmentStart>1,tabla.SegmentStart-1,0))))) as 'Z_Szegment' 
								From (select distinct
											Flop_POG_1.FlopID,
											Flop_POG_1.fs_dbkey,
											Flop_POG_1.Store_Number,
											Pog_Table_1.POG_ID,
											Pog_Table_1.NumberOfMods,
											Flop_POG_1.SegmentStart,
											Flop_POG_1.SegmentEnd
											FROM #Flop_POG_1 as Flop_POG_1 join 
												 #Pog_Table_1 as Pog_Table_1 on (Flop_POG_1.DBParentPlanogramKey=Pog_Table_1.POG_ID)
										) as tabla
								GROUP by tabla.FlopID,
										 tabla.Store_Number,
										 tabla.POG_ID,
										 tabla.NumberOfMods
								) as tabla
						OPTION (MAXDOP 1);

					--Final report
					select distinct
						@Country as 'Country',
						Flop_POG_1.Store_Number, 
						Flop_POG_1.Store_Name,
						Flop_POG_1.Store_Format,
						Pog_Table_1.Displaygroup,
						Pog_Table_1.Displaygroup_description, 
						Pog_Table_1.POG_ID, 
						Pog_Table_1.POG_Name,
						Pog_Table_1.segmentnumber,
						Pog_Table_1.Fixture_name,
						Pog_Table_1.Fixture_depth, 
						Pog_Table_1.Fixture_width, 
						IIF(substring(Pog_Table_1.Fixture_name, 1, 3) = 'PAL' or substring (Pog_Table_1.Fixture_name, 1, 3) = 'Rak',
							IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =60) or (Pog_Table_1.Fixture_depth = 60 and Pog_Table_1.Fixture_width = 80),
							IIf(Darab_1.Darab >1,'Split_on_Half_Pallet', 'Half_Pallet'), 
							IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =120) or (Pog_Table_1.Fixture_depth= 120 and Pog_Table_1.Fixture_width = 80),
							IIf(Darab_1.Darab >1, 'Split_Pallet','Pallet'),'')),'') as 'Pallet_info',
						
						IIF(substring(Pog_Table_1.Fixture_name, 1, 3) = 'PAL' or substring (Pog_Table_1.Fixture_name, 1, 3) = 'Rak',
							IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =60) or (Pog_Table_1.Fixture_depth = 60 and Pog_Table_1.Fixture_width = 80),
							IIf(Darab_1.Darab >1,'Split_on_Half_Pallet', 'Half_Pallet'), 
							IIF((Pog_Table_1.Fixture_depth = 80 and Pog_Table_1.Fixture_width =120) or (Pog_Table_1.Fixture_depth= 120 and Pog_Table_1.Fixture_width = 80),
							IIf(Darab_1.Darab >1, 'Split_Pallet','Pallet'),Pog_Table_1.merchstyle_string)),Pog_Table_1.merchstyle_string) as 'INFO',

						Pog_Table_1.Product_id, 
						Pog_Table_1.Product_Name,
						Pog_Table_1.Position_HFacing,
						Pog_Table_1.Position_VFacing, 
						Pog_Table_1.Position_DFacing,
						Pog_Table_1.merchstyle_ID,
						Pog_Table_1.merchstyle_string,
						Pog_Table_1.brand, 
						Pog_Table_1.ownbrand,
						Pog_Table_1.Height, 
						Pog_Table_1.Width, 
						Pog_Table_1.Depth, 
						Pog_Table_1.TrayHeight, 
						Pog_Table_1.TrayWidth, 
						Pog_Table_1.TrayDepth, 
						Pog_Table_1.TrayNumberHigh, 
						Pog_Table_1.TrayNumberWide, 
						Pog_Table_1.TrayNumberDeep, 
						Pog_Table_1.TrayTotalNumber,  
						Pog_Table_1.CaseHeight, 
						Pog_Table_1.CaseWidth, 
						Pog_Table_1.CaseDepth, 
						Pog_Table_1.CaseNumberHigh, 
						Pog_Table_1.CaseNumberWide, 
						Pog_Table_1.CaseNumberDeep,
						Pog_Table_1.CaseTotalNumber,
						Pog_Table_1.H1 as 'Division', 
						Pog_Table_1.H2 as 'Department',
						Pog_Table_1.H3 as 'Section',
						Pog_Table_1.H4 as 'Group_',
						Pog_Table_1.H5 as 'Subgroup',
						Pog_Table_1.NumberOfProductsAllocated,
						Pog_Table_1.Position_Capacity,
						fl.X_POG_NUM as 'Planogram_on_Store',
						Pog_Table_1.Position_Capacity *fl.X_POG_NUM as 'Position_Capacity_X_Planogram_on_Store'
						INTO #Final
						FROM #Flop_POG_1 as Flop_POG_1 join 
							 #Pog_Table_1 as Pog_Table_1 on (Flop_POG_1.DBParentPlanogramKey=Pog_Table_1.POG_ID) join
							 #Darab_1 as Darab_1 on (Pog_Table_1.POG_ID=Darab_1.POG_ID and Pog_Table_1.segmentnumber=Darab_1.segmentnumber and Pog_Table_1.Fixture_X_position=Darab_1.Fixture_X_position and Pog_Table_1.Fixture_Y_position=Darab_1.Fixture_Y_position) left join
							 #Flop2 as FL on(FL.Store_Number=Flop_POG_1.Store_Number and FL.POG_ID=Flop_POG_1.DBParentPlanogramKey)
						OPTION (MAXDOP 1);


					Select * from #Final;

					----  Innentől csak az összesítések vannak és csoportosítások
					--select
					--	FI.Country,
					--	FI.Division,
					--	FI.Department,
					--	FI.Section,
					--	FI.Group_ as 'Group',
					--	FI.Subgroup,
					--	FI.Store_Number,
					--	FI.Product_id,
					--	FI.Product_Name,
					--	FI.INFO,
					--	SUM(FI.Planogram_on_Store) as 'CountOfLocation',
					--	SUM(FI.Position_Capacity_X_Planogram_on_Store) AS 'Capacity_BasedOn_Merchstyle'

					--	INTO #Final2
					--	FROM #Final as FI
					--	GROUP by FI.Country,
					--		FI.Store_Number,
					--		FI.Product_id,
					--		FI.Product_Name,
					--		FI.INFO,
					--		FI.Division,
					--		FI.Department,
					--		FI.Section,
					--		FI.Group_,
					--		FI.Subgroup
					--	OPTION (MAXDOP 1);

					--select F2.*,F3.Capacity_on_Store 
					--			FROM #Final2 as F2 
					--				Left join (
					--					select
					--						FI.Store_Number,
					--						FI.Product_id,
					--						SUM(FI.Capacity_BasedOn_Merchstyle) AS 'Capacity_on_Store'
					--						FROM #Final2 as FI
					--						GROUP by FI.Store_Number,FI.Product_id) as 
					--					F3 on (F2.Store_Number=F3.Store_Number and F2.Product_id=F3.Product_id)
					--			OPTION (MAXDOP 1);                                                

                        


                        
                        
    
                        """.format(
                k=k, tpnbs=tpnbs
            )

            srdTable_CE = pd.concat([srdTable_CE, pd.read_sql(srd_query, conn)])

            print(f"\n{k} SRD table is ready!\n")

    srdTable_CE = srdTable_CE.fillna(0)
    
    return srdTable_CE


def SRD_to_opsdev(a, directory, excel_inputs_f):
    
    # FOIL
    a = a[a["Store_Number"].notnull()]
    
    for x, y in zip(['NEW_Position_Capacity_X_Planogram_on_Store', 'New_Position_Capacity'], ['Position_Capacity_X_Planogram_on_Store', 'Position_Capacity']):
        a[x] = np.where(a[x].isna() , a[y], a[x] )
    
    opsdev = a.copy()
    
    opsdev['Planogram_on_Store'] = np.where(opsdev['Planogram_on_Store'] == 0, 1, opsdev['Planogram_on_Store'])
    opsdev['Position_Capacity_X_Planogram_on_Store'] =opsdev['Position_Capacity'] *  opsdev['Planogram_on_Store']
    
    foil_calc = pd.read_excel(directory / excel_inputs_f, sheet_name='foil_calc', usecols=['Country',
                                                                                            'level4',
                                                                                            'Foil Opening Reduction Opportunity',
                                                                                            'Proposed Opening %',
                                                                                            'Foil Replen Min Facing',
                                                                                            'SRP opening reduction opportunity',
                                                                                            'extra disassemble %'],
                              dtype={'level4': np.int64})
    
    
    
    
    # foil_calc = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\Repl\Repl_Stores_Inputs_2025_Q1_v6_GM and others_3 remaining stores.xlsx", sheet_name='foil_calc', usecols=['Country',
    #                                                                                         'level4',
    #                                                                                         'Foil Opening Reduction Opportunity',
    #                                                                                         'Proposed Opening %',
    #                                                                                         'Foil Replen Min Facing',
    #                                                                                         'SRP opening reduction opportunity',
    #                                                                                         'extra disassemble %'],
    #                           dtype={'level4': np.int64})
    
    
    opsdev_foil = opsdev.copy()
    
    for x in ['Division', 'Department', 'Section', 'Group_']:
        
        opsdev_foil[x] = np.where(opsdev_foil[x] == '', 0, opsdev_foil[x])
        opsdev_foil[x] = opsdev_foil[x].replace(np.nan,0)
    
    opsdev_foil['level4'] = [str(int(p1)) + str(int(p2)) + str(int(p3)) + str(int(p4)) for p1, p2, p3, p4 in zip(opsdev_foil['Division'],
                                                                                             opsdev_foil['Department'],
                                                                                             opsdev_foil['Section'],
                                                                                             opsdev_foil['Group_'])]
    
    
    opsdev_foil['level4'] = opsdev_foil['level4'].astype("int64")
    opsdev_foil = opsdev_foil.merge(foil_calc, on=['Country', 'level4'], how='left')
    
    cond=[(opsdev_foil['Foil Opening Reduction Opportunity'] == 1)
          & (opsdev_foil['merchstyle_string'] == 'Unit')
          & ((opsdev_foil['Pallet_info'].isna()) | (opsdev_foil['Pallet_info']=="")),
          
          (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)
          & (opsdev_foil['merchstyle_string'] == 'Tray')
          & ((opsdev_foil['Pallet_info'].isna()) | (opsdev_foil['Pallet_info']=="")),
          
          (opsdev_foil['Foil Opening Reduction Opportunity'] == 1)
          &  (opsdev_foil['merchstyle_string'] == 'Case')
          & ((opsdev_foil['Pallet_info'].isna()) | (opsdev_foil['Pallet_info']=="")),
          ]
    
    result = [opsdev_foil['Position_HFacing'],
              opsdev_foil['Position_HFacing'] * opsdev_foil['TrayNumberWide'],
              opsdev_foil['Position_HFacing'] * opsdev_foil['CaseNumberWide']]
    
    
    owise= (opsdev_foil['Position_HFacing'] * opsdev_foil['merchstyle_ID'])
    
    
    opsdev_foil['for_foil_check'] = np.select(cond,result,owise)
    
    opsdev_foil['foil'] = np.where((opsdev_foil['Foil Replen Min Facing'] < opsdev_foil['for_foil_check'])
                                   & (~opsdev_foil['Pallet_info'].isin(['Pallet', 'Half_Pallet', 'Split_Pallet'])), 
                                   opsdev_foil["Proposed Opening %"], 0)
    
    for x in [opsdev, opsdev_foil]:
        
        country_cond = [
            x["Country"] == "HU",
            x["Country"] == "SK",
            x["Country"] == "CZ",
        ]
        
        # Convert Store_Number to string first, then remove .0 if present
        store_number_str = x.Store_Number.astype(str).str.replace('.0', '', regex=False)
        
        store_code_create = [
            str(4) + store_number_str,
            str(2) + store_number_str,
            str(1) + store_number_str,
        ]
        
        # Use the original Store_Number as default (converted to int properly)
        x["Store_Number"] = np.select(country_cond, store_code_create, 
                                      default=x.Store_Number.astype('Int64'))
        x["Store_Number"] = x["Store_Number"].astype("int")
    
    
    
    
    opsdev_foil.rename(
        columns={
            "Country":  "country",
            "Store_Number": "store",
            "Product_id": "tpnb"}, inplace=True)
    
    opsdev_foil['store'] = opsdev_foil['store'].astype("int32")
    
    opsdev_foil = opsdev_foil[['Division',
                               'Department',
                               'Section',
                               'Group_',
                               'level4',
                               'country',
                               'store',
                               'tpnb',
                               # "Product_Name",
                               # "SRP opening reduction opportunity",
                               "Pallet_info",
                               'Position_HFacing',
                               'merchstyle_ID',
                               'merchstyle_string',
                               'TrayNumberWide',
                               'Foil Replen Min Facing',
                               'foil']].query("foil > 0").drop_duplicates()
    
    opsdev_foil = opsdev_foil[['country','store','level4', 'tpnb', 'foil']].drop_duplicates()
    
    foil_calc.rename(columns={'Country':'country'}, inplace=True)
    
    opsdev_foil = opsdev_foil.merge(foil_calc[['country', 'level4', 'SRP opening reduction opportunity','extra disassemble %']],
                                    on=['country', 'level4'], how='left')
        
    #opsdev part
    
    
    # icase
    
    opsdev['icase'] = np.where(opsdev['CaseTotalNumber'] == 0, opsdev['TrayTotalNumber'], opsdev['CaseTotalNumber'])
    
    # calculate shelf_cap ratio
    opsdev['shelf_cap_ratio'] = opsdev['Position_Capacity_X_Planogram_on_Store'] / opsdev.groupby(['Store_Number', 'Product_id'])['Position_Capacity_X_Planogram_on_Store'].transform('sum')
    
    # list of repl_type
    srp = ["Tray", "Case"]
    nsrp = ["Unit", "Display", "Alternate", "Loose", "Log Stack"]
    pallet_info = ["Half_Pallet", "Pallet", "Split_Pallet"]
    
    # create columns for repl_type
    opsdev["full_pallet"] = np.where(opsdev.Pallet_info == "Pallet", opsdev['shelf_cap_ratio'], 0)
    opsdev["mu"] = np.where(opsdev.Pallet_info == "Half_Pallet", opsdev['shelf_cap_ratio'], 0)
    opsdev["split_pallet"] = np.where(opsdev.Pallet_info == "Split_Pallet", opsdev['shelf_cap_ratio'], 0)
    opsdev["nsrp"] = np.where(
        (opsdev.merchstyle_string.isin(nsrp)) & (~opsdev.Pallet_info.isin(pallet_info)),
        opsdev['shelf_cap_ratio'],
        0,
    )
    opsdev["srp"] = np.where(opsdev.merchstyle_string.isin(srp), opsdev['shelf_cap_ratio'], 0)
    opsdev["srp"] = np.where((opsdev.Pallet_info == "Split_Pallet"), 0, opsdev.srp)
    opsdev["srp"] = np.where(
        (opsdev.Pallet_info == "Pallet") | (opsdev.Pallet_info == "Half_Pallet"),
        0,
        opsdev.srp,
    )
    # opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]].replace('', 0, inplace=True)
    
    
    for x in ['Division','Department', 'Section', 'Group_', 'Subgroup' ]:
        opsdev[x] = opsdev[x].replace('','0')
        
    # opsdev = opsdev[opsdev['Subgroup']!='']
    
    try:
        opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]] = opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]].astype("int64")
    except:
        for x in ['Division','Department', 'Section', 'Group_', 'Subgroup' ]:
            opsdev[x] = opsdev[x].replace(np.nan,'0') 
            
        opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]] = opsdev[['Division','Department', 'Section', 'Group_', 'Subgroup' ]].astype("int64")

        
    opsdev['icream_nsrp'] = np.where((opsdev['Division'] == 1)
                                    & (opsdev['Department'] == 17)
                                    & (opsdev['Section'] == 1702)
                                    & (opsdev.merchstyle_string.isin(['Loose', 'Alternate']))
                                    , opsdev['shelf_cap_ratio'], 0)
    
    for x in [ "srp","nsrp","mu","full_pallet", "split_pallet"]:
    
        opsdev[x] = np.where(opsdev['icream_nsrp'] >0, 0, opsdev[x] )
        
        
    opsdev.rename(
        columns={
            "Country":  "country",
            "Store_Number": "store",
            "Product_id": "tpnb",
            #"Product_Name": "product_name",
            "Displaygroup_description": "display_group",
            "Displaygroup": "drg",
            'Position_Capacity_X_Planogram_on_Store':"shelfCapacity"
        },
        inplace=True,
    )
    opsdev["checkout_stand_flag"] = np.where(
        opsdev["display_group"].str.lower().str.contains("checkout"), 1, 0
    )
    opsdev["clipstrip_flag"] = np.where(
        opsdev["display_group"].str.lower().str.contains("clipstrip"), 1, 0
    )
    opsdev["backroom_flag"] = np.where(opsdev["drg"].isin(["Z2D", "L1N"]), 1, 0)
    opsdev["store"] = opsdev["store"].astype(float).astype(int)
    opsdev["tpnb"] = (
        pd.to_numeric(opsdev.tpnb, errors="coerce").fillna(0).astype(float).astype(int)
    )
    checkout_stand = opsdev[opsdev.checkout_stand_flag == 1][
        ["store", "tpnb"]
    ].drop_duplicates()
    checkout_stand["checkout_stand_flag"] = int(1)
    clipstrip_flag = opsdev[opsdev.clipstrip_flag == 1][["store", "tpnb"]].drop_duplicates()
    clipstrip_flag["clipstrip_flag"] = int(1)
    backroom_flag = opsdev[opsdev.backroom_flag == 1][["store", "tpnb"]].drop_duplicates()
    backroom_flag["backroom_flag"] = int(1)
    
    opsdev = opsdev[
        [
            "country",
            "store",
            # "level4",
            "tpnb",
            #"product_name",
            "srp",
            "nsrp",
            "mu",
            "full_pallet",
            "split_pallet",
            "icream_nsrp",
            "shelfCapacity",
            "icase"
    
        ]
    ]
    opsdev = opsdev.merge(checkout_stand, on=["store", "tpnb"], how="left")
    opsdev["checkout_stand_flag"].replace(np.nan, 0, inplace=True)
    opsdev = opsdev.merge(clipstrip_flag, on=["store", "tpnb"], how="left")
    opsdev["clipstrip_flag"].replace(np.nan, 0, inplace=True)
    opsdev = opsdev.merge(backroom_flag, on=["store", "tpnb"], how="left")
    opsdev["backroom_flag"].replace(np.nan, 0, inplace=True)
    
    
    
    
    # filter duplicates and form the dataframe again
    grouped_opsdev = (
        opsdev.groupby(["country",
                        "store",
                        # "level4", 
                        "tpnb",
                        #"product_name",
                        
                        ], observed = True)
        .agg(
            {
                "srp": "sum",
                "nsrp": "sum",
                "mu": "sum",
                "full_pallet": "sum",
                "split_pallet": "sum",
                "icream_nsrp": "sum",
                "checkout_stand_flag": "sum",
                "clipstrip_flag": "sum",
                "backroom_flag": "sum",
                'shelfCapacity':"sum",
                "icase":"mean"
            }
        )
        .reset_index()
    )
    
    grouped_opsdev["total"] = (
        grouped_opsdev.srp
        + grouped_opsdev.nsrp
        + grouped_opsdev.mu
        + grouped_opsdev.full_pallet
        + grouped_opsdev.split_pallet
        + grouped_opsdev.icream_nsrp
    )
    
    # check where is duplicates
    dupl_rows = grouped_opsdev[grouped_opsdev.total > 1.1].shape[0]
    print(f"We have: {dupl_rows} rows duplicated")
    
    
    # final dataframe
    grouped_opsdev.drop(columns={"total"}, inplace=True)
    opsdev = grouped_opsdev.copy()
    opsdev["checkout_stand_flag"] = np.where(opsdev["checkout_stand_flag"] > 0, 1, 0)
    opsdev["clipstrip_flag"] = np.where(opsdev["clipstrip_flag"] > 0, 1, 0)
    opsdev["backroom_flag"] = np.where(opsdev["backroom_flag"] > 0, 1, 0)
    
    
    return opsdev, opsdev_foil  

def Modul_numbers(excel_inputs_f,saved_filename, place_to_save, directory):
    
    print("\nCE Modul numbers downloading...\n")
    

    def get_latest_folder(directory):
        # List all directories in the given directory
        all_directories = [os.path.join(directory, d) for d in os.listdir(directory) if os.path.isdir(os.path.join(directory, d))]
        
        # Get the most recently modified directory
        latest_folder = max(all_directories, key=os.path.getmtime)
        
        return latest_folder
    
    def get_latest_xlsx_file(directory, country):
        # List all files in the given directory, excluding hidden files and files starting with ~$
        all_files = [os.path.join(directory, f) for f in os.listdir(directory) if not f.startswith('~$') and not f.startswith('.') and os.path.isfile(os.path.join(directory, f))]
    
        if country == 'HU':
            # Filter files to include only .xlsx files
            xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx')]
        if country == 'CZ':
            xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx') and f.__contains__('CZ.xlsx')]
        if country == 'SK':
            xlsx_files = [f for f in all_files if f.lower().endswith('.xlsx') and f.__contains__('SK.xlsx')]
        
        if not xlsx_files:
            return None  # No .xlsx files found
        
        # Get the most recently modified .xlsx file
        latest_xlsx_file = max(xlsx_files, key=os.path.getmtime)
        
        return latest_xlsx_file
    
    
    
    
    def read_xlsx_sheets(filepath):
        # Read all sheets of the Excel file into a dictionary of DataFrames
        xls = pd.ExcelFile(filepath)
        sheets = {}
        for sheet_name in xls.sheet_names:
            sheets[sheet_name] = pd.read_excel(xls, sheet_name)
        
        # Concatenate all DataFrames into a single DataFrame
        combined_df = pd.concat(sheets.values(), ignore_index=True)
    
        return combined_df
    
    def copy_excel_file(source, destination):
        shutil.copy(source, destination)
    
    # HU part
    hu_path = r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report Hungary"
    latest_folder_hu = get_latest_folder(hu_path)
    mods_hu_f = get_latest_xlsx_file(latest_folder_hu, 'HU')
    print(f"\nHU file: {mods_hu_f}\n")
    mods_hu = read_xlsx_sheets(mods_hu_f)
    mods_hu = mods_hu.query("Status == 'Live'")

    mods_hu = mods_hu.groupby(['Store Number', 'Department (Desc1)', 'Display Group Code','Display Group Local Description', 'Display Group Description', ], as_index=False).agg({'Number of mods':'sum', 'Number of Products allocated':'sum'})

    mods_hu['Store Number'] = mods_hu['Store Number'].map(lambda x: str(4) + str(x)).astype("int")
    print(f"HU file contains: {mods_hu['Store Number'].nunique()} stores")
    dep_df = mods_hu[['Display Group Code', 'Department (Desc1)']].drop_duplicates()
    print("\nHU Modul numbers done!!\n")
    
    # SK CZ part
    sk_cz_path = r"\\huprgvmfs05\CE_SRD_IDR_SHARED\REPORT\DB_Report_SRD_6462376\DB Report CZ  & SK\CZ&SK"
    latest_folder_sk_cz = get_latest_folder(sk_cz_path)
    latest_folder_sk_cz = get_latest_folder(latest_folder_sk_cz)
    mods_sk_f = get_latest_xlsx_file(latest_folder_sk_cz, 'SK')
    print(f"\nSK file: {mods_sk_f}\n")
    mods_sk = read_xlsx_sheets(mods_sk_f)
    mods_sk['Store Number'] = mods_sk['Store Number'].map(lambda x: str(2) + str(x)).astype("int")
    
    print(f"SK file contains: {mods_sk['Store Number'].nunique()} stores\n")

    mods_cz_f = get_latest_xlsx_file(latest_folder_sk_cz, 'CZ')
    print(f"\nCZ file: {mods_cz_f}\n")
    mods_cz = read_xlsx_sheets(mods_cz_f)
    mods_cz['Store Number'] = mods_cz['Store Number'].map(lambda x: str(1) + str(x)).astype("int")
    print(f"CZ file contains: {mods_cz['Store Number'].nunique()} stores\n")
    mods_cz_sk = pd.concat([mods_sk, mods_cz])
    mods_cz_sk = mods_cz_sk[mods_cz_sk["Planogram Status"] == 'Live']
    mods_cz_sk = mods_cz_sk.groupby(['Store Number', 'Display Group Code', 'Display Group Description'], as_index=False).agg({'POG No Of Mods':'sum', 'Number of Products allocated':'sum'})
    mods_cz_sk.rename(columns={'POG No Of Mods':'Number of mods'}, inplace=True)
    mods_cz_sk = mods_cz_sk.merge(dep_df, on="Display Group Code", how='left')
    print("\nCZ&SK Modul numbers done!!\n")
    mods_ce = pd.concat([mods_hu, mods_cz_sk])
    mods_ce.rename(columns={'Display Group Code':'drg', 'Store Number':'store'}, inplace=True)
    
    print(f"CE total file contains: {mods_ce['store'].nunique()} stores\n")
    # # DRG mapping part
    # source_path = r"\\czprgvmfs03\CERange\CE RANGE\Mastersheet CE Range\Mastersheet stuff\DRG MAPPING.xlsx"
    # destination_path = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\Moduls"
    
    # copy_excel_file(source_path, destination_path)
    
    key_to_drg = pd.read_excel(directory/excel_inputs_f, "key_table_moduls")
    
    stores = list(
        pl.read_excel(directory / excel_inputs_f, engine="calamine")["Store"]
        .unique()
    )

    
    # Create a dictionary mapping drg_letter to department
    drg_dep_mapping = key_to_drg.set_index('drg_letter')['dep'].to_dict()
    
    drg_dep_mapping = {k: v for k, v in drg_dep_mapping.items() if not (isinstance(k, float) and math.isnan(k))}
    

    
    for drg_letter, dep in drg_dep_mapping.items():
        mods_ce.loc[mods_ce['drg'].str.startswith(drg_letter), 'dep'] = dep
    
    # OFD or PROMO or CAYG
    # mods_ce['dep'] = np.where((mods_ce['Merchandising Group Description'].str.contains('OFD'))
    #   | (mods_ce['Merchandising Group Description'].str.contains('PROMO'))
    #     |(mods_ce['Merchandising Group Description'].str.contains('CAYG')), 'PROMO_OFD_CAYG', mods_ce['dep'])
    
    # free form & healthy food to PRO
    mods_ce['dep'] = np.where((mods_ce['Display Group Description']=='FREE_FROM')
      | (mods_ce['Display Group Description']=='HEALTHY_FOOD'), 'PRO', mods_ce['dep'])
    
    
    mods_ce['country'] = ['CZ' if str(x).startswith('1') else 'SK' if str(x).startswith('2') else 'HU' for x in mods_ce['store']]
    

    mods_ce = mods_ce[mods_ce.store.isin(stores)]
    
    print(f"CE (with store list) file contains: {mods_ce['store'].nunique()} stores\n")
    
    #NEWS
    mods_ce['dep'] = np.where(mods_ce['drg'] == 'P1A', 'NEW', mods_ce['dep'])
    
    
    mods_ce.to_excel(
        place_to_save / f"{saved_filename}" / f"CE_Modul_nr_{saved_filename}.xlsx", index=False
    )
    print("\nCE Modul numbers done!!\n")
    
    # return mods_ce
    
    
# @rmf.timeit
# def hierarchy_to_sold_item(item_sold_dir, cases_dir, conn):
#     # =============================================================================
#     # part of downloading hierarchy, product names and suppliers names from artgold
#     # =============================================================================


#     if not conn:
#         conn = pyodbc.connect(
#             ODBC_CONN, autocommit=True, Trusted_Connection="yes"
#         )
#         cursor = conn.cursor()
#     print("\nHierarchy to item_sold started....\n")



#     item_sold_df=pd.read_parquet(
#         item_sold_dir
#         )
    
#     cases_df = pd.read_parquet(
#         cases_dir
#         )

#     item_tpnb = item_sold_df[['country', 'tpnb']].drop_duplicates()
#     cases_tpnb = cases_df[['country', 'tpnb']].drop_duplicates()
    
#     products = pd.concat([item_tpnb,cases_tpnb ]).drop_duplicates()
    
    
#     products = products.groupby(["country"])['tpnb'].apply(lambda s: s.tolist()).to_dict()


#     for key, value in products.items():
#         #print value
#         print(key, len([item for item in value if item]))

#     df2 = pd.DataFrame()

#     for k, v in products.items():
                        
#         s = list()
        
#         for x in v:
                    
#             s.append(str(x))
#             tpnbs = ",".join(s)
        
#         sql = """ SELECT 
#                         mstr.cntr_code AS country,
#                         mstr.slad_tpnb AS tpnb,
#                     	mstr.slad_tpn AS tpn,
#                         mstr.own_brand as ownbrand,
#                     	mstr.slad_unit AS unit_type,
#                     	mstr.slad_case_size AS case_capacity,
#                     	mstr.slad_net_weight AS weight,
#                         mstr.dmat_div_des_en AS division,
#                         cast(mstr.dmat_div_code as INT) as DIV_ID,
#                         mstr.dmat_dep_des_en AS department,
#                         cast(mstr.dmat_dep_code as INT) as DEP_ID,
#                         mstr.dmat_sec_des_en AS section,
#                         cast(mstr.dmat_sec_code as INT) as SEC_ID,
#                         mstr.dmat_grp_des_en AS group,
#                         cast(mstr.dmat_grp_code as INT) as GRP_ID,
#                         mstr.dmat_sgr_des_en AS subgroup,
#                         cast(mstr.dmat_sgr_code as INT) as SGR_ID,
#                         mstr.slad_long_des as product_name,
#                         supl.dmsup_long_des as supplier_name



#                 FROM
#                         DM.dim_artgld_details mstr
#                 LEFT JOIN
#                         dw.dim_suppliers supl
#                 ON
#                         supl.dmsup_cntr_id = mstr.cntr_id 
#                 AND
#                         supl.dmsup_id = mstr.slad_dmsup_id
#                 WHERE 
#                         slad_tpnb in ({tpnbs}) 
#                 AND     
#                         cntr_code = '{k}' 
#                 AND     
#                         dmat_sgr_des_en <> "Do not use"
#                 GROUP BY 
#                         mstr.cntr_code,
#                         mstr.slad_tpnb,
#                     	mstr.slad_tpn,
#                         mstr.own_brand,
#                     	mstr.slad_unit,
#                     	mstr.slad_case_size,
#                     	mstr.slad_net_weight,
#                         mstr.dmat_div_des_en,
#                         mstr.dmat_div_code,
#                         mstr.dmat_dep_des_en,
#                         mstr.dmat_dep_code,
#                         mstr.dmat_sec_des_en,
#                         mstr.dmat_sec_code,
#                         mstr.dmat_grp_des_en,
#                         mstr.dmat_grp_code,
#                         mstr.dmat_sgr_des_en,
#                         mstr.dmat_sgr_code,
#                         mstr.slad_long_des,
#                         supl.dmsup_long_des
                        
#                         """.format(
#             tpnbs=tpnbs, k=k
#         )

#         art_gold = pd.read_sql(sql, conn)
#         df2 = pd.concat([df2, art_gold])
#         print(f"\nHierarchy part done with {k}\n")
        
#     conn.close()   
#     df2['tpnb'] = df2['tpnb'].astype("int")

#     item_cases = pd.concat([item_sold_df, cases_df]).drop_duplicates().replace(np.nan,0).groupby(['country', 'store', 'day', 'pmg', 'tpnb'], observed=True, as_index=False).sum()
    
#     item_cases.drop("unit", axis=1, inplace=True)
    
#     item_cases = item_cases.merge(df2, on=['country', 'tpnb'], how="left")
    
#     cols = ['weight', 'case_capacity']
#     for x in cols:    
#         item_cases[x] = item_cases[x].fillna(item_cases.groupby(['country', 'pmg'], observed=True)[x].transform("mean"))


#     hier_cols_str = ['division',
#                       'department','section',
#                       'group','subgroup',
#                       'product_name']

#     hier_cols_int = ['DIV_ID','DEP_ID',
#                       'SEC_ID','GRP_ID',
#                       'SGR_ID',]

#     item_cases[hier_cols_str] = item_cases[hier_cols_str].replace(np.nan,"no_data")
#     item_cases[hier_cols_int] = item_cases[hier_cols_int].replace(np.nan,0)

#     item_cases = rmf.optimize_objects(rmf.optimize_types(item_cases))

#     item_cases.to_parquet(
#         Path(str(item_sold_dir)+"_hier")
#         )  
        

@rmf.timeit
def hierarchy_to_sold_item(item_sold_dir, cases_dir, conn, schema_name="DM", table_name="dim_artgld_details"):
    if not conn:
        conn = pyodbc.connect(ODBC_CONN, autocommit=True, Trusted_Connection="yes")
    print("\nHierarchy to item_sold started....\n")

    item_sold_df = pd.read_parquet(item_sold_dir)
    # item_sold_df = item_sold_df[item_sold_df.store == 41520]
    # cases_df = pd.read_parquet(cases_dir)

    item_tpnb = item_sold_df[['country', 'tpnb']].drop_duplicates()
    # cases_tpnb = cases_df[['country', 'tpnb']].drop_duplicates()
    # products = pd.concat([item_tpnb, cases_tpnb]).drop_duplicates()
    
    products_dict = item_tpnb.groupby("country")['tpnb'].apply(list).to_dict()

    for country, tpnbs in products_dict.items():
        print(f"{country}: {len([tpnb for tpnb in tpnbs if tpnb])}")

    df2 = pd.DataFrame()

    for country, tpnbs in products_dict.items():
        tpnbs_str = ",".join(map(str, tpnbs))
        
        sql = f"""
        SELECT 
            mstr.cntr_code AS country,
            mstr.slad_tpnb AS tpnb,
            mstr.slad_tpn AS tpn,
            mstr.own_brand as ownbrand,
            mstr.slad_unit AS unit_type,
            mstr.slad_case_size AS case_capacity,
            mstr.slad_net_weight AS weight,
            mstr.dmat_div_des_en AS division_hier,
            CAST(mstr.dmat_div_code AS INT) as DIV_ID,
            mstr.dmat_dep_des_en AS department,
            CAST(mstr.dmat_dep_code AS INT) as DEP_ID,
            mstr.dmat_sec_des_en AS section,
            CAST(mstr.dmat_sec_code AS INT) as SEC_ID,
            mstr.dmat_grp_des_en AS "group",
            CAST(mstr.dmat_grp_code AS INT) as GRP_ID,
            mstr.dmat_sgr_des_en AS subgroup,
            CAST(mstr.dmat_sgr_code AS INT) as SGR_ID,
            mstr.slad_long_des as product_name,
            supl.dmsup_long_des as supplier_name,
            MAX(CASE
                WHEN ors.orsor_pack_qty IS NULL
                    THEN 1
                ELSE ors.orsor_pack_qty
            END) AS orsor_pack_qty,
            
            MAX(CASE
                WHEN ors.orsor_sku_percar IS NULL
                    THEN 1
                ELSE ors.orsor_sku_percar
            END) AS orsor_sku_percar
            
        FROM
            {schema_name.strip('.')}.{table_name} mstr
        LEFT JOIN
            dw.dim_suppliers supl
        ON
            supl.dmsup_cntr_id = mstr.cntr_id 
        AND
            supl.dmsup_id = mstr.slad_dmsup_id
            
        LEFT JOIN         
            dw.ors_orders ors
            ON  ors.orsor_cntr_id = mstr.cntr_id 
            AND ors.orsor_dmat_id = mstr.slad_dmat_id
            
        WHERE 
            mstr.slad_tpnb IN ({tpnbs_str})
        AND     
            mstr.cntr_code = ?
        AND     
            mstr.dmat_sgr_des_en <> 'Do not use'
        GROUP BY 
            mstr.cntr_code, mstr.slad_tpnb, mstr.slad_tpn, mstr.own_brand,
            mstr.slad_unit, mstr.slad_case_size, mstr.slad_net_weight,
            mstr.dmat_div_des_en, mstr.dmat_div_code, mstr.dmat_dep_des_en,
            mstr.dmat_dep_code, mstr.dmat_sec_des_en, mstr.dmat_sec_code,
            mstr.dmat_grp_des_en, mstr.dmat_grp_code, mstr.dmat_sgr_des_en,
            mstr.dmat_sgr_code, mstr.slad_long_des, supl.dmsup_long_des
        """
        try:
            art_gold = pd.read_sql(sql, conn, params=(country,))
            df2 = pd.concat([df2, art_gold])
            print(f"\nHierarchy part done with {country}\n")
        except pyodbc.Error as e:
            print(f"Error querying data for country {country}: {str(e)}")
            continue

    conn.close()
    df2['tpnb'] = df2['tpnb'].astype("int")

    # item_cases = pd.concat([item_sold_df, cases_df]).drop_duplicates().replace(np.nan, 0)
    # item_cases = item_cases.groupby(['country', 'store', 'day', 'pmg', 'tpnb'], observed=True, as_index=False).sum()
    
    # item_cases.drop("unit", axis=1, inplace=True)
    
    # CaseCapacity transformation
    df2['case_capacity'] = np.where((df2.orsor_pack_qty == 1) & (df2.case_capacity == 1), df2.orsor_sku_percar, df2.case_capacity)
    df2['case_capacity'] = np.where((df2.case_capacity == 1) & (df2.orsor_pack_qty > 1), df2.orsor_pack_qty, df2.case_capacity)
    
    item_cases = item_sold_df.merge(df2, on=['country', 'tpnb'], how="left")
    
    cols = ['weight', 'case_capacity']
    for x in cols:    
        item_cases[x] = item_cases[x].fillna(item_cases.groupby(['country', 'pmg'], observed=True)[x].transform("mean"))

    hier_cols_str = ['division_hier', 'department', 'section', 'group', 'subgroup', 'product_name']
    hier_cols_int = ['DIV_ID', 'DEP_ID', 'SEC_ID', 'GRP_ID', 'SGR_ID']

    item_cases[hier_cols_str] = item_cases[hier_cols_str].replace(np.nan, "no_data")
    item_cases[hier_cols_int] = item_cases[hier_cols_int].replace(np.nan, 0)
    
    item_cases = rmf.optimize_objects(rmf.optimize_types(item_cases))
    
    item_cases.sort_values(by=["country", "store", "day_id", "tpnb"], inplace=True)

    output_path = Path(str(item_sold_dir) + "_hier")
    item_cases.to_parquet(output_path)
    
    

def tesco_weeks_for_dataset(start_date, end_date):
    def parse_fiscal_date(date_str):
        date_str = date_str.strip("'")
        fiscal_year = int(date_str[1:5])
        week = int(date_str[6:])
        
        # Calculate the start of the fiscal year (March 1st)
        fiscal_start = datetime(fiscal_year, 3, 1)
        
        # Calculate the date based on the week number
        days_to_add = (week - 1) * 7  # Week 1 is the first week of March
        date = fiscal_start + timedelta(days=days_to_add)
        
        # Adjust to the Thursday of that week
        date += timedelta(days=(3 - date.weekday() + 7) % 7)
        return date

    def get_midmonth_thursday(year, month):
        # Start with the 15th of the month
        mid_month = datetime(year, month, 15)
        # Adjust to the nearest Thursday (3 represents Thursday)
        return mid_month + timedelta(days=(3 - mid_month.weekday() + 7) % 7)

    start = parse_fiscal_date(start_date)
    end = parse_fiscal_date(end_date)
    
    midmonth_thursdays = []
    current = start
    
    while current <= end:
        mid_thursday = get_midmonth_thursday(current.year, current.month)
        if not midmonth_thursdays or mid_thursday.month != midmonth_thursdays[-1].month:
            if mid_thursday >= start and mid_thursday <= end:
                midmonth_thursdays.append(mid_thursday)
        current += timedelta(weeks=1)
    
    return [date.strftime('%Y%m%d') for date in midmonth_thursdays]

def Create_Dataset(sql_part, data_paths):
    
    
        def find_latest_single_pick_list(directory):
            # Define the regex pattern to match the filename with date
            pattern = r"Single_pick_list_(\d{4}\.\d{2}\.\d{2})\.xlsx"
            
            # Initialize a variable to store the latest file details
            latest_file = None
            latest_date = None
            
            # Iterate over the files in the specified directory
            for filename in os.listdir(directory):
                match = re.match(pattern, filename)
                if match:
                    # Extract the date from the filename
                    file_date = datetime.strptime(match.group(1), "%Y.%m.%d")
                    # Check if this is the latest date we've found so far
                    if latest_date is None or file_date > latest_date:
                        latest_date = file_date
                        latest_file = filename
            
            return latest_file
    
    
    
    
        # rmf.create_a_folder(data_paths.directory, f"{sql_part.place_to_save}/{sql_part.saved_filename}")
        

        # for what_to_create in ['combined_productivity']: # 'item_sold','item_sold_dotcom','cases', 'stock', 'srd', 'combined_productivity'
        
        #     sql = ssh_table_create(what_to_create, sql_part.start_date, sql_part.end_date, sql_part.pmg_list  , sql_part.nr_weeks, sql_part.place_to_save, sql_part.saved_filename)        
            
        #     sql= 0
        #     if sql ==0:
                                
        #         ssh_downloader(what_to_create, sql_part.place_to_save, sql_part.saved_filename, sql_part.stores)

                
        # # Other Data we need for Dataset
        # hierarchy_to_sold_item(data_paths.directory/f"{sql_part.place_to_save}/{sql_part.saved_filename}/combined_productivity_{sql_part.saved_filename}", data_paths.directory/f"{sql_part.place_to_save}/{sql_part.saved_filename}/cases_{sql_part.saved_filename}", False)
        
        plano = pd.DataFrame()
        for x in tesco_weeks_for_dataset(sql_part.start_date, sql_part.end_date):

            df = planogram_compiling( int(x), sql_part.saved_filename, sql_part.place_to_save, True)
            plano = pd.concat([df, plano])

        shelfcap_df = plano.sort_values('capacity', ascending=False).drop_duplicates(subset=['tpnb', 'store'], keep='first')
        shelfcap_df.to_parquet(sql_part.place_to_save / f"{sql_part.saved_filename}" / f"planogram_{sql_part.saved_filename}",
                    compression="gzip",
                )
            

        # ob.OB_packaging_types_download(f"{sql_part.place_to_save}\{sql_part.saved_filename}")
        
        # SRD_tables = SRD_database_by_stores(sql_part.stores)
        # SRD_tables.to_csv(f"{sql_part.place_to_save}\{sql_part.saved_filename}\SRD_table_RAW_{sql_part.saved_filename}.csv.gzip", compression="gzip", index=False)
        # opsdev_df, foil_df = SRD_to_opsdev(SRD_tables, data_paths.directory, data_paths.excel_inputs_f)
        # opsdev_df.to_parquet( f"{sql_part.place_to_save}\{sql_part.saved_filename}\opsdev_df_{sql_part.saved_filename}", compression="gzip")
        # foil_df.to_parquet( f"{sql_part.place_to_save}\{sql_part.saved_filename}\Foil_df_{sql_part.saved_filename}", compression="gzip")


        # # =============================================================================
        # # Store related Infos gathering
        # # =============================================================================
        # store_inputs = rmf.Store_Inputs_Creator(data_paths.directory, data_paths.excel_inputs_f, sql_part.stores)

        # # =============================================================================
        # # Creating Dataset
        # # =============================================================================
        
        # dataset_combined = f"{sql_part.place_to_save}/{sql_part.saved_filename}/combined_productivity_{sql_part.saved_filename}_hier"
        # broken_cases_f = r"inputs\files_for_dataset\Dataset_25\broken_cases_list_2023w14_27.csv.gz"
        # single_pick_f_latest = find_latest_single_pick_list(r"\\euprgvmfs01\GMforCentralEurope\Supply Chain\Single Picking")
        # single_pick_f = r"\\euprgvmfs01\GMforCentralEurope\Supply Chain\Single Picking\\" +  single_pick_f_latest
        
        
        # def find_excel_file_with_string(sql_part):
        #     directory_path = f"{sql_part.place_to_save}/{sql_part.saved_filename}"
        #     target_string = "ownbrand_opening_type_"
            
        #     for filename in os.listdir(directory_path):
        #         if target_string in filename and filename.endswith('.xlsx'):
        #             return os.path.join(directory_path, filename)
    
        #     return None
        

        
        # box_op_type_f = find_excel_file_with_string(sql_part)

        
        # repl_dataset = rmf.Repl_DataSet_Creator(
        #     store_inputs,
        #     data_paths.directory,
        #     dataset_combined,
        #     box_op_type_f,
        #     broken_cases_f,
        #     single_pick_f)
        
        
        


        # saving_place = f"{sql_part.place_to_save}/{sql_part.saved_filename}/Repl_Dataset_{sql_part.saved_filename}"
        # print(f"Repl_Dataset_{sql_part.saved_filename} is being saved.....")
        # repl_dataset = repl_dataset.to_pandas()
        # repl_dataset = rmf.optimize_objects(rmf.optimize_types(repl_dataset))
        # repl_dataset.to_parquet(
        #     saving_place,
        #     compression="gzip",
        # )
        
        # Repl_Dataset = repl_dataset.to_pandas()
        # Repl_Dataset = rmf.shelfService_GM(Repl_Dataset)
        # Repl_Dataset.to_parquet(f"{sql_part.place_to_save}/{sql_part.saved_filename}/Repl_Dataset_{sql_part.saved_filename}", compression="gzip")
        
        
        # return Repl_Dataset
        
        
# Define a list of names you don't want to export
__all__ = [name for name in dir() if name not in ('ssh_table_create', 'ssh_downloader')]




def merge_dict_dataframes(dict1, dict2, suffixes=('_branded', '_ownbranded')):
    """
    Merge corresponding dataframes from two dictionaries with specific suffix handling
    
    Args:
        dict1 (dict): First dictionary containing dataframes
        dict2 (dict): Second dictionary containing dataframes
        suffixes (tuple): Suffixes to append to 'value' and 'value%' columns
    
    Returns:
        dict: Dictionary containing merged dataframes
    """
    merged_dict = {}
    
    # Ensure both dictionaries have the same keys
    assert dict1.keys() == dict2.keys(), "Dictionaries must have the same keys"
    
    for key in dict1.keys():
        df1 = dict1[key]
        df2 = dict2[key]
        
        # Identify columns that need suffixes
        suffix_cols = ['value', 'value_%']
        
        # Rename columns in df2 that need suffixes
        rename_cols = {col: f"{col}{suffixes[1]}" for col in df2.columns if col in suffix_cols}
        df2_renamed = df2.rename(columns=rename_cols)
        
        # Rename columns in df1 that need suffixes
        rename_cols = {col: f"{col}{suffixes[0]}" for col in df1.columns if col in suffix_cols}
        df1_renamed = df1.rename(columns=rename_cols)
        
        # Merge dataframes
        merged_df = df1_renamed.merge(
            df2_renamed,
            how='left', 
            left_index=True,
            right_index=True,
            suffixes=('', '_y')
        )
        
        # Drop duplicate columns from right dataframe
        cols_to_drop = [col for col in merged_df.columns if col.endswith('_y')]
        merged_df = merged_df.drop(columns=cols_to_drop)
        
        merged_dict[key] = merged_df
    
    return merged_dict

def tpnb_charts_data(df, period, act_dataset, directory, excel_inputs_f,  category_reset_df, year):
    
    
    print("\nTPNB Charts running.....\n")
    
    conn = pyodbc.connect(
        "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
    )
    



    from_last_year = []

    # Define the desired order of periods
    desired_order = ['LY_p11', 'LY_p12', 'p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7', 'p8', 'p9', 'p10', 'p11', 'p12']

    repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]

    store_list = pd.read_excel(directory / excel_inputs_f)
    store_list.columns = [x.lower() for x in store_list.columns]

    store_list = store_list['store'].unique().tolist()

    df_ce = pd.DataFrame()
    
    # for x, y in zip(periods, period ):
        
    # df = pd.read_parquet(x)
    
    df = df[df.tpnb > 0]
    
    df = df[df.store.isin(store_list)]

    condition = [
        df["store"].astype(str).str.match("^1"),
        df["store"].astype(str).str.match("^2"),
        df["store"].astype(str).str.match("^4"),
    ]
    results = ["CZ", "SK", "HU"]
    df["country"] = np.select(condition, results, 0)
        

        
        
    df = df[['country', 'store','tpnb','product_name', "srp", "nsrp", "full_pallet", "mu", "split_pallet"]]
        

        

    
    # for r in repl_types:
    #     df[r] = np.where(df[r] == 1, df.sold_units, 0)
    
    df['period'] = period
    
    

    df = df.groupby(['country', 'tpnb', 'product_name', 'period'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
    
    
    df_ce = pd.concat([df_ce, df])
    
    print(f"Done with processing {period}!")
        
    act_dataset_df = pd.read_parquet(act_dataset)
    
    def ownbrand_bool_func(ownbrand_bool, act_dataset_df, df_ce):
        

        
    
        if ownbrand_bool:
            act_dataset_df = act_dataset_df[act_dataset_df.ownbrand == "Y"]
    
        df_ce = df_ce.merge(act_dataset_df[['country', 'tpnb', 'pmg', 'Category name']].drop_duplicates(), on=['country', 'tpnb'], how='left')
   
        missing_df = df_ce[df_ce.pmg.isnull()]
        products = missing_df[['country', 'tpnb']].drop_duplicates().groupby(["country"])['tpnb'].apply(lambda s: s.tolist()).to_dict()
        
        for key, value in products.items():
            #print value
            print(key, len([item for item in value if item]))
    
        pmg_tpn = pd.DataFrame()
        
        with pyodbc.connect(
            "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
        ) as conn:
        
            if not ownbrand_bool:
                
        
                for k, v in products.items():
                                    
                    s = list()
                    
                    for x in v:
                                
                        s.append(str(x))
                        tpnbs = ",".join(s)
                    
                    sql = """ SELECT 
                                    tpns.cntr_code AS country,
                                    cast(tpns.slad_tpnb AS INT) AS tpnb,
                                    /*tpns.dmat_div_des_en AS division,
                                    cast(tpns.dmat_div_code as INT) as DIV_ID,
                                    tpns.dmat_dep_des_en AS department,
                                    cast(tpns.dmat_dep_code as INT) as DEP_ID,
                                    tpns.dmat_sec_des_en AS section,
                                    cast(tpns.dmat_sec_code as INT) as SEC_ID,
                                    tpns.dmat_grp_des_en AS group,
                                    cast(tpns.dmat_grp_code as INT) as GRP_ID,
                                    tpns.dmat_sgr_des_en AS subgroup,
                                    cast(tpns.dmat_sgr_code as INT) as SGR_ID,*/
        
                                    hier.pmg as pmg
                            FROM
                                    DM.dim_artgld_details tpns
                                    
                            LEFT JOIN tesco_analysts.hierarchy_spm hier
                            ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0")
                            AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0")
                            AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0")
                            AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0")
                            AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
                            WHERE 
                                    slad_tpnb in ({tpnbs}) 
                            AND     
                                    cntr_code = '{k}' 
                            AND     
                                    dmat_sgr_des_en <> "Do not use"
                            GROUP BY 
                                    cntr_code,
                                    slad_tpnb,
                                    /*dmat_div_des_en,
                                    dmat_div_code,
                                    dmat_dep_des_en,
                                    dmat_dep_code,
                                    dmat_sec_des_en,
                                    dmat_sec_code,
                                    dmat_grp_des_en,
                                    dmat_grp_code,
                                    dmat_sgr_des_en,
                                    dmat_sgr_code,*/
                                    hier.pmg
                                    
                                    """.format(
                        tpnbs=tpnbs, k=k
                    )
        
                    pmg_tpn = pd.concat([pmg_tpn, pd.read_sql(sql, conn)])
                    
                    
            if ownbrand_bool:
                
                for k, v in products.items():
                                    
                    s = list()
                    
                    for x in v:
                                
                        s.append(str(x))
                        tpnbs = ",".join(s)
                    
                    sql = """ SELECT 
                                    tpns.cntr_code AS country,
                                    cast(tpns.slad_tpnb AS INT) AS tpnb,
                                    /*tpns.dmat_div_des_en AS division,
                                    cast(tpns.dmat_div_code as INT) as DIV_ID,
                                    tpns.dmat_dep_des_en AS department,
                                    cast(tpns.dmat_dep_code as INT) as DEP_ID,
                                    tpns.dmat_sec_des_en AS section,
                                    cast(tpns.dmat_sec_code as INT) as SEC_ID,
                                    tpns.dmat_grp_des_en AS group,
                                    cast(tpns.dmat_grp_code as INT) as GRP_ID,
                                    tpns.dmat_sgr_des_en AS subgroup,
                                    cast(tpns.dmat_sgr_code as INT) as SGR_ID,*/
                                    own_brand as ownbrand,
        
                                    hier.pmg as pmg
                            FROM
                                    DM.dim_artgld_details tpns
                                    
                            LEFT JOIN tesco_analysts.hierarchy_spm hier
                            ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0")
                            AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0")
                            AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0")
                            AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0")
                            AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
                            WHERE 
                                    slad_tpnb in ({tpnbs})
                            AND
                                    own_brand = 'Y'
                            AND     
                                    cntr_code = '{k}' 
                            AND     
                                    dmat_sgr_des_en <> "Do not use"
                            GROUP BY 
                                    cntr_code,
                                    slad_tpnb,
                                    /*dmat_div_des_en,
                                    dmat_div_code,
                                    dmat_dep_des_en,
                                    dmat_dep_code,
                                    dmat_sec_des_en,
                                    dmat_sec_code,
                                    dmat_grp_des_en,
                                    dmat_grp_code,
                                    dmat_sgr_des_en,
                                    dmat_sgr_code,*/
                                    own_brand,
                                    hier.pmg
                                    
                                    """.format(
                        tpnbs=tpnbs, k=k
                    )
        
                    pmg_tpn = pd.concat([pmg_tpn, pd.read_sql(sql, conn)])
                    
      
        missing_df.drop("pmg", axis=1, inplace=True)
        missing_df = missing_df.merge(pmg_tpn[['country','tpnb', 'pmg']].drop_duplicates(), on=['country','tpnb'], how='left')
        df_ce = pd.concat([df_ce[df_ce.pmg.notnull()], missing_df])
            
    
        df_ce['Category name'] = np.where(df_ce['Category name'].isnull(), "no_data", df_ce['Category name'])
        
        
        df_ce.loc[df_ce['Category name'] == "PRODUCE + DRN + FRESH JUICES (spring 2024)", 'Category name'] = "PRODUCE + DRN + FRESH JUICES"

        
        # Create a dictionary to store pmg values and their corresponding most frequent Category name
        pmg_categories = {}
    
        # Iterate over unique pmg values
        for pmg_value in df_ce['pmg'].unique():
            # Filter rows where pmg is equal to current pmg_value and Category name is not "no_data"
            filtered_rows = df_ce[(df_ce['pmg'] == pmg_value) & (df_ce['Category name'] != 'no_data') & (df_ce['pmg'].str[:3] != 'HDL')]
            
            # Check if there are any non-"no_data" rows matching the conditions
            if not filtered_rows.empty:
                # Find the most frequent non-"no_data" Category name for the current pmg_value
                most_frequent_category = filtered_rows['Category name'].mode().iloc[0]
                
                # Store the most frequent non-"no_data" Category name for the current pmg_value in the dictionary
                pmg_categories[pmg_value] = most_frequent_category
                    
        # Update the 'no_data' rows in the DataFrame based on the mapping
        mask = df_ce['Category name'] == 'no_data'
        df_ce.loc[mask, 'Category name'] = df_ce.loc[mask, 'pmg'].map(pmg_categories)
        # df_ce['Category name'] = df_ce.apply(lambda row: pmg_categories.get(row['pmg'], row['Category name']) if row['Category name'] == 'no_data' else row['Category name'], axis=1)
        df_ce['Category name'] = np.where(df_ce.pmg.str[:3] == 'FRZ', 'FROZEN', df_ce['Category name'] )
        df_ce['Category name'] = np.where((df_ce['Category name'] == 'no_data')&(df_ce.pmg.str[:3] == 'HDL'), 'GM', df_ce['Category name'] )
    
    
        df_ce.rename(columns={"Category name":"category"}, inplace=True)
    
    
    
    
        cat_df = pd.read_excel(category_reset_df)[['country','category', 'DIV_DESC']].drop_duplicates()
    
        df_ce = df_ce.merge(cat_df, on=['country', 'category'], how='left')
        

    
        df_ce['DIV_DESC'] = np.where((df_ce['DIV_DESC'].isnull())&(df_ce.pmg.str[:3] == 'HDL'), 'GM', df_ce['DIV_DESC'] )
        df_ce['DIV_DESC'] = np.where((df_ce['DIV_DESC'].isnull())&(df_ce.pmg.str[:3] == 'FRZ'), 'Fresh_Froz_Food', df_ce['DIV_DESC'] )
    
        df_ce_total = df_ce.groupby([ 'DIV_DESC','category','pmg','tpnb', 'product_name', 'period'], observed=True)[["srp", "nsrp", "full_pallet", "mu", "split_pallet"]].sum().reset_index()
        df_ce_total['country'] = 'CE'
    
        df_ce = pd.concat([df_ce, df_ce_total])
    
        df_ce = df_ce[df_ce['category'] != 'no_data']
    
        # df_ce = df_ce[df_ce.category.notnull()]
        

    
        
        if not ownbrand_bool:
            df_ce.to_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\CategoryReset\df_tpn_no_wheiting_branded", compression = "gzip")
        else:
            df_ce.to_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\CategoryReset\df_tpn_no_wheiting_ownbrand", compression = "gzip")


    
                
        need_cols = [x for x in df_ce.columns if x not in ['srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']]  
    
        # =============================================================================
        # Charts
        # =============================================================================
        df_catres_sum = df_ce.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country','pmg', 'tpnb', 'product_name', 'category', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()
    
    
    
        # non_food_food = df_ce[df_ce['DIV_DESC'].isin(['NonFood Grocery', 'Food Grocery'])]
        # non_food_food['division'] = np.where(non_food_food['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)', 'Grocery (Food)')
    
        df_catres_sum_div = df_ce.copy()
    
        # df_catres_sum_div = pd.concat([df_catres_sum_div, non_food_food])
        # df_catres_sum_div['division'] = np.where(df_catres_sum_div['division'] == 'Grocery', 'Grocery (total)', df_catres_sum_div['division'])
    
        df_catres_sum_div = df_catres_sum_div.melt(id_vars = need_cols, var_name = 'repl_types').pivot_table(index = ['country', 'DIV_DESC','pmg','tpnb', 'product_name', 'repl_types'] ,columns = "period", values = 'value', observed=True).reset_index()
    
    
    
    
        cols_need = [x for x in df_catres_sum.columns if x not in [1] + df_catres_sum.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]
    
        cols_need_div = [x for x in df_catres_sum_div.columns if x not in [1] + df_catres_sum_div.filter(regex=r'^(?!1)[2-9]|1[0-2]').columns.tolist() ]
    
        df_catres_sum_category = df_catres_sum.melt(id_vars=cols_need, var_name='period' ).groupby(['country', 'category', 'repl_types','period'], observed=True)['value'].sum().reset_index()
        df_catres_sum_category['value_%'] = df_catres_sum_category['value'] / df_catres_sum_category.groupby(['country', 'category', 'period'], observed=True)['value'].transform('sum')
    
        df_catres_sum_division = df_catres_sum_div.melt(id_vars=cols_need_div, var_name='period' ).groupby(['country', 'DIV_DESC', 'repl_types','period'], observed=True)['value'].sum().reset_index()
        df_catres_sum_division['value_%'] = df_catres_sum_division['value'] / df_catres_sum_division.groupby(['country', 'DIV_DESC', 'period'],observed=True)['value'].transform('sum')
        df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'NonFood Grocery', 'Grocery (NonFood)',df_catres_sum_division['DIV_DESC'] )
        df_catres_sum_division['DIV_DESC'] = np.where(df_catres_sum_division['DIV_DESC'] == 'Food Grocery', 'Grocery (Food)',df_catres_sum_division['DIV_DESC'] )
        df_catres_sum_division = df_catres_sum_division[df_catres_sum_division.DIV_DESC != "GM"]
    
        # df_total = df_catres_sum_division[df_catres_sum_division.country == 'CE'].groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
        df_total = df_catres_sum_division.groupby(['country','repl_types', 'period'], observed=True)['value'].sum().reset_index()
    
        df_total['value_%'] = df_total['value'] / df_total.groupby(['country', 'period'],observed=True)['value'].transform('sum')
    
        df_catres_sum_category['category'] = df_catres_sum_category['category'].apply(lambda x: x.replace(";"," "))
    
    
        def rename_period_columns(df, prefix, from_last_year:list):
            df['period'] = df['period'].astype(str)  
            df['period'] = df.apply(lambda x: "LY_"+ prefix +x['period'] if x['period'] in [str(y) for y in from_last_year] else prefix + x['period'], axis = 1)    
    
    
    
        for x in [df_total, df_catres_sum_category, df_catres_sum_division ]:
            rename_period_columns(x, 'p', from_last_year)
    
            # Reorder the dataframe
            x['period'] = pd.Categorical(x['period'], categories=desired_order, ordered=True)
            x.sort_values(by=['country', 'repl_types','period'], ascending=[True,True,True],inplace=True)
            x["year"] = year
            
        return {
            'total' : df_total,
            'category': df_catres_sum_category,
            'division': df_catres_sum_division
            }
    
    df_branded = ownbrand_bool_func(False,act_dataset_df, df_ce)
    df_ownbrand =ownbrand_bool_func(True,act_dataset_df, df_ce) 
    
    
    df_dict = merge_dict_dataframes(df_branded, df_ownbrand, suffixes=('_branded', '_ownbranded'))
    # First, add the key name as a column to each DataFrame to distinguish them
    for key, df in df_dict.items():
        df['source'] = key  # or any column name you prefer instead of 'source'

    # Concatenate all DataFrames
    combined_df = pd.concat(df_dict.values(), axis=0)
    
    return combined_df








# # Save as single parquet file
# combined_df.to_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\CategoryReset\CatRes shared\TPNB\c")


# # To load it back into separate dictionary:
# loaded_df = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\CategoryReset\CatRes shared\TPNB\c")
# loaded_dict = {
#     name: group.drop('source', axis=1) 
#     for name, group in loaded_df.groupby('source')
# }
    
    
    

# Example usage:
# tpnb_df_dict = merge_dict_dataframes(dict_tpnb_df_branded, dict_tpnbs_df_ownbrand, suffixes=('_branded', '_ownbranded'))    
    
        
        
        