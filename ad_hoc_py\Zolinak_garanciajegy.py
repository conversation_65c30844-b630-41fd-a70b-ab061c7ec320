import pyodbc
import pandas as pd
import polars as pl

import time

start = time.time()


conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()

# input_df =pl.read_excel(
#     r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_07\Stolen_products_w14w27_2023.xlsx"
#     ,engine="calamine").to_pandas()


input_df = pd.read_clipboard()

products = input_df[['country', 'tpnb']].drop_duplicates()
products = products.groupby(["country"])['tpnb'].apply(lambda s: s.tolist()).to_dict()


# products = {"SK":[2002020322517, 2002020319280, 2002020312286]}
for key, value in products.items():
    #print value
    print(key, len([item for item in value if item]))

df2 = pl.DataFrame()



for k, v in products.items():
                    
    s = list()
    
    for x in v:
                
        s.append(str(x))
        tpnbs = ",".join(s)
        
        
    sql = f"""                   
                    
                    
                    SELECT
                    b.cntr_code AS COUNTRY,
                    a.site as store,
                    
                    b.dmat_div_code AS DIV_CODE,
                    b.dmat_dep_code AS DEP_CODE,
                    b.dmat_sec_code AS SEC_CODE,
                    b.dmat_grp_code AS GRP_CODE,
                    b.dmat_sgr_code AS SGR_CODE,
                    b.slad_long_des AS product_name,
                    SUM(a.amo/100) AS VALUE,
                    SUM(a.qty) AS QUANTITY
                    
                    FROM poshu.t001 a
                    LEFT JOIN DM.dim_artgld_details b
                    ON a.ean = lpad(b.slem_ean,14,0)

                    WHERE a.part_col BETWEEN 20240527 AND 20240901
                    AND b.cntr_code = "HU"
                    AND b.slad_tpnb in ({tpnbs})
                    
                    GROUP BY b.cntr_code,
                            a.site,
                            b.dmat_div_code,
                            b.dmat_dep_code,
                            b.dmat_sec_code,
                            b.dmat_grp_code,
                            b.dmat_sgr_code,
                            b.slad_long_des
                    
                    
                    
                    """

    

    df2 =pl.concat([df2, pl.read_database(sql, conn)])
    # art_gold = pd.read_sql(sql, conn)

    
print(time.time() - start)



sql = """                   
                
                
                SELECT
                b.cntr_code AS COUNTRY,
                a.site as store,
                a.pon,
                
                COUNT(a.ean) as ITEMS_SOLD_B,
                sum(a.qty_sku) as ITEMS_SOLD_A,
                count(a.qty) as QTY2,
                SUM(a.amo) as SALESEXVAT,
                sum(a.vat) as vat,
                count(a.qty_sku) as VOIDS,

                
                
                
                b.dmat_div_des_en AS DIV_DESC,
                b.dmat_div_code AS DIV_CODE,
                b.dmat_dep_des_en AS DEP_DESC,
                b.dmat_dep_code AS DEP_CODE,
                b.dmat_sec_des_en AS SEC_DESC,
                b.dmat_sec_code AS SEC_CODE,
                b.dmat_grp_des_en AS GRP_DESC,
                b.dmat_grp_code AS GRP_CODE,
                b.dmat_sgr_des_en AS SGR_DESC,
                b.dmat_sgr_code AS SGR_CODE,
                b.slad_long_des AS product_name,
                SUM(a.amo/100) AS VALUE,
                SUM(a.qty) AS QUANTITY
                
                FROM poshu.t001 a
                LEFT JOIN DM.dim_artgld_details b
                ON a.ean = lpad(b.slem_ean,14,0)

                WHERE a.part_col BETWEEN 20240527 AND 20240901
                AND b.cntr_code = "HU"
                and qty_sku < 0
                and a.site = 41001
                
                GROUP BY b.cntr_code,
                        a.site,
                        a.pon,
                        b.dmat_div_des_en,
                        b.dmat_div_code,
                        b.dmat_dep_des_en,
                        b.dmat_dep_code,
                        b.dmat_sec_des_en,
                        b.dmat_sec_code,
                        b.dmat_grp_des_en,
                        b.dmat_grp_code,
                        b.dmat_sgr_des_en,
                        b.dmat_sgr_code,
                        b.slad_long_des
                
                
                
                """


df = pl.read_database(sql, conn)








