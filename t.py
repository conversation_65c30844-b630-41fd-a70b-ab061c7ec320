#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UNIFIED VALIDATION WORKFLOW
Combines Control Panel setup + pandas/Polars implementations + exact value validation

This script runs both implementations in the same environment and validates 
that every single value in Drivers and Drivers_produce matches exactly.
"""

import pandas as pd
import numpy as np
import time
import warnings
from pathlib import Path
import pyarrow.parquet as pq

# Import all required modules
import Replenishment_Model_Functions_25 as rmf
import ClassesModel as cm

warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)


def setup_control_panel_environment():
    """Setup Control Panel environment variables"""
    print("🔧 Setting up Control Panel environment...")
    
    # Core Control Panel setup
    act_version_name = "losses_repaired_for_Q2_tag"
    new_version_name = "_"
    saved_filename = "_t"
    directory = (Path(__file__).parent if "__file__" in locals() else Path.cwd()).parent
    repl_dataset_f = "inputs/Repl_Dataset_25_nsrpChanged_newer_foil_benchmark"
    
    # File paths (matching Control Panel exactly)
    items_sold_f = r"inputs\files_for_dataset\w22_w34_rGM\item_sold_w22_w34_rGM_hier"
    items_sold_dotcom = r"inputs\files_for_dataset\w22_w34_rGM\item_sold_dotcom_w22_w34_rGM"
    stock_f = r"inputs\files_for_dataset\w22_w34_rGM\stock_w22_w34_rGM"
    ops_dev_f = r"inputs\files_for_dataset\w22_w34_rGM\opsdev_df_Dataset_25"
    box_op_type_f = r"inputs\files_for_dataset\w22_w34_rGM\ownbrand_opening_type_28-10-2024.xlsx"
    cases_f = r"inputs\files_for_dataset\w22_w34_rGM\cases_w22_w34_rGM"
    pallet_capacity_f = r"inputs\files_for_dataset\w22_w34_rGM\pallet_capacity_CE_Dataset_25"
    broken_cases_f = r"inputs\files_for_dataset\w22_w34_rGM\broken_cases_list_2023w14_27.csv.gz"
    single_pick_f = r"inputs\files_for_dataset\w22_w34_rGM\Single_pick_list_2024.10.04.xlsx"
    shelfCapacity = r"inputs\files_for_dataset\w22_w34_rGM\planogram_Dataset_25.csv.gz"
    foil_f = r"inputs\files_for_dataset\w22_w34_rGM\Foil_df_Dataset_25"
    presort_pal_del = r"inputs\files_for_dataset\PBS_PBL_presort\presort_pal_del_with4prodGroups_w14_27_benchmarks"
    
    excel_inputs_f = "inputs/Repl/Repl_Stores_Inputs_2025_Q1_v7_GBPratesUpdate (1)_Zamky&others.xlsx"
    losses_f = "inputs/files_for_dataset/Dataset_25/losses__t_benchmark"
    most_f = "inputs/MOST/Repl/MOST_Repl_25_Q2_tags.xlsb"
    selected_tpn = "inputs/Repl/selected_tpns.xlsx"
    
    act_model_outputs = f"outputs/model_outputs/{act_version_name}/OPB_DEP_{act_version_name}.xlsx"
    act_model_insights = f"outputs/model_outputs/{act_version_name}/INSIGHT_{act_version_name}.parquet.gz"
    act_model_cost_base = f"inputs/model_outputs/{act_version_name}/ReplCostBase_{act_version_name}"
    
    # Warehouse datasets
    wh_prof_drivers = "inputs/WH/WH_profile_drivers_profiles25_Q2_Pozsonyi AGAIN_DRS distance correction_benchmark_Zamky&others.xlsx"
    wh_most = "inputs/MOST/WH/MOST_Repl_WH_2025_Q2_repair_mohu_elevator RSU (1)_Zamky&others.xlsb"
    wh_cases_pmg = r"inputs\files_for_dataset\Dataset_25\wh_cases_pmgw14_w27_03_benchmark_stores"
    wh_pattisserie = r"inputs\files_for_dataset\Dataset_25\wh_pattisseriew14_w27_benchmark_stores"
    wh_deliveries = r"inputs\files_for_dataset\Dataset_25\wh_deliveries_dataw14_w27_w_sold_unit_benchmark_stores"
    night_truck_helper = "inputs/WH/night_truck_helper"
    
    # Test store (single store for faster validation)
    stores = [41510]
    
    # Create data_paths object (exact parameter order from ClassesModel.Data_Paths.__init__)
    data_paths = cm.Data_Paths(
        directory=directory,
        repl_dataset_f=repl_dataset_f,
        items_sold_f=items_sold_f,
        items_sold_dotcom=items_sold_dotcom,
        stock_f=stock_f,
        cases_f=cases_f,
        ops_dev_f=ops_dev_f,
        box_op_type_f=box_op_type_f,
        excel_inputs_f=excel_inputs_f,
        pallet_capacity_f=pallet_capacity_f,
        losses_f=losses_f,
        most_f=most_f,
        act_model_outputs=act_model_outputs,
        act_model_insights=act_model_insights,
        selected_tpn=selected_tpn,
        stores=stores,
        wh_prof_drivers=wh_prof_drivers,
        wh_most=wh_most,
        wh_cases_pmg=wh_cases_pmg,
        wh_pattisserie=wh_pattisserie,
        wh_deliveries=wh_deliveries,
        broken_cases_f=broken_cases_f,
        act_model_cost_base=act_model_cost_base,
        single_pick_f=single_pick_f,
        foil_f=foil_f,
        shelfCapacity=shelfCapacity,
        presort_pal_del=presort_pal_del,
        night_truck_helper=night_truck_helper
    )
    
    # Create store inputs
    store_inputs = rmf.Store_Inputs_Creator(directory, excel_inputs_f, stores)
    
    # Model constants (from Replenishment_Model_Running)
    RC_CAPACITY = 1 + (1 - 0.62)
    RC_DELIVERY = 0.23
    RC_VS_PAL_CAPACITY = 0.62
    RC_Capacity_Ratio = 1 + (1 - 0.62)
    shelf_trolley_cap_ratio_to_pallet = 9
    shelf_trolley_cap_ratio_to_rollcage = 5
    backstock_target = 0.4
    MODULE_CRATES = 8
    TABLE_CRATES = 4
    SALES_CYCLE = (0.2, 0.2, 0.2, 0.2, 0.2)
    FULFILL_TARGET = 0.6
    capping_shelves_ratio = 0.075
    
    # Load dataset with comprehensive preprocessing (from Replenishment_Model_25.py)
    print("📥 Loading and preprocessing replenishment dataset...")
    Repl_Dataset = pq.read_table(
        directory / repl_dataset_f,
        filters=[("store", "in", stores)],
    ).to_pandas()
    
    # Core preprocessing steps
    Repl_Dataset["is_capping_shelf"] = 0
    
    # Filter out specific stores for HDL department
    target_stores = {25002,25003,25005,25006,25011,25012,25016,25018,25023,25024,25026,25028,25032,25033,25034}
    mask = ~((Repl_Dataset['dep'] == 'HDL') & 
             (Repl_Dataset['store'].isin(target_stores)))
    Repl_Dataset = Repl_Dataset[mask]
    
    # Remove unnecessary columns
    Repl_Dataset = Repl_Dataset.loc[:, ~Repl_Dataset.columns.isin([
        'division_hier','DIV_ID', 'department','DEP_ID',
        'SEC_ID', 'GRP_ID', 'SGR_ID', 'supplier_name', 'weeks','weeksgm'
    ])]
    
    # Filter out SFB department
    Repl_Dataset = Repl_Dataset[~Repl_Dataset.dep.isin(['SFB'])]
    
    # Load and merge presort delivery data
    print("📦 Loading presort delivery data...")
    presort_pal_del_df = pd.read_parquet(directory / presort_pal_del)
    
    Repl_Dataset = Repl_Dataset.merge(
        presort_pal_del_df[[
            'country', 'store', 'pmg', 'pre_sort_perc_by_pmg', 'pallet_%',
            'MIX_CAGE_%', 'MIX_PALLET_%', 'PBL_CAGE_%','PBL_PALLET_%', 'PBS_CAGE_%', 'PBS_PALLET_%'
        ]].drop_duplicates(),
        on=['country', 'store', 'pmg'], 
        how='left'
    )
    
    # Fill NaN values with 0 for delivery columns
    delivery_cols = [
        'pre_sort_perc_by_pmg', 'pallet_%','MIX_CAGE_%', 'MIX_PALLET_%', 'PBL_CAGE_%',
        'PBL_PALLET_%', 'PBS_CAGE_%', 'PBS_PALLET_%'
    ]
    Repl_Dataset[delivery_cols] = Repl_Dataset[delivery_cols].replace(np.nan, 0)
    
    # Column name handling for validation compatibility
    # Keep sales_excl_vat for pandas compatibility, add sales for Polars if needed
    if 'sales_excl_vat' in Repl_Dataset.columns:
        # Keep sales_excl_vat for pandas function
        # Add sales column for Polars compatibility if it doesn't exist
        if 'sales' not in Repl_Dataset.columns:
            Repl_Dataset['sales'] = Repl_Dataset['sales_excl_vat']
        print("📋 Column compatibility: sales_excl_vat (pandas) + sales (polars) available")
    
    print(f"✅ Dataset loaded and preprocessed: {len(Repl_Dataset):,} rows for store {stores}")
    print(f"📊 Dataset columns: {len(Repl_Dataset.columns)} total")
    
    return {
        'data_paths': data_paths,
        'store_inputs': store_inputs,
        'Repl_Dataset': Repl_Dataset,
        'stores': stores,
        'backstock_target': backstock_target,
        'RC_Capacity_Ratio': RC_Capacity_Ratio,
        'shelf_trolley_cap_ratio_to_pallet': shelf_trolley_cap_ratio_to_pallet,
        'shelf_trolley_cap_ratio_to_rollcage': shelf_trolley_cap_ratio_to_rollcage,
        'MODULE_CRATES': MODULE_CRATES,
        'TABLE_CRATES': TABLE_CRATES,
        'FULFILL_TARGET': FULFILL_TARGET,
        'SALES_CYCLE': SALES_CYCLE,
        'RC_CAPACITY': RC_CAPACITY,
        'RC_DELIVERY': RC_DELIVERY,
        'RC_VS_PAL_CAPACITY': RC_VS_PAL_CAPACITY,
        'capping_shelves_ratio': capping_shelves_ratio,
        'selected_tpn': data_paths.selected_tpn,
        'only_tpn': False,

        'version': act_version_name
    }

def run_pandas_implementation(env):
    """Run pandas implementation of Repl_Drivers_Calculation_TPN"""
    print("🐼 RUNNING PANDAS IMPLEMENTATION...")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # Call original pandas function with all required parameters
        pandas_drivers, pandas_drivers_produce = rmf.Repl_Drivers_Calculation_TPN(
            directory=env['data_paths'].directory,
            Repl_Dataset=env['Repl_Dataset'],
            store_inputs=env['store_inputs'],
            backstock_target=env['backstock_target'],
            RC_Capacity_Ratio=env['RC_Capacity_Ratio'],
            shelf_trolley_cap_ratio_to_pallet=env['shelf_trolley_cap_ratio_to_pallet'],
            shelf_trolley_cap_ratio_to_rollcage=env['shelf_trolley_cap_ratio_to_rollcage'],
            excel_inputs_f=env['data_paths'].excel_inputs_f,
            MODULE_CRATES=env['MODULE_CRATES'],
            TABLE_CRATES=env['TABLE_CRATES'],
            FULFILL_TARGET=env['FULFILL_TARGET'],
            SALES_CYCLE=env['SALES_CYCLE'],
            RC_CAPACITY=env['RC_CAPACITY'],
            RC_DELIVERY=env['RC_DELIVERY'],
            RC_VS_PAL_CAPACITY=env['RC_VS_PAL_CAPACITY'],
            only_tpn=env['only_tpn'],
            tpnb_store=False,
            tpnb_country=False,
            selected_tpn=env['selected_tpn'],
            capping_shelves_ratio=env['capping_shelves_ratio'],
            stores=env['stores'],
            version=env['version'],
            shelfService_gm=False,
            cases_to_replenish_only=False
        )
        
        pandas_time = time.time() - start_time
        print(f"✅ Pandas completed in {pandas_time:.2f} seconds")
        print(f"   Drivers shape: {pandas_drivers.shape}")
        print(f"   Drivers_produce shape: {pandas_drivers_produce.shape}")
        
        return pandas_drivers, pandas_drivers_produce, pandas_time
        
    except Exception as e:
        print(f"❌ Pandas implementation failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None, 0


def run_polars_implementation(env):
    """Run Polars implementation of Repl_Drivers_Calculation_polars"""
    print("⚡ RUNNING POLARS IMPLEMENTATION...")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # Import polars_drivers module
        import polars_drivers as pd_polars
        
        # Call Polars function with all required parameters
        polars_drivers, polars_drivers_produce = pd_polars.Repl_Drivers_Calculation_polars(
            directory=env['data_paths'].directory,
            Repl_Dataset=env['Repl_Dataset'],
            store_inputs=env['store_inputs'],
            backstock_target=env['backstock_target'],
            RC_Capacity_Ratio=env['RC_Capacity_Ratio'],
            shelf_trolley_cap_ratio_to_pallet=env['shelf_trolley_cap_ratio_to_pallet'],
            shelf_trolley_cap_ratio_to_rollcage=env['shelf_trolley_cap_ratio_to_rollcage'],
            excel_inputs_f=env['data_paths'].excel_inputs_f,
            MODULE_CRATES=env['MODULE_CRATES'],
            TABLE_CRATES=env['TABLE_CRATES'],
            FULFILL_TARGET=env['FULFILL_TARGET'],
            SALES_CYCLE=env['SALES_CYCLE'],
            RC_CAPACITY=env['RC_CAPACITY'],
            RC_DELIVERY=env['RC_DELIVERY'],
            RC_VS_PAL_CAPACITY=env['RC_VS_PAL_CAPACITY'],
            only_tpn=env['only_tpn'],
            tpnb_store=False,
            tpnb_country=False,
            selected_tpn=env['selected_tpn'],
            capping_shelves_ratio=env['capping_shelves_ratio'],
            stores=env['stores'],
            version=env['version'],
            shelfService_gm=False,
            cases_to_replenish_only=False
        )
        
        polars_time = time.time() - start_time
        print(f"✅ Polars completed in {polars_time:.2f} seconds")
        print(f"   Drivers shape: {polars_drivers.shape}")
        print(f"   Drivers_produce shape: {polars_drivers_produce.shape}")
        
        return polars_drivers, polars_drivers_produce, polars_time
        
    except Exception as e:
        print(f"❌ Polars implementation failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None, 0


def exact_dataframe_comparison(df1, df2, name):
    """Compare two DataFrames for exact equality with detailed reporting"""
    try:
        print(f"📊 Comparing {name}...")
        
        # Shape comparison
        if df1.shape != df2.shape:
            print(f"❌ Shape mismatch: {df1.shape} vs {df2.shape}")
            
            # Show detailed column differences when shapes don't match
            df1_cols = set(df1.columns)
            df2_cols = set(df2.columns)
            missing_in_df2 = df1_cols - df2_cols
            missing_in_df1 = df2_cols - df1_cols
            
            print(f"\n🔍 DETAILED COLUMN ANALYSIS:")
            if missing_in_df2:
                print(f"   ❌ Missing in Polars: {sorted(missing_in_df2)}")
            if missing_in_df1:
                print(f"   ❌ Missing in Pandas: {sorted(missing_in_df1)}")
            
            # Show first 30 columns from each for debugging
            pandas_cols_sorted = sorted(df1_cols)
            polars_cols_sorted = sorted(df2_cols)
            print(f"\n📋 First 30 columns comparison:")
            print(f"   Pandas: {pandas_cols_sorted[:30]}")
            print(f"   Polars: {polars_cols_sorted[:30]}")
            
            return False
        
        print(f"✅ Shape match: {df1.shape}")
        
        # Column comparison
        df1_cols = set(df1.columns)
        df2_cols = set(df2.columns)
        
        if df1_cols != df2_cols:
            missing_in_df2 = df1_cols - df2_cols
            missing_in_df1 = df2_cols - df1_cols
            print(f"❌ Column mismatch:")
            if missing_in_df2:
                print(f"   Missing in Polars: {sorted(missing_in_df2)}")
            if missing_in_df1:
                print(f"   Missing in Pandas: {sorted(missing_in_df1)}")
            
            # Show first 20 columns from each for debugging
            print(f"\n🔍 First 20 columns comparison:")
            pandas_cols_sorted = sorted(df1_cols)
            polars_cols_sorted = sorted(df2_cols)
            print(f"   Pandas first 20: {pandas_cols_sorted[:20]}")
            print(f"   Polars first 20: {polars_cols_sorted[:20]}")
            
            return False
        
        print(f"✅ Column match: {len(df1.columns)} columns")
        
        # Sort both DataFrames by all columns for consistent comparison
        common_cols = sorted(list(df1_cols))
        df1_sorted = df1[common_cols].sort_values(by=common_cols).reset_index(drop=True)
        df2_sorted = df2[common_cols].sort_values(by=common_cols).reset_index(drop=True)
        
        # Value comparison column by column
        mismatched_cols = []
        
        for col in common_cols:
            # Handle different data types
            try:
                # Convert to same data type for comparison
                col1 = pd.to_numeric(df1_sorted[col], errors='ignore')
                col2 = pd.to_numeric(df2_sorted[col], errors='ignore')
                
                if not col1.equals(col2):
                    # For numeric columns, check with tolerance
                    if pd.api.types.is_numeric_dtype(col1) and pd.api.types.is_numeric_dtype(col2):
                        # Fill NaN values with 0 for comparison
                        col1_filled = col1.fillna(0)
                        col2_filled = col2.fillna(0)
                        
                        # Check if values are close (tolerance for floating point)
                        if not np.allclose(col1_filled, col2_filled, rtol=1e-10, atol=1e-10, equal_nan=True):
                            mismatched_cols.append(col)
                            diff_mask = ~np.isclose(col1_filled, col2_filled, rtol=1e-10, atol=1e-10, equal_nan=True)
                            num_diffs = diff_mask.sum()
                            print(f"❌ {col}: {num_diffs} value differences detected")
                            
                            if num_diffs <= 5:  # Show first few differences
                                diff_indices = np.where(diff_mask)[0][:5]
                                for idx in diff_indices:
                                    print(f"     Row {idx}: {col1_filled.iloc[idx]} ≠ {col2_filled.iloc[idx]}")
                    else:
                        mismatched_cols.append(col)
                        print(f"❌ {col}: Non-numeric differences detected")
                        
            except Exception as e:
                mismatched_cols.append(col)
                print(f"❌ {col}: Comparison error - {e}")
        
        if mismatched_cols:
            print(f"❌ {len(mismatched_cols)} columns have differences: {mismatched_cols[:10]}")
            return False
        else:
            print(f"✅ All {len(common_cols)} columns match exactly!")
            return True
            
    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run():
    
    # Setup environment
    env = setup_control_panel_environment()
    print()
    
    # Run pandas implementation
    pandas_drivers, pandas_drivers_produce, pandas_time = run_pandas_implementation(env)
    if pandas_drivers is None:
        print("💥 Cannot continue - pandas implementation failed")
        return False
    
    # Run Polars implementation  
    polars_drivers, polars_drivers_produce, polars_time = run_polars_implementation(env)
    if polars_drivers is None:
        print("💥 Cannot continue - Polars implementation failed")
        return False
    
    print()
    print("🚀 PERFORMANCE COMPARISON")
    print("-" * 40)
    speedup = pandas_time / polars_time if polars_time > 0 else float('inf')
    print(f"⏱️  Pandas time: {pandas_time:.2f} seconds")
    print(f"⚡ Polars time: {polars_time:.2f} seconds")
    print(f"🚀 Speedup: {speedup:.2f}x faster")
    
    print()
    print("🔍 EXACT VALUE VALIDATION - DRIVERS ONLY")
    print("=" * 50)
    print("⚖️  Comparing drivers DataFrame with zero tolerance")
    print("📝 Note: Skipping produce drivers comparison as requested")
    print()
    
    # Validate Drivers DataFrame ONLY
    print("📊 VALIDATING DRIVERS DATAFRAME")
    print("-" * 40)
    drivers_match = exact_dataframe_comparison(
        pandas_drivers, polars_drivers, "Drivers"
    )
    
    print()
    print("🎯 FINAL VALIDATION RESULT")
    print("=" * 50)
    
    if drivers_match:
        print("✅ SUCCESS: DRIVERS EXACT VALUE EQUIVALENCE CONFIRMED!")
        print("🎉 Every single value in Drivers DataFrame matches exactly")
        print(f"🚀 Performance improvement: {speedup:.2f}x faster")
        print("🏭 Polars optimization validated - READY FOR PRODUCTION!")
        return True
    else:
        print("❌ FAILURE: DRIVERS VALUE DIFFERENCES DETECTED")
        print("🔧 Polars implementation requires further debugging")
        print(f"   Drivers match: {'❌'}")
        return False

if __name__ == "__main__":
    print("UNIFIED VALIDATION WORKFLOW")
    print("=" * 50)
    print("🎯 This script validates exact equivalence between pandas and Polars")
    print("⚖️  Every value must match exactly for production approval")
    print()
    
    success = run()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 VALIDATION COMPLETE: POLARS READY FOR PRODUCTION!")
    else:
        print("🔧 VALIDATION FAILED: DEBUGGING REQUIRED")
    print("=" * 60)
