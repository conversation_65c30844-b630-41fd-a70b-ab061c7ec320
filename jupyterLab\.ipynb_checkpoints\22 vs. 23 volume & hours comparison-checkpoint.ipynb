{"cells": [{"cell_type": "markdown", "id": "63eaef59-f448-43da-9fed-6705afc53db6", "metadata": {}, "source": ["## Dependencies"]}, {"cell_type": "code", "execution_count": null, "id": "fb4f7366-2710-46b3-a70a-bd108b1acc6c", "metadata": {}, "outputs": [], "source": ["new = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl_Dataset_2022_new_14w_27w_promo_flag.parquet\")\n", "store_format = new[['store', 'Format']].drop_duplicates()\n", "\n", "for x in [ 'srp', 'nsrp', 'full_pallet', 'mu']:\n", "    new.loc[new[x] > 0, x] = new['sold_units']\n", "\n", "a = new.groupby(['country', 'division'], observed=True)[ 'srp', 'nsrp', 'full_pallet', 'mu'].sum().reset_index()\n", "a = a.melt(id_vars=['country',  'division'], value_vars=['srp', 'nsrp', 'full_pallet', 'mu'], var_name='repl_type', value_name='total')\n", "a['percent'] = a.total / a.groupby(['country', 'division'])['total'].transform(\"sum\")\n", "a = a.sort_values(by=['country', 'division', 'repl_type'], ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "82657502-29f7-4398-bbbc-ef7eeed70504", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "\n", "for c in (a.country.unique().tolist()):\n", "\n", "\n", "    fig = go.Figure()\n", "    fig.add_trace(go.Bar(\n", "        y=a.division.unique().tolist(),\n", "        x=[x * 100 for x in a.query(\"country == @c & repl_type == 'srp'\")['percent'].tolist()],\n", "        text=[f\"SRP: {str(round(x * 100, 1) )+'%'}\"  for x in a.query(\"country == @c & repl_type == 'srp'\")['percent'].tolist()],\n", "        textposition='auto',\n", "        name=a.repl_type.unique().tolist()[0],\n", "        orientation='h',\n", "        marker=dict(\n", "            color='rgba(112, 127, 206, 0.8)',\n", "            line=dict(color='rgba(112, 127, 206, 1.0)', width=3)\n", "        )\n", "    ))\n", "    fig.add_trace(go.Bar(\n", "        y=a.division.unique().tolist(),\n", "        x=[x * 100 for x in a.query(\"country == @c & repl_type == 'nsrp'\")['percent'].tolist()],\n", "        text=[f\"NSRP: {str(round(x * 100, 1) )+'%'}\"  for x in a.query(\"country == @c & repl_type == 'nsrp'\")['percent'].tolist()],\n", "        textposition='auto',\n", "        name=a.repl_type.unique().tolist()[1],\n", "        orientation='h',\n", "        marker=dict(\n", "            color='rgba(245, 89, 15, 0.37)',\n", "            line=dict(color='rgba(245, 89, 15, 0.5)', width=3)\n", "        )\n", "    ))\n", "\n", "\n", "    fig.add_trace(go.Bar(\n", "\n", "        y=a.division.unique().tolist(),\n", "        x=[x * 100 for x in a.query(\"country == @c & repl_type == 'mu'\")['percent'].tolist()],\n", "        text=[f\"MU: {str(round(x * 100, 1) )+'%'}\"  for x in a.query(\"country == @c & repl_type == 'mu'\")['percent'].tolist()],\n", "        textposition='auto',\n", "        name=a.repl_type.unique().tolist()[2],\n", "        orientation='h',\n", "        marker=dict(\n", "            color='rgba(71, 245, 39, 0.8)',\n", "            line=dict(color='rgba(71, 245, 39, 1.0)', width=3)\n", "        )\n", "    ))\n", "\n", "\n", "    fig.add_trace(go.Bar(\n", "\n", "        y=a.division.unique().tolist(),\n", "        x=[x * 100 for x in a.query(\"country == @c & repl_type == 'full_pallet'\")['percent'].tolist()],\n", "        text=[f\"FULL PALLET: {str(round(x * 100, 1) )+'%'}\"  for x in a.query(\"country == @c & repl_type == 'full_pallet'\")['percent'].tolist()],\n", "        textposition='inside',\n", "        name=a.repl_type.unique().tolist()[3],\n", "        orientation='h',\n", "        marker=dict(\n", "            color='rgba(39, 157, 245, 0.8)',\n", "            line=dict(color='rgba(39, 157, 245, 1.0)', width=3)\n", "        )\n", "    ))\n", "    \n", "\n", "    \n", "\n", "    fig.update_layout(barmode='stack', title_text= f'Country: {c}', uniformtext=dict(mode=\"hide\", minsize=10),\n", "                         paper_bgcolor='rgb(248, 248, 255)',\n", "                            plot_bgcolor='rgb(248, 248, 255)',\n", "                            margin=dict(l=120, r=10, t=140, b=80))\n", "    fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "3ebe7e87-1c46-4999-8b26-71d50fa6e75b", "metadata": {}, "outputs": [], "source": ["c"]}, {"cell_type": "code", "execution_count": null, "id": "891463c0-face-40d8-9016-1e62ce0bc062", "metadata": {}, "outputs": [], "source": ["               x = captures, y = values,\n", "               base = 0,\n", "               text = text, textposition = 'outside',   \n", "               measure = [\"absolute\",  \"relative\", \"relative\",\n", "                          \"relative\",\"relative\",\"relative\",\"relative\",\"relative\",\"absolute\",\"relative\",\"relative\", \"total\"],\n", "                connector = {\"line\":{\"color\":\"rgb(63, 63, 63)\"}},\n", "                texttemplate = '%{text:,}'"]}, {"cell_type": "code", "execution_count": null, "id": "d3571c6e-1574-47ef-8e8b-e96206dcd350", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "538442d1-830a-47d2-a147-8d1c66fcb41b", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "from pathlib import Path\n", "import pyarrow.parquet as pq\n", "import plotly.express as px\n", "import warnings\n", "import plotly.graph_objects as go\n", "import numpy as np\n", "from plotly.subplots import make_subplots\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": null, "id": "abb09998-7031-44df-954d-57d17994d401", "metadata": {}, "outputs": [], "source": ["from plotly.subplots import make_subplots\n", "import plotly.graph_objects as go\n", "\n", "fig = make_subplots(\n", "    rows=2, cols=2, subplot_titles=('title 1', 'title 2', 'title 3','title 4'),\n", "    specs=[[{\"type\": \"xy\"}, {\"type\": \"polar\"}],\n", "           [{\"type\": \"domain\"}, {\"type\": \"xy\"}]],\n", ")\n", "\n", "fig.add_trace(go.Bar(y=[2, 3, 1]),\n", "              row=1, col=1)\n", "\n", "fig.add_trace(go.<PERSON>(theta=[0, 45, 90], r=[2, 3, 1]),\n", "              row=1, col=2)\n", "\n", "fig.add_trace(go.Pie(values=[2, 3, 1]),\n", "              row=2, col=1)\n", "\n", "fig.add_trace(go.<PERSON>(x=[2, 3, 1], y=[0.5, 1, 2], mode=\"lines\"),\n", "              row=2, col=2)\n", "\n", "fig.update_layout(height=700, showlegend=False)\n", "\n", "\n", "fig.layout.annotations[0].update(x=0.025)\n", "fig.layout.annotations[2].update(x=0.025)\n", "fig.layout.annotations[1].update(x=0.575)\n", "fig.layout.annotations[3].update(x=0.575)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "a5450520-2027-48a6-af92-4104014b35e3", "metadata": {"tags": []}, "outputs": [], "source": ["fig = make_subplots(\n", "    rows=1, cols=2,\n", "    shared_xaxes=False,\n", "    shared_yaxes=False,\n", "    #vertical_spacing=0.1,\n", "    column_widths=[0.5, 0.5],\n", "    #vertical_spacing = [0.8],\n", "    specs=[[{\"type\": \"domain\"}, None]])\n", "    \n", "    \n", "fig.add_trace(\n", "    go.Pie(values=[20,40],\n", "    labels=[\"Reds\",\"Blues\"],\n", "    domain={\"x\":[0.2,0.8], \"y\":[0.1,0.9]},\n", "    hole=0.5,\n", "    direction=\"clockwise\",\n", "    sort=False,\n", "    marker={\"colors\":[\"#CB4335\",\"#2E86C1\"]}),1, 1)\n", "\n", "fig.add_trace(\n", "    go.Pie(values=[5,15,30,10],\n", "        labels=[\"Medium Red\",\"Light Red\",\"Medium Blue\",\"Light Blue\"],\n", "        domain={\"x\":[0.1,0.9], \"y\":[0,1]},\n", "        hole=0.8,\n", "        direction=\"clockwise\",\n", "        sort=False,\n", "        marker={\"colors\":[\"#EC7063\",\"#F1948A\",\"#5DADE2\",\"#85C1E9\"]},\n", "        showlegend=True),1, 1)\n", "\n", "\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "c6c76998-efbe-4076-a9f6-bae8df8ce172", "metadata": {"tags": []}, "outputs": [], "source": ["data = [# Portfolio (inner donut)\n", "        go.Pie(values=[20,40],\n", "        labels=[\"Reds\",\"Blues\"],\n", "        domain={\"x\":[0.2,0.8], \"y\":[0.1,0.9]},\n", "        hole=0.5,\n", "        direction=\"clockwise\",\n", "        sort=False,\n", "        marker={\"colors\":[\"#CB4335\",\"#2E86C1\"]}),\n", "        # Individual components (outer donut)\n", "        go.Pie(values=[5,15,30,10],\n", "        labels=[\"Medium Red\",\"Light Red\",\"Medium Blue\",\"Light Blue\"],\n", "        domain={\"x\":[0.1,0.9], \"y\":[0,1]},\n", "        hole=0.75,\n", "        direction=\"clockwise\",\n", "        sort=False,\n", "        marker={\"colors\":[\"#EC7063\",\"#F1948A\",\"#5DADE2\",\"#85C1E9\"]},\n", "        showlegend=False)\n", "                 ]\n", "\n", "fig = go.Figure(data=data, layout={\"title\":\"Nested Pie Chart\"})\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "id": "36a7f9e7-505c-45dc-8af9-59f6cb721df8", "metadata": {}, "source": ["# Volume Comparison"]}, {"cell_type": "code", "execution_count": null, "id": "a82c034d-d5d3-4589-a284-67415b8cabc6", "metadata": {"tags": []}, "outputs": [], "source": ["# dir\n", "old_volume_dir =  Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2021\\Model_Datasets\\Volume_table_22_Q1_LY_CASE_UNIT_RATIO_2.7_HU_GM_MP_closure_remCZ_SK.xlsx\")\n", "old_hours_dir = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2021\\Model_Outputs\\OPB_DEP_21001_fixed_drivers.xlsx\")\n", "\n", "new_volume_dir = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2022\\Model_Datasets\\Repl_Dataset_2022_new_14w_27w.parquet\")\n", "new_hours_dir = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\OPB_DEP_backstock_logic.xlsx\")\n", "\n", "# files\n", "old_volume = pd.read_excel(old_volume_dir, usecols=['country', 'store', 'pmg', 'items_sold', 'cases_delivered', 'dep' ]).rename(columns={'items_sold' : 'sold_units'})\n", "old_hours = pd.read_excel(old_hours_dir, usecols=['Country', 'Store', 'Format', 'Dep', 'Total Weekly Hours'])\n", "new_volume = pq.read_table(new_volume_dir).to_pandas()\n", "\n", "new_volume = new_volume.groupby(['country', 'store', 'tpnb', 'division', 'pmg', 'dep'], observed=True).agg({'sold_units' : 'sum', 'cases_delivered' : 'sum'}).reset_index()\n", "new_volume = new_volume.groupby(['country', 'store', 'division', 'pmg',  'dep'], observed=True).agg({'sold_units' : 'sum', 'cases_delivered' : 'sum'}).reset_index()\n", "\n", "new_hours = pd.read_excel(new_hours_dir, usecols=['Country', 'Store', 'Format', 'Dep', 'Total Weekly Hours'])\n", "\n", "pmg = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl\\Stores_Inputs_2023_Q1_presort_verz1_modul.xlsx\",\n", "                    sheet_name=\"pmg\", usecols=[\"Dep\", \"Division\"]).drop_duplicates()\n", "pmg = pmg[pmg.Division != 'Other']\n", "\n", "\n", "new_hours = new_hours.merge(pmg, on=\"Dep\", how=\"left\")\n", "old_hours = old_hours.merge(pmg, on=\"Dep\", how=\"left\")\n", "\n", "\n", "old_volume['which_one'] = \"2022_as_is\"\n", "old_volume.rename(columns={'dep': 'Dep'}, inplace=True)\n", "old_volume = old_volume.merge(pmg, on=\"Dep\", how=\"left\")\n", "new_volume['which_one'] = \"2023_to_be\"\n", "new_volume.rename(columns={'division': 'Division', 'dep': 'Dep'}, inplace=True)\n", "\n", "volume = pd.concat([old_volume, new_volume ]).reset_index(drop=True)\n", "volume_group = volume.groupby(['country', 'Division', 'which_one'], observed=True)['sold_units', 'cases_delivered'].sum().reset_index()\n", "\n", "\n", "#stock\n", "new_stock = pq.read_table(new_volume_dir).to_pandas()\n", "new_stock = new_stock.groupby(['country', 'store', 'tpnb', 'division', 'pmg', 'dep'], observed=True).agg({'sold_units' : 'sum', 'cases_delivered' : 'sum', 'stock' : 'mean'}).reset_index()\n", "new_stock = new_stock.groupby([\"country\", 'division'])['stock'].sum().reset_index()\n", "new_stock['which_one'] = \"2023_to_be\"\n", "\n", "old_stock = pq.read_table(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2021\\Model_Datasets\\repl_dataset_2022_vol2.parquet\").to_pandas()\n", "old_stock = old_stock.groupby(['country', 'store', 'tpnb', 'division', 'pmg', 'dep'], observed=True).agg({'sold_units' : 'sum',  'stock' : 'sum'}).reset_index()\n", "old_stock = old_stock.groupby([\"country\", 'division'])['stock'].sum().reset_index()\n", "old_stock['which_one'] = \"2022_to_be\"\n", "\n", "volume_stock = pd.concat([old_stock, new_stock ]).reset_index(drop=True)"]}, {"cell_type": "markdown", "id": "293b3243-5b4d-4f16-acaa-e712b6d6876b", "metadata": {"tags": []}, "source": ["## Part_1: cases_delivered"]}, {"cell_type": "code", "execution_count": null, "id": "f16b58d4-d62a-45dc-873b-2a2d0448121f", "metadata": {"tags": []}, "outputs": [], "source": ["fig = px.histogram(volume_group, x=\"Division\", y=\"cases_delivered\",\n", "             color='which_one', barmode='group', text_auto='.2s', title=f\"CE_cases_delivered\",\n", "             height=400, facet_col=\"country\")\n", "            \n", "fig.show()"]}, {"cell_type": "markdown", "id": "ff7d57ec-5d76-467b-be8e-8aa3fdfa2616", "metadata": {"tags": []}, "source": ["## Part_2: Sold_units"]}, {"cell_type": "code", "execution_count": null, "id": "6d9de430-a411-4ac9-89e1-ee14d5cd6700", "metadata": {"tags": []}, "outputs": [], "source": ["fig = px.histogram(volume_group, x=\"Division\", y=\"sold_units\",\n", "             color='which_one', barmode='group', text_auto='.2s', title=f\"CE_sold_units\",\n", "             height=400,  facet_col=\"country\")\n", "            \n", "fig.show()"]}, {"cell_type": "markdown", "id": "2ea75039-9626-49b1-9ffd-5b4b2e7a6c46", "metadata": {}, "source": ["## Part_3: Stock"]}, {"cell_type": "code", "execution_count": null, "id": "2c3f2ec2-94a1-42d3-8fa8-7a947f8e6119", "metadata": {"tags": []}, "outputs": [], "source": ["fig = px.histogram(volume_stock, x=\"division\", y=\"stock\",\n", "             color='which_one', barmode='group', text_auto='.2s', title=f\"CE_Stock\",\n", "             height=400,  facet_col=\"country\")\n", "            \n", "fig.show()"]}, {"cell_type": "markdown", "id": "c0183fb9-edf5-4a25-a338-8659082b72e8", "metadata": {}, "source": ["## Part_3: Hours Comparison"]}, {"cell_type": "code", "execution_count": null, "id": "d1c37fe7-8cd8-4b6b-956a-151a81d90771", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e1bca6c8-60b7-4060-8a3c-6f843ba3fe10", "metadata": {"tags": []}, "outputs": [], "source": ["hours = old_hours.merge(new_hours, on=['Country','Store','Format','Division'], how='inner').rename(\n", "    columns={'Total Weekly Hours_x' : '2022_as_is', 'Total Weekly Hours_y' : '2023_to_be'}).melt(\n", "    id_vars=['Country','Store','Format','Division'],\n", "    value_vars=['2022_as_is', '2023_to_be'],\n", "    var_name=\"which_one\", value_name='hours')\n", "\n", "hours_group = hours.groupby(['Country','Format', 'Division', 'which_one'], observed=True)['hours'].sum().reset_index()\n", "#hours_group = hours_group.query('Division == \"Grocery\"')\n", "\n", "fig = px.histogram(hours_group, x=\"Division\", y=\"hours\",\n", "             color='which_one', barmode='group', text_auto='.2s', title=f\"CE_hours\", facet_col=\"Country\",  facet_row=\"Format\",\n", "             height=600, category_orders={\"Format\": [\"1K\", \"Express\", \"Compact\", \"Hypermarket\"]})\n", "fig.update_yaxes(matches=None)\n", "fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=12))\n", "fig.update_yaxes(title_font_color='white')\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "367b06f2-361c-4717-b32e-62d349eca181", "metadata": {"tags": []}, "outputs": [], "source": ["for x in (hours_group.Country.unique().tolist()):\n", "    \n", "    hours_group_ = hours_group.query(f\"Country == @x\")\n", "\n", "    fig = px.histogram(hours_group_, x=\"Dep\", y=\"hours\",\n", "                 color='which_one', barmode='group', text_auto='.2s', title=f\"{x}_hours\",  \n", "                 height=400)\n", "    fig.show()"]}, {"cell_type": "markdown", "id": "acc738fa-7a0a-4642-bacd-2533848d8b81", "metadata": {}, "source": ["## Insight-Activity Groups Comparison"]}, {"cell_type": "code", "execution_count": null, "id": "60bf258e-bcb3-4b34-b594-62b3d670fa15", "metadata": {}, "outputs": [], "source": ["new_insight = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\insight_dep.parquet\")\n", "to_be_act = pq.read_table(new_insight).to_pandas()"]}, {"cell_type": "code", "execution_count": null, "id": "f7acdef8-2718-4c9a-9de2-c7629216384c", "metadata": {}, "outputs": [], "source": ["to_be_act[to_be_act.Suboperation.str.contains(\"Write empty\")].head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "1f7971a4-0c58-43f3-8e7d-7daf08cfeef0", "metadata": {}, "outputs": [], "source": ["a.pivot_table(index=['Store', 'Dep'], columns='Driver_1', values='Driver_1_value', aggfunc=\"sum\", fill_value=0).reset_index().to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\drivers_dep.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "b5ac3fce-fb10-425e-9b83-5ca985fa8f61", "metadata": {}, "outputs": [], "source": ["a = a[['Store', 'Dep', 'Driver_1', 'Driver_1_value']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "11a20f91-e273-4d4a-8c41-028a13877bd3", "metadata": {}, "outputs": [], "source": ["a = to_be_act[['Store', 'Dep', 'Suboperation', 'Driver_1', 'Driver_1_value']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": null, "id": "f819d6f3-83c5-4e1c-8955-845f7cc9ad12", "metadata": {"tags": []}, "outputs": [], "source": ["# files\n", "\n", "old_insight = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2021\\Model_Outputs\\INSIGHT_21001_fixed_drivers.csv.gz\")\n", "new_insight = Path(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\INSIGHT_23_Q1_vol1.parquet.gz\")\n", "\n", "\n", "as_is_act = pd.read_csv(old_insight).groupby(['Country','Format', 'Store', 'Division', 'Activity_Group'])['hours'].sum().reset_index()\n", "to_be_act = pq.read_table(new_insight).to_pandas().groupby(['Country','Format', 'Store', 'Division', 'Activity_Group'])['hours'].sum().reset_index()\n", "\n", "merged_table = as_is_act.merge(to_be_act, on=['Country', 'Format', 'Store', 'Division', 'Activity_Group'], how='inner', suffixes=('_as_is', '_to_be')).query(\"hours_to_be.notnull()\")\n", "grouped_table = merged_table.rename(\n", "    columns={'hours_as_is':'2022_as_is', 'hours_to_be':'2023_to_be'}\n", "    ).melt(\n", "    id_vars=merged_table.columns[:5].tolist(),\n", "    value_vars=['2022_as_is', '2023_to_be'],\n", "    var_name=\"which_one\", value_name='hours')\n", "\n", "act_groups = ['Replenishment', 'Stock Movement', 'Post-sort', 'Pre-sort', 'RSU', 'Waste']\n", "country = grouped_table.Country.unique().tolist()  # ['HU']\n", "div = grouped_table.Division.unique().tolist() # ['Grocery']\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "41ac4595-85df-4faa-a214-4437c626d977", "metadata": {"tags": []}, "outputs": [], "source": ["fig = px.histogram(grouped_table.query(\"Activity_Group in @act_groups\"), x=\"Activity_Group\", y=\"hours\",\n", "             color='which_one', barmode='group', text_auto='.2s', title=f\"CE_Activity_Group_hours\", facet_col=\"Country\", facet_row='Format', height=600,\n", "            category_orders={\"Format\": [\"1K\", \"Express\", \"Compact\", \"Hypermarket\"]})\n", "\n", "fig.update_yaxes(matches=None)\n", "fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=12))\n", "fig.update_yaxes(title_font_color='white')\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "caa5a66d-0ed4-49b1-989a-219dc4da8eb7", "metadata": {"tags": []}, "outputs": [], "source": ["fig = px.histogram(grouped_table.query(\"Activity_Group in @act_groups\"), x=\"Activity_Group\", y=\"hours\",\n", "             color='which_one', barmode='group', text_auto='.2s', title=f\"CE_Activity_Group_hours\", facet_col=\"Country\", facet_row='Format', height=600\n", "            , category_orders={\"Format\": [\"1K\", \"Express\", \"Compact\", \"Hypermarket\"]})\n", "fig.update_yaxes(matches=None)\n", "fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=12))\n", "fig.update_yaxes(title_font_color='white')\n", "fig.show()"]}, {"cell_type": "markdown", "id": "e43595ee-8dad-4f71-825f-085892e85b0e", "metadata": {}, "source": ["## Repl Type pie charts"]}, {"cell_type": "code", "execution_count": null, "id": "ade6d506-0f32-4042-a324-9eee19de833c", "metadata": {}, "outputs": [], "source": ["new2 = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl_Dataset_2022_23_Q1_vol3_new_prices.parquet\")"]}, {"cell_type": "code", "execution_count": null, "id": "d394036a-46e6-45ee-805b-ccfdd277175b", "metadata": {}, "outputs": [], "source": ["new2.head()"]}, {"cell_type": "code", "execution_count": 2, "id": "cbf38144-d5f5-4b7f-bef1-ce4073a943e1", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "from pathlib import Path\n", "import pyarrow.parquet as pq\n", "import plotly.express as px\n", "import warnings\n", "import plotly.graph_objects as go\n", "import numpy as np\n", "from plotly.subplots import make_subplots\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option('display.max_columns', None)\n", "\n", "\n", "# new = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl_Dataset_2022_new_14w_27w_promo_flag.parquet\")\n", "# store_format = new[['store', 'Format']].drop_duplicates()\n", "\n", "# for x in [ 'srp', 'nsrp', 'full_pallet', 'mu']:\n", "#     new.loc[new[x] > 0, x] = new['sold_units']\n", "\n", "# new = new[new.division==\"Grocery\"].groupby(['country', 'Format'], observed=True)[ 'srp', 'nsrp', 'full_pallet', 'mu'].sum().reset_index()\n", "# new = new.melt(id_vars=['country', 'Format'], value_vars=['srp', 'nsrp', 'full_pallet', 'mu'], var_name='repl_type', value_name='total')\n", "# new_gr = new.groupby(['country', 'repl_type'])['total'].sum().reset_index()\n", "\n", "\n", "\n", "\n", "\n", "new2 = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl_Dataset_2022_23_Q1_vol3_new_prices.parquet\")\n", "store_format = new2[['store', 'format']].drop_duplicates()\n", "\n", "\n", "for x in [ 'srp', 'nsrp', 'full_pallet', 'mu', 'split_pallet']:\n", "    new2.loc[new2[x] > 0, x] = new2['sold_units']\n", "    \n", "new2['srp_'] = new2['srp'] + new2['split_pallet']\n", "new2.drop(['srp', 'split_pallet'], axis=1, inplace=True)\n", "new2.rename(columns={'srp_':'srp'},inplace=True)\n", "new2 = new2[new2.division==\"Grocery\"].groupby(['country', 'pmg'], observed=True)[ 'srp', 'nsrp', 'full_pallet', 'mu'].sum().reset_index()\n", "new2 = new2.melt(id_vars=['country', 'pmg'], value_vars=['srp', 'nsrp', 'full_pallet', 'mu'], var_name='repl_type', value_name='total')\n", "new2_gr = new2.groupby(['country','pmg', 'repl_type'])['total'].sum().reset_index()\n", "\n", "\n", "\n", "old = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2021\\Model_Datasets\\repl_dataset_2022_vol2.parquet\")\n", "\n", "for x in [ 'srp', 'nsrp', 'full_pallet', 'mu']:\n", "    old.loc[old[x] > 0, x] = old['sold_units']\n", "    \n", "old = old.merge(store_format, on='store', how='left')\n", "old = old[old.division==\"Grocery\"].groupby(['country', 'pmg'], observed=True)[ 'srp', 'nsrp', 'full_pallet', 'mu'].sum().reset_index()\n", "old = old.melt(id_vars=['country', 'pmg'], value_vars=['srp', 'nsrp', 'full_pallet', 'mu'], var_name='repl_type', value_name='total')\n", "old_gr = old.groupby(['country','pmg', 'repl_type'])['total'].sum().reset_index()\n", "\n", "pmg_names= pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\apps\\ReplTypeChanger_app_split_pallet_version\\inputs\\pmg_codes_store_format.xlsx\")\n", "pmg_names.columns =  [x.lower() for x in pmg_names.columns]\n", "old_gr = old_gr.merge(pmg_names[['pmg', 'pmg_code_name']], on = ['pmg'], how='left')\n", "new2_gr = new2_gr.merge(pmg_names[['pmg', 'pmg_code_name']], on = ['pmg'], how='left')\n", "old_gr['dep'] = old_gr['pmg'].str[:3]\n", "new2_gr['dep'] = new2_gr['pmg'].str[:3]\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "id": "43b2273a-7fb8-4de4-8be0-0ef7160227dd", "metadata": {}, "outputs": [], "source": ["summary = old_gr.merge(new2_gr[['country','pmg','repl_type','total_22']], on=['country','pmg','repl_type'], how='left')"]}, {"cell_type": "code", "execution_count": 17, "id": "afde20c0-3f2b-4f41-8cbe-f8f43d72a536", "metadata": {}, "outputs": [], "source": ["summary['diff'] =  (summary['total_22']- summary['total_21'])/summary['total_21']"]}, {"cell_type": "code", "execution_count": 19, "id": "c5690c8d-1591-4c61-bd4e-fd9c794ebc3f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>pmg</th>\n", "      <th>repl_type</th>\n", "      <th>total_21</th>\n", "      <th>pmg_code_name</th>\n", "      <th>dep</th>\n", "      <th>total_22</th>\n", "      <th>diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>full_pallet</td>\n", "      <td>751962.142864</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "      <td>966533.31250</td>\n", "      <td>0.285348</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>mu</td>\n", "      <td>0.000000</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "      <td>0.00000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>nsrp</td>\n", "      <td>438468.142901</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "      <td>311961.21875</td>\n", "      <td>-0.288520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>srp</td>\n", "      <td>535634.357103</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "      <td>648771.18750</td>\n", "      <td>0.211220</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CZ</td>\n", "      <td>BWS02</td>\n", "      <td>full_pallet</td>\n", "      <td>73269.714285</td>\n", "      <td>BWS02-Canned Beers</td>\n", "      <td>BWS</td>\n", "      <td>98449.00000</td>\n", "      <td>0.343652</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  country    pmg    repl_type       total_21        pmg_code_name  dep  \\\n", "0      CZ  BWS01  full_pallet  751962.142864  BWS01-Bottled Beers  BWS   \n", "1      CZ  BWS01           mu       0.000000  BWS01-Bottled Beers  BWS   \n", "2      CZ  BWS01         nsrp  438468.142901  BWS01-Bottled Beers  BWS   \n", "3      CZ  BWS01          srp  535634.357103  BWS01-Bottled Beers  BWS   \n", "4      CZ  BWS02  full_pallet   73269.714285   BWS02-Canned Beers  BWS   \n", "\n", "       total_22      diff  \n", "0  966533.31250  0.285348  \n", "1       0.00000  0.000000  \n", "2  311961.21875 -0.288520  \n", "3  648771.18750  0.211220  \n", "4   98449.00000  0.343652  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["summary['diff'] =  summary['diff'].replace(np.nan, 0).replace([np.inf, -np.inf], 0)\n", "summary.head()"]}, {"cell_type": "code", "execution_count": 26, "id": "87e088ea-ff24-4bdd-bc91-2ff411161837", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>pmg</th>\n", "      <th>repl_type</th>\n", "      <th>total_21</th>\n", "      <th>pmg_code_name</th>\n", "      <th>dep</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>full_pallet</td>\n", "      <td>751962.142864</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>mu</td>\n", "      <td>0.000000</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>nsrp</td>\n", "      <td>438468.142901</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>srp</td>\n", "      <td>535634.357103</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CZ</td>\n", "      <td>BWS02</td>\n", "      <td>full_pallet</td>\n", "      <td>73269.714285</td>\n", "      <td>BWS02-Canned Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  country    pmg    repl_type       total_21        pmg_code_name  dep\n", "0      CZ  BWS01  full_pallet  751962.142864  BWS01-Bottled Beers  BWS\n", "1      CZ  BWS01           mu       0.000000  BWS01-Bottled Beers  BWS\n", "2      CZ  BWS01         nsrp  438468.142901  BWS01-Bottled Beers  BWS\n", "3      CZ  BWS01          srp  535634.357103  BWS01-Bottled Beers  BWS\n", "4      CZ  BWS02  full_pallet   73269.714285   BWS02-Canned Beers  BWS"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["old_gr.head()"]}, {"cell_type": "code", "execution_count": 24, "id": "5dab3a3a-c7bd-4688-85a0-db9a09781830", "metadata": {}, "outputs": [], "source": ["summary.pivot_table(index=['country','pmg', 'pmg_code_name'], columns=['repl_type']).reset_index().to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_feb\\SRP_CE_weightedSales_of_products_pmg2.xlsx\")"]}, {"cell_type": "code", "execution_count": 21, "id": "70bb417b-435d-4391-986c-7bb3041a0884", "metadata": {}, "outputs": [], "source": ["summary.to_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_feb\\SRP_CE_weightedSales_of_products_pmg.xlsx\",index=False)"]}, {"cell_type": "code", "execution_count": 3, "id": "610ae60c-fb86-4d3a-a5ab-5765c7877b34", "metadata": {}, "outputs": [], "source": ["def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                            separator=None, auto_open=False):\n", "    with open(html_fname, 'w') as f:\n", "        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))\n", "        for fig in plotly_figs[1:]:\n", "            if separator:\n", "                f.write(separator)\n", "            f.write(fig.to_html(full_html=False, include_plotlyjs=False))\n", "\n", "    if auto_open:\n", "        import pathlib, webbrowser\n", "        uri = pathlib.Path(html_fname).absolute().as_uri()\n", "        webbrowser.open(uri)"]}, {"cell_type": "code", "execution_count": 30, "id": "55c59fb5-a7d2-4fd8-a9e6-38a65215d5fe", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>pmg</th>\n", "      <th>repl_type</th>\n", "      <th>total</th>\n", "      <th>pmg_code_name</th>\n", "      <th>dep</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>full_pallet</td>\n", "      <td>751962.142864</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>mu</td>\n", "      <td>0.000000</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>nsrp</td>\n", "      <td>438468.142901</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CZ</td>\n", "      <td>BWS01</td>\n", "      <td>srp</td>\n", "      <td>535634.357103</td>\n", "      <td>BWS01-Bottled Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CZ</td>\n", "      <td>BWS02</td>\n", "      <td>full_pallet</td>\n", "      <td>73269.714285</td>\n", "      <td>BWS02-Canned Beers</td>\n", "      <td>BWS</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  country    pmg    repl_type          total        pmg_code_name  dep\n", "0      CZ  BWS01  full_pallet  751962.142864  BWS01-Bottled Beers  BWS\n", "1      CZ  BWS01           mu       0.000000  BWS01-Bottled Beers  BWS\n", "2      CZ  BWS01         nsrp  438468.142901  BWS01-Bottled Beers  BWS\n", "3      CZ  BWS01          srp  535634.357103  BWS01-Bottled Beers  BWS\n", "4      CZ  BWS02  full_pallet   73269.714285   BWS02-Canned Beers  BWS"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["old_gr.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "e44692e9-dc66-4591-84b8-00ee25841cd1", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "\n", "cont = []\n", "for a,b in zip((old_gr.pmg.unique().tolist()), (old_gr.pmg_code_name.unique().tolist()) ):\n", "    \n", "\n", "    # Create subplots: use 'domain' type for Pie subplot\n", "    fig = make_subplots(rows=1, cols=6, specs=[[{'type':'domain'}, {'type':'domain'},{'type':'domain'}, {'type':'domain'},{'type':'domain'}, {'type':'domain'}]])\n", "    fig.add_trace(go.Pie(labels=old_gr[old_gr.country == 'HU']['repl_type'].tolist(), values=old_gr[(old_gr.pmg == a) &(old_gr.country == 'HU')]['total'].tolist(), name=\"HU\", textinfo='percent',marker_colors=px.colors.qualitative.T10),\n", "                  1, 1)\n", "    \n", "    fig.add_trace(go.Pie(labels=new2_gr[new2_gr.country == 'HU']['repl_type'].tolist(), values=new2_gr[(new2_gr.pmg == a)&(new2_gr.country == 'HU')]['total'].tolist(), name=\"HU\", textinfo='percent', marker_colors=px.colors.qualitative.T10 ),\n", "                  1, 2)\n", "    \n", "    fig.add_trace(go.Pie(labels=old_gr[old_gr.country == 'CZ']['repl_type'].tolist(), values=old_gr[(old_gr.pmg == a)&(old_gr.country == 'CZ')]['total'].tolist(), name=\"CZ\", textinfo='percent',marker_colors=px.colors.qualitative.T10),\n", "              1, 3)\n", "\n", "    fig.add_trace(go.Pie(labels=new2_gr[new2_gr.country == 'CZ']['repl_type'].tolist(), values=new2_gr[(new2_gr.pmg == a)&(new2_gr.country == 'CZ')]['total'].tolist(), name=\"CZ\", textinfo='percent', marker_colors=px.colors.qualitative.T10 ),\n", "              1, 4)\n", "    fig.add_trace(go.Pie(labels=old_gr[old_gr.country == 'SK']['repl_type'].tolist(), values=old_gr[(old_gr.pmg == a)&(old_gr.country == 'SK')]['total'].tolist(), name=\"SK\", textinfo='percent',marker_colors=px.colors.qualitative.T10),\n", "              1, 5)\n", "\n", "    fig.add_trace(go.Pie(labels=new2_gr[new2_gr.country == 'SK']['repl_type'].tolist(), values=new2_gr[(new2_gr.pmg == a)&(new2_gr.country == 'SK')]['total'].tolist(), name=\"SK\", textinfo='percent', marker_colors=px.colors.qualitative.T10 ),\n", "              1, 6)\n", "\n", "\n", "\n", "    fig.update_layout(\n", "        # title_text=f\"{a} (<b>GROCERY - number of products</b>) % Last Year Model vs. 23-24v1 (09.21) vs. 23-24v2 (01.19)\",\n", "        title_text=f\"<b>{b}</b>\",\n", "        height=400, width=1500,\n", "        template=\"none\",\n", "        uniformtext_mode='hide',\n", "        uniformtext_minsize=9,\n", "        # Add annotations in the center of the donut pies.\n", "        annotations=[dict(text=\"HU-21<br>w14-w27\", x=0.042, y=0.5, font_size=15, showarrow=False),\n", "                     dict(text=\"HU-22<br>w14-w27\", x=0.213, y=0.5, font_size=15, showarrow=False),\n", "                     dict(text=\"SK-21<br>w14-w27\", x=0.415, y=0.5, font_size=15, showarrow=False),\n", "                     dict(text=\"SK-22<br>w14-w27\", x=0.587, y=0.5, font_size=15, showarrow=False),\n", "                     dict(text=\"CZ-21<br>w14-w27\", x=0.786, y=0.5, font_size=15, showarrow=False),\n", "                     dict(text=\"CZ-22<br>w14-w27\", x=0.958, y=0.5, font_size=15, showarrow=False),\n", "                     ])\n", "    \n", "        # Use `hole` to create a donut-like pie chart\n", "    fig.update_traces(hole=.5, hoverinfo=\"label+percent+name\", textposition='inside')\n", "    # fig.show()\n", "    cont.append(fig)\n", "\n", "        \n", "html_fname = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_feb\\SRP_CE_PieCharts_weightedSales_of_products_pmg_.html\"\n", "plotly_figs = cont\n", "combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                                separator=None, auto_open=False)"]}, {"cell_type": "code", "execution_count": null, "id": "39d6882e-2314-4c90-bb02-ce669f4aacc4", "metadata": {"tags": []}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "for a in ['HU', 'CZ', 'SK']:\n", "    \n", "\n", "    # Create subplots: use 'domain' type for Pie subplot\n", "    fig = make_subplots(rows=2, cols=2, specs=[[{'type':'pie'}, {'type':'pie'}], [{ \"type\": \"table\" }, None]])\n", "    \n", "    fig.add_trace(go.Table(\n", "                        header=dict(values=list(df.columns),\n", "                                    fill_color='paleturquoise',\n", "                                    align='left'),\n", "                        cells=dict(values=df.transpose().values.tolist(),\n", "                                   fill_color='lavender',\n", "                                   align='left')),\n", "                                        row=1, col=1)\n", "    \n", "    \n", "    fig.add_trace(go.Pie(labels=old_gr[old_gr.country == a]['repl_type'].tolist(), values=old_gr[old_gr.country == a]['total'].tolist(), name=a, textinfo='label+percent'),\n", "                  2, 1).update_traces(hole=.4, hoverinfo=\"label+percent+name\")\n", "    fig.add_trace(go.Pie(labels=new_gr[new_gr.country == a]['repl_type'].tolist(), values=new_gr[new_gr.country == a]['total'].tolist(), name=a, textinfo='label+percent'),\n", "                  2, 2).update_traces(hole=.4, hoverinfo=\"label+percent+name\")\n", "    \n", "\n", "\n", "\n", "    fig.update_layout(\n", "        title_text=f\"{a} Replenishment Types % 2022 vs. 2023\",\n", "        height=400, width=1500,\n", "        template=\"none\",\n", "        # Add annotations in the center of the donut pies.\n", "        annotations=[dict(text=a, x=0.19, y=0.5, font_size=25, showarrow=False),\n", "                     dict(text=a, x=0.81, y=0.5, font_size=25, showarrow=False),\n", "                     ])\n", "    \n", "        # Use `hole` to create a donut-like pie chart\n", "    #fig.update_traces(hole=.4, hoverinfo=\"label+percent+name\")\n", "    fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "0e7ddfe6-b47b-4eb7-8051-92610ced5969", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\OPB_DEP_DIFF_foil_0%.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "3017abbc-1636-4626-8d9e-5a2c24a37504", "metadata": {}, "outputs": [], "source": ["df = df.groupby(['Country'])['Total Weekly Hours', 'diff_hours', 'Yearly GBP', 'diff_in_GBP'].sum().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "316e17fe-26d1-4b06-bd3b-29091880cfe7", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "b3efb27d-e671-4f5f-87eb-b3c46c923e18", "metadata": {}, "outputs": [], "source": ["as_is_hours = df['Total Weekly Hours'].values.tolist()\n", "to_be_hours=df['diff_hours'].values.tolist()\n", "as_is_hours = [int(item) for item in as_is_hours]\n", "to_be_hours = [int(item) for item in to_be_hours]"]}, {"cell_type": "code", "execution_count": null, "id": "2d12a948-8b24-45a9-86cc-92f7d30f6fe6", "metadata": {}, "outputs": [], "source": ["to_be_gbp"]}, {"cell_type": "code", "execution_count": null, "id": "26188817-440b-48cc-917b-b5277f2f9858", "metadata": {}, "outputs": [], "source": ["country=df['Country'].unique().tolist()\n", "as_is_gbp = df['Yearly GBP'].values.tolist()\n", "to_be_gbp=df['diff_in_GBP'].values.tolist()\n", "as_is_gbp = [int(item) for item in as_is_gbp]\n", "to_be_gbp = [int(item) for item in to_be_gbp]"]}, {"cell_type": "code", "execution_count": null, "id": "5b2993e0-2f8f-4011-9b2e-4a2df572e527", "metadata": {}, "outputs": [], "source": ["df['Country'].unique().tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "dc6752f7-d1a4-40f0-9e4f-7c003e8396ba", "metadata": {}, "outputs": [], "source": ["to_be_gbp"]}, {"cell_type": "code", "execution_count": null, "id": "7e1a61fe-0013-478e-8512-0bf44d638846", "metadata": {}, "outputs": [], "source": ["fig_effects = go.Figure()\n", "captures = [\"<b>CE Replenishment Total 'AS IS' Cost Base</b>\", 'Stores changes','volumes effects',\t\"New Model Engine\",\t'Profile update','Pre-Sort %', 'WGLL (free spaces + Season changing)',\n", "            'Facing-Rumble Activity Group', \"<b>Total without Warehouse part</b>\", 'WH replenishment Part','WH Profile update', '<b>Total V1</b>', 'Data duplication correction (WH & NEWS)',\n", "            'PET & Household extra hours for high pallet', 'WH SK ALU & PET data update', 'Product Tagging update', 'New Replenishment type: Split Pallet','<b>Total V2</b>']\n", "values=[68698018, 782949,-8245684,-69622, 123718,-127015, 508699,2936622,64607686,6763276, 23167, 71394128, -972106, 23024, 44963, 187901, -99206 ,70578703]\n", "text = ['68698018', \"+782949\",'-8245684','-69622', '+123718','-127015', '+508699','+2936622','64607686','+6763276','+23167', '71394128', '-972106', '23024', '44963', '187901', '-99206' ,'70578703']\n", "\n", "#season changes wgll 306479\n", "\n", "fig_effects.add_trace(go.Waterfall(\n", "               x = captures, y = values,\n", "               base = 0,\n", "               text = text, textposition = 'outside',   \n", "               measure = [\"absolute\",  \"relative\", \"relative\",\n", "                          \"relative\",\"relative\",\"relative\",\"relative\",\"relative\",\"absolute\",\"relative\",\"relative\", \"total\", \"relative\",\"relative\", 'relative', 'relative', 'relative' ,'total'],\n", "                connector = {\"line\":{\"color\":\"rgb(63, 63, 63)\"}},\n", "                texttemplate = '%{text:,}'\n", "               ))\n", "\n", "fig_effects.update_traces(textfont_size=15)\n", "fig_effects.update_layout(\n", "             height=800, width=1400, yaxis_range=[50000000,76000000]\n", "                   )\n", "fig_effects.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "629a49fb-6791-49b3-9b75-b4ce807001bf", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "\n", "\n", "\n", "fig2  = go.Figure()\n", "hrz = [ \"CZ\",  \"25% foil (cz)\", \"HU\",\"25% foil (hu)\",\"SK\",\"25% foil (sk)\"]\n", "vrt  = [25945232, -315390, 22308395, -412495, 22637051,-78873 ]\n", "text = ['', '-315,390', '', '-412,495', '', '-78,873']\n", "fig2.add_trace(go.Waterfall(\n", "               x = hrz, y = vrt,\n", "               base = 0,\n", "               text = text, textposition = 'auto',   \n", "               measure = [\"absolute\",  \"relative\", \"absolute\",\n", "                          \"relative\",\"absolute\",\"relative\"] \n", "               ))             \n", "fig2.update_layout(\n", "                   title_text = \"Scenario 3: 25% Foil\",\n", "                   title_font=dict(size=25,family='Verdana', \n", "                                   color='darkred'), height=600, width=800, yaxis_range=[20000000,26000000]\n", "                   )\n", "fig2.show()"]}, {"cell_type": "code", "execution_count": null, "id": "32b52e26-bb0e-49e1-bdac-0a21ed395a3b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0c1dbe2b-d4ba-4ff3-aadc-ac52da8709e5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "78b05b5d-af5e-4904-bb5b-13f85a697bb5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3a802455-dd76-4ec3-99cd-8d8d75a0163c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7baa248c-4767-405c-bd45-310466d4d1fd", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\OPB_DEP_DIFF_effect_of_facing.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "17e31a93-398c-4a7e-9ba7-560e446284b3", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "abd5dfb0-6a3c-4247-9e11-088c7b494268", "metadata": {}, "outputs": [], "source": ["df = df[df.Division.isin(['General Merchandise', 'Grocery'])].groupby(['Country'])['Total Weekly Hours', 'diff_hours', 'Yearly GBP', 'diff_in_GBP'].sum().reset_index()\n"]}, {"cell_type": "code", "execution_count": null, "id": "141d7679-2b1a-4dd9-a14f-03a12f63b37c", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "98223808-1247-4035-abd5-b0340e27546b", "metadata": {}, "outputs": [], "source": ["\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "f1635850-f24f-4431-8af9-9f8a3a56d9af", "metadata": {}, "outputs": [], "source": ["to_be"]}, {"cell_type": "code", "execution_count": null, "id": "420ae9af-5d20-4437-884b-746878b0f5dd", "metadata": {}, "outputs": [], "source": ["df['Total Weekly Hours'].values.tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "d05417c1-32a4-4d86-9b8d-d82a007002e1", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "country=df['Country'].unique().tolist()\n", "\n", "as_is_hours = df['Total Weekly Hours'].values.tolist()\n", "to_be_hours=df['diff_hours'].values.tolist()\n", "as_is_hours = [int(item) for item in as_is]\n", "to_be_hours = [int(item) for item in to_be]\n", "\n", "as_is_gbp = df['Yearly GBP'].values.tolist()\n", "to_be_gbp=df['diff_in_GBP'].values.tolist()\n", "as_is_gbp = [int(item) for item in as_is_gbp]\n", "to_be_gbp = [int(item) for item in to_be_gbp]\n", "\n", "\n", "\n", "\n", "fig = go.Figure(data=[\n", "    go.Bar(name='As is hours ', x=country, y=as_is, text=as_is),\n", "    go.Bar(name='extra Facing/Rumble hours', x=country, y=to_be, text=to_be)\n", "])\n", "# Change the bar mode\n", "fig.update_layout(barmode='stack', height=600, width=800,font=dict(\n", "        \n", "        size=18,\n", "        color=\"<PERSON><PERSON><PERSON><PERSON>\"\n", "    ))\n", "\n", "fig.update_traces(texttemplate='%{text:,}')\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "4f4b19e4-3723-4f82-a0cd-a853c36b2bbe", "metadata": {}, "outputs": [], "source": ["import waterfall_chart\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "\n", "a = ['mon','tue','wen','thu','fri','sat','sun']\n", "b = [10,-30,-7.5,-25,95,-7,45]\n", "waterfall_chart.plot(a, b, net_label='Total', rotation_value=360)"]}, {"cell_type": "code", "execution_count": null, "id": "947b1437-a3af-4f35-9b3b-d32605c59ddd", "metadata": {}, "outputs": [], "source": ["country=df['Country'].unique().tolist()\n", "as_is_gbp = df['Yearly GBP'].values.tolist()\n", "to_be_gbp=df['diff_in_GBP'].values.tolist()\n", "as_is_gbp = [int(item) for item in as_is_gbp]\n", "to_be_gbp = [int(item) for item in to_be_gbp]\n", "\n", "\n", "fig = go.Figure(data=[\n", "    go.Bar(name='As is GBP ', x=country, y=as_is_gbp, text=None),\n", "    go.Bar(name='extra Facing/<br>Rumble GBP', x=country, y=to_be_gbp, text=to_be_gbp, texttemplate='%{text:,.0f}')\n", "])\n", "# Change the bar mode\n", "fig.update_layout(barmode='stack', height=600, width=800,title = \"Yearly GBP Effect of Facing/Rumble Activity Group\",\n", "                  font=dict(\n", "        \n", "        size=14\n", "        \n", "    ))\n", "\n", "#fig.update_traces(texttemplate='%{text:,.0f}')  #'%{text:,}'\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "62afbadc-573c-4ec4-834b-3ba7c4eca798", "metadata": {}, "outputs": [], "source": ["np.stack((binnum_attr, tnl_attr, pop_attr), axis=-1)"]}, {"cell_type": "code", "execution_count": null, "id": "fe557b47-9795-434b-821b-1d3323e395d8", "metadata": {}, "outputs": [], "source": ["new = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl_Dataset_2022_new_14w_27w_promo_flag.parquet\")\n", "store_format = new[['store', 'Format']].drop_duplicates()\n", "\n", "for x in [ 'srp', 'nsrp', 'full_pallet', 'mu']:\n", "    new.loc[new[x] > 0, x] = new['sold_units']"]}, {"cell_type": "code", "execution_count": null, "id": "899cbd6b-7f1d-48ee-9cf3-022861189c26", "metadata": {}, "outputs": [], "source": ["new = new.groupby(['country', 'division'], observed=True)[ 'srp', 'nsrp', 'full_pallet', 'mu'].sum().reset_index()\n", "new = new.melt(id_vars=['country', 'division'], value_vars=['srp', 'nsrp', 'full_pallet', 'mu'], var_name='repl_type', value_name='total')\n", "new_gr = new.groupby(['country', 'division', 'repl_type'])['total'].sum().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "287dd1f7-d528-432a-9448-b913eab940b1", "metadata": {}, "outputs": [], "source": ["new['percent'] = new.total / new.groupby(['division', 'repl_type'])['total'].transform(\"sum\")"]}, {"cell_type": "code", "execution_count": null, "id": "75745545-8ed6-4c79-aa97-12a7c0d7b0c6", "metadata": {"tags": []}, "outputs": [], "source": ["perc = new.groupby(['division', 'repl_type']).sum().reset_index()\n", "perc['percent'] = perc.total / perc.groupby(['repl_type'])['total'].transform(\"sum\") * 100"]}, {"cell_type": "code", "execution_count": null, "id": "dbf60381-41d0-41e3-af8b-07e486fcb459", "metadata": {}, "outputs": [], "source": ["perc"]}, {"cell_type": "code", "execution_count": null, "id": "044e25a4-85c7-46f1-8db7-282d1f148c7c", "metadata": {}, "outputs": [], "source": ["fig2 = go.Figure(go.Bar(x=[13.428147],orientation='h', y=[''], text=[13.428147],textposition='auto', customdata=custom_data, legendgroup=\"full_pallet\", name='full_pallet', marker_color='rgb(128,128,128)', marker_opacity=1.0))\n", "fig2.add_trace(go.Bar(x=[43.801029],orientation='h' ,y=[''],text=[43.801029], customdata=custom_data, legendgroup=\"mu\", name='mu', marker_color='rgb(230,25,75)', marker_opacity=1.0))\n", "fig2.add_trace(go.Bar(x=[14.901077],orientation='h' ,y=[''], customdata=custom_data, legendgroup=\"nsrp\", name='nsrp', marker_color='rgb(60,180,75)', marker_opacity=1.0))\n", "fig2.add_trace(go.Bar(x=[31.189325],orientation='h', y=[''], customdata=custom_data, legendgroup=\"srp\",  name='srp', marker_color='rgb(0,130,200)', marker_opacity=1.0))\n", "fig2.update_layout(barmode='relative')\n", "fig2.update_layout(xaxis=dict(title='<b>Replenishment Types % (%)</b>', titlefont_size=20, tickfont_size=16))\n", "fig2.update_layout(legend=dict(font=dict(size=14),borderwidth=1))\n", "fig2.show()"]}, {"cell_type": "code", "execution_count": null, "id": "30e9d29c-31e5-43de-83f8-4e61eb2ae869", "metadata": {}, "outputs": [], "source": ["a = [2]\n", "b= [1]\n", "c= [3]\n", "\n", "\n", "custom_data= np.stack((a, b, c), axis=-1)"]}, {"cell_type": "code", "execution_count": null, "id": "e15fbaf8-9063-4168-bba2-d486c4f0de16", "metadata": {}, "outputs": [], "source": ["import plotly\n", "import plotly.graph_objects as go\n", "import numpy as np\n", "binnum_attr=[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]\n", "tnl_attr=[164549.654, 278570.473, 201857.2155, 146326.9935, 116167.091, 98121.201, 86260.078, 79611.275, 346547.786, 573329.789, 345360.231, 513364.5035, 287341.8995, 472867.1225, 143591.5138]\n", "pop_attr=[296699, 195322, 82853, 42382, 26001, 17926, 13310, 10637, 33974, 32444, 12302, 12449, 4635, 4781, 785]\n", "verbose_customdata = np.stack((binnum_attr, tnl_attr, pop_attr), axis=-1)\n", "\n", "fig = go.Figure(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[3.618, 1.526, 0.899, 0.674, 0.587, 0.566, 0.566, 0.622, 0.644, 0.392, 0.185, 0.094, 0.056, 0.029, 0.060], customdata=verbose_customdata, legendgroup=\"M0\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M0 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M0', marker_color='rgb(128,128,128)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[36.797, 21.312, 12.348, 9.136, 8.275, 8.028, 8.307, 8.749, 9.195, 5.667, 3.191, 1.561, 0.890, 0.466, 0.377], customdata=verbose_customdata, legendgroup=\"M1\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M1 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M1', marker_color='rgb(230,25,75)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[45.265, 40.620, 30.699, 23.548, 20.190, 18.252, 17.691, 18.222, 17.714, 12.791, 7.958, 4.590, 2.436, 1.391, 2.040], customdata=verbose_customdata, legendgroup=\"M2\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M2 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M2', marker_color='rgb(60,180,75)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[12.552, 28.142, 34.866, 33.760, 30.362, 27.477, 24.941, 23.311, 20.421, 16.449, 11.854, 7.587, 4.617, 2.679, 3.101], customdata=verbose_customdata, legendgroup=\"M3\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M3 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M3', marker_color='rgb(0,130,200)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[1.572, 7.198, 17.117, 24.482, 27.654, 28.867, 28.483, 27.705, 25.384, 22.417, 17.922, 14.306, 10.080, 7.284, 7.308], customdata=verbose_customdata, legendgroup=\"M4\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M4 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M4', marker_color='rgb(245,130,48)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.177, 1.028, 3.369, 7.020, 10.741, 13.425, 15.438, 16.022, 16.681, 17.102, 14.868, 11.920, 8.350, 4.642, 3.424], customdata=verbose_customdata, legendgroup=\"M5\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M5 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M5', marker_color='rgb(145,30,180)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.014, 0.147, 0.539, 0.898, 1.445, 2.123, 2.673, 2.974, 4.886, 7.980, 9.202, 8.248, 6.609, 4.891, 4.884], customdata=verbose_customdata, legendgroup=\"M6\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M6 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M6', marker_color='rgb(70,240,240)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.003, 0.012, 0.120, 0.309, 0.460, 0.801, 1.243, 1.570, 3.151, 7.628, 10.602, 10.732, 7.845, 4.673, 2.921], customdata=verbose_customdata, legendgroup=\"M7\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M7 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M7', marker_color='rgb(0,0,128)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.000, 0.001, 0.018, 0.075, 0.142, 0.279, 0.341, 0.416, 1.054, 4.200, 7.894, 10.699, 11.194, 9.985, 8.756], customdata=verbose_customdata, legendgroup=\"M8\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M8 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M8', marker_color='rgb(210,245,80)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.003, 0.010, 0.019, 0.043, 0.086, 0.103, 0.191, 0.265, 0.409, 2.451, 5.941, 8.565, 9.452, 7.728, 4.578], customdata=verbose_customdata, legendgroup=\"M9\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M9 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M9', marker_color='rgb(0,128,128)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.000, 0.000, 0.002, 0.017, 0.033, 0.051, 0.050, 0.085, 0.274, 1.564, 4.395, 8.221, 11.563, 14.457, 13.762], customdata=verbose_customdata, legendgroup=\"M10\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M10 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M10', marker_color='rgb(220,190,255)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.000, 0.003, 0.004, 0.028, 0.019, 0.022, 0.049, 0.030, 0.095, 0.514, 2.237, 4.737, 8.285, 9.802, 6.008], customdata=verbose_customdata, legendgroup=\"M11\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M11 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M11', marker_color='rgb(170,255,195)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.000, 0.000, 0.000, 0.002, 0.001, 0.001, 0.005, 0.025, 0.083, 0.695, 2.848, 6.794, 13.261, 22.648, 20.153], customdata=verbose_customdata, legendgroup=\"M12\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M12 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M12', marker_color='rgb(255,215,180)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.000, 0.000, 0.000, 0.003, 0.003, 0.005, 0.024, 0.004, 0.008, 0.093, 0.582, 1.165, 3.187, 4.934, 7.068], customdata=verbose_customdata, legendgroup=\"M13\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M13 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M13', marker_color='rgb(0,0,0)', marker_opacity=1.0))\n", "fig.add_trace(go.Bar(x=['0~1', '1~2', '2~3', '3~4', '4~5', '5~6', '6~7', '7~8', '8~13', '13~24', '24~33', '33~53', '53~73', '73~158', '158~1644'], y=[0.000, 0.000, 0.000, 0.004, 0.000, 0.000, 0.000, 0.000, 0.000, 0.056, 0.321, 0.781, 2.176, 4.394, 15.559], customdata=verbose_customdata, legendgroup=\"M14\", hovertemplate='<b>Bin: %{customdata[0]: .0f}</b><br>'+'<b>M14 portion: %{y:.2f}%</b><br>'+'<b>Total length (all layers): %{customdata[1]:.2f}</b><br>'+'<b>Nets: %{customdata[2]: .0f}</b><br>', name='M14', marker_color='rgb(128,0,0)', marker_opacity=1.0))\n", "fig.update_layout(barmode='relative')\n", "fig.update_layout(title_text='<b>Metal Layer Usage by Length: block node</b>', title_font_size=28, yaxis=dict(title='<b>Metal Layer Usage (%)</b>', titlefont_size=20, tickfont_size=16), xaxis=dict(title='<b>Net Length Range (um)</b>', titlefont_size=20, tickfont_size=16))\n", "fig.update_layout(legend=dict(font=dict(size=14),borderwidth=1))\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "1cb106a5-be51-4551-a3cc-8b44822843cc", "metadata": {}, "outputs": [], "source": ["s=200\n", "pd.DataFrame({\"Country\": np.random.choice([\"USA America\", \"JPY one two\", \"MEX\", \"IND\", \n", "  \"AUS\"], s),\n", "   \"economy_cat\": np.random.choice([\"developing\",\"develop\"], s),\n", "   \"gdp\": np.random.randint(5, 75, s),\n", "   })"]}, {"cell_type": "code", "execution_count": null, "id": "b47271d2-12c7-496e-94f1-92b2fa5a3b79", "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "import pandas as pd\n", "import numpy as np\n", "from plotly.subplots import make_subplots\n", "import plotly.graph_objects as go\n", "\n", "df = pd.DataFrame(\n", "    [\n", "        dict(Task=\"Job A\", Start=\"2009-01-01\", Finish=\"2009-02-28\", Resource=\"Alex\"),\n", "        dict(Task=\"Job B\", Start=\"2009-03-05\", Finish=\"2009-04-15\", Resource=\"Alex\"),\n", "        dict(Task=\"Job C\", Start=\"2009-02-20\", Finish=\"2009-05-30\", Resource=\"Max\"),\n", "    ]\n", ")\n", "df[\"Start\"] = pd.to_datetime(df[\"Start\"])\n", "df[\"Finish\"] = pd.to_datetime(df[\"Finish\"])\n", "\n", "\n", "fig2 = px.timeline(df, x_start=\"Start\", x_end=\"Finish\", y=\"Task\", color=\"Resource\")\n", "\n", "df2 = pd.DataFrame(\n", "    {\n", "        \"Date\": pd.date_range(\n", "            df.loc[:, [\"Start\", \"Finish\"]].values.min(),\n", "            df.loc[:, [\"Start\", \"Finish\"]].values.max(),\n", "            freq=\"W-MON\",\n", "        )\n", "    }\n", ").pipe(lambda d: d.assign(Value=np.random.randint(1, 20, len(d))))\n", "\n", "df3 = pd.DataFrame(\n", "    {\n", "        \"Date\": pd.date_range(\n", "            df.loc[:, [\"Start\", \"Finish\"]].values.min(),\n", "            df.loc[:, [\"Start\", \"Finish\"]].values.max(),\n", "            freq=\"W-MON\",\n", "        )\n", "    }\n", ").pipe(lambda d: d.assign(Value=np.random.randint(1, 20, len(d))))\n", "trace1 = go.<PERSON><PERSON>(x=df2.Date, y=df2.Value)\n", "trace2 = go.<PERSON><PERSON>(x=df3.Date, y=df3.Value)\n", "\n", "\n", "fig = make_subplots(rows=3, cols=1, figure=fig2, shared_xaxes=True)\n", "fig.add_trace(trace1, row=2, col=1)\n", "fig.add_trace(trace2, row=3, col=1)\n", "fig.update_layout(xaxis1_showticklabels=True, xaxis2_showticklabels=True, xaxis3_showticklabels=True)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "daf11543-d0b1-438d-a4b3-488a18a32c40", "metadata": {}, "outputs": [], "source": ["insight = pd.read_parquet(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\INSIGHT_repl_new_profiles.parquet.gz\")\n", "\n", "insight_gr = insight.groupby(['Division','Activity_Group'], observed=True)['Yearly GBP'].sum().reset_index().sort_values(by=['Division', 'Yearly GBP'], ascending=[True, False])\n", "\n", "top_4_act_group = pd.DataFrame()\n", "\n", "for x in sorted(set(insight_gr.Division)):\n", "    top_4_act_group = pd.concat([top_4_act_group, insight_gr[insight_gr.Division == x][:4]])"]}, {"cell_type": "code", "execution_count": null, "id": "9dfdf614-fab7-4b8b-82e2-c87591bcb266", "metadata": {}, "outputs": [], "source": ["perc.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bb517b93-bf64-420b-93d0-ed162fc3a3a9", "metadata": {}, "outputs": [], "source": ["\n", "    \n", "cont = []\n", "    \n", "for div in top_4_act_group.Division.unique():\n", "    \n", "    top_4_act_groups = top_4_act_group[top_4_act_group.Division == div]\n", "    \n", "    \n", "    fig = make_subplots(\n", "    rows=4, cols=2,\n", "    shared_xaxes=False,\n", "    shared_yaxes=False,\n", "    vertical_spacing=0.1,\n", "    column_widths=[0.4, 0.6],\n", "    row_heights=[0.1, 0.1, 0.1, 0.7],\n", "    #vertical_spacing = [0.8],\n", "    specs=[[None,{\"type\": \"bar\",\"rowspan\": 2}],\n", "          [{\"type\": \"table\",\"rowspan\": 3}, None],\n", "          [None, None],\n", "          [None, {\"type\":\"pie\"}]])\n", "    \n", "    \n", "    fig.add_trace(\n", "        go.Pie(\n", "            labels= perc[perc.division == div]['repl_type'].tolist(), values=perc[perc.division == div]['total'].tolist(), name='',\n", "            textinfo='label+percent', insidetextorientation='radial', textfont=dict(color='#000000'), marker_colors=px.colors.sequential.deep, hole=.4\n", "            ),\n", "                  4, 2)\n", "\n", "    fig.add_trace(\n", "        go.Bar(\n", "                x=[int(x) for x in top_4_act_groups.sort_values(by='Yearly GBP')['Yearly GBP']],\n", "                y=[f'<b>{x}</b>' for x in top_4_act_groups.sort_values(by='Yearly GBP')['Activity_Group']],\n", "                orientation='h',\n", "                name=\"Top Activities' Cost Base\",\n", "                marker=dict(color=[int(x) for x in top_4_act_groups.sort_values(by='Yearly GBP')['Yearly GBP']],\n", "\n", "        colorscale=\"Blugrn\"\n", "    )),\n", "        row=1, col=2\n", "    )\n", "\n", "\n", "\n", "    fig.add_trace(\n", "        go.Table(columnwidth = [8,15,20],\n", "            header=dict(\n", "                values=[f'<b>{x}</b>' for x in top_4_act_groups.columns],\n", "                font=dict(color='white', size=16),\n", "                fill_color='darkcyan',\n", "                align=\"center\"\n", "            ),\n", "            cells=dict(\n", "                values=top_4_act_groups.values.T,\n", "                align=\"center\", format=[\"\",\"\", \",.0f\"], fill_color='mediumaquamarine', font=dict(color='white', size=14),\n", "                height = 40\n", "            )\n", "        ),\n", "        row=2, col=1\n", "    )\n", "    fig.update_layout(\n", "        legend=dict(x=1, y=1, font_size=10),\n", "        paper_bgcolor='rgb(248, 248, 240)',\n", "        plot_bgcolor=\"rgb(171,217,233)\",\n", "    )\n", "\n", "    annotations = []\n", "\n", "    cost_base = [int(x) for x in top_4_act_groups.sort_values(by='Yearly GBP')['Yearly GBP']]\n", "    act_group = [f'<b>{x}</b>' for x in top_4_act_groups.sort_values(by='Yearly GBP')['Activity_Group']]\n", "\n", "    # Adding labels\n", "    for yd, xd in zip(cost_base, act_group):\n", "        # labeling the scatter savings\n", "        annotations.append(dict(xref='x1', yref='y1',\n", "                                y=xd, x=yd + 200000,\n", "                                text='{:,}'.format(yd) + '',\n", "                                font=dict(family='Arial', size=14,\n", "                                          color=\"white\"),\n", "                                showarrow=False))\n", "        \n", "        \n", "\n", "    fig.update_layout(annotations=annotations,\n", "                      title={'text': f\"Division: <b>{div}</b>\"}, title_font_size=30, height= 600, showlegend=False, #template='ggplot2'\n", "                     )\n", "\n", "    #cont.append(fig)\n", "    fig.show()\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "3d17be49-a9b2-45f3-b331-0c59e1c10986", "metadata": {}, "outputs": [], "source": ["def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                                separator=None, auto_open=False):\n", "    with open(html_fname, 'w') as f:\n", "        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))\n", "        for fig in plotly_figs[1:]:\n", "            if separator:\n", "                f.write(separator)\n", "            f.write(fig.to_html(full_html=False, include_plotlyjs=False))\n", "\n", "    if auto_open:\n", "        import pathlib, webbrowser\n", "        uri = pathlib.Path(html_fname).absolute().as_uri()\n", "        webbrowser.open(uri)"]}, {"cell_type": "code", "execution_count": null, "id": "f9092a7e-0617-4ab5-a832-7aabf3d458a0", "metadata": {}, "outputs": [], "source": ["html_fname = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\py\\ad_hoc_py\\CE_summary.html\"\n", "plotly_figs = cont\n", "combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                                separator=None, auto_open=False)"]}, {"cell_type": "code", "execution_count": null, "id": "be08b310-2f6b-45c3-b735-860f0a665037", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e6dd91b0-8c96-4d7c-8b4a-f9155998d882", "metadata": {"tags": []}, "outputs": [], "source": ["fig =  ff.create_table(a)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "284f37d1-40ad-4f43-afcf-8f977c91f192", "metadata": {}, "outputs": [], "source": ["proba"]}, {"cell_type": "code", "execution_count": null, "id": "b488364f-2069-4389-be9d-81cdb41351c7", "metadata": {}, "outputs": [], "source": ["[int(x) for x in proba.sort_values(by='Yearly GBP')['Yearly GBP']]"]}, {"cell_type": "code", "execution_count": null, "id": "7fac1d62-fa9f-4165-b7a7-06f545f29dcf", "metadata": {}, "outputs": [], "source": ["range(len(x))"]}, {"cell_type": "code", "execution_count": null, "id": "e37f06cf-b797-473a-ada6-d34e70592851", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "import pandas as pd\n", "\n", "\n", "\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    shared_xaxes=True,\n", "    shared_yaxes=False,\n", "    vertical_spacing=0.1,\n", "    column_widths=[0.4, 0.6],\n", "    specs=[[{\"type\": \"table\",\"rowspan\": 2},{\"type\": \"bar\" }],\n", "          [None, None]]\n", "    \n", ")\n", "\n", "fig.add_trace(\n", "    go.Bar(\n", "            x=[int(x) for x in proba.sort_values(by='Yearly GBP')['Yearly GBP']],\n", "            y=[f'<b>{x}</b>' for x in proba.sort_values(by='Yearly GBP')['Activity_Group']],\n", "            orientation='h',\n", "            name=\"Top Activities' Cost Base\",\n", "            marker=dict(\n", "                        color='rgba(50, 171, 96, 0.6)',\n", "            line=dict(\n", "                    color='rgba(50, 171, 96, 1.0)',\n", "            width=1))\n", "        \n", "    ),\n", "    row=1, col=2\n", ")\n", "\n", "\n", "fig.add_trace(\n", "    go.Table(columnwidth = [8,15,20],\n", "        header=dict(\n", "            values=[f'<b>{x}</b>' for x in proba.columns],\n", "            font=dict(color='white', size=12),\n", "            fill_color='darkcyan',\n", "            align=\"center\"\n", "        ),\n", "        cells=dict(\n", "            values=proba.values.T,\n", "            align=\"center\", format=[\"\",\"\", \",.0f\"], fill_color='mediumaquamarine',\n", "        )\n", "    ),\n", "    row=1, col=1\n", ")\n", "fig.update_layout(xaxis2=dict(\n", "        zeroline=False,\n", "        showline=False,\n", "        showticklabels=True,\n", "        showgrid=True,\n", "        domain=[0.47, 1],\n", "        side='top',\n", "        dtick=25000),\n", "\n", "    legend=dict(x=0, y=1, font_size=10),\n", "    paper_bgcolor='rgb(248, 248, 255)',\n", "    plot_bgcolor='rgb(248, 248, 255)',\n", ")\n", "\n", "\n", "annotations = []\n", "\n", "cost_base = [int(x) for x in proba.sort_values(by='Yearly GBP')['Yearly GBP']]\n", "act_group = [f'<b>{x}</b>' for x in proba.sort_values(by='Yearly GBP')['Activity_Group']]\n", "\n", "# Adding labels\n", "for yd, xd in zip(cost_base, act_group):\n", "    # labeling the scatter savings\n", "    annotations.append(dict(xref='x1', yref='y1',\n", "                            y=xd, x=yd + 300000,\n", "                            text='{:,}'.format(yd) + '',\n", "                            font=dict(family='Arial', size=14,\n", "                                      ),\n", "                            showarrow=False))\n", "    \n", "fig.update_layout(annotations=annotations)\n", "\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "f6653636-2744-463c-b00c-15bec9b67a9d", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "import numpy as np\n", "\n", "y_saving = [1.3586, 2.2623000000000002, 4.9821999999999997, 6.5096999999999996,\n", "            7.4812000000000003, 7.5133000000000001, 15.2148, 17.520499999999998\n", "            ]\n", "y_net_worth = [93453.919999999998, 81666.570000000007, 69889.619999999995,\n", "               78381.529999999999, 141395.29999999999, 92969.020000000004,\n", "               66090.179999999993, 122379.3]\n", "x = ['Japan', 'United Kingdom', 'Canada', 'Netherlands',\n", "     'United States', 'Belgium', 'Sweden', 'Switzerland']\n", "\n", "\n", "# Creating two subplots\n", "fig = make_subplots(rows=1, cols=2, specs=[[{}, {}]], shared_xaxes=True,\n", "                    shared_yaxes=False, vertical_spacing=0.001)\n", "\n", "fig.append_trace(go.Bar(\n", "    x=y_saving,\n", "    y=x,\n", "    marker=dict(\n", "        color='rgba(50, 171, 96, 0.6)',\n", "        line=dict(\n", "            color='rgba(50, 171, 96, 1.0)',\n", "            width=1),\n", "    ),\n", "    name='Household savings, percentage of household disposable income',\n", "    orientation='h',\n", "), 1, 1)\n", "\n", "fig.append_trace(go.<PERSON>(\n", "    x=y_net_worth, y=x,\n", "    mode='lines+markers',\n", "    line_color='rgb(128, 0, 128)',\n", "    name='Household net worth, Million USD/capita',\n", "), 1, 2)\n", "\n", "fig.update_layout(\n", "    title='Household savings & net worth for eight OECD countries',\n", "    yaxis=dict(\n", "        showgrid=False,\n", "        showline=False,\n", "        showticklabels=True,\n", "        domain=[0, 0.85],\n", "    ),\n", "    yaxis2=dict(\n", "        showgrid=False,\n", "        showline=True,\n", "        showticklabels=False,\n", "        linecolor='rgba(102, 102, 102, 0.8)',\n", "        linewidth=2,\n", "        domain=[0, 0.85],\n", "    ),\n", "    xaxis=dict(\n", "        zeroline=False,\n", "        showline=False,\n", "        showticklabels=True,\n", "        showgrid=True,\n", "        domain=[0, 0.42],\n", "    ),\n", "    xaxis2=dict(\n", "        zeroline=False,\n", "        showline=False,\n", "        showticklabels=True,\n", "        showgrid=True,\n", "        domain=[0.47, 1],\n", "        side='top',\n", "        dtick=25000,\n", "    ),\n", "    legend=dict(x=0.029, y=1.038, font_size=10),\n", "    margin=dict(l=100, r=20, t=70, b=70),\n", "    paper_bgcolor='rgb(248, 248, 255)',\n", "    plot_bgcolor='rgb(248, 248, 255)',\n", ")\n", "\n", "annotations = []\n", "\n", "y_s = np.round(y_saving, decimals=2)\n", "y_nw = np.rint(y_net_worth)\n", "\n", "# Adding labels\n", "for ydn, yd, xd in zip(y_nw, y_s, x):\n", "    # labeling the scatter savings\n", "    annotations.append(dict(xref='x2', yref='y2',\n", "                            y=xd, x=ydn ,\n", "                            text='{:,}'.format(ydn) + 'M',\n", "                            font=dict(family='Arial', size=12,\n", "                                      color='rgb(128, 0, 128)'),\n", "                            showarrow=False))\n", "    # labeling the bar net worth\n", "    annotations.append(dict(xref='x1', yref='y1',\n", "                            y=xd, x=yd + 3,\n", "                            text=str(yd) + '%',\n", "                            font=dict(family='Arial', size=12,\n", "                                      color='rgb(50, 171, 96)'),\n", "                            showarrow=False))\n", "# Source\n", "annotations.append(dict(xref='paper', yref='paper',\n", "                        x=-0.2, y=-0.109,\n", "                        text='OECD \"' +\n", "                             '(2015), Household savings (indicator), ' +\n", "                             'Household net worth (indicator). doi: ' +\n", "                             '10.1787/cfc6f499-en (Accessed on 05 June 2015)',\n", "                        font=dict(family='Arial', size=10, color='rgb(150,150,150)'),\n", "                        showarrow=False))\n", "\n", "fig.update_layout(annotations=annotations)\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "5517a34b-0349-4d20-8e40-5b53d45eb62f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8324cf00-527c-4395-930d-93aec8a6367d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "120026f3-34f1-4013-86c9-527286a05054", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e3d698d1-b9c3-4aac-a388-2581e949ae33", "metadata": {"tags": []}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "\n", "fig = make_subplots(rows=1, cols=2, specs=[[{}, None]], shared_xaxes=False,\n", "                    shared_yaxes=False, vertical_spacing=0.001)\n", "\n", "fig.append_trace(go.Table(columnorder = [1,2,3],\n", "  columnwidth = [10,10,20],\n", "    header=dict(values=[f'<b>{x}</b>' for x in proba.columns],\n", "               font=dict(color='white', size=12),\n", "    height=40, fill_color='darkcyan'),\n", "    cells=dict(values=proba.values.T,\n", "    align=\"center\", format=[\"\",\"\", \",.0f\"], fill_color='mediumaquamarine'))\n", ",row=1, col=1)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "72932c74-a1c9-49ae-82b7-8e8844def92f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "67d6896a-1d06-4764-9d0c-b04308c24af7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "433567a2-1316-4a67-871e-719cf8931b0e", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "import plotly.figure_factory as ff\n", "\n", "table_data = [['Team', 'Wins', 'Losses', 'Ties'],\n", "              ['Montréal<br>Canadiens', 18, 4, 0],\n", "              ['Dallas Stars', 18, 5, 0],\n", "              ['NY Rangers', 16, 5, 0],\n", "              ['Boston<br>Bruins', 13, 8, 0],\n", "              ['Chicago<br>Blackhawks', 13, 8, 0],\n", "              ['LA Kings', 13, 8, 0],\n", "              ['Ottawa<br>Senators', 12, 5, 0]]\n", "\n", "fig = ff.create_table(table_data, height_constant=60)\n", "\n", "teams = ['Montréal Canadiens', 'Dallas Stars', 'NY Rangers',\n", "         'Boston Bruins', 'Chicago Blackhawks', 'LA Kings', 'Ottawa Senators']\n", "GFPG = [3.54, 3.48, 3.0, 3.27, 2.83, 2.45, 3.18]\n", "GAPG = [2.17, 2.57, 2.0, 2.91, 2.57, 2.14, 2.77]\n", "\n", "trace1 = go.<PERSON><PERSON><PERSON>(x=teams, y=GFPG,\n", "                    marker=dict(color='#0099ff'),\n", "                    name='Goals For<br>Per Game',\n", "                    xaxis='x2', yaxis='y2')\n", "trace2 = go.<PERSON><PERSON><PERSON>(x=teams, y=GAPG,\n", "                    marker=dict(color='#404040'),\n", "                    name='Goals Against<br>Per Game',\n", "                    xaxis='x2', yaxis='y2')\n", "\n", "fig.add_traces([trace1, trace2])\n", "\n", "# initialize xaxis2 and yaxis2\n", "fig['layout']['xaxis2'] = {}\n", "fig['layout']['yaxis2'] = {}\n", "\n", "# Edit layout for subplots\n", "fig.layout.xaxis.update({'domain': [0, .42]})\n", "fig.layout.xaxis2.update({'domain': [.5, 1.]})\n", "\n", "# The graph's yaxis MUST BE anchored to the graph's xaxis\n", "fig.layout.yaxis2.update({'anchor': 'x2'})\n", "fig.layout.yaxis2.update({'title': 'Goals'})\n", "\n", "# Update the margins to add a title and see graph x-labels.\n", "fig.layout.margin.update({'t':60, 'b':100})\n", "fig.layout.update({'title': '2016 Hockey Stats'}, width=900, template=\"plotly_dark\")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "12ec00df-de41-43da-bdea-3a892039dc00", "metadata": {}, "outputs": [], "source": ["import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "import numpy as np\n", "\n", "y_saving = [1.3586, 2.2623000000000002, 4.9821999999999997, 6.5096999999999996,\n", "            7.4812000000000003, 7.5133000000000001, 15.2148, 17.520499999999998\n", "            ]\n", "y_net_worth = [93453.919999999998, 81666.570000000007, 69889.619999999995,\n", "               78381.529999999999, 141395.29999999999, 92969.020000000004,\n", "               66090.179999999993, 122379.3]\n", "x = ['Japan', 'United Kingdom', 'Canada', 'Netherlands',\n", "     'United States', 'Belgium', 'Sweden', 'Switzerland']\n", "\n", "\n", "# Creating two subplots\n", "fig = make_subplots(rows=1, cols=2, specs=[[{}, {}]], shared_xaxes=True,\n", "                    shared_yaxes=False, vertical_spacing=0.001)\n", "\n", "fig.append_trace(go.Bar(\n", "    x=y_saving,\n", "    y=x,\n", "    marker=dict(\n", "        color='rgba(50, 171, 96, 0.6)',\n", "        line=dict(\n", "            color='rgba(50, 171, 96, 1.0)',\n", "            width=1),\n", "    ),\n", "    name='Household savings, percentage of household disposable income',\n", "    orientation='h',\n", "), 1, 1)\n", "\n", "fig.append_trace(go.<PERSON>(\n", "    x=y_net_worth, y=x,\n", "    mode='lines+markers',\n", "    line_color='rgb(128, 0, 128)',\n", "    name='Household net worth, Million USD/capita',\n", "), 1, 2)\n", "\n", "fig.update_layout(\n", "    title='Household savings & net worth for eight OECD countries',\n", "    yaxis=dict(\n", "        showgrid=False,\n", "        showline=False,\n", "        showticklabels=True,\n", "        domain=[0, 0.85],\n", "    ),\n", "    yaxis2=dict(\n", "        showgrid=False,\n", "        showline=True,\n", "        showticklabels=False,\n", "        linecolor='rgba(102, 102, 102, 0.8)',\n", "        linewidth=2,\n", "        domain=[0, 0.85],\n", "    ),\n", "    xaxis=dict(\n", "        zeroline=False,\n", "        showline=False,\n", "        showticklabels=True,\n", "        showgrid=True,\n", "        domain=[0, 0.42],\n", "    ),\n", "    xaxis2=dict(\n", "        zeroline=False,\n", "        showline=False,\n", "        showticklabels=True,\n", "        showgrid=True,\n", "        domain=[0.47, 1],\n", "        side='top',\n", "        dtick=25000,\n", "    ),\n", "    legend=dict(x=0.029, y=1.038, font_size=10),\n", "    margin=dict(l=100, r=20, t=70, b=70),\n", "    paper_bgcolor='rgb(248, 248, 255)',\n", "    plot_bgcolor='rgb(248, 248, 255)',\n", ")\n", "\n", "annotations = []\n", "\n", "y_s = np.round(y_saving, decimals=2)\n", "y_nw = np.rint(y_net_worth)\n", "\n", "# Adding labels\n", "for ydn, yd, xd in zip(y_nw, y_s, x):\n", "    # labeling the scatter savings\n", "    annotations.append(dict(xref='x2', yref='y2',\n", "                            y=xd, x=ydn - 20000,\n", "                            text='{:,}'.format(ydn) + 'M',\n", "                            font=dict(family='Arial', size=12,\n", "                                      color='rgb(128, 0, 128)'),\n", "                            showarrow=False))\n", "    # labeling the bar net worth\n", "    annotations.append(dict(xref='x1', yref='y1',\n", "                            y=xd, x=yd + 3,\n", "                            text=str(yd) + '%',\n", "                            font=dict(family='Arial', size=12,\n", "                                      color='rgb(50, 171, 96)'),\n", "                            showarrow=False))\n", "# Source\n", "annotations.append(dict(xref='paper', yref='paper',\n", "                        x=-0.2, y=-0.109,\n", "                        text='OECD \"' +\n", "                             '(2015), Household savings (indicator), ' +\n", "                             'Household net worth (indicator). doi: ' +\n", "                             '10.1787/cfc6f499-en (Accessed on 05 June 2015)',\n", "                        font=dict(family='Arial', size=10, color='rgb(150,150,150)'),\n", "                        showarrow=False))\n", "\n", "fig.update_layout(annotations=annotations)\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "5565f76f-96d6-4bdd-b71c-3a6031a2535d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import pyarrow.parquet as pq\n", "import plotly.express as px\n", "import warnings\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": null, "id": "e2b47551-e16f-427e-a0f0-2dc47812372b", "metadata": {}, "outputs": [], "source": ["shelf_trolley_df = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\outputs\\OPB_Dep_23_Q1_vol2_product_tagging_update_w40-45.xlsx\", \"Drivers\") "]}, {"cell_type": "code", "execution_count": null, "id": "739c4813-e5e0-425c-a7ae-cb385b197123", "metadata": {}, "outputs": [], "source": ["shelf_trolley_df['Backstock Pallets/Rollcage'] = shelf_trolley_df['Backstock Pallets'] + shelf_trolley_df['Backstock Rollcages']\n", "shelf_trolley_df['Empty Pallets/Rollcage'] = shelf_trolley_df['Empty Pallets'] + shelf_trolley_df['Empty Rollcages']\n", "shelf_trolley_df['New Delivery Pallets/Rollcage'] = shelf_trolley_df['New Delivery - Pallets'] + shelf_trolley_df['New Delivery - Rollcages']"]}, {"cell_type": "code", "execution_count": null, "id": "85bd1d89-5dd5-45e0-b6f9-ff9a2980ee9e", "metadata": {}, "outputs": [], "source": ["shelf_trolley_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2b2bffc7-7038-439f-8161-a1e678ef9b5e", "metadata": {}, "outputs": [], "source": ["df_melt = shelf_trolley_df.melt(id_vars=shelf_trolley_df.iloc[:,:5].columns.tolist(), var_name='drivers')"]}, {"cell_type": "code", "execution_count": null, "id": "a1ea87a9-0cc1-4a92-a971-927ead56ee4a", "metadata": {}, "outputs": [], "source": ["df_melt_gr = df_melt[(df_melt.Format.isin(['1K', 'Express']) & (~df_melt['Dep'].isin(['WH', 'NEW'])))].groupby(['Country','Format', 'drivers'])['value'].mean().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "4812035a-48ec-4201-81ec-ec0543b800ca", "metadata": {}, "outputs": [], "source": ["df_melt_gr_to_plot = df_melt_gr[df_melt_gr.drivers.isin(['Backstock Pallets/Rollcage', 'Empty Pallets/Rollcage', 'New Delivery Pallets/Rollcage', 'Distance: WH to SF'])]"]}, {"cell_type": "code", "execution_count": null, "id": "85417a44-06f9-4254-8130-83e1a5f137f0", "metadata": {}, "outputs": [], "source": ["fig = px.histogram(df_melt_gr_to_plot, x=\"Country\", y=\"value\",\n", "             color='drivers', barmode='group', text_auto='.1f', height=600\n", "            )\n", "\n", "fig.update_yaxes(matches=None)\n", "fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=20))\n", "fig.update_yaxes(title_font_color='white')\n", "fig.layout.update({'title': 'Drivers from which Shelf trolley drivers are coming from'}, width=1200, template=\"seaborn\", font=dict(\n", "        size=16))\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "9232ec1f-2e44-4d5a-95e2-46928730ed76", "metadata": {}, "outputs": [], "source": ["fig = px.histogram(df_melt_gr_to_plot, x=\"Country\", y=\"value\",\n", "             color='drivers', barmode='group', text_auto='.1f', facet_row='Format', height=600,\n", "            category_orders={\"Format\": [\"1K\", \"Express\"]})\n", "\n", "fig.update_yaxes(matches=None)\n", "fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=20))\n", "fig.update_yaxes(title_font_color='white')\n", "fig.layout.update({'title': 'Drivers from which Shelf trolley drivers are coming from'}, width=1200, template=\"seaborn\", font=dict(\n", "        size=16))\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "8c7ed720-8eb9-449b-9c5f-4903547bacd8", "metadata": {}, "outputs": [], "source": ["df_melt_gr_to_plot_shelf = df_melt_gr[df_melt_gr.drivers.str.contains(\"Shelf\")]"]}, {"cell_type": "code", "execution_count": null, "id": "70cb0a01-0cdb-450a-a97f-e560c1591bc0", "metadata": {}, "outputs": [], "source": ["fig = px.histogram(df_melt_gr_to_plot_shelf, x=\"Country\", y=\"value\",\n", "             color='drivers', barmode='group', text_auto='.2s', facet_row='Format', height=600,\n", "            category_orders={\"Format\": [\"1K\", \"Express\"]})\n", "\n", "fig.update_yaxes(matches=None)\n", "fig.for_each_annotation(lambda a: a.update(text=a.text.split(\"=\")[-1], font_size=20))\n", "fig.update_yaxes(title_font_color='white')\n", "fig.layout.update({'title': '<PERSON><PERSON> Drivers for an average store'}, width=1200, template=\"seaborn\", font=dict(size=18))\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "cd632dbd-4194-4c8e-84b9-e2a7f163b704", "metadata": {}, "outputs": [], "source": ["a = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_jan\\shelf_trolley_costbase.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "7b6bd830-7d11-4894-af18-560a69c4b99b", "metadata": {}, "outputs": [], "source": ["a"]}, {"cell_type": "code", "execution_count": null, "id": "9907551f-784a-47f0-8984-081e30bdcc36", "metadata": {}, "outputs": [], "source": ["a.groupby('Country')['Yearly GBP'].sum().reset_index().T"]}, {"cell_type": "code", "execution_count": null, "id": "05ba258b-8472-4f99-835c-5c380565150b", "metadata": {}, "outputs": [], "source": ["a = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\Documents\\Turn up the workin' !!!!!!!\\#MODELS\\###GA MODELS\\#REPLENISHMENT\\ReplModel_2023\\others\\23_jan\\shelf_ins.xlsx\", header=1)"]}, {"cell_type": "code", "execution_count": null, "id": "f8908492-f4e7-437b-903b-7d6dccca62f8", "metadata": {}, "outputs": [], "source": ["a.head()"]}, {"cell_type": "code", "execution_count": null, "id": "155489a2-9f3d-4bc9-b1d5-1884343953e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3b542ad8-072f-401f-8aff-b394ebf588ce", "metadata": {"tags": []}, "outputs": [], "source": ["fig = px.bar(cz, x=\"Year\", y=\"GBP\", text_auto=True, color='Year')\n", "fig.layout.update({'title': 'Product Tagging'}, width=500,height=400, template=\"seaborn\", font=dict(size=18))\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "1268afb4-df92-4fa6-a794-1ac6a2183823", "metadata": {}, "outputs": [], "source": ["cz = pd.DataFrame({'Country':['CZ', 'CZ'], 'GBP' : [130376,140680], 'Year':['2022','2023']})\n", "hu = pd.DataFrame({'Country':['HU', 'HU'], 'GBP' : [135092,215020], 'Year':['2022','2023']})\n", "sk = pd.DataFrame({'Country':['SK', 'SK'], 'GBP' : [130542,193928], 'Year':['2022','2023']})"]}, {"cell_type": "code", "execution_count": null, "id": "ffafc6e3-1a1c-434b-8bcd-ef2e80fa9809", "metadata": {"tags": []}, "outputs": [], "source": ["for x,y in zip([cz, hu, sk], countries):\n", "\n", "    fig = px.bar(x, x=\"Year\", y=\"GBP\", text_auto='.4s', color='Year')\n", "    fig.layout.update({'title': f'Product Tagging {y}'}, width=500,height=400, template=\"seaborn\", font=dict(size=18))\n", "    fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "ff841ad3-8b75-4815-8a14-0de7676720cb", "metadata": {}, "outputs": [], "source": ["cz_pre = pd.DataFrame({'Country':['CZ', 'CZ'], 'GBP' : [1478,700], 'Year':['2022','2023']})\n", "hu_pre = pd.DataFrame({'Country':['HU', 'HU'], 'GBP' : [2039,878], 'Year':['2022','2023']})\n", "sk_pre = pd.DataFrame({'Country':['SK', 'SK'], 'GBP' : [821,759], 'Year':['2022','2023']})"]}, {"cell_type": "code", "execution_count": null, "id": "4457a6d8-8cb8-46f8-b12f-e4de178dcf82", "metadata": {}, "outputs": [], "source": ["countries = ['CZ', 'HU', 'SK']"]}, {"cell_type": "code", "execution_count": null, "id": "c6ba344e-1b12-4b38-bc9f-b49d8683597a", "metadata": {"tags": []}, "outputs": [], "source": ["for x,y in zip([cz_pre, hu_pre, sk_pre], countries):\n", "\n", "    fig = px.bar(x, x=\"Year\", y=\"GBP\", text_auto=True, color='Year')\n", "    fig.layout.update({'title': f'Pre-Sort {y}'}, width=500,height=400, template=\"seaborn\", font=dict(size=18))\n", "    fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "e52641b0-699e-4f67-a6b3-247e4ba3d8e7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}