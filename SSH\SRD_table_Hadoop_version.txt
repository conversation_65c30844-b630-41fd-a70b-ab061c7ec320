--set spark.sql.legacy.timeParserPolicy=LEGACY;
--set spark.sql.parquet.writeLegacyFormat=true;

-- Flop Table -- -- START --
with Flop_POG_1 AS (
	SELECT DISTINCT
		f.dbkey AS FlopID
		, f.desc12 AS Store_Number
		, f.desc11 AS Store_Name
		, f.desc2 AS Store_Format
		, fs.DBParentPlanogramKey
		, fs.dbkey AS FS_DBkey
		, fs.SegmentStart
		, fs.SegmentEnd	
		, f.int_cntr_code
		FROM stg_ikb.ix_flr_floorplan AS f
			LEFT JOIN stg_ikb.ix_flr_section AS fs ON (fs.dbparentfloorplankey = f.dbkey AND fs.int_cntr_code = f.int_cntr_code)
		WHERE f.dbstatus = 1 -- AND f.desc12 = 1520 AND f.int_cntr_code = 'HU'
		),
-- Flop Table -- -- END --

-- POG Table -- -- START --
Pog_Table_0 AS (
	SELECT DISTINCT
		p.desc2 AS Displaygroup
		, p.desc24 AS Displaygroup_description	
		, p.Dbkey AS POG_ID
		, p.name AS POG_NAME
		, p.dbversiONkey
		, p.DBStatus AS POG_DBStatus
		, p.int_cntr_code AS Country
		, p.NumberOfProductsAllocated
		, p.desc8 AS NumberOfMods 
		FROM stg_ikb.ix_spc_planogram AS p 
		WHERE (p.dbstatus not in ('4','3'))	AND CONCAT(p.dbversiONkey,p.int_cntr_code) in (SELECT DISTINCT CONCAT(dbparentplanogramkey,int_cntr_code) as A FROM Flop_POG_1)),
-- POG Table -- -- END --

-- Darab Table -- -- START --
Darab_1 AS (
	SELECT DISTINCT 
		p_help.POG_ID
		, p_help.POG_NAME
		, pos.segment
		, fx.x
		, fx.y
		, p_help.Country
		, COUNT(CONCAT(p_help.POG_ID, pos.segment, fx.x, fx.y)) AS Darab
		FROM  Pog_Table_0 AS p_help
			LEFT JOIN stg_ikb.ix_spc_position AS pos ON (p_help.POG_ID=pos.dbparentplanogramkey AND pos.int_cntr_code = p_help.Country) 
			LEFT JOIN stg_ikb.ix_spc_fixture AS fx ON (p_help.POG_ID=fx.DBParentPlanogramKey AND pos.DBparentfixturekey=fx.dbkey AND fx.int_cntr_code = p_help.Country) 
		GROUP BY p_help.POG_ID
			, p_help.POG_NAME
			, pos.segment
			, fx.x
			, fx.y
			, p_help.Country),
-- Darab Table -- -- END --

-- PositiON Table -- -- START --
Pos_Table_1 AS (
	SELECT DISTINCT 
		pos.segment
		, pos.hfacings
		, pos.vfacings
		, pos.dfacings
		, pos.merchstyle
		, pos.capacity
		, pos.dbparentplanogramkey
		, pos.dbparentfixturekey
		, pos.dbparentproductkey
		, pos.int_cntr_code AS Country
		FROM  Pog_Table_0 AS p_help
			LEFT JOIN stg_ikb.ix_spc_position AS pos ON (p_help.POG_ID=pos.dbparentplanogramkey AND pos.int_cntr_code = p_help.Country)),
-- PositiON Table -- -- END --

-- Pog1 Table -- -- START --
Pog_Table_1 AS (
	SELECT DISTINCT 
		p_help.Displaygroup
		, p_help.Displaygroup_description
		, p_help.POG_ID
		, p_help.POG_Name 
		, p_help.NumberOfProductsAllocated
		, p_help.NumberOfMods
		, pos.segment
		, fx.dbkey as Fixture_dbkey
		, fx.name AS Fixture_name
		, fx.depth AS Fixture_depth 
		, fx.Width AS Fixture_width
		, fx.x AS Fixture_X_position
		, fx.y AS Fixture_Y_position
		, pr.id AS Product_id
		, pr.Product_Name
		, pos.hfacings
		, pos.vfacings 
		, pos.dfacings
		, pos.merchstyle
		, pos.capacity AS Position_Capacity
		, CASE 
  			WHEN pos.merchstyle=0 THEN 'Unit'
  			WHEN pos.merchstyle=1 THEN 'Tray'
  			WHEN pos.merchstyle=2 THEN 'Case'
  			WHEN pos.merchstyle=3 THEN 'Display'
  			WHEN pos.merchstyle=4 THEN 'Alternate'
  			WHEN pos.merchstyle=5 THEN 'Loose'
  			WHEN pos.merchstyle=6 THEN 'Log Stack' 
  			ELSE 'NULL' 
  			END AS merchstyle_string
		, pr.desc18 AS brand
		, pr.desc23 AS ownbrand  ---desc26 volt
		, pr.Height
		, pr.Width 
		, pr.Depth 
		, pr.TrayHeight 
		, pr.TrayWidth 
		, pr.TrayDepth 
		, pr.TrayNumberHigh 
		, pr.TrayNumberWide 
		, pr.TrayNumberDeep 
		, pr.TrayTotalNumber  
		, pr.CASeHeight
		, pr.CASeWidth 
		, pr.CASeDepth 
		, pr.CASeNumberHigh 
		, pr.CASeNumberWide 
		, pr.CASeNumberDeep
		, pr.CASeTotalNumber
		, perf.capacity AS Capacity_ON_Planogram
		, p_help.Country
		FROM Pog_Table_0 AS p_help
			LEFT JOIN Pos_Table_1 AS pos ON (p_help.POG_ID=pos.dbparentplanogramkey AND pos.Country = p_help.Country) 
			LEFT JOIN stg_ikb.ix_spc_product AS pr ON (pr.dbkey=pos.dbparentproductkey AND pr.int_cntr_code = p_help.Country)
			LEFT JOIN stg_ikb.ix_spc_fixture AS fx ON (p_help.POG_ID=fx.DBParentPlanogramKey AND pos.DBparentfixturekey=fx.dbkey AND fx.int_cntr_code = p_help.Country)
			LEFT JOIN stg_ikb.ix_spc_performance AS perf ON (p_help.POG_ID=perf.DBParentPlanogramKey AND pr.dbkey=perf.DBParentProductKey AND perf.int_cntr_code = p_help.Country)),
-- Pog1 Table -- -- END --

-- Flop2 Table -- -- START --
Flop2 AS (
	SELECT DISTINCT 
		tabla.FlopID 
		, tabla.Store_Number
		, tabla.POG_ID
		, IF(tabla.Z_Szegment = Tabla.NumberOfMods,1,
			IF(tabla.Z_Szegment > Tabla.NumberOfMods,
			IF((tabla.Z_Szegment /Tabla.NumberOfMods)=(Round(tabla.Z_Szegment /Tabla.NumberOfMods,1)),tabla.Z_Szegment /Tabla.NumberOfMods,0),0)) AS X_POG_NUM
		, tabla.int_cntr_code
		FROM (SELECT DISTINCT
			tabla.FlopID
			, tabla.Store_Number
			, tabla.POG_ID
			, tabla.NumberOfMods
			, SUM(IF( tabla.SegmentStart=0 AND tabla.SegmentEnd=0,tabla.NumberOfMods
			  ,	IF(tabla.SegmentStart=1 AND tabla.SegmentEnd=tabla.NumberOfMods,tabla.NumberOfMods
			  , IF(tabla.SegmentStart=tabla.SegmentEnd,1,tabla.SegmentEnd- IF(tabla.SegmentStart>1,tabla.SegmentStart-1,0))))) 
		      AS Z_Szegment 
			, tabla.int_cntr_code
			FROM (SELECT DISTINCT
				Flop_POG_1.FlopID
				, Flop_POG_1.fs_dbkey
				, Flop_POG_1.Store_Number
				, Pog_Table_1.POG_ID
				, Pog_Table_1.NumberOfMods
				, Flop_POG_1.SegmentStart
				, Flop_POG_1.SegmentEnd
				, Flop_POG_1.int_cntr_code
				FROM Flop_POG_1 
					LEFT JOIN Pog_Table_1 ON (Flop_POG_1.DBParentPlanogramKey=Pog_Table_1.POG_ID AND Flop_POG_1.int_cntr_code=Pog_Table_1.Country)) AS tabla
			GROUP by tabla.FlopID
			, tabla.Store_Number
			, tabla.POG_ID
			, tabla.NumberOfMods
			, tabla.int_cntr_code
			) AS tabla)
-- Flop2 Table -- -- END --

-- Final Table -- -- START --
SELECT DISTINCT
		Flop_POG_1.int_cntr_code AS Country
		, Flop_POG_1.Store_Number
		, Flop_POG_1.Store_Name
		, Flop_POG_1.Store_Format
		, Pog_Table_1.Displaygroup
		, Pog_Table_1.Displaygroup_description
		, Pog_Table_1.POG_ID
		, Pog_Table_1.POG_Name
		, Pog_Table_1.segment AS Segmentnumber
		, Pog_Table_1.Fixture_dbkey
		, Pog_Table_1.Fixture_name
		, Pog_Table_1.Fixture_depth
		, Pog_Table_1.Fixture_width
		, IF(substring(Pog_Table_1.Fixture_name, 1, 3) = 'PAL' or substring(Pog_Table_1.Fixture_name, 1, 3) = 'Rak'
		  ,	IF((Pog_Table_1.Fixture_depth = 80 AND Pog_Table_1.Fixture_width =60) or (Pog_Table_1.Fixture_depth = 60 AND Pog_Table_1.Fixture_width = 80)
		  ,	IF(Darab_1.Darab >1,'Split_ON_Half_Pallet', 'Half_Pallet')
		  , IF((Pog_Table_1.Fixture_depth = 80 AND Pog_Table_1.Fixture_width =120) or (Pog_Table_1.Fixture_depth= 120 AND Pog_Table_1.Fixture_width = 80)
		  ,	If(Darab_1.Darab >1, 'Split_Pallet','Pallet'),'')),'') 
		    AS Pallet_info
		, IF(substring(Pog_Table_1.Fixture_name, 1, 3) = 'PAL' or substring (Pog_Table_1.Fixture_name, 1, 3) = 'Rak'
		  ,	IF((Pog_Table_1.Fixture_depth = 80 AND Pog_Table_1.Fixture_width =60) or (Pog_Table_1.Fixture_depth = 60 AND Pog_Table_1.Fixture_width = 80)
		  ,	IF(Darab_1.Darab >1,'Split_ON_Half_Pallet', 'Half_Pallet')
		  , IF((Pog_Table_1.Fixture_depth = 80 AND Pog_Table_1.Fixture_width =120) or (Pog_Table_1.Fixture_depth= 120 AND Pog_Table_1.Fixture_width = 80)
		  ,	If(Darab_1.Darab >1, 'Split_Pallet','Pallet'),Pog_Table_1.merchstyle_string)),Pog_Table_1.merchstyle_string) 
		    AS INFO
		, Pog_Table_1.Product_id
		, Pog_Table_1.Product_Name
		, Pog_Table_1.hfacings AS Position_HFacing
		, Pog_Table_1.vfacings AS Position_VFacing 
		, Pog_Table_1.dfacings AS Position_DFacing
		, Pog_Table_1.merchstyle AS merchstyle_ID
		, Pog_Table_1.merchstyle_string
		, Pog_Table_1.brand
		, Pog_Table_1.ownbrand
		, Pog_Table_1.Height
		, Pog_Table_1.Width
		, Pog_Table_1.Depth
		, Pog_Table_1.TrayHeight
		, Pog_Table_1.TrayWidth
		, Pog_Table_1.TrayDepth
		, Pog_Table_1.TrayNumberHigh
		, Pog_Table_1.TrayNumberWide
		, Pog_Table_1.TrayNumberDeep
		, Pog_Table_1.TrayTotalNumber
		, Pog_Table_1.CaseHeight
		, Pog_Table_1.CaseWidth 
		, Pog_Table_1.CaseDepth 
		, Pog_Table_1.CaseNumberHigh
		, Pog_Table_1.CaseNumberWide 
		, Pog_Table_1.CaseNumberDeep
		, Pog_Table_1.CaseTotalNumber
		, artrp.dmat_div_code AS Division
		, artrp.dmat_dep_code AS Department
		, artrp.dmat_sec_code AS Section
		, artrp.dmat_grp_code AS Group_
		, artrp.dmat_sgr_code AS Subgroup
		, Pog_Table_1.NumberOfProductsAllocated
		, Pog_Table_1.Position_Capacity
		, fl.X_POG_NUM AS Planogram_on_Store
		, Pog_Table_1.Position_Capacity * fl.X_POG_NUM AS Position_Capacity_X_Planogram_on_Store
		FROM Flop_POG_1 
			JOIN Pog_Table_1 ON (Flop_POG_1.DBParentPlanogramKey=Pog_Table_1.POG_ID AND Flop_POG_1.int_cntr_code=Pog_Table_1.Country) 
			JOIN Darab_1 ON (Pog_Table_1.POG_ID=Darab_1.POG_ID AND Pog_Table_1.segment=Darab_1.segment	AND Pog_Table_1.Fixture_X_position=Darab_1.x AND Pog_Table_1.Fixture_Y_position=Darab_1.y AND Flop_POG_1.int_cntr_code=Darab_1.Country) 
			JOIN Flop2 AS FL ON (FL.Store_Number=Flop_POG_1.Store_Number AND FL.POG_ID=Flop_POG_1.DBParentPlanogramKey AND FL.int_cntr_code=Flop_POG_1.int_cntr_code) 
			LEFT JOIN dm.dim_artrep_details AS artrp ON (Pog_Table_1.Product_id = artrp.slad_tpnb AND artrp.cntr_code = Pog_Table_1.Country);
-- Final Table -- -- END --

