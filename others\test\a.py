import streamlit as st
from typing import Dict, Any
from string import Template
from textwrap import dedent

# CSS Template class
class HTML_Template:
    base_style = Template(
        dedent(
            """
            <style>
                $css
            </style>"""
        )
    )

# Enhanced CSS for cards
card_css = """
.st-key-control_card {
    background-color: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin: 1rem 0;
    min-height: 100px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.st-key-control_card:hover {
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

.st-key-variables_card {
    background-color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin: 1rem 0;
    min-height: 100px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.st-key-variables_card:hover {
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

.st-key-result_card {
    background-color: #e3f2fd;
    border-radius: 1rem;
    padding: 1.5rem;
    margin: 1rem 0;
    min-height: 100px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.st-key-result_card:hover {
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

/* Style for headers inside cards */
.st-key-variables_card h3, .st-key-result_card h3 {
    color: #1e88e5;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e0e0e0;
}

/* Style for text inside cards */
div[data-testid="stText"] div {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}
"""

def initialize_session_state():
    """Initialize session state variables if they don't exist"""
    if 'variables' not in st.session_state:
        st.session_state.variables = {
            'number1': 0,
            'number2': 0,
            'operation': 'add'
        }
    if 'result' not in st.session_state:
        st.session_state.result = None

def perform_calculation(variables: Dict[str, Any]) -> float:
    """Perform the selected operation on the input numbers"""
    num1 = variables['number1']
    num2 = variables['number2']
    operation = variables['operation']
    
    if operation == 'add':
        return num1 + num2
    elif operation == 'subtract':
        return num1 - num2
    elif operation == 'multiply':
        return num1 * num2
    elif operation == 'divide':
        if num2 != 0:
            return num1 / num2
        else:
            st.error("Cannot divide by zero!")
            return None

@st.dialog("Setup Variables")
def setup_variables():
    """Dialog for setting up calculation variables"""
    st.write("Enter your numbers and choose an operation")
    
    number1 = st.number_input("First Number", value=st.session_state.variables['number1'])
    number2 = st.number_input("Second Number", value=st.session_state.variables['number2'])
    operation = st.selectbox(
        "Operation",
        options=['add', 'subtract', 'multiply', 'divide'],
        index=['add', 'subtract', 'multiply', 'divide'].index(st.session_state.variables['operation'])
    )
    
    if st.button("Save"):
        st.session_state.variables.update({
            'number1': number1,
            'number2': number2,
            'operation': operation
        })
        st.rerun()

def main():
    # Apply CSS
    st.markdown(HTML_Template.base_style.substitute(css=card_css), unsafe_allow_html=True)
    
    st.title("Calculator with Variable Setup")
    
    # Initialize session state
    initialize_session_state()
    
    # Control buttons card
    with st.container(key="control_card"):
        col1, col2 = st.columns([1, 2])
        with col1:
            if st.button("Setup Variables", type="primary", use_container_width=True):
                setup_variables()
        with col2:
            if st.button("Calculate", type="primary", use_container_width=True):
                result = perform_calculation(st.session_state.variables)
                if result is not None:
                    st.session_state.result = result
    
    # Variables display card
    with st.container(key="variables_card"):
        st.subheader("Current Variables")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.write(f"Number 1: {st.session_state.variables['number1']}")
        with col2:
            st.write(f"Number 2: {st.session_state.variables['number2']}")
        with col3:
            st.write(f"Operation: {st.session_state.variables['operation']}")
    
    # Result card
    if st.session_state.result is not None:
        with st.container(key="result_card"):
            st.subheader("Result")
            st.write(f"{st.session_state.variables['number1']} {st.session_state.variables['operation']} {st.session_state.variables['number2']} = {st.session_state.result}")

if __name__ == "__main__":
    main()