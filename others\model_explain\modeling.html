<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modeling Process</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
        }
        .process-flow {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .step {
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            background-color: white;
        }
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .step-number {
            background-color: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .step-title {
            font-size: 1.5rem;
            color: #2c3e50;
            margin: 0;
        }
        .step-content {
            display: flex;
            flex-direction: column;
            margin-left: 55px;
        }
        .data-flow-diagram {
            width: 100%;
            overflow-x: auto;
            margin: 30px 0;
        }
        .detail-box {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 12px 15px;
            margin: 10px 0;
            border-radius: 0 5px 5px 0;
        }
        .example-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 12px 15px;
            margin: 10px 0;
            border-radius: 0 5px 5px 0;
        }
        .key-insight {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 12px 15px;
            margin: 10px 0;
            border-radius: 0 5px 5px 0;
        }
        .code-block {
            background-color: #f5f5f5;
            padding: 12px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        .arrow-down {
            display: flex;
            justify-content: center;
            padding: 10px 0;
        }
        .arrow-down svg {
            width: 30px;
            height: 50px;
            fill: #7f8c8d;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .figure {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .figure-caption {
            text-align: center;
            font-style: italic;
            color: #555;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Modeling Process</h1>
            <p>From raw data sources to activity-based labor hours calculation</p>
        </div>

        <div class="data-flow-diagram">
            <svg width="100%" height="640" viewBox="0 0 1100 640">
                <!-- Main Flow Background -->
                <rect x="50" y="20" width="1000" height="600" fill="#f8f9fa" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                
                <!-- Step 1: Data Collection & Transformation -->
                <rect x="100" y="50" width="900" height="120" fill="white" rx="8" ry="8" stroke="#3498db" stroke-width="2"/>
                <text x="120" y="80" font-size="18" font-weight="bold" fill="#2c3e50">Step 1: Data Collection & Transformation</text>
                
                <!-- Data Sources Container -->
                <rect x="120" y="100" width="300" height="50" fill="#e1f5fe" rx="5" ry="5" stroke="#81d4fa" stroke-width="1"/>
                <text x="270" y="130" text-anchor="middle" font-size="14">Hadoop + Other Sources</text>
                
                <!-- ETL Process -->
                <rect x="480" y="100" width="160" height="50" fill="#e8f5e9" rx="5" ry="5" stroke="#a5d6a7" stroke-width="1"/>
                <text x="560" y="130" text-anchor="middle" font-size="14">ETL Transformation</text>
                
                <!-- Dataset Creation -->
                <rect x="780" y="100" width="160" height="50" fill="#ede7f6" rx="5" ry="5" stroke="#b39ddb" stroke-width="1"/>
                <text x="860" y="130" text-anchor="middle" font-size="14">Weekly Model Dataset</text>
                
                <!-- Arrows -->
                <line x1="420" y1="125" x2="480" y2="125" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="640" y1="125" x2="780" y2="125" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <!-- Step 2: Driver Creation -->
                <rect x="100" y="200" width="900" height="120" fill="white" rx="8" ry="8" stroke="#3498db" stroke-width="2"/>
                <text x="120" y="230" font-size="18" font-weight="bold" fill="#2c3e50">Step 2: Driver Creation</text>
                
                <!-- Model Dataset -->
                <rect x="120" y="250" width="200" height="50" fill="#ede7f6" rx="5" ry="5" stroke="#b39ddb" stroke-width="1"/>
                <text x="220" y="280" text-anchor="middle" font-size="14">Weekly Model Dataset</text>
                
                <!-- Python Engine -->
                <rect x="380" y="250" width="200" height="50" fill="#fff3e0" rx="5" ry="5" stroke="#ffe0b2" stroke-width="1"/>
                <text x="480" y="275" text-anchor="middle" font-size="14">Python Data Engine</text>
                <text x="480" y="290" text-anchor="middle" font-size="12">Aggregation & Calculation</text>
                
                <!-- Driver Output -->
                <rect x="700" y="250" width="200" height="50" fill="#e0f7fa" rx="5" ry="5" stroke="#80deea" stroke-width="1"/>
                <text x="800" y="275" text-anchor="middle" font-size="14">Store Specific Drivers</text>
                <text x="800" y="290" text-anchor="middle" font-size="12">e.g., Cases per Store</text>
                
                <!-- Arrows -->
                <line x1="320" y1="275" x2="380" y2="275" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="580" y1="275" x2="700" y2="275" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <!-- Step 3: Activity Creation -->
                <rect x="100" y="350" width="900" height="120" fill="white" rx="8" ry="8" stroke="#3498db" stroke-width="2"/>
                <text x="120" y="380" font-size="18" font-weight="bold" fill="#2c3e50">Step 3: Activities + Drivers</text>
                
                <!-- Drivers Input -->
                <rect x="120" y="400" width="180" height="50" fill="#e0f7fa" rx="5" ry="5" stroke="#80deea" stroke-width="1"/>
                <text x="210" y="425" text-anchor="middle" font-size="14">Store Drivers</text>
                <text x="210" y="440" text-anchor="middle" font-size="12">e.g., Stock Volumes</text>
                
                <!-- Activity Standards -->
                <rect x="380" y="400" width="180" height="50" fill="#f3e5f5" rx="5" ry="5" stroke="#ce93d8" stroke-width="1"/>
                <text x="470" y="425" text-anchor="middle" font-size="14">Activity Standards</text>
                <text x="470" y="440" text-anchor="middle" font-size="12">e.g. "Tray fill", 25 sec/case</text>
                
                <!-- Activity Calculation -->
                <rect x="640" y="400" width="260" height="50" fill="#fff8e1" rx="5" ry="5" stroke="#ffe082" stroke-width="1"/>
                <text x="770" y="425" text-anchor="middle" font-size="14">Store Activity Calculations</text>
                <text x="770" y="440" text-anchor="middle" font-size="12">Driver × Frequency × Activity Standard</text>
                
                <!-- Arrows -->
                <line x1="300" y1="425" x2="380" y2="425" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="560" y1="425" x2="640" y2="425" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <!-- Step 4: Hours Calculation -->
                <rect x="100" y="500" width="900" height="100" fill="white" rx="8" ry="8" stroke="#3498db" stroke-width="2"/>
                <text x="120" y="530" font-size="18" font-weight="bold" fill="#2c3e50">Step 4: Hours Calculation</text>
                
                <!-- Activity Times -->
                <rect x="120" y="550" width="200" height="40" fill="#fff8e1" rx="5" ry="5" stroke="#ffe082" stroke-width="1"/>
                <text x="220" y="575" text-anchor="middle" font-size="14">Activity Time Values</text>
                
                <!-- Conversion Process -->
                <rect x="400" y="550" width="200" height="40" fill="#e8f5e9" rx="5" ry="5" stroke="#a5d6a7" stroke-width="1"/>
                <text x="500" y="575" text-anchor="middle" font-size="14">Seconds → Hours Conversion</text>
                
                <!-- Final Output -->
                <rect x="680" y="550" width="220" height="40" fill="#ffebee" rx="5" ry="5" stroke="#ffcdd2" stroke-width="1"/>
                <text x="790" y="575" text-anchor="middle" font-size="14">Store Activity Hours</text>
                
                <!-- Arrows -->
                <line x1="320" y1="570" x2="400" y2="570" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="600" y1="570" x2="680" y2="570" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <!-- Arrow definitions -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
                    </marker>
                </defs>
                
                <!-- Vertical flow arrows -->
                <line x1="550" y1="170" x2="550" y2="200" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
                <line x1="550" y1="320" x2="550" y2="350" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
                <line x1="550" y1="470" x2="550" y2="500" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
            </svg>
        </div>
        
        <div class="process-flow">
            <div class="step">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <h2 class="step-title">Data Collection & Transformation</h2>
                </div>
                <div class="step-content">
                    <p>The process begins with extracting raw retail data from multiple sources and transforming it into a consistent format.</p>
                    
                    <div class="detail-box">
                        <strong>Data Sources:</strong>
                        <ul>
                            <li>Hadoop + Other Sources in a unified data extraction process</li>
                            <li>Data types include: sales data, inventory levels, product information, store details, merchandising data</li>
                        </ul>
                    </div>
                    
                    <div class="detail-box">
                        <strong>Transformation Process:</strong>
                        <ul>
                            <li>Data cleaning and normalization</li>
                            <li>Handling missing values</li>
                            <li>Format standardization across sources</li>
                            <li>Time period filtering (May 27 - Sept 1, 2024)</li>
                        </ul>
                    </div>
                    
                    <div class="key-insight">
                        <strong>Output:</strong> A consistent dataset in Parquet format containing all retail data needed for further processing.
                    </div>
                </div>
            </div>

            <div class="arrow-down">
                <svg viewBox="0 0 24 24">
                    <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
                </svg>
            </div>
            
            <div class="step">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <h2 class="step-title">Driver Creation</h2>
                </div>
                <div class="step-content">
                    <p>In this step, we create "Drivers" - the quantifiable metrics that drive labor needs at the store level.</p>
                    
                    <div class="detail-box">
                        <strong>Process:</strong>
                        <ul>
                            <li>Python data engine processes the Model Dataset</li>
                            <li>Aggregates data by store, product, day of week</li>
                            <li>Calculates key drivers like weekly case volumes, units sold, etc.</li>
                        </ul>
                    </div>
                    
                    <div class="example-box">
                        <!-- <strong>Example Driver Calculation:</strong>
                        <div class="code-block">
# Python pseudocode for driver creation
import pandas as pd

# Load model dataset
model_df = pd.read_parquet("model_dataset.parquet")

# Create cases_per_store driver
cases_per_store = model_df.groupby(['store', 'day'])[['cases_delivered']].sum()

# Calculate weekly units sold per store
units_sold_per_store = model_df.groupby(['store', 'day'])[['sold_units']].sum()

# Export drivers
cases_per_store.to_parquet("cases_driver.parquet")
units_sold_per_store.to_parquet("units_driver.parquet")</div>
                    </div> -->
                    
                    <div class="key-insight">
                        <strong>Output:</strong> Store-specific driver datasets that quantify volumes (e.g., cases delivered per day, units sold) for each store.
                    </div>
                </div>
            </div>

            <div class="arrow-down">
                <svg viewBox="0 0 24 24">
                    <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
                </svg>
            </div>
            
            <div class="step">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <h2 class="step-title">Activities + Drivers</h2>
                </div>
                <div class="step-content">
                    <p>This step pairs drivers with activity standards to determine the time required for each operational activity.</p>
                    
                    <div class="detail-box">
                        <strong>Activity Standards:</strong>
                        <ul>
                            <li>Predefined time standards for different retail activities</li>
                            <li>Measured in time per unit (e.g., 25 seconds per case for tray filling)</li>
                            <li>Based on time & motion studies and industry standards</li>
                        </ul>
                    </div>
                    
                    <div class="figure">
                        <table>
                            <tr>
                                <th>Activity</th>
                                <th>Driver</th>
                                <th>Time Standard</th>
                                <th>Calculation</th>
                            </tr>
                            <tr>
                                <td>Tray Fill</td>
                                <td>Cases Delivered</td>
                                <td>25 sec/case</td>
                                <td>Cases × Frequency × 25 sec</td>
                            </tr>
                            <tr>
                                <td>Shelf Replenishment</td>
                                <td>Units Sold</td>
                                <td>10 sec/unit</td>
                                <td>Units × Frequency × 10 sec</td>
                            </tr>
                            <tr>
                                <td>Price Changes</td>
                                <td>Price Change Count</td>
                                <td>30 sec/change</td>
                                <td>Changes × Frequency × 30 sec</td>
                            </tr>
                        </table>
                        <div class="figure-caption">Example activity standards and calculations</div>
                    </div>
                    
                    <div class="key-insight">
                        <strong>Output:</strong> A dataset of calculated time values for each activity in each store based on Driver × Frequency × Activity Standard.
                    </div>
                </div>
            </div>

            <div class="arrow-down">
                <svg viewBox="0 0 24 24">
                    <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
                </svg>
            </div>
            
            <div class="step">
                <div class="step-header">
                    <div class="step-number">4</div>
                    <h2 class="step-title">Hours Calculation</h2>
                </div>
                <div class="step-content">
                    <p>The final step converts activity time values into labor hours for each store.</p>
                    
                    <div class="detail-box">
                        <strong>Process:</strong>
                        <ul>
                            <li>Convert seconds to minutes and hours</li>
                            <li>Sum hours by activity type, department, or store</li>
                            <li>Apply any efficiency factors or adjustments</li>
                        </ul>
                    </div>
                    
                    <!-- <div class="example-box">
                        <strong>Example Calculation:</strong>
                        <div class="code-block">
# Pseudocode for hours calculation
# Time values from previous step (in seconds)
tray_fill_seconds = cases_delivered * frequency * 25  # seconds
shelf_replenishment_seconds = units_sold * frequency * 10  # seconds

# Convert to hours
tray_fill_hours = tray_fill_seconds / 3600  # convert to hours
shelf_replenishment_hours = shelf_replenishment_seconds / 3600  # convert to hours

# Total store hours
total_store_hours = tray_fill_hours + shelf_replenishment_hours + other_activity_hours</div>
                    </div> -->
                    
                    <div class="key-insight">
                        <strong>Final Output:</strong> Store-specific labor hours needed for each activity, providing actionable workforce planning insights.
                    </div>
                </div>
            </div>
        </div>
        
        <div class="key-insight" style="margin-top: 30px; margin-bottom: 30px;">
            <strong>Summary:</strong> This 4-step process takes raw retail data and transforms it into actionable labor hours, enabling precise workforce planning and optimization at the store level.
        </div>
    </div>
</body>
</html>