import pyodbc
import pandas as pd
import polars as pl

import time

start = time.time()


conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()






df2 = pd.DataFrame()

for x in ["'f2023w12'","'f2023w22'","'f2023w26'"]:
    
    sql = """ SELECT  cast(stores.dmst_store_code as INT) AS store, 
                        hier.pmg AS pmg,  
                        SUM(sunit.sltrg_tr_unit) AS sold_units_dotcom,
                        SUM(sunit.sltrg_tr_salex) AS sales_excl_vat,
                        {x} as week
                        FROM dw.sl_trg sunit 
                        JOIN dm.dim_stores stores ON stores.cntr_id = sunit.sltrg_cntr_id AND stores.dmst_store_id = sunit.sltrg_dmst_id 
                        LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
                        JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.sltrg_dmat_id
                        AND mstr.cntr_id = sunit.sltrg_cntr_id 
                        LEFT JOIN dw.dim_suppliers supl ON supl.dmsup_cntr_id = mstr.cntr_id 
                        AND supl.dmsup_id = mstr.slad_dmsup_id
                        JOIN tesco_analysts.hierarchy_spm_odbc hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
                        AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                        AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                        AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                        AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
                        WHERE cal.dmtm_fw_code = {x}
                        AND stores.cntr_code IN ('HU', 'CZ', 'SK')
                        AND sunit.sltrg_tr_unit > 0 
                        AND SUBSTRING(hier.pmg, 1, 3) IN ('MPC')
                        AND stores.convenience IN ('Convenience', 'HM')
                        GROUP BY  stores.dmst_store_code, week,  hier.pmg
                        ORDER BY  stores.dmst_store_code, week,  hier.pmg
                    
                    """.format(
        x=x
    )

    # art_gold = pl.read_database(sql, conn)
    art_gold = pd.read_sql(sql, conn)
    df2 = pd.concat([df2,art_gold])
    
    
sql = """ SELECT  cast(stores.dmst_store_code as INT) AS store, 
                    hier.pmg AS pmg,  
                    SUM(sunit.sltrg_tr_unit)/14 AS sold_units_dotcom,
                    SUM(sunit.sltrg_tr_salex)/14 AS sales_excl_vat
                    FROM dw.sl_trg sunit 
                    JOIN dm.dim_stores stores ON stores.cntr_id = sunit.sltrg_cntr_id AND stores.dmst_store_id = sunit.sltrg_dmst_id 
                    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
                    JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.sltrg_dmat_id
                    AND mstr.cntr_id = sunit.sltrg_cntr_id 
                    LEFT JOIN dw.dim_suppliers supl ON supl.dmsup_cntr_id = mstr.cntr_id 
                    AND supl.dmsup_id = mstr.slad_dmsup_id
                    JOIN tesco_analysts.hierarchy_spm_odbc hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
                    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
                    WHERE cal.dmtm_fw_code between 'f2023w14' and 'f2023w27'
                    AND stores.cntr_code IN ('HU', 'CZ', 'SK')
                    AND sunit.sltrg_tr_unit > 0 
                    AND SUBSTRING(hier.pmg, 1, 3) IN ('MPC')
                    AND stores.convenience IN ('Convenience', 'HM')
                    GROUP BY  stores.dmst_store_code,  hier.pmg
                    ORDER BY  stores.dmst_store_code,  hier.pmg
                
                """

# art_gold = pl.read_database(sql, conn)
art_gold = pd.read_sql(sql, conn)

    
print(time.time() - start)