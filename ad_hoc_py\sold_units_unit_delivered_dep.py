import pandas as pd

import pyodbc


with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:
   
    sql = """
    
    
    SELECT COALESCE(A.store, B.store) AS store,
       COALESCE(A.dep, B.dep) AS dep,
       A.sold_units,
       B.unit_delivered
FROM
(
    -- Subquery A
    SELECT 
        CAST(stores.dmst_store_code AS INT) AS store,
        SUBSTRING(d.pmg, 1, 3) AS dep,
        SUM(sunit.slsms_unit)  AS sold_units
    FROM dw.sl_sms sunit 
    JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id 
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
    JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id 
    JOIN tesco_analysts.hierarchy_spm d ON mstr.dmat_div_code = LPAD(d.div_code, 4, "0") AND mstr.dmat_dep_code = LPAD(d.dep_code, 4, "0") 
                                           AND mstr.dmat_sec_code = LPAD(d.sec_code, 4, "0") AND mstr.dmat_grp_code = LPAD(d.grp_code, 4, "0") 
                                           AND mstr.dmat_sgr_code = LPAD(d.sgr_code, 4, "0") 
    WHERE cal.dmtm_fw_code BETWEEN 'f2022w45' AND  'f2023w44'
          AND stores.cntr_code IN ('HU', 'SK', 'CZ')
          AND sunit.slsms_unit > 0 
          AND sunit.slsms_salex > 0 
          AND stores.convenience IN ('Convenience', 'HM')
    GROUP BY stores.dmst_store_code, SUBSTRING(d.pmg, 1, 3)
) A
FULL OUTER JOIN
(
    -- Subquery B
    SELECT 
        CAST(CONCAT(a.int_cntr_id, a.store) AS INT) AS store,
        SUBSTRING(d.pmg, 1, 3) AS dep,
        SUM(a.qty)  AS unit_delivered
    FROM stg_go.go_106_order_receiving a
    LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product AND b.cntr_id = a.int_cntr_id
    RIGHT JOIN tesco_analysts.hierarchy_spm d ON b.dmat_div_code = LPAD(d.div_code, 4, "0") 
                                                AND b.dmat_dep_code = LPAD(d.dep_code, 4, "0") 
                                                AND b.dmat_sec_code = LPAD(d.sec_code, 4, "0") 
                                                AND b.dmat_grp_code = LPAD(d.grp_code, 4, "0") 
                                                AND b.dmat_sgr_code = LPAD(d.sgr_code, 4, "0") 
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
    WHERE  a.int_cntr_code IN ('HU', 'CZ', 'SK')
          AND cal.dmtm_fw_code BETWEEN 'f2022w45' AND  'f2023w44'
    GROUP BY CONCAT(a.int_cntr_id, a.store), SUBSTRING(d.pmg, 1, 3)
) B
ON A.store = B.store AND A.dep = B.dep
ORDER BY COALESCE(A.store, B.store), COALESCE(A.dep, B.dep);
    
    
    """
    
    
    
    
    df = pd.read_sql(sql, conn)