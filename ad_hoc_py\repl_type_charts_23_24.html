<!DOCTYPE html>
<html>
<head>
    <title>Repl Type Charts</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.1/chart.umd.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #111827;
            color: white;
            font-family: system-ui, -apple-system, sans-serif;
        }
        .container {
            max-width: 1400px;
            margin: 0 1rem;
            background-color: #111827;
            padding: 1.5rem;
            border-radius: 0.5rem;
        }
        .section-container {
            display: flex;
            gap: 2rem;
            margin-bottom: 3rem;
            position: relative;
            padding-top: 3rem; /* Add padding to accommodate the legend */
        }
        .data-table {
            flex: 0 0 800px;
            background: #1f2937;
            border-radius: 0.5rem;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            height: auto;
            min-height: 300px;
            /* Add these new properties */
            transition: transform 0.2s ease-in-out;
            transform-origin: center center;
        }


        .data-table:hover {
            transform: translate(5px, -5px);
            box-shadow: -3px 3px 10px rgba(0, 0, 0, 0.3);
        }
        .charts-container {
            flex: 1;
            display: flex;
            justify-content: center;
            gap: 2rem;
            padding: 1rem;
            height: 300px;
        }
        .chart-wrapper {
            width: 340px;
            height: 340px;
            position: relative;
            padding: 0;
        }
        .chart-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            text-align: center;
            color: white;
        }
        .main-legend {
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 1.5rem;
            padding: 1rem;
            background-color: #111827; /* Match background color */
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            display: inline-block;
        }
        .legend-text {
            color: white;
            font-size: 14px;
            white-space: nowrap;      /* Prevent text wrapping */
            display: inline-block;     /* Allow transformation */
            transform-origin: center;  /* Rotate around center */
        }
        table {
            width: 100%;
            border-collapse: collapse;
            color: white;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            table-layout: fixed;
        }
        th, td {
            padding: 0.5rem 0.25rem;
            text-align: left;
            border-bottom: 1px solid #374151;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        td {
            white-space: nowrap;
        }
        th {
            font-size: 0.85rem;
            background-color: #374151;
            font-weight: bold;
            color: white;
            white-space: nowrap;
            text-align: center;  /* Change from 'left' to 'center' */
            padding: 0.5rem 0.25rem;
        }
        thead th {
            text-align: center !important;  /* This will ensure headers are centered regardless of other rules */
        }
        td.number-cell {
            text-align: right;
        }
        .difference {
            font-size: 0.875rem;
        }
        td .positive {
            color: #34D399;
        }
        td .negative {
            color: #F87171;
        }
        .impact-value {
            display: block;
            line-height: 1.4;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 0.8rem; /* Reduced font size for weekly hrs and yearly GBP */
        }
        .percentage-group, .number-group {
            text-align: right;
            border-left: 1px solid #4B5563;
            border-right: 1px solid #4B5563;
        }
        .separator {
            border-left: 2px solid #4B5563;
        }
        .weekly-diff, .yearly-diff {
            text-align: right;
            white-space: normal;
            font-size: 0.8rem; /* Reduced font size for these columns */
        }
        .footnotes {
            font-size: 0.75rem;
            color: #9CA3AF;
            margin-top: auto;
            padding-top: 1rem;
        }
        .footnotes p {
            margin: 0.25rem 0;
        }

        /* First, remove or modify existing alignment rules */
        td.number-cell {
            text-align: center; /* Change from 'right' to 'center' */
        }

        .percentage-group, .number-group {
            text-align: center; /* Change from 'right' to 'center' */
            border-left: 1px solid #4B5563;
            border-right: 1px solid #4B5563;
        }

        .weekly-diff, .yearly-diff {
            text-align: center; /* Change from 'right' to 'center' */
            white-space: normal;
            font-size: 0.8rem;
        }

        /* Add a new rule for all table cells except first column */
        td:not(:first-child) {
            text-align: center;
        }

        /* Keep the Type column left-aligned */
        td:first-child {
            text-align: left;
        }

        /* Center the impact values */
        .impact-value {
            display: block;
            line-height: 1.4;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 0.8rem;
            text-align: center; /* Add this line */
        }

        /* Updated column width specifications */
        th:nth-child(1) { width: 13%; } /* Type */
        th:nth-child(2), th:nth-child(3) { width: 9%; } /* As Is, To Be */
        th:nth-child(4) { width: 7%; } /* Diff */
        th:nth-child(5), th:nth-child(6), th:nth-child(7) { width: 7%; } /* Percentages */
        th:nth-child(8) { width: 14%; } /* Weekly Hrs */
        th:nth-child(9) { width: 14%; } /* Yearly GBP */
    </style>
</head>
<body>
    <div class="container">
        <div class="section-container">
            <div class="data-table">
                <h3 class="chart-title">Grocery Data</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th class="number-cell">As Is*</th>
                            <th class="number-cell">To Be**</th>
                            <th class="number-group">Diff</th>
                            <th class="separator percentage-group">As Is %</th>
                            <th class="percentage-group">To Be %</th>
                            <th class="percentage-group">Diff %</th>
                            <th class="weekly-diff">Weekly Hrs***</th>
                            <th class="yearly-diff">Yearly GBP***</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>NSRP Cases</td>
                            <td class="number-cell">1,038,754</td>
                            <td class="number-cell">1,309,900</td>
                            <td class="number-group"><span class="difference positive">+271,146</span></td>
                            <td class="separator percentage-group">36,7%</td>
                            <td class="percentage-group">39,3%</td>
                            <td class="percentage-group"><span class="difference positive">(+2,7%)</span></td>
                            <td class="weekly-diff">
                                <span class="impact-value difference positive">NSRP: +3,712</span>
                                <!-- <span class="impact-value difference positive">SRP: +2,547</span>
                                <span class="impact-value difference positive">Pallets: +1,626</span> -->
                            </td>
                            <td class="yearly-diff">
                                <span class="impact-value difference positive">NSRP: +1,423,649</span>
                                <!-- <span class="impact-value difference positive">SRP: +968,689</span>
                                <span class="impact-value difference positive">Pallets: +616,468</span> -->
                            </td>
                        </tr>
                        <tr>
                            <td>SRP Cases</td>
                            <td class="number-cell">1,099,914</td>
                            <td class="number-cell">1,272,427</td>
                            <td class="number-group"><span class="difference positive">+172,513</span></td>
                            <td class="separator percentage-group">38,8%</td>
                            <td class="percentage-group">38,2%</td>
                            <td class="percentage-group"><span class="difference negative">(-0,6%)</span></td>
                            <td class="weekly-diff">
                                <!-- <span class="impact-value difference negative">NSRP: -825</span> -->
                                <span class="impact-value difference negative">SRP: -566</span>
                                <!-- <span class="impact-value difference negative">Pallets: -361</span> -->
                            </td>
                            <td class="yearly-diff">
                                <!-- <span class="impact-value difference negative">NSRP: -316,366</span> -->
                                <span class="impact-value difference negative">SRP: -215,264</span>
                                <!-- <span class="impact-value difference negative">Pallets: -136,993</span> -->
                            </td>
                        </tr>
                        <tr>
                            <td>Full + Half Pallet Cases</td>
                            <td class="number-cell">695,192</td>
                            <td class="number-cell">745,926</td>
                            <td class="number-group"><span class="difference positive">+50,734</span></td>
                            <td class="separator percentage-group">24,5%</td>
                            <td class="percentage-group">22,4%</td>
                            <td class="percentage-group"><span class="difference negative">(-2,1%)</span></td>
                            <td class="weekly-diff">
                                <!-- <span class="impact-value difference negative">NSRP: -2,887</span>
                                <span class="impact-value difference negative">SRP: -1,980</span> -->
                                <span class="impact-value difference negative">Pallets: -1,265</span>
                            </td>
                            <td class="yearly-diff">
                                <!-- <span class="impact-value difference negative">NSRP: -1,107,282</span>
                                <span class="impact-value difference negative">SRP: -753,425</span> -->
                                <span class="impact-value difference negative">Pallets: -479,476</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="footnotes">
                    <p>* as is: average week based on 2023w14-w27</p>
                    <p>** to be: average week based on 2024w14-w27</p>
                    <p>*** Weekly Hrs and Yearly GBP: What the impact of Diff% would be in hours and GBP</p>
                </div>
            </div>
            <div class="charts-container">
                <div class="chart-wrapper">
                    <canvas id="groceryAsIs"></canvas>
                </div>
                <div class="chart-wrapper">
                    <canvas id="groceryToBe"></canvas>
                </div>
            </div>
        </div>

        <div class="section-container">
            <div class="data-table">
                <h3 class="chart-title">GM Data</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th class="number-cell">As Is*</th>
                            <th class="number-cell">To Be**</th>
                            <th class="number-group">Diff</th>
                            <th class="separator percentage-group">As Is %</th>
                            <th class="percentage-group">To Be %</th>
                            <th class="percentage-group">Diff %</th>
                            <th class="weekly-diff">Weekly Hrs***</th>
                            <th class="yearly-diff">Yearly GBP***</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>NSRP Cases</td>
                            <td class="number-cell">419,713</td>
                            <td class="number-cell">382,716</td>
                            <td class="number-group"><span class="difference negative">-36,997</span></td>
                            <td class="separator percentage-group">95,2%</td>
                            <td class="percentage-group">82,8%</td>
                            <td class="percentage-group"><span class="difference negative">(-12,5%)</span></td>
                            <td class="weekly-diff">
                                <span class="impact-value difference negative">NSRP: -2,641</span>
                                <!-- <span class="impact-value difference negative">SRP: -2,297</span>
                                <span class="impact-value difference negative">Pallets: -1,862</span> -->
                            </td>
                            <td class="yearly-diff">
                                <span class="impact-value difference negative">NSRP: -999,079</span>
                                <!-- <span class="impact-value difference negative">SRP: -870,046</span>
                                <span class="impact-value difference negative">Pallets: -706,823</span>
                            </td> -->
                        </tr>
                        <tr>
                            <td>SRP Cases</td>
                            <td class="number-cell">19,747</td>
                            <td class="number-cell">77,331</td>
                            <td class="number-group"><span class="difference positive">+57,584</span></td>
                            <td class="separator percentage-group">4,5%</td>
                            <td class="percentage-group">16,7%</td>
                            <td class="percentage-group"><span class="difference positive">(+12,2%)</span></td>
                            <td class="weekly-diff">
                                <!-- <span class="impact-value difference positive">NSRP: +2,578</span> -->
                                <span class="impact-value difference positive">SRP: +2,242</span>
                                <!-- <span class="impact-value difference positive">Pallets: +1,817</span> -->
                            </td>
                            <td class="yearly-diff">
                                <!-- <span class="impact-value difference positive">NSRP: +975,101</span> -->
                                <span class="impact-value difference positive">SRP: +849,166</span>
                                <!-- <span class="impact-value difference positive">Pallets: +689,859</span> -->
                            </td>
                        </tr>
                        <tr>
                            <td>Full + Half Pallet Cases</td>
                            <td class="number-cell">1,306</td>
                            <td class="number-cell">2,441</td>
                            <td class="number-group"><span class="difference positive">+1,135</span></td>
                            <td class="separator percentage-group">0,3%</td>
                            <td class="percentage-group">0,5%</td>
                            <td class="percentage-group"><span class="difference positive">(+0,2%)</span></td>
                            <td class="weekly-diff">
                                <!-- <span class="impact-value difference positive">NSRP: +42</span>
                                <span class="impact-value difference positive">SRP: +37</span> -->
                                <span class="impact-value difference positive">Pallets: +30</span>
                            </td>
                            <td class="yearly-diff">
                                <!-- <span class="impact-value difference positive">NSRP: +15,985</span>
                                <span class="impact-value difference positive">SRP: +13,920</span> -->
                                <span class="impact-value difference positive">Pallets: +11,309</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="footnotes">
                    <p>* as is: average week based on 2023w14-w27</p>
                    <p>** to be: average week based on 2024w22-w34</p>
                    <p>*** Weekly Hrs and Yearly GBP: What the impact of Diff% would be in hours and GBP</p>
                </div>
            </div>
            <div class="charts-container">
                <div class="chart-wrapper">
                    <canvas id="gmAsIs"></canvas>
                </div>
                <div class="chart-wrapper">
                    <canvas id="gmToBe"></canvas>
                </div>
            </div>
        </div>

        <div class="section-container">
            <div class="data-table">
                <h3 class="chart-title">Fresh Data</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th class="number-cell">As Is*</th>
                            <th class="number-cell">To Be**</th>
                            <th class="number-group">Diff</th>
                            <th class="separator percentage-group">As Is %</th>
                            <th class="percentage-group">To Be %</th>
                            <th class="percentage-group">Diff %</th>
                            <th class="weekly-diff">Weekly Hrs***</th>
                            <th class="yearly-diff">Yearly GBP***</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>NSRP Cases</td>
                            <td class="number-cell">405,787</td>
                            <td class="number-cell">398,324</td>
                            <td class="number-group"><span class="difference negative">-7,463</span></td>
                            <td class="separator percentage-group">33,9%</td>
                            <td class="percentage-group">30,3%</td>
                            <td class="percentage-group"><span class="difference negative">(-3,5%)</span></td>
                            <td class="weekly-diff">
                                <span class="impact-value difference negative">NSRP: -1,771</span>
                                <!-- <span class="impact-value difference negative">SRP: -1,238</span>
                                <span class="impact-value difference negative">Pallets: -865</span> -->
                            </td>
                            <td class="yearly-diff"> 
                                <span class="impact-value difference negative">NSRP: -688,791</span>                               
                                <!-- <span class="impact-value difference negative">SRP: -480,094</span>
                                <span class="impact-value difference negative">Pallets: -334,084</span> -->
                            </td>
                        </tr>
                        <tr>
                            <td>SRP Cases</td>
                            <td class="number-cell">709,645</td>
                            <td class="number-cell">828,425</td>
                            <td class="number-group"><span class="difference positive">+118,780</span></td>
                            <td class="separator percentage-group">59,3%</td>
                            <td class="percentage-group">63,1%</td>
                            <td class="percentage-group"><span class="difference positive">(+3,8%)</span></td>
                            <td class="weekly-diff">
                                <!-- <span class="impact-value difference positive">NSRP: +1,923</span> -->
                                <span class="impact-value difference positive">SRP: +1,343</span>
                                <!-- <span class="impact-value difference positive">Pallets: +939</span> -->
                            </td>
                            <td class="yearly-diff"> 
                                <!-- <span class="impact-value difference positive">NSRP: +747,830</span>                                -->
                                <span class="impact-value difference positive">SRP: +521,245</span>
                                <!-- <span class="impact-value difference positive">Pallets: +362,719</span> -->
                            </td>
                        </tr>
                        <tr>
                            <td>Full + Half Pallet Cases</td>
                            <td class="number-cell">82,236</td>
                            <td class="number-cell">86,349</td>
                            <td class="number-group"><span class="difference positive">+4,113</span></td>
                            <td class="separator percentage-group">6,9%</td>
                            <td class="percentage-group">6,6%</td>
                            <td class="percentage-group"><span class="difference negative">(-0,3%)</span></td>
                            <td class="weekly-diff">
                                <!-- <span class="impact-value difference negative">NSRP: -152</span>
                                <span class="impact-value difference negative">SRP: -106</span> -->
                                <span class="impact-value difference negative">Pallets: -74</span>
                            </td>
                            <td class="yearly-diff"> 
                                <!-- <span class="impact-value difference negative">NSRP: -59,039</span>                               
                                <span class="impact-value difference negative">SRP: -41,150</span> -->
                                <span class="impact-value difference negative">Pallets: -28,635</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="footnotes">
                    <p>* as is: average week based on 2023w14-w27</p>
                    <p>** to be: average week based on 2024w14-w27</p>
                    <p>*** Weekly Hrs and Yearly GBP: What the impact of Diff% would be in hours and GBP</p>
                </div>
            </div>
            <div class="charts-container">
                <div class="chart-wrapper">
                    <canvas id="freshAsIs"></canvas>
                </div>
                <div class="chart-wrapper">
                    <canvas id="freshToBe"></canvas>
                </div>
            </div>
        </div>

        <div class="main-legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #FF4444;"></div>
                <span class="legend-text">NSRP ratio</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #0088FE;"></div>
                <span class="legend-text">SRP ratio</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #FFD700;"></div>
                <span class="legend-text">Full + Half Pallet ratio</span>
            </div>
        </div>
    </div>

    <script>
        const COLORS = ['#FF4444', '#0088FE', '#FFD700'];
        const LABELS = ['NSRP ratio', 'SRP ratio', 'Full + Half Pallet ratio'];

        const createPieChart = (elementId, data, title, diffs = null) => {
            const ctx = document.getElementById(elementId);
            
            const config = {
                type: 'pie',
                data: {
                    labels: LABELS,
                    datasets: [{
                        data: data,
                        backgroundColor: COLORS,
                        borderWidth: 2,
                        borderColor: '#111827',
                        hoverBackgroundColor: COLORS,
                        hoverBorderColor: 'white',
                        hoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    layout: {
                        padding: 20
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: true,
                            backgroundColor: 'black',
                            titleColor: 'white',
                            bodyColor: 'white',
                            padding: 8,
                            displayColors: false,
                            callbacks: {
                                title: function(context) {
                                    return context[0].label;
                                },
                                label: function(context) {
                                    const value = context.raw;
                                    if (diffs) {
                                        const diff = diffs[context.dataIndex];
                                        const sign = diff > 0 ? '+' : '';
                                        return `${value}% (${sign}${diff}%)`;
                                    }
                                    return `${value}%`;
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: title,
                            color: 'white',
                            font: { size: 16 }
                        }
                    },
                    animation: {
                        animateRotate: true,
                        animateScale: true
                    },
                    elements: {
                        arc: {
                            hoverOffset: 20
                        }
                    }
                }
            };

            return new Chart(ctx, config);
        };
// Data definitions
const groceryAsIsData = [37, 39, 25];
        const groceryToBeData = [39, 38, 22];
        const groceryToBeDiffs = [2, -1, -3];

        const gmAsIsData = [95, 4, 0];
        const gmToBeData = [83, 17, 1];
        const gmToBeDiffs = [-12, 13, 1];

        const freshAsIsData = [34, 59, 7];
        const freshToBeData = [30, 63, 7];
        const freshToBeDiffs = [-4, 4, 0];

        // Create all charts when the page loads
        window.addEventListener('load', () => {
            createPieChart('groceryAsIs', groceryAsIsData, '(as is*)');
            createPieChart('groceryToBe', groceryToBeData, '(to be**)', groceryToBeDiffs);

            createPieChart('gmAsIs', gmAsIsData, '(as is*)');
            createPieChart('gmToBe', gmToBeData, '(to be**)', gmToBeDiffs);

            createPieChart('freshAsIs', freshAsIsData, '(as is*)');
            createPieChart('freshToBe', freshToBeData, '(to be**)', freshToBeDiffs);
        });
    </script>
</body>
</html>