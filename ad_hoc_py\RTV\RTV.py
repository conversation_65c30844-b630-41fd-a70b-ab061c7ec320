import pandas as pd
import numpy as np
import glob
import os
import re
import pyodbc
import polars as pl


pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)


# Directory containing the Excel files
directory_path = r"C:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\RTV\HU"

# List all Excel files in the directory
excel_files = glob.glob(os.path.join(directory_path, "*.xlsx"))

# Initialize an empty list to store the melted DataFrames
melted_dfs = []


# Function to check and remove empty first row and first column
def clean_first_row_col(df):
    if df.iloc[0].isnull().all():
        df = df.drop(0).reset_index(drop=True)
    if df.iloc[:, 0].isnull().all():
        df = df.drop(df.columns[0], axis=1)
    return df

# Iterate through each Excel file
for file_path in excel_files:
    # Load the first sheet of the Excel file into a pandas DataFrame
    df = pd.read_excel(file_path, sheet_name=0)
    
    # Clean the first row and first column if they are empty
    df = clean_first_row_col(df)

    # Get the value of cell A1 for the 'week' column
    week_value = df.columns[0].rstrip()

    # Find the index of the column "Össz. Javasolt db."
    javasolt_db_col_index = df.columns[df.iloc[1] == "Össz. Javasolt db."].tolist()[0]

    # Get the index of the column with "Javasolt menny." and replace them with the row above values
    for col in df.columns[df.columns.get_loc(javasolt_db_col_index) + 1:]:
        df.at[1, col] = df.at[0, col]

    # Drop the first row and set the second row as column names
    df.columns = df.iloc[1]
    df = df.drop([0, 1]).reset_index(drop=True)

    # Add the 'week' column with the value of cell A1
    df['week'] = week_value

    # Rearrange columns to have 'week' as the first column
    cols = df.columns.tolist()
    cols = cols[-1:] + cols[:-1]
    df = df[cols]

    # Ensure column names are strings
    df.columns = df.columns.map(str)

    # Identify id_vars as columns whose names are not numeric
    id_vars = [col for col in df.columns if not col.isnumeric()]

    # Melt the DataFrame
    df_melted = df.melt(id_vars=id_vars, var_name='store', value_name='values').sort_values(by="store")

    # Append the melted DataFrame to the list
    melted_dfs.append(df_melted)
    
    print(f"Done with {file_path}\n")

# Concatenate all melted DataFrames into one big DataFrame
final_df = pd.concat(melted_dfs, ignore_index=True)


# Assuming df is your dataframe
columns_to_keep = list(final_df.columns[:final_df.columns.get_loc("values")+1])
final_df = final_df[columns_to_keep]
final_df = final_df.replace(np.nan,0)
final_df = final_df[final_df['Tpn'].astype("str") != '0']



# Function to extract week number and format it
def extract_week_number(week_str):
    # Extract numbers with optional prefix
    match = re.search(r'(\d{2,4}_)?(\d{1,2})', week_str)
    if match:
        prefix = match.group(1) if match.group(1) else ""
        week_number = match.group(2)
        
        # Determine year based on week number
        if not prefix:
            week_number = int(week_number)
            if week_number <= 32:
                year = 2024
            else:
                year = 2023
            return f"{year}_{week_number:02}"
        else:
            return f"{prefix}{week_number}"
    return None

# Apply the function to create a new column
final_df['formatted_week'] = final_df['week'].apply(extract_week_number)
final_df["TVH"] = np.where(final_df.Tpn.astype("str").str.contains('TVH'), "EPW", 0)
final_df.loc[(final_df["values"] == "-"), "values"] = 0
final_df.loc[(final_df["values"] < 0), "values"] = 0


# Function to clean the column values
def clean_value(value):
    if isinstance(value, str):
        cleaned_value = value.strip().lower()
        if 'raktári' in cleaned_value:
            return 'Raktári'
        elif 'direkt' in cleaned_value or 'direct' in cleaned_value:
            return 'Direct'
    return value

# Apply the function to the column
final_df['Raktári vagy/Direkt'] = final_df['Raktári vagy/Direkt'].apply(clean_value)
final_df.loc[final_df['Raktári vagy/Direkt'] == 0, "Raktári vagy/Direkt"] = "EPW"

final_df.rename(columns={"TPNB":"tpnb"}, inplace=True)
final_df['country'] = "HU"


conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()


products = final_df[['country', 'tpnb']].drop_duplicates()
products = products.groupby(["country"])['tpnb'].apply(lambda s: s.tolist()).to_dict()

for key, value in products.items():
    #print value
    print(key, len([item for item in value if item]))

df2 = pl.DataFrame()

for k, v in products.items():
                    
    s = list()
    
    for x in v:
                
        s.append(str(x))
        tpnbs = ",".join(s)
    
    sql = """ SELECT 
                    cntr_code AS country,
                    cast(slad_tpnb AS INT) AS tpnb,
                    hier.pmg as pmg
                    /*slad_tpn AS tpn,
                    slad_unit AS unit_type,
                    slad_case_size AS case_capacity,
                    slad_net_weight AS weight,
                    dmat_div_des_en AS division,
                    cast(dmat_div_code as INT) as DIV_ID,
                    dmat_dep_des_en AS department,
                    cast(dmat_dep_code as INT) as DEP_ID,
                    dmat_sec_des_en AS section,
                    cast(dmat_sec_code as INT) as SEC_ID,
                    dmat_grp_des_en AS group,
                    cast(dmat_grp_code as INT) as GRP_ID,
                    dmat_sgr_des_en AS subgroup,
                    cast(dmat_sgr_code as INT) as SGR_ID,
                    slad_long_des as product_name*/
            FROM
                    DM.dim_artgld_details mstr
                    
            JOIN
                    tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
                    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
            WHERE 
                    slad_tpnb in ({tpnbs}) 
            AND     
                    cntr_code = '{k}' 
            AND     
                    dmat_sgr_des_en <> "Do not use"
            GROUP BY 
                    cntr_code,
                    slad_tpnb,
                    hier.pmg
                    /*slad_tpn,
                    slad_unit,
                    slad_case_size,
                    slad_net_weight,
                    dmat_div_des_en,
                    dmat_div_code,
                    dmat_dep_des_en,
                    dmat_dep_code,
                    dmat_sec_des_en,
                    dmat_sec_code,
                    dmat_grp_des_en,
                    dmat_grp_code,
                    dmat_sgr_des_en,
                    dmat_sgr_code,
                    slad_long_des*/
                    
                    """.format(
        tpnbs=tpnbs, k=k
    )

    df2 =pl.concat([df2, pl.read_database(sql, conn)])
    
df2 = df2.to_pandas()

final_df = final_df.merge(df2, on=["country", "tpnb"], how="left")

final_df["dep"] = final_df["pmg"].str[:3]

final_df.loc[(final_df.tpnb !=0)&(final_df["pmg"].isnull()), "dep"] = "HDL"
final_df["store_tpnb"] = np.where((final_df.tpnb != 0) & (final_df["values"] != 0), 1, 0)
final_df =  final_df[(final_df["values"] != 0)]
final_df["store"] = final_df["store"].astype("int")


#whatif-no suppliers what GM team gave us
# garafarm_storelist = [
#     41008, 41009, 41014, 41015, 41025, 41037, 41059, 41400, 41540, 41560,
#     41640, 41710, 41890, 41970, 41029, 41420, 41620, 41001, 41002, 41003,
#     41005, 41006, 41007, 41012, 41019, 41021, 41022, 41024, 41031, 41034,
#     41036, 41038, 41044, 41051, 41053, 41410, 41440, 41450, 41460, 41470,
#     41490, 41500, 41530, 41550, 41580, 41600, 41610, 41650, 41660, 41670,
#     41680, 41700, 41730, 41740, 41760, 41770, 41790, 41800, 41810, 41860,
#     41900, 41940, 41950, 41990
# ]

# # Keywords to exclude
# exclude_keywords = [ "cardex", "intersett", "nekupto", ]

# # Create conditions for filtering
# condition_gara = ~((final_df['Szállító'].astype(str).str.lower().str.contains('gara')) & (final_df['store'].isin(garafarm_storelist)))
# condition_hbs_lapker = ~((final_df['Szállító'].astype(str).str.lower().str.contains('hbs|lapker')) & (final_df['store'] < 43000))
# condition_others = ~final_df['Szállító'].astype(str).str.lower().str.contains('|'.join(exclude_keywords))

# # Combine conditions
# final_condition = condition_gara & condition_hbs_lapker & condition_others

# # Apply the combined condition to filter the DataFrame
# final_df = final_df[final_condition]




final_df = final_df.groupby(["formatted_week","store","Raktári vagy/Direkt"],as_index=False).agg({"values":"sum","Szállítószám":"nunique", 'store_tpnb':"sum"})



final_df = final_df.pivot_table(index=["formatted_week","store","Szállítószám", "store_tpnb"],columns="Raktári vagy/Direkt", values="values", fill_value=0).reset_index()\
        .sort_values(by=["formatted_week", "store"])
        
        
#to save in excel
final_df.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_07\RTV_HU_wo_suppliers_GM.xlsx",index=False)
            
