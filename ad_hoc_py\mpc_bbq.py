      
        
        
import pandas as pd
from pathlib import Path
import pyodbc
import glob
import polars as pl
import sys
import os
import time
import numpy as np

sys.path.append(os.path.dirname(Path.cwd()))
import Replenishment_Model_Functions_23 as rmf
import Get_System_Data_SQL as gsd

import warnings

warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)


time_start = time.time()


######### for SQL queries ##########
saved_filename = "MPC_BBQ"  # it will be the dataset name
replace_pmg = "PPD01"


start_date = "'f2023w18'"
end_date = "'f2023w22'"
nr_weeks = int(end_date[7:9]) - int(start_date[7:9]) + 1
date_plan = 20230813  # for planogram file
####################################


directory = (
    Path(__file__).parent if "__file__" in locals() else Path.cwd()
).parent

place_to_save = Path(directory / f"inputs/files_for_dataset/{saved_filename}")
broken_case = False

excel_inputs_f = "inputs/Repl/Stores_Inputs_2023_Q1_Vol1.xlsx"
box_op_type_f = "inputs/files_for_dataset/ownbrand_opening_type_2022_mod.xlsx"
broken_cases_f = "inputs/files_for_dataset/broken_cases_list_22w14_27.csv.gz"
catres_path = r"others\CategoryReset\Category Reset CE_hier.xlsx"


stores = list(
    pd.read_excel(directory / excel_inputs_f, usecols=["Country", "Format", "Store"])[
        "Store"
    ].unique()
)

MPC_bbq = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_08\MFP+BBQ SRP proposals.xlsx", sheet_name="ce")


products_to_fetch = MPC_bbq.groupby(["country"])["tpnb"].apply(lambda s: s.tolist()).to_dict()


print("\n###########")
print("JDA/Planogram has started to download...")
print("###########\n")

SRD_tables = gsd.SRD_database_TPNB(products_to_fetch)

SRD_tables = gsd.SRD_table_added_hierarchy(SRD_tables)

country_cond = [
    SRD_tables["Country"] == "HU",
    SRD_tables["Country"] == "SK",
    SRD_tables["Country"] == "CZ",
]

store_code_create = [
    str(4) + SRD_tables.Store_Number.astype(str),
    str(2) + SRD_tables.Store_Number.astype(str),
    str(1) + SRD_tables.Store_Number.astype(str),
]


SRD_tables["Store_Number"] = np.select(country_cond, store_code_create, 0)



conn = pyodbc.connect(
    "DSN=UKHadoop_CE_spark", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()


item_sold_df = pd.DataFrame()
item_sold_dotcom_df = pd.DataFrame()
stock_df = pd.DataFrame()
cases_delivered_df = pd.DataFrame()
pallet_cap_df = pd.DataFrame()

print("\n###########")
print("Item Sold Downloading for new Products")
print("###########\n")
for k, v in products_to_fetch.items():

    s = list()
    for x in v:

        s.append(str(x))

    tpnbs = tuple(s)

    # tpnbs = ",".join(s)



    item_sold = """
            SELECT 
            cast(stores.dmst_store_code as INT) AS store,
            cal.dtdw_day_desc_en as day,  hier.pmg AS pmg,
            cast(mstr.slad_tpnb as INT) AS tpnb,
            mstr.own_brand as ownbrand,
            mstr.slad_long_des AS product_name,
            mstr.slad_unit AS unit_type,
            mstr.slad_case_size AS case_capacity,
            mstr.slad_net_weight AS weight,
            SUM(sunit.slsms_unit)/{nr_weeks} AS sold_units,
            SUM(sunit.slsms_salex)/{nr_weeks} AS sales_excl_vat 
            FROM dw.sl_sms sunit 
            JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id 
            LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
            JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id
            AND mstr.cntr_id = sunit.slsms_cntr_id 
            JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
            AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
            AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
            AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
            AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
            WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
            AND stores.cntr_code = '{k}'
            AND sunit.slsms_unit > 0 
            AND sunit.slsms_salex > 0 
            AND mstr.slad_tpnb in {tpnbs}
            AND stores.convenience IN ('Convenience', 'HM')
            GROUP BY  stores.dmst_store_code, cal.dtdw_day_desc_en,  hier.pmg, mstr.slad_tpnb, mstr.own_brand, mstr.slad_long_des, mstr.slad_unit, mstr.slad_case_size, mstr.slad_net_weight  /* ,mstr.cntr_prod_origin, mstr.slad_dmsup_id, sunit.slsms_dmcs_type*/
            ORDER BY  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb, cal.dtdw_day_desc_en
                """.format(
        start=start_date, end=end_date, k=k, nr_weeks=nr_weeks, tpnbs=tpnbs
    )

    sold_units = pd.read_sql(item_sold, conn)
    item_sold_df = pd.concat([item_sold_df, sold_units])

    print(f"\nItem Sold Done with {k}\n")

print("\n###########")
print("Item Sold DotCom Downloading for new Products")
print("###########\n")
for k, v in products_to_fetch.items():

    s = list()
    for x in v:

        s.append(str(x))

    tpnbs = tuple(s)
    isold_dotcom = """
            SELECT 
            cast(stores.dmst_store_code as INT) AS store,
            cal.dtdw_day_desc_en as day,
            hier.pmg AS pmg,
            cast(mstr.slad_tpnb as INT) AS tpnb,
            SUM(sunit.sltrg_tr_unit)/{nr_weeks} AS sold_units_dotcom
            FROM dw.sl_trg sunit 
            JOIN dm.dim_stores stores ON stores.cntr_id = sunit.sltrg_cntr_id AND stores.dmst_store_id = sunit.sltrg_dmst_id 
            LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
            JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.sltrg_dmat_id
            AND mstr.cntr_id = sunit.sltrg_cntr_id 
            JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
            AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
            AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
            AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
            AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
            WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
            AND stores.cntr_code = '{k}'
            AND sunit.sltrg_tr_unit > 0 
            AND mstr.slad_tpnb in {tpnbs}
            AND stores.convenience IN ('Convenience', 'HM')
            GROUP BY  stores.dmst_store_code, cal.dtdw_day_desc_en, hier.pmg,  mstr.slad_tpnb
            ORDER BY  stores.dmst_store_code,  hier.pmg, mstr.slad_tpnb, cal.dtdw_day_desc_en
            """.format(
        start=start_date, end=end_date, k=k, nr_weeks=nr_weeks, tpnbs=tpnbs
    )

    sold_units_dotcom = pd.read_sql(isold_dotcom, conn)
    item_sold_dotcom_df = pd.concat([item_sold_dotcom_df, sold_units_dotcom])
    print(f"\nSold Unit Dotcom Done with {k}\n")

print("\n###########")
print("Stock Downloading for new Products")
print("###########\n")
for k, v in products_to_fetch.items():

    s = list()
    for x in v:

        s.append(str(x))

    tpnbs = tuple(s)

    stock_sql = """
            SELECT 
            CAST(stores.dmst_store_code AS INT) AS store,
            cal.dtdw_day_desc_en as day,
            hier.pmg AS pmg,
            CAST(mstr.slad_tpnb AS INT) AS tpnb,
            SUM(stock.slstks_stock_unit_sl)/{nr_weeks} AS stock,
            AVG(stock.slstks_price) as item_price
            FROM dw.sl_stocks stock
            JOIN dm.dim_stores stores ON stock.slstks_dmst_id = stores.dmst_store_id AND stock.slstks_cntr_id = stores.cntr_id
            JOIN dm.dim_artgld_details mstr ON mstr.slad_dmat_id = stock.slstks_dmat_id AND mstr.cntr_id = stock.slstks_cntr_id
            LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = stock.part_col
            JOIN tesco_analysts.hierarchy_spm hier
            ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0")
            AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0")
            AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0")
            AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0")
            AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
            WHERE cal.dmtm_fw_code BETWEEN {start} AND {end}
            AND stores.cntr_code = '{k}'
            AND mstr.slad_tpnb in {tpnbs}
            AND stock.slstks_stock_unit_sl > 0
            GROUP BY stores.dmst_store_code, hier.pmg, cal.dtdw_day_desc_en, mstr.slad_tpnb
            ORDER BY stores.dmst_store_code, hier.pmg, mstr.slad_tpnb
                """.format(
        start=start_date, end=end_date, k=k, nr_weeks=nr_weeks, tpnbs=tpnbs
    )

    stock = pd.read_sql(stock_sql, conn)
    stock_df = pd.concat([stock_df, stock])
    print(f"\nStock Done with {k}\n")

print("\n###########")
print("Cases Delivered Downloading for new Products")
print("###########\n")
for k, v in products_to_fetch.items():

    s = list()
    for x in v:

        s.append(str(x))

    tpnbs = tuple(s)

    Cases_delivered = """
            select 
            CAST(CONCAT(cases.int_cntr_id, cases.store) AS INT) as store,
            cal.dtdw_day_desc_en as day,
            CAST(cases.product as INT) as tpnb,
            mstr.slad_long_des AS product_name,
            hier.pmg AS pmg,
            SUM(cases.qty)/{nr_weeks} as unit,
            AVG(mstr.slad_case_size) as artgld_case_capacity
            from stg_go.go_106_order_receiving cases
            LEFT JOIN dm.dim_artgld_details mstr ON mstr.slad_tpnb = cases.product and mstr.cntr_id = cases.int_cntr_id
            Right JOIN tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = lpad(hier.div_code,4,"0")
            AND mstr.dmat_dep_code = lpad(hier.dep_code,4,"0")
            AND mstr.dmat_sec_code = lpad(hier.sec_code,4,"0")
            AND mstr.dmat_grp_code = lpad(hier.grp_code,4,"0")
            AND mstr.dmat_sgr_code = lpad(hier.sgr_code,4,"0")
            LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = cases.part_col
            WHERE cases.int_cntr_code = '{k}'
            and cases.product in {tpnbs}
            and cal.dmtm_fw_code BETWEEN {start} AND {end}
            GROUP BY  CONCAT(cases.int_cntr_id, cases.store), cal.dtdw_day_desc_en, cases.product, mstr.slad_long_des, hier.pmg
            ORDER BY  CONCAT(cases.int_cntr_id, cases.store), cal.dtdw_day_desc_en, hier.pmg
            """.format(
        start=start_date, end=end_date, k=k, nr_weeks=nr_weeks, tpnbs=tpnbs
    )

    cases = pd.read_sql(Cases_delivered, conn)
    cases_delivered_df = pd.concat([cases_delivered_df, cases])

    print(f"\nCases Done with {k}\n")

print("\n###########")
print("Pallet Capacity Downloading for new Products")
print("###########\n")
for k, v in products_to_fetch.items():

    s = list()
    for x in v:

        s.append(str(x))

    tpnbs = tuple(s)

    pallet_cap = """
            SELECT CAST(table1.tpnb AS INT) AS tpnb,
            table1.pmg AS pmg,
            table1.supplier_id AS supplier_id,
            table1.case_size AS case_size,
            table1.case_type AS case_type,
            table1.country AS country,
            table1.pallet_capacity AS pallet_capacity,
            MAX(year) AS year
            FROM
            (SELECT 
             tpns.slad_tpnb AS tpnb,
             hier.pmg AS pmg,
             pc.lnass_dmsup_id AS supplier_id, 
             pc.lnass_case_size AS case_size,
             pc.lnass_pur_unit AS case_type, 
             pc.lnass_no_cart_p AS pallet_capacity,
            tpns.cntr_code AS country,
            max(date_format(pc.lnass_upd_date,'yyyy')) AS year
            FROM syln{k}.ln_art_sup_st pc
            LEFT JOIN dm.dim_artgld_details tpns ON pc.lnass_dmat_id = tpns.slad_dmat_id
            LEFT JOIN tesco_analysts.hierarchy_spm hier
            ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0")
            AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0")
            AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0")
            AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0")
            AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
            WHERE pc.lnass_no_cart_p > 0
            AND tpns.cntr_code = '{k}'
            AND tpns.slad_tpnb in {tpnbs}
            GROUP BY tpns.slad_tpnb, hier.pmg, pc.lnass_dmsup_id, pc.lnass_case_size, pc.lnass_pur_unit, pc.lnass_no_cart_p, tpns.cntr_code) table1
            WHERE table1.year IS NOT NULL
            AND table1.pmg IS NOT NULL
            GROUP BY table1.tpnb, table1.pmg, table1.supplier_id, table1.case_size, table1.case_type, table1.country, table1.pallet_capacity
            ORDER BY table1.country, table1.pmg, table1.tpnb, table1.pallet_capacity
            
            
            
        
            """.format(
        k=k, tpnbs=tpnbs
    )

    pallet = pd.read_sql(pallet_cap, conn)
    pallet_cap_df = pd.concat([pallet_cap_df, pallet])
    pallet_cap_df = (
        pallet_cap_df.groupby(["country", "tpnb"])["pallet_capacity"]
        .mean()
        .reset_index()
    )

    print(f"\nPallet Capacity Done with {k}\n")


store_inputs = rmf.Store_Inputs_Creator(directory, excel_inputs_f, stores)

# opsdev part

opsdev = SRD_tables.copy()

del SRD_tables

opsdev, foil = gsd.SRD_to_opsdev(opsdev, directory, excel_inputs_f)

a = opsdev.copy()



planogram_df = gsd.planogram_compiling(date_plan, saved_filename, place_to_save)


for x in [item_sold_df,item_sold_dotcom_df,stock_df,cases_delivered_df]:
    
    x['pmg'] = replace_pmg
    
    
    
# item_sold_df = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\MPC_BBQ\item_sold_df")
# item_sold_dotcom_df = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\MPC_BBQ\item_sold_dotcom_df")
# stock_df = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\MPC_BBQ\stock_df")
# cases_delivered_df = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\MPC_BBQ\cases_delivered_df")
# opsdev =  pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\MPC_BBQ\opsdev")
# planogram_df = pd.read_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\MPC_BBQ\planogram_df")



# for x, z in zip([item_sold_df,item_sold_dotcom_df,stock_df,cases_delivered_df, opsdev, planogram_df ],['item_sold_df',
#                                                                                                     'item_sold_dotcom_df',
#                                                                                                     'stock_df',
#                                                                                                     'cases_delivered_df',
#                                                                                                     'opsdev', 'planogram_df']):
    
#     x.to_parquet(fr"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\MPC_BBQ\{z}", compression="gzip")
    
    
    
    
    
with pl.StringCache():
    print("\n###########")
    print("Repl_Dataset Build: has been started to calculate...")
    print("###########\n")

    # Creating Base for Repl_Dataset
    weekdays = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
    ]
    store_inputs_lower = store_inputs.copy()
    store_inputs_lower.columns = [i.lower() for i in store_inputs_lower.columns]
    store_inputs_lower = store_inputs_lower.rename(columns={"pmg name": "pmg_name"})

    store_inputs_lower = store_inputs_lower[
        ["store", "country", "format", "division", "pmg", "dep", "is_capping_shelf"]
    ].drop_duplicates()
    store_inputs_lower = pl.from_pandas(store_inputs_lower)

    # planogram = pl.read_csv(directory / planogram_f, sep=',', ignore_errors=True).lazy()
    planogram = pl.from_pandas(planogram_df)
    planogram = planogram.select(["store", "tpnb", "icase", "capacity"])
    planogram = planogram.unique(subset=["tpnb", "icase", "store", "capacity"])
    planogram = planogram.with_columns(
        [pl.col("tpnb").cast(pl.Int64), pl.col("store").cast(pl.Int64)]
    )

    # opsdev = pl.read_parquet(directory / ops_dev_f).lazy()
    opsdev = pl.from_pandas(opsdev).with_columns(pl.col('tpnb').cast(pl.Int64),
                                                 pl.col('store').cast(pl.Int64))
    opsdev = opsdev.with_columns(pl.lit(0).alias('frozen_srp')) # once there is system data it should be deleted
    opsdev = opsdev.with_columns(pl.lit(0).alias('single_pick')) # once there is system data it should be deleted

    opsdev = opsdev.select(
        [
            "store",
            "tpnb",
            "srp",
            "nsrp",
            "mu",
            "full_pallet",
            "split_pallet",
            "frozen_srp",
            "single_pick",
            "checkout_stand_flag",
            "clipstrip_flag",
            "backroom_flag",
        ]
    )

    # stock = pl.read_parquet(directory / stock_f).lazy()
    stock = pl.from_pandas(stock_df)
    stock = stock.select(["store", "day", "tpnb", "stock", "item_price"])

    # isold = pl.read_parquet(directory / items_sold_f).lazy()
    isold = pl.from_pandas(item_sold_df)
    # isold_dotcom = pl.read_parquet(directory / items_sold_dotcom).lazy()
    isold_dotcom = pl.from_pandas(item_sold_dotcom_df)
    isold_dotcom = isold_dotcom.select(["store", "day", "tpnb", "sold_units_dotcom"])

    # cases = pl.read_parquet(directory / cases_f).lazy()
    cases_delivered_df = cases_delivered_df[
        cases_delivered_df.artgld_case_capacity.notnull()
    ]
    cases = pl.from_pandas(cases_delivered_df)
    cases = cases.select(["store", "day", "tpnb", "unit", "artgld_case_capacity"])
    cases = cases.with_columns(
        [(pl.col("unit") / pl.col("artgld_case_capacity")).alias("cases_delivered")]
    )

    op_type = pl.read_excel(directory / box_op_type_f)
    # pallet_cap = pl.read_parquet(directory / pallet_capacity_f)
    pallet_cap = pl.from_pandas(pallet_cap_df)

    print("Repl_Dataset Build: Inputs are loaded into Memory!")

    Repl_Dataset = isold.select(["store", "pmg", "tpnb"]).unique()
    Repl_Dataset = Repl_Dataset.with_columns([pl.lit(None).alias("day")])
    Repl_Dataset = Repl_Dataset.with_columns(
        [pl.col("day").map(lambda s: weekdays).alias("day")]
    )
    Repl_Dataset = Repl_Dataset.explode("day")
    Repl_Dataset = Repl_Dataset.unique()
    Repl_Dataset = Repl_Dataset.join(
        isold.select(["store", "pmg", "tpnb", "day", "sold_units", "sales_excl_vat"]),
        on=["store", "pmg", "tpnb", "day"],
        how="left",
    )
    Repl_Dataset = Repl_Dataset.join(
        isold.select(
            [
                pl.all().exclude(
                    ["sold_units", "sales_excl_vat", "day", "__index_level_0__"]
                )
            ]
        ).unique(),
        on=["store", "pmg", "tpnb"],
        how="left",
    )
    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when(pl.col("pmg") == "DRY18")
            .then("DRY15")
            .otherwise(pl.col("pmg"))
            .alias("pmg")
        ]
    )
    Repl_Dataset = Repl_Dataset.with_columns([pl.col("day").cast(pl.Utf8)])
    Repl_Dataset = Repl_Dataset.join(
        store_inputs_lower, on=["store", "pmg"], how="left"
    )
    Repl_Dataset = Repl_Dataset.join(
        isold_dotcom, on=["store", "day", "tpnb"], how="left"
    )
    Repl_Dataset = Repl_Dataset.join(stock, on=["store", "day", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.join(opsdev, on=["tpnb", "store"], how="left")
    Repl_Dataset = Repl_Dataset.join(planogram, on=["store", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.join(cases, on=["store", "day", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.join(op_type, on=["country", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.join(pallet_cap, on=["country", "tpnb"], how="left")
    Repl_Dataset = Repl_Dataset.fill_null(0)

    Repl_Dataset = (
        Repl_Dataset.with_columns(
            [
                pl.when(
                    (pl.col("srp") == 0)
                    & (pl.col("nsrp") == 0)
                    & (pl.col("full_pallet") == 0)
                    & (pl.col("mu") == 0)
                    & (pl.col("split_pallet") == 0)
                    & (pl.col("frozen_srp") == 0)
                    & (pl.col("single_pick") == 0)
                )
                .then(1)
                .otherwise(pl.col("nsrp"))
                .alias("nsrp"),
                pl.when(pl.col("icase") == 0)
                .then(pl.col("case_capacity"))
                .otherwise(pl.col("icase"))
                .alias("icase"),
            ]
        )
        .drop("case_capacity")
        .rename({"icase": "case_capacity"})
    )

    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when(pl.col("case_capacity") < pl.col("artgld_case_capacity"))
            .then(pl.col("artgld_case_capacity"))
            .otherwise(pl.col("case_capacity"))
            .alias("case_capacity")
        ]
    )

    Repl_Dataset = (
        Repl_Dataset.with_columns(
            [(pl.col("unit") / pl.col("case_capacity")).alias("cases_delivered")]
        )
        .drop("artgld_case_capacity")
        .fill_null(0)
    )

    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when(pl.col("unit_type") != "KG")
            .then("SNGL")
            .otherwise(pl.col("unit_type"))
            .alias("unit_type")
        ]
    )

    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when(pl.col("dep") == "NEW")
            .then("HDL")
            .otherwise(pl.col("dep"))
            .alias("dep")
        ]
    )
    Repl_Dataset = Repl_Dataset.with_columns(
        [pl.col("pallet_capacity").round(0).alias("pallet_capacity")]
    )

    Repl_Dataset = Repl_Dataset.with_columns(
        [
            pl.when((pl.col("srp") > 0) & (pl.col("pmg").str.contains("FRZ")))
            .then(1)
            .otherwise(pl.col("nsrp"))
            .alias("nsrp"),
            pl.when((pl.col("srp") > 0) & (pl.col("pmg").str.contains("FRZ")))
            .then(0)
            .otherwise(pl.col("srp"))
            .alias("srp"),
        ]
    )

    if broken_case == False:

        broken_cases_list = pl.read_csv(directory / broken_cases_f)
        Repl_Dataset = Repl_Dataset.join(
            broken_cases_list, on=["store", "day", "tpnb"], how="left"
        ).fill_null(0)

    Repl_Dataset = Repl_Dataset.to_pandas()
    int_list = [
        "srp",
        "nsrp",
        "mu",
        "full_pallet",
        "split_pallet",
        "frozen_srp",
        "single_pick",
        "checkout_stand_flag",
        "backroom_flag",
        "clipstrip_flag",
        "case_capacity",
        "is_capping_shelf",
        "capacity",
    ]  #
    Repl_Dataset[int_list] = Repl_Dataset[int_list].apply(lambda x: x.astype("int32"))
    int_part = rmf.optimize_types(Repl_Dataset.select_dtypes(include=["float", "int"]))
    cat_part = Repl_Dataset.select_dtypes(exclude=["float", "int"])
    Repl_Dataset = pd.concat([int_part, cat_part], axis=1)

    
    Repl_Dataset = Repl_Dataset[Repl_Dataset.country.notnull()]
    Repl_Dataset["as_is_model_contains?"] = 'N'
    Repl_Dataset[Repl_Dataset['opening_type'].isnull(), 'opening_type'] = 'not Ownbrand'
    Repl_Dataset = rmf.optimize_objects(rmf.optimize_types(Repl_Dataset))
    
    catres_df = gsd.category_reset_to_df(directory, catres_path)
    # catres_df = rmf.optimize_objects(rmf.optimize_types(catres_df))
    catres_df[['tpnb', 'tpn']] = catres_df[['tpnb','tpn']].astype("int64")
    Repl_Dataset = Repl_Dataset.merge(catres_df[['country','tpnb', 'Category name']].drop_duplicates(), on=['country','tpnb'], how='left')

    
    
    Repl_Dataset.to_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\MPC_BBQ\mpc_bbq_dataset", compression="gzip")
    
    

    
    
