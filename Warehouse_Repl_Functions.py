import pandas as pd
import numpy as np
import pyarrow.parquet as pq
from pathlib import Path
from Replenishment_Model_Functions_25 import timeit
import polars as pl


@timeit
def WareHouse_Drivers_Profiles(
    directory,
    excel_inputs_f,
    wh_prof_drivers,
    wh_cases_pmg,
    wh_pattisserie,
    wh_deliveries,
    stores,
):

    pd.set_option("display.max_columns", None)

    # Initialize store_inputs and days
    store_inputs = pl.read_excel(directory / excel_inputs_f, engine="calamine").to_pandas().iloc[:, :4]
    # store_inputs = pd.read_excel(directory / excel_inputs_f).iloc[:, :4]
    store_inputs = store_inputs[store_inputs.Store.isin(stores)]
    weekdays = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
    ]
    store_inputs["day"] = ""
    store_inputs["day"] = store_inputs["day"].apply(lambda x: weekdays)
    store_inputs_wh = store_inputs.explode("day").drop_duplicates()

    # Initialize WH cases and deliveries to be one table
    # wh_profile = pd.read_excel(directory / wh_prof_drivers, skiprows=9).iloc[:, 1:]
    wh_profile = pl.read_excel(directory / wh_prof_drivers, engine="calamine",  read_options={"header_row": 1}).to_pandas().iloc[:, 1:]

    wh_profile = wh_profile[wh_profile.Store.isin(stores)]
    # wh_drivers = pd.read_excel(directory / wh_prof_drivers, "Drivers", skiprows=1).drop(
    #     ["Plan size", "Country", "d", "Store Name"], axis=1
    # )
    wh_drivers = pl.read_excel(directory / wh_prof_drivers, sheet_name="Drivers",engine="calamine",  read_options={"header_row": 1} ).to_pandas().drop(
        ["Plan size", "Country", "d", "Store Name"], axis=1
    )
    wh_drivers = wh_drivers[wh_drivers.Store.isin(stores)]
    wh_cases_data = (
        pq.read_table(Path(directory / wh_cases_pmg), filters=[("store", "in", stores)])
        .to_pandas()
        .rename(
            columns={
                "total_wh_cases": "Cases delivered",
                "wh_cases_DTS": "Weekly direct cased delivered",
                "wh_cases_DTW": "Weekly DC cased delivered",
                "store": "Store",
            }
        )
    )
    wh_cases_data = wh_cases_data[wh_cases_data.Store.isin(stores)]

    wh_cases_data_pattisserie = (
        pq.read_table(
            Path(directory / wh_pattisserie), filters=[("store", "in", stores)]
        )
        .to_pandas()
        .rename(
            columns={
                "total_wh_cases": "Cases delivered",
                "wh_cases_DTS": "Weekly direct cased delivered",
                "wh_cases_DTW": "Weekly DC cased delivered",
                "store": "Store",
                "sec": "pmg",
            }
        )
        .query('pmg == "Patisserie_Cake"')
    )
    wh_cases_data_pattisserie = wh_cases_data_pattisserie[
        wh_cases_data_pattisserie.Store.isin(stores)
    ]

    wh_cases_data = pd.concat([wh_cases_data, wh_cases_data_pattisserie]).fillna(0)
    wh_cases_data["Weekly direct beer cases delivered"] = np.where(
        wh_cases_data.pmg == "BWS01", wh_cases_data["Weekly direct cased delivered"], 0
    )
    wh_cases_data["Weekly direct bakery cases delivered"] = np.where(
        (wh_cases_data.pmg.str.contains("ISB") | wh_cases_data.pmg.str.contains("SFB")),
        wh_cases_data["Weekly direct cased delivered"],
        0,
    )
    wh_cases_data["Weekly direct patissery cases delivered"] = np.where(
        wh_cases_data.pmg.str.contains("Patisserie_Cake"),
        wh_cases_data["Weekly direct cased delivered"],
        0,
    )
    wh_cases_data["Weekly direct cigarettes cases delivered"] = np.where(
        wh_cases_data.pmg.str.contains("CIG"),
        wh_cases_data["Weekly direct cased delivered"],
        0,
    )
    wh_cases_data["Weekly direct cosmetics cases delivered"] = np.where(
        wh_cases_data.pmg.str.contains("HEA"),
        wh_cases_data["Weekly direct cased delivered"],
        0,
    )
    wh_cases_data["Produce crates back"] = np.where(
        wh_cases_data.pmg.str.contains("PRO"), wh_cases_data["Cases delivered"], 0
    )
    wh_cases_data["Meat and poultry crates back"] = np.where(
        (wh_cases_data.pmg.str.contains("MPC") | wh_cases_data.pmg.str.contains("SFM")),
        wh_cases_data["Cases delivered"] * 0.87,
        0,
    )  # from Gábor: 87% of cases = crates MPC 87% is a simple average of countries and PMGs

    wh_cases_data.drop(
        wh_cases_data[wh_cases_data["pmg"] == "HDL01"].index, axis=0, inplace=True
    )
    wh_cases_data["dep"] = wh_cases_data["pmg"].str[:3]
    
    wh_cases_data.drop("department", axis=1, inplace=True) 
    

    wh_cases_data_grouped = wh_cases_data[wh_cases_data.columns[~wh_cases_data.columns.isin(['dep','country', 'pmg'])]].groupby(["Store", "day"]).sum().reset_index()

    wh_deliveries_data = (
        pq.read_table(
            Path(directory / wh_deliveries), filters=[("store", "in", stores)]
        )
        .to_pandas()
        .rename(
            columns={
                "store": "Store",
                "no_of_delivery_total": "Weekly deliveries",
                "no_of_delivery_DTS": "Weekly direct deliveries",
                "no_of_delivery_DTW": "Weekly DC deliveries",
                "pallets": "Weekly DC pallets",
                "rollcages": "Weekly DC rollcages",
                "sales_excl_vat" : "Sales"
                
            }
        )
        .drop("country", axis=1)
    )
    wh_deliveries_data = wh_deliveries_data[wh_deliveries_data.Store.isin(stores)]
    Drivers = store_inputs_wh.merge(
        wh_deliveries_data, on=["Store", "day"], how="left"
    ).merge(wh_cases_data_grouped, on=["Store", "day"], how="left")

    # Drivers sheet divided by 7 to get daily values and del the columns that drivers has
    wh_drivers_daily = wh_drivers.iloc[:, 1:].applymap(lambda x: x / 7)
    wh_drivers_daily.insert(0, "Store", wh_drivers.iloc[:, :1])

    for x in Drivers.iloc[:, 5:].columns.tolist():
        try:
            wh_drivers_daily.drop(x, axis=1, inplace=True)
        except:
            pass
    # Set up Drivers DataFrame
    Drivers = Drivers.merge(wh_drivers_daily, on=["Store"], how="left")
    Drivers["Dep"] = "WH"
    Drivers["Weekly deliveries"] = (
        Drivers["Weekly DC deliveries"]
        + Drivers["Weekly direct deliveries"]
        + Drivers["Weekly other deliveries"]
    )
    Drivers["Total DC pallets + Rollcages"] = (
        Drivers["Weekly DC pallets"] + Drivers["Weekly DC rollcages"]
    )
    Drivers["Weekly direct not beer, all other cases delivered"] = Drivers[
        [
            "Weekly direct bakery cases delivered",
            "Weekly direct patissery cases delivered",
            "Weekly direct news and magz cases delivered",
            "Weekly direct other GM and GRO cases delivered",
            "Weekly direct other GM and GRO cases delivered (1 case = 1 item)",
            "Weekly direct cigarettes cases delivered",
            "Weekly direct sandwich cases delivered",
            "Weekly direct cosmetics cases delivered",
        ]
    ].sum(axis=1)
    Drivers["Weekly direct cased delivered"] = Drivers[
        [
            "Weekly direct beer cases delivered",
            "Weekly direct not beer, all other cases delivered",
        ]
    ].sum(axis=1)
    Drivers["Weekly DC cased delivered"] = (
        Drivers["Cases delivered"] - Drivers["Weekly direct cased delivered"]
    )
    Drivers["Total crates back"] = (
        Drivers["Produce crates back"] + Drivers["Meat and poultry crates back"]
    )
    
    Drivers["Palletts+rc delivered"] = Drivers['Total DC pallets + Rollcages'] + Drivers['Weekly direct pallets']
    
    
    # Drivers["Palletts+rc delivered"] = np.where(Drivers.Store == 12001, 484/7, Drivers["Palletts+rc delivered"])

    # Profile sheet-changing "Y" to 1 and "N" to 0
    wh_profile_y_to_1 = wh_profile.iloc[:, 5:-2].applymap(
        lambda x: 1 if "Y" in str(x) else 0
    )

    for x, y in zip(range(5), wh_profile.iloc[:, :5].columns):

        wh_profile_y_to_1.insert(x, wh_profile.iloc[:, :5].columns[x], wh_profile[y])

    col_del = wh_profile_y_to_1.iloc[:, 1:5].columns.tolist()
    wh_profile_y_to_1.drop(col_del, axis=1, inplace=True)

    # Drivers to Profile together
    Drivers_WH = Drivers.merge(wh_profile_y_to_1, on="Store", how="left")
    Drivers_WH.rename(columns = {"sold_units": "Items Sold"}, inplace=True)

    sales_wh = Drivers_WH[["Store", "day", "Dep", "Sales", "Items Sold"]].drop_duplicates()

    
    # # New Stores to Benchmark Stores
    # new_stores = [12001, 14219, 14220, 24156, 24167, 24171, 24168, 24169, 24164 ] #24112,24157
    # benchmark_stores = [12002, 14154, 14123, 24045, 24142, 24001, 24113, 24157, 24152]
    
    
    #2025
    # new_stores = [11085, 14222, 25034, 44092, 24174, 24173] 
    # benchmark_stores = [11019, 14201, 25016, 44089, 24062, 24047] 


    # benchmark_stores_df = Drivers_WH.query("Store in @benchmark_stores")
    # Drivers_WH = Drivers_WH[~Drivers_WH.Store.isin(new_stores)]

    # for a, b in zip(new_stores, benchmark_stores):

    #     benchmark_stores_df.loc[benchmark_stores_df.Store == b, "Store"] = a

    # Drivers_WH = pd.concat([Drivers_WH, benchmark_stores_df])
    
    
    
    # # New Stores to Benchmark Stores
    # new_stores = [24170 ] #24112,24157
    # benchmark_stores = [24157]

    # benchmark_stores_df = Drivers_WH.query("Store in @benchmark_stores")
    # Drivers_WH = Drivers_WH[~Drivers_WH.Store.isin(new_stores)]

    # for a, b in zip(new_stores, benchmark_stores):

    #     benchmark_stores_df.loc[benchmark_stores_df.Store == b, "Store"] = a

    # Drivers_WH = pd.concat([Drivers_WH, benchmark_stores_df])
    
    
    
    
    # def calculate_depo_pallets(row):
        
    #     store = row['Store']
        
    #     if store == 41450:
    #         # If Store is 41450, get Palletts+rc delivered for Store 44060
    #         return Drivers_WH.loc[Drivers_WH['Store'] == 44060, 'Palletts+rc delivered'].iloc[0]
        
    #     elif store == 41460:
    #         # If Store is 41460, get Palletts+rc delivered for Store 44081
    #         return Drivers_WH.loc[Drivers_WH['Store'] == 44081, 'Palletts+rc delivered'].iloc[0]
        
    #     elif store == 41490:
    #         # If Store is 41490, get Palletts+rc delivered for Store 44032
    #         return Drivers_WH.loc[Drivers_WH['Store'] == 44032, 'Palletts+rc delivered'].iloc[0]
        
    #     elif store == 41580:
    #         # If Store is 41580, sum Palletts+rc delivered for Stores 44024 and 44026
    #         pallets_44024 = Drivers_WH.loc[Drivers_WH['Store'] == 44024, 'Palletts+rc delivered'].iloc[0]
    #         pallets_44026 = Drivers_WH.loc[Drivers_WH['Store'] == 44026, 'Palletts+rc delivered'].iloc[0]
    #         return pallets_44024 + pallets_44026
        
    #     else:
    #         return 0  # Default value if none of the conditions match

    # # Apply the function to create the new column 'DEPO pallets and rollcages'
    # Drivers_WH['DEPO pallets and rollcages'] = Drivers_WH.apply(calculate_depo_pallets, axis=1)
    
    
    # def calculate_depo_pallets(row):
    #     store = row['Store']
        
    #     try:
    #         if store == 41450:
    #             # If Store is 41450, get Palletts+rc delivered for Store 44060
    #             return Drivers_WH.loc[Drivers_WH['Store'] == 44060, 'Palletts+rc delivered'].iloc[0]
            
    #         elif store == 41460:
    #             # If Store is 41460, get Palletts+rc delivered for Store 44081
    #             return Drivers_WH.loc[Drivers_WH['Store'] == 44081, 'Palletts+rc delivered'].iloc[0]
            
    #         elif store == 41490:
    #             # If Store is 41490, get Palletts+rc delivered for Store 44032
    #             return Drivers_WH.loc[Drivers_WH['Store'] == 44032, 'Palletts+rc delivered'].iloc[0]
            
    #         elif store == 41580:
    #             # If Store is 41580, sum Palletts+rc delivered for Stores 44024 and 44026
    #             pallets_44024 = Drivers_WH.loc[Drivers_WH['Store'] == 44024, 'Palletts+rc delivered'].iloc[0]
    #             pallets_44026 = Drivers_WH.loc[Drivers_WH['Store'] == 44026, 'Palletts+rc delivered'].iloc[0]
    #             return pallets_44024 + pallets_44026
            
    #         else:
    #             return 0  # Default value if none of the conditions match
                
    #     except IndexError:
    #         # Print debugging information when an error occurs
    #         print(f"Error processing store {store}")
    #         print(f"Available stores in DataFrame: {Drivers_WH['Store'].unique()}")
    #         return 0  # Return 0 if there's an error
    
    # # Before applying the function, let's check the data
    # print("Unique stores in DataFrame:", Drivers_WH['Store'].unique())
    # print("Column names:", Drivers_WH.columns.tolist())
    
    # # Apply the function to create the new column
    # Drivers_WH['DEPO pallets and rollcages'] = Drivers_WH.apply(calculate_depo_pallets, axis=1)    
    
    def calculate_depo_pallets(row):
        store = row['Store']
        
        store_mapping = {
            41450: 44060,
            41460: 44081,
            41490: 44032,
            41580: [44024, 44026]  # Special case that needs sum of two stores
        }
        
        if store not in store_mapping:
            return 0
        
        if store == 41580:
            # Handle the special case for store 41580
            pallets_sum = 0
            for target_store in store_mapping[store]:
                matched_rows = Drivers_WH.loc[Drivers_WH['Store'] == target_store, 'Palletts+rc delivered']
                if not matched_rows.empty:
                    pallets_sum += matched_rows.iloc[0]
            return pallets_sum
        else:
            # Handle all other stores
            target_store = store_mapping[store]
            matched_rows = Drivers_WH.loc[Drivers_WH['Store'] == target_store, 'Palletts+rc delivered']
            return matched_rows.iloc[0] if not matched_rows.empty else 0
    
    # Apply the function to create the new column
    Drivers_WH['DEPO pallets and rollcages'] = Drivers_WH.apply(calculate_depo_pallets, axis=1)
    
    Drivers_WH.loc[Drivers_WH.Store == 11086, '11086Store'] = 1



    return store_inputs_wh, Drivers_WH, sales_wh


@timeit
def WareHouse_TimeValues(
    directory, wh_most, store_inputs_wh, excel_inputs_f, Drivers_WH, stores
):
    # Set up Most file
    # activity_list = pl.read_excel(directory / wh_most, sheet_name="Time Values", engine="calamine",  read_options={"header_row": 3}).to_pandas()
    
    activity_list = pd.read_excel(directory / wh_most, "Time Values", skiprows=3 )

    new_header = activity_list.iloc[0]  # grab the first row for the header
    activity_list = activity_list[1:]  # take the data less the header row
    activity_list.columns = new_header  # set the header row as the df header

    cols = [
        "Activity_key_activities",
        "Suboperation Description",
        "Activity group",
        "V F",
        "DRIVER_1",
        "DRIVER_2",
        "FREQ2",
        "DRIVER_3",
        "DRIVER_4",
        "PROFILE",
        "RA",
        "Head",
        "Newspaper_Activity",
    ]
    cols2 = [
        "Activity_key_activities",
        "Suboperation",
        "Activity_Group",
        "V_F",
        "Driver_1",
        "Driver_2",
        "Freq_Driver_2",
        "Driver_3",
        "Driver_4",
        "Profile",
        "RA",
        "Head",
        "Newspaper_Activity",
    ]
    activity_list = activity_list[cols]
    for x, y in zip(cols, cols2):
        activity_list.rename(columns={x: y}, inplace=True)

    activity_list.dropna(subset=["Activity_key_activities"], inplace=True)
    activity_list.rename(
        columns={"Activity_key_activities": "Activity_key"}, inplace=True
    )
    activity_list["Freq_Driver_2"] = activity_list["Freq_Driver_2"].replace(np.nan, 0)
    activity_list = activity_list.replace(np.nan, "no_driver")

    # Adjusting activities to stores and days
    cols = activity_list.columns
    combined_rows = activity_list[cols].apply(
        lambda row: "|".join(row.values.astype(str)), axis=1
    )
    combined_rows_values_list = list(combined_rows.values)

    time_values = store_inputs_wh.copy()
    time_values["combined_rows"] = 0
    time_values["combined_rows"] = time_values["combined_rows"].apply(
        lambda x: combined_rows_values_list
    )
    time_values = time_values.explode("combined_rows").drop_duplicates()
    time_values[list(cols)] = time_values["combined_rows"].str.split("|", expand=True)
    time_values.drop(["combined_rows"], axis=1, inplace=True)
    time_values["Dep"] = "WH"

    # Creating Basic_times and Frequency df
    tv_freq = pd.read_excel(directory / wh_most, "Time Values")

    a = tv_freq[3:4].values[:, :14]
    tv_freq_columns_a = [item for sublist in a for item in sublist]
    tv_freq_columns_b = tv_freq.columns[14:35].tolist()
    c = tv_freq[3:4].values[:, 35:]
    tv_freq_columns_c = [item for sublist in c for item in sublist]
    tv_freq_columns_ALL = tv_freq_columns_a + tv_freq_columns_b + tv_freq_columns_c

    tv_freq = tv_freq.iloc[4:, :]
    tv_freq.columns = tv_freq_columns_ALL

    tv_freq_3_columns = tv_freq[["Activity_key_activities", "RA", "Head"]]

    act = ["Activity_key_activities", "RA", "Head"]
    place = [36, 37, 38]

    for x, y in zip(place, act):
        tv_freq.drop([y], axis=1).insert(x, y, tv_freq_3_columns[y])

    cols = ["Activity_key_activities"] + tv_freq_columns_b
    cols2 = ["Activity_key_activities"] + tv_freq_columns_b
    tv_freq = tv_freq[cols]
    for x, y in zip(cols, cols2):
        tv_freq.rename(columns={x: y}, inplace=True)

    tv_freq.drop(["Unnamed: 18"], axis=1, inplace=True)

    tv_freq = tv_freq[tv_freq.Activity_key_activities.notnull()]
    times_format = tv_freq.melt(
        id_vars="Activity_key_activities",
        value_vars=["Hypermarket", "Compact", "1K", "Express"],
        var_name="Format",
        value_name="basic_time",
    )

    times_freq_country = tv_freq.melt(
        id_vars="Activity_key_activities",
        value_vars=tv_freq.columns[5:].tolist(),
        var_name="Country",
        value_name="freq",
    )

    format_abbr = ["LHM", "SHM", "1K", "EXP"]
    format_real = ["Hypermarket", "Compact", "1K", "Express"]

    for x, y in zip(format_abbr, format_real):
        times_freq_country.loc[
            times_freq_country.Country.str.startswith(x), "Format"
        ] = y

    times = times_format.merge(
        times_freq_country, on=["Activity_key_activities", "Format"], how="left"
    )

    times["Country"] = times["Country"].apply(lambda x: x[:-3][-2:])
    times = times[times["Country"] != "PL"].rename(
        columns={"Activity_key_activities": "Activity_key"}
    )
    times["Dep"] = "WH"
    times = times.drop_duplicates()
    times[["freq", "basic_time"]] = times[["freq", "basic_time"]].astype("float")

    store_inputs_format = pd.read_excel(directory / excel_inputs_f)
    store_inputs_format = store_inputs_format[store_inputs_format.Store.isin(stores)]
    time_values = time_values.merge(
        store_inputs_format[["Store", "Format"]], on="Store", how="left"
    )

    df_times = time_values.merge(
        times, on=["Activity_key", "Country", "Format", "Dep"], how="left"
    )

    drivers_to_time_values = Drivers_WH.melt(
        id_vars=["Store", "Dep", "day"], var_name="drivers"
    )
    drivers_to_time_values.value = pd.to_numeric(
        drivers_to_time_values.value, errors="coerce"
    ).replace(np.nan, 0)

    # VlookUp drivers to activities
    d_values = [
        1,
        2,
        3,
        4,
    ]  # Here we VLOOKUP driver values between df_times and drivers_df. We have 4 drivers
    driver_initial_name = "drivers"
    value_initial_name = "value"
    for x in d_values:
        driver_new_name = "Driver_" + str(x)
        value_new_name = "Driver_" + str(x) + "_value"
        drivers_to_time_values.rename(
            columns={driver_initial_name: driver_new_name}, inplace=True
        )
        drivers_to_time_values.rename(
            columns={value_initial_name: value_new_name}, inplace=True
        )
        df_times = df_times.merge(
            drivers_to_time_values,
            on=["Store", "Dep", "day", driver_new_name],
            how="left",
        )
        df_times[value_new_name] = df_times[value_new_name].replace(
            np.nan, 0
        )  # it seems we need NaN there
        driver_initial_name = driver_new_name
        value_initial_name = value_new_name
    driver_new_name = "Profile"  # Profiles
    value_new_name = "Profile_value"
    drivers_to_time_values.rename(
        columns={driver_initial_name: driver_new_name}, inplace=True
    )
    drivers_to_time_values.rename(
        columns={value_initial_name: value_new_name}, inplace=True
    )

    df_times = df_times.merge(
        drivers_to_time_values, on=["Store", "Dep", "day", driver_new_name], how="left"
    )
    df_times[value_new_name] = df_times[value_new_name].replace(
        np.nan, 0
    )  # it seems we need NaN there
    drivers_to_time_values.rename(columns={driver_new_name: "drivers"}, inplace=True)
    drivers_to_time_values.rename(columns={value_new_name: "value"}, inplace=True)
    
    
    night_truck = {"Activity_key":['Packaging RSU movementsMove pallets of beer ctates with empty beer bottles to truck and travel back  : e p truck',
                                   'Packaging RSU movementsMove pallets of beer ctates with empty beer bottles to truck and travel back  : forklift',
                                   'Packaging RSU movementsMove pallets of beer ctates with empty beer bottles to truck and travel back  : long e p truck',
                                   'Packaging RSU movementsMove pallets of beer ctates with empty beer bottles to truck and travel back - preparation : forklift',
                                   'Packaging RSU movementsMove pallets of beer ctates with empty beer bottles to truck and travel back : pallet truck',
                                   'Packaging RSU movementsMove pallets of beer ctates with empty beer bottles to truck and travel back- preparation : e p truck',
                                   'Packaging RSU movementsMove pallets of beer ctates with empty beer bottles to truck and travel back- preparation : long e p truck',
                                   'Packaging RSU movementsMove pallets of beer ctates with empty beer bottles to truck and travel back- preparation : pallet truck',
                                   'Packaging RSU movementsMove pallets of empty produce and meat&poultry crates to truck - preparation : forklift',
                                   'Packaging RSU movementsMove pallets of empty produce and meat&poultry crates to truck : forklift',
                                   'Packaging RSU movementsMove pallets of empty produce and meat&poultry crates to truck and travel back  : e p truck',
                                   'Packaging RSU movementsMove pallets of empty produce and meat&poultry crates to truck and travel back  : long e p truck',
                                   'Packaging RSU movementsMove pallets of empty produce and meat&poultry crates to truck and travel back  : pallet truck',
                                   'Packaging RSU movementsMove pallets of empty produce and meat&poultry crates to truck and travel back-- preparation : e p truck',
                                   'Packaging RSU movementsMove pallets of empty produce and meat&poultry crates to truck and travel back-- preparation : long e p truck',
                                   'Packaging RSU movementsMove pallets of empty produce and meat&poultry crates to truck and travel back-- preparation : pallet truck',
                                   'Fresh waste shippingDangerous waste shipping (meat, dairy): Communication with driver, open and close gate',
                                   'Fresh waste shippingDangerous waste shipping (meat, dairy): Wait for driver to pack fresh waste'
                                   ],
                   "night_truck_flag_activity" : ["Y"]*18}
    
    
    nt = pd.DataFrame(night_truck)
    night_truck_stores= [21025,
     21031,
     21045,
     21046,
     21048,
     21121,
     24021,
     24041,
     24042,
     24058,
     24067,
     24113,
     24116,
     24145,
     24163,
     21028,
     21035,
     21024,
     21026,
     24046,
     21039,
     21033,
     21040,
     21041,
     21043,
     21047,
     24117,
     21049,
     21054,
     24141]
    
    nts_dict = {"Store": night_truck_stores, "nts_flag": ["Y"]*len(night_truck_stores)}
    
    nts = pd.DataFrame(nts_dict)
    
    df_times = df_times.merge(nts , on ="Store", how='left').merge(nt, on='Activity_key', how="left")
    
    df_times['freq'] = np.where((df_times.nts_flag == 'Y') & (df_times.night_truck_flag_activity == 'Y'), 0, df_times['freq'])
    
    
    
    # exZabka stores are not gicing hours for stock movement wh activity group
    
    exzabka_stores = [25002,25003,25005,25006,25011,25012,25016,25018,25023,25024,25026,25028,25032,25033,25034]
    
    df_times['freq'] = np.where((df_times.Store.isin(exzabka_stores)) & (df_times.Activity_Group == 'Stock Movement WH'), 0, df_times['freq'])
    
    
    
    
    # Compressor activities out from 5 SK stores
    # comp_act = [
    #     'Walk out to compressor',
    #     'Compressing everything: stores wo cooperation',
    #     'Using the compressor - remaining waste',
    #     'Walk back from compressor',
    #     'Compressor shipping: Let the company in and out',
    #     'Compressor shipping: Supervise shipping',
    #     'Compressor shipping: paperwork',
    #     'pick up the keys for dangerous waste wh & copmressor - walk',
    #     'pick up the keys for dangerous waste wh & copmressor'
    #     ]
    
    # comp_out_stores = [21001, 21004, 21005, 21006, 21041]
    
    # df_times['freq'] = np.where((df_times.Store.isin(comp_out_stores)) & (df_times.Suboperation.isin(comp_act)), 0, df_times['freq'])

    
    
    return df_times


@timeit
def WareHouse_HoursCalculation(df_times):
    def CalcModelHours(calc_hours):
        calc_hours.Driver_3_value = np.where(
            (calc_hours.Driver_3_value == 0) & (calc_hours.Driver_3 == "no_driver"),
            1,
            calc_hours.Driver_3_value,
        )  # here we have multiplicators and as we cannot divide by 0, I changed the zeros to 1
        calc_hours.Driver_4_value = np.where(
            (calc_hours.Driver_4_value == 0) & (calc_hours.Driver_4 == "no_driver"),
            1,
            calc_hours.Driver_4_value,
        )
        calc_hours["hours"] = (
            (
                (
                    calc_hours.Driver_1_value
                    + (calc_hours.Driver_2_value * calc_hours.Freq_Driver_2 / 100)
                )
                * calc_hours.Driver_3_value
                * calc_hours.Driver_4_value
            )
            * calc_hours.basic_time
            / 60
            * calc_hours.freq
            / 100
        )
        calc_hours["hours"] = np.where(
            (calc_hours.Profile_value == 0) & (calc_hours.Profile != "no_driver"),
            0,
            calc_hours["hours"],
        )
        return calc_hours

    hours_df = df_times.copy()
    hours_df[["Freq_Driver_2", "Head"]] = hours_df[["Freq_Driver_2", "Head"]].astype(
        "float"
    )
    hours_df["RA_time"] = np.where(
        (hours_df.RA == "Y") & (hours_df["Activity_key"] !="Customer ServiceCustomer service"), hours_df.basic_time * (4 / 100), 0
    )
    hours_df["basic_time"] = hours_df.basic_time + hours_df.RA_time
    hours_df.drop(columns={"RA_time"}, axis=1, inplace=True)
    hours_df["Driver_3_value"] = (
        hours_df["Driver_3_value"] * 7
    )  # it is important if you do daily model
    hours_df = CalcModelHours(hours_df)

    # Headcount calculation
    headcount_hrs = hours_df.loc[
        hours_df.Head == 1, ("Store", "Dep", "day", "Head", "hours")
    ]
    headcount_hrs = (
        headcount_hrs.groupby(["Store", "Dep", "day"])["hours"].sum().reset_index()
    )
    headcount_hrs["Headcount"] = np.where(
        (((headcount_hrs.hours / 8) - round(headcount_hrs.hours / 8)) > 0.05),
        np.ceil(headcount_hrs.hours / 8) /7,
        round(headcount_hrs.hours / 8) /7,
    )
    headcount_hrs.drop(columns={"hours"}, axis=1, inplace=True)
    hours_df = hours_df.merge(headcount_hrs, on=["Store", "Dep", "day"], how="left")
    hours_df["Driver_1_value"] = np.where(
        hours_df.Driver_1 == "Headcount", hours_df.Headcount, hours_df["Driver_1_value"]
    )
    hours_df["Driver_2_value"] = np.where(
        hours_df.Driver_2 == "Headcount", hours_df.Headcount, hours_df["Driver_2_value"]
    )
    hours_df["Driver_3_value"] = np.where(
        hours_df.Driver_3 == "Headcount", hours_df.Headcount *7, hours_df["Driver_3_value"]
    )
    hours_df["Driver_4_value"] = np.where(
        hours_df.Driver_4 == "Headcount", hours_df.Headcount, hours_df["Driver_4_value"]
    )
    hours_df.drop(columns={"Headcount"}, axis=1, inplace=True)
    hours_df = CalcModelHours(hours_df)
    hours_df["Yearly GBP"] = 5 * hours_df.hours * 52
    hours_df["Division"] = "Warehouse"
    hours_df.drop(["Plan Size"], axis=1, inplace=True)
    hours_df.drop(["Store Name"], axis=1, inplace=True)
    

    
    return hours_df
