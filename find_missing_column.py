#!/usr/bin/env python3
"""
Script to find the exact missing column between pandas and Polars outputs
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the validation functions to get actual DataFrames
from t import run_pandas_implementation, run_polars_implementation, setup_control_panel_environment

def find_missing_column():
    """Find the exact missing column between pandas and Polars"""
    
    print("🔍 FINDING MISSING COLUMN")
    print("=" * 50)
    
    # Setup environment
    print("🔧 Setting up environment...")
    env = setup_control_panel_environment()
    
    # Run pandas implementation
    print("🐼 Running pandas implementation...")
    pandas_drivers, pandas_produce, pandas_time = run_pandas_implementation(env)
    pandas_cols = set(pandas_drivers.columns)
    
    print("⚡ Running Polars implementation...")
    polars_drivers, polars_produce, polars_time = run_polars_implementation(env)
    polars_cols = set(polars_drivers.columns)
    
    # Find differences
    missing_in_polars = pandas_cols - polars_cols
    missing_in_pandas = polars_cols - pandas_cols
    
    print(f"\n📊 RESULTS:")
    print(f"   Pandas columns: {len(pandas_cols)}")
    print(f"   Polars columns: {len(polars_cols)}")
    print(f"   Difference: {len(pandas_cols) - len(polars_cols)}")
    
    if missing_in_polars:
        print(f"\n❌ Missing in Polars: {sorted(missing_in_polars)}")
    
    if missing_in_pandas:
        print(f"\n❌ Missing in Pandas: {sorted(missing_in_pandas)}")
    
    # Show all columns sorted for detailed comparison
    pandas_sorted = sorted(pandas_cols)
    polars_sorted = sorted(polars_cols)
    
    print(f"\n🔍 DETAILED COLUMN COMPARISON:")
    print(f"   First difference search...")
    
    # Find first difference
    for i, (p_col, pol_col) in enumerate(zip(pandas_sorted, polars_sorted)):
        if p_col != pol_col:
            print(f"   First difference at position {i}:")
            print(f"      Pandas: {p_col}")
            print(f"      Polars: {pol_col}")
            break
    
    # Check if one list is shorter
    if len(pandas_sorted) != len(polars_sorted):
        shorter = min(len(pandas_sorted), len(polars_sorted))
        longer_list = pandas_sorted if len(pandas_sorted) > len(polars_sorted) else polars_sorted
        longer_name = "Pandas" if len(pandas_sorted) > len(polars_sorted) else "Polars"
        
        print(f"\n   Extra columns in {longer_name}:")
        for col in longer_list[shorter:]:
            print(f"      {col}")

if __name__ == "__main__":
    find_missing_column()
