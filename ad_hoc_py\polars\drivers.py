import polars as pl
import pandas as pd
import numpy as np








weekdays_to_divide = 7
cases_to_replenish_only = False

# Model Variables
RC_CAPACITY = 1 + (1 - 0.62)
RC_DELIVERY = 0.23
RC_VS_PAL_CAPACITY = 0.62
REX_ALLOWANCE = 4
RC_Capacity_Ratio = 1 + (1 - 0.62)
shelf_trolley_cap_ratio_to_pallet = 9
shelf_trolley_cap_ratio_to_rollcage = 5
backstock_target = 0.4
MODULE_CRATES = 8
TABLE_CRATES = 4
SALES_CYCLE = (0.2, 0.2, 0.2, 0.2, 0.2)
FULFILL_TARGET = 0.6
capping_shelves_ratio = 0.075 #0.075

news_mags_rate_for_SK = 1.7216831482098829



stores = [41520]

directory = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025"
excel_inputs_f = r"\inputs\Repl\Repl_Stores_Inputs_2025_Q1_v5_modul.xlsx"



def Store_Inputs_Creator(directory, excel_inputs_f, stores):
    """
    Creates store inputs DataFrame with improved performance.
    
    Args:
        directory: Path to directory containing Excel file
        excel_inputs_f: Excel file name
        stores: List of store IDs
    
    Returns:
        DataFrame with store inputs
    """
    # Read Excel file once
    excel_file = pd.ExcelFile(directory + excel_inputs_f)
    
    # Read and filter PMG data
    pmg_df = pd.read_excel(excel_file, "pmg")
    pmg_df = pmg_df[pmg_df.Area == "Replenishment"].drop("Area", axis=1)
    
    # Read and filter store list
    store_list_df = pd.read_excel(excel_file, "store_list")
    store_list_df = store_list_df[store_list_df.Store.isin(stores)]
    
    # Create cross product using cross merge
    store_inputs = store_list_df.merge(
        pmg_df,
        how='cross'
    )
    
    # Update HDL01 department
    store_inputs.loc[store_inputs.Pmg == "HDL01", "Dep"] = "NEW"
    
    # Read and merge capping data
    capping_df = pd.read_excel(excel_file, "capping")
    capping_df["is_capping_shelf"] = 1
    store_inputs = store_inputs.merge(
        capping_df,
        on=["Store", "Pmg"],
        how="left"
    )
    store_inputs["is_capping_shelf"] = store_inputs["is_capping_shelf"].fillna(0)
    
    # Read and merge profile data
    Dprofiles_df = pd.read_excel(excel_file, "Dprofiles")
    store_inputs = store_inputs.merge(
        Dprofiles_df,
        on=["Store", "Dep"],
        how="left"
    )
    
    Pprofiles_df = pd.read_excel(excel_file, "Pprofiles")
    store_inputs = store_inputs.merge(
        Pprofiles_df,
        on=["Country", "Format", "Pmg"],
        how="left"
    )
    
    # Convert Store to int64
    store_inputs["Store"] = store_inputs["Store"].astype("int64")
    
    # Ensure consistent column names
    expected_columns = [
        "Country",
        "Store",
        "Store Name",
        "Plan Size",
        "Format",
        "Pmg",
        "Pmg Name",
        "Dep",
        "Division",
    ]
    
    return store_inputs

store_inputs = Store_Inputs_Creator(directory, excel_inputs_f, stores)

def Repl_Drivers_Calculation(
    directory,
    Repl_Dataset,
    store_inputs,
    backstock_target,
    RC_Capacity_Ratio,
    shelf_trolley_cap_ratio_to_pallet,
    shelf_trolley_cap_ratio_to_rollcage,
    excel_inputs_f,
    MODULE_CRATES,
    TABLE_CRATES,
    FULFILL_TARGET,
    SALES_CYCLE,
    RC_CAPACITY,
    RC_DELIVERY,
    RC_VS_PAL_CAPACITY,
    only_tpn,
    tpnb_store,
    tpnb_country,
    selected_tpn,
    capping_shelves_ratio,
    stores,
    version,
    shelfService_gm,
    cases_to_replenish_only,
):
    def part1(df):
        """Initial data preparation and filtering"""
        weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        if isinstance(df, pd.DataFrame):
            df = pl.from_pandas(df)
            
        return (df
            .filter(pl.col("store").is_in(stores))
            .with_columns(
                pallet_capacity=pl.when(pl.col("mu") > 0)
                .then(pl.col("pallet_capacity") / 2)
                .otherwise(pl.col("pallet_capacity"))
            )
            .drop(["ownbrand", "checkout_stand_flag"])
            .with_columns(
                foil=pl.when(pl.col("foil") == 0).then(1).otherwise(pl.col("foil"))
            )
            .pipe(lambda df: df.filter(
                (~pl.col("pmg").is_in(
                    df.filter(
                        (pl.col("dep") == "PRO") & 
                        (pl.col("pmg") != "PRO16") & 
                        (pl.col("pmg") != "PRO19")
                    ).select("pmg").unique().get_column("pmg")
                )) & 
                (pl.col("pmg") != "HDL01")
            ))
            # For now, let's just cast to Categorical and sort, without enforcing order
            .pipe(lambda df: (
                df.with_columns(
                    day=pl.col("day").cast(pl.Categorical)
                ).sort(["store", "pmg", "tpnb", "day"])
                if df.height > 0 else df
            ))
            .with_columns(
                capacity_avg_pmg=pl.col("shelfCapacity").mean().over(["store", "pmg"])
            )
            .with_columns(
                capacity_avg_pmg=pl.when(pl.col("capacity_avg_pmg") < 5)
                .then(8)
                .otherwise(pl.col("capacity_avg_pmg"))
            )
            .fill_null(0)
            .with_columns(
                shelfCapacity=pl.when(
                    (pl.col("shelfCapacity") == 0) | (pl.col("shelfCapacity") == 1)
                )
                .then(pl.col("capacity_avg_pmg"))
                .otherwise(pl.col("shelfCapacity")),
                
                stock=pl.when(
                    (pl.col("stock") == 0) & (pl.col("sold_units") > 0)
                )
                .then(pl.col("sold_units"))
                .otherwise(pl.col("stock")),
                
                heavy=pl.col("weight") * pl.col("case_capacity"),
                weight_selector=pl.col("weight") * pl.col("case_capacity")
            )
            .with_columns(
                heavy=(pl.col("weight_selector") >= 5).cast(pl.Int8),
                light=(pl.col("weight_selector") < 5).cast(pl.Int8)
            )
            .drop("weight_selector")
            .with_columns(
                broken_items=(
                    pl.when(pl.col("broken_case_flag") == 1)
                    .then(pl.col("case_capacity"))
                    .otherwise(0)
                )
                .cast(pl.Float32)
                .alias("Broken Items"),
                
                cases_delivered_on_sf=(
                    pl.when(pl.col("broken_case_flag") == 1)
                    .then(pl.col("cases_delivered") - 1)
                    .otherwise(pl.col("cases_delivered"))
                ).cast(pl.Float32)
            )
            .with_columns(
                cases_delivered_on_sf=pl.when(pl.col("cases_delivered_on_sf") < 0)
                .then(0)
                .otherwise(pl.col("cases_delivered_on_sf"))
            )
        )
    
    
    def part2(df, backstock_target, weekdays_to_divide):
        """
        Second part of transformations dealing with SRP/pallet/mu customization 
        and cases calculations
        """
        srp_columns = ['srp', 'split_pallet', 'full_pallet', 'mu', 'icream_nsrp', 'single_pick']
        nsrp_columns = ['nsrp', 'srp', 'split_pallet', 'full_pallet', 'mu', 'icream_nsrp']
        
        return (df
            # Setup the icream_nsrp
            .with_columns(
                nsrp=pl.when(pl.col("icream_nsrp") > 0)
                .then(pl.col("icream_nsrp") + pl.col("nsrp"))
                .otherwise(pl.col("nsrp"))
            )
            # Process broken case flags
            .with_columns(
                **{x: pl.when((pl.col(x) > 0) & (pl.col("broken_case_flag") == 1))
                   .then(0)
                   .otherwise(pl.col(x))
                   for x in srp_columns}
            )
            .with_columns(
                nsrp=pl.when(pl.col("broken_case_flag") == 1)
                .then(1)
                .otherwise(pl.col("nsrp"))
            )
            # Single pick customization
            .with_columns(
                **{x: pl.when(pl.col("single_pick") > 0)
                   .then(0)
                   .otherwise(pl.col(x))
                   for x in nsrp_columns}
            )
            # Backroom calculations
            .with_columns(
                backroom_cases_dotcom=(
                    pl.when(pl.col("backroom_flag") == 1)
                    .then(pl.col("sold_units_dotcom") / pl.col("case_capacity"))
                    .otherwise(0)
                ).cast(pl.Float32)
            )
            .with_columns(
                backroom_cases_dotcom=pl.col("backroom_cases_dotcom").fill_null(0),
                
                backroom_pallets=(
                    pl.when(pl.col("backroom_cases_dotcom") > 0)
                    .then(pl.col("backroom_cases_dotcom") / pl.col("pallet_capacity"))
                    .otherwise(0)
                ).cast(pl.Float32).fill_null(0),
                
                cases_delivered_on_sf=(
                    pl.when(pl.col("backroom_cases_dotcom") > 0)
                    .then(pl.col("cases_delivered_on_sf") - pl.col("backroom_cases_dotcom"))
                    .otherwise(pl.col("cases_delivered_on_sf"))
                ).cast(pl.Float32)
            )
            .with_columns(
                cases_delivered_on_sf=pl.when(pl.col("cases_delivered_on_sf") < 0)
                .then(0)
                .otherwise(pl.col("cases_delivered_on_sf"))
            )
            # Secondary SRP and NSRP calculations
            .with_columns(
                secondary_srp=(
                    pl.when(pl.col("sold_units") > pl.col("shelfCapacity"))
                    .then(pl.col("stock") - (pl.col("shelfCapacity") / (1 - backstock_target)))
                    .otherwise(0)
                ).cast(pl.Float32).fill_null(0)
            )
            .with_columns(
                secondary_srp=pl.when(
                    (1 - (pl.col("shelfCapacity") / pl.col("stock"))) > 0.4
                )
                .then(pl.col("secondary_srp"))
                .otherwise(0)
            )
            .with_columns(
                secondary_srp=pl.when(pl.col("stock") < pl.col("shelfCapacity"))
                .then(0)
                .otherwise(pl.col("secondary_srp"))
            )
            .with_columns(
                secondary_srp=pl.when(
                    (pl.col("srp") > 0) | 
                    (pl.col("full_pallet") > 0) | 
                    (pl.col("mu") > 0) | 
                    (pl.col("split_pallet") > 0) | 
                    (pl.col("icream_nsrp") > 0)
                )
                .then(pl.col("secondary_srp") / weekdays_to_divide)
                .otherwise(0)
            )
            # Secondary NSRP calculations
            .with_columns(
                secondary_nsrp=(
                    pl.when(pl.col("sold_units") > pl.col("shelfCapacity"))
                    .then(pl.col("stock") - (pl.col("shelfCapacity") / (1 - backstock_target)))
                    .otherwise(0)
                ).cast(pl.Float32).fill_null(0)
            )
            .with_columns(
                secondary_nsrp=pl.when(
                    (1 - (pl.col("shelfCapacity") / pl.col("stock"))) > 0.4
                )
                .then(pl.col("secondary_nsrp"))
                .otherwise(0)
            )
            .with_columns(
                secondary_nsrp=pl.when(pl.col("stock") < pl.col("shelfCapacity"))
                .then(0)
                .otherwise(pl.col("secondary_nsrp"))
            )
            .with_columns(
                secondary_nsrp=pl.when(
                    (pl.col("srp") == 0) & 
                    (pl.col("full_pallet") == 0) & 
                    (pl.col("mu") == 0) & 
                    (pl.col("split_pallet") == 0) & 
                    (pl.col("icream_nsrp") == 0)
                )
                .then(pl.col("secondary_nsrp") / weekdays_to_divide)
                .otherwise(0)
            )
            # Shop floor capacity and stock calculations
            .with_columns(
                shop_floor_capacity=(
                    pl.col("shelfCapacity") + 
                    pl.col("secondary_nsrp") + 
                    pl.col("secondary_srp")
                ).cast(pl.Float32),
                
                stock_morning=(
                    pl.col("stock") - pl.col("unit") + 
                    pl.col("sold_units") + pl.col("sold_units_dotcom")
                ).fill_null(0)
            )
            # Cases to replenish calculation
            .with_columns(
                cases_to_replenish=(
                    (pl.col("sold_units") + 
                    pl.min_horizontal(pl.col(["stock", "shop_floor_capacity"])) -
                    pl.min_horizontal(pl.col(["stock_morning", "shop_floor_capacity"]))) / 
                    pl.col("case_capacity")
                )
            )
            .with_columns(
                cases_to_replenish=pl.when(pl.col("cases_to_replenish") < 0)
                .then(0)
                .otherwise(pl.col("cases_to_replenish"))
            )
        )
    
    
    def part3(df, cases_to_replenish_only, capping_shelves_ratio, weekdays_to_divide, 
              store_inputs, RC_Capacity_Ratio, shelf_trolley_cap_ratio_to_pallet):
        """
        Third part of transformations handling ClipStrip calculations, touch calculations,
        and store input merging
        """
        if cases_to_replenish_only:
            # Group by calculations for cases_to_replenish
            cases_to_replenish_tpn = (
                df.groupby(['country', 'store', 'division', 'dep', 'pmg', 'tpnb', 'product_name'])
                .agg(pl.col('cases_to_replenish').sum())
            )
            
            cases_to_replenish_dep = (
                df.groupby(['country', 'store', 'division', 'dep'])
                .agg(pl.col('cases_to_replenish').sum())
            )
            
            return df, cases_to_replenish_tpn, cases_to_replenish_dep
        
        
        

        
        # Cast product_name to string and handle ClipStrip calculations
        df = (df
            .with_columns(
                product_name=pl.col("product_name").cast(pl.Utf8)
            )
            .with_columns(
                clipstrip_flag=pl.when(
                    pl.col("product_name").str.contains("HELL") | 
                    pl.col("product_name").str.contains("XIXO")
                )
                .then(0)
                .otherwise(pl.col("clipstrip_flag"))
            )
            .with_columns({
                "Clip Strip Items": pl.when(pl.col("clipstrip_flag") == 1)
                                   .then(pl.col("cases_to_replenish") * pl.col("case_capacity"))
                                   .otherwise(0)
            })
            .with_columns({
                "Clip Strip Cases": (pl.col("Clip Strip Items") / pl.col("case_capacity"))
                                   .cast(pl.Float32)
                                   .fill_null(0),
                
                "cases_to_replenish": pl.when(pl.col("clipstrip_flag") == 1)
                                     .then(0)
                                     .otherwise(pl.col("cases_to_replenish"))
            })
            # Touch calculations
            .with_columns({
                "o_touch": pl.when(pl.col("stock") > pl.col("shop_floor_capacity"))
                          .then(pl.col("shop_floor_capacity"))
                          .otherwise(pl.col("stock"))
                          .cast(pl.Float32),
                
                "c_touch": pl.when(
                    (pl.col("is_capping_shelf") == 1) &
                    (pl.col("stock") - (capping_shelves_ratio * pl.col("shop_floor_capacity")) > 
                     pl.col("shop_floor_capacity"))
                )
                .then(capping_shelves_ratio * pl.col("shop_floor_capacity"))
                .otherwise(0)
                .cast(pl.Float32)
            })
            .with_columns({
                "t_touch": pl.when(pl.col("stock") > pl.col("shop_floor_capacity"))
                          .then(pl.col("stock") - pl.col("c_touch") - pl.col("shop_floor_capacity"))
                          .otherwise(0)
                          .cast(pl.Float32)
            })
            # Combined capping shelf cases calculation
            .with_columns({
                "Capping Shelf Cases": (
                    pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                    .then(0)
                    .otherwise(
                        ((pl.col("c_touch") / pl.col("case_capacity")) / weekdays_to_divide)
                        .cast(pl.Float32)
                        .fill_null(0)
                    )
                )
            })
            .with_columns({
                "cases_to_repl_excl_cap_cases": pl.when(
                    pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases") > 0
                )
                .then(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases"))
                .otherwise(pl.col("cases_to_replenish")),
                
                "Capping Shelf Cases": pl.when(
                    pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases") > 0
                )
                .then(pl.col("Capping Shelf Cases"))
                .otherwise(0)
            })
        )
            
        # Prepare store inputs DataFrames
        stores_df = (pl.from_pandas(store_inputs[["Store", "Format", "Plan Size"]]
                                   .drop_duplicates())
                    .select(pl.all().map(lambda x: x.lower())))
        
        dep_df = (pl.from_pandas(store_inputs[[
            "Store", "Dep", "Racking", "Pallets Delivery Ratio",
            "Backstock Pallet Ratio", "says", "pbl_pbs_25_perc_pallet"
        ]].drop_duplicates())
        .select(pl.all().map(lambda x: x.lower())))
        
        pmg_df = (pl.from_pandas(store_inputs[[
            "Country", "Format", "Pmg", "presortPerc", "prack"
        ]].drop_duplicates())
        .select(pl.all().map(lambda x: x.lower())))
        
        # Format adjustment and merges
        df = (df
            .with_columns(
                format=pl.when(pl.col("format") == "1k")
                .then("1K")
                .otherwise(pl.col("format"))
            )
            .join(stores_df, on=["store", "format"], how="inner")
            .join(pmg_df, on=["country", "format", "pmg"], how="left")
            .join(dep_df, on=["store", "dep"], how="left")
            .with_columns(
                prack=pl.when(pl.col("racking") == 1)
                .then(pl.col("prack"))
                .otherwise(0)
            )
        )
        
        # Try to update presortperc
        try:
            df = df.with_columns(
                presortperc=pl.when(pl.col("dep").is_in(["BWS", "HEA", "DRY"]))
                .then(pl.col("pre_sort_perc_by_pmg"))
                .otherwise(pl.col("presortperc"))
            )
        except:
            pass
    
        # Continue with delivery calculations
        df = (df
            .with_columns(
                new_delivery_rollcages=((pl.col("cases_delivered_on_sf") / pl.col("pallet_capacity")) 
                                      * RC_Capacity_Ratio * (1 - pl.col("pallets delivery ratio")))
                .cast(pl.Float32)
                .fill_null(0)
                .alias("New Delivery - Rollcages")
            )
        )
        
        # Try to process PBL and PBS calculations
        try:
            df = (df
                .with_columns(
                    pbl_cage=pl.col("MIX_CAGE_%") + pl.col("PBL_CAGE_%"),
                    pbl_pallet=pl.col("MIX_PALLET_%") + pl.col("PBL_PALLET_%")
                )
            )
            
            # Process cage and pallet percentages
            for col_type, cols in {
                'cage': ['PBL_CAGE_%', 'PBS_CAGE_%'],
                'pallet': ['PBL_PALLET_%', 'PBS_PALLET_%']
            }.items():
                df = df.with_columns(
                    **{col: pl.when(pl.col(cols[0]) + pl.col(cols[1]) == 0)
                       .then(0.5)
                       .otherwise(pl.col(col))
                       for col in cols}
                )
            
            # Calculate new columns based on percentages
            for col in ['PBL_CAGE_%', 'PBS_CAGE_%']:
                new_col = col[:-2]
                df = df.with_columns(
                    **{new_col: (pl.col(col) * pl.col("New Delivery - Rollcages"))
                       .cast(pl.Float32)}
                )
        except:
            pass
        
        # Final delivery calculations
        df = (df
            .with_columns(
                new_delivery_pallets=(pl.col("cases_delivered_on_sf") / pl.col("pallet_capacity")
                                    * pl.col("pallets delivery ratio"))
                .cast(pl.Float32)
                .fill_null(0)
                .alias("New Delivery - Pallets"),
                
                new_delivery_shelf_trolley=(pl.col("cases_delivered_on_sf") / pl.col("pallet_capacity")
                                          * shelf_trolley_cap_ratio_to_pallet)
                .cast(pl.Float32)
                .fill_null(0)
                .alias("New Delivery - Shelf Trolley"),
                
                pre_sorted_cases=(pl.col("presortperc") * pl.col("cases_delivered_on_sf") 
                                * pl.col("pallets delivery ratio"))
                .alias("Pre-sorted Cases"),
                
                pre_sorted_cases_pbl_pbs=(pl.col("presortperc") * pl.col("cases_delivered_on_sf") 
                                        * pl.col("pallets delivery ratio"))
                .alias("Pre-sorted Cases_pbl_pbs")
            )
            .with_columns(
                l_pre_sorted_cases=pl.when(pl.col("light") == 1)
                .then(pl.col("Pre-sorted Cases"))
                .otherwise(0)
                .cast(pl.Float32)
                .fill_null(0)
                .alias("L_Pre-sorted Cases"),
                
                h_pre_sorted_cases=pl.when(pl.col("heavy") == 1)
                .then(pl.col("Pre-sorted Cases"))
                .otherwise(0)
                .cast(pl.Float32)
                .fill_null(0)
                .alias("H_Pre-sorted Cases"),
                
                full_pallet_cases=(pl.col("cases_to_replenish") * 
                                 pl.when(pl.col("full_pallet") > 0)
                                 .then(pl.col("full_pallet"))
                                 .otherwise(0))
                .cast(pl.Float32)
                .alias("Full Pallet Cases"),
                
                mu_cases=(pl.col("cases_to_replenish") * 
                         pl.when(pl.col("mu") > 0)
                         .then(pl.col("mu"))
                         .otherwise(0))
                .cast(pl.Float32)
                .alias("MU cases")
            )
            .with_columns(
                full_half_pallet_cases=(pl.col("Full Pallet Cases") + pl.col("MU cases"))
                .alias("Full + Half Pallet Cases"),
                
                racking_pallets=(pl.col("prack") * pl.col("New Delivery - Pallets"))
                .alias("Racking Pallets")
            )
        )
        
        return df

    # Main pipeline
    return (
 Repl_Dataset.pipe(part1)\
    .pipe(lambda df: part2(df, backstock_target, weekdays_to_divide))\
    .pipe(lambda df: part3(df, cases_to_replenish_only, capping_shelves_ratio, 
                              weekdays_to_divide, store_inputs, RC_Capacity_Ratio,
                              shelf_trolley_cap_ratio_to_pallet))
    
    )