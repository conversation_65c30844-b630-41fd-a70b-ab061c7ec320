import win32com.client
import os

import pandas as pd






# Function to check if <PERSON>rque<PERSON> file exists and append CSV files to it
def append_csv_to_parquet(folder_path):
    try:
        parquet_file = os.path.join(folder_path, 'pbs_pbl_dataset.parquet')
        if not os.path.exists(parquet_file):
            df = pd.DataFrame()
        else:
            df = pd.read_parquet(parquet_file)
        for file in os.listdir(folder_path):
            if file.endswith('.csv'):
                csv_path = os.path.join(folder_path, file)
                df = pd.concat([df, pd.read_csv(csv_path, delimiter=';', skiprows=1, parse_dates=['Ldate'])], ignore_index=True).drop_duplicates()
                os.remove(csv_path)  # Remove the CSV file after processing
        df.to_parquet(parquet_file, index=False, compression="gzip")

    except Exception as e:
        pass

# Specify folder path to save attachments and CSV files
folder_path = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\PBS_PBL\dcr_reports"

# Specify email addresses to filter attachments
senders = {"<EMAIL>", "<EMAIL>", "<EMAIL>"}

# Call the function to append CSV files to Parquet
append_csv_to_parquet(folder_path)
