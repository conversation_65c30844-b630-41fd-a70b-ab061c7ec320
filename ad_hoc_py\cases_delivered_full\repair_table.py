#!/usr/bin/env python3
"""
Table repair script for fixing corrupted Parquet files in sch_analysts.tbl_cases_delivered_productivity

This script addresses the FileNotFoundException error by:
1. Refreshing table metadata
2. Repairing table partitions
3. Cleaning up corrupted files
4. Verifying table accessibility
"""

import paramiko
import sys
import time
sys.path.append('.')
import delivered

def repair_table():
    """Repair the corrupted table"""
    print('🔧 Table Repair Utility')
    print('=' * 50)
    print('Fixing corrupted Parquet files in sch_analysts.tbl_cases_delivered_productivity')
    print()
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        # Connect to server
        print('🔌 Connecting to Hadoop server...')
        ssh_client.connect(
            hostname=delivered.hostname, 
            username=delivered.username, 
            password=delivered.password, 
            timeout=30
        )
        print('✅ Connected successfully')
        
        # Step 1: Drop the corrupted table completely
        print('\n🗑️  Step 1: Dropping corrupted table...')
        drop_cmd = "echo 'DROP TABLE IF EXISTS sch_analysts.tbl_cases_delivered_productivity;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
        
        _, stdout, stderr = ssh_client.exec_command(drop_cmd, timeout=120)
        drop_output = stdout.read().decode('utf-8')
        
        if 'Error' not in drop_output or 'completed' in drop_output.lower():
            print('✅ Table dropped successfully')
        else:
            print('⚠️  Table drop had warnings (may not have existed)')
        
        print('⏳ Waiting 10 seconds for cleanup...')
        time.sleep(10)
        
        # Step 2: Clear any remaining metadata cache
        print('\n🧹 Step 2: Clearing metadata cache...')
        cache_cmd = "echo 'CLEAR CACHE;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
        
        _, stdout, stderr = ssh_client.exec_command(cache_cmd, timeout=60)
        cache_output = stdout.read().decode('utf-8')
        print('✅ Cache cleared')
        
        # Step 3: Recreate the table by running the SQL script
        print('\n🔄 Step 3: Recreating table with fresh data...')
        print('This may take a few minutes...')
        
        start_time = time.time()
        _, stdout, stderr = ssh_client.exec_command("sh /home/<USER>/cases_delivered_full/start_q 2>&1", get_pty=True)
        
        # Monitor the recreation process
        last_update = time.time()
        while True:
            if stdout.channel.exit_status_ready():
                break
                
            if stdout.channel.recv_ready():
                chunk = stdout.channel.recv(1024).decode('utf-8', errors='ignore')
                if chunk:
                    last_update = time.time()
                    
                    # Show key progress indicators
                    for line in chunk.split('\n'):
                        line = line.strip()
                        if not line:
                            continue
                            
                        if 'CREATE TABLE' in line.upper():
                            print('📋 Creating table...')
                        elif 'DROP TABLE' in line.upper():
                            print('🗑️  Dropping old table...')
                        elif 'Job' in line and 'finished' in line:
                            print('✨ Stage completed')
                        elif 'Exception' in line or 'ERROR' in line.upper():
                            print(f'⚠️  Warning: {line[:80]}...')
            
            # Timeout check
            elapsed = time.time() - start_time
            if elapsed > 600:  # 10 minutes timeout
                print('⏰ Timeout - stopping recreation')
                break
                
            time.sleep(2)
        
        # Check result
        exit_status = stdout.channel.recv_exit_status()
        total_time = time.time() - start_time
        
        print(f'⏱️  Recreation time: {total_time/60:.1f} minutes')
        
        if exit_status == 0:
            print('✅ Table recreated successfully!')
            
            # Step 4: Verify the new table
            print('\n🔍 Step 4: Verifying new table...')
            
            # Check table exists
            check_cmd = "echo 'SHOW TABLES LIKE \"*cases_delivered*\";' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
            
            _, stdout, stderr = ssh_client.exec_command(check_cmd, timeout=60)
            check_output = stdout.read().decode('utf-8')
            
            if 'tbl_cases_delivered_productivity' in check_output:
                print('✅ Table exists in catalog')
                
                # Try a simple query to verify accessibility
                print('🧪 Testing table accessibility...')
                test_cmd = "echo 'SELECT COUNT(*) FROM sch_analysts.tbl_cases_delivered_productivity LIMIT 1;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
                
                _, stdout, stderr = ssh_client.exec_command(test_cmd, timeout=180)
                test_output = stdout.read().decode('utf-8')
                test_error = stderr.read().decode('utf-8')
                
                if 'FileNotFoundException' in test_error or 'FileNotFoundException' in test_output:
                    print('❌ Table still has file issues - may need manual intervention')
                    print('💡 Try running: REFRESH TABLE sch_analysts.tbl_cases_delivered_productivity;')
                elif any(char.isdigit() for char in test_output):
                    print('✅ Table is accessible and contains data!')
                    # Extract potential row count
                    import re
                    numbers = re.findall(r'\b\d+\b', test_output)
                    if numbers:
                        print(f'📊 Estimated row count: {max(int(n) for n in numbers):,}')
                else:
                    print('⚠️  Table accessible but response unclear')
                    
            else:
                print('❌ Table not found in catalog')
                
        else:
            print(f'❌ Table recreation failed with exit code: {exit_status}')
            return False
        
        ssh_client.close()
        print('\n🔌 Connection closed')
        
        print('\n' + '=' * 50)
        print('🎉 Table repair process completed!')
        print('\n💡 If you still get FileNotFoundException errors:')
        print('   1. Wait a few minutes for S3 consistency')
        print('   2. Run: REFRESH TABLE sch_analysts.tbl_cases_delivered_productivity;')
        print('   3. Try your query again')
        
        return True
        
    except Exception as e:
        print(f'❌ Error during table repair: {e}')
        import traceback
        traceback.print_exc()
        return False
    finally:
        if ssh_client:
            ssh_client.close()

def quick_refresh():
    """Quick table refresh without full recreation"""
    print('🔄 Quick Table Refresh')
    print('=' * 30)
    
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    
    try:
        ssh_client.connect(
            hostname=delivered.hostname, 
            username=delivered.username, 
            password=delivered.password, 
            timeout=30
        )
        print('✅ Connected')
        
        # Refresh table
        refresh_cmd = "echo 'REFRESH TABLE sch_analysts.tbl_cases_delivered_productivity;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
        
        _, stdout, stderr = ssh_client.exec_command(refresh_cmd, timeout=120)
        output = stdout.read().decode('utf-8')
        
        print('✅ Table refreshed')
        print('💡 Try your query now!')
        
        ssh_client.close()
        
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    print("🔧 Table Repair Options:")
    print("1. Full repair (drop and recreate) - Recommended")
    print("2. Quick refresh only")
    
    choice = input("\nEnter choice (1 or 2): ").strip()
    
    if choice == "1":
        repair_table()
    elif choice == "2":
        quick_refresh()
    else:
        print("Invalid choice. Running full repair...")
        repair_table()
