{"cells": [{"cell_type": "code", "execution_count": 63, "id": "ea720367-aa80-4ea5-807e-0abd734594fb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import polars as pl\n", "import time\n", "from functools import wraps\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": 2, "id": "60b4e99b-1005-4c52-8007-8e94e55ce30b", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["def optimize_types(dataframe):\n", "    np_types = [\n", "        np.int8,\n", "        np.int16,\n", "        np.int32,\n", "        np.int64,\n", "        np.uint8,\n", "        np.uint16,\n", "        np.uint32,\n", "        np.uint64,\n", "        np.float32,\n", "        np.float64,\n", "    ]  # , np.float16, np.float32, np.float64\n", "    np_types = [np_type.__name__ for np_type in np_types]\n", "    type_df = pd.DataFrame(data=np_types, columns=[\"class_type\"])\n", "\n", "    type_df[\"min_value\"] = type_df[type_df[\"class_type\"].str.contains(\"int\")][\n", "        \"class_type\"\n", "    ].apply(lambda row: np.iinfo(row).min)\n", "    type_df[\"max_value\"] = type_df[type_df[\"class_type\"].str.contains(\"int\")][\n", "        \"class_type\"\n", "    ].apply(lambda row: np.iinfo(row).max)\n", "    type_df[\"min_value_f\"] = type_df[type_df[\"class_type\"].str.contains(\"float\")][\n", "        \"class_type\"\n", "    ].apply(lambda row: np.finfo(row).min)\n", "    type_df[\"max_value_f\"] = type_df[type_df[\"class_type\"].str.contains(\"float\")][\n", "        \"class_type\"\n", "    ].apply(lambda row: np.finfo(row).max)\n", "    type_df[\"min_value\"] = np.where(\n", "        type_df[\"min_value\"].isna(), type_df[\"min_value_f\"], type_df[\"min_value\"]\n", "    )\n", "    type_df[\"max_value\"] = np.where(\n", "        type_df[\"max_value\"].isna(), type_df[\"max_value_f\"], type_df[\"max_value\"]\n", "    )\n", "    type_df.drop(columns=[\"min_value_f\", \"max_value_f\"], inplace=True)\n", "\n", "    type_df[\"range\"] = type_df[\"max_value\"] - type_df[\"min_value\"]\n", "    type_df.sort_values(by=\"range\", inplace=True)\n", "    try:\n", "        for col in dataframe.loc[:, dataframe.dtypes == np.integer]:\n", "            col_min = dataframe[col].min()\n", "            col_max = dataframe[col].max()\n", "            temp = type_df[\n", "                (type_df[\"min_value\"] <= col_min) & (type_df[\"max_value\"] >= col_max)\n", "            ]\n", "            optimized_class = temp.loc[temp[\"range\"].idxmin(), \"class_type\"]\n", "            # print(\"Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}\".format(col, col_min, col_max, optimized_class))\n", "            dataframe[col] = dataframe[col].astype(optimized_class)\n", "    except ValueError:\n", "        pass\n", "    try:\n", "        for col in dataframe.loc[:, (dataframe.dtypes == np.floating)]:\n", "            col_min = dataframe[col].min()\n", "            col_max = dataframe[col].max()\n", "            type_df = type_df[\n", "                type_df[\"class_type\"].astype(\"string\").str.contains(\"float\")\n", "            ]\n", "            temp = type_df[\n", "                (type_df[\"min_value\"] <= col_min) & (type_df[\"max_value\"] >= col_max)\n", "            ]\n", "            optimized_class = temp.loc[temp[\"range\"].idxmin(), \"class_type\"]\n", "            # print(\"Col name : {} Col min_value : {} Col max_value : {} Optimized Class : {}\".format(col, col_min, col_max, optimized_class))\n", "            dataframe[col] = dataframe[col].astype(optimized_class)\n", "    except ValueError:\n", "        pass\n", "    return dataframe\n", "\n", "\n", "def optimize_floats(df: pd.DataFrame) -> pd.DataFrame:\n", "    floats = df.select_dtypes(include=[\"float64\"]).columns.tolist()\n", "    df[floats] = df[floats].apply(pd.to_numeric, downcast=\"float\")\n", "    return df\n", "\n", "\n", "def optimize_ints(df: pd.DataFrame) -> pd.DataFrame:\n", "    ints = df.select_dtypes(include=[\"int64\"]).columns.tolist()\n", "    df[ints] = df[ints].apply(pd.to_numeric, downcast=\"integer\")\n", "    return df\n", "\n", "\n", "def optimize_objects(df: pd.DataFrame):\n", "    try:\n", "        for col in df.select_dtypes(include=[\"object\"]):\n", "            if not (type(df[col][0]) == list):\n", "                num_unique_values = len(df[col].unique())\n", "                num_total_values = len(df[col])\n", "                if float(num_unique_values) / num_total_values < 0.5:\n", "                    df[col] = df[col].astype(\"category\")\n", "    except IndexError:\n", "        pass\n", "    return df\n", "\n", "\n", "def optimize(df: pd.DataFrame):\n", "    return optimize_floats(optimize_ints(optimize_objects(df)))\n"]}, {"cell_type": "code", "execution_count": 3, "id": "3d3696be-3b62-4127-90e7-03ebacacd0e8", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["def CalcModelHours(calc_hours):\n", "\n", "    calc_hours = calc_hours.with_columns(\n", "        [\n", "            pl.when(\n", "                (pl.col(\"Driver_2_value\") == 0)\n", "                &\n", "                (pl.col(\"Driver_2\") == \"no_driver\")\n", "            )\n", "            .then(1)\n", "            .otherwise(pl.col(\"Driver_2_value\"))\n", "            .alias(\"Driver_2_value\")\n", "        ]\n", "    )\n", "\n", "    calc_hours = calc_hours.with_columns(\n", "        [\n", "            (\n", "                    (\n", "                        pl.col(\"Driver_1_value\")\n", "                        * pl.col(\"Driver_2_value\")\n", "                    )\n", "\n", "\n", "                * pl.col(\"Std\")\n", "                / 60\n", "                * pl.col(\"Freq\")\n", "                / 100\n", "            ).alias(\"hours\")\n", "        ]\n", "    )\n", "\n", "    calc_hours = calc_hours.with_columns(\n", "        [\n", "            pl.when(\n", "                (pl.col(\"Profile_value\") == 0) & (pl.col(\"Profile\") != \"no_driver\")\n", "            )\n", "            .then(0)\n", "            .otherwise(pl.col(\"hours\"))\n", "            .alias(\"hours\")\n", "        ]\n", "    )\n", "\n", "    return calc_hours"]}, {"cell_type": "code", "execution_count": 59, "id": "37f49367-9d2b-4d43-8ac9-820fdc849c47", "metadata": {"jupyter": {"source_hidden": true}, "tags": []}, "outputs": [], "source": ["# Python decorator to measure execution time of Functions\n", "def timeit(func):\n", "    @wraps(func)\n", "    def timeit_wrapper(*args, **kwargs):\n", "        start_time = time.perf_counter()\n", "        result = func(*args, **kwargs)\n", "        end_time = time.perf_counter()\n", "        total_time = end_time - start_time\n", "        if func.__name__ == \"Replenishment_Model_Running\":\n", "            print(\n", "                f\" \\n{func.__name__} is done! Elapsed time (sec & min): {total_time:.2f} sec which is {total_time/60:.2f} min\"\n", "            )\n", "        else:\n", "            print(f\" \\n{func.__name__} is done! Elapsed time (sec): {total_time:.2f}\")\n", "        return result\n", "\n", "    return timeit_wrapper"]}, {"cell_type": "code", "execution_count": 64, "id": "ec16f006-602f-42cf-ab12-308f662d24f6", "metadata": {"tags": []}, "outputs": [], "source": ["@timeit\n", "def ff():\n", "    ff_input = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#F&F\\F&F_2023\\inputs\\\\F&F_input_table_23Q1.xlsb\"\n", "\n", "\n", "    #Prepare_drivers_profile_most\n", "    ff_store_profile = pd.read_excel(ff_input, sheet_name = 'Store Profile', skiprows=9)\n", "    ff_store_profile = ff_store_profile.loc[:, ~ff_store_profile.columns.str.contains('^Unnamed')]\n", "    ff_store_profile.drop(['Module value'], inplace=True, axis=1)\n", "\n", "    ff_drivers = pd.read_excel(ff_input, sheet_name = 'Drivers', skiprows=1)\n", "    sales = ff_drivers[['Number','Sales']].copy().rename(columns={'Number': 'Store'})  \n", "\n", "    ff_store_profile_drivers = ff_store_profile.merge(ff_drivers, on=['Number','Country for reporting', 'Plan Size'], how='left').fillna(0).replace(('Y', 'N'), (1, 0))\n", "    for_opb = ff_store_profile_drivers.copy()\n", "\n", "    ttb = pd.read_excel(ff_input, sheet_name = 'for_ttb')\n", "\n", "    ff_most = pd.DataFrame()\n", "    for most_tables, indicator in zip(['Time Values normal', 'Time Values season'], ['normal', 'season']):\n", "\n", "        df = pd.read_excel(ff_input, sheet_name = most_tables)\n", "        df = df[df['Measurement type'].notna()]\n", "        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]\n", "        df = df.melt(id_vars=df.iloc[:,: df.columns.get_loc(\"Profile\") +1].columns.tolist(), var_name='most_type')\n", "        searchfor = ['Freq', 'Std']\n", "        df = df.loc[df.most_type.apply(lambda x: True if any(i in x for i in searchfor) else False)].fillna(0)\n", "        df['time_value_type'] = indicator\n", "        ff_most = pd.concat([df, ff_most])\n", "\n", "    #Calc_Time_Values\n", "    time_value_df = ff_store_profile_drivers[['Number', 'Name', 'Country', 'Format']].drop_duplicates()\n", "    activity_groups_subs = list(ff_most[['Sub Operation Description', 'Activity Group', 'time_value_type']].apply(\n", "            lambda row: \"|\".join(row.values.astype(str)), axis=1))\n", "    time_value_df[\"combined_rows\"] = 0\n", "    time_value_df[\"combined_rows\"] = time_value_df[\"combined_rows\"].apply(\n", "        lambda x: activity_groups_subs)\n", "    time_value_df = time_value_df.explode(\"combined_rows\").drop_duplicates()\n", "    time_value_df[list(['Sub Operation Description', 'Activity Group', 'time_value_type'])] = time_value_df[\"combined_rows\"].str.split(\"|\", expand=True)\n", "    time_value_df.drop([\"combined_rows\"], axis=1, inplace=True)\n", "    time_value_df[\"Dep\"] = \"CLG\"\n", "\n", "    ff_most[list(['Format', 'STD_Freq'])] = ff_most[\"most_type\"].str.split(\"_\", expand=True)\n", "    ff_most.drop([\"most_type\", 'time_value_type'], axis=1, inplace=True)\n", "\n", "    def move_column_inplace(df, col, pos):\n", "        col = df.pop(col)\n", "        df.insert(pos, col.name, col)\n", "\n", "    move_column_inplace(ff_most, 'Format', 1)   \n", "\n", "\n", "    ff_most_for_tv = ff_most.pivot_table(index=ff_most.iloc[:,: ff_most.columns.get_loc(\"Profile\") +1].columns.tolist(),columns='STD_Freq', values ='value').reset_index()\n", "    ff_most_for_tv.drop(['id', 'Code', 'Code.1', 'Measurement type'], axis=1, inplace=True)\n", "\n", "\n", "    time_value_df = time_value_df.merge(ff_most_for_tv, on=['Format', 'Sub Operation Description', 'Activity Group'], how='left')\n", "\n", "    ff_store_profile_drivers = ff_store_profile_drivers.melt(id_vars='Number', var_name = 'drivers')\n", "    ff_store_profile_drivers['drivers'] = [x.lower() for x in list(ff_store_profile_drivers['drivers'].values)]\n", "\n", "    for x in ['Driver_2', 'Profile']:\n", "        time_value_df[x] = np.where(time_value_df[x] == 0, 'no_driver', time_value_df[x])\n", "    for x in ['Driver_1','Driver_2', 'Profile']:\n", "        time_value_df[x] = [x.lower() for x in list(time_value_df[x].values)]\n", "\n", "    #Calc_Model_Hours\n", "    d_values = [\n", "        1,\n", "        2]  # Here we VLOOKUP driver values between df_times and drivers_df. We have 4 drivers\n", "    driver_initial_name = \"drivers\"\n", "    value_initial_name = \"value\"\n", "    for x in d_values:\n", "        driver_new_name = \"Driver_\" + str(x)\n", "        value_new_name = \"Driver_\" + str(x) + \"_value\"\n", "        ff_store_profile_drivers.rename(\n", "            columns={driver_initial_name: driver_new_name}, inplace=True\n", "        )\n", "        ff_store_profile_drivers.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "        time_value_df = time_value_df.merge(\n", "            ff_store_profile_drivers,\n", "            on=[\"Number\", driver_new_name],\n", "            how=\"left\",\n", "        )\n", "        time_value_df[value_new_name] = time_value_df[value_new_name].replace(\n", "            np.nan, 0\n", "        )  # it seems we need NaN there\n", "        driver_initial_name = driver_new_name\n", "        value_initial_name = value_new_name\n", "    driver_new_name = \"Profile\"  # Profiles\n", "    value_new_name = \"Profile_value\"\n", "    ff_store_profile_drivers.rename(columns={driver_initial_name: driver_new_name}, inplace=True)\n", "    ff_store_profile_drivers.rename(columns={value_initial_name: value_new_name}, inplace=True)\n", "\n", "    time_value_df = time_value_df.merge(\n", "        ff_store_profile_drivers,\n", "        on=[\"Number\", driver_new_name],\n", "        how=\"left\",\n", "    )\n", "    time_value_df[value_new_name] = time_value_df[value_new_name].replace(\n", "        np.nan, 0\n", "    )  # it seems we need NaN there\n", "    ff_store_profile_drivers.rename(columns={driver_new_name: \"drivers\"}, inplace=True)\n", "    ff_store_profile_drivers.rename(columns={value_new_name: \"value\"}, inplace=True)\n", "\n", "\n", "    time_value_df = optimize_objects(optimize_types(time_value_df))\n", "    hours_df = pl.from_pandas(time_value_df)\n", "    hours_df = CalcModelHours(hours_df).to_pandas()\n", "    hours_df_normal = hours_df[hours_df.time_value_type == 'normal']\n", "    hours_df_season = hours_df[hours_df.time_value_type == 'season']\n", "\n", "    hours_df = pd.DataFrame()\n", "    for df in (hours_df_normal, hours_df_season):\n", "\n", "        # headcount\n", "        cond = [df.Format == 'HML', df.Format == 'HMS', df.Format == '1K', df.Format == 'EXP']\n", "        # result = [35, 35, 32, 30]\n", "        result = [30, 30, 30, 30]\n", "        df['for_headcount'] = df.groupby(['Number'], observed=True)['hours'].transform(\"sum\") / np.select(cond, result, 1)\n", "        df['Driver_1_value'] = np.where(df.Driver_1 == 'headcount', df['for_headcount'], df['Driver_1_value'] )\n", "        df['Driver_2_value'] = np.where(df.Driver_2 == 'headcount', df['for_headcount'], df['Driver_2_value'] )\n", "        df = pl.from_pandas(df)\n", "        df = CalcModelHours(df).to_pandas()\n", "        df.drop(['for_headcount'], axis=1, inplace=True)\n", "\n", "        hours_df = pd.concat([hours_df, df])\n", "\n", "    #Create_Outputs\n", "    ##OPB\n", "    hours_df.rename(columns={'Number': 'Store'}, inplace=True)\n", "    opb = hours_df.query(\"time_value_type == 'normal'\").groupby(['Store','Name','Country','Format','Dep','V F'], observed=True)['hours'].sum().reset_index()\n", "    opb['FIXED HOURS'] = np.where(opb['V F'] == 'F', opb.hours, 0)\n", "    opb['VARIABLE HOURS'] = np.where(opb['V F'] == 'V', opb.hours, 0)\n", "    opb = opb.groupby(['Store','Name','Country','Format','Dep'], observed=True).agg({'FIXED HOURS':'sum', 'VARIABLE HOURS':'sum'}).reset_index()\n", "\n", "    opb = opb.merge(sales, on='Store', how='left')\n", "    opb['VARIABLE CURRENCY'] = opb['Sales'] / opb['VARIABLE HOURS']\n", "    opb.drop(\"Sales\", axis=1, inplace=True)\n", "    opb['TOTAL HOURS'] = opb['VARIABLE HOURS'] + opb['FIXED HOURS']\n", "\n", "    for_opb.rename(columns={\"Number\":\"Store\"}, inplace=True)\n", "    opb = opb.merge(for_opb, on=['Store','Name', 'Country', 'Format'], how='left')\n", "\n", "    opb_activit_groups_normal = hours_df.query(\"time_value_type == 'normal'\").groupby(['Store','Name','Country','Format','Dep', 'Activity Group'], observed=True)['hours'].sum().reset_index()\n", "    opb_activit_groups_normal = opb_activit_groups_normal.pivot_table(index=['Store'], columns='Activity Group', aggfunc=\"sum\", fill_value=0, values='hours' ).reset_index()\n", "\n", "    opb_activit_groups_season = hours_df.query(\"time_value_type == 'season'\").groupby(['Store','Name','Country','Format','Dep', 'Activity Group'], observed=True)['hours'].sum().reset_index()\n", "    opb_activit_groups_season = opb_activit_groups_season.pivot_table(index=['Store'], columns='Activity Group', aggfunc=\"sum\", fill_value=0, values='hours' ).reset_index()\n", "\n", "    opb_normal = opb.merge(opb_activit_groups_normal, on='Store', how='left')\n", "\n", "    opb_season = opb.merge(opb_activit_groups_season, on='Store', how='left')\n", "\n", "    ##Create TTB\n", "    ttb_df = opb[['Store', 'Country', 'Name']].drop_duplicates()\n", "    ttb_list = list(ttb[ttb.loc[:, ~ttb.columns.isin(['Country','Store', 'Name'])].columns.tolist()].apply(\n", "            lambda row: \"|\".join(row.values.astype(str)), axis=1))\n", "    ttb_df[\"combined_rows\"] = 0\n", "    ttb_df[\"combined_rows\"] = ttb_df[\"combined_rows\"].apply(\n", "        lambda x: ttb_list)\n", "    ttb_df = ttb_df.explode(\"combined_rows\").drop_duplicates()\n", "    ttb_df[list(ttb.loc[:, ~ttb.columns.isin(['Country','Store', 'Name'])].columns.tolist())] = ttb_df[\"combined_rows\"].str.split(\"|\", expand=True)\n", "    ttb_df.drop([\"combined_rows\"], axis=1, inplace=True)\n", "    ttb_df['store match'] = [str(x) + 'WEEK_' + y for x, y in zip(ttb_df['Store'], ttb_df['week'])]\n", "\n", "\n", "    season_groups = hours_df.query(\"time_value_type == 'season'\").groupby(['Store','Name','Country','Format','Dep', 'Activity Group'], observed=True)['hours'].sum().reset_index().query(\"hours > 0\")\n", "    dict_list = (\n", "        season_groups.groupby([\"Store\", 'Activity Group'], observed=True)[\"hours\"]\n", "        .apply(lambda s: s.tolist())\n", "        .to_dict()\n", "    )\n", "\n", "    for x, y in dict_list.items():\n", "        for z, s in zip(['Promotion fix', 'New Sale fix', 'Further sale fix', 'Season change fix'], ['Promo','New Sale', 'Further sale','Season change']):\n", "            ttb_df.loc[(ttb_df.Store == x[0]) & (ttb_df[z] == '1'), z] = s\n", "            ttb_df.loc[(ttb_df.Store == x[0]) & (ttb_df[z] == x[1]), z] = y[0]\n", "    ttb_df.drop(['FIXED HOURS', 'VARIABLE CURRENCY'], axis=1, inplace=True)\n", "\n", "    ttb_df_fix_var = opb[['Store', 'FIXED HOURS', 'VARIABLE CURRENCY']].drop_duplicates()\n", "    ttb_df = ttb_df.merge(ttb_df_fix_var, on = 'Store', how='left')\n"]}, {"cell_type": "code", "execution_count": 65, "id": "650d1f08-3170-4630-8a93-0ce9fef0fa0b", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" \n", "ff is done! Elapsed time (sec): 23.02\n"]}], "source": ["ff()"]}, {"cell_type": "code", "execution_count": 66, "id": "e9533485-5d16-4079-93b4-fd656195eeba", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Store</th>\n", "      <th>Country</th>\n", "      <th>Name</th>\n", "      <th>QUARTER</th>\n", "      <th>PERIOD</th>\n", "      <th>store match</th>\n", "      <th>week</th>\n", "      <th>Promotion fix</th>\n", "      <th>New Sale fix</th>\n", "      <th>Further sale fix</th>\n", "      <th>Season change fix</th>\n", "      <th>FIXED HOURS</th>\n", "      <th>VARIABLE CURRENCY</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_1</td>\n", "      <td>1</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>28.377378</td>\n", "      <td>9289.084774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_2</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7.913464</td>\n", "      <td>0</td>\n", "      <td>28.377378</td>\n", "      <td>9289.084774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_3</td>\n", "      <td>3</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>28.377378</td>\n", "      <td>9289.084774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_4</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>33.329152</td>\n", "      <td>0</td>\n", "      <td>52.49194</td>\n", "      <td>28.377378</td>\n", "      <td>9289.084774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_5</td>\n", "      <td>5</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>28.377378</td>\n", "      <td>9289.084774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28827</th>\n", "      <td>45018</td>\n", "      <td>HU</td>\n", "      <td>SM 1225 Gyor <PERSON>ek</td>\n", "      <td>Q4</td>\n", "      <td>P12</td>\n", "      <td>45018WEEK_49</td>\n", "      <td>49</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28828</th>\n", "      <td>45018</td>\n", "      <td>HU</td>\n", "      <td>SM 1225 Gyor <PERSON>ek</td>\n", "      <td>Q4</td>\n", "      <td>P12</td>\n", "      <td>45018WEEK_50</td>\n", "      <td>50</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28829</th>\n", "      <td>45018</td>\n", "      <td>HU</td>\n", "      <td>SM 1225 Gyor <PERSON>ek</td>\n", "      <td>Q4</td>\n", "      <td>P12</td>\n", "      <td>45018WEEK_51</td>\n", "      <td>51</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28830</th>\n", "      <td>45018</td>\n", "      <td>HU</td>\n", "      <td>SM 1225 Gyor <PERSON>ek</td>\n", "      <td>Q4</td>\n", "      <td>P12</td>\n", "      <td>45018WEEK_52</td>\n", "      <td>52</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28831</th>\n", "      <td>45018</td>\n", "      <td>HU</td>\n", "      <td>SM 1225 Gyor <PERSON>ek</td>\n", "      <td>Q4</td>\n", "      <td>P12</td>\n", "      <td>45018WEEK_53</td>\n", "      <td>53</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>28832 rows × 13 columns</p>\n", "</div>"], "text/plain": ["       Store Country                   Name QUARTER PERIOD   store match week  \\\n", "0      11001      CZ          Brno Herspice      Q1     P1   11001WEEK_1    1   \n", "1      11001      CZ          Brno Herspice      Q1     P1   11001WEEK_2    2   \n", "2      11001      CZ          Brno Herspice      Q1     P1   11001WEEK_3    3   \n", "3      11001      CZ          Brno Herspice      Q1     P1   11001WEEK_4    4   \n", "4      11001      CZ          Brno Herspice      Q1     P1   11001WEEK_5    5   \n", "...      ...     ...                    ...     ...    ...           ...  ...   \n", "28827  45018      HU  SM 1225 Gyor Koztelek      Q4    P12  45018WEEK_49   49   \n", "28828  45018      HU  SM 1225 Gyor Koztelek      Q4    P12  45018WEEK_50   50   \n", "28829  45018      HU  SM 1225 Gyor Koztelek      Q4    P12  45018WEEK_51   51   \n", "28830  45018      HU  SM 1225 Gyor Koztelek      Q4    P12  45018WEEK_52   52   \n", "28831  45018      HU  SM 1225 Gyor Koztelek      Q4    P12  45018WEEK_53   53   \n", "\n", "      Promotion fix New Sale fix Further sale fix Season change fix  \\\n", "0          8.945505            0                0                 0   \n", "1                 0            0         7.913464                 0   \n", "2          8.945505            0                0                 0   \n", "3                 0    33.329152                0          52.49194   \n", "4          8.945505            0                0                 0   \n", "...             ...          ...              ...               ...   \n", "28827             1            0                0                 1   \n", "28828             1            0                0                 0   \n", "28829             1            0                0                 0   \n", "28830             0            0                0                 0   \n", "28831             0            0                0                 0   \n", "\n", "       FIXED HOURS  VARIABLE CURRENCY  \n", "0        28.377378        9289.084774  \n", "1        28.377378        9289.084774  \n", "2        28.377378        9289.084774  \n", "3        28.377378        9289.084774  \n", "4        28.377378        9289.084774  \n", "...            ...                ...  \n", "28827     0.000000                NaN  \n", "28828     0.000000                NaN  \n", "28829     0.000000                NaN  \n", "28830     0.000000                NaN  \n", "28831     0.000000                NaN  \n", "\n", "[28832 rows x 13 columns]"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["ttb_df"]}, {"cell_type": "code", "execution_count": 52, "id": "c1a20506-f8eb-4cfd-bb74-044a15312e3e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ffc5b236-72d0-49ee-9ffd-4eaccdd0b5de", "metadata": {}, "outputs": [], "source": ["ttb_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "f21b95b1-222b-4ce1-88ba-040806d34ca1", "metadata": {"tags": []}, "outputs": [], "source": ["dict_list.keys()"]}, {"cell_type": "code", "execution_count": 48, "id": "76cf34f1-7627-4cfa-8a63-362e8ade6ee8", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Store</th>\n", "      <th>Name</th>\n", "      <th>Country</th>\n", "      <th>Format</th>\n", "      <th>Sub Operation Description</th>\n", "      <th>Activity Group</th>\n", "      <th>time_value_type</th>\n", "      <th>Dep</th>\n", "      <th>V F</th>\n", "      <th>Driver_1</th>\n", "      <th>Driver_2</th>\n", "      <th>Profile</th>\n", "      <th>Freq</th>\n", "      <th>Std</th>\n", "      <th>Driver_1_value</th>\n", "      <th>Driver_2_value</th>\n", "      <th>Profile_value</th>\n", "      <th>hours</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Remerchandising arms</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>arms</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>25.00</td>\n", "      <td>3.162097</td>\n", "      <td>2766.919922</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>36.455291</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td><PERSON>le mannequins</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>mannequins</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>100.00</td>\n", "      <td>75.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td><PERSON>le mannequin legs</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>mannequin legs</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>100.00</td>\n", "      <td>18.750000</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Handle POS - fix (once by event)</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>technical driver</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>100.00</td>\n", "      <td>0.239200</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.003987</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Handle POS - Slabs</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>module value</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>6.34</td>\n", "      <td>2.932800</td>\n", "      <td>221.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.684877</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Handle POS - A4 (or A/3) - fix (once by event)</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>technical driver</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>100.00</td>\n", "      <td>0.104000</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.001733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Handle POS - A4 (or A/3) - by POS</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>modul number</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>5.95</td>\n", "      <td>0.296400</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Handle POS - A6 and A7 - fix (once by event)</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>technical driver</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>100.00</td>\n", "      <td>0.104000</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.001733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Handle event - A6 and A7 - by POS</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>arms</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>20.00</td>\n", "      <td>0.312000</td>\n", "      <td>2766.919922</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>2.877597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>WALKING (all POS handling) - all types of WH</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>walk sf-wh</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>200.00</td>\n", "      <td>0.220480</td>\n", "      <td>188.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>1.381675</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>WALKING (all POS handling) - Two-storey F&amp;F wa...</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>technical driver</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>200.00</td>\n", "      <td>1.643200</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.054773</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Reorganize clothes on hanger at shopfloor</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>products stocked</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>16.00</td>\n", "      <td>0.041600</td>\n", "      <td>22403.833984</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>2.485332</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>WALKING to get rails</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>walk sf-wh</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>400.00</td>\n", "      <td>0.220480</td>\n", "      <td>188.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>2.763349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>WALKING to get rails- Two-storey F&amp;F warehouse...</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>technical driver</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>200.00</td>\n", "      <td>1.643200</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.054773</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Move rail inside the department</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>V</td>\n", "      <td>items delivered - rail number</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>100.00</td>\n", "      <td>0.176800</td>\n", "      <td>12.210990</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.035982</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Walking distance (remerch) - all types of WH</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>F</td>\n", "      <td>walk sf-wh</td>\n", "      <td>no_driver</td>\n", "      <td>no_driver</td>\n", "      <td>100.00</td>\n", "      <td>0.220480</td>\n", "      <td>188.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.690837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>11001</td>\n", "      <td>Brno Herspice</td>\n", "      <td>CZ</td>\n", "      <td>HML</td>\n", "      <td>Walking distance (remerch) - Two-storey F&amp;F wa...</td>\n", "      <td>Season change</td>\n", "      <td>season</td>\n", "      <td>CLG</td>\n", "      <td>F</td>\n", "      <td>technical driver</td>\n", "      <td>no_driver</td>\n", "      <td>two-storey f&amp;f warehouse with lift</td>\n", "      <td>200.00</td>\n", "      <td>1.643200</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Store           Name Country Format  \\\n", "78  11001  Brno Herspice      CZ    HML   \n", "79  11001  Brno Herspice      CZ    HML   \n", "80  11001  Brno Herspice      CZ    HML   \n", "81  11001  Brno Herspice      CZ    HML   \n", "82  11001  Brno Herspice      CZ    HML   \n", "83  11001  Brno Herspice      CZ    HML   \n", "84  11001  Brno Herspice      CZ    HML   \n", "85  11001  Brno Herspice      CZ    HML   \n", "86  11001  Brno Herspice      CZ    HML   \n", "87  11001  Brno Herspice      CZ    HML   \n", "88  11001  Brno Herspice      CZ    HML   \n", "89  11001  Brno Herspice      CZ    HML   \n", "90  11001  Brno Herspice      CZ    HML   \n", "91  11001  Brno Herspice      CZ    HML   \n", "92  11001  Brno Herspice      CZ    HML   \n", "93  11001  Brno Herspice      CZ    HML   \n", "94  11001  Brno Herspice      CZ    HML   \n", "\n", "                            Sub Operation Description Activity Group  \\\n", "78                               Remerchandising arms  Season change   \n", "79                                  Handle mannequins  Season change   \n", "80                              Handle mannequin legs  Season change   \n", "81                   Handle POS - fix (once by event)  Season change   \n", "82                                 Handle POS - Slabs  Season change   \n", "83     Handle POS - A4 (or A/3) - fix (once by event)  Season change   \n", "84                  Handle POS - A4 (or A/3) - by POS  Season change   \n", "85       Handle POS - A6 and A7 - fix (once by event)  Season change   \n", "86                  Handle event - A6 and A7 - by POS  Season change   \n", "87       WALKING (all POS handling) - all types of WH  Season change   \n", "88  WALKING (all POS handling) - Two-storey F&F wa...  Season change   \n", "89          Reorganize clothes on hanger at shopfloor  Season change   \n", "90                               WALKING to get rails  Season change   \n", "91  WALKING to get rails- Two-storey F&F warehouse...  Season change   \n", "92                    Move rail inside the department  Season change   \n", "93       Walking distance (remerch) - all types of WH  Season change   \n", "94  Walking distance (remerch) - Two-storey F&F wa...  Season change   \n", "\n", "   time_value_type  Dep V F                       Driver_1   Driver_2  \\\n", "78          season  CLG   V                           arms  no_driver   \n", "79          season  CLG   V                     mannequins  no_driver   \n", "80          season  CLG   V                 mannequin legs  no_driver   \n", "81          season  CLG   V               technical driver  no_driver   \n", "82          season  CLG   V                   module value  no_driver   \n", "83          season  CLG   V               technical driver  no_driver   \n", "84          season  CLG   V                   modul number  no_driver   \n", "85          season  CLG   V               technical driver  no_driver   \n", "86          season  CLG   V                           arms  no_driver   \n", "87          season  CLG   V                     walk sf-wh  no_driver   \n", "88          season  CLG   V               technical driver  no_driver   \n", "89          season  CLG   V               products stocked  no_driver   \n", "90          season  CLG   V                     walk sf-wh  no_driver   \n", "91          season  CLG   V               technical driver  no_driver   \n", "92          season  CLG   V  items delivered - rail number  no_driver   \n", "93          season  CLG   F                     walk sf-wh  no_driver   \n", "94          season  CLG   F               technical driver  no_driver   \n", "\n", "                               Profile    Freq        Std  Driver_1_value  \\\n", "78                           no_driver   25.00   3.162097     2766.919922   \n", "79                           no_driver  100.00  75.000000        0.000000   \n", "80                           no_driver  100.00  18.750000        0.000000   \n", "81                           no_driver  100.00   0.239200        1.000000   \n", "82                           no_driver    6.34   2.932800      221.000000   \n", "83                           no_driver  100.00   0.104000        1.000000   \n", "84                           no_driver    5.95   0.296400        0.000000   \n", "85                           no_driver  100.00   0.104000        1.000000   \n", "86                           no_driver   20.00   0.312000     2766.919922   \n", "87                           no_driver  200.00   0.220480      188.000000   \n", "88                           no_driver  200.00   1.643200        1.000000   \n", "89                           no_driver   16.00   0.041600    22403.833984   \n", "90                           no_driver  400.00   0.220480      188.000000   \n", "91                           no_driver  200.00   1.643200        1.000000   \n", "92                           no_driver  100.00   0.176800       12.210990   \n", "93                           no_driver  100.00   0.220480      188.000000   \n", "94  two-storey f&f warehouse with lift  200.00   1.643200        1.000000   \n", "\n", "    Driver_2_value  Profile_value      hours  \n", "78             1.0              0  36.455291  \n", "79             1.0              0   0.000000  \n", "80             1.0              0   0.000000  \n", "81             1.0              0   0.003987  \n", "82             1.0              0   0.684877  \n", "83             1.0              0   0.001733  \n", "84             1.0              0   0.000000  \n", "85             1.0              0   0.001733  \n", "86             1.0              0   2.877597  \n", "87             1.0              0   1.381675  \n", "88             1.0              0   0.054773  \n", "89             1.0              0   2.485332  \n", "90             1.0              0   2.763349  \n", "91             1.0              0   0.054773  \n", "92             1.0              0   0.035982  \n", "93             1.0              0   0.690837  \n", "94             1.0              0   0.000000  "]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["hours_df[(hours_df['Activity Group'] == 'Season change') &(hours_df.Store == 11001)]"]}, {"cell_type": "code", "execution_count": 33, "id": "aac584db-7f5c-4c34-acf7-9ffc9cbdb00f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[33.329151784231975]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": null, "id": "20d5a4fd-f536-4b46-ab9c-35e68fb3fd28", "metadata": {}, "outputs": [], "source": ["z"]}, {"cell_type": "code", "execution_count": null, "id": "c559af4b-97dc-41a2-9a6c-eefdd4a13b9d", "metadata": {}, "outputs": [], "source": ["s"]}, {"cell_type": "code", "execution_count": 15, "id": "ab7468a1-7226-4ee8-9980-f11bb3abda72", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Season change'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["x[1]"]}, {"cell_type": "code", "execution_count": 54, "id": "b1064ed3-0008-4b3e-b8a8-39fed731e53b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Store</th>\n", "      <th>Country</th>\n", "      <th>Name</th>\n", "      <th>QUARTER</th>\n", "      <th>PERIOD</th>\n", "      <th>store match</th>\n", "      <th>week</th>\n", "      <th>FIXED HOURS</th>\n", "      <th>VARIABLE CURRENCY</th>\n", "      <th>Promotion fix</th>\n", "      <th>New Sale fix</th>\n", "      <th>Further sale fix</th>\n", "      <th>Season change fix</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_1</td>\n", "      <td>1</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_2</td>\n", "      <td>2</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7.913464</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_3</td>\n", "      <td>3</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_4</td>\n", "      <td>4</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>0</td>\n", "      <td>33.329152</td>\n", "      <td>0</td>\n", "      <td>52.49194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P1</td>\n", "      <td>11001WEEK_5</td>\n", "      <td>5</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P2</td>\n", "      <td>11001WEEK_6</td>\n", "      <td>6</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P2</td>\n", "      <td>11001WEEK_7</td>\n", "      <td>7</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>7.913464</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P2</td>\n", "      <td>11001WEEK_8</td>\n", "      <td>8</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P2</td>\n", "      <td>11001WEEK_9</td>\n", "      <td>9</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>8.945505</td>\n", "      <td>33.329152</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P3</td>\n", "      <td>11001WEEK_10</td>\n", "      <td>10</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P3</td>\n", "      <td>11001WEEK_11</td>\n", "      <td>11</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P3</td>\n", "      <td>11001WEEK_12</td>\n", "      <td>12</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>7.913464</td>\n", "      <td>52.49194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q1</td>\n", "      <td>P3</td>\n", "      <td>11001WEEK_13</td>\n", "      <td>13</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q2</td>\n", "      <td>P4</td>\n", "      <td>11001WEEK_14</td>\n", "      <td>14</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11001</td>\n", "      <td>CZ</td>\n", "      <td>Brno Herspice</td>\n", "      <td>Q2</td>\n", "      <td>P4</td>\n", "      <td>11001WEEK_15</td>\n", "      <td>15</td>\n", "      <td>nan</td>\n", "      <td>nan</td>\n", "      <td>8.945505</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Store Country           Name QUARTER PERIOD   store match week FIXED HOURS  \\\n", "0  11001      CZ  Brno Herspice      Q1     P1   11001WEEK_1    1         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P1   11001WEEK_2    2         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P1   11001WEEK_3    3         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P1   11001WEEK_4    4         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P1   11001WEEK_5    5         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P2   11001WEEK_6    6         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P2   11001WEEK_7    7         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P2   11001WEEK_8    8         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P2   11001WEEK_9    9         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P3  11001WEEK_10   10         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P3  11001WEEK_11   11         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P3  11001WEEK_12   12         nan   \n", "0  11001      CZ  Brno Herspice      Q1     P3  11001WEEK_13   13         nan   \n", "0  11001      CZ  Brno Herspice      Q2     P4  11001WEEK_14   14         nan   \n", "0  11001      CZ  Brno Herspice      Q2     P4  11001WEEK_15   15         nan   \n", "\n", "  VARIABLE CURRENCY Promotion fix New Sale fix Further sale fix  \\\n", "0               nan      8.945505            0                0   \n", "0               nan             0            0         7.913464   \n", "0               nan      8.945505            0                0   \n", "0               nan             0    33.329152                0   \n", "0               nan      8.945505            0                0   \n", "0               nan             0            0                0   \n", "0               nan      8.945505            0         7.913464   \n", "0               nan             0            0                0   \n", "0               nan      8.945505    33.329152                0   \n", "0               nan             0            0                0   \n", "0               nan      8.945505            0                0   \n", "0               nan      8.945505            0         7.913464   \n", "0               nan      8.945505            0                0   \n", "0               nan             0            0                0   \n", "0               nan      8.945505            0                0   \n", "\n", "  Season change fix  \n", "0                 0  \n", "0                 0  \n", "0                 0  \n", "0          52.49194  \n", "0                 0  \n", "0                 0  \n", "0                 0  \n", "0                 0  \n", "0                 0  \n", "0                 0  \n", "0                 0  \n", "0          52.49194  \n", "0                 0  \n", "0                 0  \n", "0                 0  "]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["ttb_df.head(15)"]}, {"cell_type": "code", "execution_count": 53, "id": "25a6fbf6-ee67-4323-981e-a151a1f79d09", "metadata": {"tags": []}, "outputs": [], "source": ["\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "37fb44bd-fe95-4aae-8f2b-8734e241f591", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "19ff577a-a322-4c14-959d-f8bca48e4592", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}