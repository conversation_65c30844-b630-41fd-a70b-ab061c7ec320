import sys
import os
import pandas as pd
from pathlib import Path
import numpy as np

sys.path.append(os.path.dirname(Path.cwd()))
import Get_System_Data_SQL as gsd
from datetime import datetime
import Replenishment_Model_Functions_25 as rmf

directory = (
    Path(__file__).parent if "__file__" in locals() else Path.cwd()
).parent.parent


# directory = r"C:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023"
srd_tables_places = r"inputs/JDA_SRD_Tables/"

excel_inputs_f = "inputs/Repl/Stores_Inputs_2024_Q1_wo_prepacked_hours_fluct_cust_repaired.xlsx"


modelDataSet_as_is = pd.read_parquet(
    r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\Repl_Dataset_2024_SFP01_kristalycukor"
    )

# modelDataSet_as_is.rename(columns={'frozen_srp':'icream_nsrp'},inplace=True)

def JDA_SRD_CE_report():

    print("\nJDA_SRD_CE_report has been started....\n")

    folder_name = datetime.now().strftime("%d-%m-%Y")

    if not os.path.isdir(directory / srd_tables_places / f"{folder_name}"):

        os.makedirs(directory / srd_tables_places / f"{folder_name}")

    SRD_tables = gsd.SRD_database()
    
    # SRD_tables = gsd.SRD_table_added_hierarchy(SRD_tables)
    
    return SRD_tables, folder_name


SRD_tables, folder_name = JDA_SRD_CE_report()

# SRD_tables = rmf.optimize_objects(rmf.optimize_types(SRD_tables))
# SRD_tables = SRD_tables[SRD_tables.Store_Format != 0]



SRD_tables.to_csv(
    directory / srd_tables_places / f"{folder_name}/CE_JDA_SRD.csv.gzip", compression="gzip", index=False
)

opsdev, foil = gsd.SRD_to_opsdev(SRD_tables.copy(), directory, excel_inputs_f )

opsdev = rmf.optimize_objects(rmf.optimize_types(opsdev))

opsdev.to_parquet(
    directory / srd_tables_places / f"{folder_name}/CE_JDA_SRD_for_model", compression="gzip"
)

foil.to_parquet(
    directory / srd_tables_places / f"{folder_name}/foil", compression="gzip"
)

# =============================================================================
# if you need to make a comparison to 'AS IS' model
# =============================================================================
opsdev.columns = (opsdev.iloc[:, :4].columns.tolist()
                  + opsdev.iloc[:, 4:10].add_suffix("_new").columns.tolist()
                  + opsdev.iloc[:,10:].columns.tolist())

opsdev.drop("product_name", axis=1, inplace=True)


modelDataSet_as_is_ = modelDataSet_as_is.merge(opsdev[opsdev.iloc[:,:9].columns.tolist()], on=['country',
                                                                                              'store', 
                                                                                              'tpnb'], how='left')

for x, y in zip(["srp_new", "nsrp_new", "mu_new", "full_pallet_new", "split_pallet_new", "icream_nsrp_new"], ['srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', "icream_nsrp"]):
    cond = [modelDataSet_as_is_[x].notnull()]
    result = [modelDataSet_as_is_[x]]
    modelDataSet_as_is_[y] = np.select(cond, result, modelDataSet_as_is_[y])
    modelDataSet_as_is_.drop(x, axis=1, inplace=True)
    
rmf.optimize_objects(rmf.optimize_types(modelDataSet_as_is_)).to_parquet(directory / srd_tables_places / f"{folder_name}/as_is_modelDataSet_updated_{folder_name[:5]}", compression="gzip"
)







