{"cells": [{"cell_type": "code", "execution_count": 120, "id": "ea58d4f2-3399-4699-b290-fdbc9f43675b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 129, "id": "13c5a959-c5e8-4e70-ad9a-08113fb34ab9", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from pathlib import Path\n", "import pyarrow.parquet as pq\n", "import plotly.express as px\n", "import warnings\n", "import plotly.graph_objects as go\n", "import numpy as np\n", "from plotly.subplots import make_subplots\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "pd.set_option('display.max_columns', None)\n", "\n", "closed_store_list = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl\\Store list 2024 Q1.xlsx\", sheet_name=\"closed stores\")['Store'].unique().tolist()\n", "\n", "store_list = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2023\\inputs\\Repl\\Stores_Inputs_2023_Q1_Vol3.xlsx\").query(\"Store not in @closed_store_list\")['Store'].unique().tolist()\n", "\n", "dataset_23 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\as_is_modelDataSet_updated_12-06_op_type_frozen_srp_test_new_method_for_24q1\"\n", "dataset_24 = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\Repl_Dataset_2024\"\n", "plano = pd.read_csv(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\inputs\\files_for_dataset\\plano\\planogram_w14_w27.csv.gz\")\n", "\n", "comp_dataset = pd.DataFrame()\n", "for x, y in zip([dataset_23, dataset_24], [\"Vol_22_w14_27\", \"Vol_23_w14_27\"]):\n", "    df = pd.read_parquet(x).query(\"store in @store_list\").query(\"dep not in ['SFM', 'SFB']\")\n", "\n", "    if y == \"Vol_22_w14_27\":\n", "        df['capacity'] = df['shelfCapacity']\n", "    if y == \"Vol_23_w14_27\":\n", "        df = df.merge(plano[['store','tpnb','capacity']].drop_duplicates(), on=['store','tpnb'], how='left')\n", "    \n", "    #print(f\"{df.division.unique()}\")\n", "    df = df.groupby([\"country\", \"division\"], observed=True, as_index=False).agg({\"cases_delivered\":\"sum\",\"sold_units\":\"sum\",\"stock\":\"sum\",\"shelfCapacity\":\"sum\", \"unit\":\"sum\", \"capacity\":\"sum\"})\n", "    df['version'] = y\n", "\n", "    comp_dataset = pd.concat([comp_dataset, df])\n", "comp_dataset['stock'] = comp_dataset['stock']/7\n", "comp_dataset['shelfCapacity'] = comp_dataset['shelfCapacity']/7\n", "comp_dataset['capacity'] = comp_dataset['capacity']/7\n", "comp_dataset['division'] = np.where(comp_dataset['division'] == 'Fresh', 'Prepacked Fresh',  comp_dataset['division'])\n", "ce = comp_dataset.groupby(['division', 'version'],observed=True,as_index=False).sum()\n", "ce['country'] = 'CE'\n", "comp_dataset = pd.concat([comp_dataset, ce])\n", "comp_dataset.sort_values(by=[\"version\", 'division'], inplace=True)\n", "\n", "# total hours\n", "hours_df = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\OPB_DEP_DIFF__23vs24.xlsx\").query(\"Dep not in ['WH'] and Country.notnull()\")\n", "hours_df.columns = [x.lower() for x in hours_df.columns]\n", "hours_df = hours_df.groupby(['country', 'division'],as_index=False)[['with_2023_volume', 'with_2024_volume']].sum()\n", "hours_df['changes%'] = ((hours_df['with_2024_volume']-hours_df['with_2023_volume'])/hours_df['with_2023_volume'] *100 ).round(1).astype(str) + ' %'\n", "\n", "ce = hours_df.groupby(['division'],observed=True,as_index=False)[['with_2023_volume','with_2024_volume']].sum()\n", "ce['country'] = 'CE'\n", "ce['changes%'] = ((hours_df['with_2024_volume']-hours_df['with_2023_volume'])/hours_df['with_2023_volume'] *100 ).round(1).astype(str) + ' %'\n", "ce_ = ce.melt(id_vars=['country', 'division'], value_vars=['with_2023_volume', 'with_2024_volume'],var_name=\"version\")\n", "ce_ = ce_.merge(ce[['country','division','changes%']], on=['country','division'], how='left')\n", "\n", "hours_df_ = hours_df.melt(id_vars=['country', 'division'], value_vars=['with_2023_volume', 'with_2024_volume'],var_name=\"version\")\n", "hours_df_ = hours_df_.merge(hours_df[['country','division','changes%']], on=['country','division'], how='left')\n", "\n", "hours_df_ = pd.concat([hours_df_, ce_])\n", "hours_df_['division'] = np.where(hours_df_['division'] == 'General Merchandise', 'GM',  hours_df_['division'])\n", "hours_df_['changes%'] = np.where(hours_df_['version'] == 'with_2023_volume', 'no',  hours_df_['changes%'])\n", "\n", "# fix variable\n", "fix_var = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\OPB_Dep_2024 vs. 2023.xlsx\")[['Country','Store','Format','Dep','Division','Fix Hours_24','Variable Hours_24', 'Fix Hours_23','Variable Hours_23']]\n", "fix_var.columns = [x.lower() for x in fix_var.columns]\n", "fix = fix_var.melt(id_vars=['country','store','format','dep','division'], value_vars=['fix hours_24','fix hours_23'], var_name='version').sort_values(by=[\"version\", 'division'])\n", "ce = fix.groupby(['division','dep', 'version'],observed=True,as_index=False)['value'].sum()\n", "ce['country'] = 'CE'\n", "fix = pd.concat([fix, ce])\n", "fix['division'] = np.where(fix['division'] == 'General Merchandise', 'GM',  fix['division'])\n", "var = fix_var.melt(id_vars=['country','store','format','dep','division'], value_vars=['variable hours_24','variable hours_23'], var_name='version').sort_values(by=[\"version\", 'division'])\n", "ce = var.groupby(['division','dep', 'version'],observed=True,as_index=False)['value'].sum()\n", "ce['country'] = 'CE'\n", "var = pd.concat([var, ce])\n", "var['division'] = np.where(var['division'] == 'General Merchandise', 'GM',  var['division'])\n", "\n", "# cases to replenish\n", "cases_to_replenish = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\cases_to_replenish__23_asis.xlsx\").query(\"dep not in ['WH', 'PRO'] and country.notnull()\")\n", "cases_to_replenish = cases_to_replenish.melt(id_vars=['country', 'store', 'division', 'dep'], value_vars=['cases_to_replenish_23', 'cases_to_replenish_24'],var_name=\"version\")\n", "ce = cases_to_replenish.groupby(['division','dep','version'], observed=True,as_index=False)['value'].sum()\n", "ce['country']= 'CE'\n", "cases_to_replenish = pd.concat([cases_to_replenish, ce])\n", "cases_to_replenish['division'] = np.where(cases_to_replenish['division'] == 'General Merchandise', 'GM',  cases_to_replenish['division'])\n", "cases_to_replenish['division'] = np.where(cases_to_replenish['division'] == 'Fresh', 'Prepacked Fresh',  cases_to_replenish['division'])\n", "cases_to_replenish.sort_values(by=[\"version\", 'division'], inplace=True)\n", "\n", "#varC\n", "varc = pd.read_excel(r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\outputs\\OPB_Dep_2024 vs. 2023.xlsx\")\n", "varc.columns = [x.lower() for x in varc.columns]\n", "varc = varc[['country','format','division', 'variable currency_23', 'variable currency_24']].groupby(['country','division'],observed=True,as_index=False)[['variable currency_23', 'variable currency_24']].sum()\n", "varc['changes%'] = ((varc['variable currency_24']-varc['variable currency_23'])/varc['variable currency_23'] *100 ).round(1).astype(str) + ' %'\n", "varc_df = varc.melt(id_vars=['country','division'], value_vars=['variable currency_23', 'variable currency_24'], var_name='version')\n", "varc_df = varc_df.merge(varc[['country','division','changes%']], on=['country','division'], how='left')\n", "varc_df['changes%'] = np.where(varc_df['version'] == 'variable currency_23', 'no',  varc_df['changes%'])\n", "\n", "def combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                            separator=None, auto_open=False):\n", "    with open(html_fname, 'w') as f:\n", "        f.write(plotly_figs[0].to_html(include_plotlyjs=include_plotlyjs))\n", "        for fig in plotly_figs[1:]:\n", "            if separator:\n", "                f.write(separator)\n", "            f.write(fig.to_html(full_html=False, include_plotlyjs=False))\n", "\n", "    if auto_open:\n", "        import pathlib, webbrowser\n", "        uri = pathlib.Path(html_fname).absolute().as_uri()\n", "        webbrowser.open(uri)\n", "cont = []\n"]}, {"cell_type": "code", "execution_count": 223, "id": "bafc63d5-97c4-4998-9d9c-bd3f5e53c2b8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 221, "id": "78cc385a-8490-41c2-b050-f0651a20776e", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>division</th>\n", "      <th>version</th>\n", "      <th>value</th>\n", "      <th>changes%</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CZ</td>\n", "      <td>GM</td>\n", "      <td>with_2023_volume</td>\n", "      <td>7940.689289</td>\n", "      <td>-6.5 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CZ</td>\n", "      <td>Grocery</td>\n", "      <td>with_2023_volume</td>\n", "      <td>29297.549187</td>\n", "      <td>-8.2 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CZ</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>with_2023_volume</td>\n", "      <td>13922.126282</td>\n", "      <td>-12.6 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CZ</td>\n", "      <td>Produce</td>\n", "      <td>with_2023_volume</td>\n", "      <td>13284.234418</td>\n", "      <td>-8.9 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>HU</td>\n", "      <td>GM</td>\n", "      <td>with_2023_volume</td>\n", "      <td>12209.901861</td>\n", "      <td>-3.4 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>HU</td>\n", "      <td>Grocery</td>\n", "      <td>with_2023_volume</td>\n", "      <td>39642.264455</td>\n", "      <td>-5.2 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>HU</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>with_2023_volume</td>\n", "      <td>14574.324597</td>\n", "      <td>-17.8 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>HU</td>\n", "      <td>Produce</td>\n", "      <td>with_2023_volume</td>\n", "      <td>12874.976432</td>\n", "      <td>1.9 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>SK</td>\n", "      <td>GM</td>\n", "      <td>with_2023_volume</td>\n", "      <td>6891.980750</td>\n", "      <td>-9.1 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>SK</td>\n", "      <td>Grocery</td>\n", "      <td>with_2023_volume</td>\n", "      <td>26620.799365</td>\n", "      <td>-13.7 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>SK</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>with_2023_volume</td>\n", "      <td>10698.132659</td>\n", "      <td>-20.0 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>SK</td>\n", "      <td>Produce</td>\n", "      <td>with_2023_volume</td>\n", "      <td>11232.887009</td>\n", "      <td>-4.6 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>CZ</td>\n", "      <td>GM</td>\n", "      <td>with_2024_volume</td>\n", "      <td>7423.097566</td>\n", "      <td>-6.5 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>CZ</td>\n", "      <td>Grocery</td>\n", "      <td>with_2024_volume</td>\n", "      <td>26904.722386</td>\n", "      <td>-8.2 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>CZ</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>with_2024_volume</td>\n", "      <td>12165.979909</td>\n", "      <td>-12.6 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>CZ</td>\n", "      <td>Produce</td>\n", "      <td>with_2024_volume</td>\n", "      <td>12106.861245</td>\n", "      <td>-8.9 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>HU</td>\n", "      <td>GM</td>\n", "      <td>with_2024_volume</td>\n", "      <td>11791.113480</td>\n", "      <td>-3.4 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>HU</td>\n", "      <td>Grocery</td>\n", "      <td>with_2024_volume</td>\n", "      <td>37574.350006</td>\n", "      <td>-5.2 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>HU</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>with_2024_volume</td>\n", "      <td>11974.581866</td>\n", "      <td>-17.8 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>HU</td>\n", "      <td>Produce</td>\n", "      <td>with_2024_volume</td>\n", "      <td>13118.789773</td>\n", "      <td>1.9 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>SK</td>\n", "      <td>GM</td>\n", "      <td>with_2024_volume</td>\n", "      <td>6266.464659</td>\n", "      <td>-9.1 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>SK</td>\n", "      <td>Grocery</td>\n", "      <td>with_2024_volume</td>\n", "      <td>22979.499919</td>\n", "      <td>-13.7 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>SK</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>with_2024_volume</td>\n", "      <td>8561.070520</td>\n", "      <td>-20.0 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>SK</td>\n", "      <td>Produce</td>\n", "      <td>with_2024_volume</td>\n", "      <td>10712.786085</td>\n", "      <td>-4.6 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CE</td>\n", "      <td>GM</td>\n", "      <td>with_2023_volume</td>\n", "      <td>27042.571900</td>\n", "      <td>-6.5 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CE</td>\n", "      <td>Grocery</td>\n", "      <td>with_2023_volume</td>\n", "      <td>95560.613006</td>\n", "      <td>-8.2 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CE</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>with_2023_volume</td>\n", "      <td>39194.583538</td>\n", "      <td>-12.6 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CE</td>\n", "      <td>Produce</td>\n", "      <td>with_2023_volume</td>\n", "      <td>37392.097859</td>\n", "      <td>-8.9 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CE</td>\n", "      <td>GM</td>\n", "      <td>with_2024_volume</td>\n", "      <td>25480.675705</td>\n", "      <td>-6.5 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>CE</td>\n", "      <td>Grocery</td>\n", "      <td>with_2024_volume</td>\n", "      <td>87458.572311</td>\n", "      <td>-8.2 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>CE</td>\n", "      <td>Prepacked Fresh</td>\n", "      <td>with_2024_volume</td>\n", "      <td>32701.632296</td>\n", "      <td>-12.6 %</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>CE</td>\n", "      <td>Produce</td>\n", "      <td>with_2024_volume</td>\n", "      <td>35938.437103</td>\n", "      <td>-8.9 %</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   country         division           version         value changes%\n", "0       CZ               GM  with_2023_volume   7940.689289   -6.5 %\n", "1       CZ          Grocery  with_2023_volume  29297.549187   -8.2 %\n", "2       CZ  Prepacked Fresh  with_2023_volume  13922.126282  -12.6 %\n", "3       CZ          Produce  with_2023_volume  13284.234418   -8.9 %\n", "4       HU               GM  with_2023_volume  12209.901861   -3.4 %\n", "5       HU          Grocery  with_2023_volume  39642.264455   -5.2 %\n", "6       HU  Prepacked Fresh  with_2023_volume  14574.324597  -17.8 %\n", "7       HU          Produce  with_2023_volume  12874.976432    1.9 %\n", "8       SK               GM  with_2023_volume   6891.980750   -9.1 %\n", "9       SK          Grocery  with_2023_volume  26620.799365  -13.7 %\n", "10      SK  Prepacked Fresh  with_2023_volume  10698.132659  -20.0 %\n", "11      SK          Produce  with_2023_volume  11232.887009   -4.6 %\n", "12      CZ               GM  with_2024_volume   7423.097566   -6.5 %\n", "13      CZ          Grocery  with_2024_volume  26904.722386   -8.2 %\n", "14      CZ  Prepacked Fresh  with_2024_volume  12165.979909  -12.6 %\n", "15      CZ          Produce  with_2024_volume  12106.861245   -8.9 %\n", "16      HU               GM  with_2024_volume  11791.113480   -3.4 %\n", "17      HU          Grocery  with_2024_volume  37574.350006   -5.2 %\n", "18      HU  Prepacked Fresh  with_2024_volume  11974.581866  -17.8 %\n", "19      HU          Produce  with_2024_volume  13118.789773    1.9 %\n", "20      SK               GM  with_2024_volume   6266.464659   -9.1 %\n", "21      SK          Grocery  with_2024_volume  22979.499919  -13.7 %\n", "22      SK  Prepacked Fresh  with_2024_volume   8561.070520  -20.0 %\n", "23      SK          Produce  with_2024_volume  10712.786085   -4.6 %\n", "0       CE               GM  with_2023_volume  27042.571900   -6.5 %\n", "1       CE          Grocery  with_2023_volume  95560.613006   -8.2 %\n", "2       CE  Prepacked Fresh  with_2023_volume  39194.583538  -12.6 %\n", "3       CE          Produce  with_2023_volume  37392.097859   -8.9 %\n", "4       CE               GM  with_2024_volume  25480.675705   -6.5 %\n", "5       CE          Grocery  with_2024_volume  87458.572311   -8.2 %\n", "6       CE  Prepacked Fresh  with_2024_volume  32701.632296  -12.6 %\n", "7       CE          Produce  with_2024_volume  35938.437103   -8.9 %"]}, "execution_count": 221, "metadata": {}, "output_type": "execute_result"}], "source": ["hours_df_"]}, {"cell_type": "code", "execution_count": 224, "id": "6e43f0a9-96cd-4826-b5a9-8b6b8b014ea0", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "customdata": [["no"], ["no"], ["no"], ["no"]], "hovertemplate": "version=with_2023_volume<br>country=CZ<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "with_2023_volume", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "with_2023_volume", "offsetgroup": "with_2023_volume", "orientation": "v", "showlegend": true, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["GM", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x", "y": [7940.689288553921, 29297.549186749282, 13922.126281619623, 13284.234417863248], "yaxis": "y"}, {"alignmentgroup": "True", "customdata": [["no"], ["no"], ["no"], ["no"]], "hovertemplate": "version=with_2023_volume<br>country=HU<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "with_2023_volume", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "with_2023_volume", "offsetgroup": "with_2023_volume", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["GM", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x2", "y": [12209.901860892996, 39642.26445502482, 14574.324597175026, 12874.976432389336], "yaxis": "y2"}, {"alignmentgroup": "True", "customdata": [["no"], ["no"], ["no"], ["no"]], "hovertemplate": "version=with_2023_volume<br>country=SK<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "with_2023_volume", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "with_2023_volume", "offsetgroup": "with_2023_volume", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["GM", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x3", "y": [6891.980750408721, 26620.799364697417, 10698.132659051747, 11232.887008677499], "yaxis": "y3"}, {"alignmentgroup": "True", "customdata": [["no"], ["no"], ["no"], ["no"]], "hovertemplate": "version=with_2023_volume<br>country=CE<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "with_2023_volume", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "with_2023_volume", "offsetgroup": "with_2023_volume", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["GM", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x4", "y": [27042.571899855637, 95560.61300647153, 39194.583537846396, 37392.09785893008], "yaxis": "y4"}, {"alignmentgroup": "True", "customdata": [["-6.5 %"], ["-8.2 %"], ["-12.6 %"], ["-8.9 %"]], "hovertemplate": "version=with_2024_volume<br>country=CZ<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "with_2024_volume", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "with_2024_volume", "offsetgroup": "with_2024_volume", "orientation": "v", "showlegend": true, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["GM", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x", "y": [7423.09756648282, 26904.72238642513, 12165.979909251502, 12106.861245076174], "yaxis": "y"}, {"alignmentgroup": "True", "customdata": [["-3.4 %"], ["-5.2 %"], ["-17.8 %"], ["1.9 %"]], "hovertemplate": "version=with_2024_volume<br>country=HU<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "with_2024_volume", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "with_2024_volume", "offsetgroup": "with_2024_volume", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["GM", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x2", "y": [11791.113479643904, 37574.3500057728, 11974.581866433104, 13118.789772732542], "yaxis": "y2"}, {"alignmentgroup": "True", "customdata": [["-9.1 %"], ["-13.7 %"], ["-20.0 %"], ["-4.6 %"]], "hovertemplate": "version=with_2024_volume<br>country=SK<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "with_2024_volume", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "with_2024_volume", "offsetgroup": "with_2024_volume", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["GM", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x3", "y": [6266.4646585322, 22979.4999192912, 8561.070520245703, 10712.786085040394], "yaxis": "y3"}, {"alignmentgroup": "True", "customdata": [["-6.5 %"], ["-8.2 %"], ["-12.6 %"], ["-8.9 %"]], "hovertemplate": "version=with_2024_volume<br>country=CE<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "with_2024_volume", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "with_2024_volume", "offsetgroup": "with_2024_volume", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["GM", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x4", "y": [25480.675704658923, 87458.57231148913, 32701.632295930307, 35938.43710284911], "yaxis": "y4"}], "layout": {"annotations": [{"font": {}, "showarrow": false, "text": "country=CZ", "x": 0.1175, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {}, "showarrow": false, "text": "country=HU", "x": 0.3725, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {}, "showarrow": false, "text": "country=SK", "x": 0.6275, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {}, "showarrow": false, "text": "country=CE", "x": 0.8824999999999998, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}], "barmode": "group", "height": 400, "hoverlabel": {"bgcolor": "white", "font": {"family": "<PERSON><PERSON>", "size": 16}}, "legend": {"title": {"text": "version"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "<b>Total Hours 2023 vs. 2024</b>"}, "width": 1800, "xaxis": {"anchor": "y", "autorange": true, "domain": [0, 0.235], "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "xaxis2": {"anchor": "y2", "autorange": true, "domain": [0.255, 0.49], "matches": "x", "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "xaxis3": {"anchor": "y3", "autorange": true, "domain": [0.51, 0.745], "matches": "x", "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "xaxis4": {"anchor": "y4", "autorange": true, "domain": [0.7649999999999999, 0.9999999999999999], "matches": "x", "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "yaxis": {"anchor": "x", "autorange": true, "domain": [0, 1], "range": [0, 30839.525459736087], "title": {"text": "value"}, "type": "linear"}, "yaxis2": {"anchor": "x2", "autorange": true, "domain": [0, 1], "range": [0, 41728.69942634192], "showticklabels": false, "type": "linear"}, "yaxis3": {"anchor": "x3", "autorange": true, "domain": [0, 1], "range": [0, 28021.894068102545], "showticklabels": false, "type": "linear"}, "yaxis4": {"anchor": "x4", "autorange": true, "domain": [0, 1], "range": [0, 100590.11895418055], "showticklabels": false, "type": "linear"}}}, "image/png": "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", "text/html": ["<div>                            <div id=\"b2b57331-1d9c-41cd-9283-4de8f4c378b1\" class=\"plotly-graph-div\" style=\"height:400px; width:1800px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"b2b57331-1d9c-41cd-9283-4de8f4c378b1\")) {                    Plotly.newPlot(                        \"b2b57331-1d9c-41cd-9283-4de8f4c378b1\",                        [{\"alignmentgroup\":\"True\",\"customdata\":[[\"no\"],[\"no\"],[\"no\"],[\"no\"]],\"hovertemplate\":\"version=with_2023_volume<br>country=CZ<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"with_2023_volume\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"with_2023_volume\",\"offsetgroup\":\"with_2023_volume\",\"orientation\":\"v\",\"showlegend\":true,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x\",\"y\":[7940.689288553921,29297.549186749282,13922.126281619623,13284.234417863248],\"yaxis\":\"y\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"no\"],[\"no\"],[\"no\"],[\"no\"]],\"hovertemplate\":\"version=with_2023_volume<br>country=HU<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"with_2023_volume\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"with_2023_volume\",\"offsetgroup\":\"with_2023_volume\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x2\",\"y\":[12209.901860892996,39642.26445502482,14574.324597175026,12874.976432389336],\"yaxis\":\"y2\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"no\"],[\"no\"],[\"no\"],[\"no\"]],\"hovertemplate\":\"version=with_2023_volume<br>country=SK<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"with_2023_volume\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"with_2023_volume\",\"offsetgroup\":\"with_2023_volume\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x3\",\"y\":[6891.980750408721,26620.799364697417,10698.132659051747,11232.887008677499],\"yaxis\":\"y3\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"no\"],[\"no\"],[\"no\"],[\"no\"]],\"hovertemplate\":\"version=with_2023_volume<br>country=CE<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"with_2023_volume\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"with_2023_volume\",\"offsetgroup\":\"with_2023_volume\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x4\",\"y\":[27042.571899855637,95560.61300647153,39194.583537846396,37392.09785893008],\"yaxis\":\"y4\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"-6.5 %\"],[\"-8.2 %\"],[\"-12.6 %\"],[\"-8.9 %\"]],\"hovertemplate\":\"version=with_2024_volume<br>country=CZ<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"with_2024_volume\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"with_2024_volume\",\"offsetgroup\":\"with_2024_volume\",\"orientation\":\"v\",\"showlegend\":true,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x\",\"y\":[7423.09756648282,26904.72238642513,12165.979909251502,12106.861245076174],\"yaxis\":\"y\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"-3.4 %\"],[\"-5.2 %\"],[\"-17.8 %\"],[\"1.9 %\"]],\"hovertemplate\":\"version=with_2024_volume<br>country=HU<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"with_2024_volume\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"with_2024_volume\",\"offsetgroup\":\"with_2024_volume\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x2\",\"y\":[11791.113479643904,37574.3500057728,11974.581866433104,13118.789772732542],\"yaxis\":\"y2\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"-9.1 %\"],[\"-13.7 %\"],[\"-20.0 %\"],[\"-4.6 %\"]],\"hovertemplate\":\"version=with_2024_volume<br>country=SK<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"with_2024_volume\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"with_2024_volume\",\"offsetgroup\":\"with_2024_volume\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x3\",\"y\":[6266.4646585322,22979.4999192912,8561.070520245703,10712.786085040394],\"yaxis\":\"y3\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"-6.5 %\"],[\"-8.2 %\"],[\"-12.6 %\"],[\"-8.9 %\"]],\"hovertemplate\":\"version=with_2024_volume<br>country=CE<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"with_2024_volume\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"with_2024_volume\",\"offsetgroup\":\"with_2024_volume\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x4\",\"y\":[25480.675704658923,87458.57231148913,32701.632295930307,35938.43710284911],\"yaxis\":\"y4\",\"type\":\"bar\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.235],\"title\":{\"text\":\"division\"}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"value\"}},\"xaxis2\":{\"anchor\":\"y2\",\"domain\":[0.255,0.49],\"matches\":\"x\",\"title\":{\"text\":\"division\"}},\"yaxis2\":{\"anchor\":\"x2\",\"domain\":[0.0,1.0],\"showticklabels\":false},\"xaxis3\":{\"anchor\":\"y3\",\"domain\":[0.51,0.745],\"matches\":\"x\",\"title\":{\"text\":\"division\"}},\"yaxis3\":{\"anchor\":\"x3\",\"domain\":[0.0,1.0],\"showticklabels\":false},\"xaxis4\":{\"anchor\":\"y4\",\"domain\":[0.7649999999999999,0.9999999999999999],\"matches\":\"x\",\"title\":{\"text\":\"division\"}},\"yaxis4\":{\"anchor\":\"x4\",\"domain\":[0.0,1.0],\"showticklabels\":false},\"annotations\":[{\"font\":{},\"showarrow\":false,\"text\":\"country=CZ\",\"x\":0.1175,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{},\"showarrow\":false,\"text\":\"country=HU\",\"x\":0.3725,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{},\"showarrow\":false,\"text\":\"country=SK\",\"x\":0.6275,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{},\"showarrow\":false,\"text\":\"country=CE\",\"x\":0.8824999999999998,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"}],\"legend\":{\"title\":{\"text\":\"version\"},\"tracegroupgap\":0},\"title\":{\"text\":\"<b>Total Hours 2023 vs. 2024</b>\"},\"barmode\":\"group\",\"height\":400,\"width\":1800,\"hoverlabel\":{\"font\":{\"size\":16,\"family\":\"Rockwell\"},\"bgcolor\":\"white\"}},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('b2b57331-1d9c-41cd-9283-4de8f4c378b1');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = px.bar(hours_df_, x=\"division\", y=\"value\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>Total Hours 2023 vs. 2024</b>\",\n", "                           height=400,\n", "              width = 1800, facet_col=\"country\", hover_data=['changes%'] ).update_yaxes(matches=None)\n", "\n", "\n", "fig.update_layout(\n", "    hoverlabel=dict(\n", "        bgcolor=\"white\",\n", "        font_size=16,\n", "        font_family=\"Rockwell\",\n", "\n", "    ))\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 209, "id": "f496a830-e1ab-4b63-85c9-f6416cdbe358", "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "customdata": [["no"], ["no"], ["no"], ["no"]], "hovertemplate": "version=variable currency_23<br>country=CZ<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "variable currency_23", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "variable currency_23", "offsetgroup": "variable currency_23", "orientation": "v", "showlegend": true, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["General Merchandise", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x", "y": [1294929.7109101, 6223855.911520301, 7575412.738872088, 1120593.5845969694], "yaxis": "y"}, {"alignmentgroup": "True", "customdata": [["no"], ["no"], ["no"], ["no"]], "hovertemplate": "version=variable currency_23<br>country=HU<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "variable currency_23", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "variable currency_23", "offsetgroup": "variable currency_23", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["General Merchandise", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x2", "y": [27354603.22426336, 95371815.4206692, 127478551.10167336, 16773178.968944319], "yaxis": "y2"}, {"alignmentgroup": "True", "customdata": [["no"], ["no"], ["no"], ["no"]], "hovertemplate": "version=variable currency_23<br>country=SK<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "variable currency_23", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "variable currency_23", "offsetgroup": "variable currency_23", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["General Merchandise", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x3", "y": [49367.54780302135, 249671.36445024543, 272143.1333892453, 42892.51205021229], "yaxis": "y3"}, {"alignmentgroup": "True", "customdata": [["20.2 %"], ["4.1 %"], ["16.3 %"], ["13.8 %"]], "hovertemplate": "version=variable currency_24<br>country=CZ<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "variable currency_24", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "variable currency_24", "offsetgroup": "variable currency_24", "orientation": "v", "showlegend": true, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["General Merchandise", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x", "y": [1557080.31995971, 6477385.154987169, 8813466.664672654, 1274891.048395363], "yaxis": "y"}, {"alignmentgroup": "True", "customdata": [["9.4 %"], ["17.2 %"], ["30.0 %"], ["-3.6 %"]], "hovertemplate": "version=variable currency_24<br>country=HU<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "variable currency_24", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "variable currency_24", "offsetgroup": "variable currency_24", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["General Merchandise", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x2", "y": [29926307.6023276, 111735985.92582199, 165659047.32280505, 16175974.76827599], "yaxis": "y2"}, {"alignmentgroup": "True", "customdata": [["19.6 %"], ["18.3 %"], ["40.0 %"], ["15.0 %"]], "hovertemplate": "version=variable currency_24<br>country=SK<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>", "legendgroup": "variable currency_24", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "variable currency_24", "offsetgroup": "variable currency_24", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y:.2s}", "type": "bar", "x": ["General Merchandise", "Grocery", "Prepacked Fresh", "Produce"], "xaxis": "x3", "y": [59034.43039589737, 295320.6712268974, 381087.2033159442, 49316.95478304137], "yaxis": "y3"}], "layout": {"annotations": [{"font": {}, "showarrow": false, "text": "country=CZ", "x": 0.15999999999999998, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {}, "showarrow": false, "text": "country=HU", "x": 0.49999999999999994, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {}, "showarrow": false, "text": "country=SK", "x": 0.8399999999999999, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}], "barmode": "group", "height": 400, "hoverlabel": {"bgcolor": "white", "font": {"family": "<PERSON><PERSON>", "size": 16}}, "legend": {"title": {"text": "version"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "<b>VarC% 2023 vs. 2024</b>"}, "width": 1800, "xaxis": {"anchor": "y", "autorange": true, "domain": [0, 0.31999999999999995], "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "xaxis2": {"anchor": "y2", "autorange": true, "domain": [0.33999999999999997, 0.6599999999999999], "matches": "x", "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "xaxis3": {"anchor": "y3", "autorange": true, "domain": [0.6799999999999999, 0.9999999999999999], "matches": "x", "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "yaxis": {"anchor": "x", "autorange": true, "domain": [0, 1], "range": [0, 9277333.331234373], "title": {"text": "value"}, "type": "linear"}, "yaxis2": {"anchor": "x2", "autorange": true, "domain": [0, 1], "range": [0, 174377944.5503211], "showticklabels": false, "type": "linear"}, "yaxis3": {"anchor": "x3", "autorange": true, "domain": [0, 1], "range": [0, 401144.4245430991], "showticklabels": false, "type": "linear"}}}, "image/png": "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", "text/html": ["<div>                            <div id=\"96dfc327-d722-48df-973f-152c4b4889aa\" class=\"plotly-graph-div\" style=\"height:400px; width:1800px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"96dfc327-d722-48df-973f-152c4b4889aa\")) {                    Plotly.newPlot(                        \"96dfc327-d722-48df-973f-152c4b4889aa\",                        [{\"alignmentgroup\":\"True\",\"customdata\":[[\"no\"],[\"no\"],[\"no\"],[\"no\"]],\"hovertemplate\":\"version=variable currency_23<br>country=CZ<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"variable currency_23\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"variable currency_23\",\"offsetgroup\":\"variable currency_23\",\"orientation\":\"v\",\"showlegend\":true,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"General Merchandise\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x\",\"y\":[1294929.7109101,6223855.911520301,7575412.738872088,1120593.5845969694],\"yaxis\":\"y\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"no\"],[\"no\"],[\"no\"],[\"no\"]],\"hovertemplate\":\"version=variable currency_23<br>country=HU<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"variable currency_23\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"variable currency_23\",\"offsetgroup\":\"variable currency_23\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"General Merchandise\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x2\",\"y\":[27354603.22426336,95371815.4206692,127478551.10167336,16773178.968944319],\"yaxis\":\"y2\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"no\"],[\"no\"],[\"no\"],[\"no\"]],\"hovertemplate\":\"version=variable currency_23<br>country=SK<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"variable currency_23\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"variable currency_23\",\"offsetgroup\":\"variable currency_23\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"General Merchandise\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x3\",\"y\":[49367.54780302135,249671.36445024543,272143.1333892453,42892.51205021229],\"yaxis\":\"y3\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"20.2 %\"],[\"4.1 %\"],[\"16.3 %\"],[\"13.8 %\"]],\"hovertemplate\":\"version=variable currency_24<br>country=CZ<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"variable currency_24\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"variable currency_24\",\"offsetgroup\":\"variable currency_24\",\"orientation\":\"v\",\"showlegend\":true,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"General Merchandise\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x\",\"y\":[1557080.31995971,6477385.154987169,8813466.664672654,1274891.048395363],\"yaxis\":\"y\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"9.4 %\"],[\"17.2 %\"],[\"30.0 %\"],[\"-3.6 %\"]],\"hovertemplate\":\"version=variable currency_24<br>country=HU<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"variable currency_24\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"variable currency_24\",\"offsetgroup\":\"variable currency_24\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"General Merchandise\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x2\",\"y\":[29926307.6023276,111735985.92582199,165659047.32280505,16175974.76827599],\"yaxis\":\"y2\",\"type\":\"bar\"},{\"alignmentgroup\":\"True\",\"customdata\":[[\"19.6 %\"],[\"18.3 %\"],[\"40.0 %\"],[\"15.0 %\"]],\"hovertemplate\":\"version=variable currency_24<br>country=SK<br>division=%{x}<br>value=%{y}<br>changes%=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"variable currency_24\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"variable currency_24\",\"offsetgroup\":\"variable currency_24\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"texttemplate\":\"%{y:.2s}\",\"x\":[\"General Merchandise\",\"Grocery\",\"Prepacked Fresh\",\"Produce\"],\"xaxis\":\"x3\",\"y\":[59034.43039589737,295320.6712268974,381087.2033159442,49316.95478304137],\"yaxis\":\"y3\",\"type\":\"bar\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.31999999999999995],\"title\":{\"text\":\"division\"}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"value\"}},\"xaxis2\":{\"anchor\":\"y2\",\"domain\":[0.33999999999999997,0.6599999999999999],\"matches\":\"x\",\"title\":{\"text\":\"division\"}},\"yaxis2\":{\"anchor\":\"x2\",\"domain\":[0.0,1.0],\"showticklabels\":false},\"xaxis3\":{\"anchor\":\"y3\",\"domain\":[0.6799999999999999,0.9999999999999999],\"matches\":\"x\",\"title\":{\"text\":\"division\"}},\"yaxis3\":{\"anchor\":\"x3\",\"domain\":[0.0,1.0],\"showticklabels\":false},\"annotations\":[{\"font\":{},\"showarrow\":false,\"text\":\"country=CZ\",\"x\":0.15999999999999998,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{},\"showarrow\":false,\"text\":\"country=HU\",\"x\":0.49999999999999994,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{},\"showarrow\":false,\"text\":\"country=SK\",\"x\":0.8399999999999999,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"}],\"legend\":{\"title\":{\"text\":\"version\"},\"tracegroupgap\":0},\"title\":{\"text\":\"<b>VarC% 2023 vs. 2024</b>\"},\"barmode\":\"group\",\"height\":400,\"width\":1800,\"hoverlabel\":{\"font\":{\"size\":16,\"family\":\"Rockwell\"},\"bgcolor\":\"white\"}},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('96dfc327-d722-48df-973f-152c4b4889aa');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = px.bar(varc_df, x=\"division\", y=\"value\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>VarC% 2023 vs. 2024</b>\",\n", "                           height=400,\n", "              width = 1800, facet_col=\"country\", hover_data=['changes%'] ).update_yaxes(matches=None)\n", "\n", "\n", "fig.update_layout(\n", "    hoverlabel=dict(\n", "        bgcolor=\"white\",\n", "        font_size=16,\n", "        font_family=\"Rockwell\",\n", "\n", "    ))\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "a8b07996-c39e-45d4-bdc4-2a1a0935c50c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 138, "id": "fcc2dfcc-7503-4438-be2a-c467e534842c", "metadata": {}, "outputs": [], "source": ["cont = []"]}, {"cell_type": "markdown", "id": "4d3d3707-f21f-4845-9f8a-c893757b12ec", "metadata": {}, "source": ["# Hours comparison"]}, {"cell_type": "markdown", "id": "7da2841d-4263-433a-9fcb-b0cf45c02f9a", "metadata": {}, "source": ["### total by countries:\n", "* CE: -5000"]}, {"cell_type": "markdown", "id": "852d387c-c175-41c2-ab10-da47486074ac", "metadata": {}, "source": ["### total hours"]}, {"cell_type": "code", "execution_count": 139, "id": "8ae7f9ee-0115-48d6-b57e-f5c866197d78", "metadata": {"scrolled": true}, "outputs": [], "source": ["fig = px.histogram(hours_df, x=\"division\", y=\"value\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>hours 2023 vs. 2024</b>\",\n", "                           height=400,\n", "              width = 1800, facet_col=\"country\")\n", "            \n", "cont.append(fig)"]}, {"cell_type": "markdown", "id": "cd9698cb-240a-4d22-9ed3-f88eebed0e9b", "metadata": {}, "source": ["* ### fix hours"]}, {"cell_type": "code", "execution_count": 140, "id": "4e59c457-de93-4e96-a1c1-d1e3a5907733", "metadata": {}, "outputs": [], "source": ["fig = px.histogram(fix, x=\"division\", y=\"value\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>Fix hours 2023 vs. 2024</b>\",\n", "                           height=400,\n", "              width = 1800, facet_col=\"country\")\n", "            \n", "cont.append(fig)"]}, {"cell_type": "markdown", "id": "36c569bf-73c7-458a-8ca3-263c8f927dcb", "metadata": {}, "source": ["* ### variable hours"]}, {"cell_type": "code", "execution_count": 141, "id": "5f8cea05-fe34-4bd7-bb89-6e1954adb322", "metadata": {}, "outputs": [], "source": ["fig = px.histogram(var, x=\"division\", y=\"value\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>Variable Hours 2023 vs. 2024</b>\",\n", "                           height=400,\n", "              width = 1800, facet_col=\"country\")\n", "            \n", "cont.append(fig)"]}, {"cell_type": "markdown", "id": "7e2b8546-3f52-4677-a4b6-a524ae501c04", "metadata": {}, "source": ["## Volumes comparison"]}, {"cell_type": "code", "execution_count": 142, "id": "52ef2d62-b792-4bce-b520-f9d720773c50", "metadata": {"scrolled": true}, "outputs": [], "source": ["fig = px.histogram(comp_dataset, x=\"division\", y=\"cases_delivered\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>CE_cases_delivered</b>\",\n", "                           height=400,\n", "              width = 1800, facet_col=\"country\")\n", "            \n", "cont.append(fig)"]}, {"cell_type": "code", "execution_count": 96, "id": "e9d44c71-0b92-4521-81c0-9c2c6e7bddad", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 143, "id": "65b33e08-b5ed-4e52-94fb-d9ed690aeb80", "metadata": {"scrolled": true}, "outputs": [], "source": ["\n", "\n", "\n", "fig = px.histogram(cases_to_replenish, x=\"division\", y=\"value\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>cases_to_replenish 2023 vs. 2024</b>\",\n", "                           height=400,\n", "              width = 1800, facet_col=\"country\")#.update_layout(**default)\n", "\n", "            \n", "cont.append(fig)"]}, {"cell_type": "code", "execution_count": 7, "id": "8135c2af-0263-40cf-8b3b-5114e9b4819d", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true, "source_hidden": true}}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"alignmentgroup": "True", "bingroup": "x", "histfunc": "sum", "hovertemplate": "version=Volume_2022_w14_27 (avg. week)<br>country=CZ<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>", "legendgroup": "Volume_2022_w14_27 (avg. week)", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "Volume_2022_w14_27 (avg. week)", "offsetgroup": "Volume_2022_w14_27 (avg. week)", "orientation": "v", "showlegend": true, "texttemplate": "%{value:.2s}", "type": "histogram", "x": ["Fresh", "GM", "Grocery", "Produce"], "xaxis": "x", "y": [10109786, 9774648, 61133997, 639310], "yaxis": "y"}, {"alignmentgroup": "True", "bingroup": "x", "histfunc": "sum", "hovertemplate": "version=Volume_2022_w14_27 (avg. week)<br>country=HU<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>", "legendgroup": "Volume_2022_w14_27 (avg. week)", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "Volume_2022_w14_27 (avg. week)", "offsetgroup": "Volume_2022_w14_27 (avg. week)", "orientation": "v", "showlegend": false, "texttemplate": "%{value:.2s}", "type": "histogram", "x": ["Fresh", "GM", "Grocery", "Produce"], "xaxis": "x2", "y": [9716671, 16802430, 67406856, 878358], "yaxis": "y2"}, {"alignmentgroup": "True", "bingroup": "x", "histfunc": "sum", "hovertemplate": "version=Volume_2022_w14_27 (avg. week)<br>country=SK<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>", "legendgroup": "Volume_2022_w14_27 (avg. week)", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "Volume_2022_w14_27 (avg. week)", "offsetgroup": "Volume_2022_w14_27 (avg. week)", "orientation": "v", "showlegend": false, "texttemplate": "%{value:.2s}", "type": "histogram", "x": ["Fresh", "GM", "Grocery", "Produce"], "xaxis": "x3", "y": [7455515, 7207327, 53237707, 537229], "yaxis": "y3"}, {"alignmentgroup": "True", "bingroup": "x", "histfunc": "sum", "hovertemplate": "version=Volume_2023_w14_27 (avg. week)<br>country=CZ<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>", "legendgroup": "Volume_2023_w14_27 (avg. week)", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "Volume_2023_w14_27 (avg. week)", "offsetgroup": "Volume_2023_w14_27 (avg. week)", "orientation": "v", "showlegend": true, "texttemplate": "%{value:.2s}", "type": "histogram", "x": ["GM", "Grocery", "Fresh", "Produce"], "xaxis": "x", "y": [6098798.285714285, 48833024, 7836522.857142857, 579110.1785714285], "yaxis": "y"}, {"alignmentgroup": "True", "bingroup": "x", "histfunc": "sum", "hovertemplate": "version=Volume_2023_w14_27 (avg. week)<br>country=HU<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>", "legendgroup": "Volume_2023_w14_27 (avg. week)", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "Volume_2023_w14_27 (avg. week)", "offsetgroup": "Volume_2023_w14_27 (avg. week)", "orientation": "v", "showlegend": false, "texttemplate": "%{value:.2s}", "type": "histogram", "x": ["GM", "Grocery", "Fresh", "Produce"], "xaxis": "x2", "y": [10223395.42857143, 48740790.85714286, 7212599.428571428, 528990.0357142857], "yaxis": "y2"}, {"alignmentgroup": "True", "bingroup": "x", "histfunc": "sum", "hovertemplate": "version=Volume_2023_w14_27 (avg. week)<br>country=SK<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>", "legendgroup": "Volume_2023_w14_27 (avg. week)", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "Volume_2023_w14_27 (avg. week)", "offsetgroup": "Volume_2023_w14_27 (avg. week)", "orientation": "v", "showlegend": false, "texttemplate": "%{value:.2s}", "type": "histogram", "x": ["GM", "Grocery", "Fresh", "Produce"], "xaxis": "x3", "y": [4943122.857142857, 40100612.571428575, 6269816, 416912.8214285714], "yaxis": "y3"}], "layout": {"annotations": [{"font": {}, "showarrow": false, "text": "country=CZ", "x": 0.15999999999999998, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {}, "showarrow": false, "text": "country=HU", "x": 0.49999999999999994, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}, {"font": {}, "showarrow": false, "text": "country=SK", "x": 0.8399999999999999, "xanchor": "center", "xref": "paper", "y": 1, "yanchor": "bottom", "yref": "paper"}], "autosize": true, "barmode": "group", "legend": {"title": {"text": "version"}, "tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "CE_shelfCapacity"}, "xaxis": {"anchor": "y", "autorange": true, "domain": [0, 0.31999999999999995], "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "xaxis2": {"anchor": "y2", "autorange": true, "domain": [0.33999999999999997, 0.6599999999999999], "matches": "x", "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "xaxis3": {"anchor": "y3", "autorange": true, "domain": [0.6799999999999999, 0.9999999999999999], "matches": "x", "range": [-0.5, 3.5], "title": {"text": "division"}, "type": "category"}, "yaxis": {"anchor": "x", "autorange": true, "domain": [0, 1], "range": [0, 70954585.26315789], "title": {"text": "sum of shelfCapacity"}, "type": "linear"}, "yaxis2": {"anchor": "x2", "autorange": true, "domain": [0, 1], "matches": "y", "range": [0, 70954585.26315789], "showticklabels": false, "type": "linear"}, "yaxis3": {"anchor": "x3", "autorange": true, "domain": [0, 1], "matches": "y", "range": [0, 70954585.26315789], "showticklabels": false, "type": "linear"}}}, "image/png": "iVBORw0KGgoAAAANSUhEUgAABrsAAAFoCAYAAADn+EAcAAAAAXNSR0IArs4c6QAAIABJREFUeF7s3XucFNWd///PXBwGAyJgAEcJChrReEFdFL8JLIZcVljWhHwlZEmiwRC+sIk/kS8+GLOuSy4MC4u47kaWJaJu4kPE75K4PiRXDavZlchDQ6JRViOiRgQiEkTDbS6/x6nZ01TXVHdXT9epU6fq1f8g3VXn8vzU2Ey/+5yq6+rq6hIeCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCDgoUEfY5WDVGDICCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggIAnQNjFhYAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIOCsAGGXs6Vj4AgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAoRdXAMIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAALOChB2OVs6Bo4AAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIEDYxTWAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCDgrABhl7OlY+AIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAKEXVwDCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACzgoQdjlbOgaOAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCBA2MU1gAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg4KwAYZezpWPgCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAChF1cAwgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAs4KEHY5WzoGjgACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgggQNjFNYAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIOCsAGGXs6Vj4AgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAoRdXAMIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAALOChB2OVs6Bo4AAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIEDYxTWAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCDgrABhl7OlY+AIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAKEXVwDCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACzgoQdjlbOgaOAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCBA2MU1gAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg4KwAYZezpWPgCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAChF1cAwgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAs4KEHY5WzoGjgACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgggQNjFNYAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIOCsAGGXs6Vj4AgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAoRdXAMIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAALOChB2OVs6Bo4AAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIEDYxTWAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCDgrABhl7OlY+AIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAAAKEXVwDCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACzgoQdjlbOgaOAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCBA2MU1gAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg4KwAYZezpWPgCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAAChF1cAwgggAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAs4KEHY5W7r4Br5v/wGZu2ilXDJmtNwwZ3osDb/0yk6Zc+MKmXf1lTJt8oRCm7qvZ57f7j339RtnFb0eS+c0ggACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAArkRIOxKqNQbNj4mNy9bW9TbyUMHy+plC2TUiBbv+S1bt8k11y8tOaIpk8bJ4oWzpG9zU6yjTirsqtTPwUNH5Jbla+XhRzYXzc/UvGNF9DV26+r1svHRXxRqWyr4M9U/7SKAAAIIIIAAAggggAACCCCAAAIIIIAAAgggkCcBwi7D1dYBz5tv7S8KtlS3OgDTq5t02HX3bYtk7JjRhkd2rPlKIVRvBhIW8Kj5tbat6eGg2tdzDwZbOgB7+tkXQ8/rzdhMn0PYZVqY9hFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQOCZA2GXwatBBzas798iqpfNl4ID+PXpTodBvX/6dfHziJYXAJ29hlw7GJn/40pLbKP5o05NyxumnFlbBGSxb7E2zsit2UhpEAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKAgQNhl8GLQq5Wi3pfK1MouHba8sXtvYbb+MflXdo2/9PyirRRLjT24LWNwRVYw4AnbxvG8s0d6IeBd635QtO1fpZJE2RJStaGOu+OeB+W2r31Z/nX9jwrbI+p+dfhY7faJwfuOqb7881cru57cus2b21t/OODdu8xvr46/9jOTRVuHhZumroVKtryOAAIIIIAAAggggAACCCCAAAIIIIAAAggggIBrAoRdBisW3M6uUlcmAo5S2wmqe4PpkMUf3qgQ5oY5072hho2n1Go1f8CjQqSo2xjqvt/XMiTy/chUiKUe0yZPKJDqYMt/DzQdigXvjRYcq5rT8lXrZOa0jxRWjul5qg7890krFWD+y3cfkknjL/bOj2Kh2i3Vh3pNtbFrz1uRTSpdW7yOAAIIIIAAAggggAACCCCAAAIIIIAAAggggEBWBQi7DFY2GHpU6koHKaWOC66eqtSeej0sBFLPqzDq4KHDcu5Zp0upe3bpMGbYkEFFAVjYfbeC4Va1YdclY0aX3MIwyjz1HKZPnVgIwcrNXa22mnf1lUWBWbCf4D3Got7bLGrYpfoLu48Z2x5GqTjHIIAAAggggAACCCCAAAIIIIAAAggggAACCCDQLUDYZfBK6G3YFec9u3SAFty6zz/tSmGXOlavbiq14igYjJkOu8K2ZlTj9G+7WCrsKjXfStsj6j7bWmfL2DGjS1451YRdYWNR41j/0KaS93kzeMnSNAIIIIAAAggggAACCCCAAAIIIIAAAggggAACzgkQdhksWRq2MVTTC1sx5t+uMGrYpdq6Zfnawr2vwuh0u9WGXdVsY6hc77xvY1GwVc3KrmAwp89986394t8GMbjqKuo2k9WEXcrQH26pv89dtFL8K9QMXqI0jQACCCCAAAIIIIAAAggggAACCCCAAAIIIICA8wKEXQZLWOr+TqW6jBqm1DrkYFgUNezq29wU+V5SUcMuNZdqQsFSW/xVE3YF51tqBVgw7DKxskvN3z/24S1DZMXq9azqqvUi53wEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACB3AgQdhkstV5B9OrOPSXDCxV0PPP8dpkw7oLCCqw4tzF8bPOvRG1hOHBA/8JMg2FPNWFXqWAoyFhN2KWPnfzhS0vet+vZ/35Z+jb3kbf2vS3XXL9UgkbVhF3B0KrUdpPV3LNLj2/UiBYvvHty67ZCzcPGFvRS57yw/Xfe0+8feWpN9y8zeEnTNAIIIIAAAggggAACCCCAAAIIIIAAAggggAACqRMg7DJcklJb5Klu9T2i9H2mTKzsCgungv1UE3bpAO/pZ18s2vJPz0f9OW3yBKkm7FLn6DFNmTSucH8w9Xywv0En9ve2+btkzOhCIKTHr0LDSvfs0sf6t00Mc9fPnTx0cI+tDVXY5u8nGIoFwy49BzUffe+z4GWnvdTz/q0UDV+eNI8AAggggAACCCCAAAIIIIAAAggggAACCCCAgPMChF0JlVAHW/7uSgUppYYUDIKiDl1vW6iPD/ZbTdil2wibj1pBtmrpfG8VWbVhl2pXh0IPP7K5aGrBefvDLXWg6ndJ62y5qW1N0b2uwsaojvffr0x3FLyvmTpm/KXnS2vbmh7hk57bG7v3FsbpX2kWtlIsOObgGIL3EYtaW45DAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCDvAoRdeb8CMjz/qFsupoHAxKq+NMyLMSCAAAIIIIAAAggggAACCCCAAAIIIIAAAgggYFqAsMu0MO1bE3Al7IqyzaE1RDpGAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCDlAoRdKS9QcHhhW+iVmkLYdn2OTbem4boSdrGqq6YyczICCCCAAAIIIIAAAggggAACCCCAAAIIIIBAzgUIu3J+ATB9BBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQMBlAcIul6vH2BFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBnAsQduX8AmD6CCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggIDLAoRdLlePsSOAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACORcg7Mr5BcD0EUAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAGXBQi7XK4eY0cAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEci5A2JXzC4DpI4AAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIuCxB2uVw9xo4AAggggAACCCCAAAIIIIAAAggggAACCCCAAAII5FyAsCvnFwDTRwABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQRcFiDscrl6jB0BBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQyLkAYVfOLwCmjwACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgi4LEDY5XL1GDsCCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAgggkHMBwq6cXwBMHwEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBwWYCwy+XqMXYEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAIOcChF05vwCYPgIIIIAAAggggAACCCCAAAIIIIAAAggggAACCCDgsgBhl8vVY+wIIIAAAggggAACCCCAAAIIIIAAAggggAACCCCAQM4FCLtyfgEwfQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEDAZQHCLperx9gRQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgZwLEHbl/AJg+ggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIICAywKEXS5Xj7EjgAACCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAjkXIOzK+QXA9BFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABlwUIu1yuHmNHAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBHIuQNiV8wuA6SOAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACLgsQdrlcPcaOAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACCORcgLAr5xcA00cAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEXBYg7HK5eowdAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEMi5AGFXzi8Apo8AAggggAACCCCAAAIIIIAAAggggAACCCCAAAIIuCxA2OVy9Rg7AggggAACCCCAAAIIIIAAAggggAACCCCAAAIIIJBzAcKunF8ATB8BBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQcFmAsMvl6jF2BBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQCDnAoRdOb8AmD4CCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggg4LIAYZfL1WPsCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggEDOBQi7cn4BMH0EEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAwGUBwi6Xq8fYEXBc4KVXdspX29bIN1tny6gRLYnM5tbV6+XO+zYW+poyaZwsXjhLdu5+U+bcuELe2L23xzjuvm2RjB0zOpHx0QkCCCCAQL4Fknxv3LJ1m6xYvV5WLZ0vAwf09+D37T8gcxetlAVzpnvvfRs2Piabn3rOe6/s29wUeky+K8bsEUAAAQSSFEjyfVLNS70P3rxsbdEU9e+HwfdMddDBQ0fkluVrZdiQQXLDnOlJ0tAXAggggAACuRcg7Mr9JQAAAvYEkvxFRf/SoWbr/8BO/fIyvGVIaJilPgR84KFNRcfb06JnBBBAAIE8CCT53kjYlYcrijkigAAC2RKw/T6p+n/k8afkS5+d2uMLIvp3znEXnyPTJk/IFjyzQQABBBBAwAEBwi4HisQQEUhKQP/j/OFHNntdXvuZyYVvo/lXRPmfD37jW/3jf/kd66Ttptnet8TVee/88ZC8884fRbV78tDBsnrZAmkZepL3jTfdl+pPfUPutZ17vG+Q9+t3vNz/4KPyl9MmyTPPv1z4hrk6rje/4IR9oFfOtTd9JFUn+kEAAQQQSE4gb++NrOxK7tqiJwQQQCALAll+n1S/y6pHqRVawfdMdfxpw4cRdGXhwmYOCCCAAAJOChB2OVk2Bo1A/AJh2y38aNOTMmHcGPnBo5sLWxipnv3bMkQJuzY++gsv4FJbFfqPV1sHBrcx1NtE+LcOVM/teG1XUfDm/6UjuDWh1tHBmuq30i8qftGw7SjiF6dFBBBAAIG0C2T9vZGVXWm/AhkfAgggkG6BrL9Phv1uWur3RvWlTf/vrOmuHKNDAAEEEEAgmwKEXdmsK7NCoGqBUiuZwrZi8H849rP//GXRvTzCVnb5gyn/1oClwq6we4O0LlkjC+fNkEEn9hf939Xc56uab9lVc2zV0JyAAAIIIOCMQNbfG9V78jXXLw2th/7SCffscuZyZaAIIIBA4gJZf59UoMF7dp139sjCvS71lyQvGTNa/F/wTLwQdIgAAggggAACngBhFxcCAgh4AsGQSrPosOuqqRML97XyH5tE2KXGogModX+t4H204lzZFfahHpcIAggggEA+BbL+3ljLyq7efPEkn1cRs0YAAQSyK5D198lg5YL3gT50+LDMXbRSVNilti9c/9CmQhCW3aozMwQQQAABBNIrQNiV3towMgQSFUjzt/J0GKe2PFSPBf/n04XgLSpSlHt2RTkman8chwACCCDgvkAe3xuDW/n6V2T3bW7yisp9Ld2/tpkBAgggEIdA1t8nw4z8vzOq11XYtWDOdO/3U/UlzF173pLFC2eJfs+Mw5k2EEAAAQQQQCCaAGFXNCeOQiDzAsH91tXf/+3h/5BPTflT755d+ltqzX36FN2zKxgQqX/gP7l1W+EbbcF7Zfk/NNPfhNO/HCjkUiur9Phe3bmnV9+WC34LT//yofpTq8XOOP2UXm2PmPkLgwkigAACORbI+ntjlJVdOvyaPnWiTJs8wbsa+DAvxz8UTB0BBBDwCWT9fVK9342/9PyiL1r6f78NfkEk7B5mXDAIIIAAAgggkJwAYVdy1vSEQOoF9D/Wn3l+uzfWaz8zWW6YM73wwdad923s8bz+0Eu/1vqVmfLzJ5+Rtptmy8AB/b0PxNRDtxP8hrh/D3R1fxB1Y9/gPbs0XBz30gpueThl0jjvm3cq0Lt52drQGun7lqS+gAwQAQQQQCB2gSy/N0YJuxSo+ub+nBtXyBu793q++r2Tb63HfrnRIAIIIOCcQNbfJ4P3tvT/jhwMu1Tx9HPvaxnCCi/nrmYGjAACCCDgugBhl+sVZPwI5ERA/dLQdvu90nrdTC9E44EAAggggEDeBXhvzPsVwPwRQAABBMoJ8D7J9YEAAggggEC+BAi78lVvZouAswJqBdiO13YVVog5OxEGjgACCCCAQEwCvDfGBEkzCCCAAAKZFOB9MpNlZVIIIIAAAgiUFCDs4uJAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBwVoCwy9nSMXAEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAHCLq4BBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABZwUIu2os3c69B2tsgdMRQAABBBBIRqCpsV5OGtAnkc7+eLhD/vDOkUT6ohMEEEAAAQRqFTixX5Mc36eh1mYinf/m/sNypL0z0rEchAACCCCAgG2BlsF9bQ+B/hFAAIFIAoRdkZhKH0TYVSMgpyOAAAIIJCZA2JUYNR0hgAACCDgmQNjlWMEYLgIIIIBAYgKEXYlR0xECCNQoQNhVIyBhV42AnI4AAgggkJgAYVdi1HSEAAIIIOCYAGGXYwVjuAgggAACiQkQdiVGTUcIIFCjAGFXjYCEXTUCcjoCCCCAQGIChF2JUdMRAggggIBjAoRdjhWM4SKAAAIIJCZA2JUYNR0hgECNAoRdNQISdtUIyOkIIIAAAokJEHYlRk1HCCCAAAKOCRB2OVYwhosAAgggkJgAYVdi1HSEAAI1ChB21QhI2FUjIKcjgAACCCQmQNiVGDUdIYAAAgg4JkDY5VjBGC4CCCCAQGIChF2JUdMRAgjUKEDYVSMgYVeNgJyOAAIIIJCYAGFXYtR0hAACCCDgmABhl2MFY7gIIIAAAokJEHYlRk1HCCBQowBhV42AhF01AnI6AggggEBiAoRdiVHTEQIIIICAYwKEXY4VjOEigAACCCQmQNiVGDUdIYBAjQKEXTUCEnbVCMjpCCCAAAKJCRB2JUZNRwgggAACjgkQdjlWMIaLAAIIIJCYQJ7Crn37D8jcRStl+tSJMm3yhMSM6QgBBOIRIOyq0ZGwq0ZATkcAAQQQSEyAsCsxajpCIBUCd97dIK+8WpeKsVQaxOxZHTL81K5Kh/E6AsYECLuM0dIwAggggIDjAnkKuxwvFcNHIPcChF01XgKEXTUCcjoCCCCAQGIChF2JUdMRAqkQIOxKRRkYhCMChF2OFIphIoAAAggkLmAj7Nqw8THZ/NRzsnjhLOnb3OTN+eChI3LL8rUy7uJzCquu1HE3L1tbMLn7tkUydsxo7++6jT//6GXeai31UK+fO3qk187Dj2wunPf1G2d5beqVXQvmTC+089IrO2XOjSvkjd17veOv/cxkuWHO9B5j2vHaLrnzvo3e81MmjSsae+JFo0MEcipA2FVj4Qm7agTkdAQQQACBxAQIuxKjpiMEUiFA2JWKMjAIRwQIuxwpFMNEAAEEEEhcwEbYpQKmr7atkW+2zpZRI1q8OQefCwZiOpRqa53tBVU6CPOHU6qdW1ev99rTgZUKuO5a9wOZe/Un5NDhw14wpsOuYJs6cBs2ZJB3vv67Cs500MZWiIlfonSIQEGAsKvGi4Gwq0ZATkcAAQQQSEyAsCsxajpCIBUChF2pKAODcESAsMuRQjFMBBBAAIHEBWyEXcFQSU1ahVdq9ZQKmVSg1LpkjSycN6MQhgWDrKirw/ygwZVdYW1s2bpNVqxeL6uWzpfmPn16rDYLC9QSLxodIpBTAcKuGgtP2FUjIKcjgAACCCQmQNiVGDUdIZAKAcKuVJSBQTgiQNjlSKEYJgIIIIBA4gI2wi41SX+opP4etuJKby3oR9ErucKCKnWcf+vD4HaDwbBLrQI7bfiwwraJ6nx/0NYy9CTCrsSvSDpEoLQAYVeNVwdhV42AnI4AAgggkJgAYVdi1HSEQCoECLtSUQYG4YgAYZcjhWKYCCCAAAKJC9gKu/zBk5q0Xk01cED/HlsahqGUCrv0sWGhV3AbQ8KuxC83OkSgJgHCrpr4RAi7agTkdAQQQACBxAQIuxKjpiMEUiFQTdh15eQGueIj9dLYINLeIfKDn3bKgxs7xP+8f1L79ovc+Z12mfKxBjn7/XXy+BOdcs+6jsIhX2ttlJZhPZ8vBTN7VocMP7UrFW4MIp8ChF35rDuzRgABBBCoLGAr7FIj0/fX0qP032fLv9IrbBaVwi59TrkVZGxjWPn64AgE0iRA2FVjNQi7agTkdAQQQACBxAQIuxKjpiMEUiEQNexSgdZHJ9bLTzZ1B1yVHirI2v+2yIpvtcuCv2qU00fUyYF3RO65r122vdjlBWSXf6hemptFnniyOAQr1TZhVyV1XjctQNhlWpj2EUAAAQRcFbAZdr30yk6Zc+MKj271sgU97s+18dFfFD2vgqvXdu7xth0MC6rUarG22++V1utmilohph7+44Iru3T/ba2zZeyY0RK8l5j++7iLzyna6lCHdDqcc7X2jBsB1wQIu2qsGGFXjYCcjgACCCCQmABhV2LUdIRAKgSihF2jz6yTaz/XKM8+Fy2U0kHW/d/rkCe2dHph14ATuqf70std3uouFYb9/k2Rs86sky1PR2uXsCsVl0yuB0HYlevyM3kEEEAAgTICNsMuHSap4S1eOEv6NjcVjdS/FaF64eShgwvhV6mVXSqIuvO+jYV2zjt7pKxaOt8Lv4L37PL+jfs/gZu+P5i+J5h6jbCLHx0E0iUQe9il/6cwferEokQ7XdOObzSEXfFZ0hICCCCAgFkBwi6zvrSOQNoEooRdl42tl5lXNUhzn2Oj11sUqlVawYd/VZd6TYddT/2qS8b9Sb08+liHjL+s3tvWUAVjhF1puyoYTykBwi6uDQQQQAABBMIFbIZd1AQBBBCoRiD2sEt1rpaMXnP90sI4/Il3NYNz4VjCLheqxBgRQAABBJQAYRfXAQL5EogSdgVXaumVXgcPdsnftLUXgQWP9Ydd6lgVhJ3Qv05++etOeeGlLi9EI+zK1zXn8mwJu1yuHmNHAAEEEDApQNhlUpe2EUAgTgEjYZd/gHql1zPPb/eezlrwRdgV5+VIWwjtRZbIAAAgAElEQVQggAACJgUIu0zq0jYC6RPoTdilZnH1jAYZe1G93PtA91aF6qFDsF27u7x7demHXtmlwi513rnn1Mud32mXgSfWEXal75JgRGUECLu4PBBAAAEEEAgXIOziykAAAVcEjIddwX1N/TBZCL4Iu1y51BknAggggABhF9cAAvkSiBJ2qW0MP/3JBvnZzzvlwY0dHpA/tNJbGYYFYOpYf9jl19XbI7KyK1/XnMuzJexyuXqMHQEEEEDApABhl0ld2kYAgTgFjIRdwZsDhoVaasVX2+33Sut1M70bALr6IOxytXKMGwEEEMifAGFX/mrOjPMtECXsUkJq+8G+feu8FVnqce3nGsW/gqvUqi7CrnxfX1mbPWFX1irKfBBAAAEE4hIg7IpLknYQQMC0QOxhl962cPrUiTJt8gTT46/Yvv/+YeedPVJWLZ1fCNf8odyUSeNk8cJZ0re5SQ4eOiK3LF8rr+7cU3S8npvqVLdD2FWxBByAAAIIIJASAcKulBSCYSCQkEDUsEuHWQMHdA9s567i+3Wp1VvDhnaHYXqll54CK7sSKibdGBcg7DJOTAcIIIAAAo4KEHY5WjiGjUAOBWIPu9JkqIKuFavXFwVWenzB125dvd576YY50wth1/4D78rHJ44thHYqHPvRpi2inifsSlOlGQsCCCCAQBQBwq4oShyDQHYEooZdaZjx7FkdMvzUrjQMhTHkVICwK6eFZ9oIIIAAAhUFCLsqEnEAAgikRCD2sEutfmpdskYWzpsho0a0FE1TBUwPPLSpsILKpEG5cah+Vbh12vBhhSDLH3419+njreyaNP4ieeTxp72tFtVDbbuonrvr/h8SdpksHm0jUIXA07+sk+8/1FDFGfYOHXtRp0z98057A6Dn3AsQduX+EgAgZwKEXTkrONOtSYCwqyY+TkYAAQQQyLAAYVeGi8vUEMiYQKJh10uv7JTld6yTtptmG79Pl+przo0r5I3dewsl0/cO09sUjrv4nELYpY7/atsa+WbrbGkZepIXdl01daK8tnOPd/7wliHef6s//avF2MYwYz8RTMc5AcIu50rGgC0KEHZZxKdrBCwIEHZZQKdLZwUIu5wtHQNHAAEEEDAskPaw68hRkZd3RN8h4LgmkZEj6gyr0TwCCNgQSDTsUtsAbn7quURWdgVXkfnvJXbFh8cVwqyxY0Z77qXCrjNOP8VbqXZCv+O9FV6/ffn1orDr0JEOG3WjTwQQ+B+BJ57sknsfiP6PGptwHxpXJzM+xT+obNYg7313dnXJ8X0aE2E4fLRTurrc+NlMBIROELAgsPKOLnnpZTd+Dhd8uU5O50MHC1cJXWqBuro66XNcfSIgBw+3i+qPBwIIIIAAAi4INDelezedPb8XuekbRyNTDjlJZMnNx0U+Pq4Dy91uJ64+aCf9Amq3uSe3bgu97VL6Rx99hP5bRgXPMmkQW9gVtpIqOJGThw6W1csW9NjeMDpT9CPDtkzUYduir3xWlv7jdyXKyi4Vhvm3PAz+j+mtA0eiD4ojEUAgdoEtT4k88D03PiwYN1Zk2pVufOgYe6FoMBUC9XV1cmK/ZP5Rf/BIhxw8zBdCUlF4BpFbgVVr6uTlV9yY/pfndMn7hrsxVkaZTYG+fRqkb0If5u1/56h08IWQbF5IzAoBBBDIoMCg/k2pnlVSYVfYTmEaRn3mvOO1XXLDnOklrdIadqnPve+8b2Nh3F+/cVZhJzT1pF5A8szz271j7r5tkejFI2pO11y/tHDulEnjiha5VGo7zgurUg10/VSfixfOkr7Nla/rcuNX/d28bG2PKQT9/AckeYunOG1701a5sEu1F7zFVG/6CDsntrBLN17pXllxDbxSO2FbJvov+ij37FLbGOofXt1f8H9MbGNYqRK8joBZAbYxNOtL69kSYBvDbNWT2SBQSYBtDCsJ8ToCxwTYxpCrAQEEEEAAgXCBtG9jmFTYpXTCdi3TIUrY58h+0TSGXWrsq+75vnxhxhXeLYf0Ypa21tneZ+LBgM+/M9qoES2eh7rlj//YYUMGeaFfpbbj+nnzB276FkbBtvU8Hn5kswQDuVLjqHb8lTKRqNdJXC6226kUdimvttvv9XbSU9deXI/Yw664BlZrO/oC0j9gOoVeMGe69wMY/B+MvwDlLj7Crlorw/kIxCtA2BWvJ61lW4CwK9v1ZXYIBAUIu7gmEIguQNgV3YojEUAAAQTyJUDYdazewbBHvRJccOFf8eMPVvyfKavz5i5aKfpzavV3/8IM3c85Z50u9z/4qDcAFeSMv/T8wkqqYLBTqt9qrtawcGv5Heuk7abZXiBRbnWb6qfcLYwqnesfZzA4Cq6IClsVVG5llz5e9dHbWyxVGn+llWVhC3N03fXKOv+ueGGW/vwiuOJOtVVuVVlvff1BYbCPcq8Fw66wrQvVc+qaDi42quaaDR6b2bBLTTRY9GDBS/1PgLCrlkuKcxFIVqDasOtrrY3SMqx728Odu7rkb9raiwa84K8aZcAJUvT8ZWPrZeZVDXLwkMid32mXbS92b0V45eQGueIj9XLgneLnSwmMvahTpv55Z7JA9IaAT4Cwi8sBgXwJEHblq97MtjYBwq7a/DgbAQQQQCC7AoRdx2obFnj4Q46wxRW79rzlbZv37LbtsmL1eu9eTepRKeyac+MKmXf1ld6WgnrF1eQPX+qtmgpbYbX+oU2F+0D1dou4SotF1Lgr3YtJHRO2nWOw7XI/McHP5lWfGx/9hXd7pJahJ8kty9dKcCVdqbDJP95yYVyln+By46+0qku1ra6Nx3/x6yIbdd7GRzbLzGkf9bpXY9XXy6HDh6V1yRpZOG+Gd0sofx/awL+KTpn4b9kUh++5o0d61v7FRHpMwTEEDYLuYdt8VgoIK9Uk7PXYwi5d8C98+s/krvt/KHofz2Cn5509MlM3YGMbw95cdpyDQHwC1YRdKuhSj2DApZ67ekaDjL+s+6bkwRBMhV2f/mSDNDaKbHm6U+5Z130fItXeCf3r5Gg7YVd8FaUlkwKEXSZ1aRuB9AkQdqWvJowovQKEXemtDSNDAAEEELArQNhV7O9fZaRe8QcvwSDIH0q9te/tqsKur7atkW+2zi4EHf5wzB+86EDCH3T09t5QwfGHtVMq7Kq0TWOlbe2CV7kOQtQWi2q7uzNOP0VOGjRALv/ghaHb34UFJ8Hnagm7yo0/SmijjlEPFV6WeoSFpacNH+ad46/Fzt1viv/6qLTqLKy/KL5v/eFAUT+qHR2kXvCBM0q+psarvdT4S62mCwsAa/2/XWxhV60DcfV8wi5XK8e4syIQNexSYda559QXrcwKMyi1skuFXS+93CUtJ9fJPfe1y1ln1su4P6mX/W93yeBBdRXbVX2xsisrV5278yDscrd2jByB3ggQdvVGjXPyKkDYldfKM28EEEAAgUoChF3FQv4VLOqVezf8VBbOnSF9m5uKtiJUr/mPNR12qftR+R9R702lz/GvKlJzUY+wACss9FHHtbat8VZeqVVIwUdY25WuOx3ufH76x+W/tjwrk8Zf7FlPmzxeNmx8vGCu2wkLnFS/eovAuG10e2FbW4bNrVTY5b/nmDrPv1DIH3Cpe6vp4Cu4JWJvwq4ovipUUysM39i9t2hKavc8FXaVek2HXdr+7tsWhW5VSNhV6afAwuuEXRbQ6RIBn0DUsEuFWGe/v3v7Qv14/Iljq7T0c+XCrp/9vFMuvqDOC71OGlwnb+7t/nPYUMIuLko3BAi73KgTo0QgLgHCrrgkaScPAoRdeagyc0QAAQQQ6I0AYVdPNf89oNSrerWOzZVdwW39qql1qTAqSqhiIuhSY1dBoV7RdeG5Z4pawbZ81TpvWueedVqPFVJRV1dVe8+uSkFd1BVrYcFO0C4YLuqwVN2f7YGHNknrdTO9e6cFA7behF1RfEvdZ0zVoNxr6nXtNv9LV8niW+8pbMfovy6dCbvCLoLeoFfzQ2nrWMIuW/L0i0C3QNSwS205uP9tkRXf6r5Hl1rpddkl9fKDn3bKgxu7tyVUj0phlzrm8g/Ve/fvUiu8pnysgbCLi9EZAcIuZ0rFQBGIRYCwKxZGGsmJAGFXTgrNNBFAAAEEqhYg7OpJpj6kV6tWTuh3vMz5/F8UVjMFwwv/Z+T+e3Y19+njbX+otx7U9+Ty36Mr6jaGY8eMFhX0+O/ZpT6H/7eH/0M+NeVPvRVn5R7lwprg5/nBkCXurQv949R9v7pzT+GWSGqeNy9bK2ErhUyEXZWCrKirutS8wsKhoF+wjuo89dyPNm2Rs0YNL9zvK6wu/nu86W0up0+dWHLbxCi++hh9zy49j9++/DuZMG5M0f28/K99fOIlRfd2K+WkfMdfen7oqq+q/0f1PyfEvo1h8AZy/oH1dr/Q3k4uifMIu5JQpg8ESgv0NuwafWadXPu5Rtm1u6sQgKleKoVd//1ip3fes891rwpTx7OyiyvUFQHCLlcqxTgRiEeAsCseR1rJhwBhVz7qzCwRQAABBKoXIOzqaabDhPe1DJHFC2cVBUo6kFFn+bcSDAYbOuBSW8Sp4/r1O76wYikYDvjv0aXCreDfdSiigiD9UFvNlbs/lDpOt/PM89uLJukfd/AYf9AUtk3gyUMHe9sZDjqxv6j7jJVru9LVGAywSgVG11y/tKipUtvmVXPPrko2qkN/YFlpLmGZiX5Ob0H5oUvOk/0H3i2Ee6pNfZ20tc4uCoX8148KjNRDrQDzXx/lwi59zex4bVchRAvzDY5R11dtV1nutbD7v/m3utQry/RqtUp+UV+PPezy70Ua3KOz0vK2qINO03GEXWmqBmPJo0DUsCsYYumwS4dW2q5S2OVfBabOIezK41Xn7pwJu9ytHSNHoDcChF29UeOcvAoQduW18swbAQQQQKCSAGFXJSFeRyCagKmFQOXymGgjS/4ovRVopUC22pHFHnaxsqvaEnA8AgjUIhA17LpycoNc8ZF6eeLJ7hVZahvDsRfVy70PdMgTWzoLQyDsqqUanJt2AcKutFeI8SEQrwBhV7yetJZtAcKubNeX2SGAAAII9F4g7WHXvv0i3/7X7ltWRHmcOKBOZn++Icqhzh/jX2UWnIx/hU7SE03DuMJWpWmH884eWbS6Kk4f1e+TW7fV3H7QsNRqtjjHHldbcRmEjSf2sEt1EnZjuuDeo3Hh2G6HlV22K0D/eReIGnYpJxVwjb+s3iNr75Ci+3X5X9Omjz/RHYxdNrZePv3JBvnZz4vv76WOY2VX3q9At+ZP2OVWvRgtArUKEHbVKsj5eRIg7MpTtZkrAggggEA1AmkPu6qZC8cigEC2BYyEXYrMv2+kJnQpYYxadsKuqFIch4AZgWrCLjMjiN7q2Is6ZeqfH1tFFv1MjkQgHgHCrngcaQUBVwQIu1ypFONMgwBhVxqqwBgQQAABBNIoQNiVxqowJgQQCBMwFnblhZuwKy+VZp5pFSDsSmtlGFcaBQi70lgVxoSAOQHCLnO2tJw9AcKu7NWUGSGAAAIIxCNA2BWPI60ggIB5AcKuGo0Ju2oE5HQEahQg7KoRkNNzJUDYlatyM1kEhLCLiwCB6AKEXdGtOBIBBBBAIF8ChF35qjezRcBlASNhV9gWhhrJ5A3ebBSCsMuGOn0icEyAsIurAYHoAoRd0a04EoEsCBB2ZaGKzCEpAcKupKTpBwEEEEDANQHCLtcqxngRyK9A7GHXwUNH5Jbla2XcxefIBR84Q+7d8FNZOHeG9G1ukltXr5fxl54vY8eMzow4YVdmSslEHBUg7HK0cAzbigBhlxV2OkXAmgBhlzV6OnZQgLDLwaIxZAQQQACBRAQIuxJhphMEEIhBIPawa9/+A9K6ZI0snDfDG97yO9ZJ202zZeCA/rJl6zZ54KFNsnjhLC/8ysKDsCsLVWQOLgsQdrlcPcaetABhV9Li9IeAXQHCLrv+9O6WAGGXW/VitAjUIrB3b538w7caamkisXPfe1KXfGVeR2L90RECYQKpD7uOHJb2F38TuXh1TX2k4cwPRD6eAxFAwB0Bo2HXoBP7S9vt90rrdTO9sEttb+gPv9xhKj1Swq4sVJE5uCxA2OVy9Rh70gKEXUmL0x8CdgUIu+z607tbAoRdbtWL0SJQiwBhVy16nJtHgbSHXZ27fidvX9e96CLKo37YKXLC7fdHOTTWY9QikBWr18uqpfO9z8l55FNA7Xz35NZtmb8O1DzV44Y503sU2qRB7GGXfxvDaZMneFsXnjZ8mKj/3rDxMdn81HOs7MrnzzKzRsCIAGGXEVYazagAYVdGC8u0ECghQNjFpYFAdAHCruhWHImA6wKEXa5XkPEnLUDY1S0e/MzbXwf1mfeO13aFfrCvj0tr2KU+u7/zvo2F6Xz9xlne5/j6oXZxm7topTzz/HbvqbtvW1S4RZGa0zXXLy0cO2XSuKLP/ZXLzcvWlmw7zmu5Ug10/VSfUXedK2cTnJueS9DPP8cs7npXqoblwi51jj8zivM6iD3sCg7O/wNx8tDBsnrZAhk1oiXOOVhti5VdVvnpHAEh7OIiQCC6AGFXdCuORCALAoRdWagic0hKgLArKWn6QcC+AGGX/RowArcECLuO1StsIYcOUa6aOrEQAoVVOI1hlxr7qnu+L1+YcUVhV7Y5N66QttbZ3lyCAZ/ate2rbWvkm62zvc/3lcfwliFFxw4bMsgL/YJt64xgwZzpZZ2q/enwB27XfmZyaOCo5/HwI5slGMiV6q+STVgGom/tFJZ9RL1Oqp1/Wo+vFHap68G/I2Bc8zAedsU10LS2Q9iV1sowrrwIEHblpdLMMw4Bwq44FGkDAXcECLvcqRUjtS9A2GW/BowAgaQECLuSkqafrAgQdh2rZDDsUa8Eb9vjX/HjD1b8YZc6T62W8gc//pUuup9zzjpd7n/wUW8AKsgZf+n5hZVUwWCnVL/VXIdh4Zb/lkTlVrepfsrt6lbpXP84VRDiD46CK6LCVgWVW9mlj1d99HbXuUrjr7SyrNTtnfyrx/wLhcIs/QFScMWdmlu5VWW99fUHhcE+yr0WDLvCti5Uz6lrWgWrcT0Iu2qUJOyqEZDTEahRgLCrRkBOz5UAYVeuys1kERDCLi4CBKILEHZFt+JIBFwXIOxyvYKMP2kBwq5j4mGBhz/kCK7eUh/m79rzlrdt3rPbthfu2aVarBR2qRVW866+0ttSUAUl6u+TP3ypt3IpbIXV+oc2Fe4D1dst4oKrr8JWo1W6F5OaW9h9mvQc9KqxctdxcBWU6nPjo7/wdoxrGXqS3LJ8rQRX0pUKm/zjreUWS+VWpgXDubC5KcvHf/HrIht13sZHNsvMaR/1TvFfL4cOHy4K/Px9aAP/KjplMu7ic4q2oCxlHNX33NEjPWvdT7kxBA2C7mHbfFYKCHvz/zpjYVdw38osbmGowAm7enPZcQ4C8QkQdsVnSUvZFyDsyn6NmSECfgHCLq4HBKILEHZFt+JIBFwXqCbs+lpro7QMqyua8vMvdMmKb7XLlZMb5IqP1EtjQ/fL+/aL3Pmddtn2YpeMPrNOrv1co/RtFrn3gQ55Ykund8xlY+tl5lXdJ/ifL2X63pO65CvzOlwnZ/yOCxB2FRfQv8pIveIPXoJBkD+Uemvf21WFXf7tAoNBi//vOpDwBx29vTdUcPxh7ZQKu0pt0+hfgRR15ZFy1UGI2mJRbXd3xumnyEmDBsjlH7wwdPu7sOAk+FwtYVe5kC9KaKOOUQ///dCC/2sIC0tPGz7MO8dfi5273yzaTrLSqrOw/wVF8X3rDweK+lHt6CD1gg+cUfI1NV7tpcZfajVdWABY6/8ujYRdCsufJqtBVpPe1jqpJM8n7EpSm74Q6ClA2MVVgUB0AcKu6FYciUAWBAi7slBF5pCUAGFXUtL0g4B9gWrDLjXiv2lr7zHwBX/VKG/u7ZJ71nWHUSoY08fqsOu4RpHXXu8Ox9RDnTP8lDppbCTssn8lMIKoAoRdxVL+FSzqlXs3/FQWzp0hfZubCkGADjT8x5oOu9T9qPyPqPem0uf4VxWpuahH1JVd6rjWtjXeyquw+1WptqoNZHS48/npH5f/2vKsTBp/sWc9bfJ42bDx8YK5Hn9Y4OTfIjBuG91e2NaWYT9bpcIu/z3H1HnnnT2ysELPH3Cpe6vp4Cu4JWK1trq2Dzy0Scr5qlBNrSh8Y/feoimp0FKFXaVe02HXnfdt9M67+7ZFoVsVOhF2lVvS19tUOer/fG0cR9hlQ50+ETgmQNjF1YBAdAHCruhWHIlAFgQIu7JQReaQlABhV1LS9IOAfYG4wq7gTFSQNWxonbe6Sz3Uyq5XX+uS9w2vkw0PdQdi06Y2eM+ddWYdK7vsXwqMIKIAYVdPKP89oLyf7ckTvINsruwKbusXsbyFcevtFnXQpV6IEqpECbr0WKKsgNLHqoxBr+i68NwzRa1gW75qnffyuWed1mOFVJS2e7OyKywE9NuWW/HlPy4s2AnaBcNFHZaq+7OpYKr1upkycED/HttY9ibsiuJb6j5jYddG8HrTbvO/dJUsvvUeWThvRo8g1Jmwy38DOf9EywFV8wOYpmMJu9JUDcaSRwHCrjxWnTn3VoCwq7dynIeAmwKEXW7WjVHbESDssuNOrwjYEKg27PJvY6i3MAyOW6/kOniwy1sFpv/+7HOdctLg7m0Q1Sow9d/qz7EX1RN22Sg+ffZKgLCrJ5v6kF6tWjmh3/Ey5/N/UfgQPxhelLpnV3OfPt72h3rrQb0jmv8eXVG3MRw7ZrS35Z9/lzUVfvzbw/8hn5ryp96Ks3KPcmFNMEQJrmIqtXWh6k+FKXet+4HMvfoT3hj0ApnpUydWdV+pV3fuKax00rdNClspZCLsqhRkRV3VVSocCvqF7ZannvvRpi1y1qjhhft9hdXFf4+3KNa6jXK++hh9zy49j9++/DuZMG5M0f28/K99fOIlRcFvKSflO/7S80NXffXqf1YiEvs2hsEbnPkHRtjV2zJxHgIIlBIg7OLaQCC6AGFXdCuORCALAoRdWagic0hKgLArKWn6QcC+QDVhl3+0+h5dTzzZWdi6UL2u7+sVds8uFXa98FKXfPqTDXK0XbwVXu8fVUfYZf8yYARVCBB29cTSYcL7WobI4oWzigIlHcios/xbCQaDDR1wqS3i1HH9+h1fWLEUDAfK3bNLhV3q4e9X/T3K/bH899Pyz9I/7uAx/qApbJvAk4cOLmxnGHw9ypj84wgGWGHZQnAbQHV+qW3zqlnZVclG9eMPLCv9SIVlJvo5vQXlhy45T/YfeLcQ7qk2S90ayn/9qMBIPdQKMHU9RAm79DWz47VdhRAtzDc4Rn99y70Wdv83/1aXemWZXq1WyS/q67GHXarjUtsVRklYow48LcexsistlWAceRVwKeyaMfhB+ZNnb3OiVO0Tr5Qjn/6yE2NlkNEFCLuiW3EkAlkQcCns+uuTb5X+B151gv3ozP9POocOd2KsDDK6AGFXdCuORMB1gd6GXWreKtja/7YU7sHltwjbxlCFXeqeXv7zrp7RQNjl+kWUs/ETduWs4EzXmICpWzz57w1X6p5pxibVy4b1VqB6C9BeNtPjtNjDrlKpZ9iA/Tdci2tCSbdD2JW0OP0hUCxA2GXmiiDsMuNqu1XCLtsVoH8EkhVwKez6ev086fv688kC9bK3Q4v+STpHnNXLszktrQKEXWmtDONCIH4BU2GXWvl1+Yfq5f7vdci+P3R59+zSYZd/FoRd8deUFs0KpD3s6nrr9/Lu7V+LjFA/6L1y/HV/E/l4lw8Mrvbyz8W/QifpOaZhXGGr0rSDycxC9fvk1m1Fq7d64x80LLWarTdtmz4nLoOwccYedpnGSFv7hF1pqwjjyZsAYZeZihN2mXG13Sphl+0K0D8CyQoQdpnxJuwy42q7VcIu2xWgfwSSE4gadl02tl7+9H/Vy9J/aPcGp8Ksj06sl59s6pQHN3bIzf+3UX79XJf33+qhVm/17Vsnd36n+3jCruRqSk9mBdIedpmdPa0jgIBLAoRdNVaLsKtGQE5HoEYBwq4aAUucTthlxtV2q4RdtitA/wgkK0DYZcabsMuMq+1WCbtsV4D+EUhOIGrYNfrMOi+wGjjg2Ngef+LY/brUCq3xl9UXXix1zy61jaH/wcqu5GpNT/EIEHbF40grCCBgXsBI2OW/QVpwCiaXAZrn6tkDYZcNdfpE4JgAYZeZq4Gwy4yr7VYJu2xXgP4RSFaAsMuMN2GXGVfbrRJ22a4A/SOQnEDUsCu5EZXu6b0ndclX5hWHZWkYF2PIlwBhV77qzWwRcFkg9rDr4KEjcsvytTLu4nPkgg+cIfdu+KksnDtD+jY3idqPcfyl58vYMaNdNisaO2FXZkrJRBwVIOwyUzjCLjOutlsl7LJdAfpHIFkBwi4z3oRdZlxtt0rYZbsC9I9AcgKEXclZ01M2BAi7slFHZoFAHgRiD7v27T8grUvWyMJ5Mzy/5Xesk7abZsvAAf1ly9Zt8sBDm2Txwlle+JWFB2FXFqrIHFwWyHrYtaO9Tr52oEn+d992mdzc/Y2+P3SK/O2BPvJ6Z/eWGV/oe6THa0Pru2R+vyPSXNddXd3OmY3Fz5eqPWGXyz8VpcdO2JXNujIrBEoJZD3sWvPucfKb9nr52/6H5cT/2UVKv98dkO4nvtrvsIw5rtP7742HGuSug01Fz6nnVTs/PtLY4/lSroRd2fyZI+zKZl2ZFQJhAoRdXBcIVCdA2FWdF0cjgIA9AaNh16AT+0vb7fdK63UzvbBLbW/oD7/sTTu+ngm74rOkJQR6I5DlsMv/gZ0OtA51iax8p0kuOK7DC7j0Mdf1O+p9mKeCsL9/p0kOdNXJF47vfk5/kLejo1761RF29eY6y8o5hF1ZqSTzQCCaQJbDLh1QnVLfWQi79JdBrvmf97+tR+vl9neOk7/pf0ROa+zywq4fHW6UkfaAppgAACAASURBVA2dMuc9R70vhKj30TV/bJI3OkT0e2klXcKuSkJuvk7Y5WbdGDUCvREg7OqNGufkWYCwK8/VZ+4IuCUQe9jl38Zw2uQJ3taFpw0fJuq/N2x8TDY/9Rwru9y6RhgtAqkWyGrYpUOrz/Y9Kt87dFwh3FIf3N39x+OKvsWuPvBTj9nvOVoIuyb1aZdfH23wPszb1VEnDx5qlFMbOuWF9oaiFV+lisvKrlRf9r0eHGFXr+k4EQEnBbIadqnQ6vWOehnb1FH0nqie/9XRY+9zwS+I6PNUMdW56gsh6j30jMYOefDQcaJDskrFJuyqJOTm64RdbtaNUSPQGwHCrt6ocU6eBQi78lx95o6AWwKxh13B6attDecuWinPPL9dTh46WFYvWyCjRrS4pVRmtKzsykwpmYijAlkMu/zfTB/d2Fm0kiss7PJ/uKc+2FMru754/FH5yeFG78O8nR110tLQ5f3p/xCwXMkJuxz9gagwbMKubNaVWSFQSiCLYZf/PW9be/EXQIJhl3LxfyFEh10f7dPufQlkRt92+fdDjfIXze3S9k4TYVfOf5QIu3J+ATD9XAkQduWq3Ew2BgHCrhgQaQIBBBIRMB52JTILi50QdlnEp2sERCRrYZf+FvoVze3eN86D30oPbtGkLoJSYZf3Id8fm2RIffd2TY8eLv7GO2FX/n6ECLvyV3NmnG+BrIVd6gsfPzjUWFihHPwCiNqScOW7TTL/Pd3bFpYKu9RKaBWCqe19r+p7VE5r6PTuhcnKrnz/vBB25bv+zD5fAoRd+ao3s61dgLCrdkNaQACBZAQIu2p0JuyqEZDTEahRIGthlw6zXu+s7yGj79ulPtz75jt9il7/WFN70TaGamXXsIYub1WYDs7CvvFeip+VXTVemCk9nbArpYVhWAgYEsha2KXex+462NRDy3/frrBj9PunXtmlwi5/cKa+WELYZegidKhZwi6HisVQEahRgLCrRkBOz50AYVfuSs6EEXBWINawa8vWbXLN9Uvl7tsWydgxo4tQyr3mrJ6IEHa5XD3GngWBrIVdwZoEV3aF1Ux9O13fe0Tf60uFXfpb7focwq4sXPG1zYGwqzY/zkbANYGshV1B/7Ctff3HqPfQ1e8eJ1c2t3vvif6wy39c2KrpcrXmnl2u/SREGy9hVzQnjkIgCwKEXVmoInNIUoCwK0lt+kIAgVoEYg27bl293hvLDXOmh46p0uu1TMTWuYRdtuTpF4FugbyHXcEAi7CLn4xyAoRdXB8I5Esg72GX/35dqvKEXfm6/qudLWFXtWIcj4C7AoRd7taOkdsRIOyy406vCCBQvUBsYde+/Qdk7qKVsmDO9B6ruvSw1OquFavXy6ql82XggP7VjzaFZxB2pbAoDClXAnkMu/xbNOntC3XRCbtydflXPVnCrqrJOAEBpwXyGHapgOvHRxq9uuntC3URCbucvpyND56wyzgxHSCQGgHCrtSUgoE4IkDY5UihGCYCCEisYVfrkjWycN4MGTWiJZT2pVd2yvI71knbTbMJu7j4EEAgFoGsh12xIPWiEe7Z1Qs0B04h7HKgSAwRgRgFsh52xUhVVVNsY1gVlzMHE3Y5UyoGikDNAoRdNRPSQM4ECLtyVnCmi4DDArGFXQcPHZFblq+Vq6ZOLLuy64GHNsnihbOkb3PPm0u76MjKLherxpizJEDYZaaahF1mXG23SthluwL0j0CyAoRdZrwJu8y42m6VsMt2BegfgeQECLuSs6anbAgQdmWjjswCgTwIxBZ2KawNGx+THa/tKnvPrtOGD5NpkydkxpawKzOlZCKOChB2mSkcYZcZV9utEnbZrgD9I5CsAGGXGW/CLjOutlsl7LJdAfpHIDkBwq7krOkpGwKEXdmoI7NAIA8CsYZdenWXgvOv3tLPv7pzj5X7dZUalwrnbl621qvzlEnjCmMuNV59XzJ1vL7vGGFXHn5MmGOaBQi7zFSHsMuMq+1WCbtsV4D+EUhWgLDLjDdhlxlX260SdtmuAP0jkJwAYVdy1vSUDQHCrmzUkVkgkAeBWMMuDeYPkfRzX79xlpUVXTq4eviRzUWB1pat22TF6vWF0OrW1eu9od4wZ7roc/YfeFc+PnFsYdxqXj/atEXU84RdefjxYI4uCBB2makSYZcZV9utEnbZrgD9I5CsAGGXGW/CLjOutlsl7LJdAfpHIDkBwq7krOkpGwKEXdmoI7NAIA8CsYRdesXTgjnT5YzTT5HWJWtk4bwZMmpEi3VDFWKprRPVY/NTzxVWb+nn9ZaK/vCruU8f7/5jk8ZfJI88/rS0XjfTO7/t9nu95+66/4eEXdYrywAQ6BYg7DJzJRB2mXG13Sphl+0K0D8CyQoQdpnxJuwy42q7VcIu2xWgfwSSEyDsSs6anrIhQNiVjToyCwTyIBBb2KUDrkEn9k9N2OVfraVWZemwSxVWhVnjLj6nsGrrpVd2ylfb1sg3W2dLy9CTvNevmjpRXtu5x7sOhrcM8f5b/elfEcY2hnn4MWGOaRYg7DJTHcIuM662WyXssl0B+kcgWQHCLjPehF1mXG23SthluwL0j0ByAoRdyVnTUzYECLuyUUdmgUAeBGIJu/S2fyo8uvyDF6Yi7FLh1o7XdnnbEqpHWNilwqyxY0Z7r5cKu/RKtRP6He+t8Prty68XhV15uEiYIwJpFvj55k65+76ONA+xMLbPD3tIzv/lrU6Mtc+ffUr6zprvxFgZZHSBo+2dclxjffQTajjySHuXNDXW1dACpyKAQK0Cf/cP7fLi9q5am0nk/KXHfVkaX/1NIn3V2kn/pd+WhpHdv0PwyI7AkfZOUV8KSeKR5PtxEvOhDwRcE9jze5GbvnHUiWGfNXC3zJW/c2Ks9cNOkeP/zyInxsogEUAAAQSyKRBL2KVo9FaGzzy/vazUeWePLGwBaJJUreq6876NPbqYMmmcLPrKZ2XpP3430souFYb5tzwM3uuLlV0mq0jbCFQWYGVXZaPeHMHKrt6opf8cVnalv0aMEIE4BVjZFafmsbZY2WXG1XarrOyyXQH6RyA5AZdWdo3ut0O++OIXksOpoafOU0fJoa/+cw0tcGpaBVjZldbKMC4EEAgKxBZ26YZV6JWme3bpcflXdvVtbioKsNQxYffs8q/80u0QdvFDhEC6BAi7zNSDsMuMq+1WCbtsV4D+EUhWgLDLjDdhlxlX260SdtmuAP0jkJwAYZcZa8IuM65paJWwKw1VYAwIIBBFIPawK0qnNo4Jhl3B0Mp/fy+9LSNhl41K0ScC1QkQdlXnFfVowq6oUm4dR9jlVr0YLQK1ChB21SoYfj5hlxlX260SdtmuAP0jkJwAYZcZa8IuM65paJWwKw1VYAwIIBBFILdhl8JRAdjNy9Z6Tmp7w8ULZ4la9UXYFeXS4RgE0iFA2GWmDoRdZlxtt0rYZbsC9I9AsgKEXWa8CbvMuNpulbDLdgXoH4HkBAi7zFgTdplxTUOrhF1pqAJjQACBKAK5CbuiYPTmGO7Z1Rs1zkEgPgHCrvgs/S0Rdplxtd0qYZftCtA/AskKEHaZ8SbsMuNqu1XCLtsVoH8EkhMg7DJjTdhlxjUNrRJ2paEKjAEBBKIIxBJ2qft0zV20Up55fnvFPs87e6SsWjpfBg7oX/FYFw4g7HKhSowxywKEXWaqS9hlxtV2q4RdtitA/wgkK0DYZcabsMuMq+1WCbtsV4D+EUhOIOth16EukZXvNHmg8/sdkea6btuNhxrkroPdz1/U2FF4TR+/u7NO/rb/YTmxvvv4P3SK/O2BPt5/+58vVSnCruSu4aR7IuxKWpz+EECgtwKxhF297TwL5xF2ZaGKzMFlAcIuM9Uj7DLjartVwi7bFaB/BJIVIOwy403YZcbVdquEXbYrQP8IJCeQ5bBLB1dPtzcUBVpbj9bL3X88rhBarXn3OA989nuOij7nna46+WBTu0xu7vBeU+HYfx5plHe7CLuSuzrT2RNhVzrrwqgQQKCnAGFXjVcFYVeNgJyOQI0ChF01ApY4nbDLjKvtVgm7bFeA/hFIVoCwy4w3YZcZV9utEnbZrgD9I5CcQJbDLhVindLQ6WH+6mhDYfWWfl4HWf7wS638UivBxjW1y+YjjTL3PUe881e92/3cg4eOhWTlqsTKruSu4aR7IuxKWpz+EECgtwJGwq6Dh47ILcvXysOPbJaThw6W1csWSMvQk7znxl18jkybPKG3403deYRdqSsJA8qZAGGXmYITdplxtd0qYZftCtA/AskKEHaZ8SbsMuNqu1XCLtsVoH8EkhPIatjlX62lVmXpsEvJqjDrguM6Cqu2drTXycp3m2T+e47IsIYu7/UrmttlZ0f3noctDV3ef6s//SvCCLuSu07T1BNhV5qqwVgQQKCcgJGw69bV6+W04cPkig+Pk+Wr1snMaR+RUSNaZMvWbfLAQ5tk8cJZ0re5e59g1x+EXa5XkPG7LkDYZaaChF1mXG23SthluwL0j0CyAoRdZrwJu8y42m6VsMt2BegfgeQEshh2qXDr9Y56b1tC9QgLu1SYNea47lVfpcKu0xo65e/faZL31Im3wmtHR/H2h+WqxMqu5K7hpHsi7EpanP4QQKC3ArGHXfv2H5DWJWtk4bwZ3mouf9j10is7Zfkd66TtptkycED/3o45VecRdqWqHAwmhwKEXWaKTthlxtV2q4RdtitA/wgkK0DYZcabsMuMq+1WCbtsV4D+EUhOIIthl1rV9eMjjT0QL2rskHnvOSJ3vBttZZcKw/xbHgbv9UXYldx1mqaeCLvSVA3GggAC5QQSDbtY2cXFiAACcQsQdsUt2t0eYZcZV9utEnbZrgD9I5CsAGGXGW/CLjOutlsl7LJdAfpHIDmBLIZdQT3/yi51T64o9+zyr/zS7RF2JXddprknwq40V4exIYCAXyD2sEs1vmHjY7L5qeek9bqZ8o9rv+dtYzjoxP4yd9FKmT51Ivfs4hpEAIHYBAi7YqMsaoiwy4yr7VYJu2xXgP4RSFaAsMuMN2GXGVfbrRJ22a4A/SOQnEAew65gaOW/v9ehru57ehF2JXcNutYTYZdrFWO8CORXwEjYpTjVKq5rrl9aJHv3bYtk7JjRmdJmG8NMlZPJOChA2GWmaIRdZlxtt0rYZbsC9I9AsgKEXWa8CbvMuNpulbDLdgXoH4HkBPIYdildtdrrroNNHrTa3nB+vyOiVn0RdiV37bnaE2GXq5Vj3AjkT8BY2JUXSsKuvFSaeaZVgLDLTGUIu8y42m6VsMt2BegfgWQFCLvMeBN2mXG13Sphl+0K0D8CyQnkIexKTvNYT52njpJDX/1nG13Tp2EBwi7DwDSPAAKxCRB21UhJ2FUjIKcjUKMAYVeNgCVOJ+wy42q7VcIu2xWgfwSSFSDsMuNN2GXG1XarhF22K0D/CCQnQNhlxpqwy4xrGlol7EpDFRgDAghEETASdu3bf8C7P9czz2/vMYbzzh4pq5bOl4ED+kcZX+qPIexKfYkYYMYFCLvMFJiwy4yr7VYJu2xXgP4RSFaAsMuMN2GXGVfbrRJ22a4A/SOQnABhlxlrwi4zrmlolbArDVVgDAggEEXASNh16+r1Xt83zJkeZQxOH0PY5XT5GHwGBAi7zBSRsMuMq+1WCbtsV4D+EUhWgLDLjDdhlxlX260SdtmuAP0jkJwAYZcZa8IuM65paJWwKw1VYAwIIBBFIPawS63qal2yRhbOmyGjRrREGYPTxxB2OV0+Bp8BAcIuM0Uk7DLjartVwi7bFaB/BJIVIOwy403YZcbVdquEXbYrQP8IJCdA2GXGmrDLjGsaWiXsSkMVGAMCCEQRIOyKolTmGMKuGgE5HYEaBQi7agQscTphlxlX260SdtmuAP0jkKwAYZcZb8IuM662WyXssl0B+kcgOQHCLjPWhF1mXNPQKmFXGqrAGBBAIIpA7GGX6lRtY3ja8GEybfKEKGNw+hjCLqfLx+AzIEDYZaaIhF1mXG23SthluwL0j0CyAoRdZrwJu8y42m6VsMt2BegfgeQECLvMWBN2mXFNQ6uEXWmoAmNAAIEoAkbCrpde2Sn3bvipLJw7Q/o2N0UZh7PHEHY5WzoGnhEBwi4zhSTsMuNqu1XCLtsVoH8EkhUg7DLjTdhlxtV2q4RdtitA/wgkJ0DYZcaasMuMaxpaJexKQxUYAwIIRBGIJexS9+mau2ilPPP89op9nnf2SFm1dL4MHNC/4rEuHEDY5UKVGGOWBQi7zFSXsMuMq+1WCbtsV4D+EUhWgLDLjDdhlxlX260SdtmuAP0jkJwAYZcZa8IuM65paJWwKw1VYAwIIBBFIJawK0pHWT2GsCurlWVerggQdpmpFGGXGVfbrRJ22a4A/SOQrABhlxlvwi4zrrZbJeyyXQH6RyA5AcIuM9aEXWZc09AqYVcaqsAYEEAgigBhVxSlMscQdtUIyOkI1ChA2FUjYInTCbvMuNpulbDLdgXoH4FkBQi7zHgTdplxtd0qYZftCtA/AskJEHaZsSbsMuOahlYJu9JQBcaAAAJRBIyEXbeuXi+79rwlixfO8sZwy/K18vAjm+XkoYNl9bIFMmpES5SxOXEMYZcTZWKQGRYg7DJTXMIuM662WyXssl0B+kcgWQHCLjPehF1mXG23SthluwL0j0ByAoRdZqwJu8y4pqFVwq40VIExIIBAFIHYwy59/64Fc6bL2DGjZcvWbfLAQ5u84OvZbdsL/923uSnK+FJ/DGFX6kvEADMuQNhlpsCEXWZcbbdK2GW7AvSPQLIChF1mvAm7zLjabpWwy3YF6B+B5AQIu8xYE3aZcU1Dq4RdaagCY0AAgSgCRsKu1iVrZOG8Gd4KLrXKSz1umDNdXnplpyy/Y5203TRbBg7oH2V8qT+GsCv1JWKAGRcg7DJTYMIuM662WyXssl0B+kcgWQHCLjPehF1mXG23SthluwL0j0ByAoRdZqwJu8y4pqFVwq40VIExIIBAFIHYw66Dh4542xZeNXWinHH6KTJ30Urxr/JasXq9rFo6n7ArSnU4BgEEKgoQdlUk6tUBhF29Ykv9SYRdqS8RA0QgVgHCrlg5C40Rdplxtd0qYZftCtA/AskJEHaZsSbsMuOahlYJu9JQBcaAAAJRBGIPu1SnagXXnBtXyBu798q1n5nsrerS2xteMma09/esPFjZlZVKMg9XBQi7zFSOsMuMq+1WCbtsV4D+EUhWgLDLjDdhlxlX260SdtmuAP0jkJwAYZcZa8IuM65paJWwKw1VYAwIIBBFwEjYFaXjrBxD2JWVSjIPVwUIu8xUjrDLjKvtVgm7bFeA/hFIVoCwy4w3YZcZV9utEnbZrgD9I5CcAGGXGWvCLjOuaWiVsCsNVWAMCCAQRYCwK4pSmWMIu2oE5HQEahQg7KoRsMTphF1mXG23SthluwL0j0CyAoRdZrwJu8y42m6VsMt2BegfgeQECLvMWBN2mXFNQ6uEXWmoAmNAAIEoAoRdUZQIu2pU4nQEzAkQdpmxJewy42q7VcIu2xWgfwSSFSDsMuNN2GXG1XarhF22K0D/CCQnQNhlxpqwy4xrGlol7EpDFRgDAghEESDsiqJE2FWjEqcjYE6AsMuMLWGXGVfbrRJ22a4A/SOQrABhlxlvwi4zrrZbJeyyXQH6RyA5AcIuM9aEXWZc09AqYVcaqsAYEEAgigBhVxQlwq4alTgdAXMChF1mbAm7zLjabpWwy3YF6B+BZAUIu8x4E3aZcbXdKmGX7QrQPwLJCRB2mbEm7DLjmoZWCbvSUAXGgAACUQRiCbv27T8gcxetlAVzpssZp58irUvWyMJ5M2TUiJYoY3D6GO7Z5XT5GHwGBAi7zBSRsMuMq+1WCbtsV4D+EUhWgLDLjDdhlxlX260SdtmuAP0jkJwAYZcZa8IuM65paJWwKw1VYAwIIBBFILawSwdcg07sT9gVRZ5jEEAgFgHCrlgYezRC2GXG1XarhF22K0D/CCQrQNhlxpuwy4yr7VYJu2xXgP4RSE6AsMuMNWGXGdc0tErYlYYqMAYEEIgiEEvYdfDQEbll+VoZd/E5cvkHLyTsiiLPMQggEIsAYVcsjIRdZhhT1yphV+pKwoAQMCpA2GWGl7DLjKvtVgm7bFeA/hFIToCwy4w1YZcZ1zS0StiVhiowBgQQiCIQS9ilOtJbGT7z/Pay/Z539khZtXS+DBzQP8r4Un8M2ximvkQMMOMChF1mCszKLjOutlsl7LJdAfpHIFkBwi4z3oRdZlxtt0rYZbsC9I9AcgKEXWasCbvMuKahVcKuNFSBMSCAQBSB2MIu3ZkKvbhnVxR6jkEAgTgECLviUOzZBmGXGVfbrRJ22a4A/SOQrABhlxlvwi4zrrZbJeyyXQH6RyA5AcIuM9aEXWZc09AqYVcaqsAYEEAgikDsYVeUTrN0DCu7slRN5uKiAGGXmaoRdplxtd0qYZftCtA/AskKEHaZ8SbsMuNqu1XCLtsVoH8EkhMg7DJjTdhlxjUNrRJ2paEKjAEBBKIIEHZFUSpzDGFXjYCcjkCNAoRdNQKWOJ2wy4yr7VYJu2xXgP4RSFaAsMuMN2GXGVfbrRJ22a4A/SOQnABhlxlrwi4zrmlolbArDVVgDAggEEXAWNi1Zes2ueb6pUVjuPu2RTJ2zOgo43LmGMIuZ0rFQDMqQNhlprCEXWZcbbdK2GW7AvSPQLIChF1mvAm7zLjabpWwy3YF6B+B5AQIu8xYE3aZcU1Dq4RdaagCY0AAgSgCRsIuFXStWL1eVi2dLwMH9PfG8dIrO2XOjStk3tVXyrTJE6KMzYljCLucKBODzLAAYZeZ4hJ2mXG13Sphl+0K0D8CyQoQdpnxJuwy42q7VcIu2xWgfwSSEyDsMmNN2GXGNQ2tEnaloQqMAQEEogjEHnYdPHREblm+Vq6aOrHHKi4Vgj3w0CZZvHCW9G1uijK+1B9D2JX6EjHAjAsQdpkpMGGXGVfbrRJ22a4A/SOQrABhlxlvwi4zrrZbJeyyXQH6RyA5AcIuM9aEXWZc09AqYVcaqsAYEEAgikDsYde+/QekdckaWThvhowa0VI0BrW6a/kd66TtptmFFV9RBtmbY3To9vAjmwunB7dR3LDxMbl52Vrv9SmTxhVCOH3uqzv3FK1OU3Obu2ild7xetUbY1ZvqcA4C8QkQdsVn6W+JsMuMq+1WCbtsV4D+EUhWgLDLjDdhlxlX260SdtmuAP0jkJwAYZcZa8IuM65paJWwKw1VYAwIIBBFIPawKy0ru1Qwdde6H8jcqz/hrSJTq8pa29bI6mULvBAuuNXiravXe143zJkueg77D7wrH584trDtogrHfrRpi6jnCbuiXF4cg4B5AcIuM8aEXWZcbbdK2GW7AvSPQLIChF1mvAm7zLjabpWwy3YF6B+B5AQIu8xYE3aZcU1Dq4RdaagCY0AAgSgCsYddqlMVCq1/aFOq7tmlV2UtmDPd215RhVunDR9WCLL84Vdznz7eVoyTxl8kjzz+tLReN9OzbLv9Xu+5u+7/IWFXlKuLYxBIQICwywwyYZcZV9utEnbZrgD9I5CsAGGXGW/CLjOutlsl7LJdAfpHIDkBwi4z1oRdZlzT0CphVxqqwBgQQCCKgJGwS3WswqNrrl9aNIbgNoJRBhjXMWoLxa+2rZFvts6WlqEneWHWuIvPKYRdYa+r+469tnOPN4ThLUO8/1Z/rli9nrArrsLQDgI1ChB21QhY4nTCLjOutlsl7LJdAfpHIFkBwi4z3oRdZlxtt0rYZbsC9I9AcgKEXWasCbvMuKahVcKuNFSBMSCAQBQBY2FXlM6TOkZvS6jDrbCtFkuFXWecfop3D7IT+h3vrfD67cuvF4Vde98+nNQ06AcBBEIEtjxVJ//v+3VO2PzlSQ/KRc/c5sRYuz78Cen8y684MVYGGV2gob5O1Id5STwOHumQPx5qT6Ir+kAAgRIC//ztenn5FTd4vtHwV9L8u+ecGGzHX98hctpZToyVQUYXOL65Ufo2NUQ/oYYj//DOUeno7KyhBU5FAIFaBN7cWyfLb3Pjd8iz+++Qa1/4Qi3TTe7c4aOk45Z/Sa4/ekpMYPAJfRLri44QQACBWgQyH3bpYGvYkEHe/bjUIxh+qedKhV3BLQ+D9/o6fJRfUmq5ADkXgVoFnniyS7673o2fw88NfUgu2HprrVNO5PzGj02T466+PpG+6CQ5gc7OLunbJ5kP8nh/TK6u9IRAKYGV3+qU377c5QTQksYvS9Nrv3FirH2+8S9Sf/poJ8bKIKsT6HNcfXUn9PLog4c7pL7ejQ/aezlFTkMg1QK//73I3/5dR6rHqAf3gRN2yBf+242wq/59Z0iftrVOuDLI6gSSen+sblQcjQACCPQUyHTYFRZ0aYIo9+xS2xiqsMv/CIZdO/ce5LpCAAGLAmxjaAafbQzNuNpulW0MbVeA/hFIVoBtDM14s42hGVfbrbKNoe0K0D8CyQmwjaEZa7YxNOOahlbZxjANVWAMCCAQRSCzYVfY6q1yoZUKv9RDrf4K2+ZQn0vYFeWy4hgEkhMg7DJjTdhlxtV2q4RdtitA/wgkK0DYZcabsMuMq+1WCbtsV4D+EUhOgLDLjDVhlxnXNLRK2JWGKjAGBBCIIpDZsEttSzjnxhXyxu69RQ7XfmZyYTvDDRsfk5uXdS+xnjJpnCxeOEv6NjcRdkW5cjgGgZQIEHaZKQRhlxlX260SdtmuAP0jkKwAYZcZb8IuM662WyXssl0B+kcgOQHCLjPWhF1mXNPQKmFXGqrAGBBAIIpAZsOuKJOP4xi2MYxDkTYQ6L0AYVfv7cqdSdhlxtV2q4RdtitA/wgkK0DYZcabsMuMq+1WCbtsV4D+EUhOgLDLjDVhlxnXNLRK2JWGKjAGBBCIIkDYFUWpzDGEXTUCcjoCNQoQdtUIWOJ0wi4zrrZbJeyyXQH6RyBZCzTQvQAAIABJREFUAcIuM96EXWZcbbdK2GW7AvSPQHIChF1mrAm7zLimoVXCrjRUgTEggEAUASNhV6ktBNWAzjt7pKxaOl8GDugfZXypP4awK/UlYoAZFyDsMlNgwi4zrrZbJeyyXQH6RyBZAcIuM96EXWZcbbdK2GW7AvSPQHIChF1mrAm7zLimoVXCrjRUgTEggEAUgdjDroOHjsgty9fKuIvPkWmTJ0QZg9PHEHY5XT4GnwEBwi4zRSTsMuNqu1XCLtsVoH8EkhUg7DLjTdhlxtV2q4RdtitA/wgkJ0DYZcaasMuMaxpaJexKQxUYAwIIRBGIPezat/+AtC5ZIwvnzZBRI1qijMHpYwi7nC4fg8+AAGGXmSISdplxtd0qYZftCrjf/4EDdbLlKTfmMWBAnVx8YacbgzU0SsIuM7CEXWZcbbdK2GW7AvSPQHIChF1mrAm7zLimoVXCrjRUgTEggEAUgdjDLr2y66qpE2XsmNFRxuD0MYRdTpePwWdAgLDLTBEJu8y42m6VsMt2Bdzv//WddbL62w1OTOTUU7vkS7M6nBirqUESdpmRJewy42q7VcIu2xWgfwSSEyDsMmNN2GXGNQ2tEnaloQqMAQEEogjEHnapTjdsfEw2P/WcLF44S/o2N0UZh7PHEHY5WzoGnhEBwi4zhSTsMuNqu1XCLtsVcL9/wi63akjYZaZehF1mXG23SthluwL0j0ByAoRdZqwJu8y4pqFVwq40VIExIIBAFAEjYddLr+yUOTeukDd27+0xhvPOHimrls6XgQP6Rxlf6o8h7Ep9iRhgxgUIu8wUmLDLjKvtVgm7bFfA/f4Ju9yqIWGXmXoRdplxtd0qYZftCtA/AskJEHaZsSbsMuOahlYJu9JQBcaAAAJRBGIPu/Q2huMuPkemTZ4QZQxOH0PY5XT5GHwGBAi7zBSRsMuMq+1WCbtsV8D9/gm73KohYZeZehF2mXG13Sphl+0K0D8CyQkQdpmxJuwy45qGVgm70lAFxoAAAlEEYg+79u0/IK1L1sjCeTNk1IiWKGNw+hjCLqfLx+AzIEDYZaaIhF1mXG23SthluwLu90/Y5VYNCbvM1Iuwy4yr7VYJu2xXgP4RSE6AsMuMNWGXGdc0tErYlYYqMAYEEIgiEHvYpVd2XTV1oowdMzrKGJw+hrDL6fIx+AwIEHaZKSJhlxlX260SdtmugPv9Vxt2XTa2Xj79yQb52c875cGNHQWAr7U2Ssuwuh4gjz/RKS+81CUzr2qQg4dE7vxOu2x7scs77srJDXLFR+rlwDvFz5dSPfXULvnSrGN9uq9f/QwIu6o3i3IGYVcUJfeOIexyr2aMGIHeChB29Vau/HmEXWZc09AqYVcaqsAYEEAgikDsYZfqdMPGx2TzU8/J4oWzpG9zU5RxOHsMYZezpWPgGREg7DJTSMIuM662WyXssl0B9/uPGnaNPrNOrv1cowwcINLeIfKDnxaHXUEJFWRd/qF6uf973eGUCsgaG0W2PN0p96zrfk4FZCf0r5Oj7YRdUa8kwq6oUtUdR9hVnZcrRxN2uVIpxolA7QKEXbUbhrVA2GXGNQ2tEnaloQqMAQEEogjEHnapbQznLlopzzy/PbT/884eKauWzpeBA/pHGV/qj8lj2PXt73xP7v/+j3vU5u8Xz5cLzn1/aM3854w+8zT5xk3zZMAJ/eXQoSOyctV3Zefu3xeeUw3sf/uA/PWSO7y29LGpvxgYoBUBwi4z7IRdZlxtt0rYZbsC7vcfNezSMy21sisooYKs/W+LrPhWu+hzXnq5S1pOrpN77muXs86sl3F/Ui/73+6SwYPqilZ8lVJlZZcIYZeZnznCLjOutlsl7LJdAfpHIDkBwi4z1oRdZlzT0CphVxqqwBgQQCCKQOxhV5ROs3RMHsOuYP1e+d0b8i/3bJAbv/J5L8AKPn74yH/KL3/93zJ/7melublJ/H9Xx6qw68C7f5QJl10ofzbpg97p6pjHnvilHHjnXcKuLP3AGJgLYZcBVBEh7DLjartVwi7bFXC/fxNhl39V1xNbOgthl9r68OIL6kSFXicNrpM393b/OWwoYVfUK4mwK6pUdccRdlXn5crRLoZd6nemFXd8twfxgnmfLfxe5X9Rf6Fw24s7Ck8Pee8gWfLXX5YRp57s/Q6m2gt+iVF/cbHclxtdqTPjREAJEHaZuQ4Iu8y4pqFVwq40VIExIIBAFAHCrihKZY4h7BJRv/yc2jIk9BcqRadeV48vfu6T3p+/evYF+fZ3v+eFWH2a+nhh1/+69AL5r1/8SuZde5V3zB13PuA99//+/aeEXTVeo1k/nbDLTIUJu8y42m6VsMtsBcI+RFQ9+lc0B0cQXC3t/4BSvV/+31tWSvBDS/1hZKkPM03O0kTY5V/VpcbuXw2m/q62N1T371IrvKZ8rIGwq4oCE3ZVgVXFoYRdVWA5dKiLYVeQV70PLfvHf5UvXT3NC6/CXle7Z3zxs58M3ZFDvb88/OPH5YxRI2TO5z/lfVFRfbFx9d3/5v1545evLrmTh0OltjLUuP+NoNtrGfrewpdK1cRUnW76xj/JuWeNKnreyqRT3Clhl5niEHaZcU1Dq4RdaagCY0AAgSgCsYddbGMYhT07x1Ra1eX/B/flH/wTL/Dyh2N6G8PJH/2QvLH79x7MyUPf6/23+lOHYmErxrKjyExqESDsqkWv9LmEXWZcbbdK2JV8BdQHh7/buafwhQ//CNR74HcfeFiuuvIj3spo/QGV/jDR+3LIdzbICSf0L6ye1h9kvv32AZnysfElv2hiaqZxh11Xz2iQsRfVy70PdIha1aUe/rDrv1/s9O799exz3ffuWvBXjYRdVRSXsKsKrCoOJeyqAsuhQ7MQdpV7z1Gl0AFJubBLvWepx9gLP+AFW+p3tzNHvc/7EmKp8xwqc6qGWsu/Efz/Hvji56YVQkhVr+2vvC7933M8YVeZahN2mflRIOwy45qGVgm70lAFxoAAAlEEYg+7SnV68NARWb5qncyc9hEZNaIlyticOCbvK7sqrepSRdSBltqqcMsvf1P0DXd/2HXa+072vonY7/i+3gqvHa++QdjlxE+B3UESdpnxJ+wy42q7VcKuZCtQ6Rv2wdHo98QLzz/LC7FU2LXxJz+X0e8/Xfo2N3nPqQ/G1L+ptr3wsujjkpxVnGHX6DPrvCBr1+4u715d+lHuPl+EXdVVm7CrOq+oRxN2RZVy6zjXw64o7znB1UX+LQxVtXT48tHLx8n3N26Sz3zy4/LvP/wP+Ys/+1P5+oo1sYRd/hXNn/7Ex0K/DKLGEhxrcJW061suRqmX/yco+G8Eff7HLh8nW599wVuJt/vNvV7d3nfKMO/fCfo2Am79JCYzWsIuM86EXWZc09AqYVcaqsAYEEAgikBiYZcazIaNj8mO13bJDXOmRxmbE8fkOezyb0dYbuVVMBDztsf4yc+LtjFUK7v0Nwf1lohR23fiQrE8SL0VlhpGue20/MMMCzLTuKUWYZeZi4uwy4yr7VYJu5KtQKVv2AdHE/zGvQ67rv3sJ+TO735f1J/6Q0f1d9fDrrBVXcqEsCu+65SwKz7Log+dF/2TdI44y0zjtGpNwPWwq9r3HB1u6d/L1O9z/jb0CiEVSKkvJZbb/jBq0YLb25c7T70Hqt0+/Pd0Dt4H2uUtF6utV/DfCP6w7Cc/2+ytxNO7o6g//VZR65On4wi7zFSbsMuMaxpaJexKQxUYAwIIRBFINOx66ZWdsvyOddJ202wZOKB/lPGl/pi8hl3Bb5aVKpR/5ZYKs9RDbdP09/94j/zfr1wtQ08a7N2zS4dd/nYIu+K5/Kt19N/oOng/mDRuqUXYFc91EmyFsMuMq+1WCbuSq0C139hWIwu7x6Va2aW+mb3pP7fIY0/8UiZcdqFM/OBY770zzWGXXrU1cMAx8337Re78Trtse7HLC7RmXtUgW57u3p7Q/yDsiu86JeyKz9LfEiu7zLjabtXlsMv/+1XYvbpK2Qbfq/wBjP7ChXoPOnzkcM1hl789dS+wah/B32n0WFU7rm25GMe/EfxtKAN1X7WhQwZ7K7zUvxkIu8pfYYRd1f4ERjuesCuak4tHEXa5WDXGjEA+BQi7aqx7XsOucgGKf+WW+oag+vDu92/uK2yjUG5lF2FXjRdk4PTe/CKlmyi1sittW2oRdsV7zejWCLvMuNpulbAruQpU8+11Narge6V6zv/BoNqa6F/u2eDdu6tPU5/Uh13JSZfu6dRTu+RLs4qDtDSMK8kxEHaZ0SbsMuNqu1WXw65q33O0dbmwy1+PSvf6ilI7/xfq9PF/v3h+4V5TldoI+51S3V/M5JaLlcbU29errVfYvxH8tQt+gVRZxxl2qf5/9ZsXvJ1ZSu3oEtx20v+lSf1ay9D3Fm2tqO9Veu5ZoxLfcpGwq7dXb/nzCLvMuKahVcKuNFSBMSCAQBSBRMOuW1ev98bENoZRSpPeYyr9shMMu/Tqrkd/vsWblH8bvbCVX3rm1a5ISq+YvZHpXyD2/P6twiDK7Y3vH2m5sCtNW2oRdpm5vgi7zLjabpWwK5kKVPsN+7APsdRIS30LPurqahOzrfaeXSbGELVNwi4Rwq6oV0t1xxF2VeflytGuhl3l3nP07wI3fvlqL1RS7yvqoXfcCIYipbbWq/T7X5QaB3+3UGNZ9k/3yJK//rKUW42mQ7Kwe3apsOuLn/uk94WR7a+8LnFuuRhlTr05Jq5/I5T7UmOcYZe+x1q5rfCD/y4JXnd6rG+/fUC++LlphetP163/e44n7CpzMY3ut0O++OIXenO5JX4OYVfi5Il1SNiVGDUdIYBAjQKxh1379h+QuYtWyjPPb+8xtCmTxsnihbO8m6xn5RF1ZdfON+rkhz+ud2Lap57SKR/7SJcTY2WQ5QWCH5TqX1SnfPRDhf3vS7VQLuxK05ZahF1mfgoIu/7/9u49/oqq3v/453vhIhcBuaMmeUk0vKRhmGaoWYp5NHqI+lPDVCI52iMkTSxTuwhBSOVJIhPhKEeFXyYPj9jJVCo6klRe8oIXFDURSFDC5Pa9nMdnvq3t2us7s/fMnpl9mf36/qPfLzNrZp5rZs+e9V5rTTqulS6VsKs8NRDUY9vvM7hQ727Crnj1Narv03LWc5fFK6RMa7cdcIhsv/zGxLdG2JU4qVcgYVc6rpUutRbDrmKdH9zQwe0IVyhAsusjjbCr2L6750PQNIYadiU95WKa52Kh+77b+aXQsuUIu0z4qdNE/vyOXwaO7PLrpGrvu9nXTx8/Wp54+gVvqkUdsX7vsuXygT2HyOoXXiHsKnDSEXaleUVSdlgBwq6wUiyHAAKVFkg87Kr0AZV7+2HDrpdfaZAFtzeVe/dK2t4B+7fL+f+vvqf9uXdpo2x+p6Ekv3KvNO6MNunbxz+c9GsoDdvTr1jYVS1TahF2pXPGEXal41rpUqs17PrtiiZZs6bSOuG2f/wn2+WDw9sCFy40KtkNu9wpf0yhJxw7ymv0ef6ltWLe2WW/3yRqA2G4Iwu3VC2N7CLsYmRXuLM6+lKEXdHNamGNWgy7asHV7KM7aqzQDB9+x1XOKRfTci02c4kddpn3pK1+cW3e7pjvCPrvM2/6T/nShHGdRsaFfd4rdJx2Gfp9JGrYZa9v7+uDj6z03rH25oa/y9DBA73/JjnlYti6YxrDsFLRlmNkVzSvWlqasKuWaot9RaC+BQi7YtY/YVdMwCpd/eZ5zbJ+Q5XunLNbl01ulYED/MMu7b1p3vFi5lcPmp7EPdpiYVe1NLwSdqVznhJ2peNa6VKrNez6xS+b5Mm/1kYHg/HjWmXkyPod/UzYlc5VzMguke80Tpbd3nguHeCESyXsShi0Sooj7Eq3IvymVLQDFPff9ZlFwxB7ysX7H1yRG12U5pSL6UpUf+luh8liIZ3fyL+gsEuPft6CX8jgQf29EV7L/7CKsKvIKcHIruq/ZuphDwm76qGWOUYEsiGQStil7+Zav3GzN2Wh/lw7a77c/9BKGTq4v8ybOVX222dYNvREhLArM1WZdyBZCbtMj8mBA/p589m7DyKFpjUk7Er+3D67/1L56NM/TL7gFEok7EoBtQqKJOyKXwmEXQ0y7+e1MVKdkV2M7Ip/xfuXQNiVlmxlyyXsSt9fQ5OvXTvH29CggXvkva+rmqZcTF+iurdg3pPm7mWh93bZdWvWM++KtkflDR7QX+bMvUPGnnSsF2QmMQqtFE1GdpWiVnwdRnYVN6rVJQi7arXm2G8E6k8g8bDLvLNr6qTxMurwEbLqidWy5L7lXvD19OqXc/+flfd2EXZl86LJStilteNOkzV18nm593X5hV3uw439IFqN749hZFc61yBhVzqulS6VsCt+DRB2EXbFP4s6l8DILkZ2pXFeUWY0AcKuaF4sXT8CxUZ2+Ulox0mdrlADrXK8XyxqbRB2RRULtzxhVzinWlyKsKsWa419RqA+BVIJu6bdcItcMflsbwSXjvLSn8snjZc1r66TWTffJdOvnij9+vTOhDhhVyaqsdNBZCnsymYNvX9UhF3p1DBhVzqulS6VsCt+DRB2EXbFP4sIu/wMmcYwjTOLMqMIEHZF0WLZehLwC7vs94vZ09uriztai7Ar3tnCNIbx/Fg7GQHCrmQcKQUBBNIXSDzs2rZ9pzdt4ZmnjZH9P7inXHLVHLFHec2et1jmzphC2FWkbo8e1Shnfa5JHlnRJkuXteYtPfXfm+WgD3W82+TtLSK33t4iq19sF13n3DObZNv29/+my5w+tklO+VSjbH03/+9Bu3DCHitl7DPT0j/7EthC6yGjZcfk7yRQUn4RhF2Jk6ZWIGFXOrSEXem4VrpUwq74NUDYld2w6502kR+821Uu7rFLhjfnv5dt2fYmuW1bV+8EOqK5Vab02indG0R0neu2dpPBje25v+kya1sa5Ntbu8oBzfl/DzoDGdnFyK74n06UEFeAsCuuIOtnVSBM2GXPDmKmLzQehF3xzgzCrnh+rJ2MAGFXMo6UggAC6QskHnbpLusIrklXzpY3N2ySi84Z643qMtMbHnX4CO/3rPwkPbJrxAENctH5zdKvj0hLq8gDv8kPuyac3SSjjmiURUta5dFVbfLtac0e5bemt3hhlwZkzc0iq/7SJgvv6gjJdJndezfIrhbCrrDnXbGwywSIzf96bcnvH33f228b7vLPvdAus3/S4i2qdfqJoxvF/pv+3YSa7t/d8i+b3CoDB+Q3yoU9ziwsR9iVTi0SdqXjWulSazXsMp05unfrECz0uegua8y37xDv3vmh/RpifeZecPwaOWDwO5WuylDbbxs2XNp79Qm1bNiF3liXvbBre7vInHe7yl9amqS3tMm3eu/MC7ue2NUoC97rItf13iF9G0Vu+WcXj2tiz11e2KUB2db2Bvlij11yeJc27990mbWtjdKrgbAr7LnFyK6wUiyXlkA1hl36PPjt73U871X7T5euItdc1fF8U68/mzY3yNZ/dHRKrfafAQPapVev+n2GZBrDdM5QpjFMx7UaSiXsqoZaYB8QQCCMQCphV5gNZ2WZpMMu4xI0skuDqy3/kLygxIRfuq6GXWteaZdhQxtk4Z0tcuABjTL6o42y5R/t0n+PhtwosEL+jOwSKRR2mYZUEyhqWHX0UY2dgkm7LnXEnVneb32tw5YWkbt/2RFi2sHlK6++H4z51RthV4Pce9+/Uscq/2A5u/9S+ejTP6zyvezYvSyFXebddBef9znvvQH1/FOLYZfpBLJ+Q8dnoek88OhjhTsZ2PWsnQf67N7RMcR0Gin1M3dar+uk/4u/rYnTaMeka6X18GMT3dcshl0GKGhklwZXeza1ydjuHZ2I7PBLf9ew68RuLfLUriaZ1HOXrG9tkKXbm2WvpjZ5oaUpb8RXUGUwsouRXYleqBEKc6cbi7Bq5hYl7IpXpYRdIv/9QKM8tqoxHmSZ1j7t1FYZdSRhV5m4Y22GkV2x+Fg5IQHCroQgKQYBBFIXIOyKSVzOsMtt8NNd10a/k8Y0yoPL22Tj39tzUx8eeViDF3oN6N8gb23q+O+QwYRdYau7UNiljaa2pV+92Nux60inpHSX14bXkQc3yqbN7bJrV0eQqdvo0kW8gNI08AbtO2EXYVfY8zrKcpUKu17925ty9Xf/Qzb+fbMMGriH3PDNS2WfvYbm7fr27Ttlztw7vL9NueQ8Me8JsKdPOeHYUbl/q6WwK+19rcWwyx3RrPVuj2oudl67nUfifuYSdmVvZJc5h/zCLjPq67AurbmwS6conPPPrjKl507p29iem/rwwR3NMqprq6xrbZBhTe3ef5/cRdhV7Bo1/87IrsJS9v1Rl5w6+Tw5+cRjcivp+3PuvvfX3u8jDhgu3716svTZveMdyea++fCKVd7v9rq1FHalva+EXWGvVv/lCLsIu+KdQeVdm5Fd6XhnbWSX3lv15+LzP5cOWIKlpr2vhF0JVhZFIYBAqgKEXTF5KxF2Pf3s+73Zg8IuPazjj2303t+lI7xO/XQTYVeEuo4SdoVpeNWG2UEDG7zRX6ZuzCgu0/D664db5dMnNIn+V6c11KkR9fdiYdfVR94jvdvejnB0lVu05YTPS3vPXonuANMYJsqZK6wSYZc25P3gpoXytcsmdAq4zI7ZDXZ2oOW+S8D+sp9GgGQHa2bf7PcTFGuUrFQwl5Wwy+10UOgqsEd16XJxP3MJu+oz7Dqle0tuisKgsEvPr1ve6yqDGtu8EV4P72gi7IpwiyLsCsZy72Pu724I5P5e6J6YRoBkB2/mqEzA5t4f7Xu5LlvJYI6wK8IF67Noz+Ztcv3rY+MVUq61d+sl793Y0Yid5A8ju5LUTLcswq50fCsRdrn3FT0yt9OHOVq9Pz35zAt5HUL0OfJr187xFnHXSzNAMtv9wfVTcrOPuPdPt2NLpfZVbQi70rlmKBUBBJIXIOyKaVqJsMsOP4LCrudfbPPe/WWCsSgNg0xjWHgaQ3eklp5CxUYZmJEJ+j41fc+X+84uHdl16+0doeTeezbI62+0y/2/bvXqsFjYNX3XF6TLW6/HPJPLs/q26xdK+6BhiW6MsCtRzlxh5Q67TOPW2JOOLTjVoD4A7DVskLefjz/1fG70lvm76eVuh1+67DdvuFnMNIamkfCwD3+o5F56hRoHizVKljuYs8+QWgy73Klf9XjC3tP8pgQ2YVepn7mEXfUZdoUZ2TWkqd1795cJxpZtJ+yKcoci7ArWcjuDmHvmRw490Bvd5TbGuffAmTf9p3xpwrhcRxJ7eb+g7PYly3xHVoetz0KNg7q9oYMH5jXsabmm13y5gzn7mAi7wtaw/3KEXYzsincGlXdtwq50vCsVdhXrMKlHa4IkO9By76+FOoskKWaHVibs0nv7HUvulzNP/5Q3MtuEeFdeOsG7Z1ZqX81xE3YleQZQFgIIpClA2BVTt5xhlwlVir2z65EVbaLT5dk/YRsGdR3CrsJhl2loPehD+S8ftgMs217DMR1lZ7+PS9/hZd7FZTe86jvWzLJvv9NO2BXi+iTsCoFUwiLlDrtMQLT6xbW5vXV7ewc1zukKOq2hafTT3+2Hgb6798qFXQfuP9xbtlioVoysUNgVplFSA7tyBXP2sdRi2KX7r5+TOuLV/lm3vt17B1ehH7+OCHE/cwm76ivs0vMrzDu7Lu6xS4Y357/7hLCr2Cdp/r8TdhX20nvgI3/4kxdC6c/PFt4jV172hbwGseOP+agXGtkdQPxGTdv3sOV/WJXrPPL8S2tl2YMr8qYIjlaLHUtH6Qlv78uOnTuk3MGcfXyEXaXU9vvrEHYRdsU7g8q7NmFXOt7VGnbpveZv6zbKqI98WH5+xy9zI7vM302HC/ee6d7P/EaGRZXUbeg9fNIFn5dZNy3Mdch0y3E7tlRiX+19IuyKWtMsjwAClRIg7IopX+6wy313id2Q59eD3RweYVe0ii40jaFbkr6Da8I5zbLyT51DRl3Wblhd/WJHQ5hdb37/rssUexeY2Q9GdvHOrmhnd7ilyx12mS/9puHOfLkfOKCf13Dnfrm3G8f0iNwAyy/sOu/MsfLw71blhWLhNDov5U5jaE9haBr6/Bolu3XtVvZgzt77Wg273BrQz1B9L+XCu/I7dtjL+Y3CDfpMjvKZS9hVf2HXE7saZcF7XeS63jukb2NH+KU/E3vuEr/3fJnzkLAr2icsYVdhL2+01u33yOYtW733WtpTG5l75tZ/vierHn8mbxom9/6qW/ELu044bpTcsWRZ3tRO0Wrw/aWLTcNklnTv9ZUI5uxjJOwqtcY71iPsqq2w64KRK+TALs/Hq/Qyrd36kWNFQ5Qkfwi7ktR8v6xKhV3mnc8d3+nz31tp3/O0U4cddrlhljtDh9vZUgOzOO/vCuqQqSO33J9C+6LLpr2v7v4QdqVzzVAqAggkL0DYFdM06bDLBBz9+ry/Y29vEW+KOxOUaHBlRhXZ/0bYFbMyrdWjhF3uyAEz5ZYZuaUNrqd8qlEefazjXWvuvxN2xas3RnbF8wtau9Jhl+6XPQ3TkqW/kbvv/XWn3dXRX5dcOF7mzl9cdGSXjhoLmr89jqJ50Dj1pGPzR2v5NEr6TdeYdjBnH1sWwi6384b7mWqON2h62bifuYRd2Qu7treLN/3gX1qacpfLEc2tMqXXTun+r0HcGlzdtq2r9+/2vxF2xfn0zF+XsCvY0g2s3PuOO5WvNuzd/+AKL7h65x/vdnofpht2zb75Dm/j7rtBkqhddxomU6YJxOxR3JUI5uxjJOyKV+OEXbUVdl3e5wcybPX98Sq9TGvvPH+qtHz85ES3RtiVKGeusEqEXe6R6P3l72+97Y1Sdkcs+00nb8+4ERQg6TL2FPql6GnZ9ujlYu+V9htVVq599Ts+wq5Sap11EECgEgKEXTHVkw67Yu5OIqszjWHxaQy1EXXYkI4WMHf6Qr+GV3fVLhi3AAAgAElEQVQKrqB3dplAU8tlZFe405mwK5xT1KXKHXa5X/51f/VhJGg6Jb/51ItNDahhlL4nJIkpmlxPe+RZoUbJMceMCjWyK61grlbDLruThzt9YdBn7qgjGmXRklZ5dFVbXnURdkX9NMhf/o112Qu74okks3bbAYfI9stvTKYwq5RbFzTJq6/lT7uc+EYSKpCwKxjS735oGsHOO/PUoqObi00NqMHYNVMnyq133Bt7ml+/o3DDOHsZ+36+4a1NFQ3mCLviXcyEXYRd8c6g4LUJu9bKxS9+MS3eRMuthrDLfhZ7dNVTYjp02AdqOkBqh0r9MaO1/MIu0+HSvFurVDDT+UNHZ7s/btl2YNe9e0dnqzCj0JLaV79jJOwqteZZDwEEyi1A2BVTnLArJmDM1VsPGS07Jn8nZimdV48ysivxjUcskGkMmcYw4ikTavFyh13mC7zphae/u+/hCmoc0wcAv1565sHFfWgp9L6tUDg+C9lhV6FGSfddKlqUve/6+zdvuFnSCuZqNewqtV7SWI+RXYRdaZxXhF0ihF3BZ5Y7OspvZJe5f+o90R7ZpS+5txvICt0T9Z1Zeg+6+LzPid+USqWe+4XCLrtRUsuvZDBH2FVqDXesR9hF2BXvDApem7CLsCvKueU3Stis7z4zhnkPlt5fLzrvDPnRz+6UL00YJ/vsNTTK7gQuGzSyyy/o0kIqua+6fcKuRKqdQhBAoAwChF0xkQm7YgLGXJ2wS4Swi7Ar5mXku3olwi4zxd/DK1Z5++S+B8veUb/Ayn6Plj0tkt+DRNBDRBhL3c8HHvqDnHLiMaKNim75xRolKxnMEXaFqeHCyxB2EXbFP4s6l0DYRdhV7LzSe8fXrp2TW8zvnV3m/ulO2eveX+113ftp0LSDxfbP/LveEx9Z8Sc5Y+zx3p/c93D91y8ekGM+dniusdC9H1cymCPsClvL/ssRdhF2xTuDgtcm7CLsKnRu/fZ//yzDPzAs776iy/u9W8t9DnPvUX6zh5iy/N4rGeecD3pGDdr3Su6r7hNhV5zaZl0EECinAGFXTG3CrpiAMVcn7CLsYhrDmBdRwOqVCLvSOZJ0SjXvGjGlu+85KdQoqeuUK5hzj56wK/75QNhF2BX/LOpcAmEXYVca51UlynSDNd0He3om9/5od07RZcsZzLk+hF3xzhjCLsKueGcQYVeQwIhehF2Fzq1i9xV7XTfs0n+z13c7i7hTB+qyM/9jodzwzUtjj/Bywy7zu05nb//Y98lK7avuD2FXWp9wlIsAAkkLEHbFFCXsigkYc3XCLsIuwq6YF1HA6oRd6bhWulTCrvg1QNhF2BX/LCLs8jNkGsM0zizKjCJA2BVFq/OyhF2EXfHOIMIuwq60ziDKTUKAsCsJRcpAAIFyCBB2xVQm7IoJGHN1wi7CLsKumBcRYVc6gFVaKmFX/Ioh7CLsin8WEXYRdqVxFlFmXAHCrniChF2EXfHOIMIuwq60ziDKTUKAsCsJRcpAAIFyCBB2hVC+Z9nv5JqZ870lTz1xtFx/xYWyW/eu3u+EXSEAU1yEsIuwi7ArnQuMkV3puFa6VMKu+DVA2EXYFf8sIuwi7ErjLKLMuAKEXfEECbsIu+KdQYRdhF1pnUGUm4QAYVcSipSBAALlECDsKqK86onVMnveYpk7Y4r069Nbbpy32Fvj8knjCbvKcYYW2QZhF2EXYVc6FyJhVzqulS6VsCt+DRB2EXbFP4sIuwi70jiLKDOuAGFXPEHCLsKueGcQYRdhV1pnEOUmIUDYlYQiZSCAQDkECLuKKGu4NXzvITJu7HHekm74xciucpymwdsg7CLsIuxK5xok7ErHtdKlEnbFrwHCLsKu+GcRYRdhV2eB97Y1yMYNDWmcXomX2aNnuwwa2J54uZUukLArXg0QdhF2xTuDCLsIu9I6gyg3CQHCriQUKQMBBMohQNhVQHnb9p1y7az5MvrIg3Nh15pX18k3pt8i35s2UfbbZxjTGJbjLC2wDcIuwi7CrnQuwrTCrt+vaJJdLW3p7HTCpX5m+yKRXbsSLjWd4nadNiFUwYRdoZgKLkTYRdgV/ywi7CLs6izw7HONcteSxjROr8TLHPWht+WMI15OvNw0Cmzv3U/ahn4gVNGEXaGYAhci7CLsincGBa+98/yp0vLxkxMtftOmBvnRT5oSLTOtwkb0WisXv/jFtIpPtNy2vfaT7d/4aaJlamEbNjbItvdqo0PI4MHtsttu2esQQtiV+GlNgQggkJIAYVcBWBN2nXnaGBl1+AhvSTfsClsvz73QLrN/0hJ28You95lBj8lJT369ovsQduNdjjxGen79+2EXD73cdd9vkb+tq40vKDPbJkjjhtdCH1slF9z9x3dL45A9E92FFSvbZMGdrYmWmVZhXxhynxz6+I1pFZ9oud1O/rzsduGURMvUwi77+i7Ztj3xYlMp8AebThbZuSOVspMutO9/LRdpbi5a7K7WdunSVJ4HxZ0tbaLhWpifn9/eKiv/VBsh6LV9vy29n3skzGFVfJmeX/uedDnqk4nux9rX2uW7s2vj+8zH+z8r457+90SPP63Cmg86THpd/5PEi//+j1rkxZdr4/vMjC6XSvNrzyRukEaBvWf8XJr27Xg2SOrnL0+2y83za+Pa+vyQR+Tox7+d1KGnWk7X4z4jPS69JtQ2oty3QhVYYKGw9+OWVpEvX14bHW96NW+T614fG5emLOs39OglfRb8KvFtLfr/rfLI72vj+8y0ATdK/7/el7hBGgX2uGSadD3+1ESL3vh3kau/WxvX1sjd18oFz9dG2NW0z/7Se9aCROtKC/vxz1rkqWdq4/vMd3t+Xbq/9FjiBmkU2OtbP5LmkUemUTRlIoAAAhUTIOwqQB9mZFfFao4NI4AAAggggAACCCCAAAIIIIAAAggggAACCCCAAAIICGFXkZOg2Du7OIcQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQqJ0DYVcR+1ROrZfa8xTJ3xhTp16e3aPilP5dPGl+5WmPLCCCAAAIIIIAAAggggAACCCCAAAIIIIAAAggggAACngBhV4gT4Z5lv5NrZs73ljz1xNFy/RUXym7du4ZYM9uLlPr+smyrcHQIIJAVAbezQ1aOi+MojwCdY8rjzFYQQKAyAvp8tPLPz/JcVBn+mt7q21u2yiVXzZGpk8bn3otd0wfEziOAAAKWAJ9xnA4IIIBAZQUIuyrrn9rW7YDObOSQg/bNjVBLYsOEXUkohivDrz4X/PAq7wHRfJn6wLBBeQ0OWj+TrpwtR4w8gIaIcMx5SxnXvz73cu7vhN0lQKa4inmv4v0Prcxt5aJzxiY28pawK8XKq2DRfte27s53rrxQxo09LrE9I+xKjLJgQcU+q/U6vuCrMzrVr7mvJl3v5Tnqym+l0PeSyu8de6AC5tw3GkMH95d5M6fKfvsMSwSIsCsRxqorxD1vdAeTPndoCC5ftZvnwTc3bOr0Xdl8j35t3ca8NgJTP7qCmd2mfHucjS3pd8Bb71yWO5ikr6FsKFX2KNzvMUm2lfEZV9m6ZesIIIAAYVdGz4FyPIASdqV/8piHEN2SPaJQv0BN//EimfaVc72dmHbDLfLOlq0y9ctn5XpI6pfs59e8Ln169yTsilhV5kHfBIpmdb2u9CfJBvGIu8biloC5PkYfebBXJ+b3IYP2SCTwIuzK5ulWrgdQwq70z58wn9XedfzTu6Vvn94y/eqJ3pTUeg6Y++b4fzuez/QIVRXme4ka81N5Afcepr9Pm35LYoFXOZ41Kq9Yf3tQju8+5boP11/t5R+xXqM3L1za6ZrX7yef+NihMnLEvnLtrPmyZes/5TNjRuXuhbre/yxf5f2dsCvaWWTO7aMOH5H3LKLtJovu+Y1cccnZzBAUjTS1pd17mP6++L7liZzzfMalVm0UjAACCIQSIOwKxVR7CwU9gJob72c/dbQsWPwr78BML8+g6RrdXtNmdMu6DW/JN6bfIp896eMy/aZFXllJjqqoPfXk9zjMA6dptPvcKcfKHx9f7X2J1rrRL9T7fmCoPPXsGsKuCFXjBihBq+qD4rvvbZd3331PdGSRGR1g9+Rzrwf3WjLruCOUzN/9rtdvfPV8mbdwqXxv2sRc7+ww50kEgppZ1K+u1GLJfcu9c37uwnsj11FQLz9Fcafc0boevveQXONA1PqtGeiM7WihB1DTicPc10wvT1P/ZqSnHYS754z5N/czgl69yZ5IYT+rzWfCoQfvJz126+Zdr1pn723b4d0fTVie7N5lt7Qw95tSriMVs68lu4e1PdLE/rvfdg49aF/p3q1rrpEx7HmSxRpz68pYnHnaGNmj3+553+HDfNapkTtawX0mMN9N7G3pLASl1G8W66QWjqnQNR703TfoGnXPGXMf3KNvb+87lXke1VFHzJ6Q7NkRprHdXKcnfuIIeej3f8l1otQOlfq32+7+VSIN/8keWXWXFqYTgFmmV68ecvfSh3PtJ4VeneE+K9rPmEHrudfrVZeeI/c/9Me86UPrufOyW1emTeWKyWfLk8+85E3TG6WO/EZR2s8EeuZePmm8dwLbz6v6epRS6re6rwT2DgEEEKisAGFXZf1T23qxsMud8s7tyWI34roNuovueVDGnjhaNr+z1Zsmb+wJH/Nu3GG+VKd2wBktOMzIAPuL2dJfrfB66r2+bqPsPWyQ91/epxDt5Aj7pV/rZtnDf8zrLWlfd7pV7S1pRhmZ62P8aWNyo5B+t/IJOW704Z2W0xEH+kXbNAa416t9TdZzQ57fsdt1oGFXlDpye73bDT4m7LDfL2HXQyn1m9RUUtHOcJYuFnbZ9zXVcutWPyNm3XyXN0pI74Pm/3U0i/7bS6/8TT4z5iivUdg+/8I0gFA74QXCflabBoUpXzpT5vxsieh/77z3ITnnjBO93wm7wpubhmu7wcZvbdPgY74f6jLu57VZZvq0id6IdPd76NPPvyK7de8mm9/+R95oJF1u7evrve+dftuxr0+9JsOEc9EEamdp99jtzz4Nu9zPuqh1ZH+mmQ5wQWFXKfVbO9LZ2tNiYZf7vcr97mRfo26Drv6uP/t/cE8v7DLfb813Zj6PkzuXwnz22aG0PjPqj3l+1P/OnreYsCtClYR9JjPhlN1xyq0v/Q65fuNmr/Oe+0ypv//P8se8Z8gHHl6ZNxrJbcPxew4y99Cw9/QIBDW1qPu93P5eqWHXNTPnS9g62r5jh/eZZp7z3WcNt03H/mwstX5rCpudRQABBMosQNhVZvBybc7taa7b1Zu1ebiwG2wLjY646rLzZMZNd+Qa7O39dxua/Hpxlut4s7odN2j06/Wsx27CEf3/mT+5U/YcOtAb4aVfgAm7op0dbkNZ0Ggd90tr0HVkHhRfeuUN34dGvwZbU+/HH/MR3xd4F2psj3a0tb20a+6GElHr6La7HvBA7F53pv7074VGdgU1KhSqX6bDrMz5V+gdT26Dre5hodER3udvwLRghR5stRcnP/EEwn5W2w0Kek/UqZl0uqZTThjtdTSgcTVaPYT5XqIhsI78t0cgB30Wep+tE84IrAv3OnLvf+52TOOdGXXr7m+0o63tpd3PLjtwSqKOwoZdZqo0v2utUP0yHWZlzj97lJbZA3v2Avt7krne7L/Z1+gjf3jcd1owv04n9XytplHTbtDojjrRdgFzbepoT20j0OfJ3Xv18EZ4BT23pLGvWSnTry3EHg1rRi/6PZ/7fRaa+5v6+N3rwsxw4V6vdidZ7VRp2hDqsQOeG3bZAWPUOtKOOXY4HCXs8nv20HorVr88S2Tlk4PjQACBNAQIu9JQrYIyi43s8gu7dCo2+8d8ITM9Vcz0TeaBh7Ar/YoOGtnljjgxX1SHDR7gNRjpQ4vpKU3YFa2egkYLuF84g4IUY69btR/49aHRTK9nfzn1m/JA19XrLCjscnti2j30oh1tbS/tTvlg3EyIFLWONOyypyWMMrLLbVQwsoXql7CrMudfsZFdboOCX8Of7rnp7Rk09RphV7r1G/az2r42tUHBjMTr3q0bYVcJVRTme0lQkGKPgtRNmxEgJuyy759m19xp8/TvZso9v+3ov9uj+a6/caE3UroeG/Lczy6/KSDdQDJKHUUNu6LWL2FXCRdoAqsUG9nlNp4Xuka1Dv0a+82zZdBo+QQOo+6LCKpH+zuQHXbpc6MdOIYZGVb3yA5AoZFd9udlUJDiTo1ud2Z1P5t1037PQfp304ajM1y416v+bupZR+/5PZvWS726ncPtqVT92tLcQN4ODjXssi2jhl2l1C9hV72cqRwnAgiUIkDYVYpaDaxTStjl9xDqHqrduKT/ZjcKMrIr+RMj6EEjKOxyG3OYNit6nQQ9qIQNu+yey3Y9FRrZ5fcFV/e8UKO8V/ZP7/YO0G6sin7EtbtGselCqmVkV1D91q58be95KWFX2MYA+5wj7Er3PAn7WR0URBf7/Eh372u39DDfS5IYNWSECo32KBZ4btn6Tzlwv71zo3VrV720PS/UWO1nF3X0XdSwK2hkl93AW9qRslaSAqWEXWHq0P7M9evMxciuJGsx+BmiUNhl7wFhV2n1EfTsHSbs0i2a2SUKtbmYPSvW9hLUOcWUreVM/fJZXgfZevwp1E4SFHYF1VGaI7vCtNHVY/1xzAgggEAhAcKujJ4fUcIuJXDn0tcvT7+4/7cy9sSjZdlDj8rnT/2kaO8Rwq7ynjDmS6xuVefsNj14CLvSrQfTG9qep7tY2OVeR2bUQNA7u/Rhc9lDK2Xc2E/mvbNLyzHv/TnqIwf5TmOoy5iHVfd9XunKVFfpxRqr/R7y7M86t4785st/7InV3vsK3FEgZsTW5Amne+9gc6dQDFO/+l4nfsovEDXscutW99i8d8TsvWkoIOwqb32G+awm7Eq2TsJ8L/ELu8x65p5Y7J1d+j6S/T+4V6d3dunR2O+O9ZvaydyP3fdtJCtR/aVFDbvC1JGZLUCPXmcSMN9P3ZE67rXpPmeEqV9GdlXmHIsaduny7nS+5hp97PHnvOtYO+MRdpW/PvW6u3nh0rz3CxN2pVsPxveow0fkdbQoFna515E9pZ75vDX3T9NOo+0z7ju77H8LGtllrsXX1m2s63eyRQ27CtVRmHtg0P2z1PplZFe61zKlI4BAbQsQdtV2/QXufdSwy24YMIXa87Pfeuey3LZMAMA0huU7edxh9vZUNPYQekZ2JVcnfu/1MdeEbiWot5w9XctF54zNe9Bxp7Qz5bnTUAwd3N97MNW51N33RJkjLNabLzmJ6i2plLDL1J35THPryK0/E3Zpo5tdfzrVRa9ePWTkgcO9sEt/otZvPU6rVQ1nU9SwS/fZ/Tywp1GbdOVseXPDJu/Q7ClQGNlVntou9llN2JVOPRT6XhI04sqtK7tDifvZbH/PcafjM5/bQdvRsoLqPR2N6iw1atjl91nn1+lHpz3X7ynHjT5M3n33vVxnLLuezjr9BO/f7B7p9v01TP1Wp2r29ypq2GWutwu+OiOHY67RoGuXd3aV7zzym07bXNeFniUY2RWvjtzpPYtNkee2xdjL+302288v7v240Dv2zFExkrKjs3fQ6x4KjdDTjjTud373c1DrT8NEM1Wr/ayv988Lxp8sTz27Jnf/dL8fhanfeGcoayOAAALZFiDsynb9cnQIIJBRAX14XXTPb+SKS87OjfjL6KFyWAgggAACCEQS0Ia8T3zs0LqdnikSFgsjgAACCNSNgAYr03+8SKZ95VxhBG3dVDsHigACCNSVAGFXXVU3B4sAAlkRoEdeVmqS40AAAQQQSFJAO4PwrsQkRSkLAQQQQCArAjpqae3r6+v2fZZZqUeOAwEEEEAgWICwi7MDAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEECgZgUIu2q26thxBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABwi7OAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgZoVIOyq2apjxxFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBAi7OAcQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQRqVoCwq2arjh1HAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBAg7OIcQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQqFkBwq6arTp2HAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAgLCLcwABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKBmBQi7arbq2HEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAHCLs4BBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBmhUg7KrZqmPHEUAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEECLs4BxBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBGpWgLCrZquOHUcAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEECDs4hxAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBCoWQHCrpqtOnYcgeoRuGfZ72Tln5+V66+4UHbr3rV6dow9QQABBBBAoIIC27bvlGtnzZfRRx4s48YeV8E9YdMIIIAAAghUl8CqJ1bL7HmLZe6MKdKvT+/q2jn2BgEEEEAAAQRqUoCwqyarjZ1GoLoE7LBL90wb9oYM2kMunzQ+1I7q+ovvWx7qQSfKsqE2zkIIIIAAAgikJOCGXW9v2SqXXDVHxp82JlT4FWV5s60o99+UDptiEUAAAQQQKCrghl1Rn/OiLK/bmjb9Fpk3c6rst8+wovvGAggggAACCCBQmwKEXbVZb+w1AlUlwMiuqqoOdgYBBBBAoEoEGNlVJRXBbiCAAAIIVJ0AI7uqrkrYIQQQQAABBGpegLCr5quQA0Cg/AJrXl0nk66cLW9u2JTb+KknjvamMdQfe8omvyDMbfxzl3HLHzq4f64Xnl95N85bLLfeuczbtr2s/m4eoqZOGu/15jP7vOCHV8mow0eUH48tIoAAAghkWkDvU9fMnJ93jN+58kJvJJcZqaX3pJEj9vWd4lDvgd+Yfot8b9pE2aNvb28kmC5v7ln2PU83ctE5Y72R1H7Bmvnb/Q+t9PbnkIP2zRtFbe6pnz3paG87fvfRTFcWB4cAAgggUDYBcw/863Mv57Zp35fs57ynV7/sO8Wh3gP1R+977nOhe8/T5cz91y9Y079d8NUZuX0xy+ofzL5OnnC6/PeDj4q5j5p7btnQ2BACCCCAAAIIRBIg7IrExcIIIGCCqOnTJuYa3vymMTTvJ7Eb7cyUEe7f7PW379jRqWFPH0ReX7fRayh0H2r0gWf9xs2594W5U1SYhxgTxuk7xaJMeUGNI4AAAgggEFbAvb8ETWNowiu/DhyF7ol+DXtzF94rXzz7FOnerVteeOY3raG7fyaYsxvv3Ptq2GNnOQQQQAABBIIE/Kbl9ZvG0LwH2u+Z0O4woh1A/J4LTRBmAqvb7npALplwhrjhmfvM6O6f+f2tzVtynS79noOpcQQQQAABBBCoLgHCruqqD/YGgaoXsHvTmZ0tFHYFNbatfX197p1e9vrrNryV69HuN596sWDMbVj068XnF8BVPTw7iAACCCBQ1QJuI5zubLGwy204M8ufedoYr0OJW6bfPdiguNvyu9cVayjUsphWqqpPM3YOAQQQqEkBv84dhcIu7aDo16lxyX3Lc50cCz2Dukjutoo90/qFbUxNXJOnHjuNAAIIIFBnAoRddVbhHC4CcQSCvuAXe9CwHy50++6UTH7rm6ki7OkkdF03GJt1810y/eqJ0q9P79yh6TImTCPsilPjrIsAAgggEFbAL1wqFnZp2XaDm3vPcsMpe8old0pCv84edqOgOQ7d3vC9h/iOlibsClvbLIcAAgggEEXAL1wqFnbZ99Vhgwd0mvrXDdDsaYTtWT3ce5sZCW06lpjj0O2ZZ0u/Z1bCrig1zrIIIIAAAghURoCwqzLubBWBmhRwe5ybgygWdtmNdbrO7HmLfd8Zou/80l58+uPOuW5CL8Kumjx12GkEEEAg8wJ2I5npgBEm7LIb+3S6Jf3Rd5Hoj99oMdNoZ94zYkIvdxpDLZewK/OnHQeIAAII1ISA3dHC7HCxsMu+hx724f07zf7hN1pMy/YLvexpDAm7auKUYScRQAABBBAoSYCwqyQ2VkKgPgVKHdmlWqY3n5EzDXnmgcTMz27CLls46EXEYaaXYGRXfZ6rHDUCCCBQboFSR3aZQOuLZ50st939KzHv8yoUdpljK9TrnWkMy30GsD0EEEAAgSCBUkZ22c+Jhx68nzz17JrcFIZhniHt58CXXnkjr8Ml0xhyriKAAAIIIJBNAcKubNYrR4VAagJueGTeN3LEyAO8hw/9uXbWfBl95MHeFEl2g9ykK2d7v86bOVXs93HZvfK0193v//hUrle7G7D5vYh4/cbNuQcf92XDhF2pnQoUjAACCCDgCLiNZ6Z3uRmdHDRSyyznTrtkLz9yxL4ya+5dcu64T+XuofY9zh3ZFfTOzMX3Lc+Nrg7zDhUqGQEEEEAAgbgCbgcMc3/TcufOmOJNSe93TzLL/fW5l2XBD6/y3mdpftx3OU//8SKZ9pVzc9Pbu8+Y9uwi7jOj2c7408Z4z7Bh3sMZ14T1EUAAAQQQQCB5AcKu5E0pEYHMC7hTQ9g97fTg/cIu0+im/25PV6i/uw8q+k4vfaAxPxedMzYXfvk9BGnj4q13LvMWHzq4f16YRtiV+dORA0QAAQSqRsCdgnfaZed6PdFNB5CgsMt0HJk84fS8jiLu8vb9173n+Y2+dvfHfc8XYVfVnDrsCAIIIJB5Afe9k2ZEc6GwS1H0We+xJ1bnTYPvPp809+sAAAXvSURBVEPq7CD2M6H+u33P83smtPdHl7ffFU3YlfnTkQNEAAEEEMioAGFXRiuWw0IAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE6kGAsKseapljRAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQyKkDYldGK5bAQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgXoQIOyqh1rmGBFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBjAoQdmW0YjksBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKAeBAi76qGWOUYEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAIKMChF0ZrVgOCwEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBCoBwHCrnqoZY4RAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEMioAGFXRiuWw0IAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE6kGAsKseapljRAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQyKkDYldGK5bAQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgXoQIOyqh1rmGBFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBjAoQdmW0YjksBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKAeBAi76qGWOUYEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAIKMChF0ZrVgOCwEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBCoBwHCrnqoZY4RAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEMioAGFXRiuWw0IAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE6kGAsKseapljRAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQyKkDYldGK5bAQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgXoQIOyqh1rmGBFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBjAoQdmW0YjksBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKAeBAi76qGWOUYEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAIKMChF0ZrVgOCwEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBCoBwHCrnqoZY4RAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEMioAGFXRiuWw0IAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE6kGAsKseapljRAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQyKkDYldGK5bAQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgXoQIOyqh1rmGBFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBjAoQdmW0YjksBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKAeBAi76qGWOUYEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAIKMChF0ZrVgOCwEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBCoBwHCrnqoZY4RAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEMioAGFXRiuWw0IAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEE6kGAsKseapljRAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQyKkDYldGK5bAQQAABBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAgXoQIOyqh1rmGBFAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQACBjAoQdmW0YjksBBBAAAEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQKAeBAi76qGWOUYEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBBAIKMChF0ZrVgOCwEEEEAAAQQQQAABBBBAAAEEEEAAAQQQQAABBBCoB4H/A6hlHbO2TBMvAAAAAElFTkSuQmCC", "text/html": ["<div>                            <div id=\"fc430666-9f45-4738-b76b-2a3dea5fd5c8\" class=\"plotly-graph-div\" style=\"height:200px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"fc430666-9f45-4738-b76b-2a3dea5fd5c8\")) {                    Plotly.newPlot(                        \"fc430666-9f45-4738-b76b-2a3dea5fd5c8\",                        [{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"version=Volume_2022_w14_27 (avg. week)<br>country=CZ<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>\",\"legendgroup\":\"Volume_2022_w14_27 (avg. week)\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"Volume_2022_w14_27 (avg. week)\",\"offsetgroup\":\"Volume_2022_w14_27 (avg. week)\",\"orientation\":\"v\",\"showlegend\":true,\"texttemplate\":\"%{value:.2s}\",\"x\":[\"Fresh\",\"GM\",\"Grocery\",\"Produce\"],\"xaxis\":\"x\",\"y\":[10109786.0,9774648.0,61133997.0,639310.0],\"yaxis\":\"y\",\"type\":\"histogram\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"version=Volume_2022_w14_27 (avg. week)<br>country=HU<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>\",\"legendgroup\":\"Volume_2022_w14_27 (avg. week)\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"Volume_2022_w14_27 (avg. week)\",\"offsetgroup\":\"Volume_2022_w14_27 (avg. week)\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:.2s}\",\"x\":[\"Fresh\",\"GM\",\"Grocery\",\"Produce\"],\"xaxis\":\"x2\",\"y\":[9716671.0,16802430.0,67406856.0,878358.0],\"yaxis\":\"y2\",\"type\":\"histogram\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"version=Volume_2022_w14_27 (avg. week)<br>country=SK<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>\",\"legendgroup\":\"Volume_2022_w14_27 (avg. week)\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"Volume_2022_w14_27 (avg. week)\",\"offsetgroup\":\"Volume_2022_w14_27 (avg. week)\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:.2s}\",\"x\":[\"Fresh\",\"GM\",\"Grocery\",\"Produce\"],\"xaxis\":\"x3\",\"y\":[7455515.0,7207327.0,53237707.0,537229.0],\"yaxis\":\"y3\",\"type\":\"histogram\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"version=Volume_2023_w14_27 (avg. week)<br>country=CZ<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>\",\"legendgroup\":\"Volume_2023_w14_27 (avg. week)\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"Volume_2023_w14_27 (avg. week)\",\"offsetgroup\":\"Volume_2023_w14_27 (avg. week)\",\"orientation\":\"v\",\"showlegend\":true,\"texttemplate\":\"%{value:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Fresh\",\"Produce\"],\"xaxis\":\"x\",\"y\":[6098798.285714285,48833024.0,7836522.857142857,579110.1785714285],\"yaxis\":\"y\",\"type\":\"histogram\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"version=Volume_2023_w14_27 (avg. week)<br>country=HU<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>\",\"legendgroup\":\"Volume_2023_w14_27 (avg. week)\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"Volume_2023_w14_27 (avg. week)\",\"offsetgroup\":\"Volume_2023_w14_27 (avg. week)\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Fresh\",\"Produce\"],\"xaxis\":\"x2\",\"y\":[10223395.42857143,48740790.85714286,7212599.428571428,528990.0357142857],\"yaxis\":\"y2\",\"type\":\"histogram\"},{\"alignmentgroup\":\"True\",\"bingroup\":\"x\",\"histfunc\":\"sum\",\"hovertemplate\":\"version=Volume_2023_w14_27 (avg. week)<br>country=SK<br>division=%{x}<br>sum of shelfCapacity=%{y}<extra></extra>\",\"legendgroup\":\"Volume_2023_w14_27 (avg. week)\",\"marker\":{\"color\":\"#EF553B\",\"pattern\":{\"shape\":\"\"}},\"name\":\"Volume_2023_w14_27 (avg. week)\",\"offsetgroup\":\"Volume_2023_w14_27 (avg. week)\",\"orientation\":\"v\",\"showlegend\":false,\"texttemplate\":\"%{value:.2s}\",\"x\":[\"GM\",\"Grocery\",\"Fresh\",\"Produce\"],\"xaxis\":\"x3\",\"y\":[4943122.857142857,40100612.571428575,6269816.0,416912.8214285714],\"yaxis\":\"y3\",\"type\":\"histogram\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.31999999999999995],\"title\":{\"text\":\"division\"}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"sum of shelfCapacity\"}},\"xaxis2\":{\"anchor\":\"y2\",\"domain\":[0.33999999999999997,0.6599999999999999],\"matches\":\"x\",\"title\":{\"text\":\"division\"}},\"yaxis2\":{\"anchor\":\"x2\",\"domain\":[0.0,1.0],\"matches\":\"y\",\"showticklabels\":false},\"xaxis3\":{\"anchor\":\"y3\",\"domain\":[0.6799999999999999,0.9999999999999999],\"matches\":\"x\",\"title\":{\"text\":\"division\"}},\"yaxis3\":{\"anchor\":\"x3\",\"domain\":[0.0,1.0],\"matches\":\"y\",\"showticklabels\":false},\"annotations\":[{\"font\":{},\"showarrow\":false,\"text\":\"country=CZ\",\"x\":0.15999999999999998,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{},\"showarrow\":false,\"text\":\"country=HU\",\"x\":0.49999999999999994,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{},\"showarrow\":false,\"text\":\"country=SK\",\"x\":0.8399999999999999,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":1.0,\"yanchor\":\"bottom\",\"yref\":\"paper\"}],\"legend\":{\"title\":{\"text\":\"version\"},\"tracegroupgap\":0},\"title\":{\"text\":\"CE_shelfCapacity\"},\"barmode\":\"group\",\"height\":200},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('fc430666-9f45-4738-b76b-2a3dea5fd5c8');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = px.histogram(comp_dataset, x=\"division\", y=\"shelfCapacity\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"CE_shelfCapacity\",\n", "             height=200, facet_col=\"country\")\n", "            \n", "fig.show()"]}, {"cell_type": "code", "execution_count": 145, "id": "15217671-ee25-4b86-a0e9-e8c23eae481e", "metadata": {}, "outputs": [], "source": ["fig = px.histogram(comp_dataset, x=\"division\", y=\"unit\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>unit_delivered</b>\",\n", "                           height=400,\n", "              width = 1800,  facet_col=\"country\")\n", "            \n", "cont.append(fig)"]}, {"cell_type": "code", "execution_count": 146, "id": "ab9acc07-d731-4b80-a3bf-254118a1cf62", "metadata": {}, "outputs": [], "source": ["fig = px.histogram(comp_dataset, x=\"division\", y=\"sold_units\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>CE_sold_units</b>\",\n", "                           height=400,\n", "              width = 1800,  facet_col=\"country\")\n", "            \n", "cont.append(fig)"]}, {"cell_type": "code", "execution_count": 147, "id": "5969c2a3-f7a0-45bb-9614-fec339ab3c87", "metadata": {}, "outputs": [], "source": ["fig = px.histogram(comp_dataset, x=\"division\", y=\"stock\",\n", "             color='version', barmode='group', text_auto='.2s', title=f\"<b>CE_Stock</b>\",\n", "                           height=400,\n", "              width = 1800,  facet_col=\"country\")\n", "            \n", "cont.append(fig)"]}, {"cell_type": "code", "execution_count": 148, "id": "9a7922aa-2336-41b1-8ae4-c41d304f7dbd", "metadata": {}, "outputs": [], "source": ["html_fname = r\"c:\\Users\\<USER>\\OneDrive - Tesco\\#MODELS\\#REPLENISHMENT\\ReplModel_2024\\others\\Summary\\23 vs. 24 hours and volumes.html\"\n", "plotly_figs = cont\n", "combine_plotly_figs_to_html(plotly_figs, html_fname, include_plotlyjs='cdn', \n", "                                separator=None, auto_open=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}